<?xml version="1.0" encoding="UTF-8"?>
<configuration status="debug">
  <appenders>
    <!--异步磁盘appender，默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，默认为noblocking写日志模式-->
    <AsyncScribe blocking="false" name="ScribeAppender">
      <!-- 在指定日志名方面，scribeCategory 和 appkey 两者至少存在一种，且 scribeCategory 高于 appkey。-->
      <!-- <Property name="scribeCategory">data_update_test_lc</Property> -->
      <LcLayout/> <!--该标记表示是否开启丢失率检测，true为开启，false为不开启，默认为false-->
      <Property name="checkLoss">true</Property>
    </AsyncScribe>

    <!--ERROR日志、WARN日志单独输出到一个文件-->
    <XFrameAppender name="xframeAppender">
      <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
    </XFrameAppender>
    <XMDFile fileName="warn.log" name="warnLog">
      <Filters>
        <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
        <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
      </Filters>
    </XMDFile>

    <XMDFile fileName="info.log" name="infoLog" rolloverMax="5"
      sizeBasedTriggeringSize="100M">
      <Filters>
        <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
        <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
      </Filters>
    </XMDFile>

    <!--异步日志上报远程配置-->
    <!-- 如果期望将上报日志从 异步 改为 同步，容忍同步时延，避免丢失，请修改blocking=true参数 -->
    <XMDFile fileName="error.log" name="errorLog">
      <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
    </XMDFile>

    <CatAppender name="catAppender"/>

  </appenders>

  <loggers>
    <!-- 忽略日志列表配置 -->
    <logger additivity="false" level="error" name="com.meituan">
      <appender-ref ref="errorLog"/>
    </logger>
    <logger additivity="false" level="error" name="com.cip.crane">
      <appender-ref ref="errorLog"/>
    </logger>
    <logger additivity="false" level="error" name="com.dianping">
      <appender-ref ref="errorLog"/>
    </logger>
    <logger additivity="false" level="trace"
      name="org.springframework.beans.factory.support.DefaultListableBeanFactory">
      <appender-ref ref="xframeAppender"/>
    </logger>
    <!-- 忽略日志列表配置 end -->

    <root level="info">
      <appender-ref ref="infoLog"/>
      <appender-ref ref="warnLog"/>
      <appender-ref ref="errorLog"/>
      <appender-ref ref="ScribeAppender"/>
      <appender-ref ref="catAppender"/>
    </root>
  </loggers>
</configuration>