#file: noinspection undefined
#日志配置
lion:
  username: risk.center
  pwd: $KMS{lion_token}
domain:
  map: https://lbsapi.meituan.com
  eve: https://eve.sankuai.com
  eve_risk: https://eve.meituan.com
  monitor: https://walle.meituan.com/app/monitor/v2
  monitor_detail: https://walle.meituan.com/app/monitor/v2/detail
  hd_map: https://hdmap.sankuai.com
  ad_map: https://admap-inter.sankuai.com
  fc: https://autodrive.vip.sankuai.com
zebra:
  jdbcRef: eve_risk_center_product
  basePackage: com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter
  transactionManagerName: transactionManager0
  default-transaction-manager: true
  minPoolSize: 20
  maxPoolSize: 50
  sqlSessionFactory:
    typeAliasesPackage: com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter
    typeHandlersPackage: >
      com.meituan.xframe.boot.zebra.autoconfigure.handlers,
      com.sankuai.wallemonitor.risk.center.infra.typehandler
    plugins:
      - className: com.github.pagehelper.PageInterceptor
        properties:
          helperDialect: mysql # 数据库方言配置，默认为mysql。当使用其他数据库时请对应修改，例如：PostgreSQL数据库配置为postgresql、DB2数据配置db2等。
    configuration:
      status: info
      cacheEnabled: false
      lazyLoadingEnabled: true
      mapUnderscoreToCamelCase: true
    zebraMapperScannerConfigurer:
      basePackage: com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter
# mafka配置
mafka:
  producer:
    - producerName: RISK_DOMAIN_EVENT
      namespace: waimai
      topic: wallemonitor.risk.center.domain.event
      openHook: false
    - producerName: RISK_DOMAIN_EVENT_ASYNC
      namespace: waimai
      topic: wallemonitor.risk.center.domain.event.async
      openHook: false
    - producerName: RISK_EVENT
      namespace: waimai
      topic: wallemonitor.risk.event.message
      openHook: false
    - producerName: RISK_EVENT_STATUS
      namespace: waimai
      appkey: com.sankuai.carosscan.realtimeinfo
      topic: mad-vehicle.real.status.out
      openHook: false
    - producerName: AUTO_MARK_PROCESS
      producerType: delayProducer
      namespace: waimai
      topic: wallemonitor.risk.auto.mark.message
      openHook: false
    - producerName: CLOUD_TRIAGE_EVENT
      namespace: waimai
      appkey: com.sankuai.walleops.cloud.triage
      topic: walleops.cloud.triage.event.information
      openHook: false
    - producerName: risk_checking_queue_item.domain.event.producer
      namespace: waimai
      topic: risk.center.risk_checking_queue_item.domain.event
      openHook: false
    - producerName: common.output.risk.event.message.producer
      namespace: waimai
      topic: wallemonitor.risk.center.common.output.risk.event.message
      openHook: false
squirrel:
  - clusterName: redis-wproject-system_product
    readTimeout: 3000
  - clusterName: redis-eve-system_product
    readTimeout: 3000

wechat:
  secret : $KMS{wechat.user.auth.secret}

mender:
  url: https://walle.sankuai.com/mender/service
  clientId: 20de0414f1

reTicket:
  queryDomain: https://spider-mad-admin.sankuai.com

vehicleStatus:
  queryDomain: https://walle.sankuai.com
hdMap:
  clientId: hdmap
cloudCursor:
  clientId : beaconTower
  url: https://walle.sankuai.com/scene-ranking

# 事件平台接入
eventPlatform:
  clientId: com.sankuai.wallemonitor.risk.center
  queryDomain: https://walle.sankuai.com/data-center

# eve事件平台接入
eveEventPlatform:
  queryDomain: https://eve.sankuai.com

# case 平台接入
workstation:
  queryDomain: https://walle.sankuai.com
  caseLink: http://walle.sankuai.com/m/workstation/all?openId=%s

  # 烽火台链接
beaconTower:
  caseLink: https://eve.meituan.com/fe-panel-risk/index.html#/risk/caseList?&openCaseId=%s


udb:
  tenant: meituan.com

org:
  ak: 22dbc7680b
  secret: 
  remoteAppKey: com.sankuai.it.bsi.mdmgatewayservice
  tenantId: 1
  source: ALL