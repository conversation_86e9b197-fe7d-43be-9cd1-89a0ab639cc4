#file: noinspection undefined
#日志配置
lion:
  username: risk.center
  pwd: $KMS{lion_token}
domain:
  map: http://lbsapi.map.test.sankuai.com
  eve: http://eva.test.meituan.com
  eve_risk: https://eve.mad.test.sankuai.com
  monitor: https://walledata.mad.test.sankuai.com/app/monitor/v2
  monitor_detail: https://walledata.mad.test.sankuai.com/app/monitor/v2/detail
  hd_map: http://hdmap.mad.test.sankuai.com
  ad_map: http://manager-hdmap.mad.test.sankuai.com
  fc: https://autodrive.vip.sankuai.com
zebra:
  jdbcRef: eve_risk_center_test
  basePackage: com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter
  transactionManagerName: transactionManager0
  default-transaction-manager: true
  minPoolSize: 20
  maxPoolSize: 50
  sqlSessionFactory:
    typeAliasesPackage: com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter
    typeHandlersPackage: >
      com.meituan.xframe.boot.zebra.autoconfigure.handlers,
      com.sankuai.wallemonitor.risk.center.infra.typehandler
    plugins:
      - className: com.github.pagehelper.PageInterceptor
        properties:
          helperDialect: mysql # 数据库方言配置，默认为mysql。当使用其他数据库时请对应修改，例如：PostgreSQL数据库配置为postgresql、DB2数据配置db2等。
    configuration:
      status: info
      cacheEnabled: false
      lazyLoadingEnabled: true
      mapUnderscoreToCamelCase: true
    zebraMapperScannerConfigurer:
      basePackage: com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter
# Squirrel属性配置
squirrel:
  - clusterName: redis-wproject-system_qa
    readTimeout: 3000
  - clusterName: redis-eve-system_qa
    readTimeout: 3000
# mafka属性配置
mafka:
  producer:
    - producerName: RISK_DOMAIN_EVENT
      namespace: waimai
      topic: wallemonitor.risk.center.domain.event
      openHook: false
    - producerName: RISK_DOMAIN_EVENT_ASYNC
      namespace: waimai
      topic: wallemonitor.risk.center.domain.event.async
      openHook: false
    - producerName: RISK_EVENT
      namespace: waimai
      topic: wallemonitor.risk.event.message
      openHook: false
    - producerName: RISK_EVENT_STATUS
      namespace: waimai
      appkey: com.sankuai.carosscan.realtimeinfo
      topic: mad-vehicle.real.status.out
      openHook: false
    - producerName: CLOUD_TRIAGE_EVENT
      namespace: waimai
      appkey: com.sankuai.walleops.cloud.triage
      topic: walleops.cloud.triage.event.information
      openHook: false
    - producerName: risk_checking_queue_item.domain.event.producer
      namespace: waimai
      topic: risk.center.risk_checking_queue_item.domain.event
      openHook: false
    - producerName: common.output.risk.event.message.producer
      namespace: waimai
      topic: wallemonitor.risk.center.common.output.risk.event.message
      openHook: false


wechat:
  secret: $KMS{wechat.user.auth.secret}

mender:
  url: http://walledata.mad.test.sankuai.com/mender/service
  clientId: ce4fefa22b

reTicket:
  queryDomain: https://ticket.mad.test.sankuai.com

vehicleStatus:
  queryDomain: http://walledata.mad.test.sankuai.com

hdMap:
  clientId: hdmap

cloudCursor:
  clientId : beaconTower
  url: http://walledata.mad.test.sankuai.com/scene-ranking

#事件平台接入
eventPlatform:
  clientId: com.sankuai.wallemonitor.risk.center
  queryDomain: https://walledata.mad.test.sankuai.com/walledata

# eve事件平台接入
eveEventPlatform:
  queryDomain: https://eve.sankuai.com

# case 平台接入
workstation:
  queryDomain: http://walledata.mad.test.sankuai.com
  caseLink: http://walledata.mad.test.sankuai.com/m/workstation/all?openId=%s

  # 烽火台链接
beaconTower:
  caseLink: https://eve.mad.test.sankuai.com/fe-panel-risk/index.html#/risk/caseList?&openCaseId=%s

spring:
  main:
    lazy-initialization: true

udb:
  tenant: sankuai.info

org:
  ak: b51ed7739b
  secret: 
  remoteAppKey: com.sankuai.it.bsi.mdmgatewayservice
  tenantId: 1
  source: ALL


