package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.google.common.collect.Lists;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.LongWaitAreaConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.LongWaitAreaEliminationConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CaseMarkCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MenderOperationTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.SafetyAreaQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@CraneConfiguration
@Slf4j
public class RiskDelayPolygonEliminationCrane {
    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;


    @ConfigValue(LionKeyConstant.LONG_WAIT_AREA_ELIMINATION_CONFIG_KEY)
    private LongWaitAreaEliminationConfigDTO longWaitAreaEliminationConfig;

    @ConfigValue(LionKeyConstant.LONG_WAIT_AREA_CONFIG_KEY)
    private LongWaitAreaConfigDTO longWaitAreaConfig;

    @Crane(CraneConstant.ELIMINATION_DELAY_RECALL_AREA_CRANE)
    @OperateEnter(OperateEnterActionEnum.RISK_DELAY_POLYGON_ELIMINATION_CRANE)
    @ZebraForceMaster
    public void run() {
        if(longWaitAreaEliminationConfig == null || !longWaitAreaEliminationConfig.getOpen()) {
            return;
        }
        // 查前n个工作日之前的时间
        Date beforeTime = queryDateByMinimumMarkCase(new Date(),
                longWaitAreaEliminationConfig.getMinimumCaseInMark(), longWaitAreaEliminationConfig.getPastDays());
        // 查询存留的Safety Area
        List<SafetyAreaDO> safetyAreaDOList = safetyAreaRepository.queryByParam(SafetyAreaQueryParamDTO.builder().
                typeList(Lists.newArrayList(MenderOperationTypeEnum.IMPROPER_STRANDING_DELAY_RECALL.getName()))
                .isDeleted(false).createTimeCreateTo(beforeTime).build());

        // 查询时间段内来源是烽火台、停滞不当，停滞时间大于180s的case
        TimePeriod timePeriod = TimePeriod.builder()
                .endDate(new Date())
                .beginDate(beforeTime)
                .build();

        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder()
                        // 烽火台
                        .sourceList(Arrays.asList(RiskCaseSourceEnum.BEACON_TOWER.getCode()))
                        // 停滞不当事件
                        .caseTypeList(Arrays.asList(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode()))
                        .createTimeRange(timePeriod).durationGreatThan(longWaitAreaConfig.getSecondsLeast()).build());

        // 过滤得到 good case
        CaseMarkInfoDOQueryParamDTO caseMarkInfoDOQueryParamDTO = CaseMarkInfoDOQueryParamDTO.builder().caseIdList(
                        riskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList()))
                .build();

        Set<String> goodCaseList = caseMarkInfoRepository.queryByParam(caseMarkInfoDOQueryParamDTO).stream()
                .filter(x -> x.getCategory().equals(ISCheckCategoryEnum.GOOD_OTHER.getCategory()))
                .map(CaseMarkInfoDO::getCaseId)
                .collect(Collectors.toSet());

        // 拿到riskCase good实体
        List<RiskCaseDO> goodRiskCaseList = riskCaseDOList.stream().filter(caseItem ->
                goodCaseList.contains(caseItem.getCaseId())).collect(Collectors.toList());

        List<SafetyAreaDO> deleteSafetyAreas = new ArrayList<>();

        for (SafetyAreaDO safetyAreaDO : safetyAreaDOList) {
            // 如果区域的创建时间在 beforeTime 之后，先保留，不做淘汰处理
            if (DatetimeUtil.isAfter(safetyAreaDO.getCreateTime(), beforeTime)) {
                log.info("safety area = {}, time is after {}. ignore elimination ", safetyAreaDO, beforeTime);
                continue;
            }

            // travel riskCase, 观察是否有case命中
            boolean isCaseAppear = goodRiskCaseList.stream().anyMatch(
                    riskCaseDO -> riskCaseDO.isInSafetyArea(safetyAreaDO));

            // 没有case命中，淘汰掉这个区域
            if (!isCaseAppear) {
                safetyAreaDO.setIsDeleted(IsDeleteEnum.DELETED);
                deleteSafetyAreas.add(safetyAreaDO);
            }

        }
        log.info("safety area = {} are elimination ", deleteSafetyAreas);

        safetyAreaRepository.batchSave(deleteSafetyAreas);
    }

    /**
     * @param minimumCase        最少的case数
     * @param k                  前K天
     * @param endDate            终止时间
     *
     * 查询标注case数至少是n的前K天
     */

    public Date queryDateByMinimumMarkCase(Date endDate, int minimumCase, int k) {
        Date startDate = DatetimeUtil.getBeforeTime(endDate, TimeUnit.DAYS, k);
        // 查询时间范围内标记为GOOD的case
        CaseMarkInfoDOQueryParamDTO caseMarkInfoDOQueryParamDTO = CaseMarkInfoDOQueryParamDTO
                .builder()
                .categoryIn(Lists.newArrayList(CaseMarkCategoryEnum.GOOD_OTHER.getCategory(), CaseMarkCategoryEnum.BAD_OTHER.getCategory()))
                .createTimeRange(TimePeriod.builder().endDate(endDate).beginDate(startDate).build()).build();

        // group by Date, 统计标记case数
        Map<Date, Long> markInfoDate = caseMarkInfoRepository.queryByParam(caseMarkInfoDOQueryParamDTO).stream()
                .collect(Collectors.groupingBy(x -> DatetimeUtil.getDateYMD(x.getCreateTime()), TreeMap::new, Collectors.counting()));

        for(Map.Entry<Date, Long> entry : markInfoDate.entrySet()) {
            if(entry.getValue() >= minimumCase) {
                k -- ;
            }
            // k = 0 时，代表找到了向前推满足标记case数大于阈值的第k天
            if(k == 0) {
                return entry.getKey();
            }
        }
        // 滑动窗口前移动
        endDate = DatetimeUtil.getBeforeTime(startDate, TimeUnit.DAYS, 1);

        // 剩余天数大于0，往前递归查
        return queryDateByMinimumMarkCase(endDate, minimumCase, k);

    }
}
