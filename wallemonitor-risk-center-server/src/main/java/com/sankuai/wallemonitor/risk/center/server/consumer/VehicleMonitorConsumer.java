package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.dianping.cat.Cat;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CatMonitorConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.MonitorDataMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


/**
 * 来自状态监控的消息
 */
@Component
@Slf4j
public class VehicleMonitorConsumer {

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;


    @MafkaConsumer(
            namespace = "waimai", topic = "eve.data.replay.entrance",
            group = "risk.eve.data.replay.consumer"
    )
    @OperateEnter(OperateEnterActionEnum.VEHICLE_MONITOR_CONSUMER_ENTRY)
    public ConsumeStatus receive(String msgBody) {
        try {
            // 解析消息
            MonitorDataMessageDTO request = JacksonUtils.from(msgBody, MonitorDataMessageDTO.class);
            handleDataReplayRequest(request);
        } catch (Exception e) {
            log.error("Failed to process message: {}", e.getMessage());
        }
        return ConsumeStatus.CONSUME_SUCCESS;

    }

    /**
     * 处理request，入mtsdb
     *
     * @param monitorMessage 数据回放请求
     */
    private void handleDataReplayRequest(MonitorDataMessageDTO monitorMessage) {
        if (monitorMessage == null) {
            return;
        }
        if (MapUtils.isEmpty(monitorMessage.getTags()) || monitorMessage.getVehicleMonitorInfo() == null) {
            return;
        }
        String vin = monitorMessage.getTags().get("vin");
        Long timestamp = DatetimeUtil.getMicoTimeStampFromTime(String.valueOf(monitorMessage.getTime()));
        if (timestamp == null || timestamp == 0 || StringUtils.isBlank(vin)) {
            // 如果消息时间戳，落后于当前时间Xs，打点
            Cat.logEvent(CatMonitorConstant.MESSAGE_LATER_THAN_NOW, "data_replay_no_time", "0",
                    "vin=" + vin);
            return;
        }
        // 消息延时打印
        boolean isDelay =
                DatetimeUtil.getTimeDiff(new Date(timestamp), new Date(), ChronoUnit.SECONDS) > lionConfigRepository.getOnboardMessageAllowLaterThreshold();
        // 如果消息时间戳，落后于当前时间Xs，打点
        Cat.logEvent(CatMonitorConstant.MESSAGE_LATER_THAN_NOW, "data_replay", BooleanUtils.toString(isDelay,"-1", "0"),
                "vin=" + vin);
        VehicleRuntimeInfoContextDO runtimeFromCache = vehicleRuntimeInfoContextRepository.getFromCache(vin);
        //更新来自状态监控的数据
        runtimeFromCache.updateFromMonitor(monitorMessage.getVehicleMonitorInfo());
        vehicleRuntimeInfoContextRepository.updateCache(runtimeFromCache, timestamp);
    }


}
