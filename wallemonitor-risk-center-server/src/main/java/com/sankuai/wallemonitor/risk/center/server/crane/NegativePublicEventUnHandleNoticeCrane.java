package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Sets;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventCraneConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventDxGroupConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventNatureEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventDetailRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@CraneConfiguration
public class NegativePublicEventUnHandleNoticeCrane {

    @Resource
    private NegativePublicEventRepository negativePublicEventRepository;

    @Resource
    private NegativePublicEventDetailRepository negativePublicEventDetailRepository;

    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    @Resource
    private LockUtils lockUtils;

    @ConfigValue(key = LionKeyConstant.LION_KEY_NEGATIVE_PUBLIC_EVENT_CRANE_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private NegativePublicEventCraneConfigDTO craneConfigDTO;

    @ConfigValue(key = LionKeyConstant.LION_KEY_NEGATIVE_PUBLIC_EVENT_DX_GROUP_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private NegativePublicEventDxGroupConfigDTO dxGroupConfigDTO;


    @Crane(CraneConstant.NEGATIVE_EXTERNALITY_NOTIFICATION_CRANE)
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_QUERY_NOTICE_CRANE)
    public void run() throws Exception {
        log.info("run negative public event un handle notice crane");
        // 查询当前满足条件的异常事件
        List<Integer> natureList = Arrays.asList(NegativePublicEventNatureEnum.REAL.getCode());
        List<Integer> statusList = Arrays.asList(NegativePublicEventStatusEnum.LOCATED.getCode(),
                NegativePublicEventStatusEnum.CAUSE_IDENTIFIED.getCode());
        NegativePublicEventDOQueryParamDTO queryParamDTO = NegativePublicEventDOQueryParamDTO.builder()
                .leftJoinEventDetail(true)
                .natureList(natureList)
                .createTimeCreateTo(
                        DatetimeUtil.getBeforeTime(new Date(), TimeUnit.HOURS, craneConfigDTO.getQueryTimeHour()))
                .statusList(statusList)
                .build();

        List<NegativePublicEventDO> eventDOList = negativePublicEventRepository.queryByParam(queryParamDTO);
        if (CollectionUtils.isEmpty(eventDOList)) {
            log.info("run negative public event is empty");
            return;
        }

        // 遍历
        for (NegativePublicEventDO eventDO : eventDOList) {
            try {
                String eventId = eventDO.getEventId();
                NegativePublicEventDetailDO detailDO = negativePublicEventDetailRepository.getByEventId(eventId);
                CheckUtil.isNotNull(detailDO, "事件详情为空");
                //   加锁
                Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(eventId));
                lockUtils.batchLockCanWait(lockKeys, () -> {
                    // 定因
                    if (Objects.equals(eventDO.getStatus(), NegativePublicEventStatusEnum.LOCATED)) {
                        if (DatetimeUtil.getTimeDiff(detailDO.getDetermineNatureTime(), new Date(), ChronoUnit.HOURS)
                                >= craneConfigDTO.getReasonTimeHour()
                                && !detailDO.getReasonNoticed()) {
                            // 发送消息
                            String text = String.format("需尽快完成定因内容填写【[工单地址|%s]】",
                                    dxGroupConfigDTO.getNegativePublicEventUrl() + eventDO.getId());
                            dxNoticeAdapter.sendDxMessage(Long.valueOf(eventDO.getGroupId()), text);
                            detailDO.setReasonNoticed(true);
                            negativePublicEventDetailRepository.save(detailDO);
                        }
                        // 处置
                    } else if (Objects.equals(eventDO.getStatus(), NegativePublicEventStatusEnum.CAUSE_IDENTIFIED)) {
                        if (DatetimeUtil.getTimeDiff(eventDO.getCreateTime(), new Date(), ChronoUnit.HOURS)
                                >= craneConfigDTO.getHandleTimeHour()
                                && !detailDO.getHandleNoticed()) {
                            // 发送消息
                            String text = String.format("需尽快完成处置内容填写【[工单地址|%s]】",
                                    dxGroupConfigDTO.getNegativePublicEventUrl() + eventDO.getId());
                            dxNoticeAdapter.sendDxMessage(Long.valueOf(eventDO.getGroupId()), text);
                            detailDO.setHandleNoticed(true);
                            negativePublicEventDetailRepository.save(detailDO);
                        }
                    }
                });
            } catch (Exception e) {
                log.error("run negative public event un handle notice crane error, eventDO = {}", eventDO, e);
            }


        }

    }

}
