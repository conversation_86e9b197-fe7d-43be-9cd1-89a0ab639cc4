package com.sankuai.wallemonitor.risk.center.server.aop.filter;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.google.common.base.Joiner;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.utils.lion.LionConfigUtils;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import org.locationtech.jts.geom.Point;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * 该类用来处理日志相关的
 */
@Slf4j
public abstract class CommonAroundAspect<T extends Annotation> {

    /**
     * 打印的字段
     *
     * @return
     */
    public abstract String getLogField();

    static {
        SimpleModule module = new SimpleModule();
        module.addSerializer(Point.class, new PointSerializer());
        JacksonUtils.getObjectMapper().registerModule(module);
    }

    /**
     * 获取请求的JSON字符串
     */
    protected String getRequestJson(JoinPoint joinPoint) {
        Object[] points = joinPoint.getArgs();
        Map<String, Object> reqArgs = new HashMap<>();
        try {
            String[] argNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
            int size = argNames.length;
            int x = 0;
            while (x < size) {
                reqArgs.put(argNames[x], points[x]);
                x++;
            }
            return JacksonUtils.to(reqArgs);
        } catch (Exception e) {
            log.error("切面转换入参失败", e);
            return CharConstant.CHAR_EMPTY;
        }
    }


    /**
     * 处理环绕日志的方法
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    protected Object handleAroundLogAndReturnResult(ProceedingJoinPoint joinPoint, T annotation) throws Throwable {
        // 初始化结果对象为null，用于存储方法执行的返回结果。
        Object result = null;
        Map<String, Object> logMap = new HashMap<>();
        Throwable exec = null;
        Transaction transaction = Cat.newTransaction("API", getSignature(joinPoint));
        long startTime = System.currentTimeMillis();
        try {
            // 在执行切面之前，记录日志信息和请求参数信息。
            logMap = beforeLog(joinPoint);
            result = produceResult(joinPoint, annotation);
            return result;
        } catch (Throwable e) {
            // 将异常e封装成exec对象，用于记录异常信息。
            exec = e;
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            transaction.setDurationInMillis(duration);
            transaction.complete();
            //在执行切面之后，记录日志信息和响应结果信息。
            afterLog(logMap, exec, result, true, true);
        }
    }

    /**
     * 处理环绕日志的方法
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    protected Object handleAroundLogWithOutRequestAndReturnResult(ProceedingJoinPoint joinPoint, T annotation)
            throws Throwable {
        // 初始化结果对象为null，用于存储方法执行的返回结果。
        Object result = null;
        Map<String, Object> logMap = new HashMap<>();
        Throwable exec = null;
        Transaction transaction = Cat.newTransaction("API", getSignature(joinPoint));
        long startTime = System.currentTimeMillis();
        try {
            // 在执行切面之前，记录日志信息和请求参数信息。
            logMap = beforeLog(joinPoint);
            result = produceResult(joinPoint, annotation);
            return result;
        } catch (Throwable e) {
            // 将异常e封装成exec对象，用于记录异常信息。
            exec = e;
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            transaction.setDurationInMillis(duration);
            transaction.complete();
            // 在执行切面之后，记录日志信息和响应结果信息。
            afterLog(logMap, exec, result, false, false);
        }
    }

    /**
     * 获取结果
     *
     * @param joinPoint
     * @return
     */
    protected Object produceResult(ProceedingJoinPoint joinPoint, T annotation) throws Throwable {
        return joinPoint.proceed();

    }

    /**
     * 在方法执行前记录日志信息
     *
     * @param joinPoint
     */
    protected Map<String, Object> beforeLog(ProceedingJoinPoint joinPoint) {
        try {
            String signature = getSignature(joinPoint);
            String fullSignature = getFullSignature(joinPoint);
            String name = getLogField();
            Map<String, Object> logMap = new HashMap<>();
            logMap.put(name, signature);
            //冗余一个Key，用于判断是否需要过滤日志
            logMap.put(CommonConstant.LOG_SIGNATURE, signature);
            logMap.put(CommonConstant.FULL_LOG_SIGNATURE, fullSignature);
            logMap.put(LogCenterFieldEnum.AL_REQUEST.getFieldCode(), getRequestJson(joinPoint));
            logMap.put(LogCenterFieldEnum.AL_REQUEST_TIME.getFieldCode(), System.currentTimeMillis());
            return logMap;
        } catch (Exception e) {
            log.error("切面打印日志异常", e);
            return new HashMap<>();
        }

    }

    /**
     * 打印日志
     *
     * @param logMap
     * @param exec
     */
    protected void handleLogXmd(Map<String, Object> logMap, boolean hasException, Throwable exec) {
        try {
            if (hasException) {
                log.error(XMDLogFormat.build().putJson(JacksonUtils.to(logMap)).putTraceID().message(""), exec);
            } else {
                log.info(XMDLogFormat.build().putJson(JacksonUtils.to(logMap)).putTraceID().message(""));
            }
        } catch (Exception e) {
            log.error("打印log异常", e);
        }

    }


    /**
     * 打印日志
     *
     * @param logMap
     * @param exec
     * @param repositoryQueryResult
     * @param needRequest
     * @param needResponse
     */
    protected void afterLog(Map<String, Object> logMap, Throwable exec,
            Object repositoryQueryResult, boolean needRequest, boolean needResponse) {
        try {
            logMap.put(LogCenterFieldEnum.AL_RESPONSE.getFieldCode(), getString(repositoryQueryResult));
            logMap.put(LogCenterFieldEnum.AL_COST.getFieldCode(), getCostTime(logMap));
            boolean hasException = exec != null;
            if (hasException) {
                String code = ErrorCodeException.class.isAssignableFrom(exec.getClass()) ? String.valueOf(
                        ((ErrorCodeException) exec).getCode()) : "";
                String msg = exec.getMessage();
                logMap.put(LogCenterFieldEnum.AL_RESPONSE_CODE.getFieldCode(), code);
                logMap.put(LogCenterFieldEnum.AL_RESPONSE_MSG.getFieldCode(), msg);
            }
            if (!needRequest) {
                // 无需入参,不做打印
                logMap.remove(LogCenterFieldEnum.AL_REQUEST.getFieldCode());
            }
            if (!needResponse) {
                // 无需出参,不做打印
                logMap.remove(LogCenterFieldEnum.AL_RESPONSE.getFieldCode());
            }
            // 以下为过滤逻辑
            String signature = (String) logMap.get(CommonConstant.LOG_SIGNATURE);
            String fullSignature = (String) logMap.get(CommonConstant.FULL_LOG_SIGNATURE);
            LogFilterDTO logFilterDTO = getLogFilter(signature,fullSignature);
            if (logFilterDTO != null && logFilterDTO.isNoNeedRequest() && !hasException) {
                // 无需入参且无异常,不做打印
                logMap.put(LogCenterFieldEnum.AL_REQUEST.getFieldCode(), "FILTER");
            }
            if (logFilterDTO != null && logFilterDTO.isNoNeedResponse() && !hasException) {
                // 无需出参且无异常,不做打印
                logMap.put(LogCenterFieldEnum.AL_RESPONSE.getFieldCode(), "FILTER");
            }
            if (logFilterDTO != null && logFilterDTO.isNoNeed() && !hasException) {
                // 无需整体且无异常,不做打印
                return;
            }
            handleLogXmd(logMap, hasException, exec);
        } catch (Exception e) {
            log.error("切面打印日志异常", e);
        }


    }

    private LogFilterDTO getLogFilter(String signature, String fullSignature) {
        // 默认都需要
        LogFilterDTO logFilterDTO = LogFilterDTO.builder().build();
        Set<String> logFilterRule = getFilterList();
        if (CollectionUtils.isEmpty(logFilterRule)) {
            // 没有过滤配置
            return null;
        }
        // 配置值以 signature 开头
        String thisRule = logFilterRule.stream()
                // 如果签名的前缀是该规则，或者完整签名的前缀是该规则
                .filter(rule -> {
                    String[] ruleNameList = StringUtils.split(rule, CharConstant.CHAR_JH);
                    if (ruleNameList.length == 0) {
                        return false;
                    }
                    // 取第一个
                    String ruleName = ruleNameList[0];
                    return signature.startsWith(ruleName) || fullSignature.startsWith(ruleName);
                })
                .findFirst()
                .orElse(CharConstant.CHAR_EMPTY);
        if (StringUtils.isBlank(thisRule)) {
            // 没有规则
            return null;
        }
        if (StringUtils.equals(thisRule, signature)) {
            // 如果配置的规则和signature,完全相等
            logFilterDTO.setNoNeed(true);
            return logFilterDTO;
        }
        // 按照 XXXX、XXXX#XXXX 、 XXXX#XXXX#XXXX 分割
        String[] split = thisRule.split(CharConstant.CHAR_JH);
        if (split.length < 2) {
            // 没有，整体忽略
            logFilterDTO.setNoNeed(true);
            return logFilterDTO;
        }
        // 如果含有#号，且可分割
        logFilterDTO.setNoNeed(false);
        // 第一个是是否需要参数
        logFilterDTO.setNoNeedRequest(Boolean.parseBoolean(split[1]));
        // 第二个是是否需要返回值
        logFilterDTO.setNoNeedResponse(split.length >= 3 && Boolean.parseBoolean(split[2]));
        return logFilterDTO;

    }

    private Set<String> getFilterList() {
        try {
            return LionConfigUtils.getSet(LionKeyConstant.LION_FILTER_LIST, String.class);
        } catch (Exception e) {
            log.warn("获取过滤列表异常", e);
            return new HashSet<>();
        }
    }

    /**
     * @param queryObject
     * @return
     */
    private Object getString(Object queryObject) {
        if (queryObject == null) {
            return null;
        }
        if (ResponseEntity.class.isAssignableFrom(queryObject.getClass())) {
            return JacksonUtils.to(((ResponseEntity<?>) queryObject).getHeaders());
        }
        if (String.class.isAssignableFrom(queryObject.getClass())) {
            return queryObject.toString();
        }
        return JacksonUtils.to(queryObject);
    }

    /**
     * 获取耗时
     *
     * @param logMap
     * @return
     */
    private Long getCostTime(Map<String, Object> logMap) {
        Long requestTime = (Long) logMap.get(LogCenterFieldEnum.AL_REQUEST_TIME.getFieldCode());
        if (requestTime == null) {
            return null;
        }
        return System.currentTimeMillis() - requestTime;
    }

    /**
     * 通用切面获取方法
     *
     * @param joinPoint
     * @return
     */
    protected String getSignature(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        // 获取简单类名
        String simpleClassName = signature.getDeclaringType().getSimpleName();
        // 获取方法名
        String methodName = signature.getName();
        return Joiner.on(CharConstant.CHAR_DH).join(simpleClassName, methodName);
    }
    /**
     * 通用切面获取完整方法签名（包含包名）
     *
     * @param joinPoint 切点
     * @return 完整签名字符串
     */
    protected String getFullSignature(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        // 获取完整类名（包含包名）
        String fullClassName = signature.getDeclaringType().getName();
        // 获取方法名
        String methodName = signature.getName();
        return Joiner.on(CharConstant.CHAR_MH).join(fullClassName, methodName);
    }

    @Component
    public static class PointSerializer extends JsonSerializer<Point> {
        @Override
        public void serialize(Point point, JsonGenerator gen, SerializerProvider provider) throws IOException {
            gen.writeStartObject();
            gen.writeNumberField("x", point.getX());
            gen.writeNumberField("y", point.getY());
            gen.writeEndObject();
        }
    }

    /**
     * 日志忽略器
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class LogFilterDTO {
        private boolean noNeed;
        private boolean noNeedRequest;
        private boolean noNeedResponse;
    }
}
