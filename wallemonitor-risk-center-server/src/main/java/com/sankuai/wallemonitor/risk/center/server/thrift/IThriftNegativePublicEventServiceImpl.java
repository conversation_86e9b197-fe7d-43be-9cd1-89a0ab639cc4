package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Sets;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftPage;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventBaseInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventCreateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventDetailRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventDetermineNatureInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventDetermineReasonInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventHandleInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventListRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventReviewInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.response.NegativePublicEventDetailResponse;
import com.sankuai.wallemonitor.risk.center.api.response.NegativePublicEventListResponse;
import com.sankuai.wallemonitor.risk.center.api.response.vo.NegativePublicEventOperationRecordVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.NegativePublicEventSourceInfoVO;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftNegativePublicEventService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.UserInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.ListDiffDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventDetailExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventStateMachineDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventHandleDegreeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventLevelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventNatureEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventNatureEnum.EventGroup;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventRelatedFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventRelatedDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventDetailRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventRelatedRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventRelatedDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.DiffUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.UuidUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeTemplateConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftNegativePublicEventServiceImpl implements IThriftNegativePublicEventService {

    @Resource
    private NegativePublicEventRepository negativePublicEventRepository;

    @Resource
    private NegativePublicEventDetailRepository negativePublicEventDetailRepository;

    @Resource
    private NegativePublicEventRelatedRepository negativePublicEventRelatedRepository;

    @Resource
    private LockUtils lockUtils;

    @ConfigValue(key = LionKeyConstant.LION_KEY_STATE_MACHINE_TRANSITION_RULE_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private Map<Integer, List<String>> stateMachineTransitionRuleConfig;

    /**
     * 筛选负外部性时间列表
     *
     * @param request
     * @return
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_FILTER_LIST)
    public EveThriftPageResponse<List<NegativePublicEventListResponse>> list(NegativePublicEventListRequest request) {
        // 参数校验
        CheckUtil.isNotNull(request, "请求参数不能为空");
        CheckUtil.isNotBlank(request.getCreateTimeStart(), "创建时间开始不能为空");
        CheckUtil.isNotBlank(request.getCreateTimeEnd(), "创建时间结束不能为空");

        // 构建查询参数
        NegativePublicEventDOQueryParamDTO paramDTO = buildNegativePublicEventDOQueryParam(request);

        Paging<NegativePublicEventDO> negativePublicEventDOList = negativePublicEventRepository.queryByParamByPage(
                paramDTO, request.getPageNum(), request.getPageSize());

        // 组建响应参数
        List<NegativePublicEventListResponse> responseList = negativePublicEventDOList.getElements().stream()
                .map(this::convertDO2Response)
                .collect(Collectors.toList());

        EveThriftPageResponse response = EveThriftPageResponse.ok(responseList);
        EveThriftPage pageInfo = new EveThriftPage();
        pageInfo.setPageNum(request.getPageNum());
        pageInfo.setPageSize(request.getPageSize());
        pageInfo.setTotal(negativePublicEventDOList.getTotal());
        response.setPaging(pageInfo);
        return response;
    }

    /**
     * 查询负外部性事件详情
     *
     * @param request
     * @return
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_QUERY_DETAIL)
    public EveThriftResponse<NegativePublicEventDetailResponse> queryEventDetail(
            NegativePublicEventDetailRequest request) {
        // 参数校验
        CheckUtil.isNotNull(request, "请求参数不能为空");
        CheckUtil.isNotAllEmpty(request, "请求参数不能全部为空");

        NegativePublicEventDO negativePublicEventDO = negativePublicEventRepository.getByEventId(request.getId(),
                request.getEventId());
        CheckUtil.isNotNull(negativePublicEventDO, "查询负外部性事件不存在");
        // 构建负外部性事件详情响应对象
        NegativePublicEventDetailResponse response = buildEventDetailResponse(negativePublicEventDO);
        return EveThriftResponse.ok(response);
    }

    /**
     * 创建负外部性事件
     *
     * @param request
     * @return
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_CREATE)
    @Transactional
    public EveThriftResponse<EmptyResponse> create(NegativePublicEventCreateRequest request) {
        // 参数校验
        CheckUtil.isNotNull(request, "请求参数不能为空");
        CheckUtil.isNotBlank(request.getCity(), "城市不能为空");
        CheckUtil.isNotBlank(request.getDistrict(), "区县不能为空");
        CheckUtil.isNotBlank(request.getPerceiveTime(), "感知时间不能为空");
        CheckUtil.isNotBlank(request.getOccurTime(), "发生时间不能为空");
        CheckUtil.isNotNull(request.getType(), "事件类型不能为空");
        CheckUtil.isNotNull(NegativePublicEventTypeEnum.fromCode(request.getType()), "事件类型不合法");
        CheckUtil.isNotEmpty(request.getSourceList(), "事件来源不能为空");
        List<NegativePublicEventSourceEnum> sourceEnumList = NegativePublicEventSourceEnum.getEnumByCode(
                request.getSourceList());
        CheckUtil.isNotEmpty(sourceEnumList, "事件来源不合法");

        //事件详情的字符大小不能超过2000
        CheckUtil.isTrue(request.getEventDesc().length() <= 2000, "事件详情字符大小不能超过2000");

        // 构建负外部性事件DO对象
        NegativePublicEventDO negativePublicEventDO = convertRequest2DO(request);
        // 保存负外部性事件DO对象到数据库中
        negativePublicEventRepository.save(negativePublicEventDO);
        // 保存负外部性事件关联DO对象到数据库中
        List<NegativePublicEventRelatedDO> relatedDOList = new ArrayList<>();
        sourceEnumList.stream().filter(Objects::nonNull)
                .forEach(sourceEnum -> {
                    NegativePublicEventRelatedDO relatedDO = new NegativePublicEventRelatedDO();
                    relatedDO.setEventId(negativePublicEventDO.getEventId());
                    relatedDO.setFieldName(NegativePublicEventRelatedFieldEnum.SOURCE.getName());
                    relatedDO.setFieldValue(sourceEnum.getCode().toString());
                    relatedDOList.add(relatedDO);
                });
        if (CollectionUtils.isNotEmpty(request.getRelatedFileLinks())) {
            request.getRelatedFileLinks().stream()
                    .filter(StringUtils::isNotBlank)
                    .forEach(link -> {
                        NegativePublicEventRelatedDO relatedDO = new NegativePublicEventRelatedDO();
                        relatedDO.setEventId(negativePublicEventDO.getEventId());
                        relatedDO.setFieldName(NegativePublicEventRelatedFieldEnum.RELATED_FILE_LINKS.getName());
                        relatedDO.setFieldValue(link);
                        relatedDOList.add(relatedDO);
                    });
        }
        negativePublicEventRelatedRepository.batchSave(relatedDOList);

        String title = buildTitle(request, sourceEnumList);
        NegativePublicEventDetailDO detailDO = new NegativePublicEventDetailDO();
        detailDO.setEventId(negativePublicEventDO.getEventId());
        detailDO.setTitle(title);
        negativePublicEventDetailRepository.save(detailDO);
        return EveThriftResponse.ok().build();
    }

    /**
     * 更新负外部性事件基本信息
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_UPDATE_BASIC_INFO)
    public EveThriftResponse<EmptyResponse> updateBaseInfo(NegativePublicEventBaseInfoUpdateRequest request) {
        CheckUtil.isNotNull(request, "请求参数不能为空");
        CheckUtil.isNotNull(request.getEventId(), "事件ID不能为空");
        Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(request.getEventId()));
        return lockUtils.batchLockCanWait(lockKeys, () -> {
            // 根据异常事件Id查询 NEGATIVE_PUBLIC_EVENT_DO
            NegativePublicEventDO eventDO = negativePublicEventRepository.getByEventId(null,
                    request.getEventId());
            CheckUtil.isNotNull(eventDO, "更新的负外部性事件不存在");
            //  更新事件的基本信息
            if (Objects.nonNull(request.getType())) {
                NegativePublicEventTypeEnum typeEnum = NegativePublicEventTypeEnum.fromCode(request.getType());
                CheckUtil.isNotNull(typeEnum, "来源类型不合法");
                eventDO.setType(typeEnum);
            }
            if (StringUtils.isNotBlank(request.getPerceiveTime())) {
                eventDO.setPerceiveTime(DatetimeUtil.parseDate(request.getPerceiveTime(),
                        DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND));
            }
            if (StringUtils.isNotBlank(request.getOccurTime())) {
                eventDO.setOccurTime(DatetimeUtil.parseDate(request.getOccurTime(),
                        DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND));
            }

            //更新省份
            Optional.ofNullable(request.getProvince()).map(StringUtils::trimToEmpty)
                    .ifPresent(eventDO::setProvince);

            //更新城市
            Optional.ofNullable(request.getCity()).map(StringUtils::trimToEmpty)
                    .ifPresent(eventDO::setCity);
            //更新区县
            Optional.ofNullable(request.getDistrict()).map(StringUtils::trimToEmpty)
                    .ifPresent(eventDO::setDistrict);
            //更新事件描述
            Optional.ofNullable(request.getEventDesc()).map(StringUtils::trimToEmpty)
                    .ifPresent(eventDO::setEventDesc);

            // 更新扩展信息
            NegativePublicEventExtInfoDTO extInfoDTO = Optional.ofNullable(eventDO.getExtInfo())
                    .orElse(new NegativePublicEventExtInfoDTO());

            Optional.ofNullable(request.getLocation()).map(StringUtils::trimToEmpty)
                    .ifPresent(extInfoDTO::setLocation);
            eventDO.setExtInfo(extInfoDTO);

            // 保存更新后的负外部性事件信息到数据库中
            negativePublicEventRepository.save(eventDO);

            // 保存关联信息
            List<NegativePublicEventRelatedDO> relatedDOList = buildBaseInfoRelatedDO(eventDO, request);
            negativePublicEventRelatedRepository.batchSave(relatedDOList);

            return EveThriftResponse.ok().build();
        });


    }

    /**
     * 更新负外部性事件定性信息
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_UPDATE_QUALITATIVE_INFO)

    public EveThriftResponse<EmptyResponse> updateDetermineNatureInfo(
            NegativePublicEventDetermineNatureInfoUpdateRequest request) {
        CheckUtil.isNotNull(request, "请求参数不能为空");
        CheckUtil.isNotNull(request.getEventId(), "事件ID不能为空");
        Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(request.getEventId()));
        return lockUtils.batchLockCanWait(lockKeys, () -> {

            // 根据事件事件ID查询 NEGATIVE_PUBLIC_EVENT_DETAIL_DO
            NegativePublicEventDetailDO detailDO = negativePublicEventDetailRepository.getByEventId(
                    request.getEventId());
            CheckUtil.isNotNull(detailDO, "更新的负外部性事件详情不存在");
            NegativePublicEventDO eventDO = negativePublicEventRepository.getByEventId(null, detailDO.getEventId());
            CheckUtil.isNotNull(eventDO, "更新的异常事件不存在");
            NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                    .eventId(request.getEventId()).fieldName(NegativePublicEventRelatedFieldEnum.REASON.getName())
                    .build();
            List<NegativePublicEventRelatedDO> reasonRelatedList = negativePublicEventRelatedRepository.queryByParam(
                    paramDTO);

            NegativePublicEventStateMachineDTO oldStateMachineDTO = new NegativePublicEventStateMachineDTO();
            oldStateMachineDTO.setNature(detailDO.getNature());
            oldStateMachineDTO.setHandleDegree(detailDO.getHandleDegree());
            oldStateMachineDTO.setStatus(eventDO.getStatus());
            oldStateMachineDTO.setReason(CollectionUtils.isNotEmpty(reasonRelatedList));

            NegativePublicEventStateMachineDTO newStateMachineDTO = new NegativePublicEventStateMachineDTO();
            newStateMachineDTO.setStatus(eventDO.getStatus());
            newStateMachineDTO.setHandleDegree(detailDO.getHandleDegree());
            newStateMachineDTO.setReason(CollectionUtils.isNotEmpty(reasonRelatedList));

            // 将更新参数赋值到对象中
            if (Objects.nonNull(request.getNature())) {
                NegativePublicEventNatureEnum natureEnum = NegativePublicEventNatureEnum.fromCode(request.getNature());
                CheckUtil.isNotNull(natureEnum, "事件性质不合法");
                detailDO.updateNature(natureEnum);
                newStateMachineDTO.setNature(natureEnum);
            }
            if (Objects.nonNull(request.getLevel())) {
                NegativePublicEventLevelEnum levelEnum = NegativePublicEventLevelEnum.fromCode(request.getLevel());
                CheckUtil.isNotNull(levelEnum, "事件等级不合法");
                detailDO.setLevel(levelEnum);
            }

            detailDO.setEventId(request.getEventId());
            // 问题分类
            Optional.ofNullable(request.getCategory()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailDO::setCategory);
            // 其他问题分类描述
            Optional.ofNullable(request.getOtherCategoryDesc()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailDO::setOtherCategoryDesc);
            // 标题
            Optional.ofNullable(request.getTitle()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailDO::setTitle);
            // 情况描述
            Optional.ofNullable(request.getConditionDesc()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailDO::setConditionDesc);

            // 更新扩展信息
            NegativePublicEventDetailExtInfoDTO detailExtInfoDTO = detailDO.getExtInfo();
            if (detailExtInfoDTO == null) {
                detailExtInfoDTO = new NegativePublicEventDetailExtInfoDTO();
            }
            //网络名称
            Optional.ofNullable(request.getInternetNickname()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailExtInfoDTO::setInternetNickname);
            // 联系人信息
            //TODO: 可以通过正则进行手机号匹配
            Optional.ofNullable(request.getContactInfo()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailExtInfoDTO::setContactInfo);
            // 事故关联
            Optional.ofNullable(request.getAccidentOrderLink()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailExtInfoDTO::setAccidentOrderLink);

            detailDO.setExtInfo(detailExtInfoDTO);

            // 关联信息
            List<NegativePublicEventRelatedDO> relatedDOList = new ArrayList<>();
            List<Long> deleteIdList = new ArrayList<>();

            // 状态流转计算
            updateStatus(eventDO, detailDO, oldStateMachineDTO, newStateMachineDTO, deleteIdList);
            buildNatureRelatedDOList(relatedDOList, deleteIdList, request);

            negativePublicEventRepository.save(eventDO);
            negativePublicEventDetailRepository.save(detailDO);
            negativePublicEventRelatedRepository.batchDeleteRelatedDO(deleteIdList);
            negativePublicEventRelatedRepository.batchSave(relatedDOList);

            return EveThriftResponse.ok().build();
        });
    }


    /**
     * 更新负外部性事件归因信息
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_UPDATE_ATTRIBUTION_INFO)
    public EveThriftResponse<EmptyResponse> updateDetermineReasonInfo(
            NegativePublicEventDetermineReasonInfoUpdateRequest request) {
        CheckUtil.isNotNull(request, "请求参数不能为空");
        CheckUtil.isNotNull(request.getEventId(), "事件ID不能为空");

        Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(request.getEventId()));
        return lockUtils.batchLockCanWait(lockKeys, () -> {

            // 查询事件详情
            NegativePublicEventDetailDO detailDO = negativePublicEventDetailRepository.getByEventId(
                    request.getEventId());
            CheckUtil.isNotNull(detailDO, "更新的事件不存在，请先完成定位操作");
            NegativePublicEventDO eventDO = negativePublicEventRepository.getByEventId(null, detailDO.getEventId());
            CheckUtil.isNotNull(eventDO, "更新的异常事件不存在");
            NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                    .eventId(request.getEventId()).fieldName(NegativePublicEventRelatedFieldEnum.REASON.getName())
                    .build();
            List<NegativePublicEventRelatedDO> reasonRelatedList = negativePublicEventRelatedRepository.queryByParam(
                    paramDTO);

            NegativePublicEventStateMachineDTO oldStateMachineDTO = new NegativePublicEventStateMachineDTO();
            oldStateMachineDTO.setNature(detailDO.getNature());
            oldStateMachineDTO.setHandleDegree(detailDO.getHandleDegree());
            oldStateMachineDTO.setStatus(eventDO.getStatus());
            oldStateMachineDTO.setReason(CollectionUtils.isNotEmpty(reasonRelatedList));

            NegativePublicEventStateMachineDTO newStateMachineDTO = new NegativePublicEventStateMachineDTO();
            newStateMachineDTO.setNature(detailDO.getNature());
            newStateMachineDTO.setStatus(eventDO.getStatus());
            newStateMachineDTO.setHandleDegree(detailDO.getHandleDegree());
            newStateMachineDTO.setReason(CollectionUtils.isNotEmpty(request.getReasonList()));

            // 判断事件性质是否需要更新归因信息
            if (Objects.isNull(detailDO.getNature()) || !NegativePublicEventNatureEnum.needUpdateReasonInfo(
                    detailDO.getNature())) {
                String errMsg = String.format("当前事件性质为%s，无需更新归因信息", detailDO.getNature().getDesc());
                return EveThriftResponse.failWithMessage(errMsg).build();
            }
            // 更新其他归因信息
            Optional.ofNullable(request.getOtherReasonDesc()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailDO::setOtherReasonDesc);

            // 更新检查结果
            Optional.ofNullable(request.getCheckResult()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailDO::setCheckResult);

            List<NegativePublicEventRelatedDO> relatedDOList = new ArrayList<>();
            List<Long> deleteIdList = new ArrayList<>();
            //  构建归因相关的DO列表和删除ID列表
            buildReasonRelatedDOList(relatedDOList, deleteIdList, request);
            // 只有reason 字段发生了变更才需要更新 归因相关字段
            if (CollectionUtils.isNotEmpty(relatedDOList) || CollectionUtils.isNotEmpty(deleteIdList)) {
                detailDO.updateReason();
            }
            // 状态流转计算 - 状态回退也会触发 related 数据表中的数据清除
            updateStatus(eventDO, detailDO, oldStateMachineDTO, newStateMachineDTO, deleteIdList);
            // 保存更新
            negativePublicEventRepository.save(eventDO);
            negativePublicEventDetailRepository.save(detailDO);
            negativePublicEventRelatedRepository.batchSave(relatedDOList);
            negativePublicEventRelatedRepository.batchDeleteRelatedDO(deleteIdList);

            return EveThriftResponse.ok().build();

        });
    }

    /**
     * 更新负外部性事件处置信息
     *
     * @param request
     * @return
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_UPDATE_DISPOSAL_INFO)
    public EveThriftResponse<EmptyResponse> updateHandleInfo(NegativePublicEventHandleInfoUpdateRequest request) {
        CheckUtil.isNotNull(request, "请求参数不能为空");
        CheckUtil.isNotNull(request.getEventId(), "事件ID不能为空");

        Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(request.getEventId()));
        return lockUtils.batchLockCanWait(lockKeys, () -> {
            NegativePublicEventDetailDO detailDO = negativePublicEventDetailRepository.getByEventId(
                    request.getEventId());
            CheckUtil.isNotNull(detailDO, "更新的事件不存在,请先完成定位操作");
            NegativePublicEventDO eventDO = negativePublicEventRepository.getByEventId(null, detailDO.getEventId());
            CheckUtil.isNotNull(eventDO, "更新的异常事件不存在");
            NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                    .eventId(request.getEventId()).fieldName(NegativePublicEventRelatedFieldEnum.REASON.getName())
                    .build();
            List<NegativePublicEventRelatedDO> reasonRelatedList = negativePublicEventRelatedRepository.queryByParam(
                    paramDTO);

            NegativePublicEventStateMachineDTO oldStateMachineDTO = new NegativePublicEventStateMachineDTO();
            oldStateMachineDTO.setNature(detailDO.getNature());
            oldStateMachineDTO.setHandleDegree(detailDO.getHandleDegree());
            oldStateMachineDTO.setStatus(eventDO.getStatus());
            oldStateMachineDTO.setReason(CollectionUtils.isNotEmpty(reasonRelatedList));

            NegativePublicEventStateMachineDTO newStateMachineDTO = new NegativePublicEventStateMachineDTO();
            newStateMachineDTO.setNature(detailDO.getNature());
            newStateMachineDTO.setStatus(eventDO.getStatus());
            newStateMachineDTO.setReason(CollectionUtils.isNotEmpty(reasonRelatedList));

            // 判断事件性质是否需要更新处置信息
            if (Objects.isNull(detailDO.getNature()) || !NegativePublicEventNatureEnum.needUpdateHandleInfo(
                    detailDO.getNature())) {
                String errMsg = String.format("当前事件性质为%s，无需更新处置信息", detailDO.getNature().getDesc());
                return EveThriftResponse.failWithMessage(errMsg).build();
            }

            if (Objects.nonNull(request.getHandleDegree())) {
                NegativePublicEventHandleDegreeEnum handleDegreeEnum = NegativePublicEventHandleDegreeEnum.fromCode(
                        request.getHandleDegree());
                CheckUtil.isNotNull(handleDegreeEnum, "处置程度不合法");
                detailDO.updateHandle(handleDegreeEnum);
                newStateMachineDTO.setHandleDegree(handleDegreeEnum);
            }
            // 处置结果描述
            Optional.ofNullable(request.getHandleResultDesc()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailDO::setHandleResultDesc);

            List<NegativePublicEventRelatedDO> relatedDOList = new ArrayList<>();
            List<Long> deleteIdList = new ArrayList<>();
            //  构建处置相关的DO列表和删除ID列表
            buildHandleRelatedDOList(relatedDOList, deleteIdList, request);

            // 状态流转计算
            updateStatus(eventDO, detailDO, oldStateMachineDTO, newStateMachineDTO, deleteIdList);

            negativePublicEventRepository.save(eventDO);
            negativePublicEventDetailRepository.save(detailDO);
            negativePublicEventRelatedRepository.batchSave(relatedDOList);
            negativePublicEventRelatedRepository.batchDeleteRelatedDO(deleteIdList);

            return EveThriftResponse.ok().build();

        });

    }

    /**
     * 更新负外部性事件复盘信息
     *
     * @param request
     * @return
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_UPDATE_REVIEW_INFO)
    public EveThriftResponse<EmptyResponse> updateReviewInfo(NegativePublicEventReviewInfoUpdateRequest request) {
        CheckUtil.isNotNull(request, "请求参数不能为空");
        CheckUtil.isNotNull(request.getEventId(), "事件ID不能为空");
        Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(request.getEventId()));
        return lockUtils.batchLockCanWait(lockKeys, () -> {
            NegativePublicEventDetailDO detailDO = negativePublicEventDetailRepository.getByEventId(
                    request.getEventId());
            CheckUtil.isNotNull(detailDO, "更新的事件不存在");

            Optional.ofNullable(request.getReviewInfo()).map(StringUtils::trimToEmpty)
                    .ifPresent(detailDO::setReviewInfo);

            // 保存更新
            negativePublicEventDetailRepository.save(detailDO);

            return EveThriftResponse.ok().build();
        });
    }

    /**
     * request 转 DO
     *
     * @param request
     * @return
     */
    private NegativePublicEventDO convertRequest2DO(NegativePublicEventCreateRequest request) {
        String reporter = CommonConstant.UNKNOWN;
        if (Objects.nonNull(UserInfoContext.getUserInfo())) {
            reporter = String.format("%s/%s", UserInfoContext.getUserInfo().getName(),
                    UserInfoContext.getUserInfo().getLogin());
        }
        NegativePublicEventDO negativePublicEventDO = NegativePublicEventDO.builder()
                .status(NegativePublicEventStatusEnum.CREATED)
                .province(request.getProvince())
                .city(request.getCity())
                .eventId(UuidUtil.uuid())
                .eventDesc(request.getEventDesc())
                .type(NegativePublicEventTypeEnum.fromCode(request.getType()))
                .district(request.getDistrict())
                .perceiveTime(DatetimeUtil.parseDate(request.getPerceiveTime(),
                        DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND))
                .occurTime(DatetimeUtil.parseDate(request.getOccurTime(),
                        DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND))
                .reporter(reporter).build();
        // 扩展信息
        NegativePublicEventExtInfoDTO extInfoDTO = NegativePublicEventExtInfoDTO.builder()
                .location(request.getLocation()).build();
        negativePublicEventDO.setExtInfo(extInfoDTO);

        return negativePublicEventDO;

    }

    /**
     * DO转Response
     *
     * @param negativePublicEventDO
     * @return
     */
    private NegativePublicEventListResponse convertDO2Response(NegativePublicEventDO negativePublicEventDO) {
        NegativePublicEventListResponse response = new NegativePublicEventListResponse();

        // NEGATIVE_PUBLIC_EVENT_DO -> response 从负外部性事件表中提取信息
        response.setId(negativePublicEventDO.getId());
        response.setEventId(negativePublicEventDO.getEventId());
        response.setType(negativePublicEventDO.getType() == null ? null : negativePublicEventDO.getType().getCode());
        response.setTypeDesc(
                negativePublicEventDO.getType() == null ? null : negativePublicEventDO.getType().getDesc());
        response.setStatus(
                negativePublicEventDO.getStatus() == null ? null : negativePublicEventDO.getStatus().getCode());
        response.setStatusDesc(
                negativePublicEventDO.getStatus() == null ? null : negativePublicEventDO.getStatus().getDesc());
        response.setCreateTime(DatetimeUtil.formatTime(negativePublicEventDO.getCreateTime()));
        response.setEventDesc(negativePublicEventDO.getEventDesc());

        // NEGATIVE_PUBLIC_EVENT_DETAIL_DO -> response 从负外部性事件详情表中获取信息
        NegativePublicEventDetailDO detailDO = negativePublicEventDetailRepository.getByEventId(
                negativePublicEventDO.getEventId());
        response.setTitle(detailDO == null ? null : detailDO.getTitle());

        // NEGATIVE_PUBLIC_EVENT_RELATED_DO -> response  从负外部性事件关联表中获取信息
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(negativePublicEventDO.getEventId()).build();
        List<NegativePublicEventRelatedDO> relatedDOList = negativePublicEventRelatedRepository.queryByParam(paramDTO);
        if (CollectionUtils.isNotEmpty(relatedDOList)) {
            Map<String, List<String>> fieldValueMap = relatedDOList.stream()
                    .collect(Collectors.groupingBy(NegativePublicEventRelatedDO::getFieldName,
                            Collectors.mapping(NegativePublicEventRelatedDO::getFieldValue, Collectors.toList())));
            List<String> sourceList = fieldValueMap.getOrDefault(NegativePublicEventRelatedFieldEnum.SOURCE.getName(),
                    new ArrayList<>());
            List<NegativePublicEventSourceInfoVO> sourceInfoList = sourceList.stream()
                    .map(source -> {
                        Integer sourceCode = Integer.valueOf(source);
                        NegativePublicEventSourceEnum sourceEnum = NegativePublicEventSourceEnum.fromCode(sourceCode);
                        return new NegativePublicEventSourceInfoVO(sourceEnum.getCode(), sourceEnum.getDesc());
                    })
                    .collect(Collectors.toList());
            response.setSourceInfoList(sourceInfoList);
        }

        return response;
    }

    /**
     * 构建查询事件详情接口的响应
     *
     * @param negativePublicEventDO
     * @return
     */
    private NegativePublicEventDetailResponse buildEventDetailResponse(NegativePublicEventDO negativePublicEventDO) {
        NegativePublicEventDetailResponse response = new NegativePublicEventDetailResponse();
        // NEGATIVE_PUBLIC_EVENT_DO -> response 从负外部性事件表中提取信息
        response.setId(negativePublicEventDO.getId());
        response.setEventId(negativePublicEventDO.getEventId());
        response.setStatus(
                negativePublicEventDO.getStatus() == null ? null : negativePublicEventDO.getStatus().getCode());
        response.setType(negativePublicEventDO.getType() == null ? null : negativePublicEventDO.getType().getCode());
        response.setCreateTime(DatetimeUtil.formatTime(negativePublicEventDO.getCreateTime()));
        response.setOccurTime(DatetimeUtil.formatTime(negativePublicEventDO.getOccurTime()));
        response.setPerceiveTime(DatetimeUtil.formatTime(negativePublicEventDO.getPerceiveTime()));
        response.setProvince(negativePublicEventDO.getProvince());
        response.setCity(negativePublicEventDO.getCity());
        response.setDistrict(negativePublicEventDO.getDistrict());
        response.setLocation(
                negativePublicEventDO.getExtInfo() == null ? null : negativePublicEventDO.getExtInfo().getLocation());
        response.setGroupId(negativePublicEventDO.getGroupId());
        response.setEventDesc(negativePublicEventDO.getEventDesc());

        // 创建时的操作记录
        List<NegativePublicEventOperationRecordVO> operationRecordList = new ArrayList<>();
        operationRecordList.add(
                NegativePublicEventOperationRecordVO.builder().operator(negativePublicEventDO.getReporter())
                        .operationTime(DatetimeUtil.formatTime(negativePublicEventDO.getCreateTime()))
                        .status(NegativePublicEventStatusEnum.CREATED.getCode())
                        .statusDes(NegativePublicEventStatusEnum.CREATED.getDesc()).build());

        // NEGATIVE_PUBLIC_EVENT_DETAIL_DO -> response 从负外部性事件详情表中获取信息
        NegativePublicEventDetailDO detailDO = negativePublicEventDetailRepository.getByEventId(
                negativePublicEventDO.getEventId());

        if (Objects.nonNull(detailDO)) {
            response.setNature(detailDO.getNature() == null ? null : detailDO.getNature().getCode());
            response.setLevel(detailDO.getLevel() == null ? null : detailDO.getLevel().getCode());
            response.setHandleDegree(detailDO.getHandleDegree() == null ? null : detailDO.getHandleDegree().getCode());
            response.setCategory(detailDO.getCategory());
            response.setConditionDesc(detailDO.getConditionDesc());

            response.setInternetNickname(
                    detailDO.getExtInfo() == null ? null : detailDO.getExtInfo().getInternetNickname());
            response.setContactInfo(detailDO.getExtInfo() == null ? null : detailDO.getExtInfo().getContactInfo());
            response.setAccidentOrderLink(
                    detailDO.getExtInfo() == null ? null : detailDO.getExtInfo().getAccidentOrderLink());

            response.setCheckResult(detailDO.getCheckResult());
            response.setHandleResultDesc(detailDO.getHandleResultDesc());
            response.setReviewInfo(detailDO.getReviewInfo());
            response.setOtherReasonDesc(detailDO.getOtherReasonDesc());
            response.setOtherCategoryDesc(detailDO.getOtherCategoryDesc());
            response.setTitle(detailDO.getTitle());

            buildOperationRecordList(detailDO, operationRecordList);
        }
        response.setOperationRecordList(operationRecordList);
        // NEGATIVE_PUBLIC_EVENT_RELATED_DO -> response  从负外部性事件关联表中获取信息
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(negativePublicEventDO.getEventId()).build();
        List<NegativePublicEventRelatedDO> historyRelatedDOList = negativePublicEventRelatedRepository.queryByParam(
                paramDTO);
        Map<String, List<String>> fieldValueMap = buildFieldName2ValueListMap(historyRelatedDOList);
        Map<String, Long> fieldName2IdMap = historyRelatedDOList.stream()
                .collect(Collectors.toMap(
                        relatedDO -> relatedDO.getFieldName() + "_" + relatedDO.getFieldValue(),
                        NegativePublicEventRelatedDO::getId,
                        (existing, replacement) -> existing
                ));
        if (CollectionUtils.isNotEmpty(fieldValueMap)) {
            // 按照字段名分组，构建 字段名称-字段取值的映射关系
            response.setRelatedFileLinks(
                    fieldValueMap.get(NegativePublicEventRelatedFieldEnum.RELATED_FILE_LINKS.getName()));
            response.setHandlers(fieldValueMap.get(NegativePublicEventRelatedFieldEnum.HANDLERS.getName()));
            response.setRelatedVins(fieldValueMap.get(NegativePublicEventRelatedFieldEnum.RELATED_VINS.getName()));
            List<String> sourceListStr = fieldValueMap.get(NegativePublicEventRelatedFieldEnum.SOURCE.getName());
            response.setSourceList(sourceListStr.stream().map(Integer::valueOf).collect(Collectors.toList()));
            response.setReasonList(fieldValueMap.get(NegativePublicEventRelatedFieldEnum.REASON.getName()));
            response.setHandleResultDescFileLinkList(
                    fieldValueMap.get(NegativePublicEventRelatedFieldEnum.HANDLE_RESULT_DESC_FILE_LINK.getName()));
            response.setConditionDescRelatedFileLinkList(
                    fieldValueMap.get(NegativePublicEventRelatedFieldEnum.CONDITION_DESC_RELATED_FILE_LINKS.getName()));
        }

        return response;
    }

    /**
     * 更新状态
     *
     * @param detailDO
     */
    public void updateStatus(NegativePublicEventDO eventDO, NegativePublicEventDetailDO detailDO,
            NegativePublicEventStateMachineDTO oldStateMachineDTO,
            NegativePublicEventStateMachineDTO newStateMachineDTO,
            List<Long> deleteIdList) {
        // 加载状态机流转规则配置 -  TODO: 核心配置要有默认值才行
        if (CollectionUtils.isEmpty(stateMachineTransitionRuleConfig)) {
            throw new RuntimeException("状态流程配置为空");
        }
        // 构建规则和状态的映射关系
        Map<String, Integer> rule2StatusMap = new ConcurrentHashMap<>();
        List<String> stateMachineTransitionRules = new CopyOnWriteArrayList<>();
        for (Map.Entry<Integer, List<String>> entry : stateMachineTransitionRuleConfig.entrySet()) {
            Integer status = entry.getKey();
            List<String> rules = entry.getValue();
            rules.forEach(rule -> rule2StatusMap.put(rule, status));
            stateMachineTransitionRules.addAll(rules);
        }

        // 计算状态流转
        Map<String, Object> context = new HashMap<>();
        context.put("oldData", oldStateMachineDTO);
        context.put("newData", newStateMachineDTO);
        String ruleName = getRuleName(stateMachineTransitionRules, context);

        NegativePublicEventStatusEnum statusEnum = eventDO.getStatus();
        // 命中规则即发生状态变更
        if (StringUtils.isNotBlank(ruleName) && rule2StatusMap.containsKey(ruleName)) {
            statusEnum = NegativePublicEventStatusEnum.fromCode(
                    rule2StatusMap.get(ruleName));
            // 当新状态小于老状态时发生状态回退
            if (statusEnum.getCode() < eventDO.getStatus().getCode()) {
                // 需要数据清除 - 清除掉定位状态之后的所有数据
                detailDO.clearAllInfoAfterDetermineNature();
                List<Long> idList = getIdListByEventId(eventDO.getEventId());
                deleteIdList.addAll(idList);
            }
        }

        // 无论是否发生状态变更，都要判断是否发生状态跳跃
        if (Objects.equals(statusEnum, NegativePublicEventStatusEnum.LOCATED) && Objects.equals(
                detailDO.getNature().getGroup(), EventGroup.OTHER)) {
            statusEnum = NegativePublicEventStatusEnum.DISPOSED;
        }
        // 更新状态
        eventDO.setStatus(statusEnum);
    }

    /**
     * 计算状态流转规则- 判断当前变更是否发生状态流转
     *
     * @param stateMachineTransitionRules
     * @param context
     * @return
     */
    private String getRuleName(List<String> stateMachineTransitionRules, Map<String, Object> context) {
        if (CollectionUtils.isEmpty(stateMachineTransitionRules)) {
            return CharConstant.CHAR_EMPTY;
        }
        // 不满足准入条件
        String ruleName = stateMachineTransitionRules.stream()
                //做逻辑判断
                .filter(filter -> SpELUtil.evaluateBoolean(filter, context)).findFirst()
                //过滤
                .orElse(CharConstant.CHAR_EMPTY);
        log.info("getRuleName ruleName:{}", ruleName);
        return ruleName;
    }

    /**
     * 构建列表页查询参数
     *
     * @param request
     * @return
     */
    private NegativePublicEventDOQueryParamDTO buildNegativePublicEventDOQueryParam(
            NegativePublicEventListRequest request) {
        NegativePublicEventDOQueryParamDTO paramDTO = NegativePublicEventDOQueryParamDTO.builder()
                .orderByCreateTime(OrderEnum.DESC)
                .createTimeCreateTo(DatetimeUtil.parseDate(request.getCreateTimeStart(),
                        DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND))
                .createTimeBelowTo(DatetimeUtil.parseDate(request.getCreateTimeEnd(),
                        DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND)).build();

        if (CollectionUtils.isNotEmpty(request.getStatusList())) {
            paramDTO.setStatusList(request.getStatusList());
        }
        paramDTO.setLeftJoinEventDetail(true);

        if (StringUtils.isNotBlank(request.getTitle())) {
            paramDTO.setTitle(request.getTitle());
        }

        if (CollectionUtils.isNotEmpty(request.getNatureList())) {
            paramDTO.setNatureList(request.getNatureList());
        }

        if (CollectionUtils.isNotEmpty(request.getTypeList())) {
            paramDTO.setEventTypeList(request.getTypeList());
        }

        if (CollectionUtils.isNotEmpty(request.getCategoryList())) {
            paramDTO.setCategoryList(request.getCategoryList());
        }

        if (CollectionUtils.isNotEmpty(request.getLevelList())) {
            paramDTO.setLevelList(request.getLevelList());
        }

        List<String> fieldNameList = new ArrayList<>();
        List<String> fieldValueList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(request.getReasonList())) {
            fieldNameList.add(NegativePublicEventRelatedFieldEnum.REASON.getName());
            fieldValueList.addAll(request.getReasonList().stream()
                    .map(Object::toString)
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(request.getSourceList())) {
            fieldNameList.add(NegativePublicEventRelatedFieldEnum.SOURCE.getName());
            fieldValueList.addAll(request.getSourceList().stream()
                    .map(Object::toString)
                    .collect(Collectors.toList()));
        }

        if (CollectionUtils.isNotEmpty(request.getVinList())) {
            fieldNameList.add(NegativePublicEventRelatedFieldEnum.RELATED_VINS.getName());
            fieldValueList.addAll(request.getVinList().stream()
                    .map(Object::toString)
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(fieldNameList) && CollectionUtils.isNotEmpty(fieldValueList)) {
            paramDTO.setFieldNameList(fieldNameList);
            paramDTO.setFieldValueList(fieldValueList);
            paramDTO.setLeftJoinEventRelated(true);
        }

        return paramDTO;
    }

    /**
     * 清除关联表中的定因信息
     *
     * @param eventId
     */
    private List<Long> getIdListByEventId(String eventId) {
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(eventId)
                .fieldName(NegativePublicEventRelatedFieldEnum.REASON.getName()).build();
        List<NegativePublicEventRelatedDO> relatedDOList = negativePublicEventRelatedRepository.queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(relatedDOList)) {
            return new ArrayList<>();
        }
        return relatedDOList.stream().map(NegativePublicEventRelatedDO::getId).collect(Collectors.toList());
    }

    /**
     * 构建字段名称-字段取值列表的映射关系
     *
     * @param relatedDOList
     * @return
     */
    private Map<String, List<String>> buildFieldName2ValueListMap(List<NegativePublicEventRelatedDO> relatedDOList) {
        return relatedDOList.stream()
                .collect(Collectors.groupingBy(NegativePublicEventRelatedDO::getFieldName,
                        Collectors.mapping(NegativePublicEventRelatedDO::getFieldValue, Collectors.toList())));

    }

    /**
     * 构建基本信息关联DO对象
     *
     * @param eventDO
     * @param request
     * @return
     */
    private List<NegativePublicEventRelatedDO> buildBaseInfoRelatedDO(NegativePublicEventDO eventDO,
            NegativePublicEventBaseInfoUpdateRequest request) {
        // 查询eventId关联的相关历史信息
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(eventDO.getEventId())
                .fieldNameList(Arrays.asList(NegativePublicEventRelatedFieldEnum.SOURCE.getName()
                        , NegativePublicEventRelatedFieldEnum.RELATED_FILE_LINKS.getName())).build();
        List<NegativePublicEventRelatedDO> historyRelatedDOList = negativePublicEventRelatedRepository.queryByParam(
                paramDTO);
        Map<String, List<String>> fieldValueMap = buildFieldName2ValueListMap(historyRelatedDOList);
        Map<String, Long> fieldName2IdMap = historyRelatedDOList.stream()
                .collect(Collectors.toMap(
                        relatedDO -> relatedDO.getFieldName() + "_" + relatedDO.getFieldValue(),
                        NegativePublicEventRelatedDO::getId,
                        (existing, replacement) -> existing
                ));

        // 批量保存相关文件链接信息
        List<NegativePublicEventRelatedDO> relatedDOList = new ArrayList<>();
        List<Long> deletedIdList = new ArrayList<>();

        // 来源信息
        if (Objects.nonNull(request.getSourceList())) {

            List<NegativePublicEventSourceEnum> currentSourceEnumList = NegativePublicEventSourceEnum.getEnumByCode(
                    request.getSourceList());
            List<String> sourceList = fieldValueMap.getOrDefault(NegativePublicEventRelatedFieldEnum.SOURCE.getName(),
                    new ArrayList<>());
            List<NegativePublicEventSourceEnum> historySourceEnumList = sourceList.stream()
                    .map(source -> {
                        Integer sourceCode = Integer.valueOf(source);
                        return NegativePublicEventSourceEnum.fromCode(sourceCode);
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 计算diff
            ListDiffDTO<NegativePublicEventSourceEnum> diffDTO = DiffUtils.getListDiff(historySourceEnumList,
                    currentSourceEnumList);
            // 保存新增
            if (CollectionUtils.isNotEmpty(diffDTO.getAdd())) {
                diffDTO.getAdd().stream()
                        .filter(Objects::nonNull)
                        .forEach(sourceEnum -> {
                            NegativePublicEventRelatedDO relatedDO = new NegativePublicEventRelatedDO();
                            relatedDO.setEventId(eventDO.getEventId());
                            relatedDO.setFieldName(NegativePublicEventRelatedFieldEnum.SOURCE.getName());
                            relatedDO.setFieldValue(String.valueOf(sourceEnum.getCode()));
                            relatedDOList.add(relatedDO);
                        });
            }

            if (CollectionUtils.isNotEmpty(diffDTO.getDelete())) {
                List<Long> deleteIdList = diffDTO.getDelete().stream()
                        .map(fieldValue -> fieldName2IdMap.get(
                                NegativePublicEventRelatedFieldEnum.RELATED_FILE_LINKS.getName() + "_" + fieldValue))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                deletedIdList.addAll(deleteIdList);

            }
        }

        // 文件链接相关
        if (Objects.nonNull(request.getRelatedFileLinks())) {

            // 查询历史关联的文件链接信息
            List<String> historyRelatedFileLinks = fieldValueMap.getOrDefault(
                    NegativePublicEventRelatedFieldEnum.RELATED_FILE_LINKS.getName(), new ArrayList<>());

            // 计算字段新旧值之间的变更
            handleRelatedDiff(historyRelatedFileLinks, request.getRelatedFileLinks(), request.getEventId(),
                    fieldName2IdMap, relatedDOList, NegativePublicEventRelatedFieldEnum.RELATED_FILE_LINKS,
                    deletedIdList);
        }
        negativePublicEventRelatedRepository.batchDeleteRelatedDO(deletedIdList);
        return relatedDOList;
    }

    /**
     * 构建标题
     *
     * @param request
     * @param sourceEnumList
     * @return
     */
    private String buildTitle(NegativePublicEventCreateRequest request,
            List<NegativePublicEventSourceEnum> sourceEnumList) {
        LocalDate now = LocalDate.now();
        // 构建日期
        String dateStr = String.format("%02d%02d", now.getMonthValue(), now.getDayOfMonth());
        String cityStr = request.getCity();
        // 构建来源
        String sourceStr = sourceEnumList.stream().map(NegativePublicEventSourceEnum::getDesc)
                .collect(Collectors.joining("/"));
        return String.join("-", dateStr, cityStr, sourceStr);
    }

    /**
     * 处理关联表字段DIFF
     *
     * @param historyList
     * @param currentList
     * @param eventId
     * @param fieldName2IdMap
     * @param relatedDOList
     * @param fieldEnum
     * @param deletedIdList
     */
    private void handleRelatedDiff(List<String> historyList,
            List<String> currentList,
            String eventId,
            Map<String, Long> fieldName2IdMap,
            List<NegativePublicEventRelatedDO> relatedDOList,
            NegativePublicEventRelatedFieldEnum fieldEnum,
            List<Long> deletedIdList) {
        // 计算DIFF
        ListDiffDTO<String> diffDTO = DiffUtils.getListDiff(historyList, currentList);

        // 保存新增
        if (CollectionUtils.isNotEmpty(diffDTO.getAdd())) {
            diffDTO.getAdd().stream()
                    .filter(StringUtils::isNotBlank)
                    .forEach(link -> {
                        NegativePublicEventRelatedDO relatedDO = new NegativePublicEventRelatedDO();
                        relatedDO.setEventId(eventId);
                        relatedDO.setFieldName(fieldEnum.getName());
                        relatedDO.setFieldValue(link);
                        relatedDOList.add(relatedDO);
                    });
        }
        // 保存需要删除的 id 列表
        if (CollectionUtils.isNotEmpty(diffDTO.getDelete())) {
            List<Long> deleteIdList = diffDTO.getDelete().stream()
                    .map(fieldValue -> fieldName2IdMap.get(fieldEnum.getName() + "_" + fieldValue))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            deletedIdList.addAll(deleteIdList);
        }
    }


    /**
     * 构建关联DO列表
     *
     * @param relatedDOList
     * @param deleteIdList
     * @param request
     */
    private void buildNatureRelatedDOList(List<NegativePublicEventRelatedDO> relatedDOList, List<Long> deleteIdList,
            NegativePublicEventDetermineNatureInfoUpdateRequest request) {
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(request.getEventId())
                .fieldNameList(Arrays.asList(NegativePublicEventRelatedFieldEnum.HANDLERS.getName()
                        , NegativePublicEventRelatedFieldEnum.CONDITION_DESC_RELATED_FILE_LINKS.getName()
                        , NegativePublicEventRelatedFieldEnum.RELATED_VINS.getName())).build();
        List<NegativePublicEventRelatedDO> historyRelatedDOList = negativePublicEventRelatedRepository.queryByParam(
                paramDTO);
        Map<String, List<String>> fieldValueMap = buildFieldName2ValueListMap(historyRelatedDOList);
        Map<String, Long> fieldName2IdMap = historyRelatedDOList.stream()
                .collect(Collectors.toMap(
                        relatedDO -> relatedDO.getFieldName() + "_" + relatedDO.getFieldValue(),
                        NegativePublicEventRelatedDO::getId,
                        (existing, replacement) -> existing
                ));

        // 关联的处置人链接
        if (Objects.nonNull(request.getHandlers())) {
            // 获取历史处置人列表，如果不存在则返回一个空的列表
            List<String> historyHandlers = fieldValueMap.getOrDefault(
                    NegativePublicEventRelatedFieldEnum.HANDLERS.getName(), new ArrayList<>());
            // 处理关联差异
            handleRelatedDiff(historyHandlers, request.getHandlers(), request.getEventId(), fieldName2IdMap,
                    relatedDOList,
                    NegativePublicEventRelatedFieldEnum.HANDLERS, deleteIdList);
        }
        // 关联的情况描述链接
        if (Objects.nonNull(request.getConditionDescRelatedFileLinkList())) {
            // 获取历史关联文件链接列表，如果不存在则返回一个空的列表
            List<String> historyRelatedFilesLinks = fieldValueMap.getOrDefault(
                    NegativePublicEventRelatedFieldEnum.CONDITION_DESC_RELATED_FILE_LINKS.getName(),
                    new ArrayList<>());

            // 处理关联差异
            handleRelatedDiff(historyRelatedFilesLinks, request.getConditionDescRelatedFileLinkList(),
                    request.getEventId(),
                    fieldName2IdMap, relatedDOList,
                    NegativePublicEventRelatedFieldEnum.CONDITION_DESC_RELATED_FILE_LINKS, deleteIdList);
        }
        // 关联的车架号信息
        if (Objects.nonNull(request.getRelatedVins())) {
            // 获取历史关联的车架号列表，如果不存在则返回一个空的列表
            List<String> historyRelatedVins = fieldValueMap.getOrDefault(
                    NegativePublicEventRelatedFieldEnum.RELATED_VINS.getName(), new ArrayList<>());
            // 处理关联差异
            handleRelatedDiff(historyRelatedVins, request.getRelatedVins(), request.getEventId(), fieldName2IdMap,
                    relatedDOList,
                    NegativePublicEventRelatedFieldEnum.RELATED_VINS, deleteIdList);
        }

    }

    /**
     * 构建归因关联DO列表
     *
     * @param relatedDOList
     * @param deleteIdList
     * @param request
     */
    private void buildReasonRelatedDOList(List<NegativePublicEventRelatedDO> relatedDOList, List<Long> deleteIdList,
            NegativePublicEventDetermineReasonInfoUpdateRequest request) {
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(request.getEventId())
                .fieldName(NegativePublicEventRelatedFieldEnum.REASON.getName()).build();
        List<NegativePublicEventRelatedDO> historyRelatedDOList = negativePublicEventRelatedRepository.queryByParam(
                paramDTO);
        Map<String, List<String>> fieldValueMap = buildFieldName2ValueListMap(historyRelatedDOList);
        Map<String, Long> fieldName2IdMap = historyRelatedDOList.stream()
                .collect(Collectors.toMap(
                        relatedDO -> relatedDO.getFieldName() + "_" + relatedDO.getFieldValue(),
                        NegativePublicEventRelatedDO::getId,
                        (existing, replacement) -> existing
                ));
        // 问题归因 reason 字段需要支持检索，所以放在关联表里进行存储
        if (Objects.nonNull(request.getReasonList())) {
            List<String> reasonList = fieldValueMap.getOrDefault(
                    NegativePublicEventRelatedFieldEnum.REASON.getName(), new ArrayList<>());

            // 处理关联差异
            handleRelatedDiff(reasonList, request.getReasonList(),
                    request.getEventId(),
                    fieldName2IdMap, relatedDOList,
                    NegativePublicEventRelatedFieldEnum.REASON, deleteIdList);
        }
    }

    /**
     * 构建处理结果关联DO列表
     *
     * @param relatedDOList
     * @param deleteIdList
     * @param request
     */
    private void buildHandleRelatedDOList(List<NegativePublicEventRelatedDO> relatedDOList, List<Long> deleteIdList,
            NegativePublicEventHandleInfoUpdateRequest request) {
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(request.getEventId())
                .fieldName(NegativePublicEventRelatedFieldEnum.HANDLE_RESULT_DESC_FILE_LINK.getName()).build();
        List<NegativePublicEventRelatedDO> historyRelatedDOList = negativePublicEventRelatedRepository.queryByParam(
                paramDTO);
        Map<String, List<String>> fieldValueMap = buildFieldName2ValueListMap(historyRelatedDOList);
        Map<String, Long> fieldName2IdMap = historyRelatedDOList.stream()
                .collect(Collectors.toMap(
                        relatedDO -> relatedDO.getFieldName() + "_" + relatedDO.getFieldValue(),
                        NegativePublicEventRelatedDO::getId,
                        (existing, replacement) -> existing
                ));
        // 问题归因 reason 字段需要支持检索，所以放在关联表里进行存储
        if (Objects.nonNull(request.getHandleResultDescFileLinkList())) {
            List<String> reasonList = fieldValueMap.getOrDefault(
                    NegativePublicEventRelatedFieldEnum.HANDLE_RESULT_DESC_FILE_LINK.getName(), new ArrayList<>());

            // 处理关联差异
            handleRelatedDiff(reasonList, request.getHandleResultDescFileLinkList(),
                    request.getEventId(),
                    fieldName2IdMap, relatedDOList,
                    NegativePublicEventRelatedFieldEnum.HANDLE_RESULT_DESC_FILE_LINK, deleteIdList);
        }
    }

    /**
     * 构建操作记录列表
     *
     * @param detailDO
     * @param operationRecordList
     */
    private void buildOperationRecordList(NegativePublicEventDetailDO detailDO,
            List<NegativePublicEventOperationRecordVO> operationRecordList) {
        if (Objects.isNull(detailDO)) {
            return;
        }
        // 定位信息
        if (StringUtils.isNotBlank(detailDO.getDetermineNatureOperator())) {
            operationRecordList.add(NegativePublicEventOperationRecordVO.builder()
                    .operator(detailDO.getDetermineNatureOperator())
                    .operationTime(DatetimeUtil.formatTime(detailDO.getDetermineNatureTime()))
                    .status(NegativePublicEventStatusEnum.LOCATED.getCode())
                    .statusDes(NegativePublicEventStatusEnum.LOCATED.getDesc())
                    .build());
        }

        // 定因信息
        if (StringUtils.isNotBlank(detailDO.getDetermineReasonOperator())) {
            operationRecordList.add(NegativePublicEventOperationRecordVO.builder()
                    .operator(detailDO.getDetermineReasonOperator())
                    .operationTime(DatetimeUtil.formatTime(detailDO.getDetermineReasonTime()))
                    .status(NegativePublicEventStatusEnum.CAUSE_IDENTIFIED.getCode())
                    .statusDes(NegativePublicEventStatusEnum.CAUSE_IDENTIFIED.getDesc())
                    .build());
        }

        // 处置信息
        if (StringUtils.isNotBlank(detailDO.getCompleteOperator())) {
            operationRecordList.add(NegativePublicEventOperationRecordVO.builder()
                    .operator(detailDO.getCompleteOperator())
                    .operationTime(DatetimeUtil.formatTime(detailDO.getCompleteTime()))
                    .status(NegativePublicEventStatusEnum.DISPOSED.getCode())
                    .statusDes(NegativePublicEventStatusEnum.DISPOSED.getDesc())
                    .build());
        }
    }


}
