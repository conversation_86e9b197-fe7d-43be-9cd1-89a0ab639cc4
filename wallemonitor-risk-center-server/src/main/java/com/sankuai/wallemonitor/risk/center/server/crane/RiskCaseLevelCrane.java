package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

@Slf4j
@CraneConfiguration
public class RiskCaseLevelCrane {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Crane(CraneConstant.RISK_CASE_LEVEL_CRANE)
    @OperateEnter(OperateEnterActionEnum.RISK_LEVEL_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("RiskCaseLevelCrane running");
        try {
            // 1 查询n分钟内的未完成且未呼叫云控的风险事件
            Map<Integer, Map<Integer, String>> riskLevelConfig = lionConfigRepository.getRiskLevelConfig();
            log.info("ReportRiskCaseCrane# queryRiskCaseDispose，riskCaseDOList = {}", riskLevelConfig);
            if (MapUtils.isEmpty(riskLevelConfig)) {
                log.info("ReportRiskCaseCrane end");
                return;
            }
            Map<RiskCaseTypeEnum, List<RiskCaseDO>> riskCaseDOMap = riskCaseRepository.queryByParam(
                    RiskCaseDOQueryParamDTO.builder()
                            .caseTypeList(Lists.newArrayList(riskLevelConfig.keySet()))
                            .statusList(RiskCaseStatusEnum.getUnTerminal())
                            .build()).stream().collect(Collectors.groupingBy(RiskCaseDO::getType));
            if (MapUtils.isEmpty(riskCaseDOMap)) {
                log.info("ReportRiskCaseCrane end");
                return;
            }
            for (Map.Entry<RiskCaseTypeEnum, List<RiskCaseDO>> entry : riskCaseDOMap.entrySet()) {
                RiskCaseTypeEnum caseTypeEnum = entry.getKey();
                List<RiskCaseDO> riskCaseDOList = entry.getValue();
                if (CollectionUtils.isEmpty(riskCaseDOList)) {
                    continue;
                }
                Map<Integer, String> levelConfig = riskLevelConfig.get(caseTypeEnum.getCode());
                if (MapUtils.isEmpty(levelConfig)) {
                    continue;
                }
                List<RiskCaseDO> updateRiskCaseDOList = riskCaseDOList.stream()
                        .peek(riskCaseDO -> this.handleUpdateRiskCaseDO(riskCaseDO, levelConfig))
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toList());
                riskCaseRepository.batchSave(updateRiskCaseDOList);
            }
        } catch (Exception e) {
            log.error("ReportRiskCaseCrane error", e);
        }
    }

    /**
     * 更新
     *
     * @param riskCaseDO
     */
    private void handleUpdateRiskCaseDO(RiskCaseDO riskCaseDO, Map<Integer, String> levelConfig) {
        if (riskCaseDO == null) {
            return;
        }
        if (MapUtils.isEmpty(levelConfig)) {
            return;
        }
        levelConfig.forEach((level, config) -> {
            try {
                if (riskCaseDO.getLevel() != null && riskCaseDO.getLevel() >= level) {
                    return;
                }
                Map<String, Object> context = new HashMap<>();
                context.put("case", riskCaseDO);
                if (SpELUtil.evaluateBoolean(config, context)) {
                    riskCaseDO.setLevel(level);
                }
            } catch (Exception e) {
                log.error("设置优先级异常", e);
            }
        });

    }


}
