package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.mtrace.instrument.util.JsonUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.OperationDataAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.SafetyAreaInfoSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO.Polygon;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.OperationDataVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.OperationDataVTO.PointUtm;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 停车区域范围数据同步
 */
@Slf4j
@CraneConfiguration
public class ParkingAreaInfoSyncCrane {

    private static final Integer DEFAULT_PAGE_SIZE = 400;

    @Resource
    private OperationDataAdapter operationDataAdapter;

    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Crane(CraneConstant.PARKING_AREA_INFO_SYNC_CRANE)
    public void run() throws Exception {
        log.info("同步停车区域范围数据定时任务开始执行");
        try {
            List<String> operationNameList = lionConfigRepository.getAreaOperateNameList();
            if (CollectionUtils.isEmpty(operationNameList)) {
                //无区域
                return;
            }
            /** 1. 查询区域适配数据总数 */
            int totalCount = operationDataAdapter.getOperationDataTotal(
                    operationNameList);
            int pageCount = (totalCount + DEFAULT_PAGE_SIZE - 1) / DEFAULT_PAGE_SIZE;

            for (int pageIndex = 1; pageIndex <= pageCount; pageIndex++) {
                /** 2. 分批次查询区域适配数据 */
                List<OperationDataVTO> operationDataVTOList = operationDataAdapter
                        .getOperationDataByPage(operationNameList,
                                pageIndex, DEFAULT_PAGE_SIZE);
                List<SafetyAreaDO> safetyAreaDOList = batchConvert2SafeAreaDO(operationDataVTOList);

                /** 3. 仓储保存有变化的 */
                safetyAreaRepository.batchSave(safetyAreaDOList);
            }
        } catch (Exception e) {
            log.error("同步停车区域范围数据定时任务异常", e);
        }
    }

    private List<SafetyAreaDO> batchConvert2SafeAreaDO(List<OperationDataVTO> operationDataVTOList) {
        List<SafetyAreaDO> safetyAreaDOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(operationDataVTOList)) {
            return safetyAreaDOList;
        }
       operationDataVTOList.forEach(operationDataVTO -> {
           if (null == operationDataVTO) {
               return;
           }
           try {
               SafetyAreaDO safetyAreaDO = convert2SafeAreaDO(operationDataVTO);
               safetyAreaDOList.add(safetyAreaDO);
           } catch (Exception e) {
               log.error("convert error! operationDataVTO:{}", JsonUtil.serialize(operationDataVTO), e);
           }
       });
        return safetyAreaDOList;
    }

    private SafetyAreaDO convert2SafeAreaDO(OperationDataVTO operationDataVTO) {
        return SafetyAreaDO.builder()
                .areaId(operationDataVTO.getId())
                .description(operationDataVTO.getDescription())
                .type(operationDataVTO.getContent().getType())
                .polygon(convert2SafetyAreaPolygon(operationDataVTO.getContent().getPolygon()))
                .extInfo(operationDataVTO.convert2SafetyAreaExtInfoDO())
                .source(SafetyAreaInfoSourceEnum.MENDER_PORTAL)
                .build();
    }

    private SafetyAreaDO.Polygon convert2SafetyAreaPolygon(OperationDataVTO.Polygon polygon) {
        SystemCheckUtil.isNotNull(polygon, "convert2SafetyAreaPolygon, polygon不能为空");
        List<PointUtm> pointUtmList = polygon.getPointUtmList();
        SystemCheckUtil.isNotEmpty(pointUtmList, "convert2SafetyAreaPolygon, pointUtmList不能为空");

        List<PositionDO> pointGcjList = new ArrayList<>();
        pointUtmList.forEach(pointUtm -> {
            SystemCheckUtil.isNotNull(pointUtm, "convert2SafetyAreaPolygon, pointUtm不能为空");
            Double utmX = pointUtm.getX();
            Double utmY = pointUtm.getY();
            PositionDO positionDO = GeoToolsUtil.utmToGcj02(utmX, utmY);
            pointGcjList.add(positionDO);
        });
        return Polygon.builder().pointGcjList(pointGcjList).build();
    }

}
