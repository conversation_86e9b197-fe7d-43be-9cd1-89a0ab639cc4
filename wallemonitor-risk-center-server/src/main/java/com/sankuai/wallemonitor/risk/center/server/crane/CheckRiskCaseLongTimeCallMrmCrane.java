package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.EventPlatSearchAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.CancelCallMrmConditionConfig;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@CraneConfiguration
public class CheckRiskCaseLongTimeCallMrmCrane {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private EventPlatSearchAdapter eventPlatSearchAdapter;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private LockUtils lockUtils;

    /**
     * 取消呼叫云控条件配置
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_CANCEL_CALL_MRM_CONDITION_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private CancelCallMrmConditionConfig conditionConfig;

    @Crane(CraneConstant.CHECK_LONG_TIME_CALL_MRM_CRANE)
    @OperateEnter(OperateEnterActionEnum.CHECK_RISK_CASE_LONG_TIME_CALL_MRM_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("定时检查是否需要取消呼叫坐席");
        try {
            // 1 检查配置
            if (Objects.isNull(conditionConfig)) {
                log.error("CheckRiskCaseCallMrmStatusCrane", new SystemException("conditionConfig is invalid"));
                return;
            }

            Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> releaseMrmStrategyConfigDTOMap =
                    lionConfigRepository.getReleaseMrmStrategyConfigDTO();

            // 2 查询n分钟内的未完成且已呼叫云控的风险事件
            RiskCaseDOQueryParamDTO riskCaseDOQueryParamDTO = RiskCaseDOQueryParamDTO.builder()
                    //只检查呼叫中且未取消的
                    .mrmCalledList(Arrays.asList(RiskCaseMrmCalledStatusEnum.CALLING.getCode()))
                    // todo: 只关注10086的呼叫，callMrmReason 为默认值
                    .callMrmReasonList(Arrays.asList("", "10086"))
                    .leftJoinRelation(true)
                    .createTimeCreateTo(
                            DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES,
                                    conditionConfig.getCheckDuration()))
                    .build();
            List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(riskCaseDOQueryParamDTO)
                    .stream().filter(riskCaseDO ->
                            releaseMrmStrategyConfigDTOMap.containsKey(new Pair<>(
                                    riskCaseDO.getSource().getCode(),
                                    riskCaseDO.getType().getCode()))
                    ).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                log.info("CheckRiskCaseCallMrmStatusCrane, riskCaseDOList is empty");
                return;
            }
            log.info("CheckRiskCaseCallMrmStatusCrane, riskCaseDOList:{}", riskCaseDOList);
            // 3 查询风险事件关联的车信息
            List<String> caseIdList = riskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
            RiderCaseVehicleRelationDOParamDTO paramDTO = RiderCaseVehicleRelationDOParamDTO.builder()
                    .caseIdList(caseIdList)
                    .build();
            List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                    paramDTO);
            Map<String, RiskCaseVehicleRelationDO> caseIdToRiskCaseVehicleRelationDOMap = riskCaseVehicleRelationDOList.stream()
                    .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, Function.identity(),
                            (existing, replacement) -> existing));


            // 4 判断是否取消呼叫
            riskCaseDOList.forEach(riskCaseDO -> {
                RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = caseIdToRiskCaseVehicleRelationDOMap.get(
                        riskCaseDO.getCaseId());
                if (Objects.isNull(riskCaseVehicleRelationDO)) {
                    log.error(String.format(
                            "CheckRiskCaseCallMrmStatusCrane, riskCaseVehicleRelationDO is null, caseId:%s",
                            riskCaseDO.getCaseId()), new IllegalArgumentException());
                    return;
                }
                String vin = riskCaseVehicleRelationDO.getVin();
                lockUtils.batchLockNoWait(
                        //对eventId和车进行加锁
                        LockKeyPreUtil.buildEventIdAndVin(Collections.singleton(riskCaseDO.getEventId()),
                                Collections.singleton(vin)),
                        () -> {
                            RiskCaseDO thisRiskCaseDo = riskCaseRepository.getByCaseId(riskCaseDO.getCaseId());
                            if (Objects.isNull(thisRiskCaseDo)
                                    //如果已经取消呼叫
                                    || Objects.equals(thisRiskCaseDo.getMrmCalled(),
                                    RiskCaseMrmCalledStatusEnum.CANCEL)) {
                                return;
                            }
                            if (isCancelCallMrm(riskCaseDO, riskCaseVehicleRelationDO)) {
                                //取消呼叫
                                riskCaseOperateService.cancelCallMrm(vin, riskCaseDO, releaseMrmStrategyConfigDTOMap);
                            }
                        });
            });
        } catch (Exception e) {
            log.error("定时检查是否需要取消呼叫坐席异常", e);
        }


    }

    /**
     * 判断是否取消呼叫
     *
     * @param riskCaseDO
     * @return
     */
    private Boolean isCancelCallMrm(RiskCaseDO riskCaseDO, RiskCaseVehicleRelationDO riskCaseVehicleRelationDO) {
        // 计算呼叫时间
        long callDuration = DatetimeUtil.getTimeDiff(riskCaseVehicleRelationDO.getRequestSeatTime(), new Date(),
                ChronoUnit.MINUTES);
        // 查询坐席呼叫次数
        Long mrmConnectCount = eventPlatSearchAdapter.queryMrmConnectionCountInTimeRange(
                riskCaseVehicleRelationDO.getVin(),
                riskCaseVehicleRelationDO.getRequestSeatTime().getTime(), new Date().getTime());
        mrmConnectCount = Objects.isNull(mrmConnectCount) ? 0L : mrmConnectCount;
        log.info("isCancelCallMrm, caseId:{}, callDuration:{}, mrmConnectCount:{}", riskCaseDO.getCaseId(),
                callDuration,
                mrmConnectCount);
        return callDuration > conditionConfig.getCallDuration()
                && mrmConnectCount > conditionConfig.getMrmConnectCount();
    }
}
