package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseTraceMappingMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskMappingParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
@Slf4j
public class RiskCaseTraceMappingConsumer {

    @Resource
    private RiskCaseOperateService riskCaseOperateService;


    @Resource
    private LockUtils lockUtils;

    @MafkaConsumer(
            namespace = "waimai", topic = "wallemonitor.risk.eventId.traceId.mapping",
            group = "wallemonitor.risk.eventId.traceId.mapping.consumer",
            deadLetter = true,
            deadLetterDelayMills = 6 * 1000
    )
    @OperateEnter(OperateEnterActionEnum.MAPPING_RISK_TRACE_CONSUMER_ENTER)
    public ConsumeStatus receive(String msg) {
        try {
            //解析消息
            EveMqCommonMessage<RiskCaseTraceMappingMessageDTO> message = JacksonUtils.from(msg,
                    new TypeReference<EveMqCommonMessage<RiskCaseTraceMappingMessageDTO>>() {
                    });
            if (message.getBody() == null || CollectionUtils.isEmpty(message.getBody().getEventIdList())
                    || StringUtils.isBlank(message.getBody().getVin())
            ) {
                log.warn("消费风险事件消息为空或者关联的eventId为空或者traceId为空",
                        new SystemException("消费风险事件消息为空或者关联的eventId为空或者traceId为空"));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            Set<String> vinSet = Sets.newHashSet(message.getBody().getVin());
            Set<String> eventIdSet = new HashSet<>(message.getBody().getEventIdList());
            //对车辆和事件加锁
            lockUtils.batchLockCanWait(LockKeyPreUtil.buildEventIdAndVin(eventIdSet, vinSet),
                    1,
                    TimeUnit.SECONDS,
                    () -> riskCaseOperateService.mappingRiskCase(RiskMappingParamDTO.builder()
                            .eventIdList(message.getBody().getEventIdList())
                            .vin(message.getBody().getVin())
                            .traceId(message.getBody().getTraceId())
                            .build()));
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (UnableGetLockException e) {
            log.warn("消费风险关联事件消息锁冲突失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        } catch (ParamInputErrorException e) {
            log.warn("消费风险事件消息业务正常失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        } catch (Throwable e) {
            log.error("消费风险事件消息异常失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        }
    }


}
