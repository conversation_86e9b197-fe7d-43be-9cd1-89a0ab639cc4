package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftUserAuthenticationService;
import com.sankuai.wallemonitor.risk.center.domain.component.WechatAuthAdminService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftUserAuthenticationServiceImpl implements IThriftUserAuthenticationService {

    @Resource
    private WechatAuthAdminService wechatAuthAdminService;

    /**
     * 根据微信code获取用户的唯一openId，并生成对应的登陆态token
     * @param code 微信code
     * @return 生成的登陆态token
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.GET_TOKEN)
    public EveThriftResponse<String> getToken(String code) {
        log.info("getToken, code = {}", code);
        CheckUtil.isNotBlank(code, "登陆凭证不可为空");
        return EveThriftResponse.ok(wechatAuthAdminService.getTokenByCode(code));
    }


}
