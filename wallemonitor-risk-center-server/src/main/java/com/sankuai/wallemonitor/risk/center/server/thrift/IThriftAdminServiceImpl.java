package com.sankuai.wallemonitor.risk.center.server.thrift;

/**
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON>
 * @Date 2024/7/2
 */

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftPage;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.AdminAddSafetyAreaRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminDeleteSafetyAreaRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminListActionChainResultRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminListRiskCaseRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminMarkRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminRiskLevelRequest;
import com.sankuai.wallemonitor.risk.center.api.request.CreateWorkstationCaseRequest;
import com.sankuai.wallemonitor.risk.center.api.request.ManualCallRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ManualHandleMrmCallRequest;
import com.sankuai.wallemonitor.risk.center.api.request.ManualRiskCaseOperateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.SearchAutoVehicleRequestVO;
import com.sankuai.wallemonitor.risk.center.api.response.AdminGetRiskCaseDetailResponse;
import com.sankuai.wallemonitor.risk.center.api.response.vo.ActionChainResultVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListRiskCaseVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.CreateWorkstationCaseVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseDisposeInfoVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseMarkInfoVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.SafetyAreaVO;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftAdminService;
import com.sankuai.wallemonitor.risk.center.api.vo.VehicleRuntimeVO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CloudCursorAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.WorkstationAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.CloudCursorResourceRequest;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.WorkstationCreateRequest;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.WorkstationCreateRequest.CaseExtension;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.CloudCursorResourceResponse;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.WorkstationCreateResponse.WorkstationCreateResponseData;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.UserInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.ActionChainResultLogConvert;
import com.sankuai.wallemonitor.risk.center.infra.convert.CaseMarkInfoConverter;
import com.sankuai.wallemonitor.risk.center.infra.convert.CaseSortDataConvert;
import com.sankuai.wallemonitor.risk.center.infra.convert.PositionConvert;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseConvert;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.DisposeInfoTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RelatedServiceNameEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskLevelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.SafetyAreaInfoSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CaseMarkInfoExtDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GeoQueryDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SafetyAreaExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.ActionChainResultLogDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseSortDataDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseRelatedServiceRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO.Polygon;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeLocationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.ActionChainResultLogRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseSortDataRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRelatedServiceRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoLocationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseSortDataDOQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.LogActionChainResultQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.LogActionChainResultQueryParamDTO.LogActionChainResultQueryParamDTOBuilder;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.SafetyAreaQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeLocationDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleBasicVTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftAdminServiceImpl implements IThriftAdminService {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private CaseMarkInfoConverter caseMarkInfoConverter;

    @Resource
    private CaseSortDataConvert sortDataConvert;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private RiskCaseConvert riskCaseConvert;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @Resource
    private CaseSortDataRepository sortDataRepository;

    @Resource
    private WorkstationAdapter workstationAdapter;

    @Resource
    private RiskCaseRelatedServiceRecordRepository riskCaseRelatedServiceRecordRepository;

    @Resource
    private PositionConvert positionDOConvert;

    @Resource
    private ActionChainResultLogRepository actionChainResultLogRepository;

    @Resource
    private ActionChainResultLogConvert actionChainResultLogConvert;

    @Value("${workstation.caseLink}")
    private String workstationCaseLinkTemplate;

    @Value("${beaconTower.caseLink}")
    private String beaconTowerCaseLinkTemplate;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private CloudCursorAdapter cloudCursorAdapter;

    @Resource
    private VehicleRuntimeInfoLocationRepository vehicleRuntimeInfoLocationRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;


    // TODO 上锁，避免并发操作
    @Override
    @OperateEnter(OperateEnterActionEnum.THRIFT_ADMIN_MARK_ENTRY)
    public EveThriftResponse<EmptyResponse> mark(AdminMarkRequest request) {
        RiskCaseDO riskCaseDO = riskCaseRepository.getByCaseId(request.getCaseId());
        CheckUtil.isNotNull(riskCaseDO, "未找到风险事件实体");
        Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(riskCaseDO.getEventId()));
        return lockUtils.batchLockCanWait(lockKeys, () -> {
            CaseMarkInfoDO caseMarkInfoDO = caseMarkInfoRepository.getByCaseId(request.getCaseId());
            log.info("caseMarkInfoDO, {}", caseMarkInfoDO);
            if (caseMarkInfoDO == null) {
                caseMarkInfoDO = CaseMarkInfoDO.builder()
                        .caseId(request.getCaseId())
                        .extInfo(CaseMarkInfoExtDO.builder().build())
                        .build();
            }
            // 进行标注
            caseMarkInfoDO.manualMark(request.getCategory(), request.getSubCategory(),
                    request.getDesc(),
                    StringUtils.defaultIfBlank(UserInfoContext.getUserMis(),
                            CommonConstant.MARK_OPERATOR_UNKNOWN));
            if (request.getLevel() != null) {
                caseMarkInfoDO.setRiskLevel(RiskLevelEnum.findByValue(request.getLevel()));
            }
            if (BooleanUtils.toBoolean(request.getCloseCase())) {
                this.manualFinishCase(Lists.newArrayList(request.getCaseId()));
            }
            caseMarkInfoRepository.save(caseMarkInfoDO);
            return EveThriftResponse.ok().build();
        });
    }

    @Override
    @OperateEnter(OperateEnterActionEnum.THRIFT_ADMIN_CHANGE_RISK_LEVEL)
    public EveThriftResponse<EmptyResponse> changeRiskLevel(AdminRiskLevelRequest request) {
        RiskCaseDO riskCaseDO = riskCaseRepository.getByCaseId(request.getCaseId());
        CheckUtil.isNotNull(riskCaseDO, "未找到风险事件实体");
        Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(riskCaseDO.getEventId()));
        return lockUtils.batchLockCanWait(lockKeys, () -> {
            CaseMarkInfoDO caseMarkInfoDO = caseMarkInfoRepository.getByCaseId(request.getCaseId());
            log.info("caseMarkInfoDO, {}", caseMarkInfoDO);
            if (caseMarkInfoDO == null) {
                caseMarkInfoDO = CaseMarkInfoDO.builder()
                        .caseId(request.getCaseId())
                        .extInfo(CaseMarkInfoExtDO.builder().build())
                        .build();
            }
            if (request.getLevel() != null) {
                caseMarkInfoDO.setRiskLevel(RiskLevelEnum.findByValue(request.getLevel()));
            }
            caseMarkInfoRepository.save(caseMarkInfoDO);
            return EveThriftResponse.ok().build();
        });
    }

    @Override
    public EveThriftResponse<List<String>> queryMarkVersionList() {
        return EveThriftResponse.ok(new ArrayList<>(lionConfigRepository.getAllAutoMarkConfig().keySet()));
    }

    @Override
    public EveThriftResponse<EmptyResponse> manualHandleMrmCall(ManualHandleMrmCallRequest request) {

        // 1 参数校验
        CheckUtil.isNotNull(request, "request不能为空");
        CheckUtil.isNotBlank(request.getVin(), "vin不能为空");
        CheckUtil.isNotBlank(request.getAction(), "action不能为空");
        CheckUtil.isNotNull(request.getReason(), "reason不能为空");
        CheckUtil.isNotNull(request.getNeedCancelCommand(), "needCancelCommand不能为空");

        // 2 调用云控
        CloudCursorResourceRequest resourceRequest = CloudCursorResourceRequest.builder()
                .action(request.getAction())
                .reason(request.getReason())
                .timestamp(System.currentTimeMillis())
                .vin(request.getVin())
                .source(CommonConstant.BEACON_TOWER_CALL_CLOUD_CONTROL_SOURCE)
                .needCancelCommand(request.getNeedCancelCommand())
                .build();
        CloudCursorResourceResponse response = cloudCursorAdapter.callCloudCursor(resourceRequest);

        // 3 结果处理
        if (response == null || !Objects.equals(response.getCode(), CommonConstant.HTTP_SUCCESS_CODE)) {
            return EveThriftResponse.failWithMessage("调用呼叫云控接口异常").build();
        }
        return EveThriftResponse.ok().build();
    }

    @Override
    @OperateEnter(OperateEnterActionEnum.THRIFT_ADMIN_GET_CASE_DETAIL_ENTRY)
    public EveThriftResponse<String> addSafetyArea(AdminAddSafetyAreaRequest request) {
        CheckUtil.isNotNull(request, "request不能为空");
        CheckUtil.isNotEmpty(request.getPolygon(), "区域不为空");
        String areaId = UUID.randomUUID().toString();
        List<PositionDO> polygon = request.getPolygon().stream().map(positionList -> PositionDO
                        .getPosition(positionList.get(0), positionList.get(1), CoordinateSystemEnum.GCJ02))
                .collect(Collectors.toList());

        SafetyAreaDO safetyAreaDO = SafetyAreaDO.builder()
                //
                .extInfo(SafetyAreaExtInfoDO.builder().build())
                //
                .areaId(areaId).description("烽火台添加，from:" + UserInfoContext.getUserMis())
                .source(SafetyAreaInfoSourceEnum.BEACON_TOWER).type("AutoParkingArea")
                //
                .polygon(Polygon.builder().pointGcjList(polygon).build()).build();
        safetyAreaRepository.batchSave(Lists.newArrayList(safetyAreaDO));
        return EveThriftResponse.ok(areaId);
    }

    @Override
    @OperateEnter(OperateEnterActionEnum.THRIFT_ADMIN_DELETE_SAFETY_AREA_ENTRY)
    public EveThriftResponse<EmptyResponse> deleteSafetyArea(AdminDeleteSafetyAreaRequest request) {
        CheckUtil.isNotNull(request, "request不能为空");
        List<String> areaId = request.getAreaId();
        List<SafetyAreaDO> safetyAreaDOList = safetyAreaRepository
                .queryByParam(SafetyAreaQueryParamDTO.builder().areaIdList(areaId).build());
        safetyAreaDOList.forEach(safetyAreaDO -> safetyAreaDO.setIsDeleted(IsDeleteEnum.DELETED));
        safetyAreaRepository.batchSave(Lists.newArrayList(safetyAreaDOList));
        return EveThriftResponse.ok(EmptyResponse.builder().build());
    }

    @Override
    public EveThriftResponse<List<VehicleRuntimeVO>> searchAutoVehicleList(SearchAutoVehicleRequestVO request) {
        // 判空
        CheckUtil.isNotNull(request, "request不能为空");
        CheckUtil.isNotNull(request.getRange(), "range不能为空");
        CheckUtil.isNotBlank(request.getLocation(), "坐标不能为空");
        List<Double> location = Arrays.stream(request.getLocation().split(CharConstant.CHAR_COMMA))
                .map(Double::parseDouble).collect(Collectors.toList());

        PositionDO positionDO = PositionDO.getPosition(location.get(0), location.get(1), CoordinateSystemEnum.WGS84);
        CheckUtil.isNotBlank(request.getLocation(), "坐标异常");
        // 查询车辆坐标
        List<VehicleRuntimeLocationDO> vehicleRuntimeInfoLocationDOList = vehicleRuntimeInfoLocationRepository
                .queryByParam(VehicleRuntimeLocationDOQueryParamDTO.builder()
                        .locationQuery(
                                GeoQueryDO.builder().distance(request.getRange()).point(positionDO.toPoint()).build())
                        .build());
        //
        if (CollectionUtils.isEmpty(vehicleRuntimeInfoLocationDOList)) {
            return EveThriftResponse.ok(Lists.newArrayList());
        }
        //
        List<String> vinList = vehicleRuntimeInfoLocationDOList.stream().map(VehicleRuntimeLocationDO::getVin)
                .collect(Collectors.toList());
        // 查询车辆基础信息
        Map<String, VehicleBasicVTO> basicMap = vehicleAdapter.queryVehicleBasicInfoList(vinList).stream()
                .collect(Collectors.toMap(VehicleBasicVTO::getVin, Function.identity(), (v1, v2) -> v1));
        List<VehicleRuntimeVO> vehicleRuntimeVOS = vehicleRuntimeInfoLocationDOList.stream()
                .map(vehicleRuntimeLocationDO -> {
                    VehicleBasicVTO vehicleBasicVTO = basicMap.get(vehicleRuntimeLocationDO.getVin());
                    return VehicleRuntimeVO.builder().vin(vehicleRuntimeLocationDO.getVin())
                            .vehicleId(Optional.ofNullable(vehicleBasicVTO).map(VehicleBasicVTO::getVehicleId)
                                    .orElse(CharConstant.CHAR_EMPTY))
                            .vehicleName(Optional.ofNullable(vehicleBasicVTO).map(VehicleBasicVTO::getVehicleName)
                                    .orElse(CharConstant.CHAR_EMPTY))
                            .position(vehicleRuntimeLocationDO.getLocation().getLocationStr(CoordinateSystemEnum.WGS84))
                            .build();
                }).collect(Collectors.toList());
        // 返回
        return EveThriftResponse.ok(vehicleRuntimeVOS);

    }

    @Override
    @OperateEnter(OperateEnterActionEnum.THRIFT_ADMIN_GET_CASE_DETAIL_ENTRY)
    public EveThriftResponse<AdminGetRiskCaseDetailResponse> getRiskCaseDetail(String caseId) {
        CheckUtil.isNotBlank(caseId, "caseId不能为空");

        // 获取基础信息
        RiskCaseDO caseDO = riskCaseRepository.getByCaseId(caseId);
        CheckUtil.isNotNull(caseDO, "未找到风险事件实体");
        // 获取关联车辆信息
        RiderCaseVehicleRelationDOParamDTO paramDTO = RiderCaseVehicleRelationDOParamDTO.builder()
                .caseId(caseId)
                .build();
        List<RiskCaseVehicleRelationDO> relationDOList = riskCaseVehicleRelationRepository.queryByParam(
                paramDTO);

        // 获取人工标注信息
        CaseMarkInfoDO caseMarkInfoDO = caseMarkInfoRepository.getByCaseId(caseId);

        AdminGetRiskCaseDetailResponse response = new AdminGetRiskCaseDetailResponse();
        response.setBase(riskCaseConvert.toRiskCaseBaseInfoVO(caseDO));
        response.setVehicleList(relationDOList.stream().map(RiskCaseVehicleRelationDO::toRiskCaseVehicleVO)
                .collect(Collectors.toList()));
        if (caseMarkInfoDO != null) {
            response.setMarkInfo(caseMarkInfoConverter.toVO(caseMarkInfoDO));
        }

        // 获取分拣信息
        CaseSortDataDO caseSortDataDO = sortDataRepository.getByCaseId(caseId);
        if (caseSortDataDO != null) {
            response.setSortData(sortDataConvert.toVO(caseSortDataDO));
        }

        // 获取风险事件关联的工作台case链接
        Map<String, Set<String>> caseId2workstationCaseIdMap = riskCaseRelatedServiceRecordRepository.queryCaseId2WorkstationCaseId(
                Arrays.asList(caseId), Arrays.asList(RelatedServiceNameEnum.WORKSTATION.name()));
        if (Objects.nonNull(caseId2workstationCaseIdMap) && CollectionUtils.isNotEmpty(
                caseId2workstationCaseIdMap.get(caseId))) {
            response.setInterventionCaseIdList(new ArrayList<>(caseId2workstationCaseIdMap.get(caseId)));
        }

        // 组装处理信息记录
        List<RiskCaseDisposeInfoVO> disposeInfoList = new ArrayList<>();
        relationDOList.forEach(relationDO -> {
            RiskVehicleExtInfoDO extInfo = relationDO.getExtInfo();
            if (extInfo == null) {
                return;
            }
            if (relationDO.getRequestSeatTime() != null) {
                disposeInfoList.add(addDisposeInfoIfValid(relationDO.getRequestSeatTime(),
                        DisposeInfoTypeEnum.REQUEST_SEAT_TIME, extInfo, relationDO.getVin()));
            }
            if (relationDO.getSeatConnectTime() != null) {
                disposeInfoList.add(addDisposeInfoIfValid(relationDO.getSeatConnectTime(),
                        DisposeInfoTypeEnum.SEAT_CONNECT_TIME, extInfo, relationDO.getVin()));
            }
            if (relationDO.getSeatExitTime() != null) {
                disposeInfoList.add(addDisposeInfoIfValid(relationDO.getSeatExitTime(),
                        DisposeInfoTypeEnum.SEAT_EXIT_TIME, extInfo, relationDO.getVin()));
            }
        });
        // 升序排序
        response.setDisposeInfoList(disposeInfoList.stream().filter(Objects::nonNull)
                .sorted(Comparator.comparing(RiskCaseDisposeInfoVO::getTimestamp))
                .collect(Collectors.toList()));
        return EveThriftResponse.ok(response);

    }

    @Override
    public EveThriftResponse<List<ActionChainResultVO>> listActionChainResult(
            AdminListActionChainResultRequest request) {
        CheckUtil.isNotNull(request, "请求不能为null");
        CheckUtil.isNotBlank(request.getCaseId(), "CaseId不能为空");

        LogActionChainResultQueryParamDTOBuilder paramDTO = LogActionChainResultQueryParamDTO.builder()
                .caseId(request.getCaseId());
        if (Objects.nonNull(request.getRound())) {
            paramDTO.round(request.getRound());
        }
        if (StringUtils.isNotBlank(request.getScene())) {
            paramDTO.scene(request.getScene());
        }
        if (StringUtils.isNotBlank(request.getVersion())) {
            paramDTO.markVersion(request.getVersion());
        }
        List<ActionChainResultLogDO> logDOList = actionChainResultLogRepository.queryByParam(paramDTO.build());
        List<ActionChainResultVO> resultVOList = logDOList.stream()
                .map(logDO -> actionChainResultLogConvert.toVO(logDO))
                .collect(Collectors.toList());
        return EveThriftResponse.ok(resultVOList);
    }

    @Override
    @OperateEnter(OperateEnterActionEnum.THRIFT_ADMIN_LIST_CASE_ENTRY)
    public EveThriftPageResponse<List<AdminListRiskCaseVO>> listRiskCase(AdminListRiskCaseRequest request) {
        CheckUtil.isNotNull(request, "请求不能为null");

        RiskCaseDOQueryParamDTO paramDTO = buildRiskCaseQueryParamDTO(request);
        Paging<RiskCaseDO> result = riskCaseRepository.queryByParamByPage(paramDTO, request.getPageNum(),
                request.getPageSize());
        List<AdminListRiskCaseVO> caseVOList = result.getElements().stream()
                .map(riskCaseDO -> riskCaseConvert.toAdminListRiskCaseVO(riskCaseDO))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(caseVOList)) {
            addVehicleListInfo(caseVOList);
            addMarkInfo(caseVOList);
            addSortData(caseVOList);
            addWorkstationLinkInfo(caseVOList);
        }
        EveThriftPage pageInfo = buildEveThriftPageInfo(result);
        EveThriftPageResponse response = EveThriftPageResponse.ok(caseVOList);
        response.setPaging(pageInfo);
        return response;
    }

    /**
     * 手动解除风险事件
     *
     * @param request 手动解除风险事件请求
     * @return 操作结果
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.MANUAL_RISK_CASE_OPERATE_ENTER)
    public EveThriftResponse<EmptyResponse> manualRiskCaseOperate(ManualRiskCaseOperateRequest request) {
        log.info("manualRiskCaseOperate, ManualRiskCaseOperateRequest = {}", request);
        // 1 参数校验
        CheckUtil.isNotBlank(request.getOperator(), "操作人不可为空");
        CheckUtil.isNotEmpty(request.getCaseIdList(), "消息列表不可为空");
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(request.getCaseIdList()).build());
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return EveThriftResponse.ok().build();
        }
        Set<String> eventIdSet = riskCaseDOList.stream().map(RiskCaseDO::getEventId).collect(Collectors.toSet());
        lockUtils.batchLockCanWait(LockKeyPreUtil.buildKeyWithEventId(eventIdSet), () -> {
            manualFinishCase(request.getCaseIdList());
        });
        return EveThriftResponse.ok().build();
    }

    /**
     * 人工呼叫
     *
     * @param request
     * @return
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.MANUAL_CALL_MRM)
    public EveThriftResponse<EmptyResponse> manualCallMrm(ManualCallRequestDTO request) {
        CheckUtil.isNotNull(request, "请求不能为null");
        CheckUtil.isNotBlank(request.getCaseId(), "caseId不能为空");
        RiskCaseDO riskCaseDO = riskCaseRepository.getByCaseId(request.getCaseId());
        CheckUtil.isNotNull(riskCaseDO, "未找到风险事件");
        lockUtils.batchLockCanWait(
                LockKeyPreUtil.buildEventIdAndVin(Sets.newHashSet(riskCaseDO.getEventId()), Sets.newHashSet()), 3,
                TimeUnit.SECONDS, () -> {
                    riskCaseOperateService.manualCallMrm(riskCaseDO.getCaseId());
                });
        return EveThriftResponse.okWithMessage("发送成功").build();
    }

    /**
     * 查询区域适配列表
     *
     * @return
     */
    @Override
    public EveThriftResponse<List<SafetyAreaVO>> querySafetyAreaList() {
        // 查询所有区域
        List<SafetyAreaDO> safetyAreaDOList = safetyAreaRepository.queryByParam(
                SafetyAreaQueryParamDTO.builder().createTimeCreateTo(DatetimeUtil.ZERO_DATE).build());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(safetyAreaDOList)) {
            return EveThriftResponse.ok(new ArrayList<>());
        }
        // 组装返回结果
        List<SafetyAreaVO> safetyAreaVOList = safetyAreaDOList.stream().map(safetyAreaDO -> {
            SafetyAreaVO safetyAreaVO = new SafetyAreaVO();
            safetyAreaVO.setAreaId(safetyAreaDO.getAreaId());
            safetyAreaVO.setPolygon(JacksonUtils.to(safetyAreaDO.getPolygon()));
            safetyAreaVO.setSource(safetyAreaDO.getSource().getCode());
            safetyAreaVO.setDescription(safetyAreaDO.getDescription());
            safetyAreaVO.setExtInfo(JacksonUtils.to(safetyAreaDO.getExtInfo()));
            safetyAreaVO.setType(safetyAreaDO.getType());
            return safetyAreaVO;
        }).collect(Collectors.toList());

        return EveThriftResponse.ok(safetyAreaVOList);
    }

    /**
     * 创建工作台case
     *
     * @param request
     * @return
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.CREATE_WORKSTATION_CASE_ENTRY)
    public EveThriftResponse<CreateWorkstationCaseVO> createWorkstationCase(CreateWorkstationCaseRequest request) {
        // 1 参数检查
        CheckUtil.isNotBlank(request.getCaseId(), "caseId不能为空");
        RiskCaseVehicleRelationDO relationDO = riskCaseVehicleRelationRepository.getByCaseId(request.getCaseId());
        CheckUtil.isNotNull(relationDO, "未找到车辆信息");
        RiskCaseDO riskCaseDO = riskCaseRepository.getByCaseId(request.getCaseId());
        CheckUtil.isNotNull(riskCaseDO, "未找到风险事件");

        // 2 查询指定case在指定时间内是否存在接管case
        List<String> workstationCaseIdList = queryWorkstationCaseIdList(riskCaseDO, relationDO.getVin());
        if (CollectionUtils.isNotEmpty(workstationCaseIdList)) {
            // 2.1 关联查询到的接管case
            List<RiskCaseRelatedServiceRecordDO> recordDOList = workstationCaseIdList.stream()
                    .map(caseId -> {
                        RiskCaseRelatedServiceRecordDO recordDO = new RiskCaseRelatedServiceRecordDO();
                        recordDO.setCaseId(riskCaseDO.getCaseId());
                        recordDO.setRelatedId(caseId);
                        recordDO.setServiceName(RelatedServiceNameEnum.WORKSTATION);
                        return recordDO;
                    })
                    .collect(Collectors.toList());
            riskCaseRelatedServiceRecordRepository.batchSave(recordDOList);

            // 2.2 构建响应
            return EveThriftResponse.ok(
                    CreateWorkstationCaseVO.builder().interventionCaseIdList(workstationCaseIdList)
                            .build());
        }

        // 3 当查询不到响应事件时选择创建
        WorkstationCreateResponseData responseData = createWorkstationCase(riskCaseDO, relationDO);
        if (Objects.nonNull(responseData)) {
            // 3.1 构建前端响应
            return EveThriftResponse.ok().build();
        }
        return EveThriftResponse.failWithMessage("createWorkstationCase error").build();
    }


    /**
     * 查询工作台链接
     *
     * @param riskCaseDO
     * @param vin
     * @return
     */
    private List<String> queryWorkstationCaseIdList(RiskCaseDO riskCaseDO, String vin) {
        // 开始时间
        Date startTime = riskCaseDO.getOccurTime();
        // 结束时间
        // todo: 兼容case未关闭的情况下进行分拣
        Date endTime = Objects.equals(riskCaseDO.getCloseTime(), DatetimeUtil.ZERO_DATE) ? new Date()
                : riskCaseDO.getCloseTime();
        // 查询工作台
        return workstationAdapter.getWorkstationCaseId(vin,
                DatetimeUtil.formatTime(startTime), DatetimeUtil.formatTime(endTime));
    }

    /**
     * 创建工作台case
     *
     * @param riskCaseDO
     * @param relationDO
     * @return
     */
    private WorkstationCreateResponseData createWorkstationCase(RiskCaseDO riskCaseDO,
            RiskCaseVehicleRelationDO relationDO) {
        // 1 拼接title
        VehicleInfoDO vehicleInfoDO = relationDO.getVehicleSnapshotInfo();
        String vehicleName = Objects.nonNull(vehicleInfoDO) ? vehicleInfoDO.getVehicleName() : "";
        String vehicleId = Objects.nonNull(vehicleInfoDO) ? vehicleInfoDO.getVehicleId() : "";
        String caseTime = DatetimeUtil.formatDate(riskCaseDO.getOccurTime(), "yyyyMMddHHmm");
        PositionDO beforePositionDO = Objects.nonNull(vehicleInfoDO) ? vehicleInfoDO.getPosition() : null;
        String title = String.format("停滞不当-%s/%s-%s", vehicleName, vehicleId, caseTime);

        // 2 经纬度转换格式
        PositionDO positionDO = positionDOConvert.toPositionDO(beforePositionDO, CoordinateSystemEnum.WGS84);

        // 3 计算停滞不当事件的真实发生时间
        Date riskCaseActualTime = riskCaseDO.getOccurTime();
        WorkstationCreateRequest workstationCreateRequest = WorkstationCreateRequest.builder()
                .isIssue(false)
                .source(CommonConstant.BEACON_TOWER)
                .caseType(CommonConstant.CASE_TYPE)
                // 停滞不当事件的真实发生时间
                .caseTime(DatetimeUtil.formatDate(riskCaseActualTime, "yyyy-MM-dd HH:mm:ss.SSS"))
                .vin(relationDO.getVin())
                .caseId(riskCaseDO.getCaseId())
                .title(title)
                .latitude(Objects.nonNull(positionDO) ? String.valueOf(positionDO.getLatitude()) : "")
                .longitude(Objects.nonNull(positionDO) ? String.valueOf(positionDO.getLongitude()) : "")
                .extension(CaseExtension.builder()
                        .beaconTowerLink(String.format(beaconTowerCaseLinkTemplate, riskCaseDO.getCaseId()))
                        .caseEndTime(DatetimeUtil.formatDate(riskCaseDO.getCloseTime(), "yyyy-MM-dd HH:mm:ss.SSS"))
                        .build())
                .build();
        return workstationAdapter.createWorkstationCase(workstationCreateRequest);
    }

    /**
     * 根据消息ID列表查询并更新风险事件实体
     *
     * @param caseIdList 消息ID列表
     */
    private void manualFinishCase(List<String> caseIdList) {
        // 1 根据 msgIds 进行查询风险事件实体
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(caseIdList).build());
        log.info("queryByParam, msgIds = {}, riskCaseDOList = {}", caseIdList, riskCaseDOList);
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            log.error("数据表中未找到相应的风险事件，msgIds = [{}]", caseIdList);
            return;
        }
        //取事件id，加锁
        List<RiskCaseDO> thisRiskCaseDoList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(caseIdList).build());
        List<RiskCaseDO> handledRiskCaseDOList = thisRiskCaseDoList.stream()
                //去掉已完成的
                .filter(riskCaseDO -> !RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus()))
                //修改未未完成
                .peek(riskCaseDO -> riskCaseDO.updateStatus(RiskCaseStatusEnum.MANUAL_DISPOSED,
                        new Date().getTime()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(handledRiskCaseDOList)) {
            riskCaseRepository.batchSave(handledRiskCaseDOList);
        }

    }

    /**
     * 添加关联车辆信息
     *
     * @param caseVOList
     */
    private void addVehicleListInfo(List<AdminListRiskCaseVO> caseVOList) {
        RiderCaseVehicleRelationDOParamDTO paramDTO1 = RiderCaseVehicleRelationDOParamDTO.builder()
                .caseIdList(caseVOList.stream().map(AdminListRiskCaseVO::getCaseId).collect(Collectors.toList()))
                .build();
        List<RiskCaseVehicleRelationDO> vehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                paramDTO1);
        Map<String, List<RiskCaseVehicleRelationDO>> caseVehicleListGroup = vehicleRelationDOList.stream()
                .collect(Collectors.groupingBy(RiskCaseVehicleRelationDO::getCaseId));
        caseVOList.forEach(caseVO -> {
            List<RiskCaseVehicleRelationDO> vehicles = caseVehicleListGroup.getOrDefault(caseVO.getCaseId(),
                    Lists.newArrayList());
            caseVO.setVehicleList(vehicles.stream().map(RiskCaseVehicleRelationDO::toRiskCaseVehicleVO)
                    .collect(Collectors.toList()));
        });
    }

    /**
     * 添加人工标注信息
     *
     * @param caseVOList
     */
    private void addMarkInfo(List<AdminListRiskCaseVO> caseVOList) {
        CaseMarkInfoDOQueryParamDTO param = CaseMarkInfoDOQueryParamDTO.builder()
                .caseIdList(caseVOList.stream().map(AdminListRiskCaseVO::getCaseId).collect(Collectors.toList()))
                .build();
        List<CaseMarkInfoDO> markInfoDOList = caseMarkInfoRepository.queryByParam(param);
        Map<String, List<CaseMarkInfoDO>> caseMarkInfoGroup = markInfoDOList.stream()
                .collect(Collectors.groupingBy(CaseMarkInfoDO::getCaseId));
        caseVOList.forEach(caseVO -> {
            List<CaseMarkInfoDO> markInfoList = caseMarkInfoGroup.getOrDefault(caseVO.getCaseId(),
                    Lists.newArrayList());
            if (CollectionUtils.isEmpty(markInfoList)) {
                caseVO.setMarkInfo(RiskCaseMarkInfoVO.builder().build());
            } else {
                caseVO.setMarkInfo(caseMarkInfoConverter.toVO(markInfoList.stream().findFirst().get()));
            }
        });
    }

    /**
     * 添加工作台接管case链接信息
     *
     * @param caseVOList
     */
    private void addWorkstationLinkInfo(List<AdminListRiskCaseVO> caseVOList) {
        // 查询指定caseID 关联的 工作台链接
        List<String> caseIdList = caseVOList.stream().map(AdminListRiskCaseVO::getCaseId).collect(Collectors.toList());
        Map<String, Set<String>> caseId2workstationCaseIdMap = riskCaseRelatedServiceRecordRepository.queryCaseId2WorkstationCaseId(
                caseIdList, Arrays.asList(RelatedServiceNameEnum.WORKSTATION.name()));
        if (MapUtils.isEmpty(caseId2workstationCaseIdMap)) {
            return;
        }
        // 补充响应
        caseId2workstationCaseIdMap.forEach((caseId, workstationCaseIdList) -> {
            // 构建工作台链接
            caseVOList.stream().filter(caseVO -> Objects.equals(caseVO.getCaseId(), caseId))
                    .forEach(caseVO -> caseVO.setInterventionCaseIdList(new ArrayList<>(workstationCaseIdList)));
        });
    }

    /**
     * 添加分拣数据
     *
     * @param caseVOList
     */
    private void addSortData(List<AdminListRiskCaseVO> caseVOList) {
        CaseSortDataDOQueryParam param = CaseSortDataDOQueryParam.builder()
                .caseIdList(caseVOList.stream().map(AdminListRiskCaseVO::getCaseId).collect(Collectors.toList()))
                .build();
        Map<String, CaseSortDataDO> sortDataDOMap = sortDataRepository.queryMapByParam(param);
        caseVOList.forEach(caseVO -> {
            CaseSortDataDO sortDataDO = sortDataDOMap.get(caseVO.getCaseId());
            caseVO.setSortData(sortDataConvert.toVO(sortDataDO));
        });
    }

    /**
     * 构建分页信息
     *
     * @param queryResult
     * @return
     */
    private EveThriftPage buildEveThriftPageInfo(Paging<RiskCaseDO> queryResult) {
        EveThriftPage pageInfo = new EveThriftPage();
        pageInfo.setPageNum(queryResult.getPageNum());
        pageInfo.setPageSize(queryResult.getPageSize());
        pageInfo.setTotal(queryResult.getTotal());
        return pageInfo;
    }

    /**
     * 构建查询参数
     *
     * @param request
     * @return
     */
    private RiskCaseDOQueryParamDTO buildRiskCaseQueryParamDTO(AdminListRiskCaseRequest request) {
        RiskCaseDOQueryParamDTO paramDTO = RiskCaseDOQueryParamDTO.builder().build();
        paramDTO.setCreateTimeRange(buildCreateTimeRange(request));

        if (CollectionUtils.isNotEmpty(request.getStatusList())) {
            paramDTO.setStatusList(request.getStatusList());
        }
        if (CollectionUtils.isNotEmpty(request.getPlaceCodeList())) {
            paramDTO.setPlaceCodeList(request.getPlaceCodeList());
        }
        if (CollectionUtils.isNotEmpty(request.getTypeList())) {
            paramDTO.setCaseTypeList(request.getTypeList());
        }
        if (CollectionUtils.isNotEmpty(request.getVinList())) {
            paramDTO.setVinList(request.getVinList());
            paramDTO.setLeftJoinRelation(true);
        }
        if (CollectionUtils.isNotEmpty(request.getPurposeList())) {
            paramDTO.setPurposeList(request.getPurposeList());
            paramDTO.setLeftJoinRelation(true);
        }
        if (CollectionUtils.isNotEmpty(request.getVhrModeList())) {
            paramDTO.setVhrModeList(request.getVhrModeList());
            paramDTO.setLeftJoinRelation(true);
        }
        if (CollectionUtils.isNotEmpty(request.getSourceList())) {
            paramDTO.setSourceList(request.getSourceList());
        }
        if (CollectionUtils.isNotEmpty(request.getCategoryList())) {
            paramDTO.setCategoryList(request.getCategoryList());
            paramDTO.setLeftJoinMarkInfo(true);
        }
        if (request.getDurationGreatThan() != null) {
            paramDTO.setDurationGreatThan(request.getDurationGreatThan());
        }
        if (request.getRecallDurationGreatThan() != null) {
            paramDTO.setRecallDurationGreatThan(request.getRecallDurationGreatThan());
        }
        if (CollectionUtils.isNotEmpty(request.getLevelList())) {
            paramDTO.setLevelList(request.getLevelList());
            paramDTO.setLeftJoinMarkInfo(true);
        }
        if (Objects.nonNull(request.getMrmCalled())) {
            paramDTO.setMrmCalledList(request.getMrmCalled() ? RiskCaseMrmCalledStatusEnum.getMrmCalledList()
                    : RiskCaseMrmCalledStatusEnum.getNotMrmCalledList());
        }

        if (CollectionUtils.isNotEmpty(request.getMrmCalledList())) {
            paramDTO.setMrmCalledList(request.getMrmCalledList());
        }

        if (CollectionUtils.isNotEmpty(request.getSubCategoryList())) {
            paramDTO.setSubCategoryList(request.getSubCategoryList());
            paramDTO.setLeftJoinMarkInfo(true);
        }
        // 归属问题查询
        if (CollectionUtils.isNotEmpty(request.getProblemList())) {
            paramDTO.setProblemList(request.getProblemList());
            paramDTO.setLeftJoinSort(true);
        }
        if (CollectionUtils.isNotEmpty(request.getCallSafetyList())) {
            paramDTO.setCallSafetyList(request.getCallSafetyList());
        }
        // 根据首次标注信息查询检索
        if (CollectionUtils.isNotEmpty(request.getFirstSubCategoryList())) {
            if (StringUtils.isNotBlank(request.getVersion())) {
                // 版本不为空
                paramDTO.setVersionSubCategoryList(request.getFirstSubCategoryList());
                paramDTO.setMarkVersion(request.getVersion());
                paramDTO.setLeftJoinMultiVersion(true);
            } else {
                // 版本为空
                paramDTO.setFirstSubCategoryList(request.getFirstSubCategoryList());
                paramDTO.setLeftJoinMarkInfo(true);
            }
        }
        // 根据最终标注人查询检索
        if (CollectionUtils.isNotEmpty(request.getFinalMarkerList())) {
            paramDTO.setLastOperatorList(request.getFinalMarkerList());
            paramDTO.setLeftJoinMarkInfo(true);
        }
        // 根据最终标注人查询检索
        if (CollectionUtils.isNotEmpty(request.getMarkRoundList())) {
            paramDTO.setMarkRoundList(request.getMarkRoundList());
            paramDTO.setLeftJoinMarkInfo(true);
        }
        if (StringUtils.isNotBlank(request.getPosition()) && request.getSearchRange() != null) {
            Double lng = Double.parseDouble(request.getPosition().split(",")[0]);
            Double lat = Double.parseDouble(request.getPosition().split(",")[1]);
            PositionDO positionDO = PositionDO.getPosition(lng, lat, CoordinateSystemEnum.WGS84);
            if (positionDO != null) {
                paramDTO.setLeftJoinLocationRelation(true);
                paramDTO.setLocationQuery(
                        GeoQueryDO.builder().point(positionDO.toPoint()).distance(request.getSearchRange()).build());
            }
        }
        // 根据caseId查询
        if (CollectionUtils.isNotEmpty(request.getCaseIdList())) {
            paramDTO.setCaseIdList(request.getCaseIdList());
        }
        // 根据事发地点查询
        if (CollectionUtils.isNotEmpty(request.getPoiNameList())) {
            paramDTO.setPoiNameList(request.getPoiNameList());
        }
        // 查询坐席是否被接管
        // TODO: 给该字段添加索引
        if (Objects.nonNull(request.getMrmIntervened())) {
            // 接管 -> 坐席接管时间大于数据库设置默认值
            if (request.getMrmIntervened()) {
                paramDTO.setSeatInterventionTimeCreateTo(
                        DatetimeUtil.getAfterTime(DatetimeUtil.ZERO_DATE, TimeUnit.DAYS, 1));
            } else {
                // 未接管 -> 坐席接管时间等于默认值即可
                paramDTO.setSeatInterventionTime(DatetimeUtil.ZERO_DATE);
            }
            paramDTO.setLeftJoinRelation(true);
        }
        // 查询呼叫云控原因
        if (CollectionUtils.isNotEmpty(request.getCallMrmReasonList())) {
            paramDTO.setCallMrmReasonList(request.getCallMrmReasonList());
            paramDTO.setLeftJoinRelation(true);
        }
        // 查询车型信息
        if (CollectionUtils.isNotEmpty(request.getVehicletypeList())) {
            paramDTO.setVehicleTypeList(request.getVehicletypeList());
            paramDTO.setLeftJoinRelation(true);
        }
        //按照时间倒序
        paramDTO.setOrderByCreateTime(OrderEnum.DESC);
        return paramDTO;
    }

    /**
     * 构建创建时间范围
     *
     * @param request
     * @return
     */
    private TimePeriod buildCreateTimeRange(AdminListRiskCaseRequest request) {
        CheckUtil.isNotBlank(request.getCreateTimeStart(), "开始时间不能为空");
        CheckUtil.isNotBlank(request.getCreateTimeEnd(), "结束时间不能为空");

        return TimePeriod.builder()
                .beginDate(DatetimeUtil.convertDatetimeStr2Date(request.getCreateTimeStart()))
                .endDate(DatetimeUtil.convertDatetimeStr2Date(request.getCreateTimeEnd()))
                .build();
    }

    /**
     * 添加处理信息
     *
     * @param time
     * @param type
     * @param extInfo
     */
    private RiskCaseDisposeInfoVO addDisposeInfoIfValid(Date time, DisposeInfoTypeEnum type,
            RiskVehicleExtInfoDO extInfo, String vin) {
        if (time == null || DatetimeUtil.ZERO_DATE.equals(time)) {
            return null;
        }
        return RiskCaseDisposeInfoVO.builder()
                .mrmMisId(extInfo.getMrmMisId())
                .mrmSeatNo(extInfo.getMrmSeatNo())
                .vin(vin)
                .role(extInfo.getRole() != null ? extInfo.getRole().getDesc() : StringUtils.EMPTY)
                .timestamp(time.getTime())
                .operationType(type.name())
                .build();
    }
}