package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.dto.TokenCheckDTO;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.MoveCarEventQueryRequest;
import com.sankuai.wallemonitor.risk.center.api.request.MoveCarEventReportRequest;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftMoveCarEventAdminService;
import com.sankuai.wallemonitor.risk.center.api.vo.MoveCarEventStatusVO;
import com.sankuai.wallemonitor.risk.center.domain.component.WechatAuthAdminService;
import com.sankuai.wallemonitor.risk.center.domain.service.MoveCarEventOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.DuplicateMoveCarEventException;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import com.sankuai.wallemonitor.risk.center.infra.exception.ReportLimitExceededException;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftMoveCarEventAdminServiceImpl implements IThriftMoveCarEventAdminService {

    @Resource
    private WechatAuthAdminService wechatAuthAdminService;

    @Resource
    private MoveCarEventOperateService moveCarEventAdminService;

    @Override
    @OperateEnter(OperateEnterActionEnum.REPORT_MOVE_CAR_ORDER)
    public EveThriftResponse<EmptyResponse> reportMoveCarEvent(MoveCarEventReportRequest request) {
        try {
            //1 登陆态校验
            CheckUtil.isNotBlank(request.getToken(), "用户登陆态不可为空");
            TokenCheckDTO tokenCheckDTO = wechatAuthAdminService.checkToken(request.getToken());
            if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
                return EveThriftResponse.codeWithMessage(HttpStatus.UNAUTHORIZED.value(), "登陆态过期，请重新登陆")
                        .build();
            }
            //2 参数校验
            CheckUtil.isNotBlank(request.getVehicleId(), "车牌号不可为空");
            CheckUtil.isNotNull(request.getEventType(), "异常事件类型不可为空");
            CheckUtil.isNotBlank(request.getMoveCarReason(), "挪车原因不可为空");
            //3 生成工单
            moveCarEventAdminService.checkAndCreateMoveCarEvent(request, tokenCheckDTO.getOpenId());
            return EveThriftResponse.ok().build();
        } catch (ParamInputErrorException e) {
            return EveThriftResponse.codeWithMessage(ResponseCodeEnum.BAD_REQUEST.getCode(), e.getMessage())
                    .build();
        } catch (DuplicateMoveCarEventException e) {
            return EveThriftResponse.codeWithMessage(ResponseCodeEnum.MOVE_CAR_REPEAT.getCode(), e.getMessage())
                    .build();
        } catch (ReportLimitExceededException e) {
            log.error("上报挪车事件失败,上报次数超过限制", e);
            return EveThriftResponse.codeWithMessage(ResponseCodeEnum.REPORT_TIME_REACH_LIMIT.getCode(), e.getMessage())
                    .build();
        } catch (UnableGetLockException e) {
            log.info("上报事件防抖", e);
            return EveThriftResponse.ok().build();
        } catch (Exception e) {
            log.error("上报挪车事件失败", e);
            return EveThriftResponse.codeWithMessage(ResponseCodeEnum.SYSTEM_ERROR.getCode(), "上报挪车事件失败")
                    .build();
        }
    }

    /**
     * 查询挪车事件状态
     *
     * @param request
     * @return
     */
    @Override
    public EveThriftResponse<MoveCarEventStatusVO> queryMoveCarEvent(MoveCarEventQueryRequest request) {
        try {
            //1 登陆态校验
            CheckUtil.isNotBlank(request.getToken(), "用户登陆态不可为空");
            TokenCheckDTO tokenCheckDTO = wechatAuthAdminService.checkToken(request.getToken());
            if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
                return EveThriftResponse.codeWithMessage(HttpStatus.UNAUTHORIZED.value(), "登陆态过期，请重新登陆")
                        .build();
            }
            //2 参数校验
            CheckUtil.isNotBlank(request.getVehicleId(), "车牌号不可为空");
            return EveThriftResponse.ok(moveCarEventAdminService.getMoveCarStatusByVehicleId(request.getVehicleId()));
        } catch (ParamInputErrorException e) {
            return EveThriftResponse.codeWithMessage(ResponseCodeEnum.MOVE_CAR_REPEAT.getCode(), e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("查询挪车事件状态失败", e);
            return EveThriftResponse.codeWithMessage(ResponseCodeEnum.SYSTEM_ERROR.getCode(), "system error")
                    .build();
        }

    }
}
