package com.sankuai.wallemonitor.risk.center.server.crane;


import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.google.common.collect.Lists;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CommonSearchAdaptor;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleContextUpdateFromDataBusCraneConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

/**
 * 更新车辆上下文状态定时任务 从数据总线获取数据，然后更新到上下文缓存中，本次只更新业务状态字段
 */
@Slf4j
@CraneConfiguration
public class VehicleRuntimeInfoContextUpdateFromDataBusCrane {

    private final static int BATCH_SIZE = 200;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private CommonSearchAdaptor commonSearchAdaptor;

    @ConfigValue(key = "risk.VehicleRuntimeInfoContextUpdateFromDataBusCrane.config", defaultValue = "50")
    private VehicleContextUpdateFromDataBusCraneConfigDTO configDTO;

    @Crane(CraneConstant.VEHICLE_RUNTIME_INFO_CONTEXT_UPDATE_FROM_DATA_BUS_CRANE)
    @OperateEnter(OperateEnterActionEnum.VEHICLE_RUNTIME_INFO_CONTEXT_UPDATE_FROM_DATA_BUS_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("获取数据总线数据更新车辆上下文定时任务开始执行");
        CheckUtil.isNotNull(configDTO, "定时任务配置不能为空");
        long startTime = System.nanoTime();
        try {
            List<String> vinList = commonSearchAdaptor.getBusinessCarVinList(configDTO.getBussinessTypeList());
            if (CollectionUtils.isEmpty(vinList)) {
                return;
            }
            CheckUtil.isNotEmpty(vinList, "业务车列表不能为空");
            // 并行处理数据
            ParallelExecutor.executeParallelTasks("vehicle_runtime_info_biz_status",
                    Lists.partition(Lists.newArrayList(vinList), configDTO.getBatchSize()),
                    (subVinList) -> {
                        // 批量查询数据总线接口,获取车辆对应的业务状态
                        Map<String, String> vin2BizStatusMap = fetchAndProcessVehicleBizStatus(subVinList);
                        log.info("vin2BizStatusMap:{}", vin2BizStatusMap);
                        // 数据更新
                        ParallelExecutor.executeParallelTasks("vehicle_runtime_info_biz_status", subVinList, vin -> {
                            try {
                                if (!vin2BizStatusMap.containsKey(vin)) {
                                    return;
                                }
                                VehicleRuntimeInfoContextDO latest = vehicleRuntimeInfoContextRepository.getFromCache(
                                        vin);
                                latest.setBizStatus(vin2BizStatusMap.getOrDefault(vin, ""));
                                vehicleRuntimeInfoContextRepository.updateCache(latest, System.currentTimeMillis());
                            } catch (Exception e) {
                                log.error(StringMessageFormatter.replaceMsg("更新车辆运行时状态信息失败, context:{}",
                                                vin),
                                        e);
                            }
                        });
                    });
        } catch (Exception e) {
            log.error("车辆运行时状态信息更新定时任务执行失败", e);
        } finally {
            long endTime = System.nanoTime();
            long durationInNanos = endTime - startTime;
            double durationInSeconds = durationInNanos / 1_000_000_000.0;
            log.info("代码执行耗时: {} 秒", durationInSeconds);
        }
    }

    /**
     * 从数据总线获取并处理车辆业务状态
     *
     * @param vinList 车辆VIN列表
     * @return VIN到业务状态的映射
     */
    private Map<String, String> fetchAndProcessVehicleBizStatus(List<String> vinList) {
        try {
            // 调用数据总线服务获取车辆业务状态
            List<VehicleEveInfoVTO> vehicleEveInfoVTOList = vehicleAdapter.queryRuntimeVehicleInfo(
                    VehicleRuntimeInfoParamVTO.builder().vinList(vinList).build());
            // 处理原始数据 - 状态更新时间不得超过24h（可以确认下数据的更新频率）
            if (CollectionUtils.isEmpty(vehicleEveInfoVTOList)) {
                return new HashMap<>();
            }
            return vehicleEveInfoVTOList.stream().collect(Collectors.toMap(VehicleEveInfoVTO::getVin,
                    VehicleEveInfoVTO::getBizStatus, (v1, v2) -> v1));

        } catch (Exception e) {
            log.error("fetchAndProcessVehicleBizStatus error", e);
        }
        return new HashMap<>();
    }
}
