package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.CommonEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ParkingFailureExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory.CreateRiskCaseDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory.CreateVehicleCaseRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GisInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCaseExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseParkingPlotRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.GisInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseParkingPlotRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 通用风险事件消费者
 */
@Component
@Slf4j
public class CommonCaseConsumer {

    private static final String PARKING_PLOT_ID = "parkingPlotId";

    @Resource
    private GisInfoRepository gisInfoRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private VehicleInfoRepository vehicleInfoRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private RiskCaseParkingPlotRelationRepository riskCaseParkingPlotRelationRepository;

    /**
     * 消费通用风险事件消息
     *
     * @param message 消息内容
     * @return 消费状态
     */
    @MafkaConsumer(
            namespace = "waimai",
            topic = "wallemonitor.risk.common.case.message",
            group = "wallemonitor.risk.common.case.consumer",
            deadLetter = true,
            deadLetterDelayMills = 6 * 1000
    )
    @OperateEnter(OperateEnterActionEnum.COMMON_CASE_CONSUMER_ENTRY)
    public ConsumeStatus receive(String message) {
        try {
            log.info("接收到通用风险事件消息: {}", message);

            // 解析消息
            CommonEventDTO eventDTO = JacksonUtils.from(message, CommonEventDTO.class);
            if (eventDTO == null) {
                log.warn("消息解析失败，消息内容为空");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 校验必要字段
            if (!isValidEvent(eventDTO)) {
                log.warn("事件数据校验失败: {}", eventDTO);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 处理风险事件
            processRiskEvent(eventDTO);

            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("处理通用风险事件消息失败: {}", message, e);
            return ConsumeStatus.CONSUME_FAILURE;
        }
    }

    /**
     * 校验事件数据
     */
    private boolean isValidEvent(CommonEventDTO eventDTO) {
        if (StringUtils.isBlank(eventDTO.getEventId())) {
            log.warn("事件ID为空");
            return false;
        }
        if (eventDTO.getCaseType() == null || RiskCaseTypeEnum.findByValue(eventDTO.getCaseType()) == null) {
            log.warn("事件类型为空");
            return false;
        }
        if (eventDTO.getSource() == null || RiskCaseTypeEnum.findByValue(eventDTO.getSource()) == null) {
            log.warn("未知事件来源");
            return false;
        }
        if (eventDTO.getOccurTime() == null) {
            log.warn("事件发生时间为空");
            return false;
        }
        if (eventDTO.getLocation() == null ||
                StringUtils.isBlank(eventDTO.getLocation().getLongitude()) ||
                StringUtils.isBlank(eventDTO.getLocation().getLatitude())) {
            log.warn("位置信息为空或不完整");
            return false;
        }
        if (Objects.equals(eventDTO.getCaseType(), RiskCaseTypeEnum.PARKING_FAILURE.getCode())
                && (eventDTO.getExtInfo() == null || Objects.isNull(eventDTO.getExtInfo().get(PARKING_PLOT_ID)))) {
            log.warn("泊车失败事件缺少停车点ID");
            return false;
        }

        return true;
    }

    /**
     * 处理风险事件
     */
    private void processRiskEvent(CommonEventDTO eventDTO) {
        try {
            if (isCaseExist(eventDTO)) {
                log.info("事件已存在，跳过处理: eventId={}", eventDTO.getEventId());
                return;
            }
            String vin = StringUtils.upperCase(eventDTO.getVin());
            // 解析位置信息
            GisInfoDO gisInfo = parseLocationInfo(eventDTO);
            // 获取车辆信息
            VehicleInfoDO vehicleInfo = vehicleInfoRepository.getByVin(vin);
            if (vehicleInfo == null) {
                log.warn("未查询到车辆信息，跳过处理: eventId={}, vin={}", eventDTO.getEventId(), vin);
                return;
            }
            // 状态为已处置
            RiskCaseDO riskCase = createRiskCase(eventDTO, gisInfo, vehicleInfo);
            RiskCaseVehicleRelationDO relationDO = createRiskCaseVehicleRelation(eventDTO, riskCase, vehicleInfo);
            // 处理泊车失败事件的特殊逻辑
            if (riskCase.getType() == RiskCaseTypeEnum.PARKING_FAILURE) {
                processParkingFailureEvent(riskCase, eventDTO);
            }
            // 保存风险事件（保存后会自动通过事件机制触发聚合告警检查）
            riskCaseVehicleRelationRepository.save(relationDO);
            riskCaseRepository.save(riskCase);
            log.info("通用风险事件保存成功: eventId={}， caseId={}", eventDTO.getEventId(), riskCase.getCaseId());
        } catch (Exception e) {
            log.error("处理风险事件失败: eventId={}", eventDTO.getEventId(), e);
            throw e;
        }
    }

    private boolean isCaseExist(CommonEventDTO eventDTO) {
        // 外部event_id、vin相同，认为事件已经存在
        List<RiskCaseDO> caseList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().eventId(eventDTO.getEventId())
                        .vinList(Collections.singletonList(eventDTO.getVin()))
                        .leftJoinRelation(true)
                        .build());
        return CollectionUtils.isNotEmpty(caseList);
    }

    /**
     * 创建风险与车辆关联关系
     */
    private RiskCaseVehicleRelationDO createRiskCaseVehicleRelation(CommonEventDTO eventDTO, RiskCaseDO riskCase,
            VehicleInfoDO vehicleInfo) {
        CreateVehicleCaseRelationDOParamDTO caseRelationDOParamDTO = CreateVehicleCaseRelationDOParamDTO.builder()
                .caseId(riskCase.getCaseId())
                .eventId(eventDTO.getEventId())
                .vin(vehicleInfo.getVin())
                .type(riskCase.getType())
                .occurTime(eventDTO.getOccurTime())
                .build();
        RiskCaseVehicleRelationDO riskRelation = RiskCaseFactory.createRiskRelation(caseRelationDOParamDTO);
        riskRelation.updatePurpose(vehicleInfo.getPurpose());
        riskRelation.updateVehicleSnapshotInfo(vehicleInfo);
        riskRelation.updateVhrMode(Optional.ofNullable(vehicleInfo.getVhr()).map(VHRModeEnum::getCode).orElse(null));
        riskRelation.updateVehicleType(vehicleInfo.getVehicleType());
        return riskRelation;
    }

    /**
     * 解析位置信息
     */
    private GisInfoDO parseLocationInfo(CommonEventDTO eventDTO) {
        try {
            PositionDO position = PositionDO.builder()
                    .longitude(Double.valueOf(eventDTO.getLocation().getLongitude()))
                    .latitude(Double.valueOf(eventDTO.getLocation().getLatitude()))
                    .coordinateSystem(CoordinateSystemEnum.GCJ02)
                    .build();
            // 调用GIS服务解析位置
            GisInfoDO gisInfoDO = gisInfoRepository.queryByPosition(position);
            if (gisInfoDO != null) {
                return gisInfoDO;
            } else {
                log.warn("位置解析失败，使用默认值: eventId={}", eventDTO.getEventId());
                return GisInfoDO.builder().poi("未知位置").city("未知城市").area("未知区域").position(position).build();
            }
        } catch (Exception e) {
            log.error("解析位置信息异常: eventId=" + eventDTO.getEventId(), e);
            return GisInfoDO.builder().poi("解析失败").city("未知城市").area("未知区域").build();
        }
    }

    /**
     * 创建风险事件
     */
    private RiskCaseDO createRiskCase(CommonEventDTO eventDTO, GisInfoDO gisInfo, VehicleInfoDO vehicleInfo) {
        CreateRiskCaseDOParamDTO riskCaseDOParamDTO = CreateRiskCaseDOParamDTO.builder()
                .eventId(eventDTO.getEventId())
                .source(RiskCaseSourceEnum.findByValue(eventDTO.getSource()))
                .type(RiskCaseTypeEnum.findByValue(eventDTO.getCaseType()))
                .status(RiskCaseStatusEnum.DISPOSED)
                .vinList(Collections.singletonList(vehicleInfo.getVin()))
                .timestamp(eventDTO.getOccurTime())
                .build();
        RiskCaseDO riskCaseDO = RiskCaseFactory.createRiskCaseDO(riskCaseDOParamDTO);
        // 构建风险事件, 从外部接收到的风险事件状态暂时置为"已处置"，真正的处置动作由告警接收人处理
        riskCaseDO.setExtInfo(buildExtInfo(eventDTO, gisInfo));
        riskCaseDO.setPoiName(gisInfo.getPoi());

        return riskCaseDO;
    }

    /**
     * 构建扩展信息
     */
    private RiskCaseExtInfoDO buildExtInfo(CommonEventDTO eventDTO, GisInfoDO gisInfo) {
        RiskCaseExtInfoDO extInfo = new RiskCaseExtInfoDO();
        if (gisInfo != null) {
            extInfo.setPosition(gisInfo.getPosition());
            extInfo.setCity(gisInfo.getCity());
            extInfo.setAre(gisInfo.getArea());
            extInfo.setPoi(gisInfo.getPoi());
        }
        // 设置描述信息
        if (StringUtils.isNotBlank(eventDTO.getDescription())) {
            extInfo.setEventDesc(eventDTO.getDescription());
        }
        return extInfo;
    }

    /**
     * 处理泊车失败事件
     */
    private void processParkingFailureEvent(RiskCaseDO riskCase, CommonEventDTO eventDTO) {
        try {
            if (MapUtils.isEmpty(eventDTO.getExtInfo()) || Objects.isNull(eventDTO.getExtInfo().get(PARKING_PLOT_ID))) {
                log.warn("泊车失败事件缺少必要字段！");
                return;
            }
            ParkingFailureExtInfoDTO parkingFailureExtInfo = ParkingFailureExtInfoDTO.builder()
                    .parkingPlotId(Objects.toString(eventDTO.getExtInfo().get("parkingPlotId")))
                    .build();

            // 创建泊车失败事件与泊位ID关联关系
            RiskCaseParkingPlotRelationDO relationDO = RiskCaseParkingPlotRelationDO.builder()
                    .caseId(riskCase.getCaseId())
                    .parkingPlotId(parkingFailureExtInfo.getParkingPlotId())
                    .build();
            riskCaseParkingPlotRelationRepository.save(relationDO);
        } catch (Exception e) {
            log.error("处理泊车失败事件失败: caseId={}", riskCase.getCaseId(), e);
        }
    }
} 