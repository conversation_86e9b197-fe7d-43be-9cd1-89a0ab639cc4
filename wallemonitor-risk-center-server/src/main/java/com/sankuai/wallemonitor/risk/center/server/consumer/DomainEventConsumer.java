package com.sankuai.wallemonitor.risk.center.server.consumer;


import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.common.ICommonConsumerProcessor;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walledelivery.commons.exception.JacksonException;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleeve.commons.CharConstant;
import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.domain.service.DomainEventService;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 领域消息发送
 */
@Slf4j
@Component
public class DomainEventConsumer {


    /**
     * 领域事件处理消费者,group name -> 消费者
     */
    private static final Map<String, ICommonConsumerProcessor> domainConsumerProcessors = new ConcurrentHashMap<>();

    @Resource
    private DomainEventService domainEventService;

    /**
     * 必须是风控服务自己的
     */
    private static final String DOMAIN_MODEL_PACKAGE_PREFIX = "com.sankuai.wallemonitor.risk.center.infra.model";


    /**
     * 判断入口的字段
     */
    private static final String KEY_EVENT_ENTRY = "entry";


    /**
     * 消费领域事件消息
     *
     * @param domainEventStr
     * @return
     */
    @MafkaConsumer(
            namespace = "waimai", topic = "wallemonitor.risk.center.domain.event",
            group = "wallemonitor.risk.center.domain.event.consumer",
            deadLetter = true,
            deadLetterDelayMills = 6 * 1000
    )
    public ConsumeStatus receive(String domainEventStr) {
        return handleMessageReceive(domainEventStr, false);
    }

    /**
     * 消费领域事件消息（异步）
     *
     * @param domainEventStr
     * @return
     */
    @MafkaConsumer(
            namespace = "waimai", topic = "wallemonitor.risk.center.domain.event.async",
            group = "wallemonitor.risk.center.domain.event.async.consumer",
            deadLetter = true,
            deadLetterDelayMills = 6 * 1000
    )
    public ConsumeStatus receiveAsync(String domainEventStr) {
        return handleMessageReceive(domainEventStr, true);
    }

    /**
     * 预检模型的消费组
     *
     * @param domainEventStr
     * @return
     */
    @MafkaConsumer(
            namespace = "waimai", topic = "risk.center.risk_checking_queue_item.domain.event",
            group = "risk_checking_queue_item.domain.event.consumer",
            deadLetter = true,
            deadLetterDelayMills = 6 * 1000
    )
    public ConsumeStatus receiveCheckQueueItemAsync(String domainEventStr) {
        return handleMessageReceive(domainEventStr, true);
    }


    /**
     * 处理领域消息的消费
     *
     * @param domainEventStr
     * @param async
     * @return
     */
    private ConsumeStatus handleMessageReceive(String domainEventStr, boolean async) {
        log.info("domainEvent{}Str:{}", BooleanUtils.toString(async, "Async", CharConstant.CHAR_EMPTY), domainEventStr);
        DomainEventResultDTO resultDTO = null;
        try {
            DomainEventEntryDTO domainEventEntry = checkMessageInvalid(domainEventStr);
            if (Objects.isNull(domainEventEntry)) {
                //消息为空时，消费成功
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            DomainEventChangeDTO<?> domainEventChangeDTO = handleCrateDomainEvent(domainEventEntry, domainEventStr);
            if (domainEventChangeDTO == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //处理器的结果必须全部成功
            resultDTO = domainEventService.process(domainEventChangeDTO, async);
            CheckUtil.isTrue(resultDTO.isAllSuccess(), "部分处理器失败!，部分重试");
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (JacksonException e) {
            //消息解析异常
            log.warn("消息解析过程出现未知异常", e);
            return ConsumeStatus.CONSUME_SUCCESS;

        } catch (ErrorCodeException e) {
            log.warn("领域事件消息消费失败", e);
            List<String> domainEventProcessResultUk = Optional.ofNullable(resultDTO)
                    .map(DomainEventResultDTO::getCannotRetryProcessResultKey).orElse(new ArrayList<>());
            if (CollectionUtils.isNotEmpty(domainEventProcessResultUk)) {
                //如果存在无法重试的处理器，打印error
                log.error(StringMessageFormatter.replaceMsg("领域事件消息消费失败,部分消费器无法重试:{}",
                        JacksonUtils.to(domainEventProcessResultUk)), e);
            }
            return ConsumeStatus.CONSUME_FAILURE;
        } catch (Exception e) {
            log.error("领域事件消息消费未知异常失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        }
    }


    /**
     * 检查是否消息异常
     *
     * @param domainEventStr
     * @return
     */
    private DomainEventEntryDTO checkMessageInvalid(String domainEventStr) {
        if (StringUtils.isBlank(domainEventStr)) {
            //消息为空时，消费成功
            log.warn("消息为空，消费失败");
            return null;
        }
        DomainEventEntryDTO domainEventEntry = JacksonUtils.getAsObject(domainEventStr, KEY_EVENT_ENTRY,
                DomainEventEntryDTO.class);
        if (domainEventEntry == null || StringUtils.isBlank(domainEventEntry.getDomainClassName())) {
            log.error("未找到消息中类名信息！", new SystemException("消息失败"));
            return null;
        }
        return domainEventEntry;
    }



    private DomainEventChangeDTO<?> handleCrateDomainEvent(DomainEventEntryDTO domainEventEntry, String message) {
        //获取领域消息对应的领域类型
        Class<?> foundClass = ReflectUtils.findClassBySimpleName(DOMAIN_MODEL_PACKAGE_PREFIX,
                domainEventEntry.getDomainClassName());
        if (foundClass == null) {
            log.warn("无法处理该类消息！domainClassName: {}", domainEventEntry.getDomainClassName());
            return null;
        }
        DomainEventDTO<?> thisDomainEventDTO = JacksonUtils.from(message,
                getTypeReference(foundClass));
        //绑定灰度模式
        return DomainEventFactory.createDomainEventChangeDTO(
                thisDomainEventDTO, foundClass);
    }


    private <T> TypeReference<DomainEventDTO<T>> getTypeReference(final Class<T> tClass) {
        return new TypeReference<DomainEventDTO<T>>() {
            @Override
            public Type getType() {
                return constructParameterizedType(DomainEventDTO.class, tClass);
            }
        };
    }

    private ParameterizedType constructParameterizedType(final Class<?> raw, final Type... args) {
        return new ParameterizedType() {
            public Type getRawType() {
                return raw;
            }

            public Type[] getActualTypeArguments() {
                return args;
            }

            public Type getOwnerType() {
                return null;
            }
        };
    }


}
