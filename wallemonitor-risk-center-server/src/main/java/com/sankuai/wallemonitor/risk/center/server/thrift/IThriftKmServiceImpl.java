package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.CreateExperimentalResultRequest;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultMissCaseDetailRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultOverviewRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultSceneDataRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftKmService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.KmServiceAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultMissCaseDetailDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultMissCaseDetailDTO.IdentifyProcessDetail;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultMissCaseDetailDTO.IdentifyProcessInfo;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultOverviewDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultSceneDataDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultSceneDataDTO.MissCaseAndTimes;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.CreateExperimentalResultParamVTO;
import java.util.Collections;
import java.util.Date;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
@Slf4j
@Service
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftKmServiceImpl implements IThriftKmService {

    @Resource
    private KmServiceAdapter kmServiceAdapter;

    private static final String KM_CONTENT_URL_FORMAT = "https://km.sankuai.com/collabpage/%s";

    @Override
    @OperateEnter(OperateEnterActionEnum.KM_CREATE_EXPERIMENTAL_RESULT_CONTENT)
    public EveThriftResponse<String> createExperimentalResultContent(CreateExperimentalResultRequest request) {
        try {
            String contentId = kmServiceAdapter.createExperimentalResultContent(
                    convertCreateExperimentalResultRequest(request));
            return EveThriftResponse.ok(String.format(KM_CONTENT_URL_FORMAT, contentId));
        } catch (Exception e) {
            log.error("创建实验结果文档失败", e);
            return EveThriftResponse.failWithMessage(e.getMessage()).data(null);
        }
    }

    private CreateExperimentalResultParamVTO convertCreateExperimentalResultRequest(
            CreateExperimentalResultRequest request) {
        if (request == null) {
            return null;
        }

        CreateExperimentalResultParamVTO paramVTO = new CreateExperimentalResultParamVTO();
        paramVTO.setTitle(request.getTitle());
        paramVTO.setOperatorEmpId(request.getOperatorEmpId());
        paramVTO.setOverview(convertOverView(request.getOverview()));
        paramVTO.setSceneDataList(Optional.ofNullable(request.getSceneDataList())
                .map(list -> list.stream().map(this::convertSceneData).collect(Collectors.toList()))
                .orElse(Collections.emptyList()));
        paramVTO.setMissCaseDetailList(Optional.ofNullable(request.getMissCaseDetailList())
                .map(list -> list.stream().map(this::convertMissCaseDetail).filter(Objects::nonNull)
                        .collect(Collectors.toList())).orElse(Collections.emptyList())
        );
        return paramVTO;
    }

    private ExperimentalResultOverviewDTO convertOverView(ExperimentalResultOverviewRequestDTO overview) {
        if (overview == null) {
            return null;
        }
        ExperimentalResultOverviewDTO overviewDTO = new ExperimentalResultOverviewDTO();
        overviewDTO.setExperimentalTime(new Date(overview.getExperimentalTime()));
        overviewDTO.setPersonName(overview.getPersonName());
        overviewDTO.setPersonMis(overview.getPersonMis());
        overviewDTO.setExperimentalContent(overview.getExperimentalContent());
        overviewDTO.setDataset(overview.getDataset());
        overviewDTO.setDatasetDataCount(overview.getDatasetDataCount());
        overviewDTO.setRound(overview.getRound());
        overviewDTO.setRecallCount(overview.getRecallCount());
        overviewDTO.setMissCount(overview.getMissCount());
        overviewDTO.setFailureCount(overview.getFailureCount());
        overviewDTO.setAccuracyCount(overview.getAccuracyCount());
        return overviewDTO;
    }


    private ExperimentalResultSceneDataDTO convertSceneData(ExperimentalResultSceneDataRequestDTO sceneData) {
        if (sceneData == null) {
            return null;
        }
        ExperimentalResultSceneDataDTO sceneDataDTO = new ExperimentalResultSceneDataDTO();
        sceneDataDTO.setCategoryEnum(ISCheckCategoryEnum.getBySubcategory(sceneData.getCategoryName()));
        sceneDataDTO.setActualCaseCount(sceneData.getActualCaseCount());
        sceneDataDTO.setRecallCaseCount(sceneData.getRecallCaseCount());
        sceneDataDTO.setMissedEvents(
                Optional.ofNullable(sceneData.getMissedEvents()).orElse(Collections.emptyList()).stream()
                        .map(element -> MissCaseAndTimes.builder().caseId(element.getCaseId())
                                .times(element.getTimes()).build()).collect(Collectors.toList()));
        return sceneDataDTO;
    }

    private ExperimentalResultMissCaseDetailDTO convertMissCaseDetail(
            ExperimentalResultMissCaseDetailRequestDTO missCaseDetail) {
        if (missCaseDetail == null) {
            return null;
        }
        ExperimentalResultMissCaseDetailDTO missCaseDetailDTO = new ExperimentalResultMissCaseDetailDTO();
        missCaseDetailDTO.setCaseId(missCaseDetail.getCaseId());
        missCaseDetailDTO.setTrueCategory(ISCheckCategoryEnum.getBySubcategory(missCaseDetail.getTrueCategory()));
        missCaseDetailDTO.setMissCount(missCaseDetail.getMissCount());
        missCaseDetailDTO.setIdentifyProcessInfo(
                Optional.ofNullable(missCaseDetail.getIdentifyProcessInfo()).orElse(Collections.emptyList()).stream()
                        .map(element -> {
                            IdentifyProcessInfo identifyProcessInfo = new IdentifyProcessInfo();
                            identifyProcessInfo.setCategory(
                                    ISCheckCategoryEnum.getBySubcategory(element.getCategory()));
                            identifyProcessInfo.setCount(element.getCount());
                            identifyProcessInfo.setDetail(
                                    Optional.ofNullable(element.getDetail()).orElse(Collections.emptyMap()).entrySet()
                                            .stream().collect(Collectors.toMap(Entry::getKey,
                                                    entry -> IdentifyProcessDetail.builder().info(entry.getValue().getInfo())
                                                            .picUrl(entry.getValue().getPicUrl()).build())));
                            return identifyProcessInfo;
                        }).collect(Collectors.toList()));
        return missCaseDetailDTO;
    }
}
