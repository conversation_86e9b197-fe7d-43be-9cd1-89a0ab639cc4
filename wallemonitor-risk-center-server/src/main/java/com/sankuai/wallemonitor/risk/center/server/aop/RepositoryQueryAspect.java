package com.sankuai.wallemonitor.risk.center.server.aop;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.OperateEnterContext;
import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.TransactionUtils;
import com.sankuai.wallemonitor.risk.center.server.aop.filter.CommonAroundAspect;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 仓储层切面
 *
 * <AUTHOR> liaolingfei
 * @Description : 操作入口切面
 * @Date 2023/6/9 16:43
 **/
@Order(3)
@Component
@Slf4j
@Aspect
public class RepositoryQueryAspect extends CommonAroundAspect<RepositoryQuery> {


    @Resource
    TransactionUtils transactionUtils;

    /**
     * 打印的字段
     *
     * @return
     */
    @Override
    public String getLogField() {
        return LogCenterFieldEnum.AL_REPOSITORY.getFieldCode();
    }

    /**
     * 获取结果
     *
     * @param joinPoint
     * @return
     */
    @Override
    protected Object produceResult(ProceedingJoinPoint joinPoint, RepositoryQuery repositoryQuery) throws Throwable {
        Object repositoryQueryResult = joinPoint.proceed();
        //根据切面获取领域对象
        Map<String, Object> queryDomain = getQueryResult(repositoryQueryResult);
        OperateEnterContext.putDomainFromRepository(queryDomain);
        return repositoryQueryResult;
    }

    @Around(value = "@annotation(repositoryQuery)")
    public Object doQueryAround(ProceedingJoinPoint joinPoint, RepositoryQuery repositoryQuery) throws Throwable {
        //切面执行
        return super.handleAroundLogAndReturnResult(joinPoint, repositoryQuery);
    }


    /**
     * 获取查询的领域对象，并放入一个深拷贝后的对象
     *
     * @param repositoryQueryResult
     * @return
     */
    private Map<String, Object> getQueryResult(Object repositoryQueryResult) {
        //返回类型
        Map<String, Object> queryDomainObject = new HashMap<>();
        Class<?> domainClass = getDomainClassAfterQuery(repositoryQueryResult);
        if (repositoryQueryResult == null || domainClass == null) {
            //找不到领域的类
            return new HashMap<>();
        } else if (repositoryQueryResult instanceof Collection<?> && CollectionUtils.isNotEmpty(
                (Collection<?>) repositoryQueryResult) && CollectionUtils.isNotEmpty(
                (Collection<?>) repositoryQueryResult) && CollectionUtils.isNotEmpty(
                ((Collection<?>) repositoryQueryResult).stream().filter(
                        Objects::nonNull).collect(
                        Collectors.toList()))) {
            //如果是集合类型，且不为空
            //取集合里面的元素类型
            Class<?> resultClass = ((Collection<?>) repositoryQueryResult).stream().findFirst().get().getClass();
            if (!domainClass.isAssignableFrom(resultClass)) {
                return queryDomainObject;
            }
            //否则就生成map
            ((Collection<?>) repositoryQueryResult).forEach(domainObj -> {
                //吧这个对象用序列化的方式深拷贝
                //FIXME 效率不高，可以优化
                queryDomainObject.put(String.valueOf(System.identityHashCode(domainObj)),
                        JacksonUtils.from(JacksonUtils.to(domainObj), domainClass));
            });

        } else if (repositoryQueryResult instanceof Map<?, ?> && MapUtils.isNotEmpty(
                (Map<?, ?>) repositoryQueryResult)) {
            //返回是map类型，且不为空
            Class<?> valueType = null;
            if (!(repositoryQueryResult.getClass().getGenericSuperclass() instanceof ParameterizedType)) {
                return new HashMap<>();
            }
            ParameterizedType parameterizedType = (ParameterizedType) repositoryQueryResult.getClass()
                    .getGenericSuperclass();
            Type[] typeArguments = parameterizedType.getActualTypeArguments();
            if (typeArguments.length > 1) {
                valueType = (Class<?>) typeArguments[1];
            }
            if (valueType != null && valueType == domainClass) {
                ((Map<?, ?>) repositoryQueryResult).values().forEach(domainObj -> {
                    //吧这个对象用序列化的方式深拷贝
                    //FIXME 效率不高，可以优化
                    queryDomainObject.put(String.valueOf(System.identityHashCode(domainObj)),
                            JacksonUtils.from(JacksonUtils.to(domainObj), domainClass));
                });
            } else {
                return new HashMap<>();
            }

        } else if (domainClass.isAssignableFrom(repositoryQueryResult.getClass())) {
            //如果返回的是单个对象
            //FIXME 效率不高，可以优化
            queryDomainObject.put(String.valueOf(System.identityHashCode(repositoryQueryResult)),
                    JacksonUtils.from(JacksonUtils.to(repositoryQueryResult), domainClass));
        }
        return queryDomainObject;
    }


    /**
     * 从返回结果里面，获取类型
     *
     * @param queryResult
     * @return
     */
    private Class<?> getDomainClassAfterQuery(Object queryResult) {
        return ReflectUtils.getDomainClass(queryResult);
    }


}
