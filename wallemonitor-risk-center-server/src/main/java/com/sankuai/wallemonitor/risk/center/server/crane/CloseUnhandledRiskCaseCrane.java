package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 凌晨两点关闭未处理的风险事件
 *
 * <AUTHOR>
 * @Date 2024/7/10
 */
@Slf4j
@CraneConfiguration
public class CloseUnhandledRiskCaseCrane {

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Crane(CraneConstant.CLOSE_UNHANDLED_RISK_CASE_CRANE)
    @OperateEnter(OperateEnterActionEnum.CLOSE_UNHANDLED_RISK_CASE_CRANE)
    public void run() throws Exception {
        log.info("凌晨两点关闭未处理的风险事件定时任务开始执行");
    }

}
