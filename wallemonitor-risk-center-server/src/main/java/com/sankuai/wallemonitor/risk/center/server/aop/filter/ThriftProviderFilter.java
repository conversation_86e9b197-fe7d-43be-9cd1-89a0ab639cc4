package com.sankuai.wallemonitor.risk.center.server.aop.filter;

import com.meituan.dorado.common.RpcRole;
import com.meituan.dorado.rpc.handler.filter.Filter;
import com.meituan.dorado.rpc.handler.filter.FilterHandler;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.meituan.dorado.rpc.meta.RpcResult;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.UserInfoContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * @Desc 提供服务 1、相关字段 mt_caller_action: 共用字段字段:  mt_request mt_response mt_response_code mt_response_msg mt_cost
 */
@Slf4j
@Component
public class ThriftProviderFilter extends AbstractThriftFilter implements Filter {


    @Override
    public RpcResult filter(RpcInvocation invocation, FilterHandler nextHandler) throws Throwable {
        // 1. 构建xmd
        String signature = getSignature(invocation);
        XMDLogFormat rpcProviderXmdFormat = initLogFormat(handleInvocation(invocation), signature);
        log.info("[provider] service: ({}) , request:({})",
                invocation.getServiceInterface().getName() + "." + invocation.getMethod().getName(),
                JacksonUtils.to(invocation.getArguments()));
        RpcResult result;
        Long startTime = System.currentTimeMillis();
        //2、调用
        try {
            //处理rpc的返回结果
            result = nextHandler.handle(invocation);
            Long cost = System.currentTimeMillis() - startTime;
            //打印格式化日志 + cat
            recordSuccess(rpcProviderXmdFormat, signature, result, cost);
        } catch (Throwable e) {
            Long cost = System.currentTimeMillis() - startTime;
            //打印错误并记录
            recordError("THRIFT_PROVIDER_RPC_EXCEPTION", rpcProviderXmdFormat, signature, e, cost);
            //格式化有异常的HandleResult
            result = handleErrorResult(invocation, e);
        } finally {
            UserInfoContext.clear();
        }
        // 3. （可选）接口调用后的逻辑
        log.info("[provider] service: ({}) , response:({})",
                invocation.getServiceInterface().getName() + "." + invocation.getMethod().getName(),
                JacksonUtils.to(result.getReturnVal()));
        return result;
    }


    @Override
    public int getPriority() {
        // 4. 设置优先级，值越大 优先级越高, 该Filter越先执行，不建议设置为最大值
        return 0;
    }

    @Override
    public RpcRole getRole() {
        // 5. 决定是调用端还是服务端的Filter，客户端：INVOKER, 服务端：PROVIDER, 两端都是：MULTIROLE
        return RpcRole.PROVIDER;
    }


}
