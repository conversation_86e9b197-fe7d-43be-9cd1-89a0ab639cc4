package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseNotifyDetectService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 定时检查并播报待播报的安全风险事件定时任务 1、支持按类型：并排、停滞不当、扎堆 2、支持按来源：pnc、保障系统、运维状态监控
 *
 * <AUTHOR>
 * @Date 2024/6/17
 */
@Slf4j
@CraneConfiguration
public class ReportRiskCaseStatCrane {

    @Resource
    private RiskCaseNotifyDetectService riskCaseNotifyDetectService;

    @Crane(CraneConstant.DETECT_RISK_CASE_AND_NOTIFY_CRANE)
    @OperateEnter(OperateEnterActionEnum.RISK_CASE_DETECT_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("定时检查并播报待播报的安全风险事件定时任务开始执行");
        try {
            riskCaseNotifyDetectService.detectAndNotifyRiskCase();
        } catch (Exception e) {
            log.error("定时检查并播报待播报的安全风险事件定时任务异常", e);
        }
    }
}
