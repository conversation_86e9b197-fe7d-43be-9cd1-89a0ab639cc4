package com.sankuai.wallemonitor.risk.center.server.aop;

import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import com.sankuai.wallemonitor.risk.center.server.aop.filter.CommonAroundAspect;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * domainService专用的log打印 特殊：mt_domain_action 通用 ： mt_request mt_response mt_response_code mt_response_msg mt_cost
 */
@Order(11)
@Component
@Slf4j
@Aspect
public class AdapterServiceAspect extends CommonAroundAspect {


    @Pointcut("execution(public * com.sankuai.wallemonitor.risk.center.infra..*Adapter..*(..))")
    public void accessLog() {
        // skip
    }

    /**
     * 打印的字段
     *
     * @return
     */
    @Override
    public String getLogField() {
        return LogCenterFieldEnum.AL_ADAPTER.getFieldCode();
    }

    /**
     * 做切面日志处理
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("accessLog()")
    public Object doQueryAround(ProceedingJoinPoint joinPoint) throws Throwable {
        return super.handleAroundLogAndReturnResult(joinPoint, null);
    }

}
