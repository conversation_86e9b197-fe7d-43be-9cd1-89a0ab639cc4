package com.sankuai.wallemonitor.risk.center.server.thrift;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.RISK_CASE_RISK_CASE_RELEASE_MESSAGE_ENTRY;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.VehicleRiskStatusQueryRequest;
import com.sankuai.wallemonitor.risk.center.api.response.vo.VehicleRiskStatusVO;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftVehicleRiskStatusQueryService;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseQueryService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.HandleStrategy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCategoryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftVehicleRiskStatusQueryServiceImpl implements IThriftVehicleRiskStatusQueryService {

    @Resource
    private RiskCaseQueryService riskCaseQueryService;

    @Resource
    private Map<String, HandleStrategy> riskHandleStrategyMap;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_VEHICLE_STATUS_CATEGORY, value = "", defaultValue = "", allowBlankValue = true)
    private ArrayList<RiskCaseCategoryDTO> riskCaseCategoryDTOS;

    @Override
    @OperateEnter(RISK_CASE_RISK_CASE_RELEASE_MESSAGE_ENTRY)
    public EveThriftResponse<List<VehicleRiskStatusVO>> batchQueryVehicleRiskStatusByVinList(
            VehicleRiskStatusQueryRequest request) {
        // 1 参数检查
        CheckUtil.isNotEmpty(request.getVinList(), "车辆列表不能为空");
        List<String> vinList = request.getVinList();

        // 2 根据风险事件类型 查询风险事件
        Map<Pair<Integer, Integer>, Map<String, RiskCaseInfoDTO>> sourceType2Vin2RiskCaseInfoMap = new HashMap<>();
        for (RiskCaseCategoryDTO caseCategoryDTO : riskCaseCategoryDTOS) {
            Integer source = caseCategoryDTO.getSource();
            Integer type = caseCategoryDTO.getType();
            // 根据类型查询风险事件
            List<RiskCaseInfoDTO> riskCaseInfoDTOList = riskCaseQueryService.queryRiskCaseByVinList(vinList, source,
                    type);
            if (CollectionUtils.isEmpty(riskCaseInfoDTOList)) {
                continue;
            }
            // 组装map
            Map<String, RiskCaseInfoDTO> vin2RiskCaseInfoMap = new HashMap<>();
            for (RiskCaseInfoDTO riskCaseInfoDTO : riskCaseInfoDTOList) {
                if (vin2RiskCaseInfoMap.containsKey(riskCaseInfoDTO.getVin())) {
                    continue;
                }
                vin2RiskCaseInfoMap.put(riskCaseInfoDTO.getVin(), riskCaseInfoDTO);
            }
            sourceType2Vin2RiskCaseInfoMap.put(new Pair<>(source, type), vin2RiskCaseInfoMap);
        }
        log.info("batchQueryVehicleRiskStatusByVinList sourceType2Vin2RiskCaseInfoMap:{}",
                sourceType2Vin2RiskCaseInfoMap);
        // 3 遍历车辆列表
        List<VehicleRiskStatusVO> vehicleRiskStatusVOList = new ArrayList<>();
        for (String vin : request.getVinList()) {
            // 构建车辆风险结构体
            VehicleRiskStatusVO vehicleRiskStatusVO = VehicleRiskStatusVO.builder().vin(vin).isImproperStranding(false)
                    .build();
            for (Map<String, RiskCaseInfoDTO> vin2RiskCaseInfoMap : sourceType2Vin2RiskCaseInfoMap.values()) {
                RiskCaseInfoDTO riskCaseInfoDTO = vin2RiskCaseInfoMap.getOrDefault(vin, null);
                if (Objects.isNull(riskCaseInfoDTO) || Objects.isNull(riskCaseInfoDTO.getSource()) || Objects.isNull(
                        riskCaseInfoDTO.getType())) {
                    continue;
                }
                // 匹配处置策略
                RiskCaseCategoryEnum riskCaseCategoryEnum = RiskCaseCategoryEnum.findByValue(
                        riskCaseInfoDTO.getSource(),
                        riskCaseInfoDTO.getType());
                if (Objects.isNull(riskCaseCategoryEnum)) {
                    continue;
                }
                HandleStrategy handleStrategy = riskHandleStrategyMap.getOrDefault(riskCaseCategoryEnum.getBeanName(),
                        null);
                if (Objects.isNull(handleStrategy)) {
                    continue;
                }
                // 处理风险事件
                handleStrategy.buildVehicleRiskStatus(vin, riskCaseInfoDTO, vehicleRiskStatusVO);
            }
            vehicleRiskStatusVOList.add(vehicleRiskStatusVO);
        }
        return EveThriftResponse.ok(vehicleRiskStatusVOList);
    }
}
