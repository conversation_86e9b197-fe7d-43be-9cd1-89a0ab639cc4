package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.commons.exception.JacksonException;
import com.sankuai.walleeve.domain.enums.CloudTriageEventStatusEnum;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.EventMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CloudEventTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CloudTriageHighNegativeMessageUpdatedConsumer {

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCaseRepository riskCaseRepository;


    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;


    /**
     * 分诊消息变化，触发标注
     *
     * @param message
     * @return
     */
    @MafkaConsumer(
            namespace = "waimai", topic = "cloud.triage.output.event.message",
            group = "wallemonitor.risk.high.negative.event.updated.consumer",
            deadLetter = true,
            deadLetterDelayMills = 6 * 1000
    )
    @OperateEnter(OperateEnterActionEnum.CLOUD_TRIAGE_NEGATIVE_EVENT_UPDATE_CONSUMER)
    public ConsumeStatus receive(String message) {
        try {
            if (StringUtils.isBlank(message)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 1 消息体解析
            EventMessageDTO messageDTO = JacksonUtils.from(message,
                    new TypeReference<EventMessageDTO>() {
                    });
            if (Objects.isNull(messageDTO)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            CloudEventTypeEnum cloudEventTypeEnum = CloudEventTypeEnum.getByCode(messageDTO.getEventType());
            if (Objects.isNull(cloudEventTypeEnum) || !CloudEventTypeEnum.isHighNegative(cloudEventTypeEnum)
                    || !CloudTriageEventStatusEnum.isTerminated(messageDTO.getStatus())) {
                //如果没有类型，或者不是高负向，或者非终态，不处理
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            RiskCaseDO highNegativeCase = riskCaseRepository.getByEventId(messageDTO.getEventId());
            if (Objects.isNull(highNegativeCase)) {
                //如果不存在，不进行处理
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //取车辆信息
            RiskCaseVehicleRelationDO caseVehicleRelationDO = riskCaseVehicleRelationRepository.getByEventIdAndVin(
                    highNegativeCase.getEventId(), messageDTO.getVin());
            CloudTriageEventStatusEnum msgStatus = CloudTriageEventStatusEnum.findByValue(
                    messageDTO.getStatus());
            //取标注信息
            CaseMarkInfoDO caseMarkInfoDO = Optional.ofNullable(
                    caseMarkInfoRepository.getByCaseId(highNegativeCase.getCaseId())).orElse(
                    RiskCaseFactory.initMarkInfo(highNegativeCase.getCaseId()));
            //获取状态
            ISCheckCategoryEnum categoryEnum = CloudTriageEventStatusEnum.COMPLETED.equals(msgStatus) ||
                    CloudTriageEventStatusEnum.HANDLING.equals(msgStatus) ? ISCheckCategoryEnum.GOOD_OTHER :
                    ISCheckCategoryEnum.BAD_OTHER;
            caseMarkInfoDO.manualMark(categoryEnum.getCategory(),
                    categoryEnum.getSubcategory(), "来自云安全标注", "云安全");
            //更新
            caseMarkInfoRepository.save(caseMarkInfoDO);
            //更新确认或者取消的时间
            caseVehicleRelationDO.safetyConfirmOrCancel(msgStatus, new Date(messageDTO.getTimestamp()));
            riskCaseVehicleRelationRepository.save(caseVehicleRelationDO);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (JacksonException e) {
            log.error(StringMessageFormatter.replaceMsg(
                            "CLOUD_TRIAGE_NEGATIVE_EVENT_UPDATE_CONSUMER# consume# message parse error, message = {}", message),
                    e);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (UnableGetLockException e) {
            log.warn(StringMessageFormatter.replaceMsg(
                            "CLOUD_TRIAGE_NEGATIVE_EVENT_UPDATE_CONSUMER# consume# message UnableGetLockException error, message = {}",
                            message),
                    e);
            return ConsumeStatus.CONSUME_FAILURE;

        } catch (Exception e) {
            log.error(StringMessageFormatter.replaceMsg(
                            "CLOUD_TRIAGE_NEGATIVE_EVENT_UPDATE_CONSUMER# consume# message unknown error, message = {}",
                            message),
                    e);
            return ConsumeStatus.CONSUME_FAILURE;
        }

    }


}
