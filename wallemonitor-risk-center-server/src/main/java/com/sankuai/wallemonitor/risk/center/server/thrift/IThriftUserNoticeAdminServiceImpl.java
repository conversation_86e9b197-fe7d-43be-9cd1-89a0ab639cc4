package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.dto.TokenCheckDTO;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.UserConfirmNoticeRequest;
import com.sankuai.wallemonitor.risk.center.api.request.WechatBasicFrontRequest;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftUserNoticeAdminService;
import com.sankuai.wallemonitor.risk.center.api.vo.UserNoticeVersionVO;
import com.sankuai.wallemonitor.risk.center.domain.component.WechatAuthAdminService;
import com.sankuai.wallemonitor.risk.center.domain.service.UserNoticeAdminService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.UserNoticeReadRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftUserNoticeAdminServiceImpl implements IThriftUserNoticeAdminService {


    @Resource
    private UserNoticeAdminService userNoticeAdminService;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private WechatAuthAdminService wechatAuthAdminService;

    /**
     * 获取用户须知 该方法用于获取用户须知，并与用户已阅读的版本进行对比，若不一致则返回最新版本的用户须知给用户。
     *
     * @return 用户须知版本信息
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.GET_USER_NOTICE)
    public EveThriftResponse<UserNoticeVersionVO> getUserNotice(WechatBasicFrontRequest request) {
        //1 登陆态校验
        CheckUtil.isNotBlank(request.getToken(), "用户登陆态不可为空");
        TokenCheckDTO tokenCheckDTO = wechatAuthAdminService.checkToken(request.getToken());
        if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
            return EveThriftResponse.codeWithMessage(HttpStatus.UNAUTHORIZED.value(), "登陆态过期，请重新登陆").build();
        }

        String userId = tokenCheckDTO.getOpenId();
        //3 与lion 中最新的版本进行对比
        Long currentVersionId = userNoticeAdminService.queryLatestNoticeVersionByUserId(userId);
        Long targetVersionId = lionConfigRepository.getUserNoticeVersionId();
        String versionContext = lionConfigRepository.getUserNoticeVersionContext();
        // 如果用户第一次登陆或者已阅读的版本与最新版本不一致
        if (Objects.isNull(currentVersionId) || !currentVersionId.equals(targetVersionId)) {
            //4 创建一条用户须知记录，并标志为未阅读
            createUserNoticeReadRecord(userId, targetVersionId);
            //5 返回最新版本的用户须知给用户
            log.info("getUserNotice, userId = {}, currentVersionId = {}, targetVersionId = {}", userId,
                    currentVersionId, targetVersionId);
            return EveThriftResponse.ok(UserNoticeVersionVO.builder().isDisplay(true).versionId(targetVersionId)
                    .versionContext(versionContext).build());
        }
        return EveThriftResponse.ok(
                UserNoticeVersionVO.builder().isDisplay(false).versionId(targetVersionId).versionContext(versionContext)
                        .build());
    }

    /**
     * 用户确认已阅读用户须知
     *
     * @param request 用户确认须知请求
     * @return 空响应 该方法用于用户确认已阅读用户须知，并更新用户须知的阅读状态。
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.CONFIRM_USER_NOTICE)
    public EveThriftResponse<EmptyResponse> confirmUserNotice(UserConfirmNoticeRequest request) {
        // 1 登陆态校验
        CheckUtil.isNotBlank(request.getToken(), "用户登陆态不可为空");
        TokenCheckDTO tokenCheckDTO = wechatAuthAdminService.checkToken(request.getToken());
        if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
            return EveThriftResponse.codeWithMessage(HttpStatus.UNAUTHORIZED.value(), "登陆态过期，请重新登陆").build();
        }
        //2 参数校验
        CheckUtil.isNotNull(request.getVersionId(), "版本ID不可为空");
        String userId = tokenCheckDTO.getOpenId();

        log.info("confirmUserNotice, userId = {}, versionId = {}", userId, request.getVersionId());
        //3 根据用户ID 和 版本ID 定位 用户阅读须知，并更新
        userNoticeAdminService.queryThenUpdateUserNotice(userId, request.getVersionId());
        return EveThriftResponse.ok().build();
    }

    /**
     * 创建用户须知阅读记录
     *
     * @param userId    用户ID
     * @param versionId 版本ID 该方法用于创建一条用户须知阅读记录，并将其标记为未阅读状态。
     */
    private void createUserNoticeReadRecord(String userId, Long versionId) {
        UserNoticeReadRecordDO userNoticeReadRecordDO = new UserNoticeReadRecordDO();
        userNoticeReadRecordDO.setUserId(userId);
        userNoticeReadRecordDO.setVersionId(versionId);
        userNoticeReadRecordDO.setConfirm(false);
        userNoticeAdminService.insertUserNoticeReadRecord(userNoticeReadRecordDO);
    }


}
