package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CatMonitorConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.OnboardCommonMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.DataContainerConsumeConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.ObstacleAbstractConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.ControlArbitrationInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.DataContainerDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.ObstacleFineTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PlannerResultData;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PlannerResultData.ChosenReferenceLine;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PlannerResultData.Curve;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PlannerResultData.ReferencePath;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskStrandingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskStrandingRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.VelocityUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.OptionalDouble;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 高负向消息消费者
 */
@Component
@Slf4j
public class OnboardMessageConsumer {

    /**
     * 车辆上下文仓储
     */
    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;


    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private RiskStrandingRecordRepository riskStrandingRecordRepository;


    private static final String[] ALL_POSTURE = {GeoElementTypeKeyConstant.RIGHT, GeoElementTypeKeyConstant.LEFT,
            GeoElementTypeKeyConstant.PREDECESSOR, GeoElementTypeKeyConstant.SUCCESSOR};


    @MafkaConsumer(
            namespace = "waimai", topic = "mad-vehicle.real.onboard.message",
            group = "risk.mad-vehicle.real.onboard.message.consumer"
    )
    @OperateEnter(OperateEnterActionEnum.COMMON_ONBOARD_MESSAGE_CONSUMER_ENTRY)
    public ConsumeStatus consume(String message) {
        if (StringUtils.isBlank(message)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        String topic = JacksonUtils.getAsString(message, "topic");
        if (StringUtils.isBlank(topic)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            Class<?> clazz = lionConfigRepository.getOnboardMessageClassByTopic(topic);
            if (Objects.isNull(clazz)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //解析消息体
            OnboardCommonMessageDTO<?> onboardCommonMessageDTO = JacksonUtils.from(message,
                    getTypeReference(clazz));
            if (onboardCommonMessageDTO == null || StringUtils.isBlank(onboardCommonMessageDTO.getTopic())
                    || StringUtils.isBlank(onboardCommonMessageDTO.getVin())
                    || Objects.isNull(onboardCommonMessageDTO.getData())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            String vin = onboardCommonMessageDTO.getVin();
            //优化时间戳
            Long timestamp = DatetimeUtil.getMicoTimeStampFromTime(onboardCommonMessageDTO.getTimestamp());
            if (Objects.isNull(timestamp) || timestamp == 0) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 如果当前时间减去过去时间戳，大于允许的阈值
            boolean isDelay =
                    DatetimeUtil.getTimeDiff(new Date(timestamp), new Date(), ChronoUnit.SECONDS) > lionConfigRepository.getOnboardMessageAllowLaterThreshold();
            // 如果消息时间戳，落后于当前时间Xs，打点
            Cat.logEvent(CatMonitorConstant.MESSAGE_LATER_THAN_NOW, "onboard_message_"+topic,
                    BooleanUtils.toString(isDelay,"-1","0"),
                    "vin=" + vin);
            if (VehicleHighNegativeMessageDTO.class.equals(clazz)) {
                return consumeHighNegativeMessage((VehicleHighNegativeMessageDTO) onboardCommonMessageDTO.getData(),
                        vin, timestamp);
            }
            if (PerceptionObstacleDTO.class.equals(clazz)) {
                return consumeObstacleMessage((PerceptionObstacleDTO) onboardCommonMessageDTO.getData(), vin,
                        timestamp);
            }
            if (ControlArbitrationInfoDTO.class.equals(clazz)) {
                return consumeControlArbitrationMessage((ControlArbitrationInfoDTO) onboardCommonMessageDTO.getData(),
                        message,
                        vin, timestamp);
            }
            if (PlannerResultData.class.equals(clazz)) {
                return consumePlannerResult((PlannerResultData)onboardCommonMessageDTO.getData(), vin, timestamp);
            }

            if(DataContainerDTO.class.equals(clazz)) {
                DataContainerConsumeConfigDTO dataContainerConsumeConfigDTO = lionConfigRepository.getDataContainerConsumeConfigDTO();
                if (dataContainerConsumeConfigDTO == null || !dataContainerConsumeConfigDTO.getOpen()) {
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                return consumeDataContainerMessage((DataContainerDTO) onboardCommonMessageDTO.getData(), vin, timestamp);
            }
        } catch (Exception e) {
            //瞬时性消息，先不失败
            log.error(StringMessageFormatter.replaceMsg("OnboardMessageConsumer consume error, message:{}", message),
                    e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 消费车道点
     * 
     * @param data
     * @param message
     * @param vin
     * @param timestamp
     * @return
     */
    private ConsumeStatus consumePlannerResult(PlannerResultData data, String vin, Long timestamp) {
        //
        String area = vehicleAdapter.getVehicleHdMapArea(vin);
        if (StringUtils.isBlank(area)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        Integer zone = hdMapAdapter.getUtmZoneByArea(area);
        List<PositionDO> lanePoints = Optional.ofNullable(data).map(PlannerResultData::getChosenReferenceLine)
                .map(ChosenReferenceLine::getReferencePath).map(ReferencePath::getCurve).map(Curve::getPoint)
                .orElse(new ArrayList<>()).stream()
                .map(point -> GeoToolsUtil.utmToWgs84WithZone(point.getX(), point.getY(), zone))
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lanePoints)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = vehicleRuntimeInfoContextRepository.getFromCache(vin);
        vehicleRuntimeInfoContextDO.setRefinedLineList(lanePoints);
        vehicleRuntimeInfoContextRepository.updateCache(vehicleRuntimeInfoContextDO, timestamp);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理ControlArbitration信息
     *
     * @param controlArbitrationInfoMessage 消息体
     * @return
     */
    private ConsumeStatus consumeControlArbitrationMessage(ControlArbitrationInfoDTO controlArbitrationInfoMessage,
            String message, String vin, Long timestamp) {
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = vehicleRuntimeInfoContextRepository.getFromCache(vin);
        vehicleRuntimeInfoContextDO.updateMonitorMetricsInfo(controlArbitrationInfoMessage, message);
        vehicleRuntimeInfoContextRepository.updateCache(vehicleRuntimeInfoContextDO, timestamp);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理高负向消息
     *
     * @param vehicleHighNegativeMessageDTO 消息体
     * @return
     */
    private ConsumeStatus consumeHighNegativeMessage(VehicleHighNegativeMessageDTO vehicleHighNegativeMessageDTO,
            String vin,
            Long timestamp) {
        //查询对应的缓存vehicleRunTime，不存在时会获取一个空对象
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = vehicleRuntimeInfoContextRepository.getFromCache(vin);
        //更新信息
        vehicleRuntimeInfoContextDO.updateFromHighNegative(vehicleHighNegativeMessageDTO);
        vehicleRuntimeInfoContextRepository.updateCache(vehicleRuntimeInfoContextDO, timestamp);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理障碍物消息
     *
     * @param obstacleDTO 消息体
     * @return
     */
    private ConsumeStatus consumeObstacleMessage(PerceptionObstacleDTO obstacleDTO, String vin, Long timestamp) {

        Double reserveDistance = lionConfigRepository.getObstacleReserveDistance();
        if (Objects.isNull(reserveDistance)) {
            // 没有保留距离的时候，不做处理
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 查询对应的缓存vehicleRunTime，不存在时会获取一个空对象
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = vehicleRuntimeInfoContextRepository.getFromCache(vin);
        // 更新信息
        vehicleRuntimeInfoContextDO.updateObstacle(obstacleDTO, reserveDistance);

        ObstacleAbstractConfigDTO obstacleAbstractConfig = lionConfigRepository.getObstacleAbstractConfig();
        vehicleRuntimeInfoContextDO.updateObstacleAbstract(new ArrayList<>());
        if(obstacleAbstractConfig != null && obstacleAbstractConfig.getOpen()) {
            List<VehicleRuntimeInfoContextDO.ObstacleAbstract> obstacleAbstracts =
                    computeObstacleAbstractList(obstacleDTO,  vehicleRuntimeInfoContextDO);

            vehicleRuntimeInfoContextDO.updateObstacleAbstract(obstacleAbstracts);
        }

        // 更新缓存
        vehicleRuntimeInfoContextRepository.updateCache(vehicleRuntimeInfoContextDO, timestamp);
        return ConsumeStatus.CONSUME_SUCCESS;
    }


    /**
     * 处理traffic_flow_buffer 消息
     *
     * */
    public ConsumeStatus consumeDataContainerMessage(DataContainerDTO dataContainerDTOMessage, String vin, long timestamp) {
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = vehicleRuntimeInfoContextRepository.getFromCache(vin);

        Optional.ofNullable(dataContainerDTOMessage)
                .map(DataContainerDTO::getPlannerMasterState)
                .map(DataContainerDTO.PlannerMasterState::getPlannerWorldState)
                .map(DataContainerDTO.PlannerWorldState::getPlannerHistory)
                .map(DataContainerDTO.PlannerHistory::getTrafficFlowStateBuffer)
                .ifPresent(x -> {
                    // 找到最匹配的车道
                    Double speed  = calculateTrafficFlowSpeed(x, vin);
                    // 更新 车流速度
                    vehicleRuntimeInfoContextDO.updateTrafficFlowSpeed(speed);
                    vehicleRuntimeInfoContextRepository.updateCache(vehicleRuntimeInfoContextDO, timestamp);
                });


        return ConsumeStatus.CONSUME_SUCCESS;
    }


    /**
     * 获取topK的流，找到最大匹配流
     * @param  bufferList      所有流的列表
     * @param  vin             车架号
     * */
    protected Double calculateTrafficFlowSpeed(
            List<DataContainerDTO.OptionalTrafficFlowState> bufferList,
            String vin)
    {
        if (CollectionUtils.isEmpty(bufferList)) {
            log.info("vin = {}, bufferList is empty", vin);
            return 0.0;
        }

        // 车流最大索引
        long maxIndex =  Math.min(
                lionConfigRepository.getDataContainerConsumeConfigDTO().getTopKTrafficFlowBuffer(),
                bufferList.stream().map(DataContainerDTO.OptionalTrafficFlowState::validTrafficFlowFeatures).count()
                ) - 1 ;

        // 当前查到的车流索引
        int bufferIndex = 0;

        // 车流匹配障碍物最大匹配数
        long maxMatch = Long.MIN_VALUE;

        // 最大匹配下标
        int maxMatchIndex = -1;

        // 获取障碍物摘要
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = vehicleRuntimeInfoContextRepository.getFromCache(vin);
        List<VehicleRuntimeInfoContextDO.ObstacleAbstract> obstacleAbstracts = vehicleRuntimeInfoContextDO.getObstacleAbstracts();
        if(obstacleAbstracts == null) {
            return null;
        }

        while (bufferIndex <= maxIndex) {
            DataContainerDTO.OptionalTrafficFlowState optionalTrafficFlowState = bufferList.get(bufferIndex);
            // 获取当前流内所有的obstacleId
            Set<Integer> obstacleIdList = optionalTrafficFlowState.getTrafficFlowObstacleFeatures().stream()
                    .filter(Objects::nonNull)
                    .map(DataContainerDTO.TrafficFlowObstacleFeature::getTrafficFlowObstacles)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .filter(Objects::nonNull)
                    .map(DataContainerDTO.TrafficFlowObstacle::getObstacleId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 查询在车道内、前继车道、后继车道内的obstacle
            long preCount = obstacleAbstracts.stream()
                    .filter(obstacleAbstract -> obstacleAbstract.getPosture().equals(GeoElementTypeKeyConstant.PREDECESSOR)
                            && obstacleIdList.contains(Integer.parseInt(obstacleAbstract.getObstacleId())))
                    .count();

            long sucCount = obstacleAbstracts.stream()
                    .filter(obstacleAbstract -> obstacleAbstract.getPosture().equals(GeoElementTypeKeyConstant.SUCCESSOR)
                            && obstacleIdList.contains(Integer.parseInt(obstacleAbstract.getObstacleId())))
                    .count();

            long maxCount = preCount + sucCount;

            if (maxCount > maxMatch) {
                maxMatch = maxCount;
                maxMatchIndex = bufferIndex;
            }
            bufferIndex++;
        }

        if (maxMatchIndex == -1) {
            log.warn("vin = {}, 未能找到最匹配的流", vin);
            return 0.0;
        }

        DataContainerDTO.OptionalTrafficFlowState maxMatchState = bufferList.get(maxMatchIndex);


        List<DataContainerDTO.TrafficFlowObstacleFeature> maxMatchFeatures = maxMatchState.getTrafficFlowObstacleFeatures();
        if (CollectionUtils.isEmpty(maxMatchFeatures)) {
            return 0.0;
        }

        log.info("maxMatch Features : {}, matchCount = {}", maxMatchFeatures, maxMatch);

        OptionalDouble average = maxMatchFeatures.stream()
                .filter(Objects::nonNull)
                .map(DataContainerDTO.TrafficFlowObstacleFeature::getTrafficFlowObstacles)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(DataContainerDTO.TrafficFlowObstacle::getObstacleSpeed)
                .filter(Objects::nonNull)
                .mapToDouble(x -> x)
                .average();
        if(average.isPresent()) {
            return average.getAsDouble();
        }

        return 0.0;
    }




    private List<VehicleRuntimeInfoContextDO.ObstacleAbstract> computeObstacleAbstractList(
            PerceptionObstacleDTO obstacleDTO,
            VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO
    ) {
        if(obstacleDTO == null) {
            // 没有障碍物上下文，直接返回
            return new ArrayList<>();
        }

        List<PerceptionObstacleDTO.PerceptionObstacle> perceptionObstacle = obstacleDTO.getPerceptionObstacle();
        if(perceptionObstacle == null || perceptionObstacle.size() == 0) {
            // 没有障碍物，直接返回
            return Lists.newArrayList();
        }

        // 获取当前位置
        PositionDO curPosition = vehicleRuntimeInfoContextDO.getLocation();

        // 获取上次记录的位置
        PositionDO preVehiclePosition = queryPreVehiclePosition(vehicleRuntimeInfoContextDO, curPosition);

        if(preVehiclePosition == null) {
            // 前序位置为空，直接返回
            log.warn("vin = {}, 车辆前序位置为空", vehicleRuntimeInfoContextDO.getVin());
            return Lists.newArrayList();
        }

        Map<String, List<HdMapElementGeoDO>> lanePositionMap = queryLaneInfo(vehicleRuntimeInfoContextDO, curPosition, preVehiclePosition);


        return ParallelExecutor.executeParallelTasksAndGetResult("obstacleAbstract",
                perceptionObstacle,
                obstacle -> computeObstacleAbstract(obstacle, curPosition, preVehiclePosition, lanePositionMap)
        ).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }


    private VehicleRuntimeInfoContextDO.ObstacleAbstract computeObstacleAbstract (
            PerceptionObstacleDTO.PerceptionObstacle obstacle,
            PositionDO curPosition,
            PositionDO preVehiclePosition,
            Map<String, List<HdMapElementGeoDO>> positionMap
    ) {
        ObstacleAbstractConfigDTO obstacleAbstractConfig = lionConfigRepository.getObstacleAbstractConfig();
        if (obstacle == null ||
            obstacle.getPosition() == null ||
            obstacle.getObstacleType() == null ||
                CollectionUtils.isEmpty(obstacleAbstractConfig.getFineTypeList())
                || !obstacleAbstractConfig.getFineTypeList().contains(obstacle.findShortFineType())) {
            return null;
        }

        PositionDO positionDO = GeoToolsUtil.utmToWgs84(obstacle.getPosition().getX(),
                obstacle.getPosition().getY());
        // 设置车道位姿
        String posture = null;
        if (positionMap != null) {
            for (Map.Entry<String, List<HdMapElementGeoDO>> entry : positionMap.entrySet()) {
                // 如果前、后、左、右有一个车道匹配上，设置Posture
                if (entry.getValue().stream().anyMatch(x -> x.isInPolygon(positionDO))) {
                    posture = entry.getKey();
                    break;
                }
            }
        }

        // 设置行径夹角
        Double angle = preVehiclePosition != null ? GeoToolsUtil.angleCalc(
                curPosition,
                positionDO,  // 使用已经转换好的WGS84坐标
                preVehiclePosition,
                curPosition
        ) : null;

        // 自车定位距离
        Double distance = GeoToolsUtil.distance(curPosition, positionDO);

        // 定位字符串
        String position = positionDO.getLongitude() + "," + positionDO.getLatitude();

        // 矢量速度 -> 合速度
        Double speed = VelocityUtils.calculateTotalVelocity(obstacle.getVelocity());

        return VehicleRuntimeInfoContextDO.ObstacleAbstract.builder()
                .obstacleId(obstacle.getId())
                .angle(angle)
                .position(position)
                .fineType(ObstacleFineTypeEnum.transferShotName(obstacle.getObstacleType().getFineType()))
                .posture(posture)
                .distance(distance)
                .velocity(obstacle.getVelocity())
                .acceleration(obstacle.getAcceleration())
                // 添加矢量速度
                .speed(speed).build();


    }

    private Map<String, List<HdMapElementGeoDO>> queryLaneInfo(
            VehicleRuntimeInfoContextDO vehicleInfo,
            PositionDO curPosition,
            PositionDO preVehiclePosition
    ) {
        // 获取车道信息（
        ObstacleAbstractConfigDTO obstacleAbstractConfigDTO = lionConfigRepository.getObstacleAbstractConfig();
        if(obstacleAbstractConfigDTO == null) {
            return null;
        }
        String hdMapArea = vehicleAdapter.getVehicleHdMapArea(vehicleInfo.getVin());
        HdMapAdapter.SearchNearbyRequestVTO searchParam = HdMapAdapter.SearchNearbyRequestVTO.builder()
                .hdMapEnum(HdMapEnum.LANE_POLYGON)
                .area(hdMapArea)
                .restrictType(obstacleAbstractConfigDTO.getLaneTypeList())
                .positionDO(vehicleInfo.getLocation())
                .range(obstacleAbstractConfigDTO.getRange())
                .build();

        List<HdMapElementGeoDO> nearbyLanes = hdMapAdapter.searchNearby(searchParam);
        List<HdMapElementGeoDO> vehicleLanes = nearbyLanes.stream()
                .filter(lane -> lane.isInPolygon(vehicleInfo.getLocation()))
                .filter(lane -> lane.isSameDirection(preVehiclePosition, curPosition, obstacleAbstractConfigDTO.getLaneSameDirectionTheta()))
                .collect(Collectors.toList());
        log.info("query nearby lanes : {}, filter vehicle lanes : {}", nearbyLanes, vehicleLanes);

        if (vehicleLanes.isEmpty()) {
            return null;
        }

        HdMapElementGeoDO currentLane = vehicleLanes.get(vehicleLanes.size() - 1);

        Map<String, List<HdMapElementGeoDO>> positionMap = new HashMap<>();

        for(String posture : ALL_POSTURE) {
            if(posture.equals(GeoElementTypeKeyConstant.SUCCESSOR) || posture.equals(GeoElementTypeKeyConstant.PREDECESSOR)) {
                // 前后车道，处理列表
                List<String> multiLanes = currentLane.<List<String>>getPropertyByKey(posture);
                if(multiLanes != null) {
                    positionMap.put(posture, multiLanes.stream().map(laneId -> hdMapAdapter.getLaneById(laneId)).collect(Collectors.toList()));
                }
            } else {
                // 左右车道，处理String
                HdMapElementGeoDO hdMapElementGeoDO = hdMapAdapter.getLaneById(currentLane.getPropertyByKey(posture));
                if(hdMapElementGeoDO != null) {
                    positionMap.put(posture, Lists.newArrayList(hdMapElementGeoDO));
                }
            }

        }

        positionMap.put(GeoElementTypeKeyConstant.CUR, Lists.newArrayList(currentLane));

        return positionMap;
    }

    /**
     * 查询车辆前序位置
     *
     * @param vehicleRuntimeInfoContextDO
     * @param curPosition
     * @return
     */
    private PositionDO queryPreVehiclePosition(VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO,
            PositionDO curPosition) {
        String vin = vehicleRuntimeInfoContextDO.getVin();
        ObstacleAbstractConfigDTO obstacleAbstractConfig = lionConfigRepository.getObstacleAbstractConfig();

        // todo: 查询车辆当前时刻是否存在停滞记录，如果存在则计算前序位置时使用 停滞时刻 - 时间阈值
        // 即停滞时需要取停滞开始前的时间，非停滞状态则需要取当前的时间
        RiskStrandingRecordDO recordDO = queryStrandingRecord(vehicleRuntimeInfoContextDO.getVin());
        Date occurTime = recordDO != null ? recordDO.getOccurTime() : new Date();
        Date timeBeforeOccurTime = DatetimeUtil.getNSecondsBeforeDateTime(occurTime,
                obstacleAbstractConfig.getPastSecond());

        // 查询历史位置数据
        List<VehicleInQueuePositionDTO> vehicleLastLocationList = vehicleAdapter
                .queryVehicleHistoryDataFromEveReplay(
                        vin,
                        DatetimeUtil.getTimeInSeconds(timeBeforeOccurTime),
                        DatetimeUtil.getTimeInSeconds(occurTime)
                )
                .stream()
                .map(vehicleDataInfoVO -> {
                    PositionDO positionDO = PositionDO.getPosition(
                            vehicleDataInfoVO.getLongitude(),
                            vehicleDataInfoVO.getLatitude(),
                            CoordinateSystemEnum.WGS84
                    );
                    if (positionDO == null) {
                        log.warn("positionDO is null, vehicleDataInfoVO: {}", JacksonUtils.to(vehicleDataInfoVO));
                        return null;
                    }
                    Double distance = GeoToolsUtil.distance(positionDO, curPosition);
                    if (distance == null) {
                        log.warn("distance is null, vehicleDataInfoVO: {}", JacksonUtils.to(vehicleDataInfoVO));
                        return null;
                    }
                    return VehicleInQueuePositionDTO.builder()
                            .position(positionDO)
                            .distance(distance)
                            .time(vehicleDataInfoVO.getTime())
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 检查处理后的列表是否为空
        if (CollectionUtils.isEmpty(vehicleLastLocationList)) {
            log.warn("处理后的历史轨迹数据为空, vin: {}", vin);
            return null;
        }
        // 获取前一个位置点
        Collections.reverse(vehicleLastLocationList);

        return vehicleLastLocationList.stream()
                .filter(position -> position != null
                        && position.getDistance() != null
                        && position.getPosition() != null
                        && position.getDistance() > obstacleAbstractConfig.getPreMinDistance()) // 设置最小距离阈值
                .findFirst()
                .map(VehicleInQueuePositionDTO::getPosition)
                .orElse(null);


    }

    private <T> TypeReference<OnboardCommonMessageDTO<T>> getTypeReference(final Class<T> tClass) {
        return new TypeReference<OnboardCommonMessageDTO<T>>() {
            @Override
            public Type getType() {
                return constructParameterizedType(OnboardCommonMessageDTO.class, tClass);
            }
        };
    }

    private ParameterizedType constructParameterizedType(final Class<?> raw, final Type... args) {
        return new ParameterizedType() {
            public Type getRawType() {
                return raw;
            }

            public Type[] getActualTypeArguments() {
                return args;
            }

            public Type getOwnerType() {
                return null;
            }
        };
    }

    /**
     * 查询车辆是否滞留记录
     *
     * @param vin
     * @return
     */
    private RiskStrandingRecordDO queryStrandingRecord(String vin) {
        // 1 参数校验
        if (StringUtils.isBlank(vin)) {
            return null;
        }
        // 2 查询指定车辆在【-5， 当前】事件段内是否存在停滞时间
        RiskStrandingRecordDOQueryParamDTO paramDTO = RiskStrandingRecordDOQueryParamDTO.builder()
                .vinList(Arrays.asList(vin))
                // todo: 5分钟之前
                .createTimeGreatTo(DatetimeUtil.getNSecondsBeforeDateTime(new Date(),
                        5 * DateTimeConstant.ONE_MINUTE_SECOND))
                .statusList(DetectRecordStatusEnum.getUnCancel()).build();
        List<RiskStrandingRecordDO> recordDOList = riskStrandingRecordRepository.queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(recordDOList)) {
            return null;
        }
        // 理论上只会存在一个
        return recordDOList.get(0);
    }


}
