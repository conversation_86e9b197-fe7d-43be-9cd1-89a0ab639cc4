package com.sankuai.wallemonitor.risk.center.server.crane;


import com.cip.crane.client.common.util.ShardItemsContext;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.VehicleCounterRuleConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.VehicleCounterRuleDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeInfoContextDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

/**
 * 车辆运行时状态信息更新定时任务
 * 1.获取全量车辆上下文数据记录
 * 2.遍历去redis拉取最新的runtime info
 * 3.更新last_update_time字段
 * 4.存表
 */
@Slf4j
@CraneConfiguration
public class VehicleRuntimeInfoContextUpdateCrane {

    private final static int BATCH_SIZE = 50;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Crane(CraneConstant.VEHICLE_RUNTIME_INFO_CONTEXT_UPDATE_CRANE)
    @OperateEnter(OperateEnterActionEnum.VEHICLE_RUNTIME_INFO_CONTEXT_UPDATE_CRANE_ENTRY)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("车辆运行时状态信息更新定时任务开始执行");
        try {
            // FIXME 只取最近24h有更新的是否会漏？
            TimePeriod lastUpdateTimeRange = TimePeriod.builder()
                    .beginDate(DatetimeUtil.getNSecondsBeforeDateTime(CommonConstant.SECONDS_PER_DAY))
                    .endDate(new Date()).build();
            // 取分片车
            Set<String> lastUpdatedVin = getSharedVehicle(
                    vehicleRuntimeInfoContextRepository.queryFromCache(lastUpdateTimeRange));
            if (CollectionUtils.isEmpty(lastUpdatedVin)) {
                return;
            }
            Lists.partition(Lists.newArrayList(lastUpdatedVin), BATCH_SIZE).forEach((subVinList) -> {
                //每百个进行更新
                VehicleRuntimeInfoContextDOQueryParamDTO paramDTO = VehicleRuntimeInfoContextDOQueryParamDTO
                        .builder().vinList(subVinList).build();
                //取map
                Map<String, VehicleRuntimeInfoContextDO> vehicleRuntimeInfoContextDOMap = vehicleRuntimeInfoContextRepository.queryByParam(
                                paramDTO)
                        .stream().collect(Collectors.toMap(VehicleRuntimeInfoContextDO::getVin, Function.identity(),
                                (v1, v2) -> v1));
                ParallelExecutor.executeParallelTasks("vehicle_runtime_info", subVinList, vin -> {
                    try {
                        lockUtils.batchLockCanWait(LockKeyPreUtil.buildVinContextKey(Sets.newHashSet()), () -> {
                            //创建或者更新
                            VehicleRuntimeInfoContextDO fromDB = vehicleRuntimeInfoContextDOMap.getOrDefault(vin,
                                    VehicleRuntimeInfoContextDO.builder().vin(vin).build()
                            );
                            VehicleRuntimeInfoContextDO latest = vehicleRuntimeInfoContextRepository.getFromCache(
                                    vin);
                            VehicleCounterRuleConfig vehicleCounterRuleConfig = lionConfigRepository.getVehicleCounterRuleConfig();
                            //更新上下文
                            fromDB.updateToSave(latest);
                            if (vehicleCounterRuleConfig != null) {
                                //使用最新的缓存，看满足什么积累规则
                                List<Pair<Boolean, VehicleCounterRuleDO>> needCounterRuleList = vehicleCounterRuleConfig
                                        .doCounter(fromDB, true);
                                //更新
                                fromDB.updateCounter(needCounterRuleList);
                            }
                            //设置更新时间
                            fromDB.setLastUpdateTime(new Date());
                            vehicleRuntimeInfoContextRepository.save(fromDB);
                        });
                    } catch (Exception e) {
                        log.error(StringMessageFormatter.replaceMsg("更新车辆运行时状态信息失败, context:{}", vin),
                                e);
                    }
                });
            });
        } catch (Exception e) {
            log.error("车辆运行时状态信息更新定时任务执行失败", e);
        }
    }

    /**
     * 任务分片
     * 
     * @param queryFromCache
     * @return
     */
    private Set<String> getSharedVehicle(Set<String> queryFromCache) {
        if (CollectionUtils.isEmpty(queryFromCache)) {
            return Sets.newHashSet();
        }
        int shardCount = ShardItemsContext.getShardCount();
        if (shardCount <= 1) {
            return queryFromCache;
        }
        List<Integer> shardItems = ShardItemsContext.getShardItems();
        log.debug("shardItems:{}", shardItems);
        return queryFromCache.stream().filter(vin -> {
            // 落在应该在的分片里面
            return shardItems.contains(hashVin(vin, shardCount));
        }).collect(Collectors.toSet());
    }

    /**
     * 做hash，确保字符串能被分区在 1,shardCount 之间，闭区间，shareCount=1时返回1
     *
     * @param vin
     * @param shardCount
     * @return
     */
    private Integer hashVin(String vin, int shardCount) {
        // 1. 取vin的hashCode
        // 2. 对分片数取模得到 0 到 shardCount-1 之间的值
        // 3. 取绝对值避免负数
        // 去掉最后的+1，直接返回[0, shardCount-1]范围的值
        return Math.abs(vin.hashCode() % shardCount);
    }
}
