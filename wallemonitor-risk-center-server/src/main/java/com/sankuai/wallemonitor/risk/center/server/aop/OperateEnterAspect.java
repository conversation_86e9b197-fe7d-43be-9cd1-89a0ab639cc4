package com.sankuai.wallemonitor.risk.center.server.aop;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mtrace.Span;
import com.meituan.mtrace.Tracer;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.OperateEnterContext;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.UserInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.OperateEnterDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SSOLogInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.DomainEventConfig;
import com.sankuai.wallemonitor.risk.center.server.aop.filter.CommonAroundAspect;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 操作切面
 *
 * <AUTHOR> liaolingfei
 * @Description : 操作入口切面
 * @Date 2023/6/9 16:43
 **/
@Order(2)
@Component
@Slf4j
@Aspect
public class OperateEnterAspect extends CommonAroundAspect {

    /**
     * 消息的时间戳
     */
    private static final String MESSAGE_TIME_STAMP_KEY = "timestamp";
    @MessageProducer(topic = MessageTopicEnum.WALLEMONITOR_RISK_DOMAIN_EVENT_MESSAGE)
    private CommonMessageProducer<DomainEventDTO> domainMessageProducer;


    @MessageProducer(topic = MessageTopicEnum.WALLEMONITOR_RISK_DOMAIN_EVENT_ASYNC_MESSAGE)
    private CommonMessageProducer<DomainEventDTO> asyncDomainMessageProduce;


    @Resource
    private DomainEventMessageProducerProvider domainEventMessageProducerProvider;

    @ConfigValue(key = "domain.event.message.partition", defaultValue = "10")
    private Integer domainEventMessagePartition;

    /**
     * 打印的字段
     *
     * @return
     */
    @Override
    public String getLogField() {
        return LogCenterFieldEnum.AL_PROVIDER.getFieldCode();
    }

    @Around(value = "@annotation(operateEnter)")
    public Object doAround(ProceedingJoinPoint joinPoint, OperateEnter operateEnter) throws Throwable {
        Map<String, Object> param = new HashMap<>();
        Throwable throwable = null;
        Object result = null;
        Transaction transaction = Cat.newTransaction("API", getSignature(joinPoint));
        long startTime = System.currentTimeMillis();
        try {
            //初始化切面
            initOperateContext(joinPoint, operateEnter);
            param = beforeLog(joinPoint);
            //切面执行
            result = joinPoint.proceed();

            return result;
        } catch (Throwable e) {
            throwable = e;
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            transaction.setDurationInMillis(duration);
            transaction.complete();
            //按照本次领域操作涉及到的领域，进行处理
            Map<String, List<DomainEventDTO<?>>> domainEventVOMap = getDomainMap();
            //发送消息
            sendDomainMessage(domainEventVOMap);
            Throwable exception = null;
            if (throwable != null) {
                exception = throwable;
            }
            //记录日志
            afterLog(param, exception, result, true, true);
            //释放领域对象
            OperateEnterContext.clear();
        }
    }

    //发消息
    private void sendDomainMessage(Map<String, List<DomainEventDTO<?>>> domainEventVOMap) {
        if (MapUtils.isEmpty(domainEventVOMap)) {
            return;
        }
        //按照类型进行组合
        domainEventVOMap.forEach((className, domainEventDTOList) -> {
            if (CollectionUtils.isEmpty(domainEventDTOList)) {
                return;
            }
            //遍历、并行发送，不批量发送的原因，是因为日志过大时，日志打印不出来
            for (int i = 0; i < domainEventDTOList.size(); i++) {
                DomainEventDTO domainEventDTO = domainEventDTOList.get(i);
                //设置traceId，每个消息做区分
                domainEventDTO.setTraceId(domainEventDTO.getTraceId() + CharConstant.CHAR_JH + i);
                //获取领域的消息处理器
                List<CommonMessageProducer> commonMessageProducers = domainEventMessageProducerProvider.getDomainMessageProducer(
                        className);
                if (CollectionUtils.isEmpty(commonMessageProducers)) {
                    //领域没有任何后处理，丢弃
                    return;
                }
                commonMessageProducers.forEach(
                        commonMessageProducer -> commonMessageProducer.sendCustomMessage(domainEventDTO));
            }
        });

    }

    /**
     * 按照类型进行区分
     *
     * @return
     */
    private Map<String, List<DomainEventDTO<?>>> getDomainMap() {
        try {
            Map<String, List<?>> domainBeforeMap = new HashMap<>();
            Map<String, List<?>> domainAfterMap = new HashMap<>();
            Map<String, Map<String, Object>> domainBeforeIdentifyMap = new HashMap<>();
            //入口信息
            OperateEnterDTO enterInfo = OperateEnterContext.getOperateEnterInfo();
            Map<String, List<DomainEventDTO<?>>> domainEventVOMap = new HashMap<>();
            //取要变更的
            Map<String, Object> allDomainAfterExecute = OperateEnterContext.getAllDomainAfterExecute();
            Map<String, Class<?>> domainClassMap = new HashMap<>();
            Map<String, Object> allDomainAfterQuery = OperateEnterContext.getAllDomainAfterQuery();
            if (MapUtils.isEmpty(allDomainAfterExecute)) {
                return domainEventVOMap;
            }
            //变更后的
            allDomainAfterExecute.forEach((identify, obj) -> {
                Class<?> domainClass = obj.getClass();
                domainClassMap.put(domainClass.getName(), domainClass);
                Object before = allDomainAfterQuery.get(identify);
                //取之前的
                if (before != null) {
                    List domainBeforeList = domainBeforeMap.computeIfAbsent(domainClass.getName(),
                            k -> new ArrayList<>());
                    domainBeforeList.add(before);
                    domainBeforeIdentifyMap.computeIfAbsent(domainClass.getName(), k -> new HashMap<>())
                            .put(String.valueOf(identify), before);
                }
                List domainAfterList = domainAfterMap.computeIfAbsent(domainClass.getName(), k -> new ArrayList<>());
                domainAfterList.add(obj);
            });
            //遍历，组装
            if (MapUtils.isEmpty(domainAfterMap)) {
                return domainEventVOMap;
            }
            //排序
            Long domainEventTime = System.currentTimeMillis();
            domainAfterMap.forEach((k, v) -> {
                //获取类和排序
                Class<?> domainClass = domainClassMap.get(k);
                String simpleClassName = domainClass.getSimpleName();
                Lists.partition(v, domainEventMessagePartition).forEach(subAfterList -> {
                    //每50个对象进行切片，防止消息大小过大
                    //取之前的50个
                    List subBeforeList = subAfterList.stream()
                            //取之前的变更对象
                            .map(afterObj -> domainBeforeIdentifyMap.computeIfAbsent(k, key -> new HashMap<>())
                                    .get(String.valueOf(System.identityHashCode(afterObj))))
                            //过滤为空
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    DomainEventDTO<?> domainEventDTO = DomainEventDTO.builder()
                            .entry(
                                    DomainEventEntryDTO.builder().domainClassName(simpleClassName)
                                            .operateEntry(enterInfo.getOperateAction())
                                            .build())
                            .timestamp(domainEventTime)
                            .operator(
                                    Optional.ofNullable(enterInfo.getOperateUser()).map(SSOLogInfoDO::getLogin).orElse(
                                            CharConstant.CHAR_EMPTY))
                            .traceId(enterInfo.getOperateTraceId())
                            .before(subBeforeList)
                            .after(subAfterList)
                            .extInfo(OperateEnterContext.getExtInfo())
                            .build();
                    domainEventVOMap.computeIfAbsent(simpleClassName, key -> new ArrayList<>()).add(domainEventDTO);
                });
            });
            return domainEventVOMap;
        } catch (Exception e) {
            log.error("获取领域对象出现问题", e);
            return new HashMap<>();
        }
    }

    private void initOperateContext(ProceedingJoinPoint joinPoint, OperateEnter operateEnter) {
        String traceId = Tracer.id();
        if (traceId == null || traceId.isEmpty()) {
            traceId = UUID.randomUUID().toString();
            if (Tracer.getServerTracer() != null) {
                Tracer.getServerTracer().setTraceId(traceId);
            }
        }
        OperateEnterDTO operateEnterDTO = new OperateEnterDTO(traceId, new Date(),
                UserInfoContext.getUserInfo(), operateEnter.value());
        OperateEnterContext.bindOperateEnterInfo(operateEnterDTO);
        OperateEnterContext.bindExecuteDomainContainer(new ConcurrentHashMap<>());
        OperateEnterContext.bindQueryDomainContainer(new ConcurrentHashMap<>());
        OperateEnterContext.bindOperateExtInfo();
        OperateEnterContext.bindDomainChangeList();
        //根据切点设置timeOut
        OperateEnterContext.bindEntryTimeStamp(getTimeStamp(joinPoint));
        OperateEnterContext.bindEntryClientIp(Tracer.getContext("clientIp"));


    }

    /**
     * 获取入口的时间
     *
     * @param joinPoint
     * @return
     */
    private long getTimeStamp(ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature m = (MethodSignature) joinPoint.getSignature();
            Long timeStamp = null;
            if (m != null && m.getReturnType() != null && (Class<?>) m.getReturnType() == ConsumeStatus.class) {
                //是消息的切点
                Object messageStr = Arrays.stream(joinPoint.getArgs()).findFirst().get();
                timeStamp = handleMessageTimeStamp(messageStr);
            }
            if (timeStamp == null) {
                //非消息的，使用tracer的信息设置
                timeStamp = Optional.ofNullable(Tracer.getServerSpan()).map(Span::getStart)
                        .orElse(System.currentTimeMillis());
            }
            return timeStamp;
        } catch (Exception e) {
            log.error("切面获取时间异常", e);
            return System.currentTimeMillis();
        }

    }

    /**
     * 获取消息体里面的时间戳
     *
     * @param messageStr
     * @return
     */
    private Long handleMessageTimeStamp(Object messageStr) {
        try {
            return JacksonUtils.getAsObject(String.valueOf(messageStr), MESSAGE_TIME_STAMP_KEY, Long.class);
        } catch (Exception e) {
            log.warn("获取失败", e);
            return null;
        }
    }


    /**
     * 领域模型的生成者获取
     */
    @Slf4j
    @Component
    public static class DomainEventMessageProducerProvider {

        @Resource
        private LionConfigRepository lionConfigRepository;

        @MessageProducer(topic = MessageTopicEnum.WALLEMONITOR_RISK_DOMAIN_EVENT_MESSAGE)
        private CommonMessageProducer<DomainEventDTO> domainMessageProducer;


        @MessageProducer(topic = MessageTopicEnum.WALLEMONITOR_RISK_DOMAIN_EVENT_ASYNC_MESSAGE)
        private CommonMessageProducer<DomainEventDTO> asyncDomainMessageProduce;


        @MessageProducer(topic = MessageTopicEnum.RISK_CHECKING_QUEUE_ITEM_DOMAIN_EVENT_MESSAGE)
        private CommonMessageProducer<DomainEventDTO> riskCheckQueueItemDomainMessageProducer;


        private static final Map<String, CommonMessageProducer> domainAsyncMessageProducerMap = new ConcurrentHashMap<>();

        @PostConstruct
        public void init() {
            domainAsyncMessageProducerMap.put(RiskCheckingQueueItemDO.class.getSimpleName(),
                    riskCheckQueueItemDomainMessageProducer);
        }

        /**
         * 获取领域消息的发送
         *
         * @param domainClassName
         * @return
         */
        public List<CommonMessageProducer> getDomainMessageProducer(String domainClassName) {
            DomainEventConfig domainEventConfig = lionConfigRepository.getDomainEventConfig();
            if (Objects.isNull(domainEventConfig)) {
                //如果没有配置，不做返回
                return Lists.newArrayList();
            }
            Boolean hasAsync = domainEventConfig.isAsyncDomain(domainClassName);
            Boolean hasSync = domainEventConfig.isSyncDomain(domainClassName);
            List<CommonMessageProducer> result = new ArrayList<>();
            if (hasAsync) {
                //如果是异步的，使用默认的异步处理或者专有的异步
                result.add(domainAsyncMessageProducerMap.getOrDefault(domainClassName, asyncDomainMessageProduce));
            }
            if (hasSync) {
                //如果是同步的
                result.add(domainMessageProducer);
            }
            //同一个类，可能既有同步的，也有异步的
            return result;

        }
    }


}
