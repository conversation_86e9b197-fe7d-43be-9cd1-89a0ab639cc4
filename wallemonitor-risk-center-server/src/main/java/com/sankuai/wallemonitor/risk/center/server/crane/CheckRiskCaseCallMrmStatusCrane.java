package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

@Slf4j
@CraneConfiguration
public class CheckRiskCaseCallMrmStatusCrane {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCaseVehicleRelationRepository relationRepository;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_CALL_MRM_QUERY_TIME_RANGE, value = "", defaultValue = "180", allowBlankValue = true)
    private Integer riskCaseCallMrmQueryTimeRange;

    @ZebraForceMaster
    @Crane(CraneConstant.CHECK_RISK_EVENT_CALL_MRM_CRANE)
    @OperateEnter(OperateEnterActionEnum.RISK_CASE_CALL_MRM_STATUS_CRANE)
    public void run() throws Exception {
        log.info("CheckRiskCaseCallMrmStatusCrane running");
        try {
            // 检查 已呼叫云控且未关闭的 风险事件（3小时内 -- 保持和风险事件呼叫云控一致）
            // 1 查询n分钟内的未完成且已呼叫云控的风险事件
            RiskCaseDOQueryParamDTO riskCaseDOQueryParamDTO = RiskCaseDOQueryParamDTO.builder()
                    .statusList(RiskCaseStatusEnum.getUnTerminal())
                    //只检查呼叫中且未取消的
                    .mrmCalledList(Arrays.asList(RiskCaseMrmCalledStatusEnum.CALLING.getCode()))
                    .createTimeCreateTo(
                            DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, riskCaseCallMrmQueryTimeRange))
                    .build();
            List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(riskCaseDOQueryParamDTO);
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                log.info("CheckRiskCaseCallMrmStatusCrane, riskCaseDOList is empty");
                return;
            }
            // 2 检查数据标注，对应的风险事件在呼叫过程中是否有工单状态变更
            List<String> caseIdList = riskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
            CaseMarkInfoDOQueryParamDTO param = CaseMarkInfoDOQueryParamDTO.builder().caseIdList(caseIdList).build();
            Map<String, CaseMarkInfoDO> caseMarkInfoDOMap = caseMarkInfoRepository.queryMapByParam(param);
            if (MapUtils.isEmpty(caseMarkInfoDOMap)) {
                log.info("CheckRiskCaseCallMrmStatusCrane, caseMarkInfoDOMap is empty");
                return;
            }
            // 3 查询风险事件关联的车辆
            List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = relationRepository.queryByParam(
                    RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIdList).build());
            Map<String, String> caseIdToVinMap = riskCaseVehicleRelationDOList.stream()
                    .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, RiskCaseVehicleRelationDO::getVin));
            // 4 查询风险事件解除策略配置
            Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> releaseMrmStrategyConfigDTOMap =
                    lionConfigRepository.getReleaseMrmStrategyConfigDTO();

            for (RiskCaseDO riskCaseDO : riskCaseDOList) {
                String vin = caseIdToVinMap.get(riskCaseDO.getCaseId());
                try {
                    lockUtils.batchLockNoWait(
                            //对eventId和车进行加锁
                            LockKeyPreUtil.buildEventIdAndVin(Collections.singleton(riskCaseDO.getEventId()),
                                    Collections.singleton(vin)),
                            () -> {
                                RiskCaseDO thisRiskCaseDo = riskCaseRepository.getByCaseId(riskCaseDO.getCaseId());
                                if (Objects.isNull(thisRiskCaseDo)
                                        //如果已经取消
                                        || Objects.equals(thisRiskCaseDo.getMrmCalled(),
                                        RiskCaseMrmCalledStatusEnum.CANCEL)
                                        //如果已经完结
                                        || RiskCaseStatusEnum.isTerminal(thisRiskCaseDo.getStatus())) {
                                    return;
                                }
                                handleRiskCase(vin, riskCaseDO, caseMarkInfoDOMap, releaseMrmStrategyConfigDTOMap);
                            });
                } catch (Exception e) {
                    log.error("CheckRiskCaseCallMrmStatusCrane error", e);
                }
            }

        } catch (Exception e) {
            log.error("CheckRiskCaseCallMrmStatusCrane error", e);
        }
    }

    /**
     * 处理风险事件
     *
     * @param riskCaseDO
     * @param caseMarkInfoDOMap
     */
    private void handleRiskCase(String vin, RiskCaseDO riskCaseDO, Map<String, CaseMarkInfoDO> caseMarkInfoDOMap,
            Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> releaseMrmStrategyConfigDTOMap) {

        // 查询标注结果
        CaseMarkInfoDO caseMarkInfoDO = caseMarkInfoDOMap.get(riskCaseDO.getCaseId());
        if (Objects.isNull(caseMarkInfoDO) || Objects.isNull(caseMarkInfoDO.getImproperStrandingReason())) {
            log.info("CheckRiskCaseCallMrmStatusCrane, caseMarkInfoDO is null, caseId:{}",
                    riskCaseDO.getCaseId());
            return;
        }

        // 判断标注结果是否包含工单状态变更
        ImproperStrandingReason improperStrandingReason = caseMarkInfoDO.getImproperStrandingReason();
        boolean needCancel = Stream.of(
                        improperStrandingReason.getWithAccidentOrder(),
                        improperStrandingReason.getWithManualParking(),
                        improperStrandingReason.getWithReOrder(),
                        improperStrandingReason.getWithRescueOrder())
                .anyMatch(Boolean.TRUE::equals);

        if (needCancel) {
            // 更新状态
            riskCaseOperateService.cancelCallMrm(vin, riskCaseDO, releaseMrmStrategyConfigDTOMap);
        }
    }


}
