package com.sankuai.wallemonitor.risk.center.server.aop;

import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import com.sankuai.wallemonitor.risk.center.server.aop.filter.CommonAroundAspect;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(11)
@Component
@Slf4j
@Aspect
public class HttpUtilsAspect extends CommonAroundAspect {
    @Pointcut(
        "execution(static * com.sankuai.walleeve.utils.HttpUtils.post*(..)) || "
                + "execution(static * com.sankuai.walleeve.utils.HttpUtils.get*(..))"
    )
    public void accessLog() {
        // skip
    }

    @Around("accessLog()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        // 取参数
        return super.handleAroundLogWithOutRequestAndReturnResult(joinPoint, null);
    }

    @Override
    public String getLogField() {
        return LogCenterFieldEnum.AL_PROVIDER.getFieldCode();
    }

    @Override
    protected String getSignature(JoinPoint joinPoint) {
        try {
            if (!(joinPoint.getSignature() instanceof MethodSignature)) {
                return "HTTP|unknown";
            }

            MethodSignature signature = (MethodSignature)joinPoint.getSignature();
            String[] paramNames = signature.getParameterNames();
            Object[] args = joinPoint.getArgs();

            if (paramNames == null || args == null || paramNames.length != args.length) {
                return "HTTP|unknown";
            }

            // 构建参数Map
            Map<String, String> paramMap = new HashMap<>();
            for (int i = 0; i < paramNames.length; i++) {
                if (args[i] instanceof String && StringUtils.isNotBlank((String)args[i])) {
                    paramMap.put(paramNames[i], (String)args[i]);
                }
            }

            // 优先使用url
            if (paramMap.containsKey("url")) {
                return "HTTP|" + paramMap.get("url");
            }

            // 其次使用domain + path
            String domain = paramMap.get("domain");
            if (StringUtils.isNotBlank(domain)) {
                String path = paramMap.get("path");
                return "HTTP|" + domain + (StringUtils.isNotBlank(path) ? path : "UNKNOWN_PATH");
            }
        } catch (Exception e) {
            log.error("Error getting HTTP signature", e);
        }
        return "HTTP|unknown";
    }
}