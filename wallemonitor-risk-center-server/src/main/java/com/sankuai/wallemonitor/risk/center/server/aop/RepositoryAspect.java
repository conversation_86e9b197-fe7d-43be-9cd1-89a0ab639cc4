package com.sankuai.wallemonitor.risk.center.server.aop;

import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.OperateEnterContext;
import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.TransactionUtils;
import com.sankuai.wallemonitor.risk.center.server.aop.dto.DomainObjectAfterExecuteDTO;
import com.sankuai.wallemonitor.risk.center.server.aop.filter.CommonAroundAspect;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 仓储层切面
 *
 * <AUTHOR> liaolingfei
 * @Description : 操作入口切面
 * @Date 2023/6/9 16:43
 **/
@Order(3)
@Component
@Slf4j
@Aspect
public class RepositoryAspect extends CommonAroundAspect<RepositoryExecute> {


    @Resource
    private TransactionUtils transactionUtils;

    /**
     * 打印的字段
     *
     * @return
     */
    @Override
    public String getLogField() {
        return LogCenterFieldEnum.AL_REPOSITORY.getFieldCode();
    }

    /**
     * 获取结果
     *
     * @param joinPoint
     * @return
     */
    @Override
    protected Object produceResult(ProceedingJoinPoint joinPoint, RepositoryExecute repositoryExecute)
            throws Throwable {
        if (repositoryExecute != null && repositoryExecute.transaction()) {
            //执行事务
            return handleTransaction(joinPoint);
        } else {
            //支持非事务类型的，发送领域变更消息
            return handleNormalResult(joinPoint);
        }
    }


    @Around(value = "@annotation(repositoryExecute)")
    public Object doExecuteAround(ProceedingJoinPoint joinPoint, RepositoryExecute repositoryExecute) throws Throwable {
        return super.handleAroundLogAndReturnResult(joinPoint, repositoryExecute);
    }

    /**
     * 非事务类型的仓储提交
     *
     * @param joinPoint
     * @return
     */
    private Object handleNormalResult(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        //根据切面获取领域对象 fix me: 默认是第一个满足要求的对象
        //执行，只要没报错就会进入这里
        Object result = joinPoint.proceed();
        DomainObjectAfterExecuteDTO executeResultDTO = getDomainObjectsAfter(args);
        OperateEnterContext.putDomainAfterExecute(executeResultDTO.getDomainIdentityHashCode2Instance());
        OperateEnterContext.addDomainSort(executeResultDTO.getDomainClass(),
                executeResultDTO.getDomainIdentityHashCodeList());
        return result;

    }

    /**
     * 事务类型的仓储提交
     *
     * @param joinPoint
     * @return
     */
    private Object handleTransaction(ProceedingJoinPoint joinPoint) {
        return transactionUtils.execute(() -> {
            try {
                //执行结果
                Object executeResult = joinPoint.proceed();
                Object[] args = joinPoint.getArgs();
                //根据切面获取领域对象 fix me: 默认是第一个满足要求的对象
                DomainObjectAfterExecuteDTO executeResultDTO = getDomainObjectsAfter(args);
                //注册事务后的处理器
                TransactionUtils.doAfterCommit(() -> {
                    OperateEnterContext.putDomainAfterExecute(executeResultDTO.getDomainIdentityHashCode2Instance());
                    OperateEnterContext.addDomainSort(executeResultDTO.getDomainClass(),
                            executeResultDTO.getDomainIdentityHashCodeList());
                });
                return executeResult;
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 从入参里面拿到对象，只支持单个或者list
     *
     * @param args
     * @return
     */
    private DomainObjectAfterExecuteDTO getDomainObjectsAfter(Object[] args) {
        DomainObjectAfterExecuteDTO resultDTO = DomainObjectAfterExecuteDTO.builder().build();
        Map<String, Object> executeDomainObject = new HashMap<>();
        List<Integer> identityList = new ArrayList<>();
        Class<?> domainClass = getDomainClassBeforeExecute(args);
        if (args == null || args.length < 1 || domainClass == null) {
            return resultDTO;
        }
        for (Object arg : args) {
            if (arg instanceof Collection<?> && CollectionUtils.isNotEmpty(
                    (Collection<?>) arg)) {
                //取集合里面的元素类型
                Class<?> resultClass = ((Collection<?>) arg).stream().findFirst().get().getClass();
                if (!domainClass.isAssignableFrom(resultClass)) {
                    return resultDTO;
                }
                //否则就生成map
                ((Collection<?>) arg).forEach(domainObj -> {
                    executeDomainObject.put(String.valueOf(System.identityHashCode(domainObj)),
                            domainObj);
                    identityList.add(System.identityHashCode(domainObj));
                });
                break;
            } else if (domainClass.isAssignableFrom(arg.getClass())) {
                //如果是单个对象
                executeDomainObject.put(String.valueOf(System.identityHashCode(arg)),
                        arg);
                identityList.add(System.identityHashCode(arg));
                break;
            }
        }
        resultDTO.setDomainIdentityHashCodeList(identityList);
        resultDTO.setDomainIdentityHashCode2Instance(executeDomainObject);
        resultDTO.setDomainClass(domainClass.getName());
        return resultDTO;


    }

    /**
     * 获取domainclass的类型
     *
     * @param args
     * @return
     */
    private Class<?> getDomainClassBeforeExecute(Object[] args) {
        if (args == null || args.length < 1) {
            return null;
        }
        //取参数里面的第一个
        return ReflectUtils.getDomainClass(args[0]);
    }


}
