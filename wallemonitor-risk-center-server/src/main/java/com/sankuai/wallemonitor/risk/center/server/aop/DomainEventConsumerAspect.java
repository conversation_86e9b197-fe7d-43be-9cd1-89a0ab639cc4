package com.sankuai.wallemonitor.risk.center.server.aop;

import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import com.sankuai.wallemonitor.risk.center.server.aop.filter.CommonAroundAspect;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * DomainEventConsumer 消费切面
 */
@Order(11)
@Component
@Slf4j
@Aspect
public class DomainEventConsumerAspect extends CommonAroundAspect {

    @Pointcut(
        "execution(public * com.sankuai.wallemonitor.risk.center.server.consumer.DomainEventConsumer.receive*(..)) || "
                + "execution(public * com.sankuai.wallemonitor.risk.center.server.consumer.DomainEventConsumer.receiveAsync*(..))"
    )
    public void accessLog() {
        // skip
    }

    @Override
    public String getLogField() {
        return LogCenterFieldEnum.AL_PROVIDER.getFieldCode();
    }

    @Around("accessLog()")
    public Object doConsumerAround(ProceedingJoinPoint joinPoint) throws Throwable {
        // 取参数
        return super.handleAroundLogWithOutRequestAndReturnResult(joinPoint, null);
    }
}