package com.sankuai.wallemonitor.risk.center.server.crane;


import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseQueryService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.HandleStrategy;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.ReTicketAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleStatusAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmFilterDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCheckQueueQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;


@Slf4j
@CraneConfiguration
@Component
public class SecurityCallCloudControlCrane {
    @Resource
    private RiskCaseQueryService riskCaseQueryService;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private VehicleInfoRepository vehicleInfoRepository;

    @Resource
    private Map<String, HandleStrategy> riskHandleStrategyMap;

    @Resource
    private VehicleStatusAdapter vehicleStatusAdapter;

    @Resource
    private ReTicketAdapter reTicketAdaptor;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_CALL_SECURITY_SYSTEM_TIME_RANGE, value = "", defaultValue = "180", allowBlankValue = true)
    private Integer riskCaseCallSecuritySystemQueryTimeRange;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Crane(CraneConstant.SECURITY_SYSTEM_CALL_CRANE)
    @OperateEnter(OperateEnterActionEnum.CALL_SECURITY_SYSTEM_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("callSecuritySystemCloudControlCrane running");
        try {
            // 1 查询n分钟内的未完成且未呼叫云控的风险事件
            List<RiskCaseDO> riskCaseDOList = riskCaseQueryService.queryRiskCaseDispose(
                    DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, riskCaseCallSecuritySystemQueryTimeRange),
                    new Date());
            log.info("ReportRiskCaseCrane# queryRiskCaseDispose，riskCaseDOList = {}", riskCaseDOList);
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                log.info("ReportRiskCaseCrane end");
                return;
            }

            // 2 获取过滤策略的lion配置
            Map<Pair<Integer, Integer>, LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO> callSecurityStrategyConfig = lionConfigRepository.getCallSecurityStrategyConfig();
            log.info("ReportRiskCaseCrane# callSecurityStrategyConfig = {}", callSecurityStrategyConfig);
            if (MapUtils.isEmpty(callSecurityStrategyConfig)) {
                log.error("callSecurityStrategyMap is empty", new IllegalArgumentException("未获取到过滤策略配置"));
                return;
            }

            // 3 按照 source/type （场景）进行第一层过滤
            List<RiskCaseDO> unFilterRiskCaseDOList = riskCaseDOList.stream()
                    .filter(riskCaseDO -> {
                        Pair<Integer, Integer> riskCaseTypePair = new Pair<>(riskCaseDO.getSource().getCode(),
                                riskCaseDO.getType().getCode());
                        return callSecurityStrategyConfig.containsKey(riskCaseTypePair) && Objects.nonNull(
                                callSecurityStrategyConfig.get(riskCaseTypePair));
                    })
                    .collect(Collectors.toList());
            log.info("ReportRiskCaseCrane# unFilterRiskCaseDOList = {}", unFilterRiskCaseDOList);
            if (CollectionUtils.isEmpty(unFilterRiskCaseDOList)) {
                return;
            }

            // 4 获取所有case对应的vin
            List<String> caseList = riskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());

            Map<String, RiskCaseDO> case2RiskMap = riskCaseDOList.stream()
                    .collect(Collectors.toMap(RiskCaseDO::getCaseId, Function.identity(), (oldV, newV) -> newV));

            Map<String, List<String>> vin2CaseMap = riskCaseVehicleRelationRepository.queryByParam(
                            RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseList).build()).stream()
                    .collect(Collectors.groupingBy(RiskCaseVehicleRelationDO::getVin, Collectors.mapping(RiskCaseVehicleRelationDO::getCaseId, Collectors.toList())));

            List<Pair<String, List<RiskCaseDO>>> vinRiskCasePairList = vin2CaseMap.entrySet().stream().map(entry -> {
                String vin = entry.getKey();
                List<RiskCaseDO> riskCase = entry.getValue().stream().map(case2RiskMap::get).collect(Collectors.toList());
                if (vin == null || CollectionUtils.isEmpty(riskCase)) {
                    return null;
                }
                return new Pair<>(vin, riskCase);
            }).filter(Objects::nonNull).collect(Collectors.toList()); // vin , List<RiskCaseDO>


            ParallelExecutor.executeParallelTasks("mrm_filter", Lists.partition(vinRiskCasePairList, 10) ,
                    (subList) -> handleRiskCaseList(subList, callSecurityStrategyConfig));


        } catch (Exception e) {
            log.error("ReportRiskCaseCrane error", e);
        }
    }

    /**
     * 将风险案例列表转换为可过滤的DTO列表和VIN到风险案例的映射
     *
     * @param vinRiskCaseDOList 风险案例列表
     * @return Pair对象，包含可过滤的DTO列表和VIN到风险案例的映射
     */
    private Pair<List<RiskCaseCallMrmFilterDTO>, Map<String, Pair<String, RiskCaseDO>>> convert2CallMrmRiskCaseDTOListAndMap(
            List<Pair<String, List<RiskCaseDO>>> vinRiskCaseDOList) {
        if (CollectionUtils.isEmpty(vinRiskCaseDOList)) {
            return null;
        }
        // ListPair -> Map
        Map<String, Pair<String, RiskCaseDO>> case2VinRiskCaseMap = new HashMap<>(); // caseId, Pair<Vin, RiskCase>

        vinRiskCaseDOList.forEach(vinRiskCaseDO -> {
            vinRiskCaseDO.getValue().forEach(riskCaseDO -> {
                case2VinRiskCaseMap.put(riskCaseDO.getCaseId(), new Pair<>(vinRiskCaseDO.getKey(), riskCaseDO));
            });
        });


        // 构建vin列表
        List<String> vinList = vinRiskCaseDOList.stream().map(Pair::getKey)
                .collect(Collectors.toList());

        // 构建case列表
        List<String> caseIdList = new ArrayList<>(case2VinRiskCaseMap.keySet());
        // 构建车辆的实时信息快照
        List<RiskCaseCallMrmFilterDTO> riskCaseCallMrmFilterDTOS = buildRiskCaseCallMrmFilterDTOList(vinList,
                case2VinRiskCaseMap, caseIdList);
        // 组装信息并返回
        return new Pair<>(riskCaseCallMrmFilterDTOS, case2VinRiskCaseMap);
    }

    /**
     * 构建风险事件与云控过滤DTO列表的映射关系
     *
     * @param vinList
     * @param case2VinRiskCaseMap
     * @param caseIdList
     * @return
     */
    public List<RiskCaseCallMrmFilterDTO> buildRiskCaseCallMrmFilterDTOList(List<String> vinList, Map<String, Pair<String, RiskCaseDO>> case2VinRiskCaseMap, List<String> caseIdList) {
        // 1 计算车辆实时状态快照
        Map<String, VehicleInfoDO> vehicleInfoDOList = vehicleInfoRepository.queryByVinList(vinList).stream()
                .collect(Collectors.toMap(VehicleInfoDO::getVin, Function.identity(), (o1, o2) -> o1));

        if (CollectionUtils.isEmpty(vehicleInfoDOList)) {
            log.error("风险统计任务查询车辆状态实时快照结果为空",
                    new RuntimeException("风险统计任务查询车辆状态实时快照结果为空"));
            return null;
        }
        log.info("风险统计任务查询车辆状态实时快照结果：{}", vehicleInfoDOList);
        // 2 查询坐席连接状态
        Map<String, Integer> vin2MrmStatusMap = vehicleStatusAdapter.getVin2MrmStatusMap(vinList);
        log.info("风险统计任务查询坐席连接状态结果：{}", vin2MrmStatusMap);
        // 4 批量查询停滞原因
        CaseMarkInfoDOQueryParamDTO param = CaseMarkInfoDOQueryParamDTO.builder().caseIdList(caseIdList).build();
        Map<String, CaseMarkInfoDO> caseIdToMarkInfoDOMap = caseMarkInfoRepository.queryMapByParam(param);
        log.info("风险统计任务查询预检标注结果：{}", caseIdToMarkInfoDOMap);

        // 5 查询预检标注结果
        RiskCheckQueueQueryParam checkQueueQueryParam = RiskCheckQueueQueryParam.builder()
                .tmpCaseIdList(caseIdList)
                .statusList(Arrays.asList(RiskQueueStatusEnum.CONFIRMED_RISK.getCode(),
                        RiskQueueStatusEnum.CONFIRMED_TIMEOUT.getCode())).build();
        List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList = riskCheckQueueRepository.queryByParam(
                checkQueueQueryParam);
        // 6 查询车辆实时信息
        Map<String, VehicleRuntimeInfoContextDO> runtimeInfoContextMap = vehicleRuntimeInfoContextRepository
                .getFullByVin(vinList).stream()
                .collect(Collectors.toMap(VehicleRuntimeInfoContextDO::getVin, Function.identity(), (v1, v2) -> v1));
        Map<String, RiskCheckingQueueItemDO> vin2itemMap = new HashMap<>();
        for (RiskCheckingQueueItemDO queueItemDO : riskCheckingQueueItemDOList) {
            if (queueItemDO.getCheckResult() == null || queueItemDO.getCheckResult().getCategory() == null) {
                continue;
            }
            vin2itemMap.put(queueItemDO.getVin(), queueItemDO);
        }
        return case2VinRiskCaseMap.values().stream().map(stringRiskCaseDOPair -> {
            String vin = stringRiskCaseDOPair.getKey();
            RiskCaseDO riskCaseDO = stringRiskCaseDOPair.getValue();
            VehicleInfoDO vehicleInfoDO = vehicleInfoDOList.get(vin);
            if (vehicleInfoDO == null) {
                log.error("vin = {}, 查找总线信息错误", vin);
                return null;
            }
            if (riskCaseDO == null) {
                log.error("vin = {}, 未找到riskCase", vin);
                return null;
            }
            CaseMarkInfoDO caseMarkInfoDO = Optional.ofNullable(riskCaseDO)
                    .map(RiskCaseDO::getCaseId).filter(caseIdToMarkInfoDOMap::containsKey)
                    .map(caseIdToMarkInfoDOMap::get).orElse(null);
            RiskCheckingQueueItemDO itemDO = vin2itemMap.get(vin);
            // 取子类型
            String subCategory = Optional.ofNullable(vin2itemMap.get(vin))
                    .map(RiskCheckingQueueItemDO::getCheckResult).map(RiskCheckResultDO::getCategory)
                    .map(ISCheckCategoryEnum::getSubcategory).orElse(CharConstant.CHAR_EMPTY);
            // 取停滞原因
            ImproperStrandingReason improperStrandingReason = caseMarkInfoDO != null
                    ? caseMarkInfoDO.getImproperStrandingReason() : null;
            return RiskCaseCallMrmFilterDTO.builder()
                    // 车辆
                    .vin(vehicleInfoDO.getVin())
                    // vhr
                    .vhr(Optional.ofNullable(vehicleInfoDO.getVhr()).map(VHRModeEnum::getCode).orElse(null))
                    // 是否故障
                    .hasAccident(Optional.ofNullable(vehicleInfoDO.getWithAccidentOrder()).orElse(false))
                    // 是否RE
                    .hasReOrder(Optional.ofNullable(vehicleInfoDO.getWithReOrder()).orElse(false))
                    // 坐席状态
                    .mrmStatus(vin2MrmStatusMap.getOrDefault(vehicleInfoDO.getVin(), null))
                    // 是否救援
                    .hasRescueOrder(Optional.ofNullable(vehicleInfoDO.getWithRescueOrder()).orElse(false))
                    // 风险时长
                    .riskDuration(DatetimeUtil.getTimeDiff(riskCaseDO.getOccurTime(),
                            new Date(), ChronoUnit.SECONDS))
                    // 约车目的
                    .purpose(vehicleInfoDO.getPurpose())
                    // 停滞原因
                    .improperStrandingReason(improperStrandingReason)
                    // 风险类型
                    .firstSubCategory(subCategory)
                    // 场地
                    .placeCode(vehicleInfoDO.getPlaceCode())
                    // 业务标签
                    .businessType(vehicleInfoDO.getBusinessType())
                    // 召回时刻item
                    .item(itemDO)
                    // caseId
                    .caseId(riskCaseDO.getCaseId())
                    // 车辆上下文
                    .vehicleRuntimeInfo(
                            Optional.ofNullable(itemDO)
                                    .map(RiskCheckingQueueItemDO::getVin)
                                    .map(runtimeInfoContextMap::get)
                                    .orElse(null)
                    ).build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 处理风险事件列表，根据不同的分类执行处置策略
     *
     * @param vinRiskPariList
     * @param callSecurityStrategyConfig
     */
    private void handleRiskCaseList(List<Pair<String, List<RiskCaseDO>>> vinRiskPariList,
            Map<Pair<Integer, Integer>, LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO> callSecurityStrategyConfig) {
        // 4 构建车辆状态实时快照
        Pair<List<RiskCaseCallMrmFilterDTO>, Map<String, Pair<String, RiskCaseDO>>> pairTemp = convert2CallMrmRiskCaseDTOListAndMap(
                vinRiskPariList);
        log.info("ReportRiskCaseCrane# pairTemp：{}", pairTemp);
        if (Objects.isNull(pairTemp)) {
            log.error("convert2CallMrmRiskCaseDTOListAndMap result is null",
                    new IllegalArgumentException("构建车辆实时快照失败"));
            return;
        }

        // 5 遍历每个风险时间的快照，根据不同的分类执行处置策略
        Map<String, RiskCaseCallMrmFilterDTO> riskCaseCallMrmFilterDTOMap = pairTemp.getKey().stream().collect(Collectors.toMap(x -> x.getCaseId(), Function.identity(), (o1, o2) -> o1));
        Map<String, Pair<String, RiskCaseDO>> case2VinRiskMap = pairTemp.getValue();

        List<String> caseIdSet = new ArrayList<>(case2VinRiskMap.keySet());
        List<String> vinSet = case2VinRiskMap.values().stream().map(Pair::getKey).collect(Collectors.toList());

        lockUtils.batchLockCanWait(LockKeyPreUtil.buildEventIdAndVin(Sets.newHashSet(caseIdSet), Sets.newHashSet(vinSet)), 1, TimeUnit.SECONDS, () -> {
            List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(RiskCaseDOQueryParamDTO.builder().caseIdList(caseIdSet).build());
            riskCaseDOList.forEach(thisRiskCaseDo -> {
                if (Objects.isNull(thisRiskCaseDo)
                        //如果已经呼叫
                        || thisRiskCaseDo.getMrmCalled().isMrmCalled()
                        //如果已经完结
                        || RiskCaseStatusEnum.isTerminal(thisRiskCaseDo.getStatus())
                        || Objects.isNull(thisRiskCaseDo.getSource())
                        || Objects.isNull(thisRiskCaseDo.getType())) {
                    log.warn(
                            String.format(
                                    "riskCaseDO source or type is null or finished or called, riskCaseDO = %s",
                                    thisRiskCaseDo),
                            new IllegalArgumentException());
                    return;
                }
                // 匹配处置策略
                RiskCaseCategoryEnum riskCaseCategoryEnum = RiskCaseCategoryEnum.findByValue(
                        thisRiskCaseDo.getSource().getCode(),
                        thisRiskCaseDo.getType().getCode());
                if (Objects.isNull(riskCaseCategoryEnum)) {
                    log.error("riskCaseCategoryEnum is null",
                            new IllegalArgumentException("风险事件呼叫云控处置策略未配置"));
                    return;
                }
                HandleStrategy handleStrategy = riskHandleStrategyMap.getOrDefault(
                        riskCaseCategoryEnum.getBeanName(), null);
                if (Objects.isNull(handleStrategy)) {
                    log.error("handleStrategy is null",
                            new IllegalArgumentException("风险事件呼叫云控处置策略类为空"));
                    return;
                }
                // 处理风险事件
                RiskCaseCallMrmFilterDTO filterDTO = riskCaseCallMrmFilterDTOMap.get(thisRiskCaseDo.getCaseId());
                Pair<Integer, Integer> riskCaseTypePair = new Pair<>(
                        thisRiskCaseDo.getSource().getCode(),
                        thisRiskCaseDo.getType().getCode());
                handleStrategy.process(callSecurityStrategyConfig.get(riskCaseTypePair), filterDTO,
                        thisRiskCaseDo);
            });
        });
    }
}
