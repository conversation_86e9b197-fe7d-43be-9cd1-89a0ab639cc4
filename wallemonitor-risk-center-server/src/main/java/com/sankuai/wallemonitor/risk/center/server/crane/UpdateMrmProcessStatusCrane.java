package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.EventPlatSearchAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.MrmConnectionInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.UpdateMrmProcessStatusCraneConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@CraneConfiguration
public class UpdateMrmProcessStatusCrane {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private EventPlatSearchAdapter eventPlatSearchAdapter;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_CALL_MRM_QUERY_TIME_RANGE, value = "", defaultValue = "180", allowBlankValue = true)
    private Integer riskCaseCallMrmQueryTimeRange;

    @ConfigValue(key = LionKeyConstant.LION_KEY_UPDATE_MRM_PROCESS_STATUS_CRANE_CONFIG, value = "", defaultValue = "{\"sourceList\":[3,5],\"typeList\":[1,9]}", allowBlankValue = true)
    private UpdateMrmProcessStatusCraneConfigDTO configDTO;


    @Crane(CraneConstant.UPDATE_MRM_PROCESS_STATUS_CRANE)
    @OperateEnter(OperateEnterActionEnum.UPDATE_MRM_PROCESS_STATUS)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("UpdateMrmProcessStatusCrane running");
        try {
            CheckUtil.isNotNull(configDTO, "更新MRM处理状态定时任务配置不能为空");
            // 1 查询X分钟内未处置的来自状态监控的停滞不当风险事件
            RiskCaseDOQueryParamDTO riskCaseDOQueryParamDTO = RiskCaseDOQueryParamDTO.builder()
                    // 状态为未处置
                    .statusList(Arrays.asList(RiskCaseStatusEnum.NO_DISPOSAL.getCode()))
                    // 来源为状态监控
                    .sourceList(configDTO.getSourceList())
                    // 停滞不当
                    .caseTypeList(configDTO.getTypeList())
                    // 创建时间早于当前时间-riskCaseCallMrmQueryTimeRange 分钟
                    .createTimeCreateTo(
                            DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, riskCaseCallMrmQueryTimeRange))
                    .build();
            List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(riskCaseDOQueryParamDTO);
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                log.info("UpdateMrmProcessStatusCrane, riskCaseDOList is empty");
                return;
            }

            // 2 判断是否更新状态为处置中
            riskCaseDOList.forEach(riskCaseDO -> {
                try {
                    lockUtils.batchLockNoWait(
                            //对eventId加锁
                            LockKeyPreUtil.buildEventIdAndVin(Collections.singleton(riskCaseDO.getEventId()),
                                    new HashSet<>()),
                            () -> {
                                RiskCaseDO thisRiskCaseDO = riskCaseRepository.getByCaseId(riskCaseDO.getCaseId());
                                if (Objects.isNull(thisRiskCaseDO)
                                        // 如果状态不是未处置
                                        || !Objects.equals(RiskCaseStatusEnum.NO_DISPOSAL,
                                        thisRiskCaseDO.getStatus())) {
                                    return;
                                }
                                updateRiskCaseStatus(thisRiskCaseDO);

                            });
                } catch (Exception e) {
                    log.warn("UpdateMrmProcessStatusCrane batchLockNoWait error", e);
                }
            });
        } catch (Exception e) {
            log.error("UpdateMrmProcessStatusCrane error", e);
        }
    }

    /**
     * 更新风险case状态
     *
     * @param riskCaseDO
     */
    private void updateRiskCaseStatus(RiskCaseDO riskCaseDO) {
        // 1 查询风险case关联的车辆信息
        RiderCaseVehicleRelationDOParamDTO paramDTO = RiderCaseVehicleRelationDOParamDTO.builder()
                .caseIdList(Collections.singletonList(riskCaseDO.getCaseId()))
                .build();
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                paramDTO);
        if (CollectionUtils.isEmpty(riskCaseVehicleRelationDOList)) {
            log.error(String.format(
                    "UpdateMrmProcessStatusCrane, riskCaseVehicleRelationDO is null, caseId:%s",
                    riskCaseDO.getCaseId()), new IllegalArgumentException());
            return;
        }
        // 2 查询云控坐席状态
        // 计算真实停滞时间， 事发时间 - toActualStagnationDurationMin
        Date actualStagnationTime = riskCaseDO.getOccurTime();
        RiskCaseVehicleRelationDO vehicleRelationDO = riskCaseVehicleRelationDOList.get(0);
        MrmConnectionInfoDTO mrmConnectionInfoDTO = eventPlatSearchAdapter.getRecentMrmConnectRecord(
                vehicleRelationDO.getVin(),
                actualStagnationTime.getTime());
        if (Objects.isNull(mrmConnectionInfoDTO)) {
            return;
        }
        // 3 更新 risk_case 表中的处置状态
        riskCaseDO.updateStatus(RiskCaseStatusEnum.IN_DISPOSAL,
                mrmConnectionInfoDTO.getConnectTime().getTime());
        riskCaseRepository.save(riskCaseDO);

        // 4 更新 risk_case_vehicle_relation 表中的坐席连接时间
        vehicleRelationDO.setSeatConnectTime(mrmConnectionInfoDTO.getConnectTime());
        vehicleRelationDO.updateSeatInfo(mrmConnectionInfoDTO.getHandler(), null, null);
        riskCaseVehicleRelationRepository.save(vehicleRelationDO);
    }

}
