package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.LaneQueryRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.MapElementRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftMapElementQueryService;
import com.sankuai.wallemonitor.risk.center.api.vo.HdMapElementGeoVO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter.SearchNearbyRequestVTO;
import com.sankuai.wallemonitor.risk.center.infra.convert.HdMapElementPolygonConvert;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapElementTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftMapElementQueryServiceImpl implements IThriftMapElementQueryService {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private HdMapElementPolygonConvert hdMapElementPolygonConvert;

    @Override
    public EveThriftResponse<List<HdMapElementGeoVO>> searchMapElementByDistance(MapElementRequestDTO request) {
        if (request == null || request.getLatitude() == null || request.getLongitude() == null) {
            return EveThriftResponse.failWithMessage("参数错误").build();
        }
        List<HdMapElementGeoDO> list = hdMapAdapter.searchNearby(SearchNearbyRequestVTO.builder()
                .range(request.getDistance())
                .restrictType(HdMapElementTypeEnum.getElementByMapType(request.getMapType()))
                .hdMapEnum(HdMapEnum.fromValue(request.getMapType()))
                .area(getAreaByHdMapVersion(request.getHdMapVersion()))
                .positionDO(PositionDO
                        .getPosition(request.getLongitude(), request.getLatitude(), CoordinateSystemEnum.WGS84))
                .build());
        List<HdMapElementGeoVO> hdMapElementGeoVOS = hdMapElementPolygonConvert.toHdMapElementGeoVO(list);
        return EveThriftResponse.ok(hdMapElementGeoVOS);
    }

    @Override
    public EveThriftResponse<List<HdMapElementGeoVO>> searchLaneById(LaneQueryRequestDTO request) {
        if (request == null || CollectionUtils.isEmpty(request.getIdList())) {
            return EveThriftResponse.failWithMessage("参数错误").build();
        }
        List<HdMapElementGeoDO> list = hdMapAdapter.queryByLaneIdList(request.getIdList());
        if (CollectionUtils.isEmpty(list)) {
            return EveThriftResponse.ok(new ArrayList<>());
        }
        List<HdMapElementGeoVO> hdMapElementGeoVOS = hdMapElementPolygonConvert.toHdMapElementGeoVO(list);
        return EveThriftResponse.ok(hdMapElementGeoVOS);
    }

    /**
     * 取高精地图关联的area
     * 
     * @param hdMapVersion
     * @return
     */
    private String getAreaByHdMapVersion(String hdMapVersion) {
        if (StringUtils.isBlank(hdMapVersion)) {
            return null;
        }
        // hualikan_hdmap_v5.185.0.r 取第一个
        String[] split = hdMapVersion.split("_");
        String area = split[0];
        return area;
    }
}
