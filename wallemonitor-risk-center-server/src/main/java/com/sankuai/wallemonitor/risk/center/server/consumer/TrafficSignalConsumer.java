package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.convert.TrafficDataConvert;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficData;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficLightConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleTrafficRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TrafficSignalConsumer {

    @Resource
    private TrafficDataConvert trafficDataConvert;

    @Resource
    private VehicleTrafficRepository vehicleContextRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @MafkaConsumer(
            namespace = "waimai", topic = "mad-vehicle.real.status.traffic",
            group = "risk.center.mad-vehicle.real.status.traffic.consumer"
    )
    @OperateEnter(OperateEnterActionEnum.TRAFFIC_CONSUMER_ENTER)
    public ConsumeStatus receive(String msg) {
        try {
            if (StringUtils.isBlank(msg)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            TrafficData trafficData = JacksonUtils.from(msg, TrafficData.class);
            if (trafficData == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            TrafficLightContextDO trafficLightContextDO = trafficDataConvert.toDO(trafficData);
            if (trafficLightContextDO == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            TrafficLightConfigDTO trafficLightConfigDTO = lionConfigRepository.getTrafficLightConfig();
            //保存车辆
            vehicleContextRepository.saveVehicleTrafficContext(trafficData.getVin(), trafficLightContextDO);
            //更新至车辆上下文，因为收到消息的时候，可能是从感知或者planning拿的，所以需要更新进去，然后查出出来，查出来的是完整的
            VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = vehicleRuntimeInfoContextRepository.getFromCache(
                    trafficData.getVin());
            //更新车辆上下文的缓存至车辆上下文
            vehicleRuntimeInfoContextDO.updateFromTraffic(
                    vehicleContextRepository.getVehicleTrafficContext(trafficData.getVin()), trafficLightConfigDTO);
            vehicleRuntimeInfoContextRepository.updateCache(vehicleRuntimeInfoContextDO, trafficData.getTimestamp());
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (UnableGetLockException e) {
            log.warn("消费红绿灯关联事件消息锁冲突失败", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (ParamInputErrorException e) {
            log.warn("消费红绿灯事件消息业务正常失败", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("消费红绿灯事件消息异常失败", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }
}
