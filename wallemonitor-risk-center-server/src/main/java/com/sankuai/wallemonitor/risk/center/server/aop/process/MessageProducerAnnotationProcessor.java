package com.sankuai.wallemonitor.risk.center.server.aop.process;

import com.google.common.collect.Maps;
import com.meituan.xframe.boot.mafka.autoconfigure.producer.MafkaProducerBeanDefinitionGenerator;
import com.meituan.xframe.boot.mafka.autoconfigure.producer.MafkaProducerProperties;
import com.meituan.xframe.util.CustomAnnotationInjectAwareBeanPostProcessor;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanDefinitionHolder;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MessageProducer注解处理器
 */
public class MessageProducerAnnotationProcessor implements
        CustomAnnotationInjectAwareBeanPostProcessor.AnnotationBeanDefinitionMapper<MessageProducer> {

    /**
     * Mafka消费者beanDefinition缓存 同一个topic，创建一个
     */
    private final ConcurrentMap<MafkaProducerProperties, BeanDefinitionHolder> messageProducerBeanHolder = new ConcurrentHashMap<>();

    private final String MESSAGE_PRODUCER_PREFIX = "riskCenterMessageProducer";


    @Override
    public Map<Field, BeanDefinitionHolder> get(Map<Field, MessageProducer> messageProducerMap,
            BeanDefinitionRegistry beanDefinitionRegistry) {
        Map<Field, BeanDefinitionHolder> beanDefinitionHolderMap = Maps.newHashMap();

        messageProducerMap.forEach((field, annotation) -> {
            //
            MafkaProducerProperties mafkaProducerProperties = handleMafkaProducerProperties(annotation);
            BeanDefinitionHolder beanDefinitionHolder;
            if (messageProducerBeanHolder.containsKey(mafkaProducerProperties)) {
                // 相同MafkaProducer配置使用相同BeanDefinition
                beanDefinitionHolder = messageProducerBeanHolder.get(mafkaProducerProperties);
            } else {
                // 否则生成新的BeanDefinition
                String beanName = MESSAGE_PRODUCER_PREFIX + mafkaProducerProperties;
                BeanDefinition beanDefinition = handleBeanDefinition(field, beanName, annotation,
                        mafkaProducerProperties,
                        beanDefinitionRegistry);
                beanDefinitionHolder = new BeanDefinitionHolder(beanDefinition, beanName);
            }
            beanDefinitionHolderMap.put(field, beanDefinitionHolder);
            messageProducerBeanHolder.putIfAbsent(mafkaProducerProperties, beanDefinitionHolder);
        });
        return beanDefinitionHolderMap;
    }

    /**
     * 构建MafkaProducerProperties
     *
     * @param annotation
     * @return
     */
    private MafkaProducerProperties handleMafkaProducerProperties(MessageProducer annotation) {
        MafkaProducerProperties mafkaProducerProperties = new MafkaProducerProperties();
        //默认只有这几种，后续可以拓展
        mafkaProducerProperties.setTopic(annotation.topic().getTopic());
        mafkaProducerProperties.setNamespace(annotation.topic().getNamespace());
        mafkaProducerProperties.setAppkey(annotation.appKey());
        return mafkaProducerProperties;
    }

    /**
     * 生成一个CommonMessageProducer类的代理实例
     *
     * @param field
     * @param mafkaProducerProperties
     * @param beanDefinitionRegistry
     * @return
     */
    private BeanDefinition handleBeanDefinition(Field field, String beanName, MessageProducer messageProduce,
            MafkaProducerProperties mafkaProducerProperties,
            BeanDefinitionRegistry beanDefinitionRegistry) {
        Class<?> thisFieldClass = field.getType();
        if (!CommonMessageProducer.class.isAssignableFrom(thisFieldClass)) {
            //注解不对无法注入
            return null;
        }
        Type genericType = field.getGenericType();

        //获取消息体的类型
        Class<?> messageDTOClass = handleMessageClass(genericType);
        //生成mafka的bean
        BeanDefinition mafkaBeanDefinition = MafkaProducerBeanDefinitionGenerator.generate(beanDefinitionRegistry,
                mafkaProducerProperties);
        BeanDefinitionHolder beanDefinitionHolder = new BeanDefinitionHolder(mafkaBeanDefinition, beanName);
        //生成一个producer
        return BeanDefinitionBuilder.genericBeanDefinition(CommonMessageProducer.class,
                        () -> buildCommonMessageProducer(messageDTOClass, beanDefinitionHolder, messageProduce))
                .getBeanDefinition();

    }

    /**
     * 获取消息体的类型
     *
     * @param genericType
     * @return
     */
    private Class<?> handleMessageClass(Type genericType) {
        Class<?> actualClass = null;
        if (!(genericType instanceof ParameterizedType)) {
            return null;
        }
        ParameterizedType parameterizedType = (ParameterizedType) genericType;
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        for (Type actualType : actualTypeArguments) {
            if (actualType instanceof Class) {
                actualClass = (Class<?>) actualType;
                // 这里的actualClass就是MessgeDTO类
            }
        }
        return actualClass;
    }

    /**
     * 生成具体类型的messageProducer
     *
     * @param genericType
     * @param beanDefinitionHolder
     * @param <T>
     * @return
     */
    private <T> CommonMessageProducer buildCommonMessageProducer(Class<T> genericType,
            BeanDefinitionHolder beanDefinitionHolder,
            MessageProducer messageProducer) {
        CommonMessageProducer<T> commonMessageProducer = new CommonMessageProducer<>(messageProducer.topic());
        return commonMessageProducer;

    }

    @Slf4j
    @Configuration
    public static class MafkaProducerAutoConfiguration {

        @Bean
        public CustomAnnotationInjectAwareBeanPostProcessor<MessageProducer> riskCenterMessageProducerAnnotationInjectProcessor() {
            return new CustomAnnotationInjectAwareBeanPostProcessor<>(MessageProducer.class,
                    new MessageProducerAnnotationProcessor());
        }

    }


}
