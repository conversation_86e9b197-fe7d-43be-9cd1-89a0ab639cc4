package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Sets;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseQueryService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.HandleStrategy;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.ReTicketAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleStatusAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmFilterDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

@Slf4j
@CraneConfiguration
public class CheckRiskCaseCallMrmCrane {

    @Resource
    private RiskCaseQueryService riskCaseQueryService;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private VehicleInfoRepository vehicleInfoRepository;

    @Resource
    private Map<String, HandleStrategy> riskHandleStrategyMap;

    @Resource
    private VehicleStatusAdapter vehicleStatusAdapter;

    @Resource
    private ReTicketAdapter reTicketAdaptor;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_CALL_MRM_QUERY_TIME_RANGE, value = "", defaultValue = "180", allowBlankValue = true)
    private Integer riskCaseCallMrmQueryTimeRange;

    @Crane(CraneConstant.RISK_CASE_CALL_MRM_CRANE)
    @OperateEnter(OperateEnterActionEnum.RISK_CASE_CALL_MRM)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("ReportRiskCaseCrane running");
        try {
            // 1 查询n分钟内的未完成且未呼叫云控的风险事件
            List<RiskCaseDO> riskCaseDOList = riskCaseQueryService.queryRiskCaseDispose(
                    DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, riskCaseCallMrmQueryTimeRange),
                    new Date());
            log.info("ReportRiskCaseCrane# queryRiskCaseDispose，riskCaseDOList = {}", riskCaseDOList);
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                log.info("ReportRiskCaseCrane end");
                return;
            }

            // 2 获取过滤策略的lion配置
            Map<Pair<Integer, Integer>, CallMrmStrategyConfigDTO> callMrmStrategyMap = lionConfigRepository.getCallMrmStrategyConfigDTO();
            log.info("ReportRiskCaseCrane# callMrmStrategyMap = {}", callMrmStrategyMap);
            if (MapUtils.isEmpty(callMrmStrategyMap)) {
                log.error("callMrmStrategyMap is empty", new IllegalArgumentException("未获取到过滤策略配置"));
                return;
            }

            // 3 按照 source/type （场景）进行第一层过滤
            List<RiskCaseDO> unFilterRiskCaseDOList = riskCaseDOList.stream()
                    .filter(riskCaseDO -> {
                        Pair<Integer, Integer> riskCaseTypePair = new Pair<>(riskCaseDO.getSource().getCode(),
                                riskCaseDO.getType().getCode());
                        return callMrmStrategyMap.containsKey(riskCaseTypePair) && Objects.nonNull(
                                callMrmStrategyMap.get(riskCaseTypePair));
                    })
                    .collect(Collectors.toList());
            log.info("ReportRiskCaseCrane# unFilterRiskCaseDOList = {}", unFilterRiskCaseDOList);
            if (CollectionUtils.isEmpty(unFilterRiskCaseDOList)) {
                return;
            }

            // 4 构建车辆状态实时快照
            Pair<List<RiskCaseCallMrmFilterDTO>, Map<String, RiskCaseDO>> pairTemp = convert2CallMrmRiskCaseDTOListAndMap(
                    unFilterRiskCaseDOList);
            log.info("ReportRiskCaseCrane# pairTemp：{}", pairTemp);
            if (Objects.isNull(pairTemp)) {
                log.error("convert2CallMrmRiskCaseDTOListAndMap result is null",
                        new IllegalArgumentException("构建车辆实时快照失败"));
                return;
            }

            // 5 遍历每个风险时间的快照，根据不同的分类执行处置策略
            List<RiskCaseCallMrmFilterDTO> riskCaseCallMrmFilterDTOS = pairTemp.getKey();
            Map<String, RiskCaseDO> vin2RiskCaseDOMap = pairTemp.getValue();

            for (RiskCaseCallMrmFilterDTO filterDTO : riskCaseCallMrmFilterDTOS) {
                try {
                    RiskCaseDO riskCaseDO = vin2RiskCaseDOMap.get(filterDTO.getVin());
                    if (riskCaseDO == null) {
                        continue;
                    }
                    lockUtils.batchLockNoWait(
                            //对eventId和车进行加锁
                            LockKeyPreUtil.buildEventIdAndVin(Sets.newHashSet(riskCaseDO.getEventId()),
                                    Sets.newHashSet(filterDTO.getVin())), () -> {
                                RiskCaseDO thisRiskCaseDo = riskCaseRepository.getByCaseId(riskCaseDO.getCaseId());
                                //走到这里为异常条件
                                if (Objects.isNull(thisRiskCaseDo)
                                        //如果已经呼叫
                                        || thisRiskCaseDo.getMrmCalled().isMrmCalled()
                                        //如果已经完结
                                        || RiskCaseStatusEnum.isTerminal(thisRiskCaseDo.getStatus())
                                        || Objects.isNull(thisRiskCaseDo.getSource())
                                        || Objects.isNull(thisRiskCaseDo.getType())) {
                                    log.warn(
                                            String.format(
                                                    "riskCaseDO source or type is null or finished or called, riskCaseDO = %s",
                                                    thisRiskCaseDo),
                                            new IllegalArgumentException());
                                    return;
                                }
                                // 匹配处置策略
                                RiskCaseCategoryEnum riskCaseCategoryEnum = RiskCaseCategoryEnum.findByValue(
                                        thisRiskCaseDo.getSource().getCode(),
                                        thisRiskCaseDo.getType().getCode());
                                if (Objects.isNull(riskCaseCategoryEnum)) {
                                    log.error("riskCaseCategoryEnum is null",
                                            new IllegalArgumentException("风险事件呼叫云控处置策略未配置"));
                                    return;
                                }
                                HandleStrategy handleStrategy = riskHandleStrategyMap.getOrDefault(
                                        riskCaseCategoryEnum.getBeanName(), null);
                                if (Objects.isNull(handleStrategy)) {
                                    log.error("handleStrategy is null",
                                            new IllegalArgumentException("风险事件呼叫云控处置策略类为空"));
                                    return;
                                }
                                // 处理风险事件
                                Pair<Integer, Integer> riskCaseTypePair = new Pair<>(
                                        thisRiskCaseDo.getSource().getCode(),
                                        thisRiskCaseDo.getType().getCode());
                                handleStrategy.process(callMrmStrategyMap.get(riskCaseTypePair), filterDTO,
                                        thisRiskCaseDo);
                            });
                } catch (UnableGetLockException e) {
                    log.warn("handleStrategy process error", e);
                }

            }
        } catch (Exception e) {
            log.error("ReportRiskCaseCrane error", e);
        }
    }


    /**
     * 将风险案例列表转换为可过滤的DTO列表和VIN到风险案例的映射
     *
     * @param riskCaseDOList 风险案例列表
     * @return Pair对象，包含可过滤的DTO列表和VIN到风险案例的映射
     */
    private Pair<List<RiskCaseCallMrmFilterDTO>, Map<String, RiskCaseDO>> convert2CallMrmRiskCaseDTOListAndMap(
            List<RiskCaseDO> riskCaseDOList) {
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return null;
        }

        List<String> caseIdList = riskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        // 构建查询参数并查询风险案例与车辆的关系
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIdList).build());

        if (CollectionUtils.isEmpty(riskCaseVehicleRelationDOList)) {
            log.error("风险统计任务构建车辆状态实时快照结果为空",
                    new RuntimeException("风险统计任务构建车辆状态实时快照结果为空"));
            return null;
        }

        Map<String, RiskCaseDO> vinToRiskCaseMap = new HashMap<>();

        // 构建vin 到风险事件的映射关系
        for (RiskCaseVehicleRelationDO relationDO : riskCaseVehicleRelationDOList) {
            RiskCaseDO riskCaseDO = riskCaseDOList.stream()
                    .filter(r -> Objects.equals(r.getCaseId(), relationDO.getCaseId()))
                    .findFirst()
                    .orElse(null);
            if (riskCaseDO != null) {
                vinToRiskCaseMap.put(relationDO.getVin(), riskCaseDO);
            }
        }

        // 构建vin列表
        List<String> vinList = riskCaseVehicleRelationDOList.stream().map(RiskCaseVehicleRelationDO::getVin)
                .collect(Collectors.toList());
        // 构建车辆的实时信息快照
        List<RiskCaseCallMrmFilterDTO> riskCaseCallMrmFilterDTOS = buildRiskCaseCallMrmFilterDTOList(vinList,
                vinToRiskCaseMap, caseIdList);
        // 组装信息并返回
        return new Pair<>(riskCaseCallMrmFilterDTOS, vinToRiskCaseMap);
    }

    /**
     * 构建可过滤的DTO列表
     *
     * @param vinList
     * @param vinToRiskCaseMap
     * @return
     */
    public List<RiskCaseCallMrmFilterDTO> buildRiskCaseCallMrmFilterDTOList(List<String> vinList,
            Map<String, RiskCaseDO> vinToRiskCaseMap, List<String> caseIdList) {
        // 1 计算车辆实时状态快照
        List<VehicleInfoDO> vehicleInfoDOList = vehicleInfoRepository.queryByVinList(vinList);
        if (CollectionUtils.isEmpty(vehicleInfoDOList)) {
            log.error("风险统计任务查询车辆状态实时快照结果为空",
                    new RuntimeException("风险统计任务查询车辆状态实时快照结果为空"));
            return null;
        }
        log.info("风险统计任务查询车辆状态实时快照结果：{}", vehicleInfoDOList);
        // 2 查询坐席连接状态
        Map<String, Integer> vin2MrmStatusMap = vehicleStatusAdapter.getVin2MrmStatusMap(vinList);
        log.info("风险统计任务查询坐席连接状态结果：{}", vin2MrmStatusMap);
        // 3 查询RE工单状态
        Map<String, Boolean> vin2ReStatusMap = reTicketAdaptor.queryByVinList(vinList);
        log.info("风险统计任务查询RE工单状态结果：{}", vin2ReStatusMap);
        // 4 批量查询停滞原因
        CaseMarkInfoDOQueryParamDTO param = CaseMarkInfoDOQueryParamDTO.builder().caseIdList(caseIdList).build();
        Map<String, CaseMarkInfoDO> caseIdToMarkInfoDOMap = caseMarkInfoRepository.queryMapByParam(param);

        List<RiskCaseCallMrmFilterDTO> riskCaseCallMrmFilterDTOS = vehicleInfoDOList.stream().map(vehicleInfoDO ->
                {
                    // 取标注信息
                    CaseMarkInfoDO caseMarkInfoDO = Optional.ofNullable(
                                    vinToRiskCaseMap.get(vehicleInfoDO.getVin()))
                            .map(RiskCaseDO::getCaseId).filter(caseId -> caseIdToMarkInfoDOMap != null)
                            .map(caseIdToMarkInfoDOMap::get).orElse(null);
                    // 取停滞原因
                    ImproperStrandingReason improperStrandingReason =
                            caseMarkInfoDO != null ? caseMarkInfoDO.getImproperStrandingReason() : null;
                    // 取标注类型
                    return RiskCaseCallMrmFilterDTO.builder()
                            .vin(vehicleInfoDO.getVin())
                            .vhr(Optional.ofNullable(vehicleInfoDO.getVhr())
                                    .map(VHRModeEnum::getCode)
                                    .orElse(null))
                            .hasAccident(Optional.ofNullable(vehicleInfoDO.getWithAccidentOrder())
                                    .orElse(false))
                            .hasReOrder(vin2ReStatusMap.getOrDefault(vehicleInfoDO.getVin(),
                                    false))
                            .mrmStatus(vin2MrmStatusMap.getOrDefault(vehicleInfoDO.getVin(),
                                    null))
                            .hasRescueOrder(Optional.ofNullable(vehicleInfoDO.getWithRescueOrder())
                                    .orElse(false))
                            .riskDuration(DatetimeUtil.getTimeDiff(
                                    vinToRiskCaseMap.get(vehicleInfoDO.getVin()).getOccurTime(),
                                    new Date(), ChronoUnit.MINUTES))
                            .purpose(vehicleInfoDO.getPurpose())
                            .improperStrandingReason(improperStrandingReason)
                            .build();
                }

        ).collect(Collectors.toList());

        return riskCaseCallMrmFilterDTOS;
    }
}
