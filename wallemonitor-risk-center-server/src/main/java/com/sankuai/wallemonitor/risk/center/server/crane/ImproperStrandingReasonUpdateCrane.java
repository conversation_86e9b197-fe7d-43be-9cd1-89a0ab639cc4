package com.sankuai.wallemonitor.risk.center.server.crane;


import static com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum.VHR_GREAT_THAN_ONE;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.pigeon.registry.zookeeper.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.component.RiskHandleCommonCompute;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.ReTicketAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.ImproperStrandingReasonUpdateCraneConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ManualParkingMarkConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DriveModeRecordQueryParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DriveModeRecordQueryParamVTO.SingleVinQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.DriveModeRecordVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 定时更新case停滞原因
 */
@Slf4j
@CraneConfiguration

public class ImproperStrandingReasonUpdateCrane {

    private final static Integer LIST_PARTITION_SIZE = 10;

    private final static Set<DriverModeEnum> manualModeList = new HashSet<>(
            Arrays.asList(DriverModeEnum.CLOUD_HARD_TAKEOVER, DriverModeEnum.CLOUD_HARD_TAKEOVER,
                    DriverModeEnum.CLOUD_HARD_TAKEOVER));
    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private ReTicketAdapter reTicketAdapter;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskHandleCommonCompute handleCommonCompute;

    @Resource
    private LockUtils lockUtils;

    @ConfigValue(key = "improper.stranding.reason.query.before.time.mins", defaultValue = "120")
    private Integer queryBeforeTimeMins;

    @ConfigValue(key = "risk.improper.stranding.reason.update.crane.config", defaultValue = "{\"sourceList\":[3,5],\"typeList\":[9]}")
    private ImproperStrandingReasonUpdateCraneConfigDTO configDTO;

    @Crane(CraneConstant.IMPROPER_STRANDING_REASON_UPDATE_CRANE)
    @OperateEnter(OperateEnterActionEnum.IMPROPER_STRANDING_CASE_CRANE)
    public void run() throws Exception {
        log.info("定时更新case停滞原因开始执行");
        CheckUtil.isNotNull(configDTO, "ImproperStrandingReasonUpdateCraneConfigDTO不能为空");
        try {
            /** 1. 查询创建时间2小时内 && source==3 && 状态为待处置或处置中的 case */
            RiskCaseDOQueryParamDTO riskCaseDOQueryParamDTO = RiskCaseDOQueryParamDTO.builder()
                    .sourceList(configDTO.getSourceList())
                    .caseTypeList(configDTO.getTypeList())
                    .statusList(Arrays.asList(RiskCaseStatusEnum.NO_DISPOSAL.getCode(),
                            RiskCaseStatusEnum.IN_DISPOSAL.getCode()))
                    .createTimeBelowTo(new Date())
                    .createTimeCreateTo(DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, queryBeforeTimeMins))
                    .build();
            List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(riskCaseDOQueryParamDTO);
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                log.info("没有符合条件需要更新的case");
                return;
            }
            Lists.partition(riskCaseDOList, LIST_PARTITION_SIZE).forEach(caseSubList -> {
                Set<String> eventIdSet = caseSubList.stream().map(RiskCaseDO::getEventId).collect(Collectors.toSet());
                Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(eventIdSet);
                try {
                    lockUtils.batchLockNoWait(lockKeys, () -> runInLock(caseSubList));
                } catch (UnableGetLockException e) {
                    log.warn("获取锁失败");
                }
            });
        } catch (Exception e) {
            log.error("定时更新case停滞原因异常", e);
        }
    }

    /**
     * 锁内执行
     *
     * @param caseList
     */
    private void runInLock(List<RiskCaseDO> caseList) {
        /** 2. 查询风险事件所关联的车辆相关信息，过滤得到 vhr > 1 的 case 数据 */
        List<String> caseIdList = caseList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        RiderCaseVehicleRelationDOParamDTO riderCaseVehicleRelationDOParamDTO = RiderCaseVehicleRelationDOParamDTO
                .builder()
                .caseIdList(caseIdList)
                .build();
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                riderCaseVehicleRelationDOParamDTO);
        riskCaseVehicleRelationDOList = riskCaseVehicleRelationDOList.stream()
                .filter(riskCaseVehicleRelationDO ->
                        null != riskCaseVehicleRelationDO.getVehicleSnapshotInfo()
                                && riskCaseVehicleRelationDO.getVehicleSnapshotInfo().getVhr()
                                == VHR_GREAT_THAN_ONE)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(riskCaseVehicleRelationDOList)) {
            log.info("没有vhr>1的车辆");
            return;
        }
        Map<String, RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOMap = riskCaseVehicleRelationDOList.stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId,
                        Function.identity(),
                        (v1, v2) -> v1));
        // 获取车辆信息
        List<String> vinList = riskCaseVehicleRelationDOList.stream()
                .filter(riskCaseVehicleRelationDO -> null
                        != riskCaseVehicleRelationDO.getVehicleSnapshotInfo())
                .map(riskCaseVehicleRelationDO -> riskCaseVehicleRelationDO.getVehicleSnapshotInfo()
                        .getVin())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vinList)) {
            log.info("没有车辆信息");
            return;
        }
        /** 3. 查询数据总线服务，得到车辆相关的救援工单和事故工单数据 */
        VehicleRuntimeInfoParamVTO vehicleRuntimeInfoParamVTO = VehicleRuntimeInfoParamVTO.builder().vinList(vinList)
                .build();
        List<VehicleEveInfoVTO> vehicleEveInfoVTOList = vehicleAdapter.queryRuntimeVehicleInfo(
                vehicleRuntimeInfoParamVTO);
        if (CollectionUtils.isEmpty(vehicleEveInfoVTOList)) {
            log.info("查询数据总线服务，没有查询到车辆信息");
        }
        Map<String, VehicleEveInfoVTO> vehicleEveInfoVTOMap = vehicleEveInfoVTOList.stream()
                .collect(Collectors.toMap(VehicleEveInfoVTO::getVin, Function.identity(),
                        (v1, v2) -> v1));
        /** 4. 查询RE服务，得到车辆相关的RE数据 */
        Map<String, Boolean> vehicleReTicketMap = reTicketAdapter.queryByVinList(vinList);

        /** 5. 查到 case 关联的标注信息 */
        CaseMarkInfoDOQueryParamDTO param = CaseMarkInfoDOQueryParamDTO.builder().caseIdList(caseIdList).build();
        RiskCaseDOQueryParamDTO caseDOQueryParamDTO = RiskCaseDOQueryParamDTO.builder().caseIdList(caseIdList).build();
        Map<String, CaseMarkInfoDO> caseMarkInfoDOMap = caseMarkInfoRepository.queryMapByParam(param);
        Map<String, RiskCaseDO> caseDOMap = riskCaseRepository.queryByParam(caseDOQueryParamDTO).stream()
                .collect(Collectors.toMap(RiskCaseDO::getCaseId, Function.identity(), (v1, v2) -> v1));

        // 通过驾驶模式的变更判断时候为主动停靠
        Map<String, DriveModeRecordVTO> driveModeRecordVTOMap = getDriveModeRecordVTOMap(riskCaseVehicleRelationDOList,
                caseMarkInfoDOMap, caseDOMap);

        /** 6. 遍历case, 更新case关联的工单信息 */
        caseList.forEach(item -> {
            try {
                CaseMarkInfoDO caseMarkInfoDO = updateOneCaseMarkInfo(item, caseMarkInfoDOMap,
                        riskCaseVehicleRelationDOMap, vehicleReTicketMap, vehicleEveInfoVTOMap, driveModeRecordVTOMap);
                log.info("case mark info:{}", JsonUtils.serialize(caseMarkInfoDO));
                if (null != caseMarkInfoDO) {
                    caseMarkInfoRepository.save(caseMarkInfoDO);
                }
            } catch (Exception e) {
                log.error("case:{} 关联工单更新异常", item, e);
            }
        });
    }

    private CaseMarkInfoDO updateOneCaseMarkInfo(RiskCaseDO riskCaseDO, Map<String, CaseMarkInfoDO> caseMarkInfoDOMap,
            Map<String, RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOMap,
            Map<String, Boolean> vehicleReTicketMap,
            Map<String, VehicleEveInfoVTO> vehicleEveInfoVTOMap,
            Map<String, DriveModeRecordVTO> driveModeRecordVTOMap) {
        CaseMarkInfoDO caseMarkInfoDO;
        String caseId = riskCaseDO.getCaseId();
        if (caseMarkInfoDOMap.containsKey(caseId)) {
            caseMarkInfoDO = caseMarkInfoDOMap.get(caseId);  // 现有数据待更新
        } else {
            caseMarkInfoDO = CaseMarkInfoDO.builder().caseId(caseId).build();    // 新增数据
        }

        // 获取 case 关联 vin
        String vin = Optional.ofNullable(riskCaseVehicleRelationDOMap.get(caseId))
                .map(RiskCaseVehicleRelationDO::getVehicleSnapshotInfo)
                .map(VehicleInfoDO::getVin)
                .orElse(null);
        if (StringUtils.isBlank(vin)) {
            log.info("caseId:{} 关联的 vin 为空", caseId);
            return null;
        }

        ImproperStrandingReason improperStrandingReason = null == caseMarkInfoDO.getImproperStrandingReason() ?
                ImproperStrandingReason.builder().build() : caseMarkInfoDO.getImproperStrandingReason();
        Boolean withReOrder = vehicleReTicketMap.get(vin);
        VehicleEveInfoVTO vehicleEveInfoVTO = vehicleEveInfoVTOMap.get(vin);
        Boolean withAccidentOrder = null == vehicleEveInfoVTO ? null : vehicleEveInfoVTO.getWithAccidentOrder();
        Boolean withRescueOrder = null == vehicleEveInfoVTO ? null : vehicleEveInfoVTO.getWithRescueOrder();

        // 工单为 true 才更新
        if (null != withReOrder && withReOrder) {
            improperStrandingReason.withReOrder();
        }
        if (null != withAccidentOrder && withAccidentOrder) {
            improperStrandingReason.withAccidentOrder();
        }
        if (null != withRescueOrder && withRescueOrder) {
            improperStrandingReason.withRescueOrder();
        }

        // 判断是否需要更新为主动停靠
        // 计算停滞时刻
        ManualParkingMarkConfigDTO manualParkingMarkConfigDTO = lionConfigRepository.getManualParkingMarkConfig();
        CheckUtil.isNotNull(manualParkingMarkConfigDTO, "手动停车配置为空");
        Date strandingTime = riskCaseDO.getOccurTime();
        if (Objects.nonNull(driveModeRecordVTOMap) && !driveModeRecordVTOMap.isEmpty()
                && handleCommonCompute.calcIsParkingByManual(driveModeRecordVTOMap.get(caseId), strandingTime,
                caseId)) {
            improperStrandingReason.withManualParking();
        }

        log.info(
                "caseId:{} 关联工单更新，withReOrder:{}, withAccidentOrder:{}, withRescueOrder:{}, withManualParking:{}",
                caseId, withReOrder, withAccidentOrder, withRescueOrder,
                improperStrandingReason.getWithManualParking());
        caseMarkInfoDO.updateImproperStrandingReason(improperStrandingReason);
        return caseMarkInfoDO;
    }


    /**
     * 查询车辆驾驶状态记录
     *
     * @param riskCaseVehicleRelationDOList
     * @return
     */
    private Map<String, DriveModeRecordVTO> getDriveModeRecordVTOMap(
            List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList,
            Map<String, CaseMarkInfoDO> caseMarkInfoDOMap, Map<String, RiskCaseDO> caseDOMap) {
        ManualParkingMarkConfigDTO manualParkingMarkConfigDTO = lionConfigRepository.getManualParkingMarkConfig();
        CheckUtil.isNotNull(manualParkingMarkConfigDTO, "手动停车配置为空");
        List<RiskCaseVehicleRelationDO> filterRiskCaseVehicleRelationDOList = riskCaseVehicleRelationDOList.stream()
                .filter(riskCaseVehicleRelationDO -> {
                    CaseMarkInfoDO caseMarkInfoDO = caseMarkInfoDOMap.get(riskCaseVehicleRelationDO.getCaseId());
                    if (Objects.nonNull(caseMarkInfoDO) && Objects.nonNull(
                            caseMarkInfoDO.getImproperStrandingReason())) {
                        ImproperStrandingReason improperStrandingReason = caseMarkInfoDO.getImproperStrandingReason();
                        Boolean withManualParking = improperStrandingReason.getWithManualParking();
                        if (Objects.nonNull(withManualParking) && withManualParking) {
                            log.info("getDriveModeRecordVTOMap, caseId:{} 已经计算过主动停靠",
                                    riskCaseVehicleRelationDO.getCaseId());
                            return false;
                        }
                    }
                    return true;
                }).filter(riskCaseVehicleRelationDO -> {
                    RiskCaseDO riskCaseDO = caseDOMap.get(riskCaseVehicleRelationDO.getCaseId());
                    if (Objects.isNull(riskCaseDO)) {
                        return false;
                    }
                    if (DatetimeUtil.getTimeDiff(riskCaseDO.getOccurTime(), new Date(), ChronoUnit.MINUTES)
                            > manualParkingMarkConfigDTO.getTimeoutCancelCheckManualParkingMins()) {
                        log.info("getDriveModeRecordVTOMap, caseId:{} 超过 {} 分钟",
                                riskCaseVehicleRelationDO.getCaseId(),
                                manualParkingMarkConfigDTO.getTimeoutCancelCheckManualParkingMins());
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
        log.info("getDriveModeRecordVTOMap, filterRiskCaseVehicleRelationDOList:{}",
                JacksonUtils.to(filterRiskCaseVehicleRelationDOList));
        if (CollectionUtils.isEmpty(filterRiskCaseVehicleRelationDOList)) {
            return Maps.newHashMap();
        }
        List<SingleVinQueryParam> vinQueryParamList = filterRiskCaseVehicleRelationDOList.stream()
                .map(x -> {
                    RiskCaseDO riskCaseDO = caseDOMap.get(x.getCaseId());
                    if (Objects.isNull(riskCaseDO)) {
                        return null;
                    }
                    return SingleVinQueryParam.builder()
                            .caseId(x.getCaseId())
                            .vin(x.getVin())
                            .startTime(DatetimeUtil.getNSecondsBeforeDateTime(riskCaseDO.getOccurTime(),
                                    manualParkingMarkConfigDTO.getQueryDriveModeNSecondsBeforeStranding()))
                            .endTime(new Date())
                            .build();
                }).filter(Objects::nonNull).collect(Collectors.toList());
        DriveModeRecordQueryParamVTO driveModeQueryParam = DriveModeRecordQueryParamVTO.builder()
                .vinQueryParamList(vinQueryParamList).build();
        return vehicleAdapter.queryDriveModeRecord(driveModeQueryParam);
    }
}
