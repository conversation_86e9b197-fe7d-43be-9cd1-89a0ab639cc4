package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.commons.exception.ParamInputErrorException;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter.SearchNearbyRequestVTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.FCDriveTaskRouteEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.FCDriveTaskRouteEventDTO.RoutePoint;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapElementTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FCDriveTaskRouteEventConsumer {

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private SquirrelAdapter squirrelAdapter;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private HdMapAdapter hdMapAdapter;


    @MafkaConsumer(
            namespace = "waimai", topic = "fleet.commander.drivetask.route.event",
            group = "wallemonitor.risk.fc.drivetask.route.event.consumer"
    )
    @OperateEnter(OperateEnterActionEnum.CONSUMER_FC_DRIVETASK_ROUTE_EVENT_MESSAGE)
    public ConsumeStatus receive(String message) {
        FCDriveTaskRouteEventDTO eventDTO = null;
        try {
            // 1 消息体解析
            eventDTO = JacksonUtils.from(message,
                    new TypeReference<FCDriveTaskRouteEventDTO>() {
                    });
            // 2 消息体校验
            CheckUtil.isNotNull(eventDTO, "消息体不能为空");
            CheckUtil.isNotBlank(eventDTO.getVin(), "车辆VIN不能为空");
            CheckUtil.isNotEmpty(eventDTO.getRoutePoints(), "该车当前正在运行的路由信息不能为空");

            // 3 将指定车辆的路由信息更新到上下文缓存
            updateVehicleRouteInfo(eventDTO);

            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (ParamInputErrorException e) {
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("FCDriveTaskRouteEventConsumer# consume# message parse error, message = {}", message, e);
            if (checkEndBeforeStartCanRetry(eventDTO)) {
                return ConsumeStatus.CONSUME_FAILURE;
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

    /**
     * 更新车辆路由信息到上下文缓存中
     *
     * @param eventDTO 路由事件DTO
     */
    private void updateVehicleRouteInfo(FCDriveTaskRouteEventDTO eventDTO) {
        String vin = eventDTO.getVin();
        String area = vehicleAdapter.getVehicleHdMapArea(vin);
        try {
            // 1. 查询车辆上下文
            VehicleRuntimeInfoContextDO contextDO = vehicleRuntimeInfoContextRepository.getFromCache(vin);
            // 2. 将路由信息更新到车辆运行时上下文
            List<String> laneIdList = IntStream.range(0, eventDTO.getRoutePoints().size()).parallel()
                    .mapToObj(i -> new Pair<>(i, eventDTO.getRoutePoints().get(i)))  // 保存索引
                    .map(entry -> {
                        RoutePoint routePoint = entry.getValue();
                        PositionDO position = GeoToolsUtil.utmToWgs84(routePoint.getX(), routePoint.getY());
                        return new Pair<>(entry.getKey(),  // 保持原索引
                                hdMapAdapter
                                        .searchNearby(SearchNearbyRequestVTO.builder().area(area).range(10D)
                                                .positionDO(position).hdMapEnum(HdMapEnum.LANE_POLYGON)
                                                .restrictType(HdMapElementTypeEnum.getLaneType()).build())
                                        .stream().filter(lane -> lane.getPolygonDO().isInPolygon(position)).findFirst()
                                        .map(HdMapElementGeoDO::getId).orElse(null));
                    }).filter(entry -> Objects.nonNull(entry.getValue())).sorted(Comparator.comparing(Pair::getKey))  // 按原始索引排序
                    .map(Pair::getValue).distinct()  // 去重
                    .collect(Collectors.toList());
            // 保存字段
            contextDO.setFcLaneIdList(laneIdList);
            // fixme: 这里是为了清理历史数据
            contextDO.setRoutePoints(Lists.newArrayList());
            // 更新到缓存
            vehicleRuntimeInfoContextRepository.updateCache(contextDO, System.currentTimeMillis());
        } catch (Exception e) {
            log.error("更新车辆[{}]路由信息到上下文缓存失败", vin, e);
            throw new RuntimeException("更新车辆路由信息到上下文缓存失败");
        }
    }


    /**
     * 判断是否可以重试
     *
     * @param eventDTO
     * @return
     */
    private boolean checkEndBeforeStartCanRetry(FCDriveTaskRouteEventDTO eventDTO) {
        if (eventDTO == null) {
            return false;
        }
        BroadCastStrategyConfigDTO strategyConfig = lionConfigRepository.getByCaseType(
                RiskCaseTypeEnum.UNKNOWN); // 临时使用UNKNOWN类型，需要在RiskCaseTypeEnum中添加FC_DRIVETASK_ROUTE_EVENT
        // 没有对应的安全风险事件类型，舍弃
        if (strategyConfig == null) {
            return false;
        }

        // 判断是否可以重试
        return strategyConfig.canRetry(eventDTO.getUpdateTimestamp());
    }
} 