package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.EventPlatSearchAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseDisposedInfoUpdateCraneConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleStatusChangeInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@CraneConfiguration
public class RiskCaseDisposedInfoUpdateCrane {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private EventPlatSearchAdapter eventPlatSearchAdapter;

    @ConfigValue(key = LionKeyConstant.LION_KEY_DISPOSED_CASE_INFO_UPDATE_CRANE_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private RiskCaseDisposedInfoUpdateCraneConfigDTO configDTO;


    @Crane(CraneConstant.RISK_CASE_DISPOSED_INFO_UPDATE_CRANE)
    @OperateEnter(OperateEnterActionEnum.RISK_CASE_DISPOSED_INFO_UPDATE_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("RiskCaseDisposedInfoUpdateCrane start");
        try {
            CheckUtil.isNotNull(configDTO, "configDTO is null");
            // 1 查询 N 分钟内创建 且 状态为终态 且 云控介入时间为默认值 且 符合指定来源/类型的风险事件
            RiskCaseDOQueryParamDTO paramDTO = RiskCaseDOQueryParamDTO.builder()
                    .vhrModeList(Arrays.asList(VHRModeEnum.VHR_GREAT_THAN_ONE.getCode()))
                    .closeTimeCreateTo(DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES,
                            configDTO.getQueryRangeMinutes()))
                    .caseTypeList(configDTO.getCaseTypeList())
                    .sourceList(configDTO.getCaseSourceList())
                    .statusList(RiskCaseStatusEnum.getTerminal())
                    .seatInterventionTime(DatetimeUtil.ZERO_DATE).build();
            paramDTO.setLeftJoinRelation(true);
            List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(paramDTO);
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                log.info("RiskCaseDisposedInfoUpdateCrane riskCaseDOList is empty");
                return;
            }
            handleUpdateMessage(riskCaseDOList);

        } catch (Exception e) {
            log.error("RiskCaseDisposedInfoUpdateCrane error", e);
        }
    }

    /**
     * 处理更新消息
     *
     * @param riskCaseDOList
     */
    private void handleUpdateMessage(List<RiskCaseDO> riskCaseDOList) {
        // 分批次
        Lists.partition(riskCaseDOList, configDTO.getBatchSize()).forEach(subRiskCaseDOList -> {
            // 并发处置
            ParallelExecutor.executeParallelTasks("vehicle_runtime_info_biz_status", subRiskCaseDOList, riskCaseDO -> {
                try {
                    lockUtils.batchLockNoWait(
                            LockKeyPreUtil.buildKeyWithEventId(Collections.singleton(riskCaseDO.getCaseId())),
                            () -> {
                                RiskCaseVehicleRelationDO relationDO = riskCaseVehicleRelationRepository.getByCaseId(
                                        riskCaseDO.getCaseId());
                                if (Objects.isNull(relationDO)) {
                                    return;
                                }
                                Date endTime = DatetimeUtil.getAfterTime(riskCaseDO.getCloseTime(), TimeUnit.SECONDS,
                                        configDTO.getQueryRangeDelaySeconds());
                                VehicleStatusChangeInfoDTO changeInfoDTO = eventPlatSearchAdapter.queryLatestUpdateToMrmDriveModeChange(
                                        relationDO.getVin(), riskCaseDO.getOccurTime(), endTime);
                                if (Objects.isNull(changeInfoDTO)) {
                                    return;
                                }
                                relationDO.setSeatInterventionTime(changeInfoDTO.getChangeTime());
                                riskCaseVehicleRelationRepository.save(relationDO);
                            });
                } catch (Exception e) {
                    log.error("RiskCaseDisposedInfoUpdateCrane.handleUpdateMessage error", e);
                }
            });
        });


    }
}
