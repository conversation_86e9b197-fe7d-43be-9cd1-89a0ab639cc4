package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftPage;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.api.request.PublicEventAdminListRequest;
import com.sankuai.wallemonitor.risk.center.api.request.PublicEventAdminSaveRequest;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListPublicEventDetailVO;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftPublicEventAdminService;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.PublicDetailConvert;
import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HandleTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.PublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.PublicDetailRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.PublicDetailDOQueryParamDTO;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftPublicEventAdminServiceImpl implements IThriftPublicEventAdminService {

    @Resource
    private PublicDetailRepository publicEventRepository;

    @Resource
    private PublicDetailConvert publicDetailConvert;

    @Override
    public EveThriftPageResponse<List<AdminListPublicEventDetailVO>> listPublicEventDetail(
            PublicEventAdminListRequest request) {
        CheckUtil.isNotNull(request, "请求参数不能为空");
        CheckUtil.isNotNull(request.getPageNum(), "分页参数不能为空");
        CheckUtil.isNotNull(request.getPageSize(), "分页参数不能为空");
        PublicDetailDOQueryParamDTO.PublicDetailDOQueryParamDTOBuilder builder = PublicDetailDOQueryParamDTO.builder();
        if (Objects.nonNull(request.getPublicEventId())) {
            builder.publicEventId(request.getPublicEventId());
        }
        if (CollectionUtils.isNotEmpty(request.getResponsiblePartyList())) {
            builder.responsiblePartyList(request.getResponsiblePartyList());
        }
        if (CollectionUtils.isNotEmpty(request.getCategoryList())) {
            builder.categoryList(request.getCategoryList());
        }
        if (StringUtils.isNotBlank(request.getOccurTimeEnd())) {
            builder.occurTimeBelowTo(DatetimeUtil.convertDatetimeStr2Date(request.getOccurTimeEnd()));
        }
        if (StringUtils.isNotBlank(request.getOccurTimeStart())) {
            builder.occurTimeCreateTo(DatetimeUtil.convertDatetimeStr2Date(request.getOccurTimeStart()));
        }
        builder.orderByOccurTime(OrderEnum.DESC);
        Paging<PublicEventDetailDO> paging = publicEventRepository.queryByParamByPage(
                builder.build(),
                request.getPageNum(), request.getPageSize()
        );
        EveThriftPageResponse<List<AdminListPublicEventDetailVO>> eveThriftPageResponse = EveThriftPageResponse.ok(
                paging.getElements().stream().map(publicDetailConvert::toVO).collect(
                        Collectors.toList()));
        EveThriftPage eveThriftPage = new EveThriftPage();
        eveThriftPage.setPageNum(paging.getPageNum());
        eveThriftPage.setPageSize(paging.getPageSize());
        eveThriftPage.setTotal(paging.getTotal());
        eveThriftPageResponse.setPaging(eveThriftPage);
        return eveThriftPageResponse;
    }

    @Override
    public EveThriftResponse<EmptyResponse> save(PublicEventAdminSaveRequest request) {
        PublicEventDetailDO publicEventDetailDO = publicEventRepository.getByEventId(request.getPublicEventId());
        CheckUtil.isNotNull(publicEventDetailDO, "事件不存在");
        //车辆相关信息
        if (StringUtils.isNotEmpty(request.getVins())) {
            publicEventDetailDO.setVins(Arrays.stream(request.getVins().split(CharConstant.CHAR_COMMA)).collect(
                    Collectors.toList()));
        }
        if (StringUtils.isNotEmpty(request.getVehicleIds())) {
            publicEventDetailDO.setVehicleIds(
                    Arrays.stream(request.getVehicleIds().split(CharConstant.CHAR_COMMA)).collect(
                            Collectors.toList()));
        }
        if (StringUtils.isNotBlank(request.getCity())) {
            publicEventDetailDO.setCity(request.getCity());
        }
        if (StringUtils.isNotBlank(request.getDescription())) {
            publicEventDetailDO.setDescription(request.getDescription());
        }
        if (StringUtils.isNotBlank(request.getDiscoveryType())) {
            publicEventDetailDO.setDiscoveryType(request.getDiscoveryType());
        }
        if (Objects.nonNull(request.getDriveMode())) {
            publicEventDetailDO.setDriveMode(DriverModeEnum.fromCode(request.getDriveMode()));
        }
        if (Objects.nonNull(request.getRiskCaseId())) {
            publicEventDetailDO.setRiskCaseId(request.getRiskCaseId());
        }
        if (StringUtils.isNotBlank(request.getTitle())) {
            publicEventDetailDO.setTitle(request.getTitle());
        }
        if (Objects.nonNull(request.getHandleType())) {
            publicEventDetailDO.setHandleType(HandleTypeEnum.fromCode(request.getHandleType()));
        }
        if (StringUtils.isNotBlank(request.getOccurTime())) {
            publicEventDetailDO.setOccurTime(DatetimeUtil.convertDatetimeStr2Date(request.getOccurTime()));
        }
        if (StringUtils.isNotBlank(request.getRequestHelpTime())) {
            publicEventDetailDO.setRequestHelpTime(DatetimeUtil.convertDatetimeStr2Date(request.getRequestHelpTime()));
        }
        if (StringUtils.isNotBlank(request.getStartHandleTime())) {
            publicEventDetailDO.setStartHandleTime(DatetimeUtil.convertDatetimeStr2Date(request.getStartHandleTime()));
        }
        if (StringUtils.isNotBlank(request.getFinishTime())) {
            publicEventDetailDO.setFinishTime(DatetimeUtil.convertDatetimeStr2Date(request.getFinishTime()));
        }
        if (StringUtils.isNotBlank(request.getCategory())) {
            publicEventDetailDO.setCategory(request.getCategory());
        }
        publicEventRepository.save(publicEventDetailDO);
        return EveThriftResponse.ok(EmptyResponse.builder().build());
    }
}
