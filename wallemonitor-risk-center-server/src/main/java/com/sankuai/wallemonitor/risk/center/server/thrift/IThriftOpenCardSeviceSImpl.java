package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.handler.OpenCardCallbackHandler;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseMessageService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.xm.openplatform.api.entity.CardCallBackReq;
import com.sankuai.xm.openplatform.api.entity.CardCallBackResp;
import com.sankuai.xm.openplatform.api.entity.PullCardData;
import com.sankuai.xm.openplatform.api.entity.PullCardReq;
import com.sankuai.xm.openplatform.api.entity.PullCardResp;
import com.sankuai.xm.openplatform.api.service.sdk.OpenCardSeviceSI;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import com.sankuai.xm.openplatform.common.enums.ResCodeEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftOpenCardSeviceSImpl implements OpenCardSeviceSI.Iface {

    @Resource
    private RiskCaseMessageService riskCaseMessageService;
    @Resource
    private LionConfigRepository lionConfigRepository;
    @Resource
    private OpenCardCallbackHandler openCardCallbackHandler;

    @Override
    @OperateEnter(OperateEnterActionEnum.CARD_CALLBACK_ENTRY)
    public CardCallBackResp cardCallBack(CardCallBackReq cardCallBackReq) throws TException {
        log.info("接收到一体化卡片回调消息，data={}", JacksonUtils.to(cardCallBackReq));
        // 异步处理卡片回调消息
        openCardCallbackHandler.asyncHandle(cardCallBackReq);
        CardCallBackResp cardCallBackResp = new CardCallBackResp();
        RespStatus respStatus = new RespStatus();
        respStatus.setCode(ResCodeEnum.SUCCESS.getCode());
        respStatus.setMsg(ResCodeEnum.SUCCESS.getMsg());
        cardCallBackResp.setStatus(respStatus);
        return cardCallBackResp;
    }

    @Override
    @OperateEnter(OperateEnterActionEnum.PULL_URL_CARD_DATA)
    public PullCardResp pullCard(PullCardReq pullCardReq) throws TException {
        PullCardResp pullCardResp = new PullCardResp();
        RespStatus respStatus = new RespStatus();
        respStatus.setCode(0);
        pullCardResp.setStatus(respStatus);
        if (pullCardReq == null || pullCardReq.getRequestParam() == null) {
            return pullCardResp;
        }
        List<String> stringList = Arrays.asList(StringUtils.split(pullCardReq.getRequestParam().getRequestId(), "_"));
        if (CollectionUtils.isEmpty(stringList) || stringList.size() != 2) {
            return pullCardResp;
        }

        String caseId = stringList.get(0);
        Map<String, Map<String, String>> caseRenderData = riskCaseMessageService.getCaseRenderData(
                Collections.singletonList(caseId));
        if (MapUtils.isEmpty(caseRenderData)) {
            return pullCardResp;
        }
        Map<String, String> caseData = caseRenderData.get(caseId);
        if (MapUtils.isEmpty(caseData)) {
            return pullCardResp;
        }
        PullCardData pullCardData = new PullCardData();
        pullCardData.setTemplateId(lionConfigRepository.getCaseUrlTemplateId());
        pullCardData.setVariableData(JacksonUtils.to(caseData));
        pullCardData.setVersion(System.currentTimeMillis());
        //设置返回
        pullCardResp.setData(pullCardData);
        return pullCardResp;
    }
}
