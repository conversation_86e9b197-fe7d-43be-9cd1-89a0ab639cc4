package com.sankuai.wallemonitor.risk.center.server.aop;

import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.server.aop.filter.CommonAroundAspect;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 消息发送切面
 */
@Order(10)
@Component
@Slf4j
@Aspect
public class MessageProducerAspect extends CommonAroundAspect {


    @Pointcut("execution(* com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer.*(..))")
    public void accessLog() {
        // skip
    }

    /**
     * 打印的字段
     *
     * @return
     */
    @Override
    public String getLogField() {
        return LogCenterFieldEnum.AL_PRODUCER.getFieldCode();
    }


    /**
     * 做切面日志处理
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("accessLog()")
    public Object doQueryAround(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            return super.handleAroundLogAndReturnResult(joinPoint, null);
        } catch (Exception e) {
            //发送消息时不中断外部流程
            log.error(String.format("%s发送消息出现异常，兜底异常日志，保护", getSignature(joinPoint)), e);
        }
        return null;

    }

    /**
     * 获得签名
     *
     * @param joinPoint
     * @return
     */
    @Override
    protected String getSignature(JoinPoint joinPoint) {
        if (!CommonMessageProducer.class.isAssignableFrom(joinPoint.getTarget().getClass())) {
            return joinPoint.getSignature().toString();
        }
        return ((CommonMessageProducer) (joinPoint.getTarget())).getMessageTopic().toString();

    }


}
