package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.cat.Cat;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskRaptorReportConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskRaptorReportConfigDTO.BusinessReporterConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

@Slf4j
@CraneConfiguration
public class UploadReportCrane {

    @Resource
    private RiskCaseVehicleRelationRepository vehicleRelationRepository;
    @Resource
    private RiskCaseRepository riskCaseRepository;
    @Resource
    private LionConfigRepository lionConfigRepository;

    @Crane(CraneConstant.RISK_REPORTER)
    @OperateEnter(OperateEnterActionEnum.UPLOAD_RISK_REPORTER)
    @ZebraForceMaster
    public void run() throws Exception {
        Map<Integer, RiskRaptorReportConfigDTO> riskRaptorReportConfigDTO = lionConfigRepository
                .getRaptorReportConfig();
        if (MapUtils.isEmpty(riskRaptorReportConfigDTO)) {
            return;
        }
        List<Integer> riskTypeList = new ArrayList<>(riskRaptorReportConfigDTO.keySet());
        Date now = new Date();
        // 查询未解除的停滞不当，按照指定间隔，上报打点，并支持tag
        TimePeriod timePeriod = TimePeriod
                .build(DatetimeUtil.getNSecondsBeforeDateTime(now, DateTimeConstant.ONE_DAY_SECOND), now);
        Map<RiskCaseTypeEnum, List<RiskCaseDO>> riskCaseDOS = riskCaseRepository
                .queryByParam(RiskCaseDOQueryParamDTO.builder()
                        // 类型
                        .caseTypeList(riskTypeList)
                        // 过去24小时
                        .createTimeRange(timePeriod)
                        // 未结束
                        .statusList(RiskCaseStatusEnum.getUnTerminal()).build())
                .stream().collect(Collectors.groupingBy(RiskCaseDO::getType));
        if (MapUtils.isEmpty(riskCaseDOS)) {
            // 不存在
            return;
        }
        Map<String, RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOMap = vehicleRelationRepository
                .queryByParam(RiderCaseVehicleRelationDOParamDTO.builder()
                        .caseIdList(riskCaseDOS.values().stream().flatMap(Collection::stream).map(RiskCaseDO::getCaseId)
                                .collect(Collectors.toList()))
                        .build())
                .stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, Function.identity(), (k1, k2) -> k1));
        riskCaseDOS.forEach((riskCaseTypeEnum, riskCaseList) -> {
            RiskRaptorReportConfigDTO configDTO = riskRaptorReportConfigDTO.get(riskCaseTypeEnum.getCode());
            if (configDTO == null) {
                return;
            }
            // 这一组的risk,被分成多个打点
            Map<String, Map<String, String>> reportAndTagList = new HashMap<>();
            riskCaseList.forEach(risk -> {
                RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = riskCaseVehicleRelationDOMap
                        .get(risk.getCaseId());
                BusinessReporterConfigDTO businessReporterConfigDTO = configDTO.matched(risk,
                        riskCaseVehicleRelationDO);
                if (businessReporterConfigDTO == null) {
                    return;
                }
                // 添加
                reportAndTagList
                        .computeIfAbsent(businessReporterConfigDTO.getRaptorBusinessType(), k -> new HashMap<>())
                        .putAll(businessReporterConfigDTO.getRaptorTagValue(risk, riskCaseVehicleRelationDO));
            });
            reportAndTagList.forEach((businessType, tags) -> {
                Cat.logMetricForCount(businessType, 1, tags);
            });
        });

    }
}
