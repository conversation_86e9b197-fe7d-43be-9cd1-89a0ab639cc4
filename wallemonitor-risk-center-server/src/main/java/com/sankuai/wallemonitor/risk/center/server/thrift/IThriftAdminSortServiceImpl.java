package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.AdminSortRequest;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftAdminSortService;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.UserInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseSortDataDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseSortDataRepository;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 分拣服务实现类
 */
@Slf4j
@Service
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftAdminSortServiceImpl implements IThriftAdminSortService {

    @Resource
    private CaseSortDataRepository repository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Override
    public EveThriftResponse<List<String>> listProblemList() {
        return EveThriftResponse.ok(lionConfigRepository.getSortProblemList());
    }

    @Override
    public EveThriftResponse<EmptyResponse> sort(AdminSortRequest request) {
        CaseSortDataDO sortData = repository.getByCaseId(request.getCaseId());
        log.info("sortData: {}", JacksonUtils.to(sortData));
        if (sortData == null) {
            sortData = RiskCaseFactory.createCaseSortData(request.getCaseId());
        }

        String sorter = StringUtils.defaultIfBlank(UserInfoContext.getUserMis(), CommonConstant.MARK_OPERATOR_UNKNOWN);
        sortData.sort(request.getProblem(), sorter, request.getDescription());
        repository.save(sortData);
        return EveThriftResponse.ok().build();
    }
}
