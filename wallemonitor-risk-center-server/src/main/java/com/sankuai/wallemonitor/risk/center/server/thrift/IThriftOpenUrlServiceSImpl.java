package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseMessageService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.xm.openplatform.api.entity.UrlCardData;
import com.sankuai.xm.openplatform.api.entity.UrlData;
import com.sankuai.xm.openplatform.api.entity.UrlDataReq;
import com.sankuai.xm.openplatform.api.entity.UrlDataResp;
import com.sankuai.xm.openplatform.api.service.sdk.OpenUrlServiceSI;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftOpenUrlServiceSImpl implements OpenUrlServiceSI.Iface {

    @Resource
    private RiskCaseMessageService riskCaseMessageService;


    @Resource
    private LionConfigRepository lionConfigRepository;

    @Override
    @OperateEnter(OperateEnterActionEnum.GET_CASE_URL_DATA)
    public UrlDataResp getUrlData(UrlDataReq urlDataReq) throws TException {
        UrlDataResp urlDataResp = new UrlDataResp();
        RespStatus respStatus = new RespStatus();
        respStatus.setCode(0);
        urlDataResp.setStatus(respStatus);
        Map<String, String> paramMap = HttpUtils.getParam(
                "https://eve.meituan.com" + HttpUrl.parse(urlDataReq.getUrl()).fragment());
        String caseId = paramMap.get("openCaseId");
        if (Objects.isNull(caseId)) {
            return urlDataResp;
        }

        Map<String, Map<String, String>> riskCaseMessage = riskCaseMessageService.getCaseRenderData(
                Collections.singletonList(caseId));
        Map<String, String> renderData = riskCaseMessage.get(caseId);
        if (Objects.isNull(renderData)) {
            return urlDataResp;
        }
        UrlData urlData = new UrlData();
        UrlCardData urlCardData = new UrlCardData();
        urlCardData.setVariableData(JacksonUtils.to(renderData));
        urlCardData.setTemplateId(lionConfigRepository.getCaseUrlTemplateId());
        urlCardData.setVersion(System.currentTimeMillis());
        urlCardData.setRequestId(caseId + "_" + System.currentTimeMillis());
        urlCardData.setEnableForward(true);
        urlData.setCardData(urlCardData);
        urlDataResp.setData(urlData);
        return urlDataResp;
    }
}
