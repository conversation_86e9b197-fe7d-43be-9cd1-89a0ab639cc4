package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftVehicleGrayStrategyService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleAttributesDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleStatusAdapter;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleRealtimeStatusVTO;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.ArrayList;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;


@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftVehicleGrayStrategyServiceImpl implements IThriftVehicleGrayStrategyService {

    /**
     * 分批查询配置常量
     */
    private static final int DEFAULT_BATCH_SIZE = 50;

    @Resource
    private VehicleInfoRepository vehicleInfoRepository;

    @Resource
    private VehicleStatusAdapter vehicleStatusAdapter;


    /**
     * 车辆灰度策略配置
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_VEHICLE_GRAY_STRATEGY_CONFIG, defaultValue = "[]")
    private List<String> vehicleGrayStrategyList;

    @Override
    @OperateEnter(OperateEnterActionEnum.VEHICLE_GRAY_STRATEGY_FILTER_ENTRY)
    public EveThriftResponse<List<String>> getVehicleGrayStrategyFilter() {
        try {

            if(CollectionUtils.isEmpty(vehicleGrayStrategyList)){
                log.info("车辆灰度策略配置为空，直接返回空列表");
                return EveThriftResponse.ok(Collections.emptyList());
            }
        
            List<VehicleRealtimeStatusVTO> vehicleStatusList = vehicleStatusAdapter.queryByVinList(-1, Collections.emptyList());
            if (CollectionUtils.isEmpty(vehicleStatusList)) {
                log.error("未能从车辆状态接口获取到车辆列表");
                return EveThriftResponse.ok(Collections.emptyList());
            }

            // 提取VIN码列表
            List<String> vinList = vehicleStatusList.stream()
                    .filter(Objects::nonNull)
                    .map(VehicleRealtimeStatusVTO::getVin)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 2. 根据VIN码列表分批查询车辆属性信息
            List<VehicleInfoDO> vehicleInfoDOs = queryVehicleInfoInBatches(vinList);

            if (CollectionUtils.isEmpty(vehicleInfoDOs)) {
                log.error("未能根据VIN列表查询到车辆信息, vins: {}", vinList);
                return EveThriftResponse.ok(Collections.emptyList());
            }

            // 3. 将DO转换为DTO
            List<VehicleAttributesDTO> allVehicles = vehicleInfoDOs.stream()
                    .map(vehicleInfoDO -> VehicleAttributesDTO.builder()
                            .vin(vehicleInfoDO.getVin())
                            .vhr(vehicleInfoDO.getVhr())
                            .businessType(vehicleInfoDO.getBusinessType())
                            .placeCode(vehicleInfoDO.getPlaceCode())
                            .purpose(vehicleInfoDO.getPurpose())
                            .build())
                    .collect(Collectors.toList());
            

            // 4. 使用ParallelExecutor进行并发灰度策略匹配
            List<String> filteredVins = filterVehiclesByGrayStrategyWithParallelExecutor(allVehicles);

           

            return EveThriftResponse.ok(filteredVins);
        } catch (Exception e) {
            log.error("获取灰度策略车辆列表失败", e);
            return EveThriftResponse.codeWithMessage(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取灰度策略车辆列表失败").build();
        }
    }

    /**
     * 使用ParallelExecutor进行车辆灰度策略过滤
     */
    private List<String> filterVehiclesByGrayStrategyWithParallelExecutor(List<VehicleAttributesDTO> allVehicles) {
        List<String> filteredVins = ParallelExecutor.executeParallelTasksAndGetResult(
            "vehicle_runtime_info",
            allVehicles,
            vehicle -> {
                try {
                    // dto转化为map
                    Map<String, Object> context = JacksonUtils.from(JacksonUtils.to(vehicle),
                            new TypeReference<Map<String, Object>>() {});

                    // 检查单个车辆是否符合灰度策略
                    boolean matches = vehicleGrayStrategyList.stream()
                            .allMatch(strategy -> {
                                Boolean result = SpELUtil.evaluateBoolean(strategy, context);
                                return Boolean.TRUE.equals(result);
                            });
                    // 只返回符合条件的车辆VIN
                    return matches ? vehicle.getVin() : null;
                } catch (Exception e) {
                    log.error("车辆{}灰度策略匹配异常: {}", vehicle.getVin(), e.getMessage());
                    return null;
                }
            }
    );
    // 过滤掉null值
    return filteredVins.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    /**
     * 分批查询车辆信息以提升性能
     * 将大批量VIN分成小批次并发查询，避免单次请求过大导致超时
     */
    private List<VehicleInfoDO> queryVehicleInfoInBatches(List<String> vinList) {
        if (CollectionUtils.isEmpty(vinList)) {
            return Collections.emptyList();
        }

        // 如果VIN数量较少，直接查询
        if (vinList.size() <= DEFAULT_BATCH_SIZE) {
            return vehicleInfoRepository.queryByVinList(vinList);
        }
       

        // 将VIN列表分批
        List<List<String>> batches = partitionList(vinList, DEFAULT_BATCH_SIZE);
        // 使用ParallelExecutor并发处理各批次
        List<List<VehicleInfoDO>> batchResults = ParallelExecutor.executeParallelTasksAndGetResult(
                "vehicle_runtime_info",
                batches,
                batch -> {
                    try {
                        long batchStartTime = System.currentTimeMillis();
                        List<VehicleInfoDO> batchResult = vehicleInfoRepository.queryByVinList(batch);
                        long batchTime = System.currentTimeMillis() - batchStartTime;
                        log.info("批次查询完成，VIN数量: {}, 耗时: {}ms, 查询到: {}条",
                                batch.size(), batchTime, batchResult.size());
                        return batchResult;
                    } catch (Exception e) {
                        log.error("批次查询失败，VIN数量: {}, 错误: {}", batch.size(), e.getMessage(), e);
                        return Collections.<VehicleInfoDO>emptyList();
                    }
                }
        );

        // 合并所有批次的结果
        List<VehicleInfoDO> allResults = new ArrayList<>();
        for (List<VehicleInfoDO> batchResult : batchResults) {
            if (batchResult != null) {
                allResults.addAll(batchResult);
            }
        }
        log.info("分批查询完成，总共查询到{}条车辆信息", allResults.size());
        return allResults;
    }

    /**
     * 将列表分割成指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(new ArrayList<>(list.subList(i, end)));
        }
        return batches;
    }


}