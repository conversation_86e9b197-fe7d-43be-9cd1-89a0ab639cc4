package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.mtrace.Tracer;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseNotifyDetectService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;

/**
 *
 */
@Slf4j
@CraneConfiguration
public class RiskCaseCalcCrane {

    @Resource
    private RiskCaseNotifyDetectService riskCaseNotifyDetectService;


    @Before
    public void init() {
        Tracer.init();
    }
    
    /**
     * 计算
     *
     * @throws Exception
     */
    @Crane(CraneConstant.CALC_RISK_CASE_CRANE)
    @OperateEnter(OperateEnterActionEnum.RISK_CASE_CALC_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("风险统计任务开始执行");
        try {
            riskCaseNotifyDetectService.calcAndNotifyRiskCase();
        } catch (Exception e) {
            log.error("定时检查并播报待播报的安全风险事件定时任务异常", e);
        }
    }
}
