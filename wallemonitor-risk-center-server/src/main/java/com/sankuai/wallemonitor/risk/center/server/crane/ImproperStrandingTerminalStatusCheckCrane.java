package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskStrandingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskStrandingRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Sets;

@Slf4j
@CraneConfiguration
public class ImproperStrandingTerminalStatusCheckCrane {

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskStrandingRecordRepository riskStrandingRecordRepository;

    @Resource
    private LockUtils lockUtils;

    @Crane(CraneConstant.IMPROPER_STRANDING_TERMINAL_STATUS_CHECK_CRANE)
    @OperateEnter(OperateEnterActionEnum.CHECK_RISK_CASE_STAGNATION_STATUS_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        // 1 查询X分钟内未处置的来自状态监控的停滞不当风险事件
        RiskCaseDOQueryParamDTO riskCaseDOQueryParamDTO = RiskCaseDOQueryParamDTO.builder()
                // 状态为未完成
                .statusList(RiskCaseStatusEnum.getUnTerminal())
                // 来源为状态监控/烽火台
                .sourceList(Arrays.asList(RiskCaseSourceEnum.BEACON_TOWER.getCode()))
                // 停滞不当事件
                .caseTypeList(Arrays.asList(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode()))
                // todo: 创建时间在最近 60 分钟内
                .createTimeCreateTo(
                        DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, 60))
                .build();
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(riskCaseDOQueryParamDTO);
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            log.info("ImproperStrandingTerminalStatusCheckCrane, riskCaseDOList is empty");
            return;
        }
        // 2、 查询停滞检测记录表中对应的case
        // todo: 停滞不当时间的 eventId 对应停滞事件的 caseId, 对应检测记录表的 tmpCaseId
        RiskStrandingRecordDOQueryParamDTO recordDOQueryParamDTO = RiskStrandingRecordDOQueryParamDTO.builder()
                .tmpCaseIdList(riskCaseDOList.stream().map(RiskCaseDO::getEventId).collect(Collectors.toList()))
                .build();
        List<RiskStrandingRecordDO> riskStrandingRecordDOList = riskStrandingRecordRepository.queryByParam(
                recordDOQueryParamDTO);
        if (CollectionUtils.isEmpty(riskStrandingRecordDOList)) {
            log.error("ImproperStrandingTerminalStatusCheckCrane, riskStrandingRecordDOList is empty");
            return;
        }
        Map<String, RiskStrandingRecordDO> riskStrandingRecordDOMap = riskStrandingRecordDOList.stream()
                .collect(Collectors.toMap(RiskStrandingRecordDO::getTmpCaseId, Function.identity()));

        // 3、 停滞不当事件状态更新
        for (RiskCaseDO riskCaseDO : riskCaseDOList) {
            try {
                //  检查风险事件的停滞不当事件是否存在于风险事件的停滞检测记录表中
                if (!riskStrandingRecordDOMap.containsKey(riskCaseDO.getEventId())) {
                    throw new IllegalArgumentException("riskStrandingRecordDOMap not contains key");
                }
                // 对风险事件的 eventId 进行加锁
                lockUtils.batchLockCanWait(
                        LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(riskCaseDO.getEventId())),
                        () -> checkRiskCaseCancelStatus(riskCaseDO,
                                riskStrandingRecordDOMap.get(riskCaseDO.getEventId())));
            } catch (Exception e) {
                log.error("ImproperStrandingTerminalStatusCheckCrane, riskCaseDO:{}", riskCaseDO, e);
            }

        }

    }

    /**
     * 检查风险事件的取消状态
     *
     * @param riskCaseDO
     * @param riskStrandingRecordDO
     */
    private void checkRiskCaseCancelStatus(RiskCaseDO riskCaseDO, RiskStrandingRecordDO riskStrandingRecordDO) {
        // 1 重新查询两者的状态
        // 如果风险事件的状态变更未已取消，则直接返回
        RiskCaseDO riskCaseDONew = riskCaseRepository.getByCaseId(riskCaseDO.getCaseId());
        if (RiskCaseStatusEnum.isTerminal(riskCaseDONew.getStatus())) {
            return;
        }
        // 如果停滞检测记录的状态是未取消，则直接返回
        RiskStrandingRecordDO riskStrandingRecordDONew = riskStrandingRecordRepository.getByTmpCaseId(
                riskStrandingRecordDO.getTmpCaseId());
        if (!DetectRecordStatusEnum.isCancel(riskStrandingRecordDONew.getStatus())) {
            return;
        }

        // 此时停滞不当事件和停滞检测记录状态不一致,需要更新停滞事件的状态
        RiskCaseUpdatedParamDTO riskCaseUpdatedParamDTO = RiskCaseUpdatedParamDTO.builder()
                .eventId(riskCaseDONew.getEventId())
                .caseId(riskCaseDONew.getCaseId())
                .vinList(Lists.newArrayList(riskStrandingRecordDO.getVin()))
                .source(riskCaseDONew.getSource())
                .type(riskCaseDONew.getType())
                .status(RiskCaseStatusEnum.DISPOSED)
                .timestamp(riskStrandingRecordDO.getCloseTime().getTime())
                .recallTime(Optional.ofNullable(riskStrandingRecordDO.getRecallTime()).map(Date::getTime).orElse(null))
                .build();
        riskCaseOperateService.createOrUpdateRiskCase(riskCaseUpdatedParamDTO);
    }
}
