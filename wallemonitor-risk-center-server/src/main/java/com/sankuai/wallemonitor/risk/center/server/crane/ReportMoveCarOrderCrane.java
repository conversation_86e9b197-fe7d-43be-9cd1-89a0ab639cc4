package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.domain.enums.MessageType;
import com.sankuai.walleeve.domain.message.dto.CloudTriageEventMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.PositionConvert;
import com.sankuai.wallemonitor.risk.center.infra.dto.MoveCarEventConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallSafetyEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseMoveCarRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseMoveCarRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseMoveCarRelationDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeInfoContextDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@CraneConfiguration
public class ReportMoveCarOrderCrane {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private PositionConvert positionConvert;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCaseMoveCarRelationRepository riskCaseMoveCarRelationRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @MessageProducer(topic = MessageTopicEnum.CLOUD_TRIAGE_EVENT_MESSAGE)
    private CommonMessageProducer<CloudTriageEventMessageDTO> cloudTriageMessageProducer;

    @ConfigValue(key = LionKeyConstant.LION_KEY_MOVE_CAR_EVENT_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private MoveCarEventConfigDTO moveCarEventConfigDTO;

    @ConfigValue(key = LionKeyConstant.SEND_CLOUD_TRIAGE_GRAY_CONFIG)
    private Map<Integer, Set<String>> carPurposeGrayConfig;


    @Crane(CraneConstant.REPORT_MOVE_CAR_ORDER_CRANE)
    @OperateEnter(OperateEnterActionEnum.REPORT_MOVE_CAR_ORDER)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("上报云分诊挪车工单定时任务");
        try {
            //  1 查询所有已创建未处置的挪车事件
            List<RiskCaseDO> riskCaseDOList = queryUnDisposedMoveCarEvent();
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                log.info("riskCaseDOList is empty, ReportMoveCarOrderCrane end");
                return;
            }
            // 2 定时取消超时未处置的扫码挪车事件
            List<RiskCaseDO> needDisposedRiskCaseDOList = cancelTimeoutUnDisposedMoveCarEvent(riskCaseDOList);
            if (CollectionUtils.isEmpty(needDisposedRiskCaseDOList)) {
                log.info("needDisposedRiskCaseDOList is empty, ReportMoveCarOrderCrane end");
                return;
            }

            //  查询风险事件的车辆相关信息
            RiderCaseVehicleRelationDOParamDTO paramDTO = new RiderCaseVehicleRelationDOParamDTO();
            paramDTO.setCaseIdList(
                    needDisposedRiskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList()));
            List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                    paramDTO);
            if (CollectionUtils.isEmpty(riskCaseVehicleRelationDOList)) {
                log.error("查询风险事件相关信息错误", new IllegalArgumentException("未查询到车辆信息"));
                return;
            }
            //  2 过滤需要上报的扫码挪车事件
            List<RiskCaseVehicleRelationDO> filterRiskCaseVehicleRelationList = getNeedToReportRiskCaseInfo(
                    riskCaseVehicleRelationDOList);

            if (CollectionUtils.isEmpty(filterRiskCaseVehicleRelationList)) {
                return;
            }

            //  3 发送工单
            reportMoveCarOrder(filterRiskCaseVehicleRelationList);
        } catch (Exception e) {
            log.error("ReportMoveCarOrderCrane is error", e);
        }
    }

    /**
     * 查询未处置的挪车事件
     *
     * @return
     */
    private List<RiskCaseDO> queryUnDisposedMoveCarEvent() {
        RiskCaseDOQueryParamDTO paramDTO = new RiskCaseDOQueryParamDTO();
        // 设置类型 -  扫码挪车
        paramDTO.setType(RiskCaseTypeEnum.MOVE_CAR_EVENT.getCode());
        // 设置数据源 - 小程序
        paramDTO.setSource(RiskCaseSourceEnum.APPLET_OF_WECHAT.getCode());
        // 设置状态 - 未处置
        paramDTO.setStatusList(Arrays.asList(RiskCaseStatusEnum.NO_DISPOSAL.getCode()));
        // 设置创建时间范围 - min
        paramDTO.setCreateTimeCreateTo(DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES,
                moveCarEventConfigDTO.getCraneQueryValidDurationMin()));
        // 设置上报状态 - 未上报
        paramDTO.setCallSafety(CallSafetyEnum.NOT_CALLED.getCode());
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(paramDTO);
        log.info("ReportMoveCarOrderCrane# queryUnDisposedMoveCarEvent，riskCaseDOList = {}", riskCaseDOList);
        return riskCaseDOList;
    }

    /**
     * 获取需要上报的工单信息
     *
     * @param riskCaseVehicleRelationDOList
     * @return
     */
    private List<RiskCaseVehicleRelationDO> getNeedToReportRiskCaseInfo(
            List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList) {
        List<String> vinList = riskCaseVehicleRelationDOList.stream().map(RiskCaseVehicleRelationDO::getVin)
                .collect(Collectors.toList());
        // 1 查询车辆实时状态
        List<VehicleEveInfoVTO> vehicleEveInfoVTOList = vehicleAdapter.queryRuntimeVehicleInfo(
                VehicleRuntimeInfoParamVTO.builder().vinList(vinList).build());
        Map<String, VehicleEveInfoVTO> vehicleEveInfoVTOMap = vehicleEveInfoVTOList.stream()
                .collect(Collectors.toMap(VehicleEveInfoVTO::getVin, Function.identity()));

        // 2 查询车辆上下文信息
        List<VehicleRuntimeInfoContextDO> realContextDOList = vehicleRuntimeInfoContextRepository.queryByParam(
                VehicleRuntimeInfoContextDOQueryParamDTO.builder().vinList(vinList).build());
        Map<String, VehicleRuntimeInfoContextDO> realContextDOMap = realContextDOList.stream()
                .collect(Collectors.toMap(VehicleRuntimeInfoContextDO::getVin, Function.identity()));
        // 3 策略过滤
        List<RiskCaseVehicleRelationDO> filterRiskCaseList = riskCaseVehicleRelationDOList.stream()
                .filter(riskCaseVehicleRelationDO -> {
                    String vin = riskCaseVehicleRelationDO.getVin();
                    if (!vehicleEveInfoVTOMap.containsKey(vin)) {
                        log.error(String.format("未查询到车辆信息，vin = %s", vin),
                                new IllegalArgumentException("未查询到车辆信息"));
                        return false;
                    }
                    VehicleEveInfoVTO vehicleEveInfoVTO = vehicleEveInfoVTOMap.get(vin);

                    SafetyAreaDO safetyAreaDO = safetyAreaRepository.querySafetyAreaDOByPosition(positionConvert.toPositionDO(vehicleEveInfoVTO.getPosition(),
                            CoordinateSystemEnum.GCJ02));

                    // 构建车辆状态
                    Map<String, Object> context = new HashMap<>();
                    context.put("vin", vin);
                    // 实时速度
                    context.put("speed", vehicleEveInfoVTO.getSpeed());
                    // 计算是否在停车区域
                    context.put("inParkingArea", safetyAreaDO != null);
                    // 如果找到了停车区域，传入areaType
                    Optional.ofNullable(safetyAreaDO).ifPresent(x -> context.put("areaType", x.getType()));
                    // 计算停滞时长
                    VehicleCounterInfoDO counterInfoDO = Optional.ofNullable(realContextDOMap.get(vin))
                            .map(VehicleRuntimeInfoContextDO::getStagnationCounter)
                            .orElse(null);
                    Integer stagnateDuration = 0;
                    if (counterInfoDO != null && counterInfoDO.getDuration() != null) {
                        stagnateDuration = counterInfoDO.getDuration();
                    }
                    context.put("stagnateDuration", stagnateDuration);

                    log.info("getNeedToReportRiskCaseInfo, context:{}", context);

                    // 规则校验
                    return !isFilter(moveCarEventConfigDTO.getFilters(), context);

                }).filter(riskCaseVehicleRelationDO -> {
                    Integer type = Optional.ofNullable(riskCaseVehicleRelationDO.getType())
                            .map(RiskCaseTypeEnum::getCode).orElse(-1);
                    String curPurpose = Optional.ofNullable(vehicleEveInfoVTOMap.get(riskCaseVehicleRelationDO.getVin()))
                            .map(VehicleEveInfoVTO::getPurpose).orElse(null);
                    return Optional.ofNullable(carPurposeGrayConfig.get(type))
                            // 命中 curPurpose  或者 curPurpose 配置为ALL，拦截, 返回false
                            .map(curPurposeSet -> {
                                boolean isHit = curPurposeSet.contains(curPurpose) || curPurposeSet.contains("ALL");
                                if (isHit) {
                                    log.info("命中灰度配置拦截，vin: {}, type: {}, curPurpose: {}, grayConfig: {}", 
                                            riskCaseVehicleRelationDO.getVin(), type, curPurpose, curPurposeSet);
                                }
                                return !isHit;
                            })
                            // type未找到配置，不拦截
                            .orElse(true);
                }).collect(Collectors.toList());

        return filterRiskCaseList;
    }

    /**
     * 上报工单
     *
     * @param riskCaseVehicleRelationDOList
     */
    private void reportMoveCarOrder(List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList) {
        Map<String, String> caseIdVinMap = riskCaseVehicleRelationDOList.stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, RiskCaseVehicleRelationDO::getVin));

        RiskCaseMoveCarRelationDOQueryParamDTO paramDTO = new RiskCaseMoveCarRelationDOQueryParamDTO();
        paramDTO.setCaseIdList(riskCaseVehicleRelationDOList.stream().map(RiskCaseVehicleRelationDO::getCaseId)
                .collect(Collectors.toList()));
        List<RiskCaseMoveCarRelationDO> riskCaseMoveCarRelationDOList = riskCaseMoveCarRelationRepository.queryByParam(
                paramDTO);
        riskCaseMoveCarRelationDOList.forEach(moveCarRelationDO -> {
            CloudTriageEventMessageDTO messageDTO = new CloudTriageEventMessageDTO();
            messageDTO.setEventId(moveCarRelationDO.getEventId());
            messageDTO.setVin(caseIdVinMap.getOrDefault(moveCarRelationDO.getCaseId(), ""));
            messageDTO.setReporter("外部人员");
            messageDTO.setEventType(CommonConstant.PUBLIC_REMOVAL_EVENT_CODE);
            messageDTO.setEventTime(moveCarRelationDO.getCreateTime());
            messageDTO.setExtInfo(moveCarRelationDO.getExtInfo());

            // 发送消息
            String result = cloudTriageMessageProducer.sendMqCommonMessage(messageDTO, MessageType.CLOUD_TRIAGE_EVENT);
            if (StringUtils.isBlank(result)) {
                return;
            }

            // 上锁更新risk_case表中的上报云安全字段
            updateRiskCaseReportedCloudSecurity(moveCarRelationDO.getEventId());
        });

    }

    /**
     * 判断是否过滤
     *
     * @param filters
     * @param context
     * @return
     */
    private boolean isFilter(List<String> filters, Map<String, Object> context) {
        if (CollectionUtils.isEmpty(filters)) {
            return false;
        }
        // 不满足准入条件
        String filterBy = filters.stream()
                //做逻辑判断
                .filter(filter -> SpELUtil.evaluateBoolean(filter, context)).findFirst()
                //过滤
                .orElse(CharConstant.CHAR_EMPTY);

        return StringUtils.isNotBlank(filterBy);
    }

    /**
     * 定时取消超时未处置的扫码挪车事件
     *
     * @param riskCaseDOList
     */
    private List<RiskCaseDO> cancelTimeoutUnDisposedMoveCarEvent(List<RiskCaseDO> riskCaseDOList) {
        long currentTimeMillis = System.currentTimeMillis();
        long timeoutDurationMillis = moveCarEventConfigDTO.getCancelTimeoutUnDisposedDurationSec() * 1000L;

        return riskCaseDOList.stream().peek(riskCaseDO -> {
            if (currentTimeMillis - riskCaseDO.getOccurTime().getTime() > timeoutDurationMillis) {
                //  更新风险事件的状态为取消,同时设置关闭时间
                riskCaseDO.updateStatus(RiskCaseStatusEnum.CANCEL, currentTimeMillis);
                riskCaseRepository.save(riskCaseDO);
            }
        }).filter(riskCaseDO -> riskCaseDO.getStatus() != RiskCaseStatusEnum.CANCEL).collect(Collectors.toList());
    }

    /**
     * 更新risk_case表中的上报云安全字段
     *
     * @param eventId
     */
    private void updateRiskCaseReportedCloudSecurity(String eventId) {
        try {
            lockUtils.batchLockNoWait(
                    //对eventId进行加锁
                    LockKeyPreUtil.buildEventIdAndVin(Collections.singleton(eventId),
                            new HashSet<>()),
                    () -> {
                        RiskCaseDO thisRiskCaseDo = riskCaseRepository.getByEventId(eventId);
                        if (Objects.isNull(thisRiskCaseDo)) {
                            return;
                        }
                        // 更新呼叫云安全状态为呼叫中
                        thisRiskCaseDo.setCallSafety(CallSafetyEnum.CALLED);
                        riskCaseRepository.save(thisRiskCaseDo);
                    });
        } catch (Exception e) {
            log.error("updateRiskCaseReportedCloudSecurity is error", e);
        }
    }
}
