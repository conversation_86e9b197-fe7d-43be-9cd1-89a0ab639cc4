package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCheckingQueueService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseMessageDTOConvert.VehicleEventDataMessageExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
@Slf4j
public class RiskCaseEventConsumer {

    /**
     * 风险事件处理服务
     */
    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    /**
     * id生成器
     */
    @Resource
    public IDGenerateRepository idGenerateRepository;

    /**
     * 风险预检队列处理服务
     */
    @Resource
    private RiskCheckingQueueService riskCheckingQueueService;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private VehicleInfoRepository vehicleInfoRepository;

    private static int OCCUR_TIME_AMEND_MILLIS = 60 * 1000;


    // 1. 消费者接收消息
    @MafkaConsumer(
            namespace = "waimai", topic = "wallemonitor.risk.event.message",
            group = "wallemonitor.risk.event.message.consumer",
            deadLetter = true,
            deadLetterDelayMills = 6 * 1000
    )
    @OperateEnter(OperateEnterActionEnum.RISK_EVENT_CONSUMER_ENTER)
    public ConsumeStatus receive(String msg) {
        try {
            //解析消息，
            EveMqCommonMessage<RiskCaseMessageDTO> message = JacksonUtils.from(msg,
                    new TypeReference<EveMqCommonMessage<RiskCaseMessageDTO>>() {
                    });
            if (message.getBody() == null || StringUtils.isBlank(message.getBody().getEventId())) {
                log.warn("消费风险事件消息为空", new SystemException("消费风险事件消息为空"));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            RiskCaseTypeEnum typeEnum = RiskCaseTypeEnum.findByValue(message.getBody().getType());
            RiskCaseSourceEnum sourceEnum = RiskCaseSourceEnum.findByValue(message.getBody().getSource());
            RiskCaseStatusEnum statusEnum = RiskCaseStatusEnum.findByValue(message.getBody().getStatus());
            // 对于不关注的异常进行过滤
            if (Objects.isNull(typeEnum) || Objects.isNull(sourceEnum)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if (Objects.equals(statusEnum, RiskCaseStatusEnum.NO_DISPOSAL)
                    && Objects.equals(typeEnum, RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                    && Objects.equals(sourceEnum, RiskCaseSourceEnum.STATUS_MONITOR)
                    && lionConfigRepository.isImproperStrandingCaseTransformSwitchOn()) {
                log.info("未在处理范围内，过滤, status: {}, type: {}, source: {}, transformSwitch: {}", statusEnum,
                        typeEnum, statusEnum, lionConfigRepository.isImproperStrandingCaseTransformSwitchOn());
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 1.1 判断是否需要进入预检队列
            //根据车辆的VHR模式和配置开关决定处理方式
            //如果是VHR>1的车辆且开启了预检队列，走停滞不当处理流程
            //否则走普通风险事件处理流程
            Boolean enableCheckingQueue = lionConfigRepository.enableImproperStrandingCheckingQueue();
            switch (sourceEnum) {
                case STATUS_MONITOR:
                    // 1.2 获取vin列表
                    List<String> vinList = Optional.ofNullable(message.getBody().getVinList())
                            .orElse(new ArrayList<>());
                    if (vinList.isEmpty()) {
                        log.warn("消费风险事件消息vin为空", new SystemException("消费风险事件消息vin为空"));
                        return ConsumeStatus.CONSUME_SUCCESS;
                    }
                    // 1.3 获取vin对应的车辆信息
                    String vin = vinList.get(0);
                    VehicleInfoDO vehicleInfoDO = vehicleInfoRepository.getByVin(vin);
                    VHRModeEnum vhrModeEnum = Optional.ofNullable(vehicleInfoDO).map(VehicleInfoDO::getVhr)
                            .orElse(null);
                    
                    // 2. 判断是否为停滞不当事件
                    if (VHRModeEnum.VHR_GREAT_THAN_ONE.equals(vhrModeEnum) && enableCheckingQueue) {
                        return handleImproperStrandingMessage(message);
                    }
                default: {
                    // 2. 处理其他事件
                    return handleReceive(message);
                }
            }
        } catch (UnableGetLockException e) {
            log.warn("消费风险事件消息锁冲突失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        } catch (ParamInputErrorException e) {
            log.warn("消费风险事件消息业务正常失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        } catch (Throwable e) {
            log.error("消费风险事件消息异常失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        }
    }

    /**
     * 处理状态监控召回的停滞不当事件
     *
     * @param message
     * @return
     */
    private ConsumeStatus handleImproperStrandingMessage(EveMqCommonMessage<RiskCaseMessageDTO> message) {
        // 1.1 判断是否为结束消息
        boolean isEndMessage = RiskCaseStatusEnum.isTerminal(
                RiskCaseStatusEnum.findByValue(message.getBody().getStatus()));

        // 1.2 获取vin列表
        Set<String> vinSet = new HashSet<>(
                Optional.ofNullable(message.getBody().getVinList()).orElse(new ArrayList<>()));
        if (CollectionUtils.isEmpty(vinSet)) {
            log.warn("消费风险事件消息失败，vinList为空", new Exception());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 1.3 获取事件id集合
        Set<String> eventIdSet = Sets.newHashSet(message.getBody().getEventId());

        // 1.4 获取风险事件类型
        RiskCaseTypeEnum typeEnum = RiskCaseTypeEnum.findByValue(message.getBody().getType());
        RiskCaseSourceEnum sourceEnum = RiskCaseSourceEnum.findByValue(message.getBody().getSource());
        VehicleEventDataMessageExtInfoDTO extInfoDTO = null;
        if (StringUtils.isNotBlank(message.getBody().getExtInfo())) {
            extInfoDTO = JacksonUtils.from(message.getBody().getExtInfo(),
                    VehicleEventDataMessageExtInfoDTO.class);
        }

        // 1.5 根据 事件Id集合 和 车辆vin号集合 加锁
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID,
                Collections.singletonList(message.getBody().getVinList().get(0)), sourceEnum, typeEnum,
                message.getTimestamp());
        VehicleEventDataMessageExtInfoDTO finalExtInfoDTO = extInfoDTO;
        // 1.6 加锁 进行批量处理
        lockUtils.batchLockCanWait(LockKeyPreUtil.buildEventIdAndVinAndType(eventIdSet, vinSet),
                1,
                TimeUnit.SECONDS,
                () -> {
                    //如果 消息类型属于结束消息 并且 开始消息不存在 则判断是否要重试
                    // TODO 考虑预检队列查是后转为正式case存表前收到解除的信号
                    RiskCheckingQueueItemDO queueItemDO = riskCheckingQueueService.getCheckingItemByEventId(
                            message.getBody().getEventId());
                    if (isEndMessage && queueItemDO == null) {
                        // 未超出lion配置重试机会，则抛出异常，会重试
                        CheckUtil.isNotTrue(checkEndBeforeStartCanRetry(message), "该风险事件结束消息需要重试");
                        return;  // 超出重试机会，停止消费
                    } else if (isEndMessage && (RiskQueueStatusEnum.isConfirmed(queueItemDO.getStatus()))
                            && !riskCaseOperateService.checkStartMessageExist(message.getBody().getEventId())) {
                        CheckUtil.isNotTrue(checkEndBeforeStartCanRetry(message), "该风险事件结束消息需要重试");
                        return;  // 超出重试机会，停止消费
                    }

                    RiskCaseStatusEnum riskCaseStatusEnum = RiskCaseStatusEnum.findByValue(message.getBody().getStatus());
                    RiskCaseTypeEnum riskCaseTypeEnum = RiskCaseTypeEnum.findByValue(message.getBody().getType());
                    long timestamp = message.getTimestamp();
                    long occurTime = timestamp;
                    long recallTime = timestamp;
                    if (Objects.equals(riskCaseStatusEnum, RiskCaseStatusEnum.NO_DISPOSAL)
                            && Objects.equals(riskCaseTypeEnum, RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                            && lionConfigRepository.isRiskOccurTimeAmendSwitchOn()) {
                        occurTime -= OCCUR_TIME_AMEND_MILLIS;
                    }

                    RiskCaseUpdatedParamDTO riskParamDTO = RiskCaseUpdatedParamDTO.builder()
                            .eventId(message.getBody().getEventId())
                            .source(RiskCaseSourceEnum.findByValue(message.getBody().getSource()))
                            .status(riskCaseStatusEnum)
                            .type(riskCaseTypeEnum)
                            .timestamp(occurTime)
                            .recallTime(recallTime)
                            .vinList(message.getBody().getVinList())
                            .messageExtInfo(finalExtInfoDTO)
                            .caseId(caseId)
                            .build();
                    // 3. 预检队列处理
                    //对于某些特定类型的风险，会先进入预检队列：
                    handleImproperStrandingEvent(riskParamDTO);
                });
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消费
     *
     * @param message
     * @return
     */
    private ConsumeStatus handleReceive(EveMqCommonMessage<RiskCaseMessageDTO> message) {
        boolean isEndMessage = RiskCaseStatusEnum.isTerminal(
                RiskCaseStatusEnum.findByValue(message.getBody().getStatus()));

        //如果 消息类型属于结束消息 并且 开始消息不存在 则判断是否要重试
        if (isEndMessage && !riskCaseOperateService.checkStartMessageExist(message.getBody().getEventId())) {
            // 未超出lion配置重试机会，则抛出异常，会重试
            CheckUtil.isNotTrue(checkEndBeforeStartCanRetry(message), "该风险事件结束消息需要重试");
            // 超出重试机会，停止消费
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        Set<String> vinSet = new HashSet<>(
                Optional.ofNullable(message.getBody().getVinList()).orElse(new ArrayList<>()));
        Set<String> eventIdSet = Sets.newHashSet(message.getBody().getEventId());
        RiskCaseTypeEnum typeEnum = RiskCaseTypeEnum.findByValue(message.getBody().getType());
        VehicleEventDataMessageExtInfoDTO extInfoDTO = null;
        if (StringUtils.isNotBlank(message.getBody().getExtInfo())) {
            extInfoDTO = JacksonUtils.from(message.getBody().getExtInfo(),
                    VehicleEventDataMessageExtInfoDTO.class);
        }
        // 根据 事件Id集合 和 车辆vin号集合 加锁
        VehicleEventDataMessageExtInfoDTO finalExtInfoDTO = extInfoDTO;
        lockUtils.batchLockCanWait(LockKeyPreUtil.buildEventIdAndVinAndType(eventIdSet, vinSet),
                1,
                TimeUnit.SECONDS,
                () -> {
                    RiskCaseStatusEnum riskCaseStatusEnum = RiskCaseStatusEnum.findByValue(message.getBody().getStatus());
                    RiskCaseTypeEnum riskCaseTypeEnum = RiskCaseTypeEnum.findByValue(message.getBody().getType());
                    long timestamp = message.getTimestamp();
                    long occurTime = timestamp;
                    long recallTime = timestamp;
                    if (Objects.equals(riskCaseStatusEnum, RiskCaseStatusEnum.NO_DISPOSAL)
                            && Objects.equals(riskCaseTypeEnum, RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                            && lionConfigRepository.isRiskOccurTimeAmendSwitchOn()) {
                        occurTime -= OCCUR_TIME_AMEND_MILLIS;
                    }
                    RiskCaseUpdatedParamDTO riskParamDTO = RiskCaseUpdatedParamDTO.builder()
                            .eventId(message.getBody().getEventId())
                            .source(RiskCaseSourceEnum.findByValue(message.getBody().getSource()))
                            .status(riskCaseStatusEnum)
                            .type(riskCaseTypeEnum)
                            .timestamp(occurTime)
                            .recallTime(recallTime)
                            .vinList(message.getBody().getVinList())
                            .messageExtInfo(finalExtInfoDTO)
                            .build();
                    riskCaseOperateService.createOrUpdateRiskCase(riskParamDTO);
                });
        return ConsumeStatus.CONSUME_SUCCESS;
    }


    /**
     * 处理车辆停滞不当风险事件
     *
     * @param paramDTO
     * @return
     */
    public void handleImproperStrandingEvent(RiskCaseUpdatedParamDTO paramDTO) {
        // 开启预检队列 事件先通过预检队列进行校验
        log.info("lionConfigRepository.enableImproperStrandingCheckingQueueParallel: {}",
                lionConfigRepository.enableImproperStrandingCheckingQueueParallel());
        if (lionConfigRepository.enableImproperStrandingCheckingQueueParallel()) {  // 队列双跑，也要正常往正式表中插入
            riskCaseOperateService.createOrUpdateRiskCase(paramDTO);
        }

        if (paramDTO.getStatus() == RiskCaseStatusEnum.NO_DISPOSAL) {
            
            // 3.1 将事件加入预检队列
            riskCheckingQueueService.append(paramDTO);
        } else if (paramDTO.getStatus() == RiskCaseStatusEnum.DISPOSED) {
            riskCheckingQueueService.cancelItem(paramDTO);
            // 如果已经转成真实风险事件了  需要取消
            if (riskCaseOperateService.checkStartMessageExist(paramDTO.getEventId())) {
                riskCaseOperateService.createOrUpdateRiskCase(paramDTO);
            }
        } else {
            log.warn("handleImproperStrandingEvent unsupported status: {}", paramDTO.getStatus());
        }
    }

    /**
     * 并且判断 该消息是否可以重试，否则抛弃
     *
     * @param message
     * @return
     */
    private boolean checkEndBeforeStartCanRetry(EveMqCommonMessage<RiskCaseMessageDTO> message) {
        RiskCaseMessageDTO riskCaseMessageDTO = message.getBody();
        RiskCaseTypeEnum typeEnum = RiskCaseTypeEnum.findByValue(riskCaseMessageDTO.getType());
        BroadCastStrategyConfigDTO strategyConfig = lionConfigRepository.getByCaseType(typeEnum);
        // 没有对应的安全风险事件类型，舍弃
        if (strategyConfig == null) {
            return false;
        }
        // 判断是否可以重试
        return strategyConfig.canRetry(message.getTimestamp());
    }
}
