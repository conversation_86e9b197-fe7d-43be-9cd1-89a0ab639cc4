package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseMessageService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.xm.openplatform.callback.api.XmOpenCallbackServiceI;
import com.sankuai.xm.openplatform.common.entity.EmptyResp;

import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

/**
 * XmOpenCallbackServiceI接口实现类
 * 提供开放平台回调服务功能
 *
 * <AUTHOR>
 * @Date 2024/12/19
 */
@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class XmOpenCallbackServiceImpl implements XmOpenCallbackServiceI.Iface {

    /**
     * 处理开放平台事件回调
     *
     * @param eventType 事件类型
     * @param eventData 事件数据
     * @return 处理结果状态码
     * @throws TException Thrift异常
     */
    @Override
    @OperateEnter(OperateEnterActionEnum.GET_CASE_URL_DATA)
    public EmptyResp eventCallback(int eventType, String eventData) throws TException {
        log.info("收到开放平台事件回调：eventType={}, eventData={}", eventType, eventData);
        return new EmptyResp();
    }

} 