package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.commons.exception.JacksonException;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.VehicleEventCodeConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseMessageDTOConvert;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseMessageDTOConvert.VehicleEventDataMessageExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleEventDataDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
@Slf4j
public class VehicleDataEventConsumer {

    /**
     * 风险事件处理服务
     */
    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseMessageDTOConvert riskCaseMessageDTOConvert;

    @Resource
    private RiskCaseRepository riskCaseRepository;


    @Resource
    private RiskCaseVehicleRelationRepository vehicleRelationRepository;

    @Resource
    private VehicleInfoRepository vehicleInfoRepository;


    @Resource
    private LockUtils lockUtils;

    @MafkaConsumer(
            namespace = "waimai", topic = "walle.data.autodrive.event",
            group = "walle.data.autodrive.event.risk.consumer",
            deadLetter = true,
            deadLetterDelayMills = 6 * 1000
    )
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.VEHICLE_DATA_CONSUMER_ENTER)
    public ConsumeStatus receive(String msg) {
        try {
            if (StringUtils.isBlank(msg)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 解析消息
            VehicleEventDataDO vehicleEventDataDO = JacksonUtils.from(msg,
                    new TypeReference<VehicleEventDataDO>() {
                    });
            if (vehicleEventDataDO == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            String vin = vehicleEventDataDO.getVin();
            RiskCaseTypeEnum typeEnum = RiskCaseTypeEnum.findByEventCode(vehicleEventDataDO.getEventCode());
            if (StringUtils.isBlank(vin) || Objects.isNull(typeEnum)) {
                //车辆为空或者不支持
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            Set<String> keySet = LockKeyPreUtil.buildVinAndType(Sets.newHashSet(vin),
                    typeEnum);
            //等待
            return lockUtils.batchLockCanWait(keySet, 2, TimeUnit.SECONDS,
                    () -> handleReceiveMessage(vehicleEventDataDO));
        } catch (UnableGetLockException e) {
            log.warn("消费风险事件消息锁冲突失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        } catch (ParamInputErrorException e) {
            log.warn("消费风险事件消息业务正常失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        } catch (JacksonException e) {
            log.warn("车辆事件格式不符合预期", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Throwable e) {
            log.error("消费风险事件消息异常失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        }
    }

    /**
     * 处理车辆消息接受
     *
     * @param vehicleEventDataDO
     * @return
     */
    private ConsumeStatus handleReceiveMessage(VehicleEventDataDO vehicleEventDataDO) {

        // 检查是否是白名单内的event code
        List<Integer> caredEventCode = lionConfigRepository.getFocusedAutocarEventCodeList();
        if (!caredEventCode.contains(vehicleEventDataDO.getEventCode())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
//
        // 检查Autocar版本是否在黑名单中，若在，则忽略
        if (checkAutocarVersionIgnored(vehicleEventDataDO)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 做转换
        List<EveMqCommonMessage<RiskCaseMessageDTO>> mqCommonMessageList = riskCaseMessageDTOConvert.vehicleEventConvert(
                vehicleEventDataDO, riskCaseRepository, vehicleRelationRepository, vehicleInfoRepository);
        if (CollectionUtils.isEmpty(mqCommonMessageList)) {
            // 检查是否可以重试，如果可重试则抛出异常
            CheckUtil.isNotTrue(failButCanRetry(vehicleEventDataDO), "车辆事件可重试");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        for (EveMqCommonMessage<RiskCaseMessageDTO> mqCommonMessage : mqCommonMessageList) {
            RiskCaseMessageDTO riskCaseMessageDTO = Optional.ofNullable(mqCommonMessage)
                    .map(EveMqCommonMessage::getBody)
                    .orElse(null);
            if (riskCaseMessageDTO == null || CollectionUtils.isEmpty(riskCaseMessageDTO.getVinList())) {
                //消息结构异常，无需处理
                continue;
            }
            VehicleEventDataMessageExtInfoDTO extInfoDTO = JacksonUtils
                    .from(riskCaseMessageDTO.getExtInfo(), VehicleEventDataMessageExtInfoDTO.class);
            Set<String> vinSet = new HashSet<>(
                    Optional.ofNullable(riskCaseMessageDTO.getVinList()).orElse(new ArrayList<>()));
            Set<String> eventIdSet = Sets.newHashSet(riskCaseMessageDTO.getEventId());
            lockUtils.batchLockCanWait(
                    LockKeyPreUtil.buildEventIdAndVin(eventIdSet, vinSet),
                    1,
                    TimeUnit.SECONDS,
                    () -> riskCaseOperateService.createOrUpdateRiskCase(RiskCaseUpdatedParamDTO.builder()
                            .eventId(riskCaseMessageDTO.getEventId())
                            .source(RiskCaseSourceEnum.findByValue(riskCaseMessageDTO.getSource()))
                            .status(RiskCaseStatusEnum.findByValue(riskCaseMessageDTO.getStatus()))
                            .type(RiskCaseTypeEnum.findByValue(riskCaseMessageDTO.getType()))
                            // .traceId(riskCaseMessageDTO.getTraceId())
                            .timestamp(mqCommonMessage.getTimestamp())
                            .recallTime(mqCommonMessage.getTimestamp())
                            .vinList(riskCaseMessageDTO.getVinList())
                            .messageExtInfo(extInfoDTO)
                            .sideBySideTimestamp(extInfoDTO != null ? extInfoDTO.getSideBySideStartTime() : null)
                            .build()));
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 判断失败后是否可以重试
     *
     * @param vehicleEventDataDO
     * @return
     */
    private boolean failButCanRetry(VehicleEventDataDO vehicleEventDataDO) {
        RiskCaseTypeEnum riskCaseTypeEnum = RiskCaseTypeEnum.findByEventCode(vehicleEventDataDO.getEventCode());
        BroadCastStrategyConfigDTO strategyConfig = lionConfigRepository.getByCaseType(riskCaseTypeEnum);
        // 没有对应的安全风险事件类型，舍弃
        if (strategyConfig == null
                || vehicleEventDataDO.getEventCode() != VehicleEventCodeConstant.SIDE_BY_SIDE_END
                && vehicleEventDataDO.getEventCode() != VehicleEventCodeConstant.TRAFFIC_JAM_END
                && vehicleEventDataDO.getEventCode() != VehicleEventCodeConstant.STAND_STILL_END) {
            //不存在配置的时候，默认为false ，或者
            return Boolean.FALSE;
        }
        //
        return strategyConfig.canRetry(vehicleEventDataDO.getEventTimestamp());

    }

    /**
     * 检查事件来源的车辆autocar版本是否在黑名单中，若在黑名单中，则忽略该事件
     *
     * @param vehicleEvent
     * @return
     */
    private Boolean checkAutocarVersionIgnored(VehicleEventDataDO vehicleEvent) {
        RiskCaseTypeEnum riskCaseTypeEnum = RiskCaseTypeEnum.findByEventCode(vehicleEvent.getEventCode());
        BroadCastStrategyConfigDTO strategyConfig = lionConfigRepository.getByCaseType(riskCaseTypeEnum);
        // 没有对应的安全风险事件类型，舍弃
        if (strategyConfig == null) {
            return Boolean.TRUE;
        }

        VehicleInfoDO vehicleInfo = vehicleInfoRepository.getByVin(vehicleEvent.getVin());
        if (vehicleInfo == null || strategyConfig.isAutocarVersionInBlackList(vehicleInfo.getAutocarVersion())) {
            // 如果车辆信息为空或者Autocar版本在黑名单中，舍弃
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }
}
