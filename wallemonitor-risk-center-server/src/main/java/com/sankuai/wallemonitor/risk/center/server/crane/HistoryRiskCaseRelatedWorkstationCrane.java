package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.WorkstationAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RelatedServiceNameEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseRelatedServiceRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRelatedServiceRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RelatedWorkstationCraneConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeTemplateConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@CraneConfiguration
public class HistoryRiskCaseRelatedWorkstationCrane {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private WorkstationAdapter workstationAdapter;

    @Resource
    private RiskCaseRelatedServiceRecordRepository riskCaseRelatedServiceRecordRepository;


    @ConfigValue(key = LionKeyConstant.LION_RELATED_WORKSTATION_CRANE_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private RelatedWorkstationCraneConfigDTO relatedWorkstationCraneConfig;

    @Crane(CraneConstant.HISTORY_RISK_CASE_RELATED_WORKSTATION_CRANE)
    @OperateEnter(OperateEnterActionEnum.HISTORY_RISK_CASE_RELATED_WORKSTATION_ENTRY)
    @ZebraForceMaster
    public void run() throws Exception {

        // 配置检查
        if (Objects.isNull(relatedWorkstationCraneConfig)
                || StringUtils.isBlank(relatedWorkstationCraneConfig.getStartTime())
                || StringUtils.isBlank(relatedWorkstationCraneConfig.getEndTime())) {
            log.error("HistoryRiskCaseRelatedWorkstationCrane#run relatedWorkstationCraneConfig is null");
            return;
        }
        Date startTime = DatetimeUtil.parseDate(relatedWorkstationCraneConfig.getStartTime(),
                DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND);
        Date endTime = DatetimeUtil.parseDate(relatedWorkstationCraneConfig.getEndTime(),
                DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND);

        // 1 查询历史风险case (支持指定日期查询)
        RiskCaseDOQueryParamDTO paramDTO = RiskCaseDOQueryParamDTO.builder().build();
        paramDTO.setCreateTimeRange(TimePeriod.builder().beginDate(startTime).endDate(endTime).build());
        // 指定状态 - 已完成事件
        paramDTO.setStatusList(RiskCaseStatusEnum.getTerminal());
        // 指定数据源
        paramDTO.setSourceList(RiskCaseSourceEnum.getSelfSourceCode());
        // 停滞不当
        paramDTO.setType(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode());

        // 关联分拣
        paramDTO.setLeftJoinSort(true);
        // 指定问题列表
        paramDTO.setProblemList(relatedWorkstationCraneConfig.getProblemList());
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(paramDTO);

        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            log.info(
                    "startTime = {},endTime = {}, HistoryRiskCaseRelatedWorkstationCrane#run riskCaseDOList is empty",
                    startTime, endTime);
            return;
        }
        // 2 每个case进行关联事件
        List<String> failedCaseIdList = new ArrayList<>();
        riskCaseDOList.forEach(riskCaseDO -> {
            try {
                // 2.1 查询参数构建
                RiskCaseVehicleRelationDO relationDO = riskCaseVehicleRelationRepository.getByCaseId(
                        riskCaseDO.getCaseId());
                if (Objects.isNull(relationDO)) {
                    log.error("getByCaseId is error", new IllegalArgumentException("关联关系不存在"));
                    return;
                }
                // 2.2 查询工作台事件
                List<String> workstationCaseIdList = queryWorkstationCaseIdList(riskCaseDO, relationDO.getVin());
                if (CollectionUtils.isEmpty(workstationCaseIdList)) {
                    return;
                }
                // 2.3 构建关联事件
                List<RiskCaseRelatedServiceRecordDO> recordDOList = workstationCaseIdList.stream()
                        .map(caseId -> {
                            RiskCaseRelatedServiceRecordDO recordDO = new RiskCaseRelatedServiceRecordDO();
                            recordDO.setCaseId(riskCaseDO.getCaseId());
                            recordDO.setRelatedId(caseId);
                            recordDO.setServiceName(RelatedServiceNameEnum.WORKSTATION);
                            return recordDO;
                        })
                        .collect(Collectors.toList());
                riskCaseRelatedServiceRecordRepository.batchSave(recordDOList);
            } catch (Exception e) {
                log.error("HistoryRiskCaseRelatedWorkstationCrane#run error, riskCaseDO:{}", riskCaseDO, e);
                failedCaseIdList.add(riskCaseDO.getCaseId());
            }
        });
        log.info("HistoryRiskCaseRelatedWorkstationCrane#run failedCaseIdList:{}", failedCaseIdList);
    }

    /**
     * 查询指定vin在指定时间内的工作台事件ID列表
     *
     * @param riskCaseDO
     * @param vin
     * @return
     */
    private List<String> queryWorkstationCaseIdList(RiskCaseDO riskCaseDO, String vin) {
        // 开始时间
        Date startTime = riskCaseDO.getOccurTime();
        // 查询工作台
        return workstationAdapter.getWorkstationCaseId(vin,
                DatetimeUtil.formatTime(startTime), DatetimeUtil.formatTime(riskCaseDO.getCloseTime()));
    }

}
