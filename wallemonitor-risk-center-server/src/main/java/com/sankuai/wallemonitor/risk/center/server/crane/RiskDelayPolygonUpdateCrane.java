package com.sankuai.wallemonitor.risk.center.server.crane;


import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.LongWaitAreaConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CaseMarkCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.SafetyAreaInfoSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.SafetyAreaFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCaseExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SafetyAreaExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.SafetyAreaQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.TransactionUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.PolygonUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.OptionalDouble;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.wallemonitor.risk.center.infra.enums.MenderOperationTypeEnum.IMPROPER_STRANDING_DELAY_RECALL;

@Slf4j
@CraneConfiguration
@Component
public class RiskDelayPolygonUpdateCrane {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;


    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private TransactionUtils transactionUtils;

    @Crane(CraneConstant.DELAY_RECALL_AREA_CRANE)
    @OperateEnter(OperateEnterActionEnum.RISK_DELAY_POLYGON_UPDATE_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        LongWaitAreaConfigDTO longWaitAreaConfig = lionConfigRepository.getLongWaitAreaConfig();
        if(longWaitAreaConfig == null || !longWaitAreaConfig.getOpen() ) {
            return;
        }
        // 查询过去几天，持续时长大于180s的 case
        TimePeriod timePeriod = TimePeriod.builder()
                .endDate(new Date())
                .beginDate(DatetimeUtil.getBeforeTime(new Date(), TimeUnit.HOURS, longWaitAreaConfig.getPastDays() * 24))
                .build();
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder()
                        // 烽火台
                        .sourceList(Arrays.asList(RiskCaseSourceEnum.BEACON_TOWER.getCode()))
                        // 停滞不当事件
                        .caseTypeList(Arrays.asList(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode()))
                        .createTimeRange(timePeriod).durationGreatThan(longWaitAreaConfig.getSecondsLeast()).build());
        if(CollectionUtils.isEmpty(riskCaseDOList)) {
            log.warn("#RiskDelayPolygonUpdateCrane# risk case is empty , time period = {}", JacksonUtils.to(timePeriod));
            return;
        }

        // 过滤得到误召case
        CaseMarkInfoDOQueryParamDTO caseMarkInfoDOQueryParamDTO = CaseMarkInfoDOQueryParamDTO.builder().caseIdList(
                        riskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList()))
                .build();

        Set<String> badCaseList = caseMarkInfoRepository.queryByParam(caseMarkInfoDOQueryParamDTO).stream()
                .filter(x -> x.getCategory().equals(CaseMarkCategoryEnum.BAD_OTHER.getCategory())
                        && !CaseMarkCategoryEnum.IN_PARKING_AREA.getSubcategory().equals(x.getSubCategory())) // 不在停车场内的BAD case
                .map(CaseMarkInfoDO::getCaseId)
                .collect(Collectors.toSet());

        if(CollectionUtils.isEmpty(badCaseList)) {
            log.warn("#RiskDelayPolygonUpdateCrane# bad case is empty");
            return;
        }


        // 根据Poi进行聚类
        Map<String, List<RiskCaseDO>>  riskCaseGroupByPoi = riskCaseDOList.stream()
                .filter(Objects::nonNull)
                .filter(riskCase -> badCaseList.contains(riskCase.getCaseId()))
                .peek(riskCase -> {
                    // 未找到poiName ，从ext_info中查找
                    if (StringUtils.isEmpty(riskCase.getPoiName())) {
                        riskCase.setPoiName(
                                Optional.ofNullable(riskCase.getExtInfo())
                                        .map(RiskCaseExtInfoDO::getPoi).orElse(null)
                        );
                    }
                })
                .collect(Collectors.groupingBy(RiskCaseDO::getPoiName));

        log.info("poiRiskList = {}", riskCaseGroupByPoi);

        // 查询已有的SafetyArea
        List<SafetyAreaDO> currentSafetyArea = safetyAreaRepository.queryByParam(SafetyAreaQueryParamDTO.builder()
                .typeList(Lists.newArrayList(IMPROPER_STRANDING_DELAY_RECALL.getName()))
                .isDeleted(false).build());

        // SafetyArea 根据Poi进行聚类
        Map<String, List<SafetyAreaDO>> currentSafetyAreaGroupByPoi =
                currentSafetyArea.stream().collect(Collectors.groupingBy(SafetyAreaDO::getDescription));


        riskCaseGroupByPoi.forEach((poiName, riskCaseList) -> {
            if(currentSafetyAreaGroupByPoi.containsKey(poiName)) {
                // 如果PoiName已经存在 ，走更新逻辑
                doUpdatePolygonFromRiskCase(poiName, riskCaseList, currentSafetyAreaGroupByPoi, longWaitAreaConfig);
            } else {
                doCreatePolygonFromRiskCase(poiName, riskCaseList, longWaitAreaConfig);
            }
        });

    }

    // safety area 的写事务，用public修饰
    public void doUpdatePolygonFromRiskCase(String poiName,
                                           List<RiskCaseDO> riskList,
                                           Map<String, List<SafetyAreaDO>> currentSafetyAreaGroupByPoi,
                                           LongWaitAreaConfigDTO longWaitAreaConfigDTO) {
        log.info("poi = {}, 走更新逻辑", poiName);


        // 获取事件对应的poi 已有区域
        List<SafetyAreaDO> safetyAreaDOList = Optional.ofNullable(currentSafetyAreaGroupByPoi.get(poiName))
                .map(areaList -> areaList.stream()
                        .filter(area -> area.getPolygon() != null &&
                                CollectionUtils.isNotEmpty(area.getPolygon().getPointGcjList()))
                        .collect(Collectors.toList()))
                .orElse(null);

        if(safetyAreaDOList == null || safetyAreaDOList.size() == 0 ) {
            return ;
        }

        // 拿到事件对应的点列
        List<RiskCaseDO> riskCaseDOList = riskList.stream()
                .filter(Objects::nonNull)
                .filter(x -> x.getLocation() != null)
                .filter(riskCase -> safetyAreaDOList.stream().noneMatch(
                        safetyAreaDO -> safetyAreaDO.isInPolygon(riskCase.getLocation()))) // 过滤得到不在任何区域的点
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(riskCaseDOList)) {
            // 如果新的点都在老区域中，直接返回
            return;
        }

        // key : 区域safetyAreaDOList的下角标。value : 可合并的case
        Map<Integer, List<RiskCaseDO>> updatePositionRecord = new HashMap<>();
        List<RiskCaseDO> remainCases = Lists.newArrayList();

        for(RiskCaseDO riskCase : riskCaseDOList) {
            double minDistance = Double.MAX_VALUE;
            int minIndex = -1;
            PositionDO point = riskCase.getLocation();
            // travel该poi下的所有区域，正常情况只会有一个区域，但是如果新的点距离原本的区域比较远，且新的点足够多，也可能会造出多个区域
            for(int i = 0; i < safetyAreaDOList.size(); i ++) {
                SafetyAreaDO safetyAreaDO = safetyAreaDOList.get(i);
                List<PositionDO> pointGcjList = safetyAreaDO.getPoints();
                // 计算距离
                Double distance = GeoToolsUtil.pointToLineSegmentDistance(point, pointGcjList);
                if(distance == null) {
                    continue;
                }
                if(distance < minDistance) {
                    // 保存最小距离和对应区域的下标
                    minDistance = distance;
                    minIndex = i;
                }
            }
            if(minIndex == -1 || minDistance > longWaitAreaConfigDTO.getMinConcatDistance()) {
                // 这个点不能被添加的任何区域，保存到remainList中
                remainCases.add(riskCase);
            } else {
                // 如果这个点可以被加入到某个区域，添加到对应area 的点列中
                updatePositionRecord.putIfAbsent(minIndex, Lists.newArrayList());
                updatePositionRecord.get(minIndex).add(riskCase);
            }
        }


        // 先处理可以组成新区域的case

        // 删除原本的区域
        List<SafetyAreaDO> deletedAreas = updatePositionRecord.keySet().stream().map(safetyAreaDOList::get)
                .peek(x -> x.setIsDeleted(IsDeleteEnum.DELETED)).collect(Collectors.toList());

        // 在原本的区域的基础上重新计算区域
        List<SafetyAreaDO> insertAreas = updatePositionRecord.entrySet().stream().map(x -> {
            Integer index = x.getKey();
            List<RiskCaseDO> riskCaseList = x.getValue();
            // 获取旧区域
            SafetyAreaDO oldSafetyArea = safetyAreaDOList.get(index);

            // 合并新点和旧点
            List<PositionDO> newPoints = Stream.concat(riskCaseList.stream().map(RiskCaseDO::getLocation),
                    oldSafetyArea.getPoints().stream()).collect(Collectors.toList());

            // 合并新旧生效时间
            Set<Integer> newEffectHours = Stream.concat(riskCaseList.stream().map(RiskCaseDO::getOccurTime).filter(Objects::nonNull).map(Date::getHours),
                    oldSafetyArea.getExtInfo().getEffectHours().stream()).collect(Collectors.toSet());

            List<PositionDO> newPolygon = createPolygon(newPoints, longWaitAreaConfigDTO);

            return SafetyAreaFactory.createSafetyArea(newPolygon, newEffectHours, poiName);
        }).collect(Collectors.toList());

        // 开启事务
        transactionUtils.execute(() -> {
            safetyAreaRepository.batchSave(deletedAreas); // 删除
            safetyAreaRepository.batchSave(insertAreas);  // 新增
        });

        if(CollectionUtils.isNotEmpty(remainCases)) {
            // 处理剩余的case，足够多会自己创建新的区域
            doCreatePolygonFromRiskCase(poiName, remainCases, longWaitAreaConfigDTO);
        }



    }


    /**
     * 创建新的Polygon
     * */

    private void doCreatePolygonFromRiskCase(String poiName,
                                             List<RiskCaseDO> riskList,
                                             LongWaitAreaConfigDTO longWaitAreaConfigDTO) {
        // 过滤低频事件poi
        if(riskList.size() < longWaitAreaConfigDTO.getMinNumPointInPoi()) {
            return ;
        }

        // 拿到事件对应的点列
        List<PositionDO> positionDOList = riskList.stream()
                .filter(x -> x.getExtInfo() != null)
                .map(x -> x.getExtInfo().getPosition())
                .filter(Objects::nonNull).collect(Collectors.toList());
        // 计算得到Polygon
        List<PositionDO> polygon = createPolygon(positionDOList, longWaitAreaConfigDTO);
        if(CollectionUtils.isEmpty(polygon)) {
            return ;
        }
        // 计算得到生效时间，单位h
        Set<Integer> effectHours = riskList.stream().map(RiskCaseDO::getOccurTime)
                .filter(Objects::nonNull).map(Date::getHours).collect(Collectors.toSet());

        SafetyAreaDO safetyArea = SafetyAreaFactory.createSafetyArea(polygon, effectHours, poiName);
        safetyAreaRepository.batchSave(Lists.newArrayList(safetyArea));
    }

    private List<PositionDO> createPolygon(List<PositionDO> positionDOList, LongWaitAreaConfigDTO longWaitAreaConfigDTO) {
        if(CollectionUtils.isEmpty(positionDOList)) {
            return Lists.newArrayList();
        }
        // 计算中心
        PositionDO centerPoint = GeoToolsUtil.calculateCenter(positionDOList);
        if(centerPoint == null) {
            log.info("计算中心失败");
            return Lists.newArrayList();
        }

        // 过滤掉 远离中心的位置
        List<PositionDO> filterPoints = positionDOList.stream()
                .filter(positionDo -> GeoToolsUtil.distance(centerPoint, positionDo) < longWaitAreaConfigDTO.getMaxR())
                .collect(Collectors.toList());

        if(filterPoints.size() == 0) {
            return Lists.newArrayList();
        }

        List<PositionDO> polygonPoints = null;


        // 重新计算中心
        PositionDO filterPointCenter = GeoToolsUtil.calculateCenter(filterPoints);

        // 计算到中心的最大距离
        Double maxDistance = filterPoints.stream()
                .map(x -> GeoToolsUtil.distance(x, filterPointCenter)).filter(Objects::nonNull)
                .max(Double::compareTo).orElse(null);
        if(maxDistance == null) {
            log.info("最大距离计算异常 : {}", JacksonUtils.to(filterPoints));
            return Lists.newArrayList();
        }
        // 不足三个点或者每个点到中心都非常近，做一个多边形临区域
        if(filterPoints.size() <= 2 ||
                maxDistance < longWaitAreaConfigDTO.getMinR()) {
            polygonPoints = createPolygonWithLessPoint(maxDistance, filterPointCenter, longWaitAreaConfigDTO);
        } else {
            // 点足够多，且足够分散，计算凸包
            polygonPoints = createConvexHull(filterPoints);
        }
        return polygonPoints;
    }


    private List<PositionDO> createPolygonWithLessPoint(Double maxDistance, PositionDO filterPointCenter,
                                                        LongWaitAreaConfigDTO longWaitAreaConfigDTO) {
        if(filterPointCenter == null) {
            return Lists.newArrayList();
        }
        return PolygonUtil.createPolygon(
                filterPointCenter.getLatitude(),
                filterPointCenter.getLongitude(),
                longWaitAreaConfigDTO.getK() * maxDistance,
                longWaitAreaConfigDTO.getN(),
                CoordinateSystemEnum.GCJ02
        );
    }

    private List<PositionDO> createConvexHull(List<PositionDO> filterPoints) {
        return PolygonUtil.computeConvexHull(filterPoints);
    }





}
