package com.sankuai.wallemonitor.risk.center.server.aop.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DomainObjectAfterExecuteDTO {

    @Builder.Default
    private List<Integer> domainIdentityHashCodeList = new ArrayList<>();

    @Builder.Default
    private Map<String, Object> domainIdentityHashCode2Instance = new HashMap<>();

    private String domainClass;

}
