package com.sankuai.wallemonitor.risk.center.server.aop.filter;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.dorado.common.RpcRole;
import com.meituan.dorado.rpc.handler.filter.Filter;
import com.meituan.dorado.rpc.handler.filter.FilterHandler;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.meituan.dorado.rpc.meta.RpcResult;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mtrace.instrument.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * @Desc 调用其他服务 1、相关字段 mt_caller_action: 共用字段字段:  mt_request mt_response mt_response_code mt_response_msg mt_cost
 */
@Slf4j
@Component
public class ThriftInvokerFilter extends AbstractThriftFilter implements Filter {


    @Override
    public RpcResult filter(RpcInvocation invocation, FilterHandler nextHandler) throws Throwable {
        // 1. 构建xmd
        String signature = getSignature(invocation);
        XMDLogFormat rpcProviderXmdFormat = initLogFormat(invocation, signature);
        log.info("[invoker] service: ({}) , request:({})",
                invocation.getServiceInterface().getName() + "." + invocation.getMethod().getName(),
                JsonUtil.serialize(invocation.getArguments()));
        RpcResult result = null;
        Long startTime = System.currentTimeMillis();
        Transaction transaction = Cat.newTransaction("API", signature);
        //2、调用
        try {
            //处理rpc的返回结果
            result = nextHandler.handle(invocation);
            // 3. （可选）接口调用后的逻辑
            log.info("[invoker] service: ({}) , response:({})",
                    invocation.getServiceInterface().getName() + "." + invocation.getMethod().getName(),
                    JsonUtil.serialize(result.getReturnVal()));
            Long cost = System.currentTimeMillis() - startTime;
            //打印格式化日志 + cat
            recordSuccess(rpcProviderXmdFormat, signature, result, cost);
            return result;
        } catch (Throwable e) {
            Long cost = System.currentTimeMillis() - startTime;
            //打印错误并记录
            recordError("THRIFT_INVOKER_RPC_EXCEPTION", rpcProviderXmdFormat, signature, e, cost);
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            transaction.setDurationInMillis(duration);
            transaction.complete();
        }
    }


    @Override
    public int getPriority() {
        // 4. 设置优先级，值越大 优先级越高, 该Filter越先执行，不建议设置为最大值
        return 0;
    }

    @Override
    public RpcRole getRole() {
        // 5. 决定是调用端还是服务端的Filter，客户端：INVOKER, 服务端：PROVIDER, 两端都是：MULTIROLE
        return RpcRole.INVOKER;
    }


}
