package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCheckingQueueService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCheckQueueQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

@Slf4j
@CraneConfiguration
public class RiskCheckingQueueCrane {

    private final static Integer BATCH_SIZE = 10;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCheckingQueueService riskCheckingQueueService;

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @ZebraForceMaster
    @Crane(CraneConstant.RISK_CHECKING_QUEUE_CRANE)
    @OperateEnter(OperateEnterActionEnum.RISK_CHECKING_QUEUE_CRANE)
    public void run() throws Exception {
        log.info("RiskCheckingQueueCrane running");
        try {
            List<Integer> statusList = RiskQueueStatusEnum.listNeedCheckingStatus().stream()
                    .map(RiskQueueStatusEnum::getCode)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(statusList)) {
                return;
            }

            // 通过配置方式决定筛选方式
            List<RiskCheckingQueueItemDO> timeToCheckingQueueItemList;
            if (Objects.equals(lionConfigRepository.getTimeToCheckingQueueItemFilterVersion(), "old")) {
                timeToCheckingQueueItemList = listTimeToCheckingQueueItemByRound(statusList);
            } else {
                timeToCheckingQueueItemList = listTimeToCheckingQueueItemByNextRoundTime(statusList);
            }

            log.info("timeToCheckingQueueItemList size: {}, list: {}", timeToCheckingQueueItemList.size(),
                    JacksonUtils.to(timeToCheckingQueueItemList));
            if (CollectionUtils.isEmpty(timeToCheckingQueueItemList)) {
                return;
            }

            lockUtils.batchLockCanWait(LockKeyPreUtil.buildLockKeys(timeToCheckingQueueItemList),
                    () -> riskCheckingQueueService.batchUpdateItemToChecking(timeToCheckingQueueItemList));
        } catch (Exception e) {
            log.error("ReportRiskCaseCrane error", e);
        }
    }

    /**
     * 通过轮次数据获取待检查队列（老）
     *
     * @param statusList
     * @return
     */
    private List<RiskCheckingQueueItemDO> listTimeToCheckingQueueItemByRound(List<Integer> statusList) {
        RiskCheckQueueQueryParam param = RiskCheckQueueQueryParam.builder()
                .statusList(statusList)
                .checking(false)
                .orderByUpdateTime(OrderEnum.ASC)
                .build();
        List<RiskCheckingQueueItemDO> queueItemList = riskCheckQueueRepository.queryByParam(param);
        log.info("queueItemList: {}", JacksonUtils.to(queueItemList));

        RiskAutoCheckConfigDTO riskCheckQueueConfigDTO = lionConfigRepository.getRiskCheckingConfig();
        List<RiskCheckingQueueItemDO> timeToCheckingQueueItemList = queueItemList.stream()
                .filter(item -> {
                    long passedSeconds = DatetimeUtil.getTimeDiff(item.getCreateTime(), new Date(),
                            ChronoUnit.SECONDS);
                    double nextRound = passedSeconds / 1.0 / riskCheckQueueConfigDTO.getRoundIntervalSeconds();
                    return nextRound >= item.getRound() && nextRound < item.getMaxRound();
                }).collect(Collectors.toList());

        return timeToCheckingQueueItemList;
    }

    /**
     * 通过nextRoundTime获取待检查队列
     *
     * @param statusList
     * @return
     */
    private List<RiskCheckingQueueItemDO> listTimeToCheckingQueueItemByNextRoundTime(List<Integer> statusList) {
        RiskCheckQueueQueryParam param = RiskCheckQueueQueryParam.builder()
                .statusList(statusList)
                .checking(false)
                .nextRoundTimeBelowTo(new Date()) // 早于下一轮执行时间查询
                .orderByUpdateTime(OrderEnum.ASC)
                .build();
        return riskCheckQueueRepository.queryByParam(param);
    }
}
