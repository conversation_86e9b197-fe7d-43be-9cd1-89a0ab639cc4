package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftServerPublisher;
import com.sankuai.walledelivery.thrift.monitor.CatReportFullApiFilter;
import com.sankuai.walleeve.dto.TokenCheckDTO;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.UserFeedbackReportRequest;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftUserFeedbackAdminService;
import com.sankuai.wallemonitor.risk.center.domain.component.WechatAuthAdminService;
import com.sankuai.wallemonitor.risk.center.domain.service.FeedbackRecordService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
@ThriftServerPublisher(filters = CatReportFullApiFilter.class)
public class IThriftUserFeedbackAdminServiceImpl implements IThriftUserFeedbackAdminService {

    @Resource
    private FeedbackRecordService feedbackRecordService;

    @Resource
    private WechatAuthAdminService wechatAuthAdminService;

    /**
     * 手机号格式校验
     */
    private static String PHONE_NUMBER_CHECK_REGEX = "^1[3-9]\\d{9}$";

    @Override
    @OperateEnter(OperateEnterActionEnum.REPORT_FEEDBACK)
    public EveThriftResponse<EmptyResponse> reportFeedback(UserFeedbackReportRequest request) {
        log.info("reportFeedback, UserFeedbackReportRequest = {}", request);
        //1 登陆态校验
        CheckUtil.isNotBlank(request.getToken(), "用户登陆态不可为空");
        TokenCheckDTO tokenCheckDTO = wechatAuthAdminService.checkToken(request.getToken());
        if (Objects.isNull(tokenCheckDTO) || !tokenCheckDTO.getIsValid()) {
            return EveThriftResponse.codeWithMessage(HttpStatus.UNAUTHORIZED.value(), "登陆态过期，请重新登陆").build();
        }
        //2 参数校验
        CheckUtil.isNotBlank(request.getFeedbackContent(), "反馈内容不可为空");
        CheckUtil.isNotNull(request.getFeedbackType(), "反馈类型不可为空");
        CheckUtil.isNotNull(request.getFeedbackChannel(), "反馈渠道不可为空");

        // 手机号格式校验
        if (StringUtils.isNotBlank(request.getPhoneNumber())) {
            boolean isValid = isValidChinesePhoneNumber(request.getPhoneNumber());
            CheckUtil.isTrue(isValid, "手机号无效");
        }

        //3 存储用户反馈
        feedbackRecordService.saveFeedbackRecord(request, tokenCheckDTO.getOpenId());

        return EveThriftResponse.ok(null);
    }

    /**
     * 验证给定的手机号是否符合中国手机号的格式
     *
     * @param phoneNumber 需要验证的手机号
     * @return 如果手机号符合格式则返回true，否则返回false
     */
    private boolean isValidChinesePhoneNumber(String phoneNumber) {
        // 中国手机号的正则表达式规则
        return phoneNumber.matches(PHONE_NUMBER_CHECK_REGEX);
    }

}
