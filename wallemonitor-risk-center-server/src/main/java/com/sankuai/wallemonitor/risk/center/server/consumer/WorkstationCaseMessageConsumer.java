package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.meituan.dbus.MQ.MQUtils;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.common.StaticUtils.EventType;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.OnCallListDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.WorkstationMsgConsumerConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RelatedServiceNameEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseRelatedServiceRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRelatedServiceRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WorkstationCaseMessageConsumer {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseRelatedServiceRecordRepository riskCaseRelatedServiceRecordRepository;


    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_WORKSTATION_MSG_CONSUMER_CONFIG, value = "", defaultValue = "1", allowBlankValue = true)
    private WorkstationMsgConsumerConfigDTO workstationMsgConsumerConfigDTO;


    @MafkaConsumer(
            namespace = "waimai", topic = "data.center.oncall.list.binlog",
            group = "wallemonitor.risk.oncall.list.binlog.consumer"
    )
    @OperateEnter(OperateEnterActionEnum.WALLE_MONITOR_RISK_ONCALL_LIST_BINLOG_CONSUMER)
    public ConsumeStatus receive(String message) {

        try {
            // 1 消息体解析、校验
            DbusUtils utils = MQUtils.newInstanceForMQ(message);

            // 过滤非insert事件 和 空事件
            if (!Objects.equals(utils.getEventType(), EventType.insert) || Objects.isNull(
                    utils.getDataMap())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            OnCallListDTO onCallListDTO = JacksonUtils.from(JacksonUtils.to(utils.getDataMap()), OnCallListDTO.class);
            if (Objects.isNull(onCallListDTO)) {
                log.error("WorkstationCaseMessageConsumer# receive# message parse error, message = {}", message);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 根据数据源进行消息过滤
            Set<String> dataSourceSet = new HashSet<>(workstationMsgConsumerConfigDTO.getDataSourceList());
            if (!dataSourceSet.contains(onCallListDTO.getDataSource())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            handleOnCallListDTO(onCallListDTO);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("CloudTriageEventMessageConsumer# consume# message parse error, message = {}", message, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

    /**
     * 处理onCallListDTO
     *
     * @param onCallListDTO
     */
    void handleOnCallListDTO(OnCallListDTO onCallListDTO) {
        // 2 查询是否有对应的风险事件
        List<Integer> riskTypeList = Objects.isNull(workstationMsgConsumerConfigDTO) ? new ArrayList<>() :
                workstationMsgConsumerConfigDTO.getRiskTypeList();
        List<Integer> riskSourceList = Objects.isNull(workstationMsgConsumerConfigDTO) ? new ArrayList<>() :
                workstationMsgConsumerConfigDTO.getRiskSourceList();
        RiskCaseDOQueryParamDTO paramDTO = new RiskCaseDOQueryParamDTO();
        paramDTO.setVinList(Lists.newArrayList(onCallListDTO.getVin()));
        paramDTO.setLeftJoinRelation(true);
        paramDTO.setCaseTypeList(riskTypeList);
        paramDTO.setSourceList(riskSourceList);

        // todo: 当事件未解除时无法进行关联
        // 真实停滞时间要 小于操作时间
        paramDTO.setOccurTimeBelowTo(onCallListDTO.getOperateTime());
        // 结束时间要 大于操作时间
        paramDTO.setCloseTimeCreateTo(onCallListDTO.getOperateTime());
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(paramDTO);
        log.info("WorkstationCaseMessageConsumer# handleOnCallListDTO# riskCaseDOList = {}", riskCaseDOList);
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return;
        }
        // 同一时间段内不能存在两个风险事件
        if (riskCaseDOList.size() > 1) {
            log.error(
                    "WorkstationCaseMessageConsumer# handleOnCallListDTO# riskCaseDOList size > 1, riskCaseDOList = {}",
                    riskCaseDOList);
        }
        RiskCaseDO riskCaseDO = riskCaseDOList.get(0);
        // 3 记录关联关系
        RiskCaseRelatedServiceRecordDO recordDO = new RiskCaseRelatedServiceRecordDO();
        recordDO.setCaseId(riskCaseDO.getCaseId());
        recordDO.setRelatedId(onCallListDTO.getCaseId());
        recordDO.setServiceName(RelatedServiceNameEnum.WORKSTATION);
        riskCaseRelatedServiceRecordRepository.save(recordDO);
    }
}
