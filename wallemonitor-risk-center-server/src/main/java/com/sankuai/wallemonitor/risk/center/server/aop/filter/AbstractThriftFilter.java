package com.sankuai.wallemonitor.risk.center.server.aop.filter;

import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.Joiner;
import com.meituan.dorado.common.RpcRole;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.meituan.dorado.rpc.meta.RpcResult;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mtrace.Tracer;
import com.meituan.mtrace.instrument.util.JsonUtil;
import com.meituan.xframe.threadpool.core.enums.ResponseCodeEnum;
import com.sankuai.inf.kms.pangolin.api.cat.Cat;
import com.sankuai.walledelivery.commons.enums.ErrorCode.StandardErrorCode;
import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.UserInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SSOLogInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lion.LionConfigUtils;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public abstract class AbstractThriftFilter {


    /**
     * 该异常用于调整不同thrift调用出现异常时的日志打印等级 当出现在这个list内时，不打印error，只打印warn
     */
    public abstract RpcRole getRole();

    /**
     * 是否是标准的thrift接口
     *
     * @param invocation
     * @return
     */
    protected boolean isStandardThrift(RpcInvocation invocation) {
        // 获取method对象
        Method method = invocation.getMethod();
        // 获取方法的返回值的类型
        Class<?> returnType = method.getReturnType();
        return returnType == EveThriftResponse.class || EveThriftResponse.class.isAssignableFrom(returnType);
    }


    /**
     * 初始化入参
     *
     * @return
     */
    protected XMDLogFormat initLogFormat(RpcInvocation invocation, String signature) {
        Object[] paramValues = invocation.getArguments();
        Parameter[] parameters = invocation.getMethod().getParameters();
        Map<String, Object> parameterMap = getMethodParamMap(paramValues, parameters);

        return XMDLogFormat.build()
                .putTag(getAction(), signature)
                .putTag(LogCenterFieldEnum.AL_REQUEST.getFieldCode(), JacksonUtils.to(parameterMap))
                .putTag(LogCenterFieldEnum.AL_OPERATOR.getFieldCode(),
                        Optional.ofNullable(UserInfoContext.getUserInfo()).map(SSOLogInfoDO::getLogin).orElse(""))
                ;

    }

    /**
     * 获取action的名称
     *
     * @return
     */
    private String getAction() {
        return getRole() == RpcRole.INVOKER ? LogCenterFieldEnum.AL_INVOKER.getFieldCode()
                : LogCenterFieldEnum.AL_PROVIDER.getFieldCode();
    }


    /**
     * 构建失败的rpc结果
     *
     * @param invocation
     * @param e
     * @return
     */
    protected RpcResult handleErrorResult(RpcInvocation invocation, Throwable e) throws Throwable {
        // 获取method对象
        if (!isStandardThrift(invocation)) {
            //如果不是非标的，就不处理
            throw e;
        }
        //如果是标准的，则进行处理
        Method method = invocation.getMethod();
        // 获取方法的返回值的类型
        Class<?> returnType = method.getReturnType();
        //这里用子类去创建，然后墙砖削弱
        EveThriftResponse<?> thriftResponse = (EveThriftResponse<?>) returnType.newInstance();
        if (ErrorCodeException.class.isAssignableFrom(e.getClass())) {
            //是标准类型
            thriftResponse.setCode(((ErrorCodeException) e).getCode());
            thriftResponse.setMessage(e.getMessage());
        } else {
            //其他类型
            thriftResponse.setCode(ResponseCodeEnum.SYSTEM_ERROR.getCode());
            thriftResponse.setMessage(ResponseCodeEnum.SYSTEM_ERROR.getMessage());
        }
        thriftResponse.setData(null);
        return RpcResult.buildSuccessResult(thriftResponse);
    }

    /**
     * 记录成功
     *
     * @param rpcProviderXmdFormat
     * @param signature
     * @param result
     */
    protected void recordSuccess(XMDLogFormat rpcProviderXmdFormat, String signature, RpcResult result, Long cost) {
        //xmd 格式化打印
        log.info(rpcProviderXmdFormat.putTag(LogCenterFieldEnum.AL_RESPONSE_CODE.getFieldCode(),
                        String.valueOf(StandardErrorCode.OK.getCode()))
                .putTag(LogCenterFieldEnum.AL_RESPONSE_MSG.getFieldCode(), null)
                .putTag(LogCenterFieldEnum.AL_RESPONSE.getFieldCode(),
                        JacksonUtils.to(result.getReturnVal()))
                .putTag(LogCenterFieldEnum.AL_COST.getFieldCode(), cost.toString())
                .message("success"));
        // 打点
        Transaction transaction = com.dianping.cat.Cat.newTransactionWithDuration("API", signature, cost);
        transaction.complete();
        //cat记录
        //核心业务
        MetricHelper.build().name(signature).tag("thriftType", getThriftType())
                .tag("success", "true").tag("code", "0").tag("cost", cost.toString())
                .count();


    }

    /**
     * 获取signature 接口类名 + 方法名
     *
     * @param invocation
     * @return
     */
    public String getSignature(RpcInvocation invocation) {
        return invocation.getServiceInterface().getSimpleName() + CharConstant.CHAR_DH + invocation.getMethod()
                .getName();
    }

    /**
     * 打印错误日志并记录结果
     *
     * @param rpcProviderXmdFormat
     * @param signature
     * @param e
     */
    protected void recordError(String errorType, XMDLogFormat rpcProviderXmdFormat, String signature,
            Throwable e,
            Long cost) {
        String exceptionName = e.getClass().getSimpleName();
        //根据日志进行打印
        String warnExcludeSignature = Joiner.on(CharConstant.CHAR_MH).join(signature, exceptionName);
        Set<String> exceptionNameList = LionConfigUtils.getSet(LionKeyConstant.LION_KEY_WARN_EXCEPTION, String.class);
        if (CollectionUtils.isNotEmpty(exceptionNameList) && exceptionNameList.contains(warnExcludeSignature)) {
            log.warn(getRole() + " thrift服务异常", e);
        } else {
            log.error(getRole() + " thrift服务异常", e);
        }
        //cat - event埋点
        int retCode = ErrorCodeException.class.isAssignableFrom(e.getClass()) ? ((ErrorCodeException) e).getCode()
                : ResponseCodeEnum.SYSTEM_ERROR.getCode();
        //xmd 格式化打印
        log.warn(rpcProviderXmdFormat.putTag(LogCenterFieldEnum.AL_RESPONSE_CODE.getFieldCode(),
                        Integer.toString(retCode))
                .putTag(LogCenterFieldEnum.AL_RESPONSE_MSG.getFieldCode(), e.getMessage())
                .putTag(LogCenterFieldEnum.AL_RESPONSE.getFieldCode(), null)
                .putTag(LogCenterFieldEnum.AL_COST.getFieldCode(), cost.toString())
                .message("error"));
        //cat记录
        //异常打点
        Cat.logEvent(errorType, signature + "#" + exceptionName + "#" + retCode);
        //核心业务
        MetricHelper.build().name(signature).tag("thriftType", getThriftType())
                .tag("success", "false")
                .tag("cost", cost.toString())
                .tag("code", String.valueOf(retCode))
                .count();
    }

    protected RpcInvocation handleInvocation(RpcInvocation invocation) {
        try {
            String usrStr = Tracer.getContext("sso.user");
            SSOLogInfoDO userInfoDO = null;
            if (StringUtils.isNotBlank(usrStr)) {
                userInfoDO = JsonUtil.deSerialize(usrStr, SSOLogInfoDO.class);
            }
            if (userInfoDO != null) {
                UserInfoContext.bind(userInfoDO);
            }
            if (invocation.getArguments() == null || invocation.getArguments().length < 1 || userInfoDO == null) {
                return invocation;
            }
            //设置用户信息上下文
        } catch (Exception e) {
            log.error("设置入参sso-user失败");
        }
        return invocation;
    }

    private String getThriftType() {
        return getRole() == RpcRole.INVOKER ? "INVOKER" : "PROVIDER";
    }


    protected Map<String, Object> getMethodParamMap(Object[] paramValues, Parameter[] parameters) {
        if (parameters == null || parameters.length < 1) {
            return new HashMap<>();
        }
        Map<String, Object> paramMaps = new HashMap<>();
        for (int i = 0; i < parameters.length; i++) {
            paramMaps.put(parameters[i].getName(), paramValues[i]);
        }
        return paramMaps;
    }

}
