package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.dianping.lion.client.util.CollectionUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.EventMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CloudTriageEventMessageConsumer {

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    /**
     * 分诊事件的完成状态
     */
    private static Integer CLOUD_TRIAGE_EVENT_COMPLETED_STATUS = 2;

    /**
     * 分诊事件的取消状态
     */
    private static Integer CLOUD_TRIAGE_EVENT_CANCELED_STATUS = 3;

    @MafkaConsumer(
            namespace = "waimai", topic = "cloud.triage.output.event.message",
            group = "wallemonitor.risk.cloud.triage.event.message.consumer"
    )
    @OperateEnter(OperateEnterActionEnum.CONSUMER_CLOUD_TRIAGE_EVENT_CHANGE_MESSAGE)
    public ConsumeStatus receive(String message) {

        try {
            // 1 消息体解析
            EventMessageDTO messageDTO = JacksonUtils.from(message,
                    new TypeReference<EventMessageDTO>() {
                    });

            // 2 消息体校验
            CheckUtil.isNotNull(messageDTO, "消息不能为空");
            CheckUtil.isNotBlank(messageDTO.getEventId(), "事件ID不能为空");
            CheckUtil.isNotBlank(messageDTO.getVin(), "车架号不能为空");
            CheckUtil.isNotNull(messageDTO.getEventType(), "事件类型不能为空");

            // 3 根据异常事件类型区分
            switch (messageDTO.getEventType()) {
                case CommonConstant.PUBLIC_REMOVAL_EVENT_CODE:
                    // 更新挪车事件状态
                    updateMoveCarEventStatus(messageDTO);
                    break;
                default:
                    break;
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (UnableGetLockException e) {
            log.warn("消费分诊消息锁冲突失败", e);
            if (checkEndBeforeStartCanRetry(message)) {
                return ConsumeStatus.CONSUME_FAILURE;
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("CloudTriageEventMessageConsumer# consume# message parse error, message = {}", message, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

    /**
     * 更新挪车事件状态
     *
     * @param messageDTO
     */
    private void updateMoveCarEventStatus(EventMessageDTO messageDTO) {

        lockUtils.batchLockCanWait(
                LockKeyPreUtil.buildEventIdAndVinAndType(Collections.singleton(messageDTO.getEventId()),
                        new HashSet<>()),
                1,
                TimeUnit.SECONDS,
                () -> {
                    // 根据eventId 查询挪车事件
                    List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                            RiskCaseDOQueryParamDTO.builder().eventId(messageDTO.getEventId()).build());
                    if (CollectionUtils.isEmpty(riskCaseDOList)) {
                        return;
                    }
                    // 判断事件状态是否为终态
                    RiskCaseDO riskCaseDO = riskCaseDOList.get(0);
                    if (RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus())) {
                        return;
                    }
                    // 更新事件状态
                    if (Objects.equals(messageDTO.getStatus(), CLOUD_TRIAGE_EVENT_COMPLETED_STATUS)) {
                        riskCaseDO.updateStatus(RiskCaseStatusEnum.DISPOSED, messageDTO.getTimestamp());
                    } else if (Objects.equals(messageDTO.getStatus(), CLOUD_TRIAGE_EVENT_CANCELED_STATUS)) {
                        riskCaseDO.updateStatus(RiskCaseStatusEnum.CANCEL, messageDTO.getTimestamp());
                    } else {
                        log.info("无需关心的状态, status = {}", messageDTO.getStatus());
                        return;
                    }
                    riskCaseRepository.save(riskCaseDO);
                });
    }

    /**
     * 判断是否可以重试
     *
     * @param message
     * @return
     */
    private boolean checkEndBeforeStartCanRetry(String message) {
        BroadCastStrategyConfigDTO strategyConfig = lionConfigRepository.getByCaseType(RiskCaseTypeEnum.MOVE_CAR_EVENT);
        // 没有对应的安全风险事件类型，舍弃
        if (strategyConfig == null) {
            return false;
        }
        EventMessageDTO messageDTO = JacksonUtils.from(message,
                new TypeReference<EventMessageDTO>() {
                });
        // 判断是否可以重试
        return strategyConfig.canRetry(messageDTO.getTimestamp());
    }

}
