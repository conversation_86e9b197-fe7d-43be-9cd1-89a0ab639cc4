package com.sankuai.wallemonitor.risk.center.server.consumer;


import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCheckingQueueService;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.MultiMarkMessageConsumeService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionManager;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.MarkMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 自动标注消费者
 */
@Slf4j
@Component
public class AutoMarkConsumer {


    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private ISCheckActionManager checkActionManager;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private LockUtils lockUtils;

    @MessageProducer(topic = MessageTopicEnum.WALLEMONITOR_RISK_AUTO_MARK_MESSAGE)
    private CommonMessageProducer<MarkMessageDTO> markProducer;

    @Resource
    private CaseMarkInfoRepository markInfoRepository;


    @Resource
    private RiskCheckingQueueService riskCheckingQueueService;

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;


    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private MultiMarkMessageConsumeService multiMarkMessageConsumeService;

    /**
     * 消费自动标注消息 message
     *
     * @return
     */
    @MafkaConsumer(
            namespace = "waimai", topic = "wallemonitor.risk.auto.mark.message",
            group = "wallemonitor.risk.auto.mark.message.consumer",
            deadLetter = true,
            deadLetterDelayMills = 12 * 1000
    )
    @OperateEnter(OperateEnterActionEnum.AUTO_MARK_RISK_CASE_PROCESS)
    public ConsumeStatus receive(String message) {
        return multiMarkMessageConsumeService.consume(message);
    }


}
