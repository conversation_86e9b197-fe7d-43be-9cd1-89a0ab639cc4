package com.sankuai.wallemonitor.risk.center.server.consumer;


import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMrmMessageDTO;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CloudCursorAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.CloudCursorResourceRequest;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.CloudCursorResourceResponse;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.RiskCaseMrmDisposeConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.RiskCaseMrmDisposeConfigDTO.MrmStatusDisposeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallCloudCursorTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import javafx.util.Pair;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
@Slf4j
public class RiskCaseMrmConsumer {

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private CloudCursorAdapter cloudCursorAdapter;

    @ConfigValue(LionKeyConstant.RISK_MRM_CONSUMER_DISPOSED_SWITCH)
    private Boolean riskMrmConsumerDisposedSwitch;
    private static final int CHECK_BATCH_SIZE = 10;
    private static final int MAX_RETRY_TIME = 3;


    @OperateEnter(OperateEnterActionEnum.RISK_MRM_PROCESS_CONSUMER_ENTER)
    @MafkaConsumer(
            namespace = "waimai", topic = "wallemonitor.risk.mrm.process.message",
            group = "wallemonitor.risk.mrm.process.message.consumer", deadLetter = true, deadLetterDelayMills = 6 * 1000
    )
    public ConsumeStatus receive(String msg) {
        try {
            //解析消息
            EveMqCommonMessage<RiskCaseMrmMessageDTO> message = JacksonUtils.from(msg,
                    new TypeReference<EveMqCommonMessage<RiskCaseMrmMessageDTO>>() {
                    });
            if (message.getBody() == null || StringUtils.isBlank(message.getBody().getTraceId())
                    || Objects.isNull(message.getBody().getStatus())
            ) {
                log.warn(msg,
                        new SystemException("风险处置消息体不完整"));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            RiskCaseVehicleStatusEnum mrmStatus = RiskCaseVehicleStatusEnum.findByValue(
                    message.getBody().getStatus());
            if (mrmStatus == null) {
                //
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if (!riskMrmConsumerDisposedSwitch) {
                // 开关未开启，空消费
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if (mrmStatus == RiskCaseVehicleStatusEnum.DISPOSED) {
                onDisposeMessageListener(message.getBody());
            }


            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (UnableGetLockException e) {
            log.warn("消费风险关联事件消息锁冲突失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        } catch (ParamInputErrorException e) {
            log.warn("消费风险事件消息业务正常失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        } catch (Throwable e) {
            log.error("消费风险事件消息异常失败", e);
            return ConsumeStatus.CONSUME_FAILURE;
        }
    }
    /**
     * 消费退控消息策略
     * */
    public void onDisposeMessageListener(RiskCaseMrmMessageDTO message) {
        Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> releaseMrmStrategyConfigDTO =
                lionConfigRepository.getReleaseMrmDisposedConfig();

        List<Integer> sourceList = releaseMrmStrategyConfigDTO.keySet().stream()
                .map(Pair::getKey).collect(Collectors.toList());

        List<Integer> typeList = releaseMrmStrategyConfigDTO.keySet().stream()
                .map(Pair::getValue).collect(Collectors.toList());

        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder()
                        // 指定vin
                        .vinList(Lists.newArrayList(message.getVin()))
                        .leftJoinRelation(true)
                        // 必须是未完成状态
                        .statusList(RiskCaseStatusEnum.getUnTerminal())
                        // 呼叫中
                        .mrmCalledList(Lists.newArrayList(RiskCaseMrmCalledStatusEnum.CALLING.getCode()))
                        // 指定来源
                        .sourceList(sourceList)
                        // 指定类型
                        .caseTypeList(typeList)
                        // 一天之内的
                        .createTimeCreateTo(DatetimeUtil.getNSecondsBeforeDateTime(new Date(),
                                DateTimeConstant.ONE_DAY_SECOND))
                        .build()
        );
        // riskCaseList to Map
        Map<String, RiskCaseDO> riskCaseDOMap = riskCaseDOList.stream()
                .collect(Collectors.toMap(RiskCaseDO::getEventId, Function.identity()));

        List<RiskCaseDO> updateRiskCaseList = new ArrayList<>();

        // 切分case
        Lists.partition(new ArrayList<>(riskCaseDOMap.keySet()), CHECK_BATCH_SIZE).forEach((eventIdList) -> {

            List<RiskCaseDO> checkCaseList = eventIdList.stream().map(riskCaseDOMap::get)
                    .collect(Collectors.toList());
            // case 批量锁
            lockUtils.batchLockCanWait(LockKeyPreUtil.buildKeyWithEventId(new HashSet<>(eventIdList)),
                2, TimeUnit.SECONDS, () -> cancelCallCloudControl(checkCaseList, updateRiskCaseList, 1));

        });

        // 修改risk case mrm called 状态
        riskCaseRepository.batchSave(updateRiskCaseList);

    }

    /**
     * @param checkCaseList      需要校验的case
     * @param updateRiskCaseList 存储修改成功的case
     * @param retryTimes         尝试次数
     *
     * */
    private void cancelCallCloudControl(List<RiskCaseDO> checkCaseList, List<RiskCaseDO> updateRiskCaseList, int retryTimes) {
        // 递归终止条件
        if (CollectionUtils.isEmpty(checkCaseList)) {
            return ;
        }
        if (retryTimes > MAX_RETRY_TIME) {
            log.error("cases = {}, 多次尝试失败 ", checkCaseList);
            return;
        }
        // 保存失败的case
        List<RiskCaseDO> failedCaseList = new ArrayList<>();
        // 拿到所有的eventId
        List<String> eventIdList = checkCaseList.stream()
                .map(RiskCaseDO::getEventId).collect(Collectors.toList());

        Map<String, RiskCaseVehicleRelationDO> vehicleRelationMap = riskCaseVehicleRelationRepository
                .queryByParam(RiderCaseVehicleRelationDOParamDTO.builder()
                        .eventIdList(eventIdList).build()).stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, Function.identity()));

        checkCaseList.forEach(riskCaseDO -> {
            // 判断case仍在未完结状态，并且是呼叫中
            if (!RiskCaseStatusEnum.getUnTerminalEnum().contains(riskCaseDO.getStatus() )
                    || !RiskCaseMrmCalledStatusEnum.CALLING.equals(riskCaseDO.getMrmCalled())) {
                log.info("riskCase = {}, 找不到风险原因", riskCaseDO);
                return;
            }
            // 当前case的vehicle relation
            RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = vehicleRelationMap.get(riskCaseDO.getCaseId());

            // 无vehicle关联，抛出异常
            if (Objects.isNull(riskCaseVehicleRelationDO) ||
                    Objects.isNull(riskCaseVehicleRelationDO.getCallMrmReason())) {
                log.error("risk case = {} 未找到呼叫原因", riskCaseDO);
                return;
            }

            // 取消呼叫云控
            CloudCursorResourceRequest request = CloudCursorResourceRequest.builder()
                    .action(CallCloudCursorTypeEnum.CANCEL.name().toLowerCase())
                    .reason(Integer.parseInt(riskCaseVehicleRelationDO.getCallMrmReason()))
                    .timestamp(System.currentTimeMillis())
                    .vin(riskCaseVehicleRelationDO.getVin())
                    .source(CommonConstant.BEACON_TOWER_CALL_CLOUD_CONTROL_SOURCE)
                    .needCancelCommand(true)
                    .build();
            CloudCursorResourceResponse cloudCursorResourceResponse = cloudCursorAdapter.callCloudCursor(request);

            // 取消呼叫失败，存到失败列表里
            if(cloudCursorResourceResponse.getCode() != 200) {
                log.error("caseId = {}, 取消呼叫失败， retryTimes = {}", riskCaseDO.getCaseId(), retryTimes);
                failedCaseList.add(riskCaseDO);
                return;
            }


            // 取消呼叫成功
            riskCaseDO.setMrmCalled(RiskCaseMrmCalledStatusEnum.CANCEL);
            updateRiskCaseList.add(riskCaseDO);
        });

        // 剩下的失败case走失败递归
        cancelCallCloudControl(failedCaseList, updateRiskCaseList, retryTimes + 1);
    }
}
