请你按照如下步骤进行分析：
步骤1:分析给你的车辆前后时间的10张图片，明确车辆前视、后视、左视、右视、俯视，包含哪些物体，需要识别的物体，如'物体识别规则描'。
步骤2:根据所识别的物体，来分析车辆所处的场景，需要识别的场景，如'场景识别规则'描述。注意使用举例图片。举例图片在'场景举例'所描述的图片里面。
步骤3:根据满足的场景，来推理当前车辆的行为，如'行为推理规则'描述，举例：本车场景为'在路中间等待'时，行为推理结果为'路中间停滞'；最先满足的行为推理规则描述里面的行为，作为最终推理出来的行为。
步骤4:告诉你确定的、满足要求的物体、场景和行为，不确定的不需要告诉询问者，不在要求识别范围内的，不要告诉询问者。
返回需要按照一个json结构，对识别到的物体来说，前视物体放在frontObjectList、后视物体放在backObjectList、左视物体放在leftObjectList、右视物体放在rightObjectList、俯视物体放在loopObjectList，按照{"name":"","reason":""} 形式，可以是多个.对识别到的场景来说，放在sceneList里面, 按照{"name":"","reason":""} 进行描述，可以是多个。对推理出的行为来说，行为放在activityList, 按照{"name":"","reason":""}进行描述，可以是多个，优先级高的在前；
frontObjectList、frontObjectList、frontObjectList、frontObjectList中的name，为物体识别规则所定义的名称，举例：[绿色交通信号灯]中的'绿色交通信号灯'，就是name之一；reason是你给的识别物体的原因，reason请压缩至100个字符。
sceneList的name，为场景识别规则所定义的名称，举例：[在路中间等待]中的'在路中间等待'，就是name之一；reason是你给的识别场景的原因，reason请压缩至100个字符。
activityList里的name，为物体识别规则所定义的名称，举例：[路中间停滞]中的'路中间停滞'，就是name之一；reason是你给的行为推理的原因，reason请压缩至100个字符。
物体、场景和推理规则，需要是给定的不能自己创造。
举例：{"frontObjectList":[{"name":"绿色交通信号灯","desc":""}],"backObjectList":[{"name":"","desc":""}],"leftObjectList":[{"name":"","desc":""}],"rightObjectList":[{"name":"","desc":""}],"loopObjectList":[{"name":"","reason":""}],"sceneList":[{"name":"在路中间等待","reason":""},"activityList":[{"name":"路中央停滞","reason":""}]}
frontObjectList、backObjectList、leftObjectList、rightObjectList中，name可以取值的范围为:绿色交通信号灯,红色交通信号灯,斑马线,禁止停车标志,导向线,停车线,停车位,车道分割线,隔离带,警示标志,路肩,机动车道,非机动车道,路口,辅路,小区内部道路,骑手,警察或执法人员,施工人员,舱门,前进线。
sceneList中，name可以取值的范围为:在路中间等待,在路边等待,在车队中,在路口等待,等信号灯变绿,阻塞通行,在车辆内部,在室内,在停车场,在停车位,在事故现场,在施工现场。
activityList中，name可以取值的范围为:路中间停滞,路口停滞,靠边停滞,非行驶区域停滞,障碍物堵路,逆行停滞,车辆顶牛,停车场停放,拖车中,在等待取餐,排队通行,待绿灯通行,其他。
本车车辆图片如图所示:
https://msstest.sankuai.com/walle-teleop-dvr/vin_transfer_ts/vehicle_s20.png
场景举例：
[image]在路中间等待:https://walle.meituan.com/replay/video/occurTime?vin=LMTZSV028NC036648&view=front&occurTime=20241021120415,https://walle.meituan.com/replay/video/occurTime?vin=LMTZSV026NC076338&view=front&occurTime=20241022110746,https://walle.meituan.com/replay/video/occurTime?vin=LMTZSV025NC010556&view=front&occurTime=20241021170555,https://walle.meituan.com/replay/video/occurTime?vin=LMTZSV024MC088826&view=front&occurTime=20241022184053
物体举例:
[image]红色交通信号灯:https://walle.meituan.com/replay/video/occurTime?vin=LMTZSV021MC066010&view=front&occurTime=20241021235730,https://walle.meituan.com/replay/video/occurTime?vin=LMTZSV027NC001874&view=front&occurTime=20241021235740