物体识别规则定义如下:
1.[绿色交通信号灯]:交通标志，为轮廓明显的、有三个及以上灯泡一次排列，横向或竖向排列，颜色为"绿色"。位于路口，用于指示行人或机动车通行；必须满足轮廓清晰的要求，只有大致的颜色不算,前方车辆尾部的红色灯不算,不清晰或者模糊的不算，颜色无法判断的不算。
2.[红色交通信号灯]:交通标志，为轮廓明显的、有三个及以上灯泡一次排列，横向或竖向排列，颜色为"红色"。位于路口，用于指示行人或机动车通行；必须满足轮廓清晰的要求，只有大致的颜色不算,前方车辆尾部的红色灯不算,不清晰或者模糊的不算，颜色无法判断的不算。
3.[无明确颜色的交通信号灯]:交通标志，为轮廓明显的、有三个及以上灯泡一次排列，横向或竖向排列，颜色不明确。位于路口，用于指示行人或机动车通行；必须满足轮廓清晰的要求，前方车辆尾部的红色灯不算,不清晰或者模糊的不算，颜色无法判断的不算。
3.[斑马线]:交通标志，由白色块状条纹组成。位于路口，供行人安全通过。
4.[禁止停车标志]: 禁止停车标志是一种交通标志，通常用来表示车辆禁止在指定区域停放或者停车。它通常以蓝色圆形背景为特征，中间穿过一个红色对角线，表示不允许停车。有时，它还可能包括如“禁止停车”等文字，以提供进一步的澄清。这个标志的设计和外观可能会根据不同国家和地区的交通规则而有所不同。
5.[导向线]:交通标志，通常为指向特定方向的箭头。位于车道内，指示车辆行驶方向。
6.[停车线]:交通标志，为白色宽线条，垂直于行驶方向。位于车道前方，标明车辆停止位置。
7.[停车位]:交通标志，由有色矩形线条构成。通常位于道路边缘或路肩旁，标示车辆停放区域。
8.[车道分割线]:交通标志，为白色或黄色线条。位于车道之间，分隔不同方向或同向车道。实线表示不可越线。
9.[隔离带]:道路设施，可为围栏或绿化带。用于分隔不同方向车道或机非车道。
10.[警示标志]:道路设施，一般为红色锥桶、三角形提示牌，警戒线。用于隔离施工现场，防止行人车辆受损。
11.[路肩]:道路设施，通常为一定宽度的硬质路面。位于道路外侧，供行人通行，禁止机动车驶入。
12.[机动车道]:道路类型，专供机动车行驶。具有明确的导向线和车道分割线，一般周围有多个同类型的车道。
13.[非机动车道]:道路类型，供非机动车和行人通行。有相应标志，通常只够行人或者一辆车通行，位于道路两侧，在隔离带和路肩之间。
14.[道路交叉口]:道路类型，两条相互垂直的道路交叉处或者汇入的公共区域，呈现十字形或者T型。从前视看，能看到两条道路相交，中间有一块较空旷区域，从左右视角看，可以看见交互的其他车道的车辆信息，垂直于车头方向有其他车道和车辆。
15.[辅路]:道路类型，供机动车和非机动车通行。位于主路两侧，在隔离带和路肩之间。
16.[小区内部道路]:道路类型，主要供行人或非机动车通行。通常无明显交通标志，有减速带、限速标识和停车位。多为单向通行，两侧为住宅楼或商铺。
17.[骑手]:交通参与者，一般为身着蓝色、绿色或者黄色的人员。
18.[警察或执法人员]:交通参与者，一般为身着警服或者执法反光服的工作人员，无安全帽。
19.[施工人员]:交通参与者，身着反光服，头戴安全帽的人员。
20.[舱门]:本车顶部的盖子，可以打开，打开方向为从右向左，完全打开时，左侧和车体链接，右侧翘起，从前视可以看见。
21.[前进线]:本车导航信息之一，可以在俯视图看到,为两个黄色细线，靠近地面延伸出去，表示车身将要前进的方向。
22.[前方车辆]:交通参与者，跟本车位于同车道，且行驶路径的方向一致的、一般为有司机的、尾灯亮起的大、中、小型机动车辆。
请你按照如下步骤，进行物体识别和输出：
步骤1:请按照上述定义的物体识别规则，观察前视、左视、后视、俯视的图片，进行物体识别。
步骤2:返回按照一个json结构，对识别到的物体来说，前视物体放在frontObjectList、左视物体放在leftObjectList、右视物体放在rightObjectList、俯视物体放在loopObjectList，按照{"name":"","reason":","position":["",""]} 形式，可以是多个，优先级高的在前；
frontObjectList、leftObjectList、rightObjectList、loopObjectList中的name，为物体识别规则所定义的名称，在[]内。举例：[绿色交通信号灯]中的'绿色交通信号灯'，就是name之一；reason是你给的识别物体的原因，reason请压缩至100个字符，给出满足的特征信息；
举例：{"frontObjectList":[{"name":"绿色交通信号灯","reason":"",position:[1,1]},{"name":"施工人员","reason":""}],"leftObjectList":[{"name":"","reason":""}],"rightObjectList":[{"name":"","reason":""}],"loopObjectList":[{"name":"","reason":""}]}
frontObjectList、leftObjectList、rightObjectList、loopObjectList中的数组元素，可以包含多个。
frontObjectList、leftObjectList、rightObjectList中，loopObjectList中的元素，name可以取值的范围为:绿色交通信号灯,红色交通信号灯,斑马线,禁止停车标志,导向线,停车线,停车位,车道分割线,隔离带,警示标志,路肩,机动车道,非机动车道,路口,辅路,小区内部道路,骑手,警察或执法人员,施工人员,舱门,前进线；返回的name取值只限定在其中。
frontObjectList、leftObjectList、rightObjectList中，loopObjectList中的元素，position表示物体相对于画面的坐标，以画面左上角为0,0点，横向表示x，竖向表示y,x取值0到图片的宽，y取值0到图片的高。将物体的左上角坐标x,y，依次放入position中。