已知物体识别的结果：前视识别的物体为:${front_result}。左视识别的物体:${left_result}。右视识别的物体:${right_result}。环视识别的物体:${loop_result}。各个视角：识别出来的物体为数组，如果为多个，表示多个识别出来的物体。
场景识别规则定义如下:
1.[车道前方空旷无车]:环境信息之一，要求：本车在车道中间，本车所在车道内的正前方，有比较长的、可以容纳至少3个以上的[前方车辆]通行的空间，但是并无[前方车辆]。必要条件：基于当前时刻的视角图片，从前视看，车头所在的正前方同车道，有一段可以行驶、可容纳3辆车以上的空间，空间内无[前方车辆]；本车距离交通信号灯较远或者无交通信号灯，从左右视看，车辆距离左右侧间隔相近或者靠近左侧[隔离带]；必须满足前视条件和左右视角条件。
2.[在路边等待]:环境信息之一，要求：本车在车道的一旁靠近路肩，且所在车道的正前方到[道路交叉口]之间，有比较长的空间可以容纳其他车辆通行，但是无其他车辆。必要条件：基于当前时刻的视角图片,从前视看，车头所在的正前方同车道，有一段可以行驶的空间、可容纳3辆车以上的空间，空间内无[前方车辆]；本车距离交通信号灯较远或者无交通信号灯；右视视角看，紧贴路肩或者隔离带；必须满足前视右视的条件。
3.[在车队中]:环境信息之一，要求：本车在车流中排队,等[前方车辆]开走后继续前行,正常停滞。必要条件：基于当前时刻的视角图片,从前视看，"前视物体"包含[前方车辆]，且本车车头正对着[前方车辆]的车尾，本车和本车前方有[前方车辆]的距离小于2个本车车身长度，本车和[前方车辆]排列均匀，[前方车辆]车尾的灯处于亮起状态并且不在上下客。
4.[道路交叉口有绿灯未通行]:环境信息之一，要求：本车车辆前方是[道路交叉口]区域，左右视角可以看到在[道路交叉口]交汇的其他车道，本车同车道无[前方车辆]，前方有[绿色交通信号灯]，表示本车在绿灯下停滞未走，有堵路的风险。必要条件："前视物体"中必须包含[绿色交通信号灯]且不可包含[红色交通信号灯]，否则不满足。
5.[障碍物阻塞通行]:环境信息之一，要求：前方有车辆或者非机动车（如电瓶车）、障碍物（如落叶、石子、水坑）、行人、无司机车辆、熄火无尾灯车辆等，挡住了本车的前进方向，影响通行。必要条件： 从前视看，本车正前方道路上有紧跟的非机动车的物体、行人或者朝向和本车不相符的[前方车辆]、车身横在路中间的[前方车辆]、正在上下客的[前方车辆]、熄火或无无司机的[前方车辆]；从俯视视角看，[前进线]和物体、行人或者朝向不同的机动车有相交；必须满足前视或者俯视条件。
6.[在其他车辆内部]:环境信息之一，要求：本车出现问题，依靠其他车辆拖行然后去往目的地。 必要条件：基于当前时刻的视角图片,从俯视视角观察，一般位于拖车等大型车辆内部，从前视视角看，有拖车车头；左视或者右视看，高于地面。
7.[在室内]:环境信息之一，要求：本车在建筑物内部。必要条件：基于当前时刻的视角图片,从前左右观察，被天花板、墙、窗户包围，看不见天空信息。
8.[在停车场]:环境信息之一，要求：本车位于停车场，修整或者保养，停放不通行。必要条件：基于当前时刻的视角图片,从前左右视角观察，左右两侧，都有有排列规则的车辆，地方较为宽阔，有很少的道路标识信息；必须满足左右视角条件。
9.[在停车位]:环境信息之一，要求：本车位于路边停车位，临时停放不通行。必要条件：基于当前时刻的视角图片,从前左右视角观察，左右两侧，有完整的停车位标识，前视角有排列规则的车辆，这些车辆无通行意图，无尾灯信息，右侧紧贴路肩。
10.[在事故现场]:环境信息之一，要求：本车周围有事故，停滞影响通行。必要条件：基于当前时刻的视角图片,从左侧、右侧、前侧视角看，每个视角必须都有多个规律间隔的警示标志且紧贴车辆；从左右两个视角看，有警察或者受损车辆；如果只有前视有警示桩，则不算是事故现场。
11.[在施工现场]:环境信息之一，要求：本车行进路线上，有施工区域，停滞影响通行。必要条件：基于当前时刻的视角图片,从左侧、右侧、前侧视角看，每个视角必须都有多个规律间隔的警示标志且紧贴本车，从左侧或者右侧看，警示标志内，有大型施工机器，如挖掘机、推土机，有携带安全帽的工作人员。如果只有警示桩但不靠近本车车身或施工区域和本车不在同一车道或者相邻的车道，则不算。
12.[有其他车辆相向行驶]:环境信息之一，要求：同车道上未相隔[道路交叉口]的[前方车辆]，朝向本车行驶，[前方车辆]的车头和本车车头方向相反，比如：从前视视角，看到同车道的车辆车头。必要条件：基于当前时刻的视角图片,前视视角的中心区域，有其他车辆的车头，并且车辆非熄火状态，必须满足前视视角。
13.[在逆向车道]: 环境信息之一，要求：本车前进方向上，有[导向线]且[导向线]的方向，不等于本车车头的前进方向，比如：车辆前行，[导向线]指向后方。必要条件：基于当前时刻的视角图片,前视视角可以看见本车道的导向线，[导向线]和车头方向相反，必须满足前视视角定义。
14.[红灯禁止通行]:环境信息之一，要求：本车前方是[道路交叉口]区域，左右视角可以看到在[道路交叉口]交汇的其他车道，本车同车道无[前方车辆]，前视是[红色交通信号灯]，车辆停滞未走，等变更为[绿色交通信号灯]然后通行，停滞正常遵守交通，无风险。必要条件：基于当前时刻的视角图片,从前视看，前方为[道路交叉口]，车辆正前方无其他等待车辆，前视的交通信号灯为[红色交通信号灯]，表示车辆在红灯下正常等待通行；只看前视视角的交通信号灯，不看其他视角的交通信号灯。
15.[在道路交叉口]:环境信息之一，要求：本车前方是[道路交叉口]区域，位于两条道路交汇区域的边缘，紧贴[道路交叉口]，本车前进方向的同车道上无[前方车辆]，本车属于头车。必要条件：从前视看，正前方同车道无[前方车辆]，并且紧挨两条道路交汇区域的边缘，正前方有斑马线等行人通过区域的特征，从左右视角看，可以看见交汇的道路和交汇道路上的其他车辆。
请你按照如下步骤，进行场景识别和输出：
步骤1:请根据上述定义的场景识别规则，使用上下文里面上次对话里面，你识别出来的各个视角的物体，进行场景识别。注意规则里面必须满足的必要条件，仔细检查上次对话中，各个视角识别出来的物体是否含有"必要条件"规定的物体，如果不含有则不满足对应的"场景识别规则"。交通灯信号只可以使用"物体识别规则"识别出来的结果，如果。
步骤2:返回按照一个json结构，对识别到的场景来说，放在sceneList里面, 按照{"name":"","reason":""} 进行描述，可以是多个。
sceneList的name，为场景识别规则所定义的名称，在[]中，举例：[车道前方空旷无车]中的'车道前方空旷无车'，就是name之一；reason是识别为该场景的原因，请在reason里面描述满足了规则定义的怎样的必要条件，reason请压缩至100个字符。
举例：{"sceneList":[{"name":"车道前方空旷无车","reason":""},{"name":"","reason":""}]}
sceneList中，name可以取值的范围为:"车道前方空旷无车","在路边等待","在车队中","道路交叉口有绿灯未通行","红灯禁止通行","障碍物阻塞通行","在其他车辆内部","在室内","在停车场","在停车位","在事故现场","在施工现场","有其他车辆相向行驶","在逆向车道","在道路交叉口"。