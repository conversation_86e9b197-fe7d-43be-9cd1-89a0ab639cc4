已知本车识别出来的场景为:${scene_result}。多个表示多个识别出来的场景。
行为推理规则定义如下:
1.[路中间停滞]:推理规则之一，编号为1。要求：本车识别出来的场景中，必须满足：包含[车道前方空旷无车]且不包含[在道路交叉口]。
2.[路口绿灯停滞]:推理规则之一，编号为2。要求：本车识别出来的场景中，必须满足：包含[道路交叉口有绿灯未通行]且包含[在道路交叉口]。
3.[靠边停滞]:推理规则之一，编号为3。要求：本车识别出来的场景中，必须满足：包含[在路边等待]且不包含[在道路交叉口]。
4.[非行驶区域停滞]:推理规则之一，编号为4。要求：本车识别出来的场景中，必须满足：包含[在事故现场]或[在施工现场]。
5.[障碍物堵路]:推理规则之一，编号为5。要求：本车识别出来的场景中，必须满足：包含[障碍物阻塞通行]。
6.[逆行停滞]:推理规则之一，编号为6。要求：本车识别出来的场景中，必须满足：包含[在逆向车道]或者[其他车辆相向行驶]。
7.[车辆顶牛]:推理规则之一，编号为7。要求：本车识别出来的场景中，必须满足：包含[其他车辆相向行驶]。
8.[停车场停放]:推理规则之一，编号为8。要求：本车识别出来的场景中，必须满足：包含[在停车场]或[在停车位]。
9.[拖车中]:推理规则之一，编号为9。要求：本车识别出来的场景中，必须满足：包含[在其他车辆内部]。
10.[排队通行]:推理规则之一，编号为10。要求：本车识别出来的场景中，必须满足：包含[在车队中]且不包含[红灯禁止通行]。
11.[待红灯变绿后通行]:推理规则之一，编号为11。要求：本车识别出来的场景，必须满足：包含[红灯禁止通行]且不包含除此之外的场景。
12.[无法识别]:推理规则之一，编号为12。要求：场景不满足其他行为推理规则时命中。
请你按照如下步骤，进行行为推理和输出：
步骤1:请根据上述定义的行为推理规则，使用之前对话里面你识别出来的场景，进行行为推理。请注意，必须严格按照规则的要求进行, 只可以使用之前识别的场景结果，不要重新识别新的物体和场景。
步骤2:对推理出来的行为，按照行为推理规则的编号进行排序，编号小的优先级高。
步骤3:返回按照一个json结构，对每个推理出的行为来说，放在activityList里面, 按照{"name":"","priority":1,"reason":""}进行描述，可以是多个，优先级高的数组的第一个。举例：同时满足[路中间停滞]、[非行驶区域停滞]的场景要求时，[路中间停滞]需要放在数组的第一个。
activityList里的name，为物体识别规则所定义的名称，举例：[路中间停滞]中的'路中间停滞'，就是name之一；priority是name在行为推理规则中，对应的行为推理规则的编号；reason是你给的行为推理的原因，reason请压缩至100个字符，并给出满足的场景信息。
activityList里的name可以取值的范围为:"路中间停滞","路口绿灯停滞","靠边停滞","非行驶区域停滞","障碍物堵路","逆行停滞","车辆顶牛","停车场停放","拖车中","在等待取餐","排队通行","待红灯变绿后通行","无法识别"。activityList里的name必须在这些枚举出来的名称范围内。
举例：{"activityList":[{"name":"路中间停滞","priority":1,"reason":""},{"name":"","priority":"","reason":""}]}