package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.sankuai.wallemonitor.risk.center.domain.config.MultiMarkMessageConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.MarkMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.junit.Test;

public class MultiMarkMessageConfigDTOTest extends SpringTestBase {

    @Resource
    private MultiMarkMessageConfigDTO multiMarkMessageConfigDTO;

    @Test
    public void test() {

        CommonMessageProducer<MarkMessageDTO> s = multiMarkMessageConfigDTO
                .getMarkMessageProducerByVersion("random_forest");

        s.sendDelayCustomMessage(MarkMessageDTO.builder().itemCaseId("23123123").build(), 7000L);


    }

}