package com.sankuai.wallemonitor.risk.center.server.test.unit.thrift;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.sankuai.walleeve.dto.TokenCheckDTO;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.JwtUtil;
import com.sankuai.wallemonitor.risk.center.api.request.UserConfirmNoticeRequest;
import com.sankuai.wallemonitor.risk.center.api.request.WechatBasicFrontRequest;
import com.sankuai.wallemonitor.risk.center.api.vo.UserNoticeVersionVO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.UserNoticeReadRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.UserNoticeReadRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import com.sankuai.wallemonitor.risk.center.server.thrift.IThriftUserNoticeAdminServiceImpl;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;

public class UserNoticeAdminServiceTest extends ServiceTestBase {

    @InjectMocks
    @Spy
    private IThriftUserNoticeAdminServiceImpl userNoticeAdminService;

    private WechatBasicFrontRequest wechatBasicFrontRequest;

    private UserConfirmNoticeRequest userConfirmNoticeRequest;

    private TokenCheckDTO tokenCheckDTO;

    private List<UserNoticeReadRecordDO> userNoticeReadRecordDOList;

    @Before
    public void setUp() {
        wechatBasicFrontRequest = new WechatBasicFrontRequest("Token");
        userNoticeReadRecordDOList = Arrays.asList(
                UserNoticeReadRecordDO.builder().userId("openId").versionId(6L).build(),
                UserNoticeReadRecordDO.builder().userId("cfd").versionId(5L).build()
        );
        userConfirmNoticeRequest = new UserConfirmNoticeRequest(6L);
        ReflectionTestUtils.setField(userConfirmNoticeRequest, "token", "Token");

        // mock jwtToken 验证
        PowerMockito.mockStatic(JwtUtil.class);
        tokenCheckDTO = TokenCheckDTO.builder()
                .isValid(true)
                .openId("openId")
                .build();
        PowerMockito.when(JwtUtil.getTokenValidAndSubject(any(), any())).thenReturn(tokenCheckDTO);

        // mock 根据用户ID查询通知版本ID userNoticeReadRecordRepository.queryByParam
        when(userNoticeReadRecordRepository.queryByParam(any(UserNoticeReadRecordDOQueryParamDTO.class))).thenAnswer(
                invocation -> {
                    UserNoticeReadRecordDOQueryParamDTO param = invocation.getArgument(0);
                    return userNoticeReadRecordDOList.stream()
                            .filter(record -> record.getUserId().equals(param.getUserId()))
                            .collect(Collectors.toList());
                }
        );

        // mock lion
        when(lionConfigRepository.getUserNoticeVersionContext()).thenReturn("用户须知版本内容");
    }

    /**
     * 测试与用户已阅读的版本进行对比，不一致则返回最新版本的用户须知给用户
     */
    @Test
    public void testGetUserNoticeNotEqual() {
        when(lionConfigRepository.getUserNoticeVersionId()).thenReturn(5L);

        // 运行
        EveThriftResponse<UserNoticeVersionVO> response = userNoticeAdminService.getUserNotice(wechatBasicFrontRequest);

        // 验证
        assertEquals(response.getData().getIsDisplay(), true);
        verify(userNoticeReadRecordRepository, times(1)).save(any());
    }

    /**
     * 测试与用户已阅读的版本进行对比，一致则不返回
     */
    @Test
    public void testGetUserNoticeEqual() {
        when(lionConfigRepository.getUserNoticeVersionId()).thenReturn(6L);

        // 运行
        EveThriftResponse<UserNoticeVersionVO> response = userNoticeAdminService.getUserNotice(wechatBasicFrontRequest);

        // 验证
        assertEquals(response.getData().getIsDisplay(), false);
        verify(userNoticeReadRecordRepository, times(0)).save(any());
    }

    /**
     * 测试用户已确认阅读用户通知
     */
    @Test
    public void testConfirmUserNotice() {
        // 运行
        EveThriftResponse<EmptyResponse> response = userNoticeAdminService.confirmUserNotice(userConfirmNoticeRequest);

        // 验证
        assertEquals(response.getCode(), 0);
        verify(userNoticeReadRecordRepository).batchSave(anyList());

    }
}
