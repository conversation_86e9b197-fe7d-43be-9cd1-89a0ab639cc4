package com.sankuai.wallemonitor.risk.center.server.test.unit.crane;

import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.crane.CloseUnhandledRiskCaseCrane;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import java.util.ArrayList;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class CloseUnhandledRiskCaseCraneTest extends ServiceTestBase {

    @Mock
    private RiskCaseRepository caseRepository;

    @InjectMocks
    private CloseUnhandledRiskCaseCrane closeUnhandledRiskCaseCrane;

    private ArrayList<RiskCaseDO> unhandledRiskCaseList;

    @Before
    public void setUp() {
        unhandledRiskCaseList = new ArrayList<>();

        MockitoAnnotations.initMocks(this);
        Mockito.when(caseRepository.queryByParam(Mockito.any(RiskCaseDOQueryParamDTO.class)))
                .thenReturn(unhandledRiskCaseList);
    }

    /**
     * 测试正常关闭未处理的风险事件
     */
//    @Test
//    public void testRunSuccess() throws Exception {
//        unhandledRiskCaseList.add(RiskCaseDO.builder().status(RiskCaseStatusEnum.NO_DISPOSAL).build());
//        unhandledRiskCaseList.add(RiskCaseDO.builder().status(RiskCaseStatusEnum.IN_DISPOSAL).build());
//
//        // 运行
//        closeUnhandledRiskCaseCrane.run();
//        // 验证
//        Mockito.verify(caseRepository).batchSave(Mockito.argThat(new ArgumentMatcher<List<RiskCaseDO>>() {
//            @Override
//            public boolean matches(List<RiskCaseDO> argument) {
//                // 忽略时间匹配，直验证结果
//                return RiskCaseStatusEnum.MANUAL_DISPOSED.equals(argument.get(0).getStatus()) &&
//                        RiskCaseStatusEnum.MANUAL_DISPOSED.equals(argument.get(1).getStatus());
//            }
//        }));
//    }

    @Test
    public void testNoNeedRun() throws Exception {
        // 运行
        closeUnhandledRiskCaseCrane.run();

        // 验证不需要 batchSave 关闭未完成的风险事件
        Mockito.verify(caseRepository, Mockito.times(0)).batchSave(Mockito.anyList());
    }
}
