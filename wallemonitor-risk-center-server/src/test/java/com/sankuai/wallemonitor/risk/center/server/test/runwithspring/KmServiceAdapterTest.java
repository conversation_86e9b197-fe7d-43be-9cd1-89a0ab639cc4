package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.ImmutableMap;
import com.sankuai.ead.citadel.document.parser.DocumentParsingException;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.KmServiceAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultMissCaseDetailDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultMissCaseDetailDTO.IdentifyProcessDetail;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultMissCaseDetailDTO.IdentifyProcessInfo;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultOverviewDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultSceneDataDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultSceneDataDTO.MissCaseAndTimes;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.CreateExperimentalResultParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.KmCreateContentParamVTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
public class KmServiceAdapterTest extends SpringTestBase {

    @Resource
    private KmServiceAdapter kmServiceAdapter;

    @Test
    public void testCopyContent() {

        KmCreateContentParamVTO paramVTO = KmCreateContentParamVTO.builder()
                .operatorEmpId(20680932)
                .title("Create by Java")
                .copyFromContentId("4297783416")
                .content("")
                .parentId("4297783416")
                .build();

        String content = kmServiceAdapter.createContent(paramVTO);
        System.out.println(content);
    }

    @Test
    public void testCreateContent() throws DocumentParsingException {
        // 数据概览
        List<String> content = Arrays.asList("优化123", "优化456");
        ExperimentalResultOverviewDTO overviewDTO = ExperimentalResultOverviewDTO.builder()
                .experimentalTime(new Date())
                .experimentalContent(content)
                .personMis("lijiayan09")
                .personName("李佳宴")
                .round(10)
                .dataset("精简风险测试集")
                .datasetDataCount(102)
                .recallCount(918)
                .missCount(82)
                .failureCount(2)
                .accuracyCount(800)
                .build();

        // 场景数据
        ExperimentalResultSceneDataDTO scene1 = ExperimentalResultSceneDataDTO.builder()
                .categoryEnum(ISCheckCategoryEnum.IN_MIDDLE_ROAD)
                .actualCaseCount(1000)
                .recallCaseCount(980)
                .missedEvents(Arrays.asList(
                        MissCaseAndTimes.builder().caseId("MT0790220241024102140S03T01").times(1).build())).build();
        ExperimentalResultSceneDataDTO scene2 = ExperimentalResultSceneDataDTO.builder()
                .categoryEnum(ISCheckCategoryEnum.STOP_ON_ROAD_SIDE)
                .actualCaseCount(1000)
                .recallCaseCount(890)
                .missedEvents(Arrays.asList(
                        MissCaseAndTimes.builder().caseId("MT0790220241024102140S03T02").times(1).build(),
                        MissCaseAndTimes.builder().caseId("MT0790220241024102140S03T03").times(3).build())).build();

        // 漏召事件列表详情
        ExperimentalResultMissCaseDetailDTO missCaseDetailDTO1 = ExperimentalResultMissCaseDetailDTO.builder()
                .caseId("MT0790220241024102140S03T01")
                // .trueCategory(ISCheckCategoryEnum.AT_ACCIDENT_SCENE)
                .missCount(10).identifyProcessInfo(Arrays.asList(
                        IdentifyProcessInfo.builder().category(ISCheckCategoryEnum.STOP_ON_ROAD_SIDE).count(8)
                                .detail(ImmutableMap.of("前视", IdentifyProcessDetail.builder().info("机动车道#停车线")
                                        .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                        .build(), "后视", IdentifyProcessDetail.builder().info("机动车道#停车线")
                                        .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                        .build())).build(),
                        IdentifyProcessInfo.builder().category(ISCheckCategoryEnum.IN_MIDDLE_ROAD).count(2)
                                .detail(ImmutableMap.of("前视", IdentifyProcessDetail.builder().info("机动车道#停车线")
                                        .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                        .build())).build())
                ).build();
        ExperimentalResultMissCaseDetailDTO missCaseDetailDTO2 = ExperimentalResultMissCaseDetailDTO.builder()
                .caseId("MT0790220241024102140S03T01")
                .trueCategory(ISCheckCategoryEnum.WAITING_RIDER_TAKING_ORDER)
                .missCount(99)
                .identifyProcessInfo(Arrays.asList(
                        IdentifyProcessInfo.builder().category(ISCheckCategoryEnum.CONFLICT_WITH_PASSAGER).count(1)
                                        .detail(ImmutableMap.of("后视", IdentifyProcessDetail.builder().info("机动车道#停车线")
                                                .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                                .build())).build(),
                                IdentifyProcessInfo.builder().category(ISCheckCategoryEnum.CONFLICT_WITH_PASSAGER).count(98)
                                        .detail(ImmutableMap.of("环视", IdentifyProcessDetail.builder().info("机动车道#停车线")
                                                .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                                .build())).build()
                        )
                ).build();

        // 创建文档
        CreateExperimentalResultParamVTO param = CreateExperimentalResultParamVTO.builder()
                .operatorEmpId(20680932)
                .title("测试环境km")
                .overview(overviewDTO)
                .sceneDataList(Arrays.asList(scene1, scene2))
                .missCaseDetailList(Arrays.asList(missCaseDetailDTO1, missCaseDetailDTO2))
                .build();
        kmServiceAdapter.createExperimentalResultContent(param);
    }
}
