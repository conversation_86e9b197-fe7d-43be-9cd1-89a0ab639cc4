package com.sankuai.wallemonitor.risk.center.server.test.unit.utiltest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.davidmoten.rtree.Entry;
import com.github.davidmoten.rtree.RTree;
import com.github.davidmoten.rtree.geometry.Geometries;
import com.github.davidmoten.rtree.geometry.Geometry;
import com.github.davidmoten.rtree.geometry.Point;
import com.github.davidmoten.rtree.geometry.Rectangle;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.Test;

import java.util.Iterator;

public class RtreeTest {


    @Test
    public void testRtree() {
        RTree<RtreeKey, Rectangle> rTree = RTree.maxChildren(6).create();

        String area1Str = "{\"pointGcjList\":[{\"latitude\":40.10136483785112,\"longitude\":116.54884874779492,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54884874779492,40.10136483785112]},{\"latitude\":40.10138535422641,\"longitude\":116.54926381962935,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54926381962935,40.10138535422641]},{\"latitude\":40.10134586019993,\"longitude\":116.54929265339621,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54929265339621,40.10134586019993]},{\"latitude\":40.10133457618187,\"longitude\":116.54887087602032,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54887087602032,40.10133457618187]}]}";
        String areaId1 = "b910f59a-4a85-4a8c-bfea-42a576dab097";

        String area2Str = "{\"pointGcjList\":[{\"latitude\":40.08848076746961,\"longitude\":116.54090940656008,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54090940656008,40.08848076746961]},{\"latitude\":40.08847877693169,\"longitude\":116.54118494648372,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54118494648372,40.08847877693169]},{\"latitude\":40.088425473182205,\"longitude\":116.54118493990892,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54118493990892,40.088425473182205]},{\"latitude\":40.08842776349104,\"longitude\":116.54090949971,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54090949971,40.08842776349104]}]}";
        String areaId2 = "5453be73318c45b7a40ae1cd30047f5d";

        SafetyAreaDO.Polygon area1 = JacksonUtils.from(area1Str, new TypeReference<SafetyAreaDO.Polygon>() {
        });

        SafetyAreaDO.Polygon area2 = JacksonUtils.from(area2Str, new TypeReference<SafetyAreaDO.Polygon>() {
        });

        PositionDO positionDO = GeoToolsUtil.calculateCenter(area1.getPointGcjList());



        Point searchPoint = Geometries.point(116.5490690242102, 40.10135765711483);


        rTree = rTree.add(RtreeKey.builder().areaId(areaId1).polygonDO(area1.getPolygonDO()).build(),
                GeoToolsUtil.getBoundingRectanglePolygon(area1.getPolygonDO()));

        rTree = rTree.add(RtreeKey.builder().areaId(areaId2).polygonDO(area2.getPolygonDO()).build(),
                GeoToolsUtil.getBoundingRectanglePolygon(area2.getPolygonDO()));

        for (Entry<RtreeKey, Rectangle> next : rTree.search(searchPoint).toBlocking().toIterable()) {
            RtreeKey value = next.value();
            if (value.getPolygonDO().isInPolygon(positionDO)) {
                System.out.println(value.getAreaId());
            }
        }
     }

     @Builder
     @AllArgsConstructor
     @NoArgsConstructor
     @Data
     static class RtreeKey {
        String areaId;
        PolygonDO  polygonDO;
     }
}
