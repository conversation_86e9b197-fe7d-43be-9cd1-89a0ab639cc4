package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMrmMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.server.StartApp;
import com.sankuai.wallemonitor.risk.center.server.consumer.RiskCaseMrmConsumer;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public class RiskCaseDisposeConsumerTest {

    /**
     * {"type":30,"body":{"vin":"LMTZSV024MC048701","traceId":"s20-175_1720407707572","mrmSeatMisId":"limuyong","status":20,"mrmRole":1,"mrmSeatNo":"sz-91","requestSeatTime":1720407718000},"timestamp":1720407718293}
     */
    @Resource
    private RiskCaseMrmConsumer riskCaseMrmConsumer;


    @Test
    @SneakyThrows
    public void testConsumer() {
        EveMqCommonMessage<RiskCaseMrmMessageDTO> message = JacksonUtils.from(
                "{\"type\":30,\"body\":{\"vin\":\"LMTZSV024MC048701\",\"traceId\":\"s20-175_1720407707572\",\"mrmSeatMisId\":\"limuyong\",\"status\":40,\"mrmRole\":1,\"mrmSeatNo\":\"sz-91\",\"requestSeatTime\":1720407718000,\"seatExitTime\":1720407741874},\"timestamp\":1720407741879}",
                new TypeReference<EveMqCommonMessage<RiskCaseMrmMessageDTO>>() {
                });
        message.getBody().setVin("LMTZSV026MC093297");
        message.getBody().setTraceId("s20-175_1720407707572");
        riskCaseMrmConsumer.receive(JacksonUtils.to(message));
    }


}
