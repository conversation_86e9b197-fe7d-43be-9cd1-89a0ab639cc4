package com.sankuai.wallemonitor.risk.center.server.test.unit.utiltest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.PolygonUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class PolygonUtilTest {

    @Test
    public void testCreatePolygon() {
        Double centerLat = 0.0;
        Double centerLon = 0.0;

        List<PositionDO> polygon = PolygonUtil.createPolygon(
                centerLat, centerLon, 1.0, 4, CoordinateSystemEnum.GCJ02);

        polygon.add(PositionDO.builder().latitude(0.0).longitude(0.0).build());
    }

    @Test
    public void testCreateHullTex() {

        String positionString = "[{'latitude': 22.731407157080017,\n" +
                "  'longitude': 114.37506617339534,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37506617339534, 22.731407157080017]},\n" +
                " {'latitude': 22.73147372694949,\n" +
                "  'longitude': 114.37501253864495,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37501253864495, 22.73147372694949]},\n" +
                " {'latitude': 22.731805276540353,\n" +
                "  'longitude': 114.3747509237405,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.3747509237405, 22.731805276540353]},\n" +
                " {'latitude': 22.731592795646932,\n" +
                "  'longitude': 114.3749193420955,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.3749193420955, 22.731592795646932]},\n" +
                " {'latitude': 22.7323474239125,\n" +
                "  'longitude': 114.37432025426136,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37432025426136, 22.7323474239125]},\n" +
                " {'latitude': 22.731665725565705,\n" +
                "  'longitude': 114.37486944826955,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37486944826955, 22.731665725565705]},\n" +
                " {'latitude': 22.731811361184867,\n" +
                "  'longitude': 114.37475330367114,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37475330367114, 22.731811361184867]},\n" +
                " {'latitude': 22.731484404615628,\n" +
                "  'longitude': 114.37500465387816,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37500465387816, 22.731484404615628]},\n" +
                " {'latitude': 22.73144999568564,\n" +
                "  'longitude': 114.37502850405923,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37502850405923, 22.73144999568564]},\n" +
                " {'latitude': 22.73135763830221,\n" +
                "  'longitude': 114.37510521347131,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37510521347131, 22.73135763830221]},\n" +
                " {'latitude': 22.731724682389086,\n" +
                "  'longitude': 114.3748146834825,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.3748146834825, 22.731724682389086]},\n" +
                " {'latitude': 22.731348656138415,\n" +
                "  'longitude': 114.37511653013325,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37511653013325, 22.731348656138415]},\n" +
                " {'latitude': 22.73191684342838,\n" +
                "  'longitude': 114.37466474961883,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37466474961883, 22.73191684342838]},\n" +
                " {'latitude': 22.73158887573858,\n" +
                "  'longitude': 114.37492126305416,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37492126305416, 22.73158887573858]},\n" +
                " {'latitude': 22.731358818195442,\n" +
                "  'longitude': 114.37510457770065,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37510457770065, 22.731358818195442]},\n" +
                " {'latitude': 22.73145185718778,\n" +
                "  'longitude': 114.37502776769037,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37502776769037, 22.73145185718778]},\n" +
                " {'latitude': 22.73158239995123,\n" +
                "  'longitude': 114.37492716800264,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37492716800264, 22.73158239995123]},\n" +
                " {'latitude': 22.731816146919023,\n" +
                "  'longitude': 114.37474349108481,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37474349108481, 22.731816146919023]},\n" +
                " {'latitude': 22.731605194871484,\n" +
                "  'longitude': 114.37490900030767,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37490900030767, 22.731605194871484]},\n" +
                " {'latitude': 22.731355515781715,\n" +
                "  'longitude': 114.37510179798562,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37510179798562, 22.731355515781715]},\n" +
                " {'latitude': 22.73170708005762,\n" +
                "  'longitude': 114.37483172193792,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37483172193792, 22.73170708005762]},\n" +
                " {'latitude': 22.731633760813057,\n" +
                "  'longitude': 114.37488426826263,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37488426826263, 22.731633760813057]},\n" +
                " {'latitude': 22.731664398517257,\n" +
                "  'longitude': 114.37486335688223,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37486335688223, 22.731664398517257]},\n" +
                " {'latitude': 22.73167749787611,\n" +
                "  'longitude': 114.37485953443078,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37485953443078, 22.73167749787611]},\n" +
                " {'latitude': 22.73152639931767,\n" +
                "  'longitude': 114.37497124981624,\n" +
                "  'coordinateSystem': 'GCJ02',\n" +
                "  'pointList': [114.37497124981624, 22.73152639931767]}]";

        List<PositionDO> positionDOList = JacksonUtils.from(positionString, new TypeReference<List<PositionDO>>() {
        });

        List<PositionDO> convexHull = PolygonUtil.computeConvexHull(positionDOList);


        System.out.println(JacksonUtils.to(convexHull));

    }
}
