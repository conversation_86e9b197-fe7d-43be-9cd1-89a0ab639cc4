package com.sankuai.wallemonitor.risk.center.server.test.unit.crane;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.MoveCarEventConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.SafetyAreaQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import com.sankuai.wallemonitor.risk.center.server.crane.ReportMoveCarOrderCrane;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


public class ReportMoveCarCraneTest extends ServiceTestBase {


    @Spy
    @InjectMocks
    ReportMoveCarOrderCrane reportMoveCarOrderCrane;



    @Before
    public void setUp() {
        Mockito.doReturn(SafetyAreaDO.builder().areaId("aaa").type("ParkingAreaLot").build())
                .when(safetyAreaRepository).querySafetyAreaDOByPosition(Mockito.any());

        VehicleEveInfoVTO vehicleEveInfoVTO1 = VehicleEveInfoVTO.builder().vin("111").purpose("目的1").build();
        VehicleEveInfoVTO vehicleEveInfoVTO2 = VehicleEveInfoVTO.builder().vin("222").purpose("目的2").build();

        MoveCarEventConfigDTO move = JacksonUtils.from("{\"maxReportNum\":10,\"eventReportValidDurationHour\":24,\"craneQueryValidDurationMin\":180,\"cancelTimeoutUnDisposedDurationSec\":70,\"filters\":[]}", new TypeReference<MoveCarEventConfigDTO>() {
        });

        ReflectionTestUtils.setField(reportMoveCarOrderCrane, "moveCarEventConfigDTO", move);
        ReflectionTestUtils.setField(reportMoveCarOrderCrane, "carPurposeGrayConfig", new HashMap<Integer, Set<String>>() {{
            put(1, Sets.newHashSet());
        }});

        Mockito.doReturn(Lists.newArrayList(vehicleEveInfoVTO1, vehicleEveInfoVTO2))
                .when(vehicleAdapter).queryRuntimeVehicleInfo(Mockito.any());

    }


    @Test
    public void testReport() {
        SafetyAreaDO safetyAreaDO = safetyAreaRepository.querySafetyAreaDOByPosition(null);
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO1 = RiskCaseVehicleRelationDO.builder().vin("111").type(RiskCaseTypeEnum.VEHICLE_STAND_STILL).build();
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO2 = RiskCaseVehicleRelationDO.builder().vin("222").type(RiskCaseTypeEnum.MOVE_CAR_EVENT).build();
        ArrayList<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOArrayList =
                Lists.newArrayList(riskCaseVehicleRelationDO1, riskCaseVehicleRelationDO2);

        Object o = ReflectionTestUtils.invokeMethod(reportMoveCarOrderCrane, "getNeedToReportRiskCaseInfo", riskCaseVehicleRelationDOArrayList);


        System.out.println(o);
    }

    @Test
    public void testFilter() {

        MoveCarEventConfigDTO m = JacksonUtils.from("{\"maxReportNum\":10,\"eventReportValidDurationHour\":24,\"craneQueryValidDurationMin\":180,\"cancelTimeoutUnDisposedDurationSec\":70,\"filters\":[\"#speed != null && #speed > 0\",\"#stagnateDuration < 60\",\"#areaType == 'ParkingLotArea'\"]}", new TypeReference<MoveCarEventConfigDTO>() {
        });
        List<String> filters = m.getFilters();

        Map<String, Object> map = new HashMap<>();

        map.put("vin", "LMTZSV028NC063879");
        map.put("stagnateDuration", 1048);
        map.put("inParkingArea", true);
        map.put("areaType", "ParkingLotArea");

        String filterBy = filters.stream()
                //做逻辑判断
                .filter(filter -> SpELUtil.evaluateBoolean(filter, map)).findFirst()
                //过滤
                .orElse(CharConstant.CHAR_EMPTY);

        System.out.println(filterBy);
        System.out.println(StringUtils.isNotBlank(filterBy));
    }
}
