package com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base;

import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.HdMapRepository;
import javax.annotation.Resource;
import org.junit.Test;

public class HdMapRepositoryImplTest extends SpringTestBase {

    @Resource
    private HdMapRepository hdMapRepository;

    @Test
    public void testGetHdMapByVin() {
        String vin = "LMVK1234567890123";
        // HdMapDO hdMapDO = hdMapRepository.isInHdMapAreaWGS84(HdMapLaneAreaTypeEnum.LIVING_AREA, p, "1.0.0", vin)
        // assertNotNull(hdMapDO);

    }

}