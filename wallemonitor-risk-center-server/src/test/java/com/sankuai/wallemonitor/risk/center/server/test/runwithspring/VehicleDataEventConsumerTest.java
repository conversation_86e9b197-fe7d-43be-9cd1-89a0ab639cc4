package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMessageDTO;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleEventDataDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.StartApp;
import com.sankuai.wallemonitor.risk.center.server.consumer.RiskCaseEventConsumer;
import com.sankuai.wallemonitor.risk.center.server.consumer.VehicleDataEventConsumer;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public class VehicleDataEventConsumerTest {

//    private String start = "{\"eventId\":\"20240617184839021_common398_s20-173\",\"eventCode\":398,\"eventName\":\"SIDE_BY_SIDE_START\",\"eventTimestamp\":1718621319021,\"senderTimestamp\":1718621319025,\"receiverTimestamp\":1718621461364,\"vin\":\"LMTZSV026NC099389\",\"vehicleId\":\"M5230\",\"vehicleName\":\"s20-173\",\"recordName\":\"20240617_181709_s20-173\",\"utmZone\":50,\"utmX\":\"669795.27756003733\",\"utmY\":\"4342593.1008461136\",\"datasource\":\"autocar\",\"content\":{\"start_timestamp\":\"1718781324016845824\",\"end_timestamp\":\"1718781353616864512\",\"append_message\":\"s20 side by side start\"}}";
//    private String end = "{\"eventId\":\"20240617184839021_common398_s20-173\",\"eventCode\":399,\"eventName\":\"SIDE_BY_SIDE_END\",\"eventTimestamp\":1718621319021,\"senderTimestamp\":1718621319025,\"receiverTimestamp\":1718621461364,\"vin\":\"LMTZSV026NC099389\",\"vehicleId\":\"M5230\",\"vehicleName\":\"s20-173\",\"recordName\":\"20240617_181709_s20-173\",\"utmZone\":50,\"utmX\":\"669795.27756003733\",\"utmY\":\"4342593.1008461136\",\"datasource\":\"autocar\",\"content\":{\"start_timestamp\":\"1718781324016845824\",\"end_timestamp\":\"1718781353616864512\",\"append_message\":\"s20 side by side start\"}}";

    //20240619151602420_common399_s20-230

    @Resource
    private IDGenerateRepository idGenerateRepository;
//
    private String start = "{\"event_id\":\"20240624140413471_common398_s20-178\",\"eventCode\":398,\"eventName\":\"SIDE_BY_SIDE_START\",\"eventTimestamp\":1719209053471,\"senderTimestamp\":1719209053517,\"receiverTimestamp\":1719209053827,\"vin\":\"LMTZSV023MC063495\",\"vehicleId\":\"M2326\",\"vehicleName\":\"s20-178\",\"recordName\":\"20240624_122526_s20-178\",\"utmZone\":50,\"utmX\":\"231094.61068511268\",\"utmY\":\"2514981.7924595727\",\"datasource\":\"autocar\",\"content\":{\"start_timestamp\":\"1719209043458009344\",\"end_timestamp\":\"1719209053458042112\",\"append_message\":\"s20 side by side start\"}}";
    private String end = "{\"event_id\":\"20240624140415163_common399_s20-178\",\"eventCode\":399,\"eventName\":\"SIDE_BY_SIDE_END\",\"eventTimestamp\":1719209055163,\"senderTimestamp\":1719209055230,\"receiverTimestamp\":1719209055571,\"vin\":\"LMTZSV023MC063495\",\"vehicleId\":\"M2326\",\"vehicleName\":\"s20-178\",\"recordName\":\"20240624_122526_s20-178\",\"utmZone\":50,\"utmX\":\"231096.85993697637\",\"utmY\":\"2514972.1740117297\",\"datasource\":\"autocar\",\"content\":{\"start_timestamp\":\"1719209043458009344\",\"end_timestamp\":\"1719209055161325568\",\"append_message\":\"s20 side by side end\"}}";


    private static String start2 = "{\"event_id\":\"20240625122134153_common398_s20-456\",\"event_code\":398,\"event_name\":\"SIDE_BY_SIDE_START\",\"eventTimestamp\":1719289294153,\"senderTimestamp\":1719289294181,\"receiverTimestamp\":1719289294586,\"vin\":\"LMTZSV029NC065527\",\"vehicleId\":\"M5790\",\"vehicleName\":\"s20-456\",\"recordName\":\"20240625_121446_s20-456\",\"utmZone\":50,\"utmX\":\"231357.30302448777\",\"utmY\":\"2514302.4968560818\",\"datasource\":\"autocar\",\"content\":{\"start_timestamp\":\"1719289283948989952\",\"end_timestamp\":\"1719289294148944640\",\"append_message\":\"s20 side by side start\"}}";
    private static String end2 = "{\"event_id\":\"20240625122134455_common399_s20-456\",\"event_code\":399,\"event_name\":\"SIDE_BY_SIDE_END\",\"eventTimestamp\":1719289294455,\"senderTimestamp\":1719289294482,\"receiverTimestamp\":1719289294858,\"vin\":\"LMTZSV029NC065527\",\"vehicleId\":\"M5790\",\"vehicleName\":\"s20-456\",\"recordName\":\"20240625_121446_s20-456\",\"utmZone\":50,\"utmX\":\"231358.22364198885\",\"utmY\":\"2514300.1986026261\",\"datasource\":\"autocar\",\"content\":{\"start_timestamp\":\"1719289283948989952\",\"end_timestamp\":\"1719289294448965632\",\"append_message\":\"s20 side by side end\"}}";

    private static String start3 = "{\"event_id\":\"20240703194625040_traffic-jam_s20-191\",\"event_code\":5003,\"event_name\":\"TRAFFIC_JAM\",\"event_timestamp\":1720007185040,\"sender_timestamp\":1720007185040,\"receiver_timestamp\":1720007185064,\"vin\":\"LMTZSV029MC063825\",\"vehicle_id\":\"M9216\",\"vehicle_name\":\"s20-191\",\"record_name\":\"\",\"utm_zone\":-1,\"utm_x\":\"\",\"utm_y\":\"\",\"datasource\":\"sceneranking\",\"content\":{\"need_handle\":true,\"stuck_vehicle_set\":[\"s20-191\",\"s20-194\",\"s20-250\"],\"source_event_code\":1003,\"source_event_id\":\"20240703194555037_traffic-jam_755964c6-7a9f-4d6c-a646-5b2570a9df64\"}}";
    private static String end3 = "{\"event_id\":\"20240703194700062_traffic-jam-end_s20-191\",\"event_code\":5004,\"event_name\":\"TRAFFIC_JAM_END\",\"event_timestamp\":1720007220062,\"sender_timestamp\":1720007220062,\"receiver_timestamp\":1720007220084,\"vin\":\"LMTZSV029MC063825\",\"vehicle_id\":\"M9216\",\"vehicle_name\":\"s20-191\",\"record_name\":\"\",\"utm_zone\":-1,\"utm_x\":\"\",\"utm_y\":\"\",\"datasource\":\"sceneranking\",\"content\":{\"start_time\":1720007155037,\"recall_time\":1720007185040,\"end_time\":1720007185040,\"in_junction\":false,\"x\":669832.4680385906,\"y\":4342641.579783007,\"initial_vehicle_set\":[\"s20-191\",\"s20-194\",\"s20-250\"],\"recall_vehicle_set\":[\"s20-191\",\"s20-194\",\"s20-250\"],\"accumulate_vehicle_set\":[\"s20-191\",\"s20-194\",\"s20-250\"],\"stuck_vehicle_set\":[\"s20-191\",\"s20-194\",\"s20-250\"],\"source_event_code\":1004,\"source_event_id\":\"20240703194555037_traffic-jam_755964c6-7a9f-4d6c-a646-5b2570a9df64\"}}";

    private static String start4 = "{\"event_id\":\"20240711101433957_adc-stagnant-recall_MKZ-08\",\"event_code\":5006,\"event_name\":\"ADC_STAGNANT_RECALL\",\"event_timestamp\":1720664100000,\"sender_timestamp\":1720664073957,\"receiver_timestamp\":1720664073975,\"vin\":\"LMTZSV026MC093297\",\"vehicle_id\":\"12000016\",\"vehicle_name\":\"MKZ-08\",\"record_name\":\"\",\"utm_zone\":-1,\"utm_x\":\"\",\"utm_y\":\"\",\"datasource\":\"sceneranking\",\"content\":{\"trace_id\":\"MKZ-08_1720663763992\",\"recall_type\":2,\"stagnant_time_in_auto\":50076,\"stagnant_time_in_any_mode\":50076,\"scene_type\":0,\"source_event_code\":1006,\"source_event_id\":\"20240711101433957_adc-stagnant-recall_MKZ-08\"}}";
    private static String end4 = "{\"event_id\":\"202407111014123123_adc-stagnant-recall_MKZ-08\",\"event_code\":5007,\"event_name\":\"ADC_STAGNANT_END\",\"event_timestamp\":1720664101453,\"sender_timestamp\":1720664101453,\"receiver_timestamp\":1720664101471,\"vin\":\"3LN6L5SU2LR601635\",\"vehicle_id\":\"12000016\",\"vehicle_name\":\"MKZ-08\",\"record_name\":\"\",\"utm_zone\":-1,\"utm_x\":\"\",\"utm_y\":\"\",\"datasource\":\"sceneranking\",\"content\":{\"trace_id\":\"MKZ-08_1720663763992\",\"recall_type\":1,\"stagnant_time_in_auto\":-1,\"stagnant_time_in_any_mode\":-1,\"scene_type\":0,\"source_event_code\":1007,\"source_event_id\":\"20240711101433957_adc-stagnant-recall_MKZ-08\"}}";
    private String s_start = "{\"type\":20,\"body\":{\"eventId\":\"1719191170_3LN6L5SU2LR601635\",\"source\":3,\"type\":1,\"status\":10,\"vinList\":[\"3LN6L5SU2LR601635\"],\"traceId\":\"\"},\"timestamp\":1719191170850}";
    private String s_end = "{\"type\":20,\"body\":{\"eventId\":\"1719191170_3LN6L5SU2LR601635\",\"source\":3,\"type\":1,\"status\":30,\"vinList\":[\"3LN6L5SU2LR601635\"],\"traceId\":\"\"},\"timestamp\":1719191170850}";


    private String s_o_start = "{\"type\":20,\"body\":{\"eventId\":\"1719191170_LMTZSV026MC093297\",\"source\":3,\"type\":1,\"status\":10,\"vinList\":[\"LMTZSV026MC093297\"],\"traceId\":\"\"},\"timestamp\":1719191170850}";
    private String s_o_end = "{\"type\":20,\"body\":{\"eventId\":\"1719191170_LMTZSV026MC093297\",\"source\":3,\"type\":1,\"status\":30,\"vinList\":[\"LMTZSV026MC093297\"],\"traceId\":\"\"},\"timestamp\":1719191170850}";


    private String start5 = "{\"event_id\":\"20240819165646614_adc-stagnant-recall_s20-465\",\"event_code\":5006,\"event_name\":\"ADC_STAGNANT_RECALL\",\"event_timestamp\":1724057806614,\"sender_timestamp\":1724057806614,\"receiver_timestamp\":1724057806372,\"vin\":\"LMTZSV025NC091137\",\"vehicle_id\":\"M7026\",\"vehicle_name\":\"s20-465\",\"record_name\":\"\",\"utm_zone\":-1,\"utm_x\":\"\",\"utm_y\":\"\",\"datasource\":\"sceneranking\",\"content\":{\"trace_id\":\"s20-465_1724057806549\",\"recall_type\":2,\"stagnant_time_in_auto\":50075,\"stagnant_time_in_any_mode\":50075,\"scene_type\":0,\"source_event_code\":1006,\"source_event_id\":\"20240819165646614_adc-stagnant-recall_s20-465\"}}";
    @Resource
    private VehicleDataEventConsumer vehicleDataEventConsumer;

    @Resource
    private RiskCaseRepository riskCaseRepository;
    @Resource
    private RiskCaseEventConsumer riskCaseConsumer;


    @Test
    public void testStart() {
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start, VehicleEventDataDO.class);
        s_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis());
        s_vehicleEventDataDO.setReceiverTimestamp(System.currentTimeMillis());
        s_vehicleEventDataDO.setEventId(System.currentTimeMillis() + "");
        vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
    }

    @Test
    @SneakyThrows
    public void testConsumer() {
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start4, VehicleEventDataDO.class);
        s_vehicleEventDataDO.setVin("LMTZSV023MC023286");
        s_vehicleEventDataDO.setEventId(idGenerateRepository.generateEventId(new Date(),
                RiskCaseTypeEnum.VEHICLE_STAND_STILL.name(), "s20-465"));
        //s_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis());
        VehicleEventDataDO e_vehicleEventDataDO = JacksonUtils.from(end4, VehicleEventDataDO.class);
        e_vehicleEventDataDO.setVin("LMTZSV023MC023286");
        e_vehicleEventDataDO.setEventTimestamp(DatetimeUtil.getNSecondsAfterDateTime(new Date(), 10).getTime());
        //e_vehicleEventDataDO.setEventTimestamp(DatetimeUtil.addSeconds(new Date(), 10));
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
        // 提交一个延时执行的任务，延时3秒执行，并返回Future对象
        Future<ConsumeStatus> resultFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        }, 0, TimeUnit.SECONDS);
        resultFuture.get();
    }


    @Test
    @SneakyThrows
    public void testConsumerEndBeforeStart() {
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start2, VehicleEventDataDO.class);
        //s_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis());
        VehicleEventDataDO e_vehicleEventDataDO = JacksonUtils.from(end2, VehicleEventDataDO.class);
        //e_vehicleEventDataDO.setEventTimestamp(DatetimeUtil.addSeconds(new Date(), 10));
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
        // 提交一个延时执行的任务，延时3秒执行，并返回Future对象
//        Future<ConsumeStatus> resultFuture = scheduler.schedule(() -> {
//            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
//            return consumeStatus;
//        }, 0, TimeUnit.SECONDS);
//        resultFuture.get();

        Future<ConsumeStatus> resultFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        }, 1, TimeUnit.SECONDS);
        resultFuture.get();
    }

    @Test
    @SneakyThrows
    public void testConsumerParallel() {
        int i = 0;
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1000);
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start2, VehicleEventDataDO.class);
        VehicleEventDataDO e_vehicleEventDataDO = JacksonUtils.from(end2, VehicleEventDataDO.class);
        //直接替换了
        vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
        // 提交一个延时执行的任务，延时3秒执行，并返回Future对象
        Future<ConsumeStatus> resultFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);
        //拦截逻辑
        // ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
    }


    @Test
    @SneakyThrows
    public void testRiskQuery() {
        riskCaseRepository.queryByParamByPage(RiskCaseDOQueryParamDTO.builder()
                        .createTimeRange(
                                TimePeriod.builder().beginDate(DatetimeUtil.getTodayStartDate()).endDate(new Date()).build())
                        .build(), 1,
                10);
        riskCaseRepository.queryByParam(RiskCaseDOQueryParamDTO.builder()
                .createTimeRange(
                        TimePeriod.builder().beginDate(DatetimeUtil.getTodayStartDate()).endDate(new Date()).build())
                .build());
    }


    @Test
    @SneakyThrows
    public void testConsumerTrafficJamStart() {
        // 正常调用事件start
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start3, VehicleEventDataDO.class);
        s_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis());
        s_vehicleEventDataDO.setReceiverTimestamp(System.currentTimeMillis());
        s_vehicleEventDataDO.setEventId(System.currentTimeMillis() + "");
        vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));

    }

    @Test
    @SneakyThrows
    public void testConsumerTrafficJamEnd() {
        // 正常调用事件end
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(end3, VehicleEventDataDO.class);
        s_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis());
        s_vehicleEventDataDO.setReceiverTimestamp(System.currentTimeMillis());
        s_vehicleEventDataDO.setEventId(System.currentTimeMillis() + "");
        vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));

    }


    @Test
    @SneakyThrows
    public void testConsumerTrafficJamNormal() {
        // 测试正常调用：先用事件end，再用start，正确运行
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
        // 修改在时间戳，让重试控制在可重试时间戳内
        VehicleEventDataDO e_vehicleEventDataDO = JacksonUtils.from(end3, VehicleEventDataDO.class);
        e_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis());
        Future<ConsumeStatus> e_resultFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
                Thread.sleep(100);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);

        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start3, VehicleEventDataDO.class);
        Future<ConsumeStatus> s_resultFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
                Thread.sleep(100);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);
        // 等待上面运行完
        e_resultFuture.get();
        s_resultFuture.get();
    }

    @Test
    public void testConsumerStandStillStart() {
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start4, VehicleEventDataDO.class);
        vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
    }

    @Test
    @SneakyThrows
    public void testConsumerStandStillEnd() {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(100);
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start4, VehicleEventDataDO.class);
        // 修改start时间戳在end发生前的第一个
        s_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis() - 1);
        vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
        VehicleEventDataDO e_vehicleEventDataDO = JacksonUtils.from(end4, VehicleEventDataDO.class);
        // e_vehicleEventDataDO 必须在 可重试时间戳 内
        e_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis());
        Future<ConsumeStatus> e_resultFuture1 = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
                Thread.sleep(100);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);
        e_resultFuture1.get();

    }

    @Test
    public void testConsumerStandStillStart2() {

        // 停滞不当两个来源同时到达，只落一个
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start4, VehicleEventDataDO.class);
        EveMqCommonMessage<RiskCaseMessageDTO> s1_vehicleEventDataDO = JacksonUtils.from(s_o_start,
                new TypeReference<EveMqCommonMessage<RiskCaseMessageDTO>>() {
                });
        vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
        riskCaseConsumer.receive(JacksonUtils.to(s1_vehicleEventDataDO));
    }

    @Test
    @SneakyThrows
    public void testConsumerStandStillEnd2() {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(100);

        // 停滞不当两个来源的结束先到，然后两个来源的开始再到，只会落一个且状态为30
        // 因为没有对应的结果，所以会返回fail，自动重试
        // 开始消息时间结束消息时间都要尽量设置现实时间，先后
        VehicleEventDataDO e_vehicleEventDataDO = JacksonUtils.from(end4, VehicleEventDataDO.class);
        e_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis());
        EveMqCommonMessage<RiskCaseMessageDTO> e1_vehicleEventDataDO = JacksonUtils.from(s_end,
                new TypeReference<EveMqCommonMessage<RiskCaseMessageDTO>>() {
                });
        e1_vehicleEventDataDO.setTimestamp(System.currentTimeMillis());
        VehicleEventDataDO s_vehicleEventDataDO = JacksonUtils.from(start4, VehicleEventDataDO.class);
        s_vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis() - 100);
        EveMqCommonMessage<RiskCaseMessageDTO> s1_vehicleEventDataDO = JacksonUtils.from(s_start,
                new TypeReference<EveMqCommonMessage<RiskCaseMessageDTO>>() {
                });
        s1_vehicleEventDataDO.setTimestamp(System.currentTimeMillis() - 100);
        // 停滞不当结束来源a
        Future<ConsumeStatus> e_resultFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(e_vehicleEventDataDO));
                Thread.sleep(1000);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);
        // 停滞不当结束来源b
        Future<ConsumeStatus> e_resultFuture1 = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = riskCaseConsumer.receive(JacksonUtils.to(e1_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = riskCaseConsumer.receive(JacksonUtils.to(e1_vehicleEventDataDO));
                Thread.sleep(200);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);

        // 停滞不当开始来源a
        Future<ConsumeStatus> s_resultFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
                Thread.sleep(200);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);

        // 停滞不当开始来源b
        vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
        Future<ConsumeStatus> s_resultFuture1 = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = riskCaseConsumer.receive(JacksonUtils.to(s1_vehicleEventDataDO));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = riskCaseConsumer.receive(JacksonUtils.to(s1_vehicleEventDataDO));
                Thread.sleep(200);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);
        e_resultFuture.get();
        e_resultFuture1.get();
        s_resultFuture.get();
        s_resultFuture1.get();

    }


    /**
     * 消费停滞不当和扎堆的消息
     */
    @Test
    @SneakyThrows
    public void testConsumerStandStillAndJam() {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(100);
        Long startTimeStamp = System.currentTimeMillis();
        Long endTimeStamp = System.currentTimeMillis() + 100;
        String vin = "LMTZSV029MC063825";
        VehicleEventDataDO standStart = JacksonUtils.from(start4, VehicleEventDataDO.class);
        VehicleEventDataDO standEnd = JacksonUtils.from(end4, VehicleEventDataDO.class);
        VehicleEventDataDO jamStart = JacksonUtils.from(start3, VehicleEventDataDO.class);
        VehicleEventDataDO jamEnd = JacksonUtils.from(end3, VehicleEventDataDO.class);
        //设置时间戳
        standStart.setEventTimestamp(startTimeStamp);
        jamStart.setEventTimestamp(startTimeStamp);
        standEnd.setEventTimestamp(endTimeStamp);
        jamEnd.setEventTimestamp(endTimeStamp);
        //设置车辆
        standStart.setVin(vin);
        standEnd.setVin(vin);
        jamStart.setVin(vin);
        jamEnd.setVin(vin);
        //先消费结束，然后消费开始
        Future<ConsumeStatus> standEndFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(standEnd));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(standEnd));
                Thread.sleep(1000);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);
        Future<ConsumeStatus> jamStartFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(jamStart));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(jamStart));
                Thread.sleep(100);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);
        Future<ConsumeStatus> jamEndFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(jamEnd));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(jamEnd));
                Thread.sleep(100);
            }
            return consumeStatus;
        }, 0, TimeUnit.MILLISECONDS);
        //消费开始
        Future<ConsumeStatus> standStartFuture = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(standStart));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(standStart));
                Thread.sleep(100);
            }
            return consumeStatus;
        }, 100, TimeUnit.MILLISECONDS);
        standEndFuture.get();
        jamStartFuture.get();
        jamEndFuture.get();
        standStartFuture.get();

    }

}
