package com.sankuai.wallemonitor.risk.center.server.test.unit.utiltest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;

public class SpELUtilTest {

    @Test
    public void testParseExpression() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        VehicleRuntimeInfoContextDO contextDO = VehicleRuntimeInfoContextDO.builder().vin("LA71AUB13S0501757")
                .vehicleLaneSectionType("middle").lng("123.123").lat("45.123").build();
        stringObjectMap.put("runtimeInfo", contextDO);
        String a = "{\"IN_JUNCTION\":\"#runtimeInfo.distanceToJunction != null and #runtimeInfo.distanceToJunction >=0 and #runtimeInfo.distanceToJunction <= 10.0\",\"IN_MIDDLE_ROAD\":\"(#runtimeInfo.distanceToJunction == -1 or #runtimeInfo.distanceToJunction == null or #runtimeInfo.distanceToJunction > 10.0 ) and {'left','middle'}.contains(#runtimeInfo.vehicleLaneSectionType)  and #runtimeInfo.frontObstacle == null \",\"STOP_BY_OBSTACLE\":\"(#runtimeInfo.distanceToJunction == -1 or #runtimeInfo.distanceToJunction == null or #runtimeInfo.distanceToJunction > 10.0 ) and {'left','middle'}.contains(#runtimeInfo.vehicleLaneSectionType) and #runtimeInfo.frontObstacle != null \",\"STOP_ON_ROAD_SIDE\":\"(#runtimeInfo.distanceToJunction == -1 or #runtimeInfo.distanceToJunction == null or #runtimeInfo.distanceToJunction > 10.0 ) and {'right'}.contains(#runtimeInfo.vehicleLaneSectionType) and #runtimeInfo.frontObstacle == null \",\"STOP_BY_INACTIVE_VEHICLE\":\"(#runtimeInfo.distanceToJunction == -1 or #runtimeInfo.distanceToJunction == null or #runtimeInfo.distanceToJunction > 10.0 ) and {'right'}.contains(#runtimeInfo.vehicleLaneSectionType) and #runtimeInfo.frontObstacle != null \"}";
        Map<String, String> map = JacksonUtils.from(a, new TypeReference<Map<String, String>>() {});
        map.forEach((str, rule) -> {
            SpELUtil.evaluateBoolean(rule, stringObjectMap);
        });

    }

    @Test
    public void testParseExpression2() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("riskCase", RiskCaseDO.builder().type(RiskCaseTypeEnum.RETROGRADE).build());
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = VehicleRuntimeInfoContextDO.builder().build();
        vehicleRuntimeInfoContextDO.setVin("LSTM");
        vehicleRuntimeInfoContextDO.setLng("123.123");
        vehicleRuntimeInfoContextDO.setLat("45.123");
        VehicleEveInfoVTO vehicleEveInfoVTO = VehicleEveInfoVTO.builder().hdMapVersion("123213123").build();
        DetectContextDTO contextDTO = DetectContextDTO.builder().build();
        contextDTO.setRuntimeContext(vehicleRuntimeInfoContextDO);
        contextDTO.setEveInfo(vehicleEveInfoVTO);
        Object result = SpELUtil.evaluateWithVariables(
                "#StringUtils.isBlank('')",
                contextDTO.getSpelContext(), Object.class);

    }
}