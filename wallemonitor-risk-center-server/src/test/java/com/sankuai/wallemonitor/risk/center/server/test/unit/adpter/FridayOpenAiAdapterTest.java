package com.sankuai.wallemonitor.risk.center.server.test.unit.adpter;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.FridayOpenAiAdapter;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FridayOpenAiAdapterTest extends ServiceTestBase {

    @Spy
    @InjectMocks
    FridayOpenAiAdapter fridayOpenAiAdapter;


    @Test
    public void testGetOpenAiAnswer() {
        FridayOpenAiAdapter.OpenAiReqDTO openAiReqDTO = new FridayOpenAiAdapter.OpenAiReqDTO();

        openAiReqDTO.setModel("risk_check_qwen_2_5_32");

        String imageUrl = "https://walle.sankuai.com/replay/video/avatarV2?vin=LMTZSV029MC034454&view=front&time=20250507082224";

        FridayOpenAiAdapter.Content userTextContent = FridayOpenAiAdapter.Content.builder().type("text").text("根据图片回答问题").build();
        FridayOpenAiAdapter.Content userImageContent1 = FridayOpenAiAdapter.Content.builder().type("image_url").imageUrl(
                FridayOpenAiAdapter.ImageUrl.builder().url(imageUrl).build()).build();

        FridayOpenAiAdapter.ReqMessage userPrompt = FridayOpenAiAdapter.ReqMessage.builder().role("user").content(Lists.newArrayList(userTextContent, userImageContent1)).build();
        openAiReqDTO.setMessages(Lists.newArrayList(userPrompt));

        String appId = "1840301155550003252";

        ReflectionTestUtils.invokeMethod(fridayOpenAiAdapter, "getOpenAiAnswer", openAiReqDTO, appId, 10);
    }

    @Test
    public void testOutputParse() {
        String input = "类型 : 静 promotional antioxid 逆行停滞, 是否需要接管: 是 ";

        // 定义正则表达式
        String regex = "类型\\s*:\\s*(.*?),\\s*是否需要接管\\s*:\\s*(.*?)(?=\\s|$)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String type = matcher.group(1).trim();      // 提取类型并去除前后空格
            String needTakeOver = matcher.group(2).trim(); // 提取是否需要接管并去除前后空格

            System.out.println("类型: " + type);
            System.out.println("是否需要接管: " + needTakeOver);
        } else {
            System.out.println("未匹配到结果");
        }
    }
}
