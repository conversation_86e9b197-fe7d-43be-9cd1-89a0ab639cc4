package com.sankuai.wallemonitor.risk.center.server.test.unit.domain.process;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseRelationCounterCallSafetyProcess;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import javafx.util.Pair;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class RiskCaseRelationCounterCallSafetyProcessTest extends ServiceTestBase {


    @InjectMocks
    @Spy
    RiskCaseRelationCounterCallSafetyProcess riskCaseRelationCounterCallSafetyProcess;


    @Test
    public void testGray() {

        String callSecuritySystemConfig = "{\n" +
                "  \"1\": {\n" +
                "    \"source\": 5,\n" +
                "    \"type\": 1,\n" +
                "    \"reason\": 6001,\n" +
                "    \"requestSource\": 11,\n" +
                "    \"filters\": [\n" +
                "      \"#vhr != '>1'\",\n" +
                "      \"#riskDuration == null || #riskDuration < 30\",\n" +
                "      \"#mrmStatus != 0 && #mrmStatus != 1 && #mrmStatus != 3\",\n" +
                "      \"#hasAccident == true\",\n" +
                "      \"#hasReOrder == true\",\n" +
                "      \"#hasRescueOrder == true\",\n" +
                "      \"#improperStrandingReason?.withRescueOrder == true\",\n" +
                "      \"#improperStrandingReason?.withAccidentOrder == true\",\n" +
                "      \"#improperStrandingReason?.withReOrder == true\",\n" +
                "      \"#improperStrandingReason?.withManualParking == true\",\n" +
                "      \"#businessType != '路测' && #purpose != '业务运营'\",\n" +
                "      \"!{'IN_MIDDLE_ROAD','OPPSITE_ROAD_DIRECTION','CONFLICT_WITH_PASSAGER','STOP_BY_OBSTACLE','IN_JUNCTION','IN_VALID_DRIVING_AREA'}.contains(#firstSubCategory)\",\n" +
                "      \"#firstSubCategory == 'IN_JUNCTION' and #vehicleRuntimeInfo.findTrafficLightType() != null and  #vehicleRuntimeInfo.findTrafficLightType().code == 1\",\n" +
                "      \"#firstSubCategory == 'STOP_BY_OBSTACLE' and #item?.checkResult?.extra?.get('crossLine') == true\",\n" +
                "      \"#vehicleRuntimeInfo.speed > 0.2\",\n" +
                "      \"(#vehicleRuntimeInfo.findCounterDuration('greenLightCounter') >= 0 and #vehicleRuntimeInfo.findCounterDuration('greenLightCounter') <= 3) or (#vehicleRuntimeInfo.findCounterDuration('greenLightCounter') < 0 and #vehicleRuntimeInfo.findTrafficLightType() != null and #vehicleRuntimeInfo.findTrafficLightType().code == 3)\"\n" +
                "    ]\n" +
                "  },\n" +
                "  \"2\": {\n" +
                "    \"source\": 5,\n" +
                "    \"type\": 13,\n" +
                "    \"reason\": 6003,\n" +
                "    \"requestSource\": 11,\n" +
                "    \"filters\": [\n" +
                "      \"#vhr != '>1'\",\n" +
                "      \"!{'路测-提速测试-北京','云辅助专项','路测-AB后沙峪测试','路测-云辅助beta-深圳'}.contains(#purpose)\"\n" +
                "    ]\n" +
                "  },\n" +
                "  \"3\": {\n" +
                "    \"source\": 5,\n" +
                "    \"type\": 15,\n" +
                "    \"reason\": 6004,\n" +
                "    \"requestSource\": 11,\n" +
                "    \"filters\": [\n" +
                "      \"#vhr != '>1'\",\n" +
                "      \"!{'路测-提速测试-北京','云辅助专项','路测-AB后沙峪测试','路测-云辅助beta-深圳'}.contains(#purpose)\"\n" +
                "    ]\n" +
                "  },\n" +
                "  \"4\": {\n" +
                "    \"source\": 5,\n" +
                "    \"type\": 12,\n" +
                "    \"reason\": 6005,\n" +
                "    \"requestSource\": 11,\n" +
                "    \"filters\": [\n" +
                "      \"#vhr != '>1'\",\n" +
                "      \"#mrmStatus != 0 && #mrmStatus != 1 && #mrmStatus != 3\",\n" +
                "      \"#hasAccident == true\",\n" +
                "      \"#hasReOrder == true\",\n" +
                "      \"#hasRescueOrder == true\",\n" +
                "      \"#improperStrandingReason?.withRescueOrder == true\",\n" +
                "      \"#improperStrandingReason?.withAccidentOrder == true\",\n" +
                "      \"#improperStrandingReason?.withReOrder == true\",\n" +
                "      \"#improperStrandingReason?.withManualParking == true\",\n" +
                "      \"#purpose == '业务运营'\",\n" +
                "      \"#businessType != '路测' && #purpose != '业务运营'\"\n" +
                "    ]\n" +
                "  },\n" +
                "  \"5\": {\n" +
                "    \"source\": 5,\n" +
                "    \"type\": 16,\n" +
                "    \"reason\": 6006,\n" +
                "    \"requestSource\": 11,\n" +
                "    \"filters\": [\n" +
                "      \"#vin != 'LA71AUB18R0515972'\"\n" +
                "    ]\n" +
                "  }\n" +
                "}";
        Map<Pair<Integer, Integer>, LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO> config = JacksonUtils.from(callSecuritySystemConfig,
                new TypeReference<Map<String, LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO>>() {
                }).entrySet().stream().collect(Collectors.toMap(
                entry -> new Pair<>(entry.getValue().getSource(), entry.getValue().getType()),
                Map.Entry::getValue
        ));

        List<RiskCaseVehicleRelationDO> vehicleRelationDOList = new ArrayList<>();

        vehicleRelationDOList.add(RiskCaseVehicleRelationDO.builder().purpose("业务运营").build());
        vehicleRelationDOList.add(RiskCaseVehicleRelationDO.builder().purpose("业务运营").build());

        config.values().forEach(configDTO -> {
            Object o = ReflectionTestUtils.invokeMethod(riskCaseRelationCounterCallSafetyProcess, "isVehicleRelationInGray", vehicleRelationDOList, configDTO);

            System.out.println(o);
        });


    }
}
