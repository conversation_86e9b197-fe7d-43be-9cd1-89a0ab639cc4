package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.StagnationEventAccidentTagMarkProcess;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseSortDataDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseSortDataRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseSortDataDOQueryParam;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

public class CaseSortDataRepositoryImplTest extends SpringTestBase {

    @Resource
    private CaseSortDataRepository caseSortDataRepository;

    @Resource
    private StagnationEventAccidentTagMarkProcess markProcess;

    @Test
    public void test() {
        caseSortDataRepository.batchSave(Lists.newArrayList(CaseSortDataDO.builder()
                .caseId("123")
                .problem("自动驾驶问题")
                .sorter("kongjian")
                .build()));

        CaseSortDataDOQueryParam param = CaseSortDataDOQueryParam.builder().build();
        List<CaseSortDataDO> result = caseSortDataRepository.queryByParam(param);
        System.out.println(JacksonUtils.to(result));
    }

    @Test
    public void testMark() {
        List<RiskCaseDO> eventDOList = new ArrayList<>();
        RiskCaseDO riskCaseDO = new RiskCaseDO();
        riskCaseDO.setCaseId("M135720250702105900S05T09");
        riskCaseDO.setEventId("20250702105900000_STRANDING_s20-479");
        eventDOList.add(riskCaseDO);
        markProcess.handleProcessMsg(eventDOList);
    }
}
