package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.davidmoten.rtree.geometry.Geometries;
import com.github.davidmoten.rtree.geometry.Point;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import com.sankuai.wallemonitor.risk.center.server.test.unit.utiltest.DateTimeUtilsTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class SafetyAreaRepositryTest extends SpringTestBase {

    @Resource
    SafetyAreaRepository safetyAreaRepository;

    @Test
    public void testSafetyQuery() {
        safetyAreaRepository.isInDelayRecallPolygon(PositionDO.builder().build(), new Date());
    }

    @Test
    public void testInSafetyArea() {
        String area1Str = "{\"pointGcjList\":[{\"latitude\":40.10136483785112,\"longitude\":116.54884874779492,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54884874779492,40.10136483785112]},{\"latitude\":40.10138535422641,\"longitude\":116.54926381962935,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54926381962935,40.10138535422641]},{\"latitude\":40.10134586019993,\"longitude\":116.54929265339621,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54929265339621,40.10134586019993]},{\"latitude\":40.10133457618187,\"longitude\":116.54887087602032,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.54887087602032,40.10133457618187]}]}";

        SafetyAreaDO.Polygon area1 = JacksonUtils.from(area1Str, new TypeReference<SafetyAreaDO.Polygon>() {
        });

        PositionDO positionDO = GeoToolsUtil.calculateCenter(area1.getPointGcjList());


        safetyAreaRepository.verifyInParkingArea(positionDO);
        safetyAreaRepository.querySafetyAreaDOByPosition(positionDO);

    }
}
