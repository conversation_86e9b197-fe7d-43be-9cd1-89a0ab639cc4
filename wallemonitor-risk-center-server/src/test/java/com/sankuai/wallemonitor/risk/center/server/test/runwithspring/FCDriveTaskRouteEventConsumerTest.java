package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.dto.FCDriveTaskRouteEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.FCDriveTaskRouteEventDTO.RoutePoint;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.server.StartApp;
import com.sankuai.wallemonitor.risk.center.server.consumer.FCDriveTaskRouteEventConsumer;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public class FCDriveTaskRouteEventConsumerTest {

    @Resource
    private FCDriveTaskRouteEventConsumer consumer;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Test
    public void test() {

        //        String vin = "LMTZSV027MC042469";
        //        // 1. 查询车辆上下文
        //        VehicleRuntimeInfoContextDO contextDO = vehicleRuntimeInfoContextRepository.getFromCache(vin);
        //        if (contextDO == null) {
        //            contextDO = new VehicleRuntimeInfoContextDO();
        //            contextDO.setVin(vin);
        //        }
        //        // 2. 将路由信息更新到车辆运行时上下文
        //        //        contextDO.setRoutePoints(eventDTO.getRoutePoints());
        //        contextDO.setLng("113.31439382802999");
        //        // 更新到缓存
        //        vehicleRuntimeInfoContextRepository.updateCache(contextDO, System.currentTimeMillis());

        String message = "{\"vin\":\"LMTZSV020NC032917\",\"route_points\":[{\"x\":194009.23169019105,\"y\":2518013.1577684227,\"z\":39.12780064570675,\"route_s\":0.0},{\"x\":194028.93825105156,\"y\":2517857.9217970027,\"z\":39.71312161371015,\"route_s\":156.4846573841528},{\"x\":194025.0757623919,\"y\":2517843.783308565,\"z\":39.702781803617086,\"route_s\":171.4846573841528},{\"x\":194010.73625334195,\"y\":2517835.6006441764,\"z\":40.07326879343297,\"route_s\":188.4846573841528},{\"x\":193740.1670354337,\"y\":2517807.7760500144,\"z\":39.009376067882286,\"route_s\":460.4846573841528},{\"x\":193666.44677678568,\"y\":2517802.1714486913,\"z\":37.126289938276585,\"route_s\":534.4846573841528},{\"x\":193651.96556401163,\"y\":2517805.08533085,\"z\":37.12,\"route_s\":549.4846573841528},{\"x\":193634.0531740722,\"y\":2517883.2329473905,\"z\":36.85669225639849,\"route_s\":630.4846573841528},{\"x\":193615.2610964918,\"y\":2518018.9267900675,\"z\":35.76123468572015,\"route_s\":767.4846573841528},{\"x\":193605.2787183866,\"y\":2518061.69974197,\"z\":35.91105403756743,\"route_s\":811.4846573841528},{\"x\":193580.38396481093,\"y\":2518109.536783872,\"z\":35.98876275123108,\"route_s\":865.4846573841528},{\"x\":193377.54900912053,\"y\":2518434.3654064965,\"z\":40.097877218388824,\"route_s\":1248.4846573841528},{\"x\":193355.87280495436,\"y\":2518489.089501023,\"z\":40.798320705756765,\"route_s\":1307.4846573841528},{\"x\":193340.75611103975,\"y\":2518571.669508448,\"z\":40.80905269631855,\"route_s\":1391.4846573841528},{\"x\":193333.97753808703,\"y\":2518625.9065914145,\"z\":40.98821283580211,\"route_s\":1446.4846573841528},{\"x\":193343.97004289954,\"y\":2518649.111032646,\"z\":41.169446947430146,\"route_s\":1472.4846573841528},{\"x\":193524.50157138283,\"y\":2518731.8717055395,\"z\":43.171201175413934,\"route_s\":1671.4846573841528},{\"x\":193699.01921745637,\"y\":2518797.292900721,\"z\":42.823250740005456,\"route_s\":1858.4846573841528},{\"x\":193714.4544882978,\"y\":2518788.6651638495,\"z\":42.35187555551732,\"route_s\":1876.4846573841528},{\"x\":193961.38593753896,\"y\":2518201.6797521147,\"z\":38.319637621245626,\"route_s\":2513.484657384153},{\"x\":193991.04290181724,\"y\":2518104.1466839206,\"z\":38.640309877629505,\"route_s\":2615.484657384153},{\"x\":194015.293426002,\"y\":2517966.372463,\"z\":39.28,\"route_s\":2755.484657384153}],\"sd_route_points\":null,\"gaode_estimated_duration\":null,\"fc_estimated_duration\":509.0268431817124,\"estimated_time_of_arrival\":1745494314823,\"update_timestamp\":1745493807066,\"route_info_id\":\"COCKPIT_CHAUFFEUR_LMTZSV020NC032917_2025-04-24_19:23:25_8946\",\"utm_zone\":50}";
        // 1 消息体解析
        FCDriveTaskRouteEventDTO eventDTO = JacksonUtils.from(message,
                new TypeReference<FCDriveTaskRouteEventDTO>() {});

        List<RoutePoint> threeHound = new ArrayList<>(eventDTO.getRoutePoints());
        while (threeHound.size() <= 300) {
            threeHound.addAll(eventDTO.getRoutePoints());
        }
        eventDTO.setRoutePoints(threeHound);
        consumer.receive(JacksonUtils.to(eventDTO));
    }


}
