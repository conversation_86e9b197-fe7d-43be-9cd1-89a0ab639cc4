package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.AdminListRiskCaseRequest;
import com.sankuai.wallemonitor.risk.center.api.response.AdminGetRiskCaseDetailResponse;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListRiskCaseVO;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftAdminService;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * @<PERSON> k<PERSON><PERSON>an
 * @Date 2024/7/2
 */
public class IThriftAdminServiceTest extends SpringTestBase {

    @Resource
    private IThriftAdminService service;

    /**
     *
     */
    @Test
    public void test() {
        EveThriftResponse<AdminGetRiskCaseDetailResponse> result = service.getRiskCaseDetail(
                "e9ec4ee51f99488baeb1fbb9fbe8b333");
        System.out.println(JacksonUtils.to(result.getData()));
    }

    @Test
    public void testListCase() {
        AdminListRiskCaseRequest request = AdminListRiskCaseRequest.builder()
                .createTimeStart("2025-05-22 00:00:00")
                .createTimeEnd("2025-25-23 23:59:59")
                //                .mrmIntervened(true)
                //                .problemList(Lists.newArrayList("自动驾驶问题/算法问题"))
                //                .poiNameList(Lists.newArrayList("测试POI"))
                .vehicletypeList(Lists.newArrayList("H24"))
                .pageNum(1)
                .pageSize(3)
                .build();
        EveThriftPageResponse<List<AdminListRiskCaseVO>> result = service.listRiskCase(request);
        System.out.println(JacksonUtils.to(result.getData()));
    }

    @Test
    public void testListCase1() {
        AdminListRiskCaseRequest request = AdminListRiskCaseRequest.builder()
                .createTimeStart("2024-07-01 00:00:00")
                .createTimeEnd("2024-08-30 23:59:59")
//                .categoryList(Lists.newArrayList("BAD"))
                .purposeList(Lists.newArrayList("业务运营"))
                .callSafetyList(Lists.newArrayList(0, 10))
                .pageNum(1)
                .pageSize(3)
                .build();
        EveThriftPageResponse<List<AdminListRiskCaseVO>> result = service.listRiskCase(request);
        System.out.println(JacksonUtils.to(result.getData()));
    }
}
