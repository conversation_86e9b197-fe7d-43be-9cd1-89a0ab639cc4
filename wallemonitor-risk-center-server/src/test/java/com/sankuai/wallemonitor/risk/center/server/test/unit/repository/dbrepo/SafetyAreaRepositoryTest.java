package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.dbrepo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.davidmoten.rtree.Entry;
import com.github.davidmoten.rtree.geometry.Geometry;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.convert.SafetyAreaConvert;
import com.sankuai.wallemonitor.risk.center.infra.convert.SafetyAreaConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.SafetyArea;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.SafetyAreaRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;


import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;


@Slf4j
@Ignore
public class SafetyAreaRepositoryTest extends RepositoryTestBase {

    @Spy
    private SafetyAreaRepositoryImpl safetyAreaRepository;

    private Set<String> existParkingAreas;

    private List<SafetyAreaDO> safetyAreaList ;

    @Before
    public void setUp() {
        SafetyAreaConvertImpl safetyAreaConvert = new SafetyAreaConvertImpl();

        String safetyAreaPath = "/Volumes/文枢工作空间/下载/safety_area.json";


        List<SafetyArea> safetyAreaList = JacksonUtils.from(readFiles(safetyAreaPath), new TypeReference<List<SafetyArea>>() {
        });
        List<SafetyAreaDO> safetyAreaDOList = safetyAreaList.stream().map(safetyAreaConvert::toDO).collect(Collectors.toList());
        this.safetyAreaList = safetyAreaDOList;

        existParkingAreas = safetyAreaDOList.stream().map(SafetyAreaDO::getAreaId).collect(Collectors.toSet());

        Mockito.doReturn(safetyAreaDOList).when(safetyAreaRepository).queryByParam(Mockito.any());
        // 把safety area 刷上去
        safetyAreaRepository.areaElementCache.refresh("PARKING_AREA");

    }


    @Test
    public void testVerifyInParkingArea() throws ExecutionException {

        String riskCaseLocationPath = "/Users/<USER>/code/python/risk/park_area_test/vehicle_location.json";
        String parkAreaIdPath = "/Users/<USER>/code/python/risk/park_area_test/park_result.json";

        List<PositionDO> positionDOList = JacksonUtils.from(readFiles(riskCaseLocationPath), new TypeReference<List<PositionDO>>() {
        });

        List<String> areaIdList = JacksonUtils.from(readFiles(parkAreaIdPath), new TypeReference<List<String>>() {
        });

        double total = 0.0;


        for(int i = 0; i < positionDOList.size(); i ++) {
            PositionDO positionDO = positionDOList.get(i);
            String areaId = areaIdList.get(i);
            if (!existParkingAreas.contains(areaId)) {
                continue;
            }
            safetyAreaRepository.verifyInParkingArea(positionDO);
//            boolean b = pairs.stream().anyMatch(x -> x.getKey().equals(areaId));
//            if (CollectionUtils.isEmpty(pairs) || !b) {
//                log.error("PositionDO = {}, expect area id = {}, but not found", JacksonUtils.to(positionDO), areaId);
//                total ++;
//            }
        }

        System.out.println(total / positionDOList.size());




    }

    private String readFiles(String filePath) {
        Path path = Paths.get(filePath);
        try {
            List<String> lines = Files.readAllLines(path);
            String jsonString = String.join("\n", lines);
            return jsonString;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


}
