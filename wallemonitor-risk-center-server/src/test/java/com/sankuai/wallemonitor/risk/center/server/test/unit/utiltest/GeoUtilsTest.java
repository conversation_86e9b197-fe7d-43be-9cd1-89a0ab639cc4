package com.sankuai.wallemonitor.risk.center.server.test.unit.utiltest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.introspect.AnnotatedMember;
import com.fasterxml.jackson.databind.introspect.JacksonAnnotationIntrospector;
import com.github.davidmoten.rtree.geometry.Geometries;
import com.github.davidmoten.rtree.geometry.Point;
import com.google.common.collect.Lists;
import com.sankuai.walle.operator.client.util.CoordinateTransferUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleeve.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.api.vo.HdMapElementGeoVO;
import com.sankuai.wallemonitor.risk.center.infra.convert.HdMapElementPolygonConvert;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.OnboardCommonMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CaseMarkInfoExtDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckingExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO.Polygon;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GeometryPolygonVTO;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.junit.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.operation.distance.DistanceOp;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;

public class GeoUtilsTest {
    // FAASCAmEwggJdAgEAAoGBAOWx77lDEDDzBe
    private static final String secret = "FAASCAmEwggJdAgEAAoGBAOWx77lDEDDzBe";

    // http[s]://manager-hdmap.mad.test.sankuai.com/
    private static final String hdMapManagerUrl = "https://manager-hdmap.mad.test.sankuai.com/";

    // api/map/lonlat/to/utm?lon=116.47752546003994&lat=39.8757798297267
    private static final String apiMapLonlatToUtm = "api/map/lonlat/to/utm?lon=%s&lat=%s";

    private static final String clientId = "hdmap";

    /**
     * jts工厂类
     */
    private static final GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory();
    private static final double METER_PER_LATITUDE = 111000.0;

    @Test
    public void testConvert() {
//        double[][] points = {
//                {197619.9487893844, 2514165.669171509},
//                {197628.04334900016, 2514158.661842282},
//                {197634.75490614818, 2514143.6757327863},
//                {197637.09313658217, 2514126.183786922},
//                {197629.56144606916, 2514103.6916837464},
//                {197593.62858942477, 2514132.6886386746}
//        };
//        // 转换为List<Double[]>
//        List<Double[]> listPoints = new ArrayList<>();
//        for (double[] point : points) {
//            Double[] pointArray = {point[0], point[1]};
//            listPoints.add(pointArray);
//        }
//        List<Double[]> wgs84 = new ArrayList<>();
//        List<Double[]> gcj02 = new ArrayList<>();
//        listPoints.forEach(doubles -> {
//            Double[] listWgs84 = GeoToolsUtil.utmToWgs84(doubles[0], doubles[1]);
//            Double[] listGcj02 = GeoToolsUtil.utmToGcj02(doubles[0], doubles[1]);
//            Double[] newWgs84List = {listWgs84[1], listWgs84[0]};
//            Double[] newGcj02List = {listGcj02[0], listGcj02[1]};
//            wgs84.add(newWgs84List);
//            gcj02.add(newGcj02List);
//        });
//        System.out.println("wgs84: " + JacksonUtils.toJson(wgs84));
//        System.out.println("gcj02: " + JacksonUtils.toJson(gcj02));

        double[][] pointsGcj02 = {
                {114.02629100792429, 22.62049364239194},
                {114.02742676673464, 22.620892124465076},
                {114.02745011591873, 22.620819577929595},
                {114.02633811391952, 22.620436466424888}
        };
        // 转换为List<Double[]>
        List<Double[]> listPointsGcj02 = new ArrayList<>();
        for (double[] point : pointsGcj02) {
            Double[] pointArray = {point[0], point[1]};
            listPointsGcj02.add(pointArray);
        }
        List<Double[]> wgs84 = new ArrayList<>();
        List<Double[]> wgs84Re = new ArrayList<>();
        List<Double[]> gcjReList = new ArrayList<>();
        listPointsGcj02.forEach(doubles -> {
            PositionDO listWgs84 = GeoToolsUtil.gcj02Wgs84(doubles[0], doubles[1]);
            PositionDO gcjRe = GeoToolsUtil.wgs84ToGcj02(listWgs84.getLongitude(), listWgs84.getLatitude());
            wgs84.add(new Double[]{listWgs84.getLatitude(), listWgs84.getLongitude()});
            wgs84Re.add(new Double[]{listWgs84.getLongitude(), listWgs84.getLatitude()});
            gcjReList.add(new Double[]{gcjRe.getLongitude(), gcjRe.getLatitude()});
        });
        System.out.println("gcj02:" + JacksonUtils.to(pointsGcj02));
        System.out.println("wgs84: " + JacksonUtils.to(wgs84));
        System.out.println("wgs84Re: " + JacksonUtils.to(wgs84Re));
        System.out.println("gcjReList: " + JacksonUtils.to(gcjReList));
    }

    @Test
    public void testConvert2() {
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = JacksonUtils.from(
                "{\"vin\":\"LMTZSV020MC021026\",\"driveMode\":\"NO_CONTROL\",\"speed\":0.0,\"lng\":\"0.000000\",\"lat\":\"0.000000\",\"batterySwitching\":false,\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"trafficLightType\":\"NONE\",\"distanceToNextJunction\":-1,\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"waitingGatePole\":false,\"lastUpdateTime\":\"2025-01-02 21:07:58\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-01-02 19:42:12\",\"duration\":5146,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-01-02 17:57:12\",\"endTime\":\"2025-01-02 17:57:24\",\"duration\":12,\"countFinished\":true},\"createTime\":\"2024-10-18 20:51:53\",\"updateTime\":\"2025-01-02 21:07:58\",\"isDeleted\":\"NOT_DELETED\",\"location\":{\"latitude\":0.0,\"longitude\":0.0,\"coordinateSystem\":\"WGS84\"}}",
                VehicleRuntimeInfoContextDO.class);
        Polygon polygon = JacksonUtils.from(
                "{\"pointGcjList\":[{\"latitude\":40.185223875404695,\"longitude\":116.63126820560691,\"coordinateSystem\":\"GCJ02\"},{\"latitude\":40.18521985801217,\"longitude\":116.63161410364442,\"coordinateSystem\":\"GCJ02\"},{\"latitude\":40.185051334859025,\"longitude\":116.6316130811775,\"coordinateSystem\":\"GCJ02\"},{\"latitude\":40.18506134189874,\"longitude\":116.63125706646758,\"coordinateSystem\":\"GCJ02\"}]}",
                Polygon.class);
        PositionDO positionDO = GeoToolsUtil.utmToGcj02(116.6339916285, 40.1954522625);
        System.out.println(positionDO);
    }

    @Test
    public void testConvert3() {
        String gemStr = "{\n" + "  \"type\": \"FeatureCollection\",\n" + "  \"features\": [\n" + "    {\n"
                + "      \"type\": \"Feature\",\n" + "      \"geometry\": {\n" + "        \"type\": \"Polygon\",\n"
                + "        \"coordinates\": [\n" + "          [\n"
                + "            [116.633944684, 40.1954503953, 21.765],\n"
                + "            [116.6339299582, 40.1954498878, 21.765],\n"
                + "            [116.6339272119, 40.1954507419, 21.766],\n"
                + "            [116.6339204617, 40.1954565098, 21.767],\n"
                + "            [116.6339195053, 40.1954620295, 21.857],\n"
                + "            [116.6339441791, 40.1954623444, 21.902],\n"
                + "            [116.633944684, 40.1954503953, 21.765]\n" + "          ]\n" + "        ]\n"
                + "      },\n" + "      \"properties\": {\n" + "        \"center\": [116.6339304458, 40.1954548229],\n"
                + "        \"o_type\": \"IsolationZone\"\n" + "      },\n" + "      \"id\": \"o_90f8ae88\"\n"
                + "    }\n" + "  ]\n" + "}\n";
        GeometryPolygonVTO geometryPolygonVTO = JacksonUtils.from(gemStr, GeometryPolygonVTO.class);
        HdMapElementPolygonConvert hdMapElementPolygonConvert = new HdMapElementPolygonConvert() {};
        List<HdMapElementGeoDO> hdMapElementGeoDOS = hdMapElementPolygonConvert
                .toHdMapElementPolygonDO(HdMapEnum.OBJECT.getValue(), geometryPolygonVTO.getFeatures().get(0));
        HdMapElementGeoDO hdMapElementGeoDO = hdMapElementGeoDOS.get(0);
        List<Coordinate> coordinates = hdMapElementGeoDO.getCoordinates();
        Coordinate coordinate = coordinates.get(0);
        // 116.6338682608, 40.1953449371
        Point searchPoint = Geometries.point(116.6338682608, 40.1953449371);
        org.locationtech.jts.geom.Geometry boundaryRectangle = geometryFactory
                .createPolygon(hdMapElementGeoDO.getBoundaryRectangle().toArray(new Coordinate[0]));
        // 判断最短距离
        double minDistance = DistanceOp.distance(boundaryRectangle,
                geometryFactory.createPoint(new Coordinate(searchPoint.x(), searchPoint.y())));
        CoordinateSystemEnum coordinateSystemEnum = hdMapElementGeoDO.getPolygonDO().getCoordinateSystemEnum();
        // 如果坐标系是经纬度（WGS84或GCJ02），将距离从度转换为米
        if (coordinateSystemEnum == CoordinateSystemEnum.WGS84 || coordinateSystemEnum == CoordinateSystemEnum.GCJ02) {
            minDistance = minDistance * METER_PER_LATITUDE; // 粗略地将度转换为米（1度约等于111公里）
        }
        System.out.println(minDistance);
    }

    @Test
    public void testConvert4() {
        // [116.5679422324, 39.7814959697]
        // 461112.674746797,4438958.59229753,
        // GeoToolsUtil.utmToWgs84(461112.674746797, 4438958.59229753);
        double longitude = 116.5679422324; // 经度
        double latitude = 39.7814959697;  // 纬度
        PositionDO wgs84 = GeoToolsUtil.wgs84ToUtm(longitude, latitude);
        System.out.printf(JacksonUtils.to(wgs84));
        System.out.printf(JacksonUtils.to(GeoToolsUtil.utmToWgs84(wgs84.getLongitude(), wgs84.getLatitude())));
    }

    @Test
    public void testConvert5() {
        // 1 = {PositionDO@5078} "PositionDO(latitude=39.7816404316, longitude=116.5679666916, coordinateSystem=WGS84)"
        // 2 = {PositionDO@5079} "PositionDO(latitude=39.7816212639, longitude=116.5679663187, coordinateSystem=WGS84)"
        // 3 = {PositionDO@5080} "PositionDO(latitude=39.7816047202, longitude=116.5679645969, coordinateSystem=WGS84)"
        // 4 = {PositionDO@5081} "PositionDO(latitude=39.7815866348, longitude=116.567961066, coordinateSystem=WGS84)"
        String a = "{\"elementType\":\"CITY_DRIVING\",\"mapType\":\"lane_polygon\",\"geoType\":\"POLYGON\",\"id\":\"lane_1b9bb8e4\",\"properties\":{\"lane_type\":\"CITY_DRIVING\",\"turn_type\":\"RIGHT_TURN\",\"impassable\":false,\"direction\":\"FORWARD\",\"speed_max\":60,\"speed_min\":0,\"spec_mark\":\"NONE\",\"road\":\"r_ffb5b42041cc\",\"section\":\"1\",\"sec_index\":-1,\"predecessor\":[\"lane_37e1bd50\"],\"successor\":[\"lane_bf5baf4a\"],\"left\":\"\",\"right\":\"\"},\"polygonDO\":{\"coordinateSystemEnum\":\"WGS84\",\"points\":[{\"latitude\":39.7816591957,\"longitude\":116.5679872045,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7816210097,\"longitude\":116.5679882731,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7816033417,\"longitude\":116.5679868467,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815840695,\"longitude\":116.5679831384,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.781564604,\"longitude\":116.5679779537,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815464894,\"longitude\":116.5679713987,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815292573,\"longitude\":116.5679630875,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815123086,\"longitude\":116.5679535739,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814959697,\"longitude\":116.5679422324,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814801246,\"longitude\":116.5679298092,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814609196,\"longitude\":116.5679116865,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814497023,\"longitude\":116.5678994529,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814360699,\"longitude\":116.5678829657,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814203488,\"longitude\":116.5678604099,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814005282,\"longitude\":116.5678264409,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.781432261,\"longitude\":116.5677973322,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814345275,\"longitude\":116.5678015766,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814382631,\"longitude\":116.5678044733,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814425385,\"longitude\":116.5678131983,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814433518,\"longitude\":116.5678169652,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814517496,\"longitude\":116.5678325826,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814736241,\"longitude\":116.567862606,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814864672,\"longitude\":116.5678771815,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814996009,\"longitude\":116.5678896751,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815130407,\"longitude\":116.5679011416,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815271921,\"longitude\":116.5679110784,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815416914,\"longitude\":116.5679200827,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815566061,\"longitude\":116.567927385,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815721749,\"longitude\":116.5679338505,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815891765,\"longitude\":116.5679391962,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7816215156,\"longitude\":116.5679445837,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7816396859,\"longitude\":116.5679453551,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7816576961,\"longitude\":116.5679442978,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7816591957,\"longitude\":116.5679872045,\"coordinateSystem\":\"WGS84\"}]},\"middleLinePoints\":[{\"latitude\":39.7816584418,\"longitude\":116.5679656343,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7816404316,\"longitude\":116.5679666916,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7816212639,\"longitude\":116.5679663187,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7816047202,\"longitude\":116.5679645969,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815866348,\"longitude\":116.567961066,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815684054,\"longitude\":116.5679558092,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815515671,\"longitude\":116.5679493076,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815354982,\"longitude\":116.5679415028,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.781519776,\"longitude\":116.5679322528,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7815045347,\"longitude\":116.567921616,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814898929,\"longitude\":116.5679096801,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814757176,\"longitude\":116.5678962709,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.781461696,\"longitude\":116.5678809789,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814492099,\"longitude\":116.567865341,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814377788,\"longitude\":116.5678489239,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814279253,\"longitude\":116.5678329263,\"coordinateSystem\":\"WGS84\"},{\"latitude\":39.7814164225,\"longitude\":116.5678118609,\"coordinateSystem\":\"WGS84\"}]}";
        HdMapElementGeoDO c = JacksonUtils.from(a, HdMapElementGeoDO.class);
        System.out.println(
                c.isInPolygon(PositionDO.getPosition(116.5679666916, 39.7816404316, CoordinateSystemEnum.WGS84)));
    }

    @Test
    public void testConvert6() {
        // 116.5679882731, 39.7816210097
        PositionDO p1 = new PositionDO(39.7816212639, 116.5679663187, CoordinateSystemEnum.WGS84);
        PositionDO p2 = new PositionDO(39.7816212639, 116.5679663187, CoordinateSystemEnum.WGS84);
        PositionDO p3 = new PositionDO(39.7816210097, 116.5679882731, CoordinateSystemEnum.WGS84);
        PositionDO p4 = new PositionDO(39.7816212639, 116.5679663187, CoordinateSystemEnum.WGS84);
        double angle = 30.0;
        System.out.println(GeoToolsUtil.isAngleLessThan(p1, p2, p3, p4, angle));
    }

    @Test

    public void testConvert7() {
        String a = getFromFile("");
        OnboardCommonMessageDTO<PerceptionObstacleDTO> messageDTO = JacksonUtils.from(a,
                new TypeReference<OnboardCommonMessageDTO<PerceptionObstacleDTO>>() {});
        List<PerceptionObstacle> perceptionObstacleList = messageDTO.getData().getPerceptionObstacle().stream()
                .map(perceptionObstacle -> new Pair<>(
                        GeoToolsUtil.distance(PositionDO.getPosition(114.036561, 22.694521, CoordinateSystemEnum.WGS84),
                                GeoToolsUtil.utmToWgs84(perceptionObstacle.getPosition().getX(),
                                        perceptionObstacle.getPosition().getY())),
                        Lists.newArrayList(perceptionObstacle.getObstacleType(),
                                GeoToolsUtil.utmToWgs84(perceptionObstacle.getPosition().getX(),
                                        perceptionObstacle.getPosition().getY()),
                                perceptionObstacle.getType(), perceptionObstacle)))
                .filter(doubleArrayListPair -> doubleArrayListPair.getKey() <= 11)
                .sorted(Comparator.comparing(Pair::getKey))
                .map(pair -> (PerceptionObstacle)pair.getValue().get(pair.getValue().size() - 1))
                .collect(Collectors.toList());
        System.out.println(JacksonUtils.to(perceptionObstacleList));

    }

    @Test
    public void testConvert8() {
        // [114.0364993, 22.6945268],
        // const curVinPosition = [114.036561, 22.694521];
        // const prePosition = [114.03657479137682,22.69451929755903];
        PositionDO pre = new PositionDO(22.69451929755903, 114.03657479137682, CoordinateSystemEnum.WGS84);
        PositionDO cur = new PositionDO(22.694521, 114.036561, CoordinateSystemEnum.WGS84);
        PositionDO ob = new PositionDO(22.6945268, 114.0364993, CoordinateSystemEnum.WGS84);
        GeoToolsUtil.angleCalc(pre, cur, cur, ob);
    }

    /**
     * 从resources目录下获取case文件的内容,每行是一个caseId
     *
     * @return
     */
    private String getFromFile(String fileName) {
        try {
            ClassPathResource resource = new ClassPathResource(fileName);
            return FileCopyUtils.copyToString(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8));
        } catch (IOException e) {
            return "";
        }
    }

    private static final String TEMPLATE = "update case_mark_info set first_operator = '%s' , first_sub_category = '%s', first_category = '%s', ext_info = '%s' where case_id = '%s' or case_id = '%s';";
    private static final String QUERY_TEMPLATE = "select * from  case_mark_info where case_id IN (%s)";

    @Test
    @SneakyThrows
    public void testConvert9() {
        String str = getFromFile("item.json");
        List<ItemValue> itemValues = JacksonUtils.from(str, new TypeReference<List<ItemValue>>() {});
        List<String> caseSqlList = new ArrayList<>();
        List<String> caseSqlList1 = new ArrayList<>();
        List<String> caseIdList = new ArrayList<>();
        itemValues.forEach(itemValue -> {
            Map<String, RiskCheckingQueueItemDO> valueMap = itemValue.getValueMap();
            valueMap.forEach((k, v) -> {
                if (!(v.getTmpCaseId() != null && v.getCheckResult() != null)) {
                    return;
                }
                CaseMarkInfoExtDO caseMarkInfoExtDO = new CaseMarkInfoExtDO();
                caseMarkInfoExtDO.setCheckRound(1);

                Map<String, Object> checkResult = ReflectUtils.getNonNullFieldAndValue(v.getCheckResult());
                // 获取历史checkResult列表
                List<Map<String, Object>> checkResultHistory = Optional.ofNullable(v.getExtInfo())
                        .map(RiskCheckingExtInfoDO::getLastCheckResult).orElse(new ArrayList<>()).stream()
                        .map(ReflectUtils::getNonNullFieldAndValue).collect(Collectors.toList());
                caseMarkInfoExtDO.setCheckResult(checkResult);
                caseMarkInfoExtDO.addHistoryCheckResultList(checkResultHistory);
                caseMarkInfoExtDO.setCheckRound(v.getRound());
                caseSqlList.add(String.format(TEMPLATE, v.getCheckResult().getCheckSource(),
                        v.getCheckResult().getCategory().getSubcategory(),
                        v.getCheckResult().getCategory().getCategory(), JacksonUtils.to(caseMarkInfoExtDO),
                        v.getTmpCaseId(), v.getEventId()));
                caseIdList.add(v.getTmpCaseId());
                caseIdList.add(v.getEventId());
            });
        });

        // 写入到文件
        FileUtils.writeLines(new File("case_imporse_stranding_and_stranding.sql"), caseSqlList);
//        FileUtils.writeLines(new File("query_list.sql"),
//                Lists.newArrayList(String.format(QUERY_TEMPLATE, String.join("','", caseIdList))));

    }

    @Test
    @SneakyThrows
    public void testConvert10() {
        // String a =
        // "{\"type\":\"FeatureCollection\",\"features\":[{\"type\":\"Feature\",\"geometry\":{\"type\":\"Polygon\",\"coordinates\":[[[116.633638567,40.1961239991,21.234],[116.6336380155,40.1961088429,21.287],[116.633634066,40.196094161,21.327],[116.6336308556,40.1960870738,21.323],[116.6336220854,40.1960736756,21.319],[116.6336104142,40.1960616501,21.317],[116.6335961986,40.1960513604,21.316],[116.6335798672,40.1960431202,21.321],[116.6335619185,40.1960371799,21.301],[116.6335525064,40.1960351315,21.276],[116.6335331645,40.196032956,21.184],[116.6334679856,40.1960300352,21.021],[116.6334668787,40.196056691,20.883],[116.6335599654,40.1960609493,21.209],[116.6335712537,40.19606299,21.326],[116.6335819085,40.1960665029,21.359],[116.6335916063,40.1960713813,21.34],[116.633600051,40.196077477,21.33],[116.6336098229,40.1960884883,21.324],[116.6336141177,40.1960967499,21.312],[116.6336164765,40.1961054577,21.281],[116.6336168276,40.1961143473,21.255],[116.6336153103,40.1961226497,21.231],[116.633638567,40.1961239991,21.234]]]},\"properties\":{\"lane_type\":\"MIXED\",\"turn_type\":\"RIGHT_TURN\",\"impassable\":false},\"id\":\"lane_85610cdba285\"}]}";
        // GeometryPolygonVTO geometryPolygonVTO = JacksonUtils.from(a, GeometryPolygonVTO.class);
//        List<HdMapElementGeoDO> dos = HdMapElementPolygonConvert.mapper
//                .toHdMapElementPolygonDO(HdMapEnum.LANE_POLYGON.getValue(), geometryPolygonVTO.getFeatures());
//        GeoToolsUtil.expandPolygonDO(dos.get(0).getPolygonDO(), 0);
        String a = "";
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setAnnotationIntrospector(new CustomAnnotationIntrospector());
        JavaType javaType = objectMapper.getTypeFactory().constructType(VehicleRuntimeInfoContextDO.class);
        objectMapper.readValue(a, javaType);
    }

    @Test
    @SneakyThrows
    public void testConvert11() {
        // laneId:"lane_263f92b40aa1"
        // road:"r_fa80a93cffe4"
        //
        // latitude:22.7247223
        // longitude:114.0187318
        PositionDO truckPosition = PositionDO.getPosition(114.0187318, 22.7247223, CoordinateSystemEnum.WGS84);
        // latitude:22.7247715
        // longitude:114.0187115
        PositionDO s20Position = PositionDO.getPosition(114.0187115, 22.7247715, CoordinateSystemEnum.WGS84);
        String a = "{\"laneId\":\"lane_263f92b40aa1\",\"points\":[[114.0183778626,22.7255242388],[114.0184310434,22.7254114103],[114.0185014323,22.7252613488],[114.018507614,22.7252521302],[114.018509488,22.7252440758],[114.0185560843,22.7251449028],[114.0187296004,22.7247756731],[114.0188109991,22.7246024759],[114.0188631899,22.7244982766],[114.0188676215,22.7244813731],[114.0188860525,22.7244425657],[114.0189677181,22.7242673683],[114.0189952272,22.724215542],[114.019040793,22.7241423612],[114.019065075,22.7241094247],[114.0191269761,22.7240350699],[114.0191016155,22.7240157463],[114.0190652731,22.7240563112],[114.0190461229,22.724079147],[114.0190125459,22.7241253128],[114.0190000601,22.7241441391],[114.0189694374,22.7241947002],[114.0189381809,22.7242543364],[114.0189157909,22.7243006645],[114.0188370397,22.7244691792],[114.0187896167,22.7245690801],[114.0186985425,22.724763062],[114.0185253318,22.7251324398],[114.0184844893,22.7252192791],[114.0183996911,22.7253987047],[114.0183469608,22.725511048],[114.0183778626,22.7255242388]],\"laneMiddleLinePoints\":[[114.0183622082,22.725517539],[114.0184154448,22.7254050889],[114.0185406632,22.7251386531],[114.01880574,22.7245755097],[114.0188706841,22.7244374286],[114.0189329052,22.7243078331],[114.0189776415,22.7242159222],[114.0190045355,22.7241689615],[114.0190337625,22.7241251783],[114.0190682425,22.7240801855],[114.0191148359,22.7240258196]],\"laneType\":\"CITY_DRIVING\",\"elementType\":\"CITY_DRIVING\"}";
        HdMapElementGeoVO c = JacksonUtils.from(a, HdMapElementGeoVO.class);
        List<PositionDO> points = c.getPoints().stream().map(twoPoint -> {
            return PositionDO.getPosition(twoPoint.get(0), twoPoint.get(1), CoordinateSystemEnum.WGS84);
        }).collect(Collectors.toList());
        List<Pair<Double, Pair<PositionDO, Pair<PositionDO, PositionDO>>>> distance = new ArrayList<>();
        for (int i = 0; i < points.size() - 1; i++) {
            PositionDO p1 = points.get(i);
            PositionDO p2 = points.get(i + 1);
            Pair<Double, Pair<PositionDO, Pair<PositionDO, PositionDO>>> pair = new Pair<>(
                    GeoToolsUtil.pointToLineSegmentDistance(truckPosition, p1, p2),
                    new Pair<>(truckPosition, new Pair<>(p1, p2)));
            distance.add(pair);
        }
        distance.sort(Comparator.comparing(Pair::getKey));
        System.out.println(distance.get(0));
        System.out.println(distance);

    }

    @Test
    public void testConvert12() {
        List<List<Double>> points = Arrays.asList(Arrays.asList(114.0177221728, 22.7269171363),
                Arrays.asList(114.0177929231, 22.7267524459), Arrays.asList(114.0178506696, 22.7266207754),
                Arrays.asList(114.0179014528, 22.7265072534), Arrays.asList(114.0180428535, 22.7261866939),
                Arrays.asList(114.0180137266, 22.7261756519), Arrays.asList(114.017872729, 22.726496322),
                Arrays.asList(114.0178221211, 22.7266098134), Arrays.asList(114.0178002346, 22.7266588334),
                Arrays.asList(114.0177577212, 22.7267578142), Arrays.asList(114.017737752, 22.7268043071),
                Arrays.asList(114.0177341184, 22.7268056518), Arrays.asList(114.0177323722, 22.7268130903),
                Arrays.asList(114.0177251031, 22.7268319547), Arrays.asList(114.0177094405, 22.7268692654),
                Arrays.asList(114.0176949738, 22.7269070208), Arrays.asList(114.0177221728, 22.7269171363));
        List<PositionDO> positionDOS = points.stream().map(twoPoint -> {
            return PositionDO.getPosition(twoPoint.get(0), twoPoint.get(1), CoordinateSystemEnum.WGS84);
        }).collect(Collectors.toList());
        // osition:{
//        x:193688.00976430983
//        y:2516300.2254769923
        // latitude:22.7262745
        // longitude:114.0179837
        // x:193688.00976430983
        // y:2516300.2254769923
        PositionDO truckPosition = GeoToolsUtil.utmToWgs84WithZone(193688.00976430983, 2516300.2254769923, 32650);
        // ehiclePosition:{
        // latitude:22.726319
        // longitude:114.017969
        PositionDO curPos = PositionDO.getPosition(114.017962, 22.726335, CoordinateSystemEnum.WGS84);
        // osition:{
        // latitude:22.726355
        // longitude:114.017961
        PositionDO prePos = PositionDO.getPosition(114.0179415, 22.7263831, CoordinateSystemEnum.WGS84);
        //
        System.out.println(
                GeoToolsUtil.pointToLineSegmentDistance(truckPosition, positionDOS, new Pair<>(prePos, curPos), 15D));

    }

    @Test
    public void testConvert13() {
        List<List<Double>> points;
        List<PositionDO> positionDOS;
        // osition:{
        // latitude:22.7262745
        // longitude:114.0179837
        PositionDO truckPosition;
        // ehiclePosition:{
        // latitude:22.726319
        // longitude:114.017969
        PositionDO curPos;
        // osition:{
        // latitude:22.726355
        // longitude:114.017961
        PositionDO prePos;
        points = Arrays.asList(Arrays.asList(114.0109174408, 22.6442404236),
                Arrays.asList(114.0108154997, 22.6443824599), Arrays.asList(114.0108384378, 22.6443966433),
                Arrays.asList(114.0109403693, 22.6442546011), Arrays.asList(114.0109174408, 22.6442404236));
        positionDOS = points.stream().map(twoPoint -> {
            return PositionDO.getPosition(twoPoint.get(0), twoPoint.get(1), CoordinateSystemEnum.WGS84);
        }).collect(Collectors.toList());
        // latitude:22.64425 longitude:114.010928
        curPos = PositionDO.getPosition(114.010928, 22.64425, CoordinateSystemEnum.WGS84);
        // latitude:22.6442123 longitude:114.0109539
        prePos = PositionDO.getPosition(114.0109539, 22.6442123, CoordinateSystemEnum.WGS84);
//        System.out
//                .println(GeoToolsUtil.pointToLineSegmentDistance(curPos, positionDOS, new Pair<>(prePos, curPos), 15D));
        System.out.println(
                GeoToolsUtil.pointToPolygonParallelSideDistance(curPos, positionDOS, new Pair<>(prePos, curPos), 15D));
    }

    private static class CustomAnnotationIntrospector extends JacksonAnnotationIntrospector {
        @Override
        public boolean hasIgnoreMarker(AnnotatedMember m) {
            if (m.getMember() instanceof Method) {
                Method method = (Method)m.getMember();
                String methodName = method.getName();

                if ((methodName.startsWith("get") && methodName.length() > 3)
                        || (methodName.startsWith("is") && methodName.length() > 2)) {

                    String potentialFieldName;
                    if (methodName.startsWith("get")) {
                        potentialFieldName = Character.toLowerCase(methodName.charAt(3)) + methodName.substring(4);
                    } else { // starts with "is"
                        potentialFieldName = Character.toLowerCase(methodName.charAt(2)) + methodName.substring(3);
                    }

                    // 检查是否存在对应的字段
                    try {
                        Field[] fields = method.getDeclaringClass().getDeclaredFields();
                        return Arrays.stream(fields).anyMatch(field -> field.getName().equals(potentialFieldName));
                    } catch (Exception e) {
                        // 不存在对应字段
                        return false; // 忽略这个方法
                    }
                }
            }
            return super.hasIgnoreMarker(m);
        }

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ItemValue {
        private Long expireTime;
        private Map<String, RiskCheckingQueueItemDO> valueMap;

    }

    @Test
    public void test123(){
        String s = "{\"entry\":{\"domainClassName\":\"VehicleRuntimeInfoContextDO\",\"operateEntry\":\"VEHICLE_RUNTIME_INFO_CONTEXT_UPDATE_CRANE_ENTRY\"},\"timestamp\":1747316140645,\"operator\":\"\",\"traceId\":\"-6866989452617351030#23\",\"extInfo\":{},\"before\":[{\"vin\":\"LMTZSV027NC092113\",\"driveMode\":\"NO_CONTROL\",\"speed\":0.0,\"batterySwitching\":false,\"bizStatus\":\"NO_BIZ_TRIP\",\"trafficLightType\":\"NONE\",\"distanceToNextJunction\":20,\"lng\":\"114.378822\",\"lat\":\"22.720194\",\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"waitingGatePole\":false,\"lastUpdateTime\":\"2025-05-15 21:35:37\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-05-15 15:03:36\",\"duration\":23520,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.findTrafficLightType()?.code == 1\",\"startTime\":\"2025-05-15 14:25:36\",\"endTime\":\"2025-05-15 14:26:56\",\"duration\":80,\"countFinished\":true},\"createTime\":\"2024-10-21 09:55:48\",\"updateTime\":\"2025-05-15 21:35:36\",\"isDeleted\":\"NOT_DELETED\",\"trafficFlowSpeed\":0.0,\"vehicleCounterInfo\":{},\"prePositionList\":[],\"allObstacleList\":[],\"frontObstacleList\":[],\"behindObstacleList\":[],\"usableLaneIds\":[],\"vehicleCurLaneIdList\":[],\"vehicleAroundLaneId\":{},\"distanceToNearCurbList\":[],\"singleLane\":false,\"fcLaneIdList\":[],\"gcj02Location\":{\"latitude\":22.717535149538424,\"longitude\":114.38377364481924,\"coordinateSystem\":\"GCJ02\",\"pointList\":[114.38377364481924,22.717535149538424]},\"location\":{\"latitude\":22.720194,\"longitude\":114.378822,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.378822,22.720194]}},{\"vin\":\"LMTZSV026NC076338\",\"driveMode\":\"NO_CONTROL\",\"speed\":0.0,\"batterySwitching\":false,\"bizStatus\":\"NO_BIZ_TRIP\",\"trafficLightType\":\"NONE\",\"distanceToNextJunction\":14,\"lng\":\"116.553192\",\"lat\":\"40.095978\",\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"waitingGatePole\":false,\"lastUpdateTime\":\"2025-05-15 21:35:36\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-04-16 17:40:16\",\"duration\":2519720,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-04-16 17:29:14\",\"endTime\":\"2025-04-16 17:29:56\",\"duration\":42,\"countFinished\":true},\"createTime\":\"2024-10-18 20:51:53\",\"updateTime\":\"2025-05-15 21:35:36\",\"isDeleted\":\"NOT_DELETED\",\"trafficFlowSpeed\":0.0,\"vehicleCounterInfo\":{},\"prePositionList\":[],\"allObstacleList\":[],\"frontObstacleList\":[],\"behindObstacleList\":[],\"usableLaneIds\":[],\"vehicleCurLaneIdList\":[],\"vehicleAroundLaneId\":{},\"distanceToNearCurbList\":[],\"singleLane\":false,\"fcLaneIdList\":[],\"gcj02Location\":{\"latitude\":40.097069109996255,\"longitude\":116.5590473258911,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.5590473258911,40.097069109996255]},\"location\":{\"latitude\":40.095978,\"longitude\":116.553192,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.553192,40.095978]}},{\"vin\":\"LMTZSV024MC062730\",\"driveMode\":\"NO_CONTROL\",\"speed\":0.0,\"batterySwitching\":false,\"bizStatus\":\"\",\"trafficLightType\":\"NONE\",\"distanceToNextJunction\":15,\"lng\":\"118.966827\",\"lat\":\"39.215942\",\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"waitingGatePole\":false,\"lastUpdateTime\":\"2025-05-15 21:35:36\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-04-29 11:07:16\",\"duration\":1420100,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-04-29 11:01:36\",\"endTime\":\"2025-04-29 11:01:54\",\"duration\":18,\"countFinished\":true},\"createTime\":\"2025-02-20 09:49:22\",\"updateTime\":\"2025-05-15 21:35:36\",\"isDeleted\":\"NOT_DELETED\",\"trafficFlowSpeed\":0.0,\"vehicleCounterInfo\":{},\"prePositionList\":[],\"allObstacleList\":[],\"frontObstacleList\":[],\"behindObstacleList\":[],\"usableLaneIds\":[],\"vehicleCurLaneIdList\":[],\"vehicleAroundLaneId\":{},\"distanceToNearCurbList\":[],\"singleLane\":false,\"fcLaneIdList\":[],\"gcj02Location\":{\"latitude\":39.216928091480064,\"longitude\":118.97272466129427,\"coordinateSystem\":\"GCJ02\",\"pointList\":[118.97272466129427,39.216928091480064]},\"location\":{\"latitude\":39.215942,\"longitude\":118.966827,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.966827,39.215942]}},{\"vin\":\"LMTZSV024MC079026\",\"driveMode\":\"NO_CONTROL\",\"speed\":0.0,\"batterySwitching\":false,\"bizStatus\":\"\",\"trafficLightType\":\"NONE\",\"distanceToNextJunction\":-1,\"lng\":\"116.626152\",\"lat\":\"40.184074\",\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"waitingGatePole\":false,\"lastUpdateTime\":\"2025-05-15 21:35:36\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-05-09 13:40:32\",\"duration\":546904,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-05-09 13:24:08\",\"endTime\":\"2025-05-09 13:25:04\",\"duration\":56,\"countFinished\":true},\"createTime\":\"2024-10-18 20:51:53\",\"updateTime\":\"2025-05-15 21:35:36\",\"isDeleted\":\"NOT_DELETED\",\"trafficFlowSpeed\":0.0,\"vehicleCounterInfo\":{},\"prePositionList\":[],\"allObstacleList\":[],\"frontObstacleList\":[],\"behindObstacleList\":[],\"usableLaneIds\":[],\"vehicleCurLaneIdList\":[],\"vehicleAroundLaneId\":{},\"distanceToNearCurbList\":[],\"singleLane\":false,\"fcLaneIdList\":[],\"gcj02Location\":{\"latitude\":40.18514768142024,\"longitude\":116.63200066997499,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.63200066997499,40.18514768142024]},\"location\":{\"latitude\":40.184074,\"longitude\":116.626152,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.626152,40.184074]}}],\"after\":[{\"vin\":\"LMTZSV027NC092113\",\"driveMode\":\"NO_CONTROL\",\"speed\":0.0,\"bizStatus\":\"NO_BIZ_TRIP\",\"distanceToJunction\":20.3,\"distanceToNextJunction\":20,\"lng\":\"114.378822\",\"lat\":\"22.720194\",\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"lastUpdateTime\":\"2025-05-15 21:35:40\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-05-15 15:03:36\",\"duration\":23524,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.findTrafficLightType()?.code == 1\",\"startTime\":\"2025-05-15 14:25:36\",\"endTime\":\"2025-05-15 14:26:56\",\"duration\":80,\"countFinished\":true},\"createTime\":\"2024-10-21 09:55:48\",\"updateTime\":\"2025-05-15 21:35:36\",\"isDeleted\":\"NOT_DELETED\",\"routePoints\":[],\"refinedLineList\":[{\"latitude\":22.7202438,\"longitude\":114.3789674,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3789674,22.7202438]},{\"latitude\":22.7202437,\"longitude\":114.3789626,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3789626,22.7202437]},{\"latitude\":22.7202417,\"longitude\":114.3787637,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3787637,22.7202417]},{\"latitude\":22.7202398,\"longitude\":114.3785647,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3785647,22.7202398]},{\"latitude\":22.7202398,\"longitude\":114.3785502,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3785502,22.7202398]},{\"latitude\":22.7202396,\"longitude\":114.3785259,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3785259,22.7202396]},{\"latitude\":22.720239,\"longitude\":114.3785114,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3785114,22.720239]},{\"latitude\":22.7202377,\"longitude\":114.3784969,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3784969,22.7202377]},{\"latitude\":22.7202354,\"longitude\":114.3784826,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3784826,22.7202354]},{\"latitude\":22.7202318,\"longitude\":114.3784685,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3784685,22.7202318]},{\"latitude\":22.7202269,\"longitude\":114.378455,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.378455,22.7202269]},{\"latitude\":22.7202205,\"longitude\":114.3784421,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3784421,22.7202205]},{\"latitude\":22.7202129,\"longitude\":114.3784301,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3784301,22.7202129]},{\"latitude\":22.7202041,\"longitude\":114.3784191,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3784191,22.7202041]},{\"latitude\":22.7201941,\"longitude\":114.3784093,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3784093,22.7201941]},{\"latitude\":22.7201832,\"longitude\":114.3784007,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3784007,22.7201832]},{\"latitude\":22.7201716,\"longitude\":114.3783934,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3783934,22.7201716]},{\"latitude\":22.7201593,\"longitude\":114.3783874,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3783874,22.7201593]},{\"latitude\":22.7201465,\"longitude\":114.3783826,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3783826,22.7201465]},{\"latitude\":22.7201334,\"longitude\":114.3783792,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3783792,22.7201334]},{\"latitude\":22.7201201,\"longitude\":114.3783768,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3783768,22.7201201]},{\"latitude\":22.7201066,\"longitude\":114.3783754,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3783754,22.7201066]},{\"latitude\":22.7200931,\"longitude\":114.3783749,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3783749,22.7200931]},{\"latitude\":22.7200796,\"longitude\":114.3783748,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3783748,22.7200796]},{\"latitude\":22.7200661,\"longitude\":114.3783751,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3783751,22.7200661]}],\"obstacleAbstracts\":[],\"vehicleCounterInfo\":{\"walkerStayInCrossWalk\":{\"rule\":\"@contextCounterCommonCompute.isWalkerWaitInCrossWalk(#context,30.0,20.0,10.0,{'PEDESTRIAN','TRICYCLE','CYCLIST','TRICYCLIST'},3,15)\",\"startTime\":\"2025-05-05 13:53:09\",\"endTime\":\"2025-05-05 13:53:15\",\"duration\":5,\"countFinished\":true},\"redLightCounter\":{\"rule\":\"#context.findTrafficLightType()?.code == 1\",\"startTime\":\"2025-05-15 14:25:34\",\"endTime\":\"2025-05-15 14:26:59\",\"duration\":84,\"countFinished\":true},\"lowTrafficFlowSpeedCounter\":{\"rule\":\"#context.trafficFlowSpeed != null && #context.trafficFlowSpeed <= 0.0000036\",\"startTime\":\"2025-04-27 15:05:33\",\"duration\":1578605,\"countFinished\":false},\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-05-15 15:03:34\",\"duration\":23524,\"countFinished\":false},\"greenLightCounter\":{\"rule\":\"#context.findTrafficLightType()?.code == 3\",\"startTime\":\"2025-05-15 14:26:59\",\"endTime\":\"2025-05-15 14:27:02\",\"duration\":3,\"countFinished\":true}},\"prePositionList\":[{\"position\":{\"latitude\":22.7202261,\"longitude\":114.3787958,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3787958,22.7202261]},\"time\":\"2025-05-15 15:03:21\",\"distance\":4.467837209246805},{\"position\":{\"latitude\":22.7202261,\"longitude\":114.3787958,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3787958,22.7202261]},\"time\":\"2025-05-15 15:03:22\",\"distance\":4.467837209246805},{\"position\":{\"latitude\":22.7202256,\"longitude\":114.3788001,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788001,22.7202256]},\"time\":\"2025-05-15 15:03:23\",\"distance\":4.170364672905294},{\"position\":{\"latitude\":22.7202225,\"longitude\":114.3788091,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788091,22.7202225]},\"time\":\"2025-05-15 15:03:24\",\"distance\":3.4341700073554446},{\"position\":{\"latitude\":22.7202176,\"longitude\":114.3788157,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788157,22.7202176]},\"time\":\"2025-05-15 15:03:25\",\"distance\":2.702584074441159},{\"position\":{\"latitude\":22.7202114,\"longitude\":114.3788203,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788203,22.7202114]},\"time\":\"2025-05-15 15:03:26\",\"distance\":1.9426326052167033},{\"position\":{\"latitude\":22.7202047,\"longitude\":114.378823,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.378823,22.7202047]},\"time\":\"2025-05-15 15:03:27\",\"distance\":1.1941984419092813},{\"position\":{\"latitude\":22.7202015,\"longitude\":114.3788236,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788236,22.7202015]},\"time\":\"2025-05-15 15:03:28\",\"distance\":0.8499549422952325},{\"position\":{\"latitude\":22.7201988,\"longitude\":114.3788238,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788238,22.7201988]},\"time\":\"2025-05-15 15:03:29\",\"distance\":0.5647637775849381},{\"position\":{\"latitude\":22.7201962,\"longitude\":114.3788238,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788238,22.7201962]},\"time\":\"2025-05-15 15:03:30\",\"distance\":0.30647618521724485},{\"position\":{\"latitude\":22.7201946,\"longitude\":114.3788237,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788237,22.7201946]},\"time\":\"2025-05-15 15:03:31\",\"distance\":0.1866911370941997},{\"position\":{\"latitude\":22.7201944,\"longitude\":114.3788237,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788237,22.7201944]},\"time\":\"2025-05-15 15:03:32\",\"distance\":0.17994643198505234},{\"position\":{\"latitude\":22.7201943,\"longitude\":114.3788238,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788238,22.7201943]},\"time\":\"2025-05-15 15:03:33\",\"distance\":0.18760909333758058},{\"position\":{\"latitude\":22.7201943,\"longitude\":114.3788238,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788238,22.7201943]},\"time\":\"2025-05-15 15:03:34\",\"distance\":0.18760909333758058},{\"position\":{\"latitude\":22.7201943,\"longitude\":114.3788238,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.3788238,22.7201943]},\"time\":\"2025-05-15 15:03:35\",\"distance\":0.18760909333758058}],\"allObstacleList\":[],\"frontObstacleList\":[],\"behindObstacleList\":[],\"usableLaneIds\":[],\"vehicleCurLaneIdList\":[],\"vehicleAroundLaneId\":{},\"distanceToNearCurbList\":[],\"singleLane\":false,\"fcLaneIdList\":[\"lane_44c318306d6f\",\"lane_22f3a1129301\",\"lane_1054e7591efc\"],\"gcj02Location\":{\"latitude\":22.717535149538424,\"longitude\":114.38377364481924,\"coordinateSystem\":\"GCJ02\",\"pointList\":[114.38377364481924,22.717535149538424]},\"location\":{\"latitude\":22.720194,\"longitude\":114.378822,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.378822,22.720194]}},{\"vin\":\"LMTZSV026NC076338\",\"driveMode\":\"NO_CONTROL\",\"speed\":0.0,\"bizStatus\":\"NO_BIZ_TRIP\",\"distanceToJunction\":-1.0,\"distanceToNextJunction\":14,\"lng\":\"116.553192\",\"lat\":\"40.095978\",\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"lastUpdateTime\":\"2025-05-15 21:35:40\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-04-16 17:40:16\",\"duration\":2519724,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-04-16 17:29:14\",\"endTime\":\"2025-04-16 17:29:56\",\"duration\":42,\"countFinished\":true},\"createTime\":\"2024-10-18 20:51:53\",\"updateTime\":\"2025-05-15 21:35:36\",\"isDeleted\":\"NOT_DELETED\",\"vehicleCounterInfo\":{\"lowTrafficFlowSpeedCounter\":{\"rule\":\"#context.trafficFlowSpeed != null && #context.trafficFlowSpeed <= 0.0000036\",\"startTime\":\"2025-04-27 15:04:45\",\"duration\":1578653,\"countFinished\":false},\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-04-25 16:41:55\",\"duration\":1745623,\"countFinished\":false}},\"prePositionList\":[{\"position\":{\"latitude\":40.09599304199219,\"longitude\":116.55319213867188,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.55319213867188,40.09599304199219]},\"time\":\"2025-04-16 17:40:01\",\"distance\":1.6726348092419623},{\"position\":{\"latitude\":40.0959948,\"longitude\":116.5531969,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531969,40.0959948]},\"time\":\"2025-04-16 17:40:02\",\"distance\":1.9140068425304668},{\"position\":{\"latitude\":40.0959932,\"longitude\":116.5531972,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531972,40.0959932]},\"time\":\"2025-04-16 17:40:03\",\"distance\":1.7470810828810734},{\"position\":{\"latitude\":40.0959933,\"longitude\":116.5531972,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531972,40.0959933]},\"time\":\"2025-04-16 17:40:04\",\"distance\":1.757840567581592},{\"position\":{\"latitude\":40.0959947,\"longitude\":116.553197,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.553197,40.0959947]},\"time\":\"2025-04-16 17:40:05\",\"distance\":1.9050366575650388},{\"position\":{\"latitude\":40.09599304199219,\"longitude\":116.55319213867188,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.55319213867188,40.09599304199219]},\"time\":\"2025-04-16 17:40:06\",\"distance\":1.6726348092419623},{\"position\":{\"latitude\":40.0959947,\"longitude\":116.553197,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.553197,40.0959947]},\"time\":\"2025-04-16 17:40:07\",\"distance\":1.9050366575650388},{\"position\":{\"latitude\":40.0959931,\"longitude\":116.5531972,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531972,40.0959931]},\"time\":\"2025-04-16 17:40:08\",\"distance\":1.7363261345514105},{\"position\":{\"latitude\":40.0959908,\"longitude\":116.5531972,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531972,40.0959908]},\"time\":\"2025-04-16 17:40:09\",\"distance\":1.4904397237620388},{\"position\":{\"latitude\":40.0959881,\"longitude\":116.5531971,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531971,40.0959881]},\"time\":\"2025-04-16 17:40:10\",\"distance\":1.203940552762507},{\"position\":{\"latitude\":40.0959856,\"longitude\":116.5531966,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531966,40.0959856]},\"time\":\"2025-04-16 17:40:11\",\"distance\":0.9312684893612732},{\"position\":{\"latitude\":40.0959834,\"longitude\":116.5531962,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531962,40.0959834]},\"time\":\"2025-04-16 17:40:12\",\"distance\":0.6986941615257197},{\"position\":{\"latitude\":40.095981,\"longitude\":116.5531958,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531958,40.095981]},\"time\":\"2025-04-16 17:40:13\",\"distance\":0.46449557552549264},{\"position\":{\"latitude\":40.0959796,\"longitude\":116.5531957,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531957,40.0959796]},\"time\":\"2025-04-16 17:40:14\",\"distance\":0.36152941817438167},{\"position\":{\"latitude\":40.0959793,\"longitude\":116.5531957,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.5531957,40.0959793]},\"time\":\"2025-04-16 17:40:15\",\"distance\":0.34633303277094235}],\"allObstacleList\":[],\"frontObstacleList\":[],\"behindObstacleList\":[],\"usableLaneIds\":[],\"vehicleCurLaneIdList\":[],\"vehicleAroundLaneId\":{},\"distanceToNearCurbList\":[],\"singleLane\":false,\"fcLaneIdList\":[],\"gcj02Location\":{\"latitude\":40.097069109996255,\"longitude\":116.5590473258911,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.5590473258911,40.097069109996255]},\"location\":{\"latitude\":40.095978,\"longitude\":116.553192,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.553192,40.095978]}},{\"vin\":\"LA71AUB12S0508683\",\"driveMode\":\"UNKNOWN\",\"lastUpdateTime\":\"2025-05-15 21:35:40\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-05-15 21:35:40\",\"duration\":0,\"countFinished\":false},\"vehicleCounterInfo\":{},\"prePositionList\":[],\"allObstacleList\":[],\"frontObstacleList\":[],\"behindObstacleList\":[],\"usableLaneIds\":[],\"vehicleCurLaneIdList\":[],\"vehicleAroundLaneId\":{},\"distanceToNearCurbList\":[],\"singleLane\":false,\"fcLaneIdList\":[]},{\"vin\":\"LMTZSV024MC062730\",\"driveMode\":\"NO_CONTROL\",\"speed\":0.0,\"distanceToJunction\":-1.0,\"distanceToNextJunction\":15,\"lng\":\"118.966827\",\"lat\":\"39.215942\",\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"lastUpdateTime\":\"2025-05-15 21:35:40\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-04-29 11:07:16\",\"duration\":1420104,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-04-29 11:01:36\",\"endTime\":\"2025-04-29 11:01:54\",\"duration\":18,\"countFinished\":true},\"createTime\":\"2025-02-20 09:49:22\",\"updateTime\":\"2025-05-15 21:35:36\",\"isDeleted\":\"NOT_DELETED\",\"obstacleAbstracts\":[],\"vehicleCounterInfo\":{\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-04-29 11:01:41\",\"endTime\":\"2025-04-29 11:01:54\",\"duration\":12,\"countFinished\":true},\"lowTrafficFlowSpeedCounter\":{\"rule\":\"#context.trafficFlowSpeed != null && #context.trafficFlowSpeed <= 0.0000036\",\"startTime\":\"2025-04-27 15:05:29\",\"duration\":1578608,\"countFinished\":false},\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-04-30 13:56:28\",\"duration\":1323549,\"countFinished\":false}},\"prePositionList\":[{\"position\":{\"latitude\":39.2159663,\"longitude\":118.9669223,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9669223,39.2159663]},\"time\":\"2025-04-29 11:07:01\",\"distance\":8.643330828900625},{\"position\":{\"latitude\":39.2159648,\"longitude\":118.9669193,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9669193,39.2159648]},\"time\":\"2025-04-29 11:07:02\",\"distance\":8.346052811457263},{\"position\":{\"latitude\":39.2159604,\"longitude\":118.9669069,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9669069,39.2159604]},\"time\":\"2025-04-29 11:07:03\",\"distance\":7.181045170237686},{\"position\":{\"latitude\":39.2159604,\"longitude\":118.9669069,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9669069,39.2159604]},\"time\":\"2025-04-29 11:07:04\",\"distance\":7.181045170237686},{\"position\":{\"latitude\":39.2159579,\"longitude\":118.9668997,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668997,39.2159579]},\"time\":\"2025-04-29 11:07:05\",\"distance\":6.50788788703169},{\"position\":{\"latitude\":39.2159554,\"longitude\":118.9668916,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668916,39.2159554]},\"time\":\"2025-04-29 11:07:06\",\"distance\":5.761321521491695},{\"position\":{\"latitude\":39.2159526,\"longitude\":118.9668829,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668829,39.2159526]},\"time\":\"2025-04-29 11:07:07\",\"distance\":4.957944905079271},{\"position\":{\"latitude\":39.2159497,\"longitude\":118.9668736,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668736,39.2159497]},\"time\":\"2025-04-29 11:07:08\",\"distance\":4.104891909452042},{\"position\":{\"latitude\":39.2159469,\"longitude\":118.9668654,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668654,39.2159469]},\"time\":\"2025-04-29 11:07:09\",\"distance\":3.3527413871617338},{\"position\":{\"latitude\":39.2159445,\"longitude\":118.9668572,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668572,39.2159445]},\"time\":\"2025-04-29 11:07:10\",\"distance\":2.616549026927679},{\"position\":{\"latitude\":39.2159426,\"longitude\":118.9668493,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668493,39.2159426]},\"time\":\"2025-04-29 11:07:11\",\"distance\":1.9223106385511883},{\"position\":{\"latitude\":39.2159417,\"longitude\":118.9668455,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668455,39.2159417]},\"time\":\"2025-04-29 11:07:12\",\"distance\":1.5941303209904696},{\"position\":{\"latitude\":39.2159405,\"longitude\":118.9668408,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668408,39.2159405]},\"time\":\"2025-04-29 11:07:13\",\"distance\":1.2005176791579877},{\"position\":{\"latitude\":39.2159394,\"longitude\":118.9668358,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668358,39.2159394]},\"time\":\"2025-04-29 11:07:14\",\"distance\":0.8113773548849309},{\"position\":{\"latitude\":39.2159387,\"longitude\":118.9668332,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.9668332,39.2159387]},\"time\":\"2025-04-29 11:07:15\",\"distance\":0.6480312205981251}],\"allObstacleList\":[],\"frontObstacleList\":[],\"behindObstacleList\":[],\"usableLaneIds\":[],\"vehicleCurLaneIdList\":[],\"vehicleAroundLaneId\":{},\"distanceToNearCurbList\":[],\"singleLane\":false,\"fcLaneIdList\":[],\"gcj02Location\":{\"latitude\":39.216928091480064,\"longitude\":118.97272466129427,\"coordinateSystem\":\"GCJ02\",\"pointList\":[118.97272466129427,39.216928091480064]},\"location\":{\"latitude\":39.215942,\"longitude\":118.966827,\"coordinateSystem\":\"WGS84\",\"pointList\":[118.966827,39.215942]}},{\"vin\":\"LMTZSV024MC079026\",\"driveMode\":\"NO_CONTROL\",\"speed\":0.0,\"distanceToJunction\":-1.0,\"distanceToNextJunction\":-1,\"lng\":\"116.626152\",\"lat\":\"40.184074\",\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"lastUpdateTime\":\"2025-05-15 21:35:40\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-05-09 13:40:32\",\"duration\":546908,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-05-09 13:24:08\",\"endTime\":\"2025-05-09 13:25:04\",\"duration\":56,\"countFinished\":true},\"createTime\":\"2024-10-18 20:51:53\",\"updateTime\":\"2025-05-15 21:35:36\",\"isDeleted\":\"NOT_DELETED\",\"routePoints\":[],\"refinedLineList\":[{\"latitude\":40.1838989,\"longitude\":116.6257921,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6257921,40.1838989]},{\"latitude\":40.1838986,\"longitude\":116.6257863,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6257863,40.1838986]},{\"latitude\":40.1838973,\"longitude\":116.6257689,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6257689,40.1838973]},{\"latitude\":40.1838953,\"longitude\":116.6257517,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6257517,40.1838953]},{\"latitude\":40.1838928,\"longitude\":116.6257345,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6257345,40.1838928]},{\"latitude\":40.18389,\"longitude\":116.6257174,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6257174,40.18389]},{\"latitude\":40.183887,\"longitude\":116.6257004,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6257004,40.183887]},{\"latitude\":40.1838833,\"longitude\":116.6256776,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256776,40.1838833]},{\"latitude\":40.1838809,\"longitude\":116.6256604,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256604,40.1838809]},{\"latitude\":40.1838789,\"longitude\":116.6256432,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256432,40.1838789]},{\"latitude\":40.1838776,\"longitude\":116.6256258,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256258,40.1838776]},{\"latitude\":40.1838768,\"longitude\":116.6256083,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256083,40.1838768]},{\"latitude\":40.1838763,\"longitude\":116.6255909,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6255909,40.1838763]},{\"latitude\":40.1838762,\"longitude\":116.6255734,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6255734,40.1838762]},{\"latitude\":40.1838761,\"longitude\":116.625556,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.625556,40.1838761]},{\"latitude\":40.1838763,\"longitude\":116.625457,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.625457,40.1838763]}],\"obstacleAbstracts\":[{\"obstacleId\":\"2761101\",\"angle\":141.46441883466065,\"distance\":40.2429101958988,\"position\":\"116.6260317,40.1838193\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"speed\":0.0,\"fineType\":\"CAR\",\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0}},{\"obstacleId\":\"2761125\",\"angle\":137.4681870836877,\"distance\":34.63028816336244,\"position\":\"116.6259589,40.1838209\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"speed\":0.0,\"fineType\":\"CAR\",\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0}},{\"obstacleId\":\"2761593\",\"angle\":141.15365081864485,\"distance\":36.73741873351074,\"position\":\"116.6259932,40.1838308\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"speed\":0.0,\"fineType\":\"CAR\",\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0}},{\"obstacleId\":\"2828634\",\"angle\":134.54599505501392,\"distance\":42.425163715837456,\"position\":\"116.6260267,40.1837705\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"speed\":0.0,\"fineType\":\"CAR\",\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0}},{\"obstacleId\":\"2978764\",\"angle\":178.98807689171178,\"distance\":9.700410985267409,\"position\":\"116.6257123,40.1839895\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"speed\":0.0,\"fineType\":\"TRICYCLIST\",\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0}},{\"obstacleId\":\"3018952\",\"angle\":134.64187022566406,\"distance\":30.813100775417677,\"position\":\"116.6259105,40.1838255\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"speed\":0.0,\"fineType\":\"TRICYCLIST\",\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0}},{\"obstacleId\":\"3019760\",\"angle\":82.67545997665893,\"distance\":18.24582575898587,\"position\":\"116.6256257,40.183807\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"speed\":0.0,\"fineType\":\"TRICYCLIST\",\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0}}],\"vehicleCounterInfo\":{\"walkerStayInCrossWalk\":{\"rule\":\"@contextCounterCommonCompute.isWalkerWaitInCrossWalk(#context,30.0,20.0,10.0,{'PEDESTRIAN','TRICYCLE','CYCLIST','TRICYCLIST'},3,15)\",\"startTime\":\"2025-05-09 13:25:01\",\"endTime\":\"2025-05-09 13:25:07\",\"duration\":5,\"countFinished\":true},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-05-09 13:24:07\",\"endTime\":\"2025-05-09 13:25:01\",\"duration\":54,\"countFinished\":true},\"lowTrafficFlowSpeedCounter\":{\"rule\":\"#context.trafficFlowSpeed != null && #context.trafficFlowSpeed <= 0.0000036\",\"startTime\":\"2025-04-27 15:04:44\",\"duration\":1578654,\"countFinished\":false},\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-05-09 13:40:30\",\"duration\":546908,\"countFinished\":false}},\"prePositionList\":[{\"position\":{\"latitude\":40.1838802,\"longitude\":116.6256136,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256136,40.1838802]},\"time\":\"2025-05-09 13:40:17\",\"distance\":50.55966842258272},{\"position\":{\"latitude\":40.1838757,\"longitude\":116.6256196,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256196,40.1838757]},\"time\":\"2025-05-09 13:40:18\",\"distance\":50.31631436781636},{\"position\":{\"latitude\":40.1838723,\"longitude\":116.6256227,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256227,40.1838723]},\"time\":\"2025-05-09 13:40:19\",\"distance\":50.24734244792245},{\"position\":{\"latitude\":40.1838704,\"longitude\":116.6256241,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256241,40.1838704]},\"time\":\"2025-05-09 13:40:20\",\"distance\":50.235801818694725},{\"position\":{\"latitude\":40.1838683,\"longitude\":116.6256253,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256253,40.1838683]},\"time\":\"2025-05-09 13:40:21\",\"distance\":50.25067807240665},{\"position\":{\"latitude\":40.1838667,\"longitude\":116.625626,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.625626,40.1838667]},\"time\":\"2025-05-09 13:40:22\",\"distance\":50.27905351703858},{\"position\":{\"latitude\":40.1838646,\"longitude\":116.6256268,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256268,40.1838646]},\"time\":\"2025-05-09 13:40:23\",\"distance\":50.32627630548433},{\"position\":{\"latitude\":40.1838623,\"longitude\":116.6256276,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256276,40.1838623]},\"time\":\"2025-05-09 13:40:24\",\"distance\":50.385015255595896},{\"position\":{\"latitude\":40.1838608,\"longitude\":116.6256279,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256279,40.1838608]},\"time\":\"2025-05-09 13:40:25\",\"distance\":50.44066050480831},{\"position\":{\"latitude\":40.1838584,\"longitude\":116.6256281,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256281,40.1838584]},\"time\":\"2025-05-09 13:40:26\",\"distance\":50.55167724465203},{\"position\":{\"latitude\":40.1838571,\"longitude\":116.6256281,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256281,40.1838571]},\"time\":\"2025-05-09 13:40:27\",\"distance\":50.62039059973289},{\"position\":{\"latitude\":40.1838561,\"longitude\":116.6256281,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256281,40.1838561]},\"time\":\"2025-05-09 13:40:28\",\"distance\":50.673464223257916},{\"position\":{\"latitude\":40.1838556,\"longitude\":116.6256281,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256281,40.1838556]},\"time\":\"2025-05-09 13:40:29\",\"distance\":50.700071652589145},{\"position\":{\"latitude\":40.1838557,\"longitude\":116.6256282,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256282,40.1838557]},\"time\":\"2025-05-09 13:40:30\",\"distance\":50.687288686862324},{\"position\":{\"latitude\":40.1838557,\"longitude\":116.6256282,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.6256282,40.1838557]},\"time\":\"2025-05-09 13:40:31\",\"distance\":50.687288686862324}],\"allObstacleList\":[],\"frontObstacleList\":[],\"behindObstacleList\":[],\"usableLaneIds\":[],\"vehicleCurLaneIdList\":[],\"vehicleAroundLaneId\":{},\"distanceToNearCurbList\":[],\"singleLane\":false,\"fcLaneIdList\":[\"lane_7d6f1d761f35\",\"lane_74e669878dae\",\"lane_ea88ca739e39\",\"lane_2279a861a781\"],\"gcj02Location\":{\"latitude\":40.18514768142024,\"longitude\":116.63200066997499,\"coordinateSystem\":\"GCJ02\",\"pointList\":[116.63200066997499,40.18514768142024]},\"location\":{\"latitude\":40.184074,\"longitude\":116.626152,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.626152,40.184074]}}]}";
        DomainEventDTO<VehicleRuntimeInfoContextDO> vehicleRuntimeInfoContextDODomainEventDTO = JacksonUtils.from(s,
                new TypeReference<DomainEventDTO<VehicleRuntimeInfoContextDO>>() {});
        DomainEventFactory.createDomainEventChangeDTO(vehicleRuntimeInfoContextDODomainEventDTO,
                VehicleRuntimeInfoContextDO.class);
    }

    @Test
    public void test124() {
        VehicleRuntimeInfoContextDO contextDO = VehicleRuntimeInfoContextDO.builder().build();
        contextDO.setDirection(new Pair<>(PositionDO.builder().build(), PositionDO.builder().build()));
        System.out.println(JacksonUtils.from(JacksonUtils.to(contextDO), VehicleRuntimeInfoContextDO.class));

    }

    @Test
    public void test125() {
        Map<String, Double> stringDoubleMap = CoordinateTransferUtils.gcj02ToWgs84(114.37499084763228, 22.731505886554388);
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = VehicleRuntimeInfoContextDO
                .builder().lat(stringDoubleMap.get(("lat")) + "").lng(stringDoubleMap.get("lon") + "").build();

        PositionDO gcj02Location = vehicleRuntimeInfoContextDO.getGCJ02Location();

        System.out.println(gcj02Location);
    }

}
