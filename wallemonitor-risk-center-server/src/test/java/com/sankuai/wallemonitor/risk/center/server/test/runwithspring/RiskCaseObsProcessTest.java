package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseObsProcess;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseObstacleDORepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskStrandingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskStrandingRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class RiskCaseObsProcessTest extends SpringTestBase {

    @Resource
    private RiskCaseObsProcess riskCaseObsProcess;

    @Mock
    private RiskStrandingRecordRepository mockRiskStrandingRecordRepository;

    @Mock
    private VehicleRuntimeInfoContextRepository mockVehicleRuntimeInfoContextRepository;

    @Mock
    private RiskCaseObstacleDORepository mockRiskCaseObstacleDORepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 使用反射注入mock对象
        ReflectionTestUtils.setField(riskCaseObsProcess, "riskStrandingRecordRepository", mockRiskStrandingRecordRepository);
        ReflectionTestUtils.setField(riskCaseObsProcess, "vehicleRuntimeInfoContextRepository", mockVehicleRuntimeInfoContextRepository);
        ReflectionTestUtils.setField(riskCaseObsProcess, "riskCaseObstacleDORepository", mockRiskCaseObstacleDORepository);
    }

    @Test
    public void testHandleRiskCaseObsV2WithNullInput() throws TException {
        // 测试空输入情况
        boolean result = riskCaseObsProcess.handleRiskCaseObsV2(new Date().getTime(), null);
        Assert.assertTrue("当输入为null时应返回true", result);

        List<VehicleRuntimeInfoContextDO> emptyList = new ArrayList<>();
        result = riskCaseObsProcess.handleRiskCaseObsV2(new Date().getTime(), emptyList);
        Assert.assertTrue("当输入为空列表时应返回true", result);
    }

    @Test
    public void testHandleRiskCaseObsV2WithNoStrandingRecord() throws TException {
        // 准备测试数据
        String vin = "TEST_VIN_001";
        List<VehicleRuntimeInfoContextDO> vehicleRuntimeInfoContextDOS = new ArrayList<>();
        VehicleRuntimeInfoContextDO contextDO = new VehicleRuntimeInfoContextDO();
        contextDO.setVin(vin);
        vehicleRuntimeInfoContextDOS.add(contextDO);

        // Mock riskStrandingRecordRepository返回空数据
        when(mockRiskStrandingRecordRepository.queryByParam(any(RiskStrandingRecordDOQueryParamDTO.class)))
                .thenReturn(new ArrayList<>());

        // 执行测试
        boolean result = riskCaseObsProcess.handleRiskCaseObsV2(new Date().getTime(), vehicleRuntimeInfoContextDOS);
        
        // 验证结果
        Assert.assertTrue("当没有未取消的停滞检测记录时应返回true", result);
    }

    @Test
    public void testHandleRiskCaseObsV2WithStrandingRecord() throws TException {
        // 准备测试数据
        String vin = "TEST_VIN_002";
        Long timestamp = new Date().getTime();
        
        // 1. 准备车辆运行时信息
        List<VehicleRuntimeInfoContextDO> vehicleRuntimeInfoContextDOS = new ArrayList<>();
        VehicleRuntimeInfoContextDO contextDO = new VehicleRuntimeInfoContextDO();
        contextDO.setVin(vin);
        
        // 创建ObstacleAbstract对象
        List<VehicleRuntimeInfoContextDO.ObstacleAbstract> obstacleAbstracts = new ArrayList<>();
        VehicleRuntimeInfoContextDO.ObstacleAbstract obstacleDTO = new VehicleRuntimeInfoContextDO.ObstacleAbstract();
        obstacleDTO.setObstacleId("OBS_001");
        obstacleDTO.setPosition("116.123,39.456");
        obstacleDTO.setPosture("STATIC");
        obstacleDTO.setDistance(10.0);
        
        obstacleAbstracts.add(obstacleDTO);
        contextDO.setObstacleAbstracts(obstacleAbstracts);
        vehicleRuntimeInfoContextDOS.add(contextDO);

        // 2. 准备停滞检测记录
        List<RiskStrandingRecordDO> strandingRecords = new ArrayList<>();
        RiskStrandingRecordDO recordDO = new RiskStrandingRecordDO();
        recordDO.setVin(vin);
        recordDO.setTmpCaseId("CASE_001");
        recordDO.setStatus(DetectRecordStatusEnum.PROCESSING);
        strandingRecords.add(recordDO);

        // 3. Mock依赖服务
        // 确保使用具体的参数matcher，确保调用的参数正确匹配
        Mockito.doReturn(strandingRecords).when(mockRiskStrandingRecordRepository)
                .queryByParam(Mockito.any(RiskStrandingRecordDOQueryParamDTO.class));
        
        Mockito.doReturn(vehicleRuntimeInfoContextDOS).when(mockVehicleRuntimeInfoContextRepository)
                .getFromCache(Mockito.eq(Arrays.asList(vin)));

        // 4. 执行测试
        boolean result = riskCaseObsProcess.handleRiskCaseObsV2(timestamp, vehicleRuntimeInfoContextDOS);
        
        // 5. 验证结果
        Assert.assertTrue("处理障碍物信息应返回true", result);
        
        // 验证riskCaseObstacleDORepository.batchSave被调用
        Mockito.verify(mockRiskCaseObstacleDORepository).batchSave(Mockito.anyList());
    }
}
