package com.sankuai.wallemonitor.risk.center.server.test.unit.consumer;

import static groovy.util.GroovyTestCase.assertEquals;
import static org.mockito.ArgumentMatchers.any;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.config.vo.ConfigChangedEvent;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.DomainEventProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.DomainEventConfig;
import com.sankuai.wallemonitor.risk.center.infra.utils.VelocityUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.SpringUtils;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxNoticeParamVTO;
import com.sankuai.wallemonitor.risk.center.server.consumer.DomainEventConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.DataTestBase;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;

public class DomainEventConsumerTest extends ServiceTestBase {

    @InjectMocks
    private DomainEventConsumer domainEventConsumer;

    public static final String riskDomainEventConfig = "{\n"
            + "    \"retryTimes\": 3,\n"
            + "    \"processConfigsList\": [\n"
            + "        {\n"
            + "            \"processName\": \"riskCaseMessageNoticeProcess\",\n"
            + "            \"domainClassNames\": [\n"
            + "                \"RiskCaseDO\",\n"
            + "                \"RiskCaseVehicleRelationDO\"\n"
            + "            ],\n"
            + "            \"operateEntry\": [\n"
            + "                \"VEHICLE_DATA_CONSUMER_ENTER\",\n"
            + "                \"MANUAL_RISK_CASE_OPERATE_ENTER\",\n"
            + "                \"RISK_EVENT_CONSUMER_ENTER\"\n"
            + "            ],\n"
            + "            \"retryCount\": 3\n"
            + "        }\n"
            + "    ],\n"
            + "    \"domainProcessSortList\": [\n"
            + "        {\n"
            + "            \"domainClassName\": \"RiskCaseDO\",\n"
            + "            \"processList\": [\n"
            + "                \"riskCaseMessageNoticeProcess\"\n"
            + "            ]\n"
            + "        },\n"
            + "        {\n"
            + "            \"domainClassName\": \"RiskCaseVehicleRelationDO\",\n"
            + "            \"processList\": [\n"
            + "                \"riskCaseMessageNoticeProcess\"\n"
            + "            ]\n"
            + "        }\n"
            + "    ]\n"
            + "}";

    private String domainEventStr = "{\n"
            + "    \"entry\": {\n"
            + "        \"domainClassName\": \"RiskCaseDO\",\n"
            + "        \"operateEntry\": \"VEHICLE_DATA_CONSUMER_ENTER\"\n"
            + "    },\n"
            + "    \"timestamp\": 1718710181682,\n"
            + "    \"operator\": \"\",\n"
            + "    \"traceId\": \"a73220a5-2f6b-4238-8c8c-0c47dbc44095\",\n"
            + "    \"extInfo\": {\n"
            + "\n"
            + "    },\n"
            + "    \"before\": [\n"
            + "\n"
            + "    ],\n"
            + "    \"after\": [\n"
            + "        {\n"
            + "            \"caseId\": \"015a397c6d0a4a8a857da122b12820c1\",\n"
            + "            \"type\": \"VEHICLE_SIDE_BY_SIDE\",\n"
            + "            \"placeCode\": \"hualikan\",\n"
            + "            \"status\": \"NO_DISPOSAL\",\n"
            + "            \"eventId\": \"20240617184839021_common398_s20-173\",\n"
            + "            \"source\": \"PNC\",\n"
            + "            \"extInfo\": {\n"
            + "                \"city\": \"北京市\",\n"
            + "                \"are\": \"顺义区\",\n"
            + "                \"poi\": \"莫奈花园\"\n"
            + "            },\n"
            + "            \"isDeleted\": \"NOT_DELETED\"\n"
            + "        }\n"
            + "    ]\n"
            + "}";
    private DomainEventConfig domainEventConfig;

    @Before
    public void setUp() throws Exception {
        // 初始化数据
        DataTestBase dataTestBase = new DataTestBase();
        // mock 事件配置
        ConfigChangedEvent event = new ConfigChangedEvent();
        event.setValue(riskDomainEventConfig);
        this.onDomainEventConfigChange(event);
        Mockito.doReturn(domainEventConfig).when(lionConfigRepository).getDomainEventConfig();

        // mock 风险关系查询 queryByParam
        mockRiskCaseVehicleRelation(dataTestBase);

        // mock 风险事件查询
        mockRiskCase(dataTestBase);

        // mock 领域事件程序调用
        Map<String, DomainEventProcess> domainEventProcessesMap = new HashMap<>();
        Map<String, OperateEnterActionEnum> processName2OperateEnterAction = new HashMap<>();
        processName2OperateEnterAction.put("riskCaseMessageNoticeProcess",
                OperateEnterActionEnum.RISK_CASE_CREATE_OR_UPDATE_MESSAGE_ENTRY);
        //  Mockito.when(riskCaseMessageNoticeProcess.process(Mockito.any())).thenReturn(true);
        domainEventProcessesMap.put("riskCaseMessageNoticeProcess", riskCaseMessageNoticeProcess);
        ReflectionTestUtils.setField(eventProcessAdapter, "domainEventProcessesMap", domainEventProcessesMap);
        ReflectionTestUtils.setField(eventProcessAdapter, "processName2entry", processName2OperateEnterAction);


        // mock eventProcessRecordRepository
        Mockito.when(eventProcessRecordRepository.getProcessResult(
                Mockito.anyList(),
                Mockito.any(DomainEventEntryDTO.class),
                Mockito.anyLong(),
                Mockito.anyString())).thenReturn(dataTestBase.resultDOlist);

        // mock springUtils返回领域名称
        PowerMockito.mockStatic(SpringUtils.class);
        PowerMockito.when(SpringUtils.getBeanName(Mockito.any())).thenReturn("riskCaseMessageNoticeProcess");

        // mock lion消息推送配置
        Mockito.when(lionConfigRepository.getCaseType2BroadCastStrategyConfig()).thenReturn(dataTestBase.map);
        Mockito.when(lionConfigRepository.getByCaseType(any(RiskCaseTypeEnum.class))).thenAnswer(invocation -> {
            RiskCaseTypeEnum riskCaseTypeEnum = invocation.getArgument(0);
            return dataTestBase.map.get(riskCaseTypeEnum);
        });

        // mock 分布式锁
        mockRedisAndLock();

        // mock VelocityUtils
        PowerMockito.mockStatic(VelocityUtils.class);
        Mockito.when(VelocityUtils.render(Mockito.anyString(), Mockito.anyMap())).thenReturn("hello render");

        // mock 大象发消息
        Mockito.when(dxNoticeAdapter.createOrUpdateDxMessage(Mockito.any(DxNoticeParamVTO.class))).thenReturn("msgId");

    }

    public void onDomainEventConfigChange(ConfigChangedEvent event) {
        String newValue = event.getValue();
        if (StringUtils.isBlank(newValue)) {
            return;
        }
        //序列化
        domainEventConfig = JacksonUtils.from(newValue, DomainEventConfig.class);
        if (domainEventConfig == null) {
            return;
        }
        domainEventConfig.resetRetryTimes();
    }

    /**
     * 测试消息为空的情况
     */
    @Test
    public void testReceiveWithEmptyMessage() {
        String domainEventStr = "";

        // 接受消息
        ConsumeStatus result = domainEventConsumer.receive(domainEventStr);

        // 验证结果
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        Mockito.verify(dxNoticeAdapter, Mockito.times(0)).createOrUpdateDxMessage(Mockito.any());
        Mockito.verify(riskCaseRepository, Mockito.times(0)).batchSave(Mockito.anyList());
    }

    /**
     * 测试消息中类名信息未找到的情况
     */
    @Test
    public void testReceiveWithNoClassNameInMessage() {
        String domainEventStr = "{\"entry\":{}}";

        // 接受消息
        ConsumeStatus result = domainEventConsumer.receive(domainEventStr);

        // 验证结果
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        Mockito.verify(dxNoticeAdapter, Mockito.times(0)).createOrUpdateDxMessage(Mockito.any());
        Mockito.verify(riskCaseRepository, Mockito.times(0)).batchSave(Mockito.anyList());
    }

    /**
     * 测试无法处理的类消息的情况
     */
    @Test
    public void testReceiveWithUnprocessableClassMessage() {
        String domainEventStr = "{\"entry\":{\"domainClassName\":\"UnprocessableClass\"}}";

        // 接受消息
        ConsumeStatus result = domainEventConsumer.receive(domainEventStr);

        // 验证结果
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        Mockito.verify(dxNoticeAdapter, Mockito.times(0)).createOrUpdateDxMessage(Mockito.any());
        Mockito.verify(riskCaseRepository, Mockito.times(0)).batchSave(Mockito.anyList());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testReceiveWithSuccess() throws Exception {

        // 接受消息
        ConsumeStatus result = domainEventConsumer.receive(domainEventStr);

        // 验证结果
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);

        // 验证大象发消息
        Mockito.verify(dxNoticeAdapter).createOrUpdateDxMessage(Mockito.any(DxNoticeParamVTO.class));
        // 验证风险事件保存
        Mockito.verify(riskCaseRepository).batchSave(Mockito.anyList());
    }
}
