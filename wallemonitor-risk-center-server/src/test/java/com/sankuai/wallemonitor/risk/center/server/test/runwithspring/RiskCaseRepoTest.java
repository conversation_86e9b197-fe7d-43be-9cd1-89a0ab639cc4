package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

public class RiskCaseRepoTest extends SpringTestBase {

    @Resource
    private RiskCaseRepository riskCaseRepository;


    @Test
    public void testQueryRiskCaseList() {
        List<String> vinList = Arrays.asList(
                "LMTZSV024MC062730",
                "LMTZSV024MC062730",
                "LMTZSV021MC001593",
                "LMTZSV024MC062730",
                "LMTZSV02XNC016109",
                "LMTZSV021MC001593",
                "LMTZSV02XNC016109",
                "LMTZSV024MC062730",
                "LMTZSV021MC001593"
        );
        List<String> codeList = Arrays.asList(
                "f30d36b6d8024539bca325f19614478d",
                "a99a1223919641cc998f2ef4ea93e576",
                "0795186268be49e59ca49925d2be6df8",
                "83fec391f5594625bafdeca051e35e4f",
                "1246238ba780470caddf19ec8478ca3e",
                "cb6130c7334b44d4852241e74a1252d8",
                "b9cd28d1ef604cdc87fe0038dc8feaa5",
                "ca24e1bab16b468c80df09b8111a4514",
                "8896d8e15c394034932171df0d042d93",
                "1905e2c0791146fe80e7191c4609e47d"
        );
//        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(RiskCaseDOQueryParamDTO.builder()
//                .vinList(vinList)
//                .caseIdList(codeList)
//                .createTimeCreateTo(DatetimeUtil.convertDatetimeStr2Date("2024-07-01 00:00:00"))
//                .createTimeBelowTo(DatetimeUtil.convertDatetimeStr2Date("2024-09-01 00:00:00"))
//                .createTimeRange(
//                        TimePeriod.builder().beginDate(DatetimeUtil.convertDatetimeStr2Date("2024-07-02 00:00:00"))
//                                .endDate(DatetimeUtil.convertDatetimeStr2Date("2024-08-08 00:00:00"))
//                                .build())
//                .leftJoinRelation(true)
//                .source(1)
//                .orderByCreateTime(OrderEnum.DESC)
//                .build());
//        System.out.println(JacksonUtils.to(riskCaseDOList));

        Paging<RiskCaseDO> paging = riskCaseRepository.queryByParamByPage(RiskCaseDOQueryParamDTO.builder()
                .vinList(vinList)
                .caseIdList(codeList)
                .createTimeCreateTo(DatetimeUtil.convertDatetimeStr2Date("2024-07-01 00:00:00"))
                .createTimeBelowTo(DatetimeUtil.convertDatetimeStr2Date("2024-09-01 00:00:00"))
                .createTimeRange(
                        TimePeriod.builder().beginDate(DatetimeUtil.convertDatetimeStr2Date("2024-07-02 00:00:00"))
                                .endDate(DatetimeUtil.convertDatetimeStr2Date("2024-08-08 00:00:00"))
                                .build())
                .leftJoinRelation(true)
                .categoryList(Lists.newArrayList("BAD"))
                .leftJoinMarkInfo(true)
                .durationGreatThan(300)
                .source(1)
                .orderByCreateTime(OrderEnum.DESC)
                .build(), 1, 20);
        System.out.println(JacksonUtils.to(paging));
    }

    @Test
    public void testQuery() {
        RiskCaseDOQueryParamDTO paramDTO = JacksonUtils.from(
                "{\"caseTypeList\":[1],\"statusList\":[10,20],\"messageId\":\"\",\"createTimeRange\":{\"endDate\":\"2024-08-08 23:54:50\",\"beginDate\":\"2024-08-08 23:44:50\"},\"leftJoinRelation\":false,\"leftJoinMarkInfo\":false,\"isDeleted\":false}",
                RiskCaseDOQueryParamDTO.class);
        List<RiskCaseDO> results = riskCaseRepository.queryByParam(paramDTO);
        System.out.println(JacksonUtils.to(results));
    }
}
