package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.ImmutableMap;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.request.CreateExperimentalResultRequest;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultMissCaseDetailRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultMissCaseDetailRequestDTO.IdentifyProcessDetailDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultMissCaseDetailRequestDTO.IdentifyProcessInfoDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultOverviewRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultSceneDataRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultSceneDataRequestDTO.MissCaseAndTimes;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftKmService;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
public class IThriftKmServiceTest extends SpringTestBase {

    @Resource
    private IThriftKmService iThriftKmService;

    @Test
    public void testCreateExperimentalResult() {
        // 数据概览
        List<String> content = Arrays.asList("优化123", "优化456");
        ExperimentalResultOverviewRequestDTO overviewDTO = ExperimentalResultOverviewRequestDTO.builder()
                .experimentalTime(new Date().getTime())
                .experimentalContent(content)
                .personMis("lijiayan09")
                .personName("李佳宴")
                .round(10)
                .dataset("精简风险测试集")
                .datasetDataCount(102)
                .recallCount(918)
                .missCount(82)
                .failureCount(2)
                .accuracyCount(800)
                .build();

        // 场景数据
        ExperimentalResultSceneDataRequestDTO scene1 = ExperimentalResultSceneDataRequestDTO.builder()
                .categoryName(ISCheckCategoryEnum.IN_MIDDLE_ROAD.getSubcategory())
                .actualCaseCount(1000)
                .recallCaseCount(980)
                .missedEvents(Arrays.asList(
                        MissCaseAndTimes.builder().caseId("MT0790220241024102140S03T01").times(1).build())).build();
        ExperimentalResultSceneDataRequestDTO scene2 = ExperimentalResultSceneDataRequestDTO.builder()
                .categoryName(ISCheckCategoryEnum.STOP_ON_ROAD_SIDE.getSubcategory())
                .actualCaseCount(1000)
                .recallCaseCount(890)
                .missedEvents(Arrays.asList(
                        MissCaseAndTimes.builder().caseId("MT0790220241024102140S03T02").times(1).build(),
                        MissCaseAndTimes.builder().caseId("MT0790220241024102140S03T03").times(3).build())).build();

        // 漏召事件列表详情
        ExperimentalResultMissCaseDetailRequestDTO missCaseDetailDTO1 = ExperimentalResultMissCaseDetailRequestDTO.builder()
                .caseId("MT0790220241024102140S03T01")
                .missCount(10).identifyProcessInfo(Arrays.asList(
                        IdentifyProcessInfoDTO.builder()
                                .category(ISCheckCategoryEnum.STOP_ON_ROAD_SIDE.getSubcategory()).count(8)
                                .detail(ImmutableMap.of("前视",
                                        IdentifyProcessDetailDTO.builder().info("机动车道#停车线")
                                                .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                                .build(), "后视",
                                        IdentifyProcessDetailDTO.builder().info("机动车道#停车线")
                                                .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                                .build())).build(),
                        IdentifyProcessInfoDTO.builder().category(ISCheckCategoryEnum.IN_MIDDLE_ROAD.getSubcategory())
                                .count(2)
                                .detail(ImmutableMap.of("前视",
                                        IdentifyProcessDetailDTO.builder().info("机动车道#停车线")
                                                .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                                .build())).build())
                ).build();
        ExperimentalResultMissCaseDetailRequestDTO missCaseDetailDTO2 = ExperimentalResultMissCaseDetailRequestDTO.builder()
                .caseId("MT0790220241024102140S03T01")
                .trueCategory(ISCheckCategoryEnum.WAITING_RIDER_TAKING_ORDER.getSubcategory())
                .missCount(99)
                .identifyProcessInfo(Arrays.asList(
                                IdentifyProcessInfoDTO.builder()
                                // .category(ISCheckCategoryEnum.AT_CONSTRUCTION_SITE.getSubcategory()).count(1)
                                        .detail(ImmutableMap.of("后视",
                                                IdentifyProcessDetailDTO.builder().info("机动车道#停车线")
                                                        .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                                        .build())).build(),
                                IdentifyProcessInfoDTO.builder()
                                        .category(ISCheckCategoryEnum.CONFLICT_WITH_PASSAGER.getSubcategory()).count(98)
                                        .detail(ImmutableMap.of("环视",
                                                IdentifyProcessDetailDTO.builder().info("机动车道#停车线")
                                                        .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                                        .build())).build()
                        )
                ).build();

        // 创建文档
        CreateExperimentalResultRequest param = CreateExperimentalResultRequest.builder()
                .operatorEmpId(20680932)
                .title("测试标题")
                .overview(overviewDTO)
                .sceneDataList(Arrays.asList(scene1, scene2))
                .missCaseDetailList(Arrays.asList(missCaseDetailDTO1, missCaseDetailDTO2))
                .build();
        EveThriftResponse<String> resp = iThriftKmService.createExperimentalResultContent(param);
        System.out.println(resp);
    }

    @Test
    public void testCreateEmptyExperimentalResult() {
        CreateExperimentalResultRequest param = CreateExperimentalResultRequest.builder()
                .operatorEmpId(20680932)
                .build();
        EveThriftResponse<String> experimentalResultContent = iThriftKmService.createExperimentalResultContent(param);
        System.out.println(experimentalResultContent);
    }

    @Test
    public void testCreatePartEmptyExperimentalResult() {
        // 数据概览
        List<String> content = Arrays.asList("优化123", "优化456");
        ExperimentalResultOverviewRequestDTO overviewDTO = ExperimentalResultOverviewRequestDTO.builder()
                .experimentalTime(new Date().getTime())
                .experimentalContent(content)
                .personMis("lijiayan09")
                .personName("李佳宴")
                .round(10)
                .dataset("精简风险测试集")
                .datasetDataCount(102)
                .recallCount(918)
                .failureCount(2)
                .build();

        // 场景数据
        ExperimentalResultSceneDataRequestDTO scene1 = ExperimentalResultSceneDataRequestDTO.builder()
                .categoryName(ISCheckCategoryEnum.IN_MIDDLE_ROAD.getSubcategory())
                .actualCaseCount(1000)
                .recallCaseCount(980).build();
        ExperimentalResultSceneDataRequestDTO scene2 = ExperimentalResultSceneDataRequestDTO.builder()
                .categoryName(ISCheckCategoryEnum.STOP_ON_ROAD_SIDE.getSubcategory())
                .actualCaseCount(1000)
                .recallCaseCount(890)
                .missedEvents(Arrays.asList(
                        MissCaseAndTimes.builder().build(),
                        MissCaseAndTimes.builder().caseId("MT0790220241024102140S03T03").times(3).build())).build();

        // 漏召事件列表详情
        ExperimentalResultMissCaseDetailRequestDTO missCaseDetailDTO1 = ExperimentalResultMissCaseDetailRequestDTO.builder()
                .caseId("MT0790220241024102140S03T01")
                // .trueCategory(ISCheckCategoryEnum.AT_ACCIDENT_SCENE.getSubcategory())
                .missCount(10).identifyProcessInfo(Arrays.asList(
                        IdentifyProcessInfoDTO.builder()
                                .category(ISCheckCategoryEnum.STOP_ON_ROAD_SIDE.getSubcategory()).count(8).build(),
                        IdentifyProcessInfoDTO.builder().category(ISCheckCategoryEnum.IN_MIDDLE_ROAD.getSubcategory())
                                .count(2).detail(ImmutableMap.of("前视", IdentifyProcessDetailDTO.builder()
                                        .picUrl("https://walle.meituan.com/replay/video/avatar?startTime=20241018095157&endTime=20241018095157&vin=LMTZSV021NC061178&view=front")
                                        .build())).build(),
                        IdentifyProcessInfoDTO.builder().category(ISCheckCategoryEnum.IN_MIDDLE_ROAD.getSubcategory())
                                .count(2).detail(ImmutableMap.of("前视",
                                        IdentifyProcessDetailDTO.builder().info("机动车道#停车线").build())).build())
                ).build();
        ExperimentalResultMissCaseDetailRequestDTO missCaseDetailDTO2 = ExperimentalResultMissCaseDetailRequestDTO.builder()
                .caseId("MT0790220241024102140S03T01")
                .trueCategory(ISCheckCategoryEnum.WAITING_RIDER_TAKING_ORDER.getSubcategory())
                .missCount(99)
                .build();

        // 创建文档
        CreateExperimentalResultRequest param = CreateExperimentalResultRequest.builder()
                .operatorEmpId(20680932)
                .title("测试标题")
                .overview(overviewDTO)
                .sceneDataList(Arrays.asList(scene1, scene2))
                .missCaseDetailList(Arrays.asList(missCaseDetailDTO1, missCaseDetailDTO2))
                .build();
        iThriftKmService.createExperimentalResultContent(param);
    }
}
