package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.dbrepo;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

import com.sankuai.wallemonitor.risk.center.infra.convert.UserNoticeReadRecordConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.UserNoticeReadRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.model.core.UserNoticeReadRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.UserNoticeReadRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;

public class UserNoticeReadRecordRepositoryTest extends RepositoryTestBase {

    @Spy
    private UserNoticeReadRecordMapper mapper;

    @Spy
    private UserNoticeReadRecordConvertImpl covert;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(userNoticeReadRecordRepository, "mapper", mapper);
        ReflectionTestUtils.setField(userNoticeReadRecordRepository, "covert", covert);
    }

    @Test
    public void testQueryByParam() {
        // mock selectList
        PowerMockito.when(mapper.selectList(any(), any(), any())).thenReturn(Collections.emptyList());

        // 运行
        UserNoticeReadRecordDOQueryParamDTO queryParamDTO = UserNoticeReadRecordDOQueryParamDTO.builder()
                .userId("userId").build();
        List<UserNoticeReadRecordDO> userNoticeReadRecordDOList = userNoticeReadRecordRepository.queryByParam(
                queryParamDTO);

        // 验证
        assertEquals(userNoticeReadRecordDOList, Collections.emptyList());


    }


    @Test
    public void testSave() {
        // 运行
        userNoticeReadRecordRepository.save(UserNoticeReadRecordDO.builder().userId("userId").build());
        // 验证
        Mockito.verify(mapper, Mockito.times(1)).save(Mockito.any());
    }

    @Test
    public void testBatchSave() {
        // mock
        userNoticeReadRecordRepository.batchSave(
                Collections.singletonList(UserNoticeReadRecordDO.builder().userId("userId").build()));
        // 验证
        Mockito.verify(mapper, Mockito.times(1)).save(Mockito.any());

    }

}
