package com.sankuai.wallemonitor.risk.center.server.test.unit.consumer;

import static com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum.IN_DISPOSAL;
import static groovy.util.GroovyTestCase.assertEquals;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMrmMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CloudCursorAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.CloudCursorResourceResponse;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.RiskCaseRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.RiskCaseVehicleRelationRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.server.consumer.RiskCaseMrmConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.DataTestBase;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

public class RiskCaseMrmConsumerTest extends ServiceTestBase {

    @InjectMocks
    private RiskCaseMrmConsumer riskCaseMrmConsumer;

    @Mock
    private RiskCaseVehicleRelationRepositoryImpl riskCaseVehicleRelationRepository;

    @Mock
    private RiskCaseRepositoryImpl riskCaseRepository;

    @Mock
    private CloudCursorAdapter cloudCursorAdapter;

    private String msg = "{\"type\":30,\"body\":{\"vin\":\"LMTZSV024MC048701\",\"traceId\":\"s20-175_1720407707572\",\"mrmSeatMisId\":\"limuyong\",\"status\":40,\"mrmRole\":1,\"mrmSeatNo\":\"sz-91\",\"requestSeatTime\":1720407718000,\"seatExitTime\":1720407741874},\"timestamp\":1720407741879}";

    @Before
    public void setUp() {
        // 初始化数据
        DataTestBase dataTestBase = new DataTestBase();

        // mock Redis和分布式锁
        mockRedisAndLock();

        // mock 查找Trace关联车辆的结果，注意 eventId
        mockRiskCaseVehicleRelation(dataTestBase);

        // mock 查询事件的状态
        mockRiskCase(dataTestBase);

        // 反射注入坐席取消呼叫开关
        ReflectionTestUtils.setField(riskCaseMrmConsumer, "riskMrmConsumerDisposedSwitch", true);

        Mockito.doReturn(Lists.newArrayList(RiskCaseVehicleRelationDO.builder().callMrmReason("9008").caseId("1111").build()))
                .when(riskCaseVehicleRelationRepository).queryByParam(Mockito.any());

        Mockito.doReturn(Lists.newArrayList(RiskCaseDO.builder().caseId("1111").eventId("1111")
                        .status(IN_DISPOSAL).mrmCalled(RiskCaseMrmCalledStatusEnum.CALLING).build()))
                .when(riskCaseRepository).queryByParam(Mockito.any());

        Mockito.doReturn(CloudCursorResourceResponse.builder().code(200).build())
                .when(cloudCursorAdapter).callCloudCursor(Mockito.any());

        // 反射注入riskCaseVehicleRelationRepository
        ReflectionTestUtils.setField(riskCaseMrmConsumer, "riskCaseVehicleRelationRepository", riskCaseVehicleRelationRepository);
        ReflectionTestUtils.setField(riskCaseMrmConsumer, "riskCaseRepository", riskCaseRepository);
        ReflectionTestUtils.setField(riskCaseMrmConsumer, "cloudCursorAdapter", cloudCursorAdapter);

    }

    /**
     * 588f18eda70dc07481fa23dbfaefc4ffe78839fe 测试消息体为空的情况
     * 40777c764be8e50f2a2ae4d2c2d5a926167ba6bf
     */
    @Test
    public void testReceiveMessageBodyIsNull() {
        msg = "{}";

        // 消费消息
        ConsumeStatus result = riskCaseMrmConsumer.receive(msg);

        assertEquals(result, ConsumeStatus.CONSUME_SUCCESS);
    }

    /**
     * 测试正常处理消息的情况
     */
    @Test
    public void testReceiveProcessMessageSuccessfully() {
        // 消费消息
        ConsumeStatus result = riskCaseMrmConsumer.receive(msg);

        // 验证结果
        assertEquals(result, ConsumeStatus.CONSUME_SUCCESS);
        Mockito.verify(riskCaseRepository, Mockito.times(1))
                .batchSave(Mockito.anyList());
//        Mockito.verify(riskCaseVehicleRelationRepository, Mockito.times(1))
//                .batchSave(Mockito.anyList());

    }

    /**
     * 测试增加风险状态流转至处置中，caseIdList 查询结果为空，提前返回
     */
    @Test
    public void testAddRiskStatusToDisposing() {
        EveMqCommonMessage<RiskCaseMrmMessageDTO> message = JacksonUtils.from(msg,
                new TypeReference<EveMqCommonMessage<RiskCaseMrmMessageDTO>>() {
                });
        message.getBody().setVin("missedVinNumber");

        // 消费消息
        ConsumeStatus result = riskCaseMrmConsumer.receive(JacksonUtils.to(message));

        // 验证结果
        assertEquals(result, ConsumeStatus.CONSUME_SUCCESS);
        Mockito.verify(riskCaseRepository, Mockito.times(1))
                .batchSave(Mockito.anyList());

    }
}
