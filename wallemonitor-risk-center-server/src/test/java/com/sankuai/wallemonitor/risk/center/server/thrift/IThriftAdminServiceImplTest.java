package com.sankuai.wallemonitor.risk.center.server.thrift;

import com.sankuai.wallemonitor.risk.center.api.request.CreateWorkstationCaseRequest;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftAdminService;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

public class IThriftAdminServiceImplTest extends SpringTestBase {

    @Resource
    private IThriftAdminService iThriftAdminService;
    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Test
    public void testCreateWorkstationCase() {
        List<RiskCaseDO> caseDOList = riskCaseRepository.queryByParam(RiskCaseDOQueryParamDTO.builder().type(1)
                .statusList(RiskCaseStatusEnum.getTerminal()).build());
        CreateWorkstationCaseRequest request = CreateWorkstationCaseRequest.builder().build();
        request.setCaseId(caseDOList.get(0).getCaseId());
        iThriftAdminService.createWorkstationCase(request);

    }

}