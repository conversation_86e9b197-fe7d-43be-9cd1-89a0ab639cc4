package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.wallemonitor.risk.center.api.request.PublicEventAdminListRequest;
import com.sankuai.wallemonitor.risk.center.api.request.PublicEventAdminSaveRequest;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListPublicEventDetailVO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import com.sankuai.wallemonitor.risk.center.server.thrift.IThriftPublicEventAdminServiceImpl;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
public class IThriftPublicServiceTest extends SpringTestBase {

    @Resource
    private IThriftPublicEventAdminServiceImpl iThriftPublicEventAdminService;

    @Test
    public void testQuery() {
        PublicEventAdminListRequest request  = new PublicEventAdminListRequest();
        request.setPageSize(100);
        request.setPageNum(1);
        request.setCategoryList(Lists.newArrayList("STOP"));

        EveThriftPageResponse<List<AdminListPublicEventDetailVO>> results = iThriftPublicEventAdminService.listPublicEventDetail(request);
        System.out.println(results);
    }

    /**
     *
     */
    @Test
    public void testSave() {
        EveThriftPageResponse<List<AdminListPublicEventDetailVO>> listEveThriftPageResponse = iThriftPublicEventAdminService.listPublicEventDetail(
                PublicEventAdminListRequest.builder()
                        .occurTimeStart("2024-06-01 00:00:00")
                        .occurTimeEnd("2024-09-12 23:59:59")
                        .pageNum(1)
                        .pageSize(50)
                        .build());
        List<AdminListPublicEventDetailVO> adminListPublicEventDetailVOS = listEveThriftPageResponse.getData();
        AdminListPublicEventDetailVO adminListPublicEventDetailVO = adminListPublicEventDetailVOS.get(0);
        iThriftPublicEventAdminService.save(PublicEventAdminSaveRequest.builder()
                .publicEventId(adminListPublicEventDetailVO.getPublicEventId())
                .category("STOP")
                .title("我在测试")
                .build());
    }


}
