package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.HdMapRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.Test;

public class HdMapElementAdapterTest extends SpringTestBase {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private HdMapRepository repository;

    @Test
    public void testGetElementByLocation() {

        // context.setLng("116.5446050628");
        // context.setLat("39.7794187599");
        Date start = new Date();
//        repository.inRestrictParking(RiskRestrictQueryDTO.builder().hdMapVersion("yizhuang_hdmap_v5.185.0.r")
//                .expandMeter(1).meter(20).restrictTypes(Lists.newArrayList(HdMapAreaEnum.DISTRICT_ENTRANCE.getValue()))
//                .position(PositionDO.getPosition(116.5446050628, 39.7794187599, CoordinateSystemEnum.WGS84)).build());

        Date end = new Date();
        System.out.println(end.getTime() - start.getTime());

    }

    @Test
    public void testLaneMap() {

        System.out.println(hdMapAdapter.getHdMapS3UrlList("yizhuang"));

    }

}