package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.GisAdapter;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.server.StartApp;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public class GisAdapterTest {

    @Resource
    private GisAdapter gisAdapter;

    @Test
    public void test() {
        Double lng = 114.385470;
        Double lat = 22.711511;
        System.out.println(
                "停车场-gcj：" + JacksonUtils.to(gisAdapter.getGisInfo(PositionDO.builder().latitude(lat).longitude(lng)
                        .coordinateSystem(CoordinateSystemEnum.GCJ02).build())));
        System.out.println(
                "停车场-wsg84：" + JacksonUtils.to(
                        gisAdapter.getGisInfo(PositionDO.builder().latitude(lat).longitude(lng)
                                .coordinateSystem(CoordinateSystemEnum.WGS84).build())));
        //22.720188,114.380005
        Double lng2 = 114.380005;
        Double lat2 = 22.720188;
        System.out.println(
                "草莓园-gcj：" + JacksonUtils.to(
                        gisAdapter.getGisInfo(PositionDO.builder().latitude(lat2).longitude(lng2)
                                .coordinateSystem(CoordinateSystemEnum.GCJ02).build())));
        System.out.println(
                "草莓园-wsg84：" + JacksonUtils.to(
                        gisAdapter.getGisInfo(PositionDO.builder().latitude(lat2).longitude(lng2)
                                .coordinateSystem(CoordinateSystemEnum.WGS84).build())));
    }

}
