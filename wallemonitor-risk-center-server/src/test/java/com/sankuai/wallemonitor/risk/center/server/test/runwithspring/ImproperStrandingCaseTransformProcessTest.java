package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.domain.process.ImproperStrandingCaseTransformProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Collections;
import java.util.Date;
import javax.annotation.Resource;
import org.apache.thrift.TException;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024/12/13
 */
public class ImproperStrandingCaseTransformProcessTest extends SpringTestBase {

    @Resource
    private ImproperStrandingCaseTransformProcess improperStrandingCaseTransformProcess;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    private static final String vin = "LMTZSV024MC062730";
    private static final String vehicleName = "s20-153";
    private static final String vehicleId = "M5629";

    private static DomainEventDTO<RiskCaseDO> eventDTO = DomainEventDTO.<RiskCaseDO>builder()
            .before(Lists.newArrayList())
            .after(Lists.newArrayList())
            .entry(DomainEventEntryDTO.builder().domainClassName(RiskCaseDO.class.getName())
                    .operateEntry(OperateEnterActionEnum.RISK_EVENT_CONSUMER_ENTER).build())
            .timestamp(System.currentTimeMillis())
            .operator("")
            .traceId("")
            .extInfo(DomainEventExtInfoDTO.builder().build())
            .build();

    /**
     * 测试被过滤的情况
     */
    @Test
    public void testMessagePass() throws TException {
        RiskCaseDO riskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.VEHICLE_STAND_STILL,
                RiskCaseStatusEnum.NO_DISPOSAL);
        eventDTO.setAfter(Lists.newArrayList(riskCaseDO));

        boolean process = improperStrandingCaseTransformProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCaseDO.class));
        System.out.println(process);
    }


    /**
     * 测试创建情况
     */
    @Test
    public void testCreate() throws TException {
        beforeCreate();
        // 接收到【停滞】RiskCaseDO(type=9，status=10) & changeType=ENTITY_EMPTY_TO_VALUE的消息
        RiskCaseDO riskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.NO_DISPOSAL);

        eventDTO.setAfter(Lists.newArrayList(riskCaseDO));

        boolean process = improperStrandingCaseTransformProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCaseDO.class));
        System.out.println(process);

    }

    /**
     * 测试创建且CheckingQueueItem存在的情况
     */
    @Test
    public void testCreateWithCheckingQueueItem() throws TException {
        Date time = new Date();
        // RiskCase表中插入一条【停滞】RiskCase(type=9&status=10)的记录
        RiskCaseDO riskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.NO_DISPOSAL, time);
        riskCaseRepository.save(riskCaseDO);

        // RiskCaseVehicleRelation表中插入一条与riskcase的eventId相同的车辆关联记录
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = getRiskCaseVehicleRelationDO(
                riskCaseDO);
        riskCaseVehicleRelationRepository.save(riskCaseVehicleRelationDO);

        // RiskCheckingQueueItem表中插入一条RiskCheckingQueueItem(status=10)的记录
        RiskCheckingQueueItemDO riskCheckingQueueItemDO = getRiskCheckingQueueItemDO(riskCaseDO,
                riskCaseVehicleRelationDO);
        riskCheckQueueRepository.save(riskCheckingQueueItemDO);

        // 接收到【停滞】RiskCaseDO(type=9，status=10) & changeType=ENTITY_EMPTY_TO_VALUE的消息
        RiskCaseDO riskCaseDO1 = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.NO_DISPOSAL, time);

        eventDTO.setAfter(Lists.newArrayList(riskCaseDO1));

        boolean process = improperStrandingCaseTransformProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCaseDO.class));
        System.out.println(process);

    }


    @Test
    public void testUpdated() throws TException, InterruptedException {
        Date time = new Date();
        // RiskCase表中插入一条【停滞】RiskCase(status=30，type=9)的记录
        RiskCaseDO strandingRiskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.DISPOSED, time);
        riskCaseRepository.save(strandingRiskCaseDO);

        // RiskCase表中插入一条【停滞不当】RiskCase(status=10，type=1)的记录
        RiskCaseDO improperStrandingRiskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER,
                RiskCaseTypeEnum.VEHICLE_STAND_STILL, RiskCaseStatusEnum.NO_DISPOSAL, time);
        riskCaseRepository.save(improperStrandingRiskCaseDO);

        // RiskCaseVehicleRelation表中插入一条与【停滞】riskcase的eventId相同的车辆关联记录
        RiskCaseVehicleRelationDO strandingCaseRelationDO = getRiskCaseVehicleRelationDO(strandingRiskCaseDO);
        riskCaseVehicleRelationRepository.save(strandingCaseRelationDO);

        // RiskCaseVehicleRelation表中插入一条与【停滞不当】riskcase的eventId相同的车辆关联记录
        RiskCaseVehicleRelationDO improperStrandingCaseRelationDO = getRiskCaseVehicleRelationDO(
                improperStrandingRiskCaseDO);
        riskCaseVehicleRelationRepository.save(improperStrandingCaseRelationDO);

        // RiskCheckingQueueItem表中插入一条RiskCheckingQueueItem(status=10)的记录
        RiskCheckingQueueItemDO riskCheckingQueueItemDO = getRiskCheckingQueueItemDO(strandingRiskCaseDO,
                strandingCaseRelationDO);
        riskCheckQueueRepository.save(riskCheckingQueueItemDO);

        // 接收到RiskCaseDO(type=9，status=30) 的消息
        RiskCaseDO riskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.DISPOSED, time);
        riskCaseDO.setCaseId(strandingRiskCaseDO.getCaseId());
        eventDTO.setAfter(Lists.newArrayList(riskCaseDO));

        boolean process = improperStrandingCaseTransformProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCaseDO.class));
        System.out.println(process);
    }


    @Test
    public void testUpdateWithoutRiskCase() throws TException {
        Date time = new Date();
        // RiskCase表中插入一条【停滞】RiskCase(status=30，type=9)的记录
        RiskCaseDO strandingRiskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.DISPOSED, time);
        riskCaseRepository.save(strandingRiskCaseDO);

        // RiskCaseVehicleRelation表中插入一条与【停滞】riskcase的eventId相同的车辆关联记录
        RiskCaseVehicleRelationDO strandingCaseRelationDO = getRiskCaseVehicleRelationDO(strandingRiskCaseDO);
        riskCaseVehicleRelationRepository.save(strandingCaseRelationDO);

        // RiskCheckingQueueItem表中插入一条RiskCheckingQueueItem(status=10)的记录
        RiskCheckingQueueItemDO riskCheckingQueueItemDO = getRiskCheckingQueueItemDO(strandingRiskCaseDO,
                strandingCaseRelationDO);
        riskCheckQueueRepository.save(riskCheckingQueueItemDO);

        // 接收到RiskCaseDO(type=9，status=30) 的消息
        RiskCaseDO riskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.DISPOSED, time);
        riskCaseDO.setCaseId(strandingRiskCaseDO.getCaseId());
        eventDTO.setAfter(Lists.newArrayList(riskCaseDO));

        boolean process = improperStrandingCaseTransformProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCaseDO.class));
        System.out.println(process);
    }

    @Test
    public void testUpdateWithConfirmCheckingQueueItem() throws TException {
        Date time = new Date();
        // RiskCase表中插入一条【停滞】RiskCase(status=30，type=9)的记录
        RiskCaseDO strandingRiskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.DISPOSED, time);
        riskCaseRepository.save(strandingRiskCaseDO);

        // RiskCase表中插入一条【停滞不当】RiskCase(status=10，type=1)的记录
        RiskCaseDO improperStrandingRiskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER,
                RiskCaseTypeEnum.VEHICLE_STAND_STILL, RiskCaseStatusEnum.NO_DISPOSAL, time);
        riskCaseRepository.save(improperStrandingRiskCaseDO);

        // RiskCaseVehicleRelation表中插入一条与【停滞】riskcase的eventId相同的车辆关联记录
        RiskCaseVehicleRelationDO strandingCaseRelationDO = getRiskCaseVehicleRelationDO(strandingRiskCaseDO);
        riskCaseVehicleRelationRepository.save(strandingCaseRelationDO);

        // RiskCaseVehicleRelation表中插入一条与【停滞不当】riskcase的eventId相同的车辆关联记录
        RiskCaseVehicleRelationDO improperStrandingCaseRelationDO = getRiskCaseVehicleRelationDO(
                improperStrandingRiskCaseDO);
        riskCaseVehicleRelationRepository.save(improperStrandingCaseRelationDO);

        // RiskCheckingQueueItem表中插入一条RiskCheckingQueueItem(status=10)的记录
        RiskCheckingQueueItemDO riskCheckingQueueItemDO = getRiskCheckingQueueItemDO(strandingRiskCaseDO,
                strandingCaseRelationDO);
        riskCheckingQueueItemDO.setStatus(RiskQueueStatusEnum.CONFIRMED_RISK);
        riskCheckQueueRepository.save(riskCheckingQueueItemDO);

        // 接收到RiskCaseDO(type=9，status=30) 的消息
        RiskCaseDO riskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.DISPOSED, time);
        riskCaseDO.setCaseId(strandingRiskCaseDO.getCaseId());
        eventDTO.setAfter(Lists.newArrayList(riskCaseDO));

        boolean process = improperStrandingCaseTransformProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCaseDO.class));
        System.out.println(process);
    }

    private RiskCheckingQueueItemDO getRiskCheckingQueueItemDO(RiskCaseDO riskCase,
            RiskCaseVehicleRelationDO relation) {
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, Collections.singletonList(vin),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.VEHICLE_STAND_STILL,
                riskCase.getOccurTime().getTime());
        String eventId = riskCase.getEventId();
        RiskCheckingQueueItemDO riskCheckingQueueItemDO = new RiskCheckingQueueItemDO();
        riskCheckingQueueItemDO.setVin(relation.getVin());
        riskCheckingQueueItemDO.setTmpCaseId(caseId);
        riskCheckingQueueItemDO.setEventId(eventId);
        riskCheckingQueueItemDO.setType(RiskCaseTypeEnum.VEHICLE_STAND_STILL);
        riskCheckingQueueItemDO.setRecallTime(riskCase.getRecallTime());
        riskCheckingQueueItemDO.setOccurTime(riskCase.getOccurTime());
        riskCheckingQueueItemDO.setStatus(RiskQueueStatusEnum.VALIDATING);
        return riskCheckingQueueItemDO;
    }

    private static RiskCaseVehicleRelationDO getRiskCaseVehicleRelationDO(RiskCaseDO strandingRiskCaseDO) {
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = new RiskCaseVehicleRelationDO();
        riskCaseVehicleRelationDO.setCaseId(strandingRiskCaseDO.getCaseId());
        riskCaseVehicleRelationDO.setEventId(strandingRiskCaseDO.getEventId());
        riskCaseVehicleRelationDO.setVin(vin);
        return riskCaseVehicleRelationDO;
    }


    private RiskCaseDO getRiskCaseDO(RiskCaseSourceEnum source, RiskCaseTypeEnum type, RiskCaseStatusEnum status) {
        return getRiskCaseDO(source, type, status, new Date());
    }

    private RiskCaseDO getRiskCaseDO(RiskCaseSourceEnum source, RiskCaseTypeEnum type, RiskCaseStatusEnum status,
            Date time) {
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, Collections.singletonList(vin),
                source, type, time.getTime());
        String eventId = idGenerateRepository.generateEventId(time, type.name(), vehicleName);
        RiskCaseDO riskCaseDO = new RiskCaseDO();
        riskCaseDO.setCaseId(caseId);
        riskCaseDO.setEventId(eventId);
        riskCaseDO.setType(type);
        riskCaseDO.setStatus(status);
        riskCaseDO.setSource(source);
        riskCaseDO.setRecallTime(time);
        riskCaseDO.setOccurTime(time);
        riskCaseDO.setCloseTime(time);
        riskCaseDO.setCreateTime(time);
        riskCaseDO.setUpdateTime(time);
        return riskCaseDO;
    }


    private void beforeCreate() {
        // RiskCase表中插入一条【停滞】RiskCase(type=9&status=10)的记录
        RiskCaseDO riskCaseDO = getRiskCaseDO(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                RiskCaseStatusEnum.NO_DISPOSAL);
        riskCaseRepository.save(riskCaseDO);

        // RiskCaseVehicleRelation表中插入一条与riskcase的eventId相同的车辆关联记录
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = getRiskCaseVehicleRelationDO(
                riskCaseDO);
        riskCaseVehicleRelationRepository.save(riskCaseVehicleRelationDO);
    }

}
