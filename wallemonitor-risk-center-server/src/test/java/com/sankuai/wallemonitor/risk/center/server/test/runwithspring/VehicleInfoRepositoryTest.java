package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

public class VehicleInfoRepositoryTest extends SpringTestBase {

    @Resource
    VehicleInfoRepository vehicleInfoRepository;


    @Test
    public void queryByVinList() {
        ArrayList<String> vinLIst = Lists.newArrayList("LMTZSV027NC027844", "LA71AUB14S0501797");

        List<VehicleInfoDO> vehicleInfoDOS = vehicleInfoRepository.queryByVinList(vinLIst);

        System.out.println(vehicleInfoDOS);
    }
}
