package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;


import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.StatusMonitorStandStillHandleStrategy;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmFilterDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * StatusMonitorStandStillHandleStrategy 测试类
 */
public class StatusMonitorStandStillHandleStrategyTest {

    @InjectMocks
    private StatusMonitorStandStillHandleStrategy statusMonitorStandStillHandleStrategy;

    @Mock
    private RiskCaseRepository riskCaseRepository;

    @Mock
    private RiskCaseOperateService riskCaseOperateService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 process 方法，当 CallMrmStrategyConfigDTO 需要过滤时
     */
    @Test
    public void testProcessWhenNeedFilter() throws Exception {
        // arrange
        CallMrmStrategyConfigDTO callMrmStrategyConfigDTO = mock(CallMrmStrategyConfigDTO.class);
        RiskCaseCallMrmFilterDTO filterDTO = new RiskCaseCallMrmFilterDTO();
        RiskCaseDO riskCaseDO = new RiskCaseDO();
        when(callMrmStrategyConfigDTO.isNeedFilter(anyMap(), any())).thenReturn(true);

        // act
        statusMonitorStandStillHandleStrategy.process(callMrmStrategyConfigDTO, filterDTO, riskCaseDO);

        // assert
        verify(riskCaseOperateService, never()).updateRiskCaseAndSendMsg(any(), any(), any());
    }

    /**
     * 测试 process 方法，当 CallMrmStrategyConfigDTO 不需要过滤时
     */
    @Test
    public void testProcessWhenNotNeedFilter() throws Exception {
        // arrange
        CallMrmStrategyConfigDTO callMrmStrategyConfigDTO = mock(CallMrmStrategyConfigDTO.class);
        RiskCaseCallMrmFilterDTO filterDTO = new RiskCaseCallMrmFilterDTO();
        RiskCaseDO riskCaseDO = new RiskCaseDO();
        when(callMrmStrategyConfigDTO.isNeedFilter(anyMap(), any())).thenReturn(false);

        // act
        statusMonitorStandStillHandleStrategy.process(callMrmStrategyConfigDTO, filterDTO, riskCaseDO);

        // assert
        verify(riskCaseOperateService, times(1)).updateRiskCaseAndSendMsg(eq(callMrmStrategyConfigDTO), eq(filterDTO),
                eq(riskCaseDO));
    }

    /**
     * 测试 process 方法，当传入的 CallMrmStrategyConfigDTO 为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testProcessWithNullCallMrmStrategyConfigDTO() throws Exception {
        // arrange
        RiskCaseCallMrmFilterDTO filterDTO = new RiskCaseCallMrmFilterDTO();
        RiskCaseDO riskCaseDO = new RiskCaseDO();


    }
}
