package com.sankuai.wallemonitor.risk.center.server.test.unit.action;

import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISAIFridayClassifyV2;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.FridayOpenAiAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.SFTFridayVideoClassifyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.SFTFridayVerifyRiskResultVTO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

public class ISAIFridayClassifyV2ActionTest extends ServiceTestBase {

    @Spy
    @InjectMocks
    private ISAIFridayClassifyV2 isaiFridayClassifyV2;

    @Spy
    @InjectMocks
    private FridayOpenAiAdapter fridayOpenAiAdapter;

    private static final String systemPrompt = "你是一个专业的自动驾驶安全评估系统，擅长准确辨别车辆是否处于风险或者正常的情况，尤其擅长辨别车辆停滞时刻的风险，并判断处自动驾驶的车辆，是否需要人工接管。\n" +
            "你不会撒谎并告诉其他人不合理的、虚假的结论，总是谨慎的分析所给的信息，做出逻辑完整的、肯定的、准确的回答。\n" +
            "在此定义：\n" +
            "- 位于施工区域、前方车辆上下客、前方车辆双闪故障、前方车辆熄火、前方车辆逆行等场景时，无法继续安全的、或者合理等待后通行，是需要人工接管的场景。\n" +
            "- 不断有其他车辆不停绕行本车时，是需要人工接管的场景。\n" +
            "- 当本自动驾驶车辆正在等红灯 或者 前方社会车辆等待红灯本自动车辆排在后方等待通行时，是不需要人工接管的场景。";

    private static final String userPrompt = "请观看连续的自动驾驶车辆视频图片，最后一张图时刻本车处于停滞状态。\n" +
            "请你分析这些连续的图片，按照如下格式进行总结,{}内为推理条件，请使用，并替换成推理后的结果。:\n" +
            "# 车辆行为:\n" +
            "- 是否正常行驶后减速:{从连续视频帧里面观察,本自动驾驶车辆是否缓慢减速至停滞}\n" +
            "- 是否倒行:{从连续视频帧里面观察，本自动驾驶车辆是否正在一点点后退}\n" +
            "- 是否缓慢蠕动行驶: {从连续视频帧里面观察，车辆是否在一点点在蠕动，有前进或者后退，并非带速行驶的速度}\n" +
            "- 道路特征：\n" +
            "# 车道环境: {从连续视频帧里面观察车辆所在的位置，是在中间、左侧，还是右侧车道}\n" +
            "- 车道类型: {是在机动车行驶的车道上，还是人车混行的车道上，还是在园区内部的道路，还是在施工区域的道路}\n" +
            "# 周围障碍物行为：\n" +
            "- 是否有其他机动车或非机动车绕行至前方:{从视频里面观察,是否有其他机动车或者非机动车，从后方绕行到本自动驾驶车辆的正前方车道，并继续前行}\n" +
            "- 是否有其他机动车或者非机动车和本自动驾驶车辆相对行驶:{从视频里面观察，如果本自动驾驶车辆前方，有机动车不断向本车行驶，则算}\n" +
            "# 正前方车辆障碍物行为:\n" +
            "- 是否双闪：{从连续视频帧里面观察，尾灯是否频繁闪动、有规则的闪动并且为黄色，如果是则为双闪}\n" +
            "- 是否靠边熄火: {从连续视频帧里面观察，车辆是否处于右侧靠边并且没有尾灯亮起的状态，是则为熄火，注意尾灯高亮才算亮起}\n" +
            "- 是否工程车辆作业: {从连续视频帧里面观察，前方有大型工程车辆的作业部件正在运动，并且车辆周围有作业人员，正在工作}\n" +
            "- 是否上下客:{从连续视频帧里面观察，前方车辆正在有人上车或者下车}\n" +
            "- 是否正在装卸货:{从连续视频帧里面观察，前方车辆车厢打开，有人正在搬运货物}\n" +
            "# 结论:\n" +
            "- 是否需要接管: {请你根据推理出来的信息和接管规则，告诉我是否需要接管本车辆,让人类介入以便继续通行。}";


    @Test
    public void testFridayAdapter() {
        SFTFridayVideoClassifyConfigDTO sftFridayVideoClassifyConfigDTO =
                SFTFridayVideoClassifyConfigDTO.builder().startNSeconds(0).endNSeconds(0).step(1).votingTimes(3).systemPrompt(systemPrompt).userPrompt(userPrompt).build();

        ReflectionTestUtils.setField(isaiFridayClassifyV2, "sftFridayVideoClassifyConfigDTO", sftFridayVideoClassifyConfigDTO);
        ReflectionTestUtils.setField(fridayOpenAiAdapter, "sftFridayVideoClassifyConfigDTO", sftFridayVideoClassifyConfigDTO);

        FridayModelParamVTO fridayModelParamVTO = FridayModelParamVTO.builder().vin("LMTZSV029NC053376")
                .modelName(sftFridayVideoClassifyConfigDTO.getModelName())
                .appId(sftFridayVideoClassifyConfigDTO.getAppId())
                .timeout(10)
                .occurTime(DatetimeUtil.parseDate("2025-07-01 20:22:48", "yyyy-MM-dd hh:mm:ss")).build();

        Object o = ReflectionTestUtils.invokeMethod(isaiFridayClassifyV2, "verifySFTResult", fridayModelParamVTO);

        System.out.println(o);



    }
}
