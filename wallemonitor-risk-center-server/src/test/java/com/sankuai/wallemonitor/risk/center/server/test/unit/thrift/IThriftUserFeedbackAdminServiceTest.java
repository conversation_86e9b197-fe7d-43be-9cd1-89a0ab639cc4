package com.sankuai.wallemonitor.risk.center.server.test.unit.thrift;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.sankuai.walleeve.dto.TokenCheckDTO;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.JwtUtil;
import com.sankuai.wallemonitor.risk.center.api.request.UserFeedbackReportRequest;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import com.sankuai.wallemonitor.risk.center.server.thrift.IThriftUserFeedbackAdminServiceImpl;
import java.util.Collections;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;

public class IThriftUserFeedbackAdminServiceTest extends ServiceTestBase {

    @InjectMocks
    private IThriftUserFeedbackAdminServiceImpl userFeedbackAdminService;

    private UserFeedbackReportRequest request;

    private TokenCheckDTO tokenCheckDTO;

    @Before
    public void setUp() {
        request = UserFeedbackReportRequest.builder()
                .feedbackChannel(1)
                .feedbackType(1)
                .feedbackContent("content")
                .urlList(Collections.singletonList("sankuai.com/url"))
                .phoneNumber("13333333333")
                .build();
        ReflectionTestUtils.setField(request, "token", "ImLoginToken");

        // mock jwtToken 验证
        PowerMockito.mockStatic(JwtUtil.class);
        tokenCheckDTO = TokenCheckDTO.builder()
                .isValid(true)
                .openId("openId")
                .build();

        // mock 分布式锁
        mockRedisAndLock();
    }

    /**
     * 测试jwt登录过期情况
     */
    @Test
    public void testReportFeedback() {
        // mock jwtToken验证
        tokenCheckDTO = TokenCheckDTO.builder().isValid(false).build();
        PowerMockito.when(JwtUtil.getTokenValidAndSubject(any(), any())).thenReturn(tokenCheckDTO);

        // 运行
        EveThriftResponse<EmptyResponse> response = userFeedbackAdminService.reportFeedback(request);

        // 验证
        assertEquals(401, response.getCode());
        verify(feedbackRecordRepository, times(0)).save(any());
    }

    /**
     * 测试正常情况
     */
    //    @Test
    //    public void testReportFeedbackNormal() {
    //        // mock jwtToken验证
    //        PowerMockito.when(JwtUtil.getTokenValidAndSubject(any(), any())).thenReturn(tokenCheckDTO);
    //
    //        // 运行
    //        EveThriftResponse<EmptyResponse> response = userFeedbackAdminService.reportFeedback(request);
    //
    //        // 验证
    //        assertEquals(response.getCode(), 0);
    //        verify(feedbackRecordRepository, times(1)).save(FeedbackRecordDO.builder()
    //                .userId("openId")
    //                .feedbackType(1)
    //                .feedbackContent("content")
    //                .feedbackChannel(1)
    //                .attachmentUrl("sankuai.com/url")
    //                .extra("{\"phoneNumber\":\"13333333333\"}")
    //                .build());
    //    }
}
