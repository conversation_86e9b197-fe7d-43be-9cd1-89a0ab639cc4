package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseHighNegativeCancelSafetyProcess;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskDetectRecordCounterUpdatedTriggerProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallSafetyEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;

public class RiskCaseHighNegativeCancelSafetyProcessTest extends SpringTestBase {

    @Resource
    private RiskCaseHighNegativeCancelSafetyProcess riskCaseHighNegativeCancelSafetyProcess;

    @Resource
    private RiskDetectRecordCounterUpdatedTriggerProcess riskDetectRecordCounterUpdatedTriggerProcess;

    @Resource
    private RiskCaseRepository caseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository vehicleRelationRepository;


    private static final String riskDoStr = "{\"id\":1612299,\"caseId\":\"MT0845220241115161416S01T01\",\"type\":\"VEHICLE_STAND_STILL\",\"placeCode\":\"shenzhenlonghua\",\"status\":\"NO_DISPOSAL\",\"messageId\":\"\",\"messageVersion\":\"\",\"eventId\":\"20241115161416734_adc-stagnant-recall_s20-186\",\"source\":\"SAFEGUARD_SYSTEM\",\"occurTime\":\"2024-11-15 16:14:17\",\"closeTime\":\"1970-01-01 08:00:01\",\"createTime\":\"2024-11-15 16:14:16\",\"updateTime\":\"2024-11-15 16:14:16\",\"extInfo\":{\"city\":\"深圳市\",\"are\":\"龙华区\",\"poi\":\"深圳协威雅铁塑制品有限公司\",\"position\":{\"latitude\":22.704744628869364,\"longitude\":114.06175320881607,\"coordinateSystem\":\"GCJ02\"}},\"level\":0,\"isDeleted\":\"NOT_DELETED\",\"mrmCalled\":\"NO_CALL\",\"disposalTime\":81,\"groupMessageMap\":{}}";

    private static final String riskCaseVehicleRelationStr = "{\"caseId\":\"MT0845220241115161416S01T01\",\"eventId\":\"20241115161416734_adc-stagnant-recall_s20-186\",\"vin\":\"LMTZSV023MC023286\",\"vehicleSnapshotInfo\":{\"vin\":\"LMTZSV023MC023286\",\"vehicleId\":\"MT08452\",\"vehicleName\":\"s20-186\",\"purpose\":\"路测-AB深圳测试\",\"vhr\":\"VHR_GREAT_THAN_ONE\",\"position\":{\"latitude\":22.707436,\"longitude\":114.05663,\"coordinateSystem\":\"WGS84\"},\"placeCode\":\"shenzhenlonghua\",\"autocarVersion\":\"65.11.3\",\"driveMode\":1,\"withRescueOrder\":false,\"withAccidentOrder\":false,\"withMaintenanceOrder\":false,\"isWaitingRed\":false},\"status\":\"INIT\",\"type\":\"VEHICLE_STAND_STILL\",\"occurTime\":\"2024-11-15 16:14:16\",\"milliBeginTime\":\"2024-11-15 16:14:16\",\"purpose\":\"路测-AB深圳测试\",\"vhrMode\":\">1\",\"isDeleted\":\"NOT_DELETED\",\"extInfoStr\":\"\"}";
    private RiskCaseDO riskCaseDO;
    private RiskCaseVehicleRelationDO vehicleRelationDO;

    @Before
    @SneakyThrows
    public void init() {

        riskCaseDO = JacksonUtils.from(riskDoStr, RiskCaseDO.class);
        vehicleRelationDO = JacksonUtils.from(riskCaseVehicleRelationStr,
                RiskCaseVehicleRelationDO.class);
        riskCaseDO.setType(RiskCaseTypeEnum.SPECIAL_AREA_STRANDING);
        riskCaseDO.setStatus(RiskCaseStatusEnum.DISPOSED);
        riskCaseDO.setCallSafety(CallSafetyEnum.CALLED);
        vehicleRelationDO.setType(RiskCaseTypeEnum.SPECIAL_AREA_STRANDING);
        vehicleRelationDO.setVin("LMTZSV020MC042359");
        caseRepository.save(riskCaseDO);
        vehicleRelationRepository.save(vehicleRelationDO);
    }


    @Test
    @SneakyThrows
    public void testProcess() {
        DomainEventDTO<RiskCaseDO> eventDTO = DomainEventDTO.<RiskCaseDO>builder().build();
        eventDTO.setBefore(Lists.newArrayList());
        eventDTO.setAfter(Lists.newArrayList(riskCaseDO));
        eventDTO.setEntry(
                DomainEventEntryDTO.builder().domainClassName(RiskCaseDO.class.getName()).operateEntry(
                                OperateEnterActionEnum.VEHICLE_DATA_CONSUMER_ENTER)
                        .build());
        eventDTO.setTimestamp(System.currentTimeMillis());
        eventDTO.setOperator("");
        eventDTO.setTraceId("");
        eventDTO.setExtInfo(DomainEventExtInfoDTO.builder().build());
        riskCaseHighNegativeCancelSafetyProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCaseDO.class));
    }

}