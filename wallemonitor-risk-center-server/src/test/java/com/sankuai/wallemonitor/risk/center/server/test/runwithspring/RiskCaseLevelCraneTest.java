package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.server.consumer.RiskCaseEventConsumer;
import com.sankuai.wallemonitor.risk.center.server.crane.RiskCaseLevelCrane;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.UUID;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class RiskCaseLevelCraneTest extends SpringTestBase {

    @Resource
    private RiskCaseLevelCrane riskCaseLevelCrane;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private RiskCaseEventConsumer riskCaseEventConsumer;


    private String s_start = "{\"type\":20,\"body\":{\"eventId\":\"25645\",\"source\":3,\"type\":1,\"status\":10,\"vinList\":[\"LMTZSV022NC017593\"],\"traceId\":\"\"},\"timestamp\":1728712604000}";
    private String s_end = "{\"type\":20,\"body\":{\"eventId\":\"25645\",\"source\":3,\"type\":1,\"status\":30,\"vinList\":[\"LMTZSV022NC017593\"],\"traceId\":\"\"},\"timestamp\":1728712804000}";


    @Test
    @SneakyThrows
    public void test() {

        riskCaseEventConsumer.receive(s_start);

//        /**
//         * 事件ID
//         */
//        private String eventId;
//        /**
//         * 来源
//         */
//        private RiskCaseSourceEnum source;
//        /**
//         * 类型
//         */
//        private RiskCaseTypeEnum type;
//        /**
//         * 状态
//         */
//        private RiskCaseStatusEnum status;
//        /**
//         * 车辆列表
//         */
//        private List<String> vinList;
//        /**
//         * 事件关联的traceId
//         */
//        private String traceId;
//
//        /**
//         * 发生时间
//         */
//        private Long timestamp;
//
//        /**
//         * 并排开始时间
//         */
//        private String sideBySideTimestamp;
//
//        /**
//         * 扩展信息
//         */
//        private String messageExtInfo;
//
//        /**
//         * 上游直接生成好caseId
//         */
//        private String caseId;
        riskCaseOperateService.createOrUpdateRiskCase(RiskCaseUpdatedParamDTO.builder()
                .eventId(UUID.randomUUID().toString())
                .caseId(UUID.randomUUID().toString())
                .source(RiskCaseSourceEnum.BEACON_TOWER)
                .type(RiskCaseTypeEnum.SPECIAL_AREA_STRANDING)
                .status(RiskCaseStatusEnum.IN_DISPOSAL)
                .vinList(Lists.newArrayList("LMTZSV022NC017593"))
                .timestamp(System.currentTimeMillis() - 60 * 1000)
                .build());

        riskCaseLevelCrane.run();


    }

}