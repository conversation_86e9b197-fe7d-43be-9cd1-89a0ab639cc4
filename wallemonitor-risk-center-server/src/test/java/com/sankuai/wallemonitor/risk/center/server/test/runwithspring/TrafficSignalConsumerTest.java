package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.dto.MonitorDataMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.OnboardCommonMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficData;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficData.TrafficHmiSignal;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficData.TrafficHmiSignal.HmiSignal;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.LineType;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.server.consumer.OnboardMessageConsumer;
import com.sankuai.wallemonitor.risk.center.server.consumer.TrafficSignalConsumer;
import com.sankuai.wallemonitor.risk.center.server.consumer.VehicleMonitorConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Collections;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class TrafficSignalConsumerTest extends SpringTestBase {

    @Resource
    private TrafficSignalConsumer trafficSignalConsumer;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private OnboardMessageConsumer messageConsumer;


    @Resource
    private VehicleMonitorConsumer monitorConsumer;

    @Test
    public void testMonitor() {
        String monitor = "{\"time\":1729075010842053,\"measurement\":\"vehicle\",\"tags\":{\"vin\":\"LMTZSV020MC042359\",\"vehicle_name\":\"S20-139\"},\"fields\":{\"mrm_seat_role\":null,\"online_status\":2,\"heading\":211.1042,\"mrm_seat_status\":1,\"latitude\":40.095966,\"kmph\":0.0,\"present_bat\":2,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"soc2\":39.0,\"position_source\":\"gnss_bestpos\",\"gear\":\"P\",\"soc1\":16.0,\"longitude\":116.55327}}";
        MonitorDataMessageDTO vehicleMonitorDTO = JacksonUtils.from(monitor, MonitorDataMessageDTO.class);
        vehicleMonitorDTO.setTime(System.currentTimeMillis());
        monitorConsumer.receive(JacksonUtils.to(vehicleMonitorDTO));
    }

    @Test
    public void testTrafficMonitor() {
        String trafficData = "{\"timestamp\":1729075187773,\"trafficHmiSignal\":{\"currentReflineId\":\"1\",\"targetReflineId\":\"1\"},\"vin\":\"LMTZSV020MC042359\"}";
        TrafficData trafficData1 = JacksonUtils.from(trafficData, TrafficData.class);
        trafficData1.setTimestamp(System.currentTimeMillis());
        trafficSignalConsumer.receive(JacksonUtils.to(trafficData1));
    }

    @Test
    @SneakyThrows
    public void testTraffic() {
        String trafficData = "{\"timestamp\":1729075187773,\"trafficHmiSignal\":{\"currentReflineId\":\"1\",\"targetReflineId\":\"1\"},\"vin\":\"LMTZSV028NC050243\"}";
        String trafficData2 = "{\"containLights\":true,\"timestamp\":1731392277124,\"trafficLightList\":[{\"bulbs\":[{\"color\":\"RED\",\"type\":\"CIRCLE\"}],\"color\":\"RED\",\"criticalLevel\":\"TrafficLightCriticalLevel_HIGH\",\"id\":\"s_cc570cf82135\",\"isBlinking\":false,\"source\":\"DETECTION\",\"status\":\"NORMAL\"}],\"vin\":\"LMTZSV028NC050243\"}";
        TrafficData trafficData1 = JacksonUtils.from(trafficData, TrafficData.class);
        TrafficData trafficData2Obj = JacksonUtils.from(trafficData2, TrafficData.class);
        trafficData1.setTrafficHmiSignal(
                TrafficHmiSignal.builder().hmiSignal(HmiSignal.builder().finalSignalId("s_cc570cf82135").build())
                        .build());
        trafficSignalConsumer.receive(JacksonUtils.to(trafficData1));
        trafficSignalConsumer.receive(JacksonUtils.to(trafficData2Obj));
        JacksonUtils.to(vehicleRuntimeInfoContextRepository.getFromCache("LMTZSV028NC050243"));
        Thread.sleep(1000);
        trafficData1.setTimestamp(System.currentTimeMillis());
        trafficData2Obj.setTimestamp(System.currentTimeMillis());
        trafficData1.setTrafficHmiSignal(
                TrafficHmiSignal.builder().hmiSignal(HmiSignal.builder().finalSignalId("wrong_id").build())
                        .build());
        trafficSignalConsumer.receive(JacksonUtils.to(trafficData1));
        trafficSignalConsumer.receive(JacksonUtils.to(trafficData2Obj));
        JacksonUtils.to(vehicleRuntimeInfoContextRepository.getFromCache("LMTZSV028NC050243"));

    }

    @Test
    public void testOnBoardMessageMonitor() {
        OnboardCommonMessageDTO<VehicleHighNegativeMessageDTO> commonMessageDTO = JacksonUtils.from(
                "{\"data\":{\"highNegativeEventMeta\":{\"commonMeta\":{\"distanceToNextJunction\":{}},\"constructionZoneMeta\":{},\"isValid\":true,\"touchLineMeta\":{}}},\"timestamp\":\"1729141491738030081\",\"topic\":\"/walle/planning/planner_result_meta\",\"vin\":\"LMTZSV021MC001593\"}",
                new TypeReference<OnboardCommonMessageDTO<VehicleHighNegativeMessageDTO>>() {
                });
        commonMessageDTO.setTimestamp(String.valueOf(System.currentTimeMillis() * 1000));
        commonMessageDTO.setTopic("/walle/planning/planner_result_meta");
        commonMessageDTO.setVin("LMTZSV020MC042359");
        VehicleHighNegativeMessageDTO dto = VehicleHighNegativeMessageDTO.builder()
                .highNegativeEventMeta(VehicleHighNegativeMessageDTO.HighNegativeEventMeta.builder()
                        .isValid(true)
                        .commonMeta(VehicleHighNegativeMessageDTO.CommonMeta.builder()
                                .distanceToNextJunction(VehicleHighNegativeMessageDTO.OptionalDouble.builder()
                                        .hasValue(true)
                                        .value(100.5)
                                        .build())
                                .isEgoOnReverseLane(false)
                                .build())
                        .touchLineMeta(VehicleHighNegativeMessageDTO.TouchLineMeta.builder()
                                .isTouchLine(true)
                                .lineType(LineType.WHITE_SOLID_LINE)
                                .build())
                        .constructionZoneMeta(VehicleHighNegativeMessageDTO.ConstructionZoneMeta.builder()
                                .isPathOverlapWithConstructionZone(true)
                                .distanceToFrontConstructionZone(0.0)
                                .build())
                        .build())
                .build();
        commonMessageDTO.setData(dto);
        messageConsumer.consume(JacksonUtils.to(commonMessageDTO));
    }

    @Test
    public void testOnBoardMessageMonitor2() {
        String msg = "{\"data\":{\"arbitrationMonitorContext\":{\"noiseCategory\":\"NOISE_CATEGORY_LOW\"},\"errorCode\":\"OK\",\"perceptionObstacle\":[{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.31016950740394644,\"y\":0.9506812697617968,\"z\":0},\"displayBox\":{},\"height\":1.7102184295654297,\"id\":911062,\"length\":4.494838237762451,\"obstacleType\":{\"coarseType\":\"CAR\",\"fineType\":\"FINE_CAR\"},\"position\":{\"x\":231153.76708595388,\"y\":2514860.371278826,\"z\":46.383682170138314},\"theta\":1.886167654088017,\"trackingTime\":0,\"type\":\"VEHICLE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":2.0023789405822754}],\"pose\":{}},\"timestamp\":\"1736307004291293033\",\"topic\":\"/apollo/perception/obstacles\",\"vin\":\"3LN6L5SU2LR601635\"}";

        OnboardCommonMessageDTO<PerceptionObstacleDTO> commonMessageDTO = JacksonUtils.from(msg,
                new TypeReference<OnboardCommonMessageDTO<PerceptionObstacleDTO>>() {});
        commonMessageDTO.setTimestamp(String.valueOf(System.currentTimeMillis()));
        VehicleRuntimeInfoContextDO contextDO = vehicleRuntimeInfoContextRepository
                .getFromCache(commonMessageDTO.getVin());
        // GeoToolsUtil.wgs84ToGcj02(114.38263163171152,22.71972476934015).getLocationStr(CoordinateSystemEnum.GCJ02);
        contextDO.setLng("114.38263163171152");
        contextDO.setLat("22.71972476934015");
        vehicleRuntimeInfoContextRepository.save(contextDO);
        vehicleRuntimeInfoContextRepository.updateCache(contextDO, System.currentTimeMillis());
        vehicleRuntimeInfoContextRepository.getFromCache(Collections.singletonList(commonMessageDTO.getVin()));
        messageConsumer.consume(JacksonUtils.to(commonMessageDTO));

    }

    @Test
    public void testFence() {
        String msg = "{\"data\":{\"highNegativeEventMeta\":{\"closestFenceAndFieldMetas\":[{\"constraintSourceType\":\"TRAFFIC_LIGHT\",\"perceptionId\":-1,\"position\":{\"x\":229938.7462339706,\"y\":2516427.1532404376}}],\"commonMeta\":{\"distanceToNextJunction\":{\"hasValue\":true,\"value\":3.0928853224220805}},\"constructionZoneMeta\":{},\"isValid\":true,\"touchLineMeta\":{}}},\"timestamp\":\"1736307014048907037\",\"topic\":\"/walle/planning/planner_result_meta\",\"vin\":\"LMTZSV022NC023667\"}";
        OnboardCommonMessageDTO<VehicleHighNegativeMessageDTO> commonMessageDTO = JacksonUtils.from(msg,
                new TypeReference<OnboardCommonMessageDTO<VehicleHighNegativeMessageDTO>>() {});
        commonMessageDTO.setTimestamp(String.valueOf(System.currentTimeMillis()));
        messageConsumer.consume(JacksonUtils.to(commonMessageDTO));
        vehicleRuntimeInfoContextRepository.getFromCache(Collections.singletonList(commonMessageDTO.getVin()));
    }

}