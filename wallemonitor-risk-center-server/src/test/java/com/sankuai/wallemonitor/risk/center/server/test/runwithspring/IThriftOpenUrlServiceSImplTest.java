package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import com.sankuai.wallemonitor.risk.center.server.thrift.IThriftOpenUrlServiceSImpl;
import com.sankuai.xm.openplatform.api.entity.UrlDataReq;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class IThriftOpenUrlServiceSImplTest extends SpringTestBase {

    @Resource
    private IThriftOpenUrlServiceSImpl iThriftOpenUrlServiceSImpl;

    @Test
    @SneakyThrows
    public void test() {
        UrlDataReq urlDataReq = new UrlDataReq();
        urlDataReq.setUrl(
                "https://eve.mad.test.sankuai.com/fe-panel-risk/index.html#/risk/caseList?createTimeStart=2024-11-04%2000%3A00%3A00&createTimeEnd=2024-11-04%2023%3A59%3A59&pageNum=1&pageSize=20&mode=table&openCaseId=M603320241104222901S01T01");
        System.out.println(JacksonUtils.to(iThriftOpenUrlServiceSImpl.getUrlData(urlDataReq)));
    }
}