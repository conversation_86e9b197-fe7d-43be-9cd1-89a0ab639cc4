package com.sankuai.wallemonitor.risk.center.server.test.unit.thrift;


import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.response.AdminGetRiskCaseDetailResponse;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseDisposeInfoVO;
import com.sankuai.wallemonitor.risk.center.infra.convert.CaseMarkInfoConverter;
import com.sankuai.wallemonitor.risk.center.infra.convert.CaseSortDataConvert;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseConvert;
import com.sankuai.wallemonitor.risk.center.infra.enums.MrmRoleEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CaseSortExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseSortDataDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import com.sankuai.wallemonitor.risk.center.server.thrift.IThriftAdminServiceImpl;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

public class IThriftAdminServiceTest extends ServiceTestBase {

    @InjectMocks
    private IThriftAdminServiceImpl adminService;

    @Spy
    private RiskCaseConvert riskCaseConvert;

    @Spy
    private CaseMarkInfoConverter caseMarkInfoConverter;

    @Spy
    private CaseSortDataConvert sortDataConvert;

    private List<RiskCaseDO> riskCaseDOList;

    private List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList;

    private List<CaseMarkInfoDO> caseMarkInfoDOList;

    private List<CaseSortDataDO> caseSortDataDOList;

    private Map<String, Set<String>> caseId2workstationCaseIdMap;


    @Before
    public void setUp() {
        riskCaseDOList = Collections.singletonList(
                RiskCaseDO.builder()
                        .caseId("e9ec4ee51f99488baeb1fbb9fbe8b333").build()
        );
        riskCaseVehicleRelationDOList = Collections.singletonList(
                RiskCaseVehicleRelationDO.builder()
                        .caseId("e9ec4ee51f99488baeb1fbb9fbe8b333")
                        .vin("LMTZSV024MC048701")
                        .extInfo(RiskVehicleExtInfoDO.builder()
                                .mrmMisId("cfd")
                                .mrmSeatNo("seatNo1")
                                .role(MrmRoleEnum.MRM_DRIVER).build())
                        .requestSeatTime(new Date(1721800830083L))
                        .seatConnectTime(new Date(1721800830083L))
                        .seatExitTime(new Date(1721800830083L))
                        .build()
        );
        caseMarkInfoDOList = Collections.singletonList(
                CaseMarkInfoDO.builder()
                        .caseId("e9ec4ee51f99488baeb1fbb9fbe8b333")
                        .build()
        );

        caseSortDataDOList = Collections.singletonList(
                CaseSortDataDO.builder()
                        .caseId("e9ec4ee51f99488baeb1fbb9fbe8b333")
                        .extInfo(CaseSortExtInfoDO.builder().build())
                        .problem("自动驾驶问题")
                        .sorter("zdc")
                        .build()
        );

        // 初始化mock风控案例数据
        when(riskCaseRepository.getByCaseId(anyString())).thenAnswer(
                invocation -> {
                    String caseId = invocation.getArgument(0);
                    return riskCaseDOList.stream()
                            .filter(riskCaseDO -> riskCaseDO.getCaseId().equals(caseId))
                            .findFirst()
                            .orElse(null);
                }
        );

        // mock 风险关系
        when(riskCaseVehicleRelationRepository.queryByParam(any(RiderCaseVehicleRelationDOParamDTO.class))).thenAnswer(
                invocation -> {
                    RiderCaseVehicleRelationDOParamDTO paramDTO = invocation.getArgument(0);
                    return riskCaseVehicleRelationDOList.stream()
                            .filter(riskCaseVehicleRelationDO -> riskCaseVehicleRelationDO.getCaseId()
                                    .equals(paramDTO.getCaseId()))
                            .collect(Collectors.toList());
                }
        );

        // mock 获取人工标注信息
        when(caseMarkInfoRepository.getByCaseId(anyString())).thenAnswer(
                invocation -> {
                    String caseId = invocation.getArgument(0);
                    return caseMarkInfoDOList.stream()
                            .filter(caseMarkInfoDO -> caseMarkInfoDO.getCaseId().equals(caseId))
                            .findFirst()
                            .orElse(null);
                }
        );

        // mock 获取工作台关联信息
        Map<String, Set<String>> mockCaseId2WorkstationCaseIdMap = new HashMap<>();
        Set<String> workstationCaseIds = new HashSet<>();
        workstationCaseIds.add("workstationCaseId1");
        workstationCaseIds.add("workstationCaseId2");
        mockCaseId2WorkstationCaseIdMap.put("e9ec4ee51f99488baeb1fbb9fbe8b333", workstationCaseIds);

        ReflectionTestUtils.setField(adminService, "workstationCaseLinkTemplate", "http://mock.workstation.url/%s");
        when(riskCaseRelatedServiceRecordRepository.queryCaseId2WorkstationCaseId(
                anyList(),
                anyList()
        )).thenReturn(mockCaseId2WorkstationCaseIdMap);

        when(sortDataRepository.getByCaseId(anyString())).thenAnswer(
                invocation -> {
                    String caseId = invocation.getArgument(0);
                    return caseSortDataDOList.stream()
                            .filter(caseSortDataDO -> caseSortDataDO.getCaseId().equals(caseId))
                            .findFirst()
                            .orElse(null);
                }
        );

    }

    @Test
    public void testGetRiskCaseDetail() {

        // 运行
        EveThriftResponse<AdminGetRiskCaseDetailResponse> response = adminService.getRiskCaseDetail(
                "e9ec4ee51f99488baeb1fbb9fbe8b333");

        // 验证
        List<RiskCaseDisposeInfoVO> disposeInfoList = Arrays.asList(
                new RiskCaseDisposeInfoVO("cfd", "seatNo1", MrmRoleEnum.MRM_DRIVER.getDesc(), "LMTZSV024MC048701",
                        "REQUEST_SEAT_TIME", 1721800830083L),
                new RiskCaseDisposeInfoVO("cfd", "seatNo1", MrmRoleEnum.MRM_DRIVER.getDesc(), "LMTZSV024MC048701",
                        "SEAT_CONNECT_TIME", 1721800830083L),
                new RiskCaseDisposeInfoVO("cfd", "seatNo1", MrmRoleEnum.MRM_DRIVER.getDesc(), "LMTZSV024MC048701",
                        "SEAT_EXIT_TIME", 1721800830083L)
        );
        assertEquals(disposeInfoList, response.getData().getDisposeInfoList());
    }
}
