package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.domain.enums.MessageType;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseTraceMappingMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.server.StartApp;
import com.sankuai.wallemonitor.risk.center.server.consumer.RiskCaseTraceMappingConsumer;
import java.util.Arrays;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public class RiskCaseTraceRelatedConsumerTest {

    @Resource
    private RiskCaseTraceMappingConsumer traceMappingConsumer;


    @Test
    @SneakyThrows
    public void testConsumer() {
        EveMqCommonMessage<RiskCaseTraceMappingMessageDTO> message = new EveMqCommonMessage<>();
        message.setType(MessageType.RISK_CASE_TRACE_MAPPING_MESSAGE.getCode());
        message.setTimestamp(System.currentTimeMillis());
        message.setBody(RiskCaseTraceMappingMessageDTO.builder()
                .traceId("s20-175_1720407707572")
                .eventIdList(Arrays.asList("1719191170_LMTZSV026MC093297"))
                .vin("LMTZSV026MC093297")
                .build());
        traceMappingConsumer.receive(JacksonUtils.to(message));
    }


}
