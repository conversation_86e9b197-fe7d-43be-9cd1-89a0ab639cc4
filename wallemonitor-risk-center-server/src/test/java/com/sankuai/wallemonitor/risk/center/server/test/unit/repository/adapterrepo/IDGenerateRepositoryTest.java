package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.adapterrepo;

import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import java.util.Arrays;
import org.junit.Assert;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;

public class IDGenerateRepositoryTest extends RepositoryTestBase {

    @Test
    public void testGenerateByKey() {
        String uid = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, Arrays.asList(""),
                RiskCaseSourceEnum.SAFEGUARD_SYSTEM, RiskCaseTypeEnum.VEHICLE_STAND_STILL, System.currentTimeMillis());
        Assert.assertTrue(StringUtils.isNotBlank(uid));
    }
}
