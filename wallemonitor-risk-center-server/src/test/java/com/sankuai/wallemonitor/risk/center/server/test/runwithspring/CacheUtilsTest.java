package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.CacheUtils;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.junit.Test;

public class CacheUtilsTest extends SpringTestBase {


    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Test
    @SneakyThrows
    public void test() {

        while (true) {
            CacheUtils.doCache(this, 2, TimeUnit.SECONDS).query(Param.builder()
                    .age(1)
                    .name("leo")
                    .build());
            this.query(Param.builder()
                    .age(2)
                    .name("jam")
                    .build());
            CacheUtils.doCache(riskCaseRepository, 2, TimeUnit.SECONDS)
                    .queryByParam(RiskCaseDOQueryParamDTO.builder()
                            .caseIdList(Lists.newArrayList("LMTZSV020MC042359"))
                            .build());
            Thread.sleep(500);
        }

    }

    public List<String> query(Param param) {
        System.out.println("我" + param.toString() + "被调用" + DatetimeUtil.formatTime(new Date()));
        return new ArrayList<>();
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Param {

        private String name;
        private int age;

        @Override
        public String toString() {
            return "{" +
                    "name='" + name + '\'' +
                    ", age=" + age +
                    '}';
        }
    }

}