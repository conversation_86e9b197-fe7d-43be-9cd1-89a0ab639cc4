package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.domain.message.dto.CloudTriageEventMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.MoveCarEventReportRequest;
import com.sankuai.wallemonitor.risk.center.api.vo.MoveCarEventStatusVO;
import com.sankuai.wallemonitor.risk.center.domain.service.MoveCarEventOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.dto.EventMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.server.consumer.CloudTriageEventMessageConsumer;
import com.sankuai.wallemonitor.risk.center.server.consumer.WorkstationCaseMessageConsumer;
import com.sankuai.wallemonitor.risk.center.server.crane.ReportMoveCarOrderCrane;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.Test;

public class MoveCarEventOperateServiceTest extends SpringTestBase {

    @Resource
    private MoveCarEventOperateService moveCarEventOperateService;

    @Resource
    private ReportMoveCarOrderCrane reportMoveCarOrderCrane;

    @Resource
    private CloudTriageEventMessageConsumer messageConsumer;

    @Resource
    private WorkstationCaseMessageConsumer workstationCaseMessageConsumer;

    @MessageProducer(topic = MessageTopicEnum.CLOUD_TRIAGE_EVENT_MESSAGE, appKey = "com.sankuai.walleops.cloud.triage")
    private CommonMessageProducer<CloudTriageEventMessageDTO> cloudTriageMessageProducer;


    @Test
    public void testCheckAndCreateMoveCarEvent() throws Exception {
        MoveCarEventReportRequest request = new MoveCarEventReportRequest();
        request.setVehicleId("M2051");
        request.setCarPosition("testCarPosition");
        request.setEventType(32);
        request.setMoveCarReason("testReason");
        String openId = "testOpenId";
        moveCarEventOperateService.checkAndCreateMoveCarEvent(request, openId);
    }

    @Test
    public void testGetMoveCarStatusByVehicleId() throws Exception {
        MoveCarEventStatusVO moveCarEventStatusVO = moveCarEventOperateService.getMoveCarStatusByVehicleId("M5230");
        System.out.println(moveCarEventStatusVO);
    }

    @Test
    public void testReportMoveCarOrder() throws Exception {
        reportMoveCarOrderCrane.run();
    }

    @Test
    public void testSendMessage() {

        EventMessageDTO eventMessageDTO = new EventMessageDTO();
        eventMessageDTO.setEventId("20241108163242465_MOVE_CAR_EVENT_M5230");
        eventMessageDTO.setEventType(32);
        eventMessageDTO.setStatus(2);
        eventMessageDTO.setVin("123456");
        eventMessageDTO.setTimestamp(new Date().getTime());

        messageConsumer.receive(JacksonUtils.to(eventMessageDTO));
    }

    @Test
    public void testWorkstationCaseMessageConsumer() {
        String msg = "{\"tableName\":\"walle_data_center.oncall_list\",\"timestamp\":1733130877000,\"scn\":264082819,\"type\":\"insert\",\"sourceIP\":\"*************\",\"data\":{\"webviz_video_url\":\"\",\"operator_id\":\"risk_center\",\"validator\":null,\"video_status\":0,\"work_table_type\":0,\"proficiency\":\"\",\"case_end_time\":null,\"optimize\":null,\"case_id\":\"20241202165209100_intervention_s20-242\",\"operation_status\":\"created\",\"weather\":\"天气-正常\",\"decline\":null,\"vin\":\"LMTZSV027MC042469\",\"manual_level\":\"\",\"id\":1539410603,\"state\":\"created\",\"tag\":null,\"longitude\":\"\",\"image\":null,\"user_eval_type\":0,\"create_time\":1733130877000,\"receiver\":\"\",\"group_name\":\"\",\"module\":\"\",\"receive_time\":null,\"offline_id\":\"\",\"weight\":\"\",\"final_level\":\"100\",\"priority\":\"\",\"data_source\":\"intervention\",\"auto_sort_type\":0,\"record_name\":\"\",\"position\":null,\"send\":\"\",\"eval_type\":0,\"case_order\":0,\"relate_case\":\"\",\"classify\":\"\",\"latitude\":\"\",\"remark\":\"\",\"eval_status\":\"\",\"video\":null,\"title\":\"\",\"update_time\":1733225412000,\"is_deleted\":0,\"is_auto_assigned\":1,\"webviz_status\":0,\"s3_data_ready\":0,\"operate_time\":1733225412000,\"recorder\":null,\"issue_id\":null,\"real_time_id\":\"\",\"sortable\":1,\"webviz_image_url\":\"\",\"image_quality\":\"成像质量-正常\",\"appearance\":\"\",\"package_version\":\"\",\"comment\":null,\"custom_proficiency\":\"\",\"category\":null,\"origin_case_id\":\"\",\"assign\":\"\"}}";
        workstationCaseMessageConsumer.receive(msg);
    }
}
