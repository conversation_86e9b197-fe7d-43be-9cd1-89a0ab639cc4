package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCaseExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.crane.CheckRiskCaseCallMrmCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.CloseUnhandledRiskCaseCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.ReportRiskCaseStatCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.RiskCaseCalcCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.UpdateMrmProcessStatusCrane;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class RiskCalcCraneTest extends SpringTestBase {

    @Resource
    private RiskCaseCalcCrane riskCalcCrane;

    @Resource
    private ReportRiskCaseStatCrane caseNotifyCrane;

    @Resource
    private CloseUnhandledRiskCaseCrane closeUnhandledRiskCaseCrane;

    @Resource
    private CheckRiskCaseCallMrmCrane checkRiskCaseCallMrmCrane;

    @Resource
    private UpdateMrmProcessStatusCrane updateMrmProcessStatusCrane;

    @Test
    @SneakyThrows
    public void testCalcCrane() {
        riskCalcCrane.run();
    }

    @Test
    @SneakyThrows
    public void testCaseNotifyCrane() {
        caseNotifyCrane.run();
    }

    @Test
    @SneakyThrows
    public void testCloseUnhandledRiskCase() {
        closeUnhandledRiskCaseCrane.run();
    }

    @Test
    @SneakyThrows
    public void testRiskCaseCallMrmCrane() {
        checkRiskCaseCallMrmCrane.run();
    }

    @Test
    @SneakyThrows
    public void testUpdateMrmProcessStatusCrane() {
        updateMrmProcessStatusCrane.run();
    }

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Test
    @SneakyThrows
    public void testRiskCaseCalcCrane() {
        RiskCaseDOQueryParamDTO paramDTO = new RiskCaseDOQueryParamDTO();
        paramDTO.setCaseIdList(new ArrayList<>());
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return;
        }
        riskCaseDOList.forEach(riskCaseDO -> {
            RiskCaseExtInfoDO extInfoDO = riskCaseDO.getExtInfo();
            if (extInfoDO == null) {
                return;
            }
            extInfoDO.getPosition();
        });
    }
}
