package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;


import ch.hsr.geohash.GeoHash;
import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.GeometryUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.MapElementRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.vo.HdMapElementGeoVO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RandomForestPredictService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RandomForestPredictionResultDTO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RandomForestTrainService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RandomForestTrainService.ModelEvaluationResultDTO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RandomForestTrainService.RiskClassifierConfigDTO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RandomForestTrainService.RiskTrainDataVO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RiskFeatureDataDTO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RiskFeatureTestDataVO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RiskFeatureTestResultVO;
import com.sankuai.wallemonitor.risk.center.infra.enums.LanePositionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.LaneTypeEnum;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import com.sankuai.wallemonitor.risk.center.server.thrift.IThriftMapElementQueryServiceImpl;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.util.CollectionUtils;


public class RiskClassifierTest extends SpringTestBase {

    @Resource
    private IThriftMapElementQueryServiceImpl iThriftLaneQueryService;

    @Resource
    private RandomForestPredictService randomForestPredictService;

    @Test
    public void testV2() throws Exception {

        // 1 加载训练数据
        List<RiskTrainDataVO> csvTrainingData = loadDataFromCsv(
                "/Users/<USER>/workspace/0220_0301_train_final.csv");
        // 3 加载测试数据
        List<RiskFeatureTestDataVO> csvTestData = loadTestDataFromCsv(
                "/Users/<USER>/workspace/0305_0308_test_final.csv");

        List<RiskFeatureDataDTO> trainData = new ArrayList<>();
        csvTrainingData.stream().forEach(trainDataVO -> {
            RiskFeatureDataDTO riskFeatureDataDTO = RiskFeatureDataDTO.builder()
                    .longitude(trainDataVO.getLongitude())
                    .latitude(trainDataVO.getLatitude())
                    .distanceToNextJunction(trainDataVO.getDistanceToNextJunction())
                    .trafficLightType(trainDataVO.getTrafficLightType())
                    .obstacleAngle(trainDataVO.getObstacleAngle())
                    .obstacleDistance(trainDataVO.getObstacleDistance())
                    // 车道信息设置默认值
                    .laneType(LaneTypeEnum.UNKNOWN.getCode())
                    .lanePosition(LanePositionEnum.NONE.getCode())
                    .risk(trainDataVO.getRisk()).build();

            // 计算车道相关特征信息
            riskFeatureDataDTO = computeFeature(trainDataVO.getHdMapVersion(), riskFeatureDataDTO);
            trainData.add(riskFeatureDataDTO);
        });

        // 4 评估训练集
        try {
            ModelEvaluationResultDTO modelEvaluationResultDTO = randomForestPredictService.evaluate(trainData);
            System.out.println(modelEvaluationResultDTO);
        } catch (Exception e) {
            System.out.println(e);
        }
        saveFeatureToCsv(trainData, "/Users/<USER>/workspace/0305_0308_lat_lon_train_feature.csv");

        Integer okNum = 0;
        Integer noNum = 0;
        Integer riskNum = 0;
        Integer predictRiskNum = 0;
        Integer predictRiskOkNum = 0;
        Integer noRiskPredictErrorNum = 0;
        // 漏召的结果数据
        List<RiskFeatureTestResultVO> riskPredictErrorDatas = new ArrayList<>();
        // 风险事件预测结果
        List<RiskFeatureTestResultVO> riskResults = new ArrayList<>();
        // 不能预测的数据集
        List<RiskFeatureTestResultVO> noPredictResults = new ArrayList<>();
        // 预测错误的结果数据
        List<RiskFeatureTestResultVO> predictErrorDatas = new ArrayList<>();
        // 筛选出可预测的测试集进行模型评估
        List<RiskFeatureDataDTO> testSelectDatas = new ArrayList<>();
        for (RiskFeatureTestDataVO testDataVO : csvTestData) {
            // 格式转化
            RiskFeatureDataDTO riskFeatureDataDTO = fromRiskFeatureTestDataVO(testDataVO);
            // 执行预测
            RandomForestPredictionResultDTO result = randomForestPredictService.predict(riskFeatureDataDTO);
            RiskFeatureTestResultVO riskFeatureTestResultVO = createRiskFeatureTestResultVO(testDataVO,
                    riskFeatureDataDTO, result);
            // result = null 表示预测失败
            if (Objects.isNull(result)) {
                noNum++;
                noPredictResults.add(riskFeatureTestResultVO);
                continue;
            }
            // 构建输出结果

            testSelectDatas.add(riskFeatureDataDTO);
            // 模型数据统计
            Boolean isRisky = testDataVO.getRisk() == 0 ? false : true;
            if (Objects.equals(result.getIsRisky(), isRisky)) {
                okNum++;
                if (isRisky) {
                    predictRiskNum++;
                    predictRiskOkNum++;
                    riskResults.add(riskFeatureTestResultVO);
                }
            } else {
                predictErrorDatas.add(riskFeatureTestResultVO);
                if (isRisky) {
                    predictRiskNum++;
                    riskPredictErrorDatas.add(riskFeatureTestResultVO);
                    riskResults.add(riskFeatureTestResultVO);
                } else {
                    noRiskPredictErrorNum++;
                }
            }
        }
        saveResultsToCsv(noPredictResults, "/Users/<USER>/workspace/0305_0308_test_final_noPredictResults.csv");
        saveResultsToCsv(riskPredictErrorDatas, "/Users/<USER>/workspace/0305_0308_test_final_riskPredictErrorDatas.csv");
        saveResultsToCsv(riskResults, "/Users/<USER>/workspace/0305_0308_test_final_riskResults.csv");
        saveResultsToCsv(predictErrorDatas,
                "/Users/<USER>/workspace/0301_0303_test_final_predictErrorDatas.csv");
        System.out.println(okNum);

        // 4 评估测试集
        try {
            ModelEvaluationResultDTO modelEvaluationResultDTO = randomForestPredictService.evaluate(testSelectDatas);
            System.out.println(modelEvaluationResultDTO);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    public void test() throws Exception {

        // 1 加载训练数据
        List<RiskTrainDataVO> csvTrainingData = loadDataFromCsv(
                "/Users/<USER>/workspace/0220_0301_train_final.csv");
        // 3 加载测试数据
        List<RiskFeatureTestDataVO> csvTestData = loadTestDataFromCsv(
                "/Users/<USER>/workspace/0305_0308_test_final.csv");

        List<RiskFeatureDataDTO> trainData = new ArrayList<>();
        csvTrainingData.stream().forEach(trainDataVO -> {
            RiskFeatureDataDTO riskFeatureDataDTO = RiskFeatureDataDTO.builder()
                    .longitude(trainDataVO.getLongitude())
                    .latitude(trainDataVO.getLatitude())
                    .distanceToNextJunction(trainDataVO.getDistanceToNextJunction())
                    .trafficLightType(trainDataVO.getTrafficLightType())
                    .obstacleAngle(trainDataVO.getObstacleAngle())
                    .obstacleDistance(trainDataVO.getObstacleDistance())
                    // 车道信息设置默认值
                    .laneType(LaneTypeEnum.UNKNOWN.getCode())
                    .lanePosition(LanePositionEnum.NONE.getCode())
                    .risk(trainDataVO.getRisk()).build();

            // 计算车道相关特征信息
            riskFeatureDataDTO = computeFeature(trainDataVO.getHdMapVersion(), riskFeatureDataDTO);
            trainData.add(riskFeatureDataDTO);
        });

        // 2 处理训练数据 - 确定离散值取值范围
        Set<String> trafficLightTypeValues = trainData.stream()
                .map(RiskFeatureDataDTO::getTrafficLightType)
                .collect(Collectors.toSet());

        RiskClassifierConfigDTO configDTO = new RiskClassifierConfigDTO();
        configDTO.setTrafficLightTypeValues(new ArrayList<>(trafficLightTypeValues));

        // 3 初始化分类器
        RandomForestTrainService classifier = new RandomForestTrainService(configDTO);
        try {
            // 4 训练模型
            classifier.trainV2(trainData);
        } catch (Exception e) {
            System.out.println(e);
        }

        // 4 评估训练集
        try {
            ModelEvaluationResultDTO modelEvaluationResultDTO = classifier.evaluate(trainData);
            System.out.println(modelEvaluationResultDTO);
        } catch (Exception e) {
            System.out.println(e);
        }
        saveFeatureToCsv(trainData, "/Users/<USER>/workspace/0305_0308_lat_lon_train_feature.csv");

        Integer okNum = 0;
        Integer noNum = 0;
        Integer riskNum = 0;
        Integer predictRiskNum = 0;
        Integer predictRiskOkNum = 0;
        Integer noRiskPredictErrorNum = 0;
        // 漏召的结果数据
        List<RiskFeatureTestResultVO> riskPredictErrorDatas = new ArrayList<>();
        // 风险事件预测结果
        List<RiskFeatureTestResultVO> riskResults = new ArrayList<>();
        // 不能预测的数据集
        List<RiskFeatureTestResultVO> noPredictResults = new ArrayList<>();
        // 预测错误的结果数据
        List<RiskFeatureTestResultVO> predictErrorDatas = new ArrayList<>();
        // 筛选出可预测的测试集进行模型评估
        List<RiskFeatureDataDTO> testSelectDatas = new ArrayList<>();
        for (RiskFeatureTestDataVO testDataVO : csvTestData) {
            // 格式转化
            RiskFeatureDataDTO riskFeatureDataDTO = fromRiskFeatureTestDataVO(testDataVO);
            // 执行预测
            RandomForestPredictionResultDTO result = classifier.predict(riskFeatureDataDTO);
            RiskFeatureTestResultVO riskFeatureTestResultVO = createRiskFeatureTestResultVO(testDataVO,
                    riskFeatureDataDTO, result);
            // result = null 表示预测失败
            if (Objects.isNull(result)) {
                noNum++;
                noPredictResults.add(riskFeatureTestResultVO);
                continue;
            }
            // 构建输出结果

            testSelectDatas.add(riskFeatureDataDTO);
            // 模型数据统计
            Boolean isRisky = testDataVO.getRisk() == 0 ? false : true;
            if (Objects.equals(result.getIsRisky(), isRisky)) {
                okNum++;
                if (isRisky) {
                    predictRiskNum++;
                    predictRiskOkNum++;
                    riskResults.add(riskFeatureTestResultVO);
                }
            } else {
                predictErrorDatas.add(riskFeatureTestResultVO);
                if (isRisky) {
                    predictRiskNum++;
                    riskPredictErrorDatas.add(riskFeatureTestResultVO);
                    riskResults.add(riskFeatureTestResultVO);
                } else {
                    noRiskPredictErrorNum++;
                }
            }
        }
        saveResultsToCsv(noPredictResults, "/Users/<USER>/workspace/0305_0308_test_final_noPredictResults.csv");
        saveResultsToCsv(riskPredictErrorDatas, "/Users/<USER>/workspace/0305_0308_test_final_riskPredictErrorDatas.csv");
        saveResultsToCsv(riskResults, "/Users/<USER>/workspace/0305_0308_test_final_riskResults.csv");
        saveResultsToCsv(predictErrorDatas,
                "/Users/<USER>/workspace/0301_0303_test_final_predictErrorDatas.csv");
        System.out.println(okNum);

        // 4 评估测试集
        try {
            ModelEvaluationResultDTO modelEvaluationResultDTO = classifier.evaluate(testSelectDatas);
            System.out.println(modelEvaluationResultDTO);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    public void randomForestAndGeoHash() throws Exception {

        // 1 加载训练数据
        List<RiskTrainDataVO> csvTrainingData = loadDataFromCsv(
                "/Users/<USER>/workspace/0220_0301_train_final.csv");

        // 2 加载geo hash 训练数据
        // todo: 更换文件名称
        List<RiskFeatureTestDataVO> csvGeoHashTrainingData = loadTestDataFromCsv(
                "/Users/<USER>/workspace/0220_0301_geohash_test.csv");

        Map<String, Double> geoHashRiskProbabilityMap = computeGeoHashRiskProbability(csvGeoHashTrainingData);

        // 3 加载测试数据
        List<RiskFeatureTestDataVO> csvTestData = loadTestDataFromCsv(
                "/Users/<USER>/workspace/0305_0308_test_final.csv");

        List<RiskFeatureDataDTO> trainData = new ArrayList<>();
        csvTrainingData.stream().forEach(trainDataVO -> {
            RiskFeatureDataDTO riskFeatureDataDTO = RiskFeatureDataDTO.builder()
                    .longitude(trainDataVO.getLongitude())
                    .latitude(trainDataVO.getLatitude())
                    .distanceToNextJunction(trainDataVO.getDistanceToNextJunction())
                    .trafficLightType(trainDataVO.getTrafficLightType())
                    .obstacleAngle(trainDataVO.getObstacleAngle())
                    .obstacleDistance(trainDataVO.getObstacleDistance())
                    // 车道信息设置默认值
                    .laneType(LaneTypeEnum.UNKNOWN.getCode())
                    .lanePosition(LanePositionEnum.NONE.getCode())
                    .risk(trainDataVO.getRisk()).build();

            // 计算车道相关特征信息
            riskFeatureDataDTO = computeFeature(trainDataVO.getHdMapVersion(), riskFeatureDataDTO);
            trainData.add(riskFeatureDataDTO);
        });

        // 2 处理训练数据 - 确定离散值取值范围
        Set<String> trafficLightTypeValues = trainData.stream()
                .map(RiskFeatureDataDTO::getTrafficLightType)
                .collect(Collectors.toSet());

        RiskClassifierConfigDTO configDTO = new RiskClassifierConfigDTO();
        configDTO.setTrafficLightTypeValues(new ArrayList<>(trafficLightTypeValues));

        // 3 初始化分类器
        RandomForestTrainService classifier = new RandomForestTrainService(configDTO);
        try {
            // 4 训练模型
            classifier.trainV2(trainData);
            classifier.saveModel("/Users/<USER>/workspace/model/model.model",
                    "/Users/<USER>/workspace/model/dataStructure.model");
        } catch (Exception e) {
            System.out.println(e);
        }

        // 4 评估训练集
        try {
            ModelEvaluationResultDTO modelEvaluationResultDTO = classifier.evaluate(trainData);
            System.out.println(modelEvaluationResultDTO);
        } catch (Exception e) {
            System.out.println(e);
        }
        saveFeatureToCsv(trainData, "/Users/<USER>/workspace/0305_0308_lat_lon_train_feature.csv");

        Integer okNum = 0;
        Integer noNum = 0;
        Integer riskNum = 0;
        Integer predictRiskNum = 0;
        Integer predictRiskOkNum = 0;
        Integer noRiskPredictErrorNum = 0;
        // 漏召的结果数据
        List<RiskFeatureTestResultVO> riskPredictErrorDatas = new ArrayList<>();
        // 风险事件预测结果
        List<RiskFeatureTestResultVO> riskResults = new ArrayList<>();
        // 不能预测的数据集
        List<RiskFeatureTestResultVO> noPredictResults = new ArrayList<>();
        // 预测错误的结果数据
        List<RiskFeatureTestResultVO> predictErrorDatas = new ArrayList<>();
        for (RiskFeatureTestDataVO testDataVO : csvTestData) {
            // 格式转化
            RiskFeatureDataDTO riskFeatureDataDTO = fromRiskFeatureTestDataVO(testDataVO);
            // 执行预测
            RandomForestPredictionResultDTO result = classifier.predict(riskFeatureDataDTO);
            RiskFeatureTestResultVO riskFeatureTestResultVO = createRiskFeatureTestResultVO(testDataVO,
                    riskFeatureDataDTO, result);
            // result = null 表示预测失败
            if (Objects.isNull(result)) {
                noNum++;
                continue;
            }
            //
            String geoHashStr = getGeoHash(testDataVO.getLongitude(), testDataVO.getLatitude());
            Double riskProbability = geoHashRiskProbabilityMap.getOrDefault(geoHashStr, 0.0);

            Boolean isPredictRisky = result.getIsRisky() ? result.getIsRisky() : riskProbability >= 0.8 ? true : false;
            // 模型数据统计
            Boolean isRisky = testDataVO.getRisk() == 0 ? false : true;
            if (Objects.equals(isPredictRisky, isRisky)) {
                okNum++;
                if (isRisky) {
                    predictRiskNum++;
                    predictRiskOkNum++;
                    riskResults.add(riskFeatureTestResultVO);
                }
            } else {
                predictErrorDatas.add(riskFeatureTestResultVO);
                // 此处为模型判断错误，但是有风险
                if (isRisky) {
                    predictRiskNum++;
                    riskPredictErrorDatas.add(riskFeatureTestResultVO);
                    riskResults.add(riskFeatureTestResultVO);
                } else {
                    noRiskPredictErrorNum++;
                }
            }
        }

        System.out.println(okNum);

    }

    @Test
    public void testLoadModel() throws Exception {
        String modelPath = "https://s3plus-bj02.sankuai.com/cloudtriage-movecar/model.model";
        String modelDataPath = "https://s3plus-bj02.sankuai.com/cloudtriage-movecar/dataStructure.model";
        // 1. 初始化分类器配置
        RiskClassifierConfigDTO configDTO = new RiskClassifierConfigDTO();
        // 如果有必要，设置 trafficLightTypeValues

        // 2. 初始化分类器
        RandomForestTrainService classifier = new RandomForestTrainService(configDTO);

        // 3. 加载模型
        classifier.loadModel(modelPath, modelDataPath);

        // 4. 验证模型是否加载成功
        // 可以用一些测试数据进行预测验证
        RiskFeatureDataDTO testData = RiskFeatureDataDTO.builder()
                .longitude(116.123)
                .latitude(39.456)
                .distanceToNextJunction(10.0)
                .trafficLightType("GREEN")
                .obstacleDistance(5.0)
                .obstacleAngle(30.0)
                .laneType(LaneTypeEnum.UNKNOWN.getCode())
                .lanePosition(LanePositionEnum.NONE.getCode())
                .build();

        RandomForestPredictionResultDTO result = classifier.predict(testData);
        System.out.println("预测结果: " + result);
    }

    /**
     * 从CSV文件中加载数据
     *
     * @param filePath
     * @return
     * @throws Exception
     */
    private List<RiskTrainDataVO> loadDataFromCsv(String filePath) throws Exception {
        List<RiskTrainDataVO> dataList = new ArrayList<>();

        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            // 跳过表头
            reader.skip(1);

            String[] line;
            while ((line = reader.readNext()) != null) {
                RiskTrainDataVO data = new RiskTrainDataVO(
                        Double.parseDouble(line[0]),                    // longitude
                        Double.parseDouble(line[1]),                    // latitude
                        line[2],                    // hdMapVersion
                        Double.parseDouble(line[3]), // distanceToNextJunction
                        line[4],                    // trafficLightType
                        Double.parseDouble(line[5]), // obstacleDistance
                        Double.parseDouble(line[6]), // obstacleAngle
                        Integer.parseInt(line[7])    // risk
                );
                dataList.add(data);
            }
        }

        return dataList;
    }

    /**
     * 从CSV文件中加载测试数据
     *
     * @param filePath
     * @return
     * @throws Exception
     */
    private List<RiskFeatureTestDataVO> loadTestDataFromCsv(String filePath) throws Exception {
        List<RiskFeatureTestDataVO> dataList = new ArrayList<>();

        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            // 跳过表头
            reader.skip(1);

            String[] line;
            while ((line = reader.readNext()) != null) {
                RiskFeatureTestDataVO data = new RiskFeatureTestDataVO(
                        line[0],                    // caseLink
                        line[1],                    // subCategory
                        Double.parseDouble(line[2]),
                        Double.parseDouble(line[3]),// position
                        line[4],                    // 高精地图版本
                        Double.parseDouble(line[5]), // distanceToNextJunction
                        line[6],                    // trafficLightType
                        Double.parseDouble(line[7]), // obstacleDistance
                        Double.parseDouble(line[8]), // obstacleAngle

                        Integer.parseInt(line[9])    // risk
                );
                dataList.add(data);
            }
        }

        return dataList;
    }

    /**
     * 保存结果到CSV文件
     *
     * @param results
     * @param filePath
     */
    private void saveResultsToCsv(List<RiskFeatureTestResultVO> results, String filePath) throws Exception {
        try (FileWriter writer = new FileWriter(filePath);
                CSVWriter csvWriter = new CSVWriter(writer)) {

            // 写入CSV表头
            String[] header = {"caseLink", "subCategory", "longitude", "latitude", "hdMapVersion",
                    "distanceToNextJunction",
                    "trafficLightType",
                    "obstacleDistance", "obstacleAngle", "laneType", "lanePosition", "risk",
                    "predictionResult", "riskProbability"};
            csvWriter.writeNext(header);

            // 写入数据行
            for (RiskFeatureTestResultVO result : results) {
                String[] data = {
                        result.getCaseLink() != null ? result.getCaseLink() : "",
                        result.getSubCategory() != null ? result.getSubCategory() : "",

                        result.getLongitude() != null ? String.valueOf(result.getLongitude()) : "",
                        result.getLatitude() != null ? String.valueOf(result.getLatitude()) : "",
                        result.getHdMapVersion() != null ? result.getHdMapVersion() : "",
                        result.getDistanceToNextJunction() != null ? String.valueOf(result.getDistanceToNextJunction())
                                : "",
                        result.getTrafficLightType() != null ? result.getTrafficLightType() : "",
                        result.getObstacleDistance() != null ? String.valueOf(result.getObstacleDistance()) : "",
                        result.getObstacleAngle() != null ? String.valueOf(result.getObstacleAngle()) : "",
                        result.getLaneType() != null ? String.valueOf(result.getLaneType()) : "",
                        result.getLanePosition() != null ? String.valueOf(result.getLanePosition()) : "",
                        result.getRisk() != null ? String.valueOf(result.getRisk()) : "",
                        result.getPredictionResult() != null ? String.valueOf(result.getPredictionResult()) : "",
                        result.getRiskProbability() != null ? String.valueOf(result.getRiskProbability()) : ""
                };
                csvWriter.writeNext(data);
            }
        }
    }


    /**
     * 保存特征到CSV文件
     *
     * @param featureDataDTOList
     * @param filePath
     */
    private void saveFeatureToCsv(List<RiskFeatureDataDTO> featureDataDTOList, String filePath) throws Exception {
        try (FileWriter writer = new FileWriter(filePath);
                CSVWriter csvWriter = new CSVWriter(writer)) {

            // 写入CSV表头
            String[] header = {"longitude", "latitude",
                    "distanceToNextJunction",
                    "trafficLightType",
                    "obstacleDistance", "obstacleAngle", "laneType", "lanePosition", "risk"};
            csvWriter.writeNext(header);

            // 写入数据行
            for (RiskFeatureDataDTO featureDataDTO : featureDataDTOList) {
                String[] data = {
                        featureDataDTO.getLongitude() != null ? String.valueOf(featureDataDTO.getLongitude()) : "",
                        featureDataDTO.getLatitude() != null ? String.valueOf(featureDataDTO.getLatitude()) : "",
                        featureDataDTO.getDistanceToNextJunction() != null ? String.valueOf(
                                featureDataDTO.getDistanceToNextJunction())
                                : "",
                        featureDataDTO.getTrafficLightType() != null ? featureDataDTO.getTrafficLightType() : "",
                        featureDataDTO.getObstacleDistance() != null ? String.valueOf(
                                featureDataDTO.getObstacleDistance()) : "",
                        featureDataDTO.getObstacleAngle() != null ? String.valueOf(featureDataDTO.getObstacleAngle())
                                : "",
                        featureDataDTO.getLaneType() != null ? String.valueOf(featureDataDTO.getLaneType()) : "",
                        featureDataDTO.getRisk() != null ? String.valueOf(featureDataDTO.getRisk()) : "",
                };
                csvWriter.writeNext(data);
            }
        }
    }


    /**
     * 将RiskFeatureTestDataVO对象转换为RiskFeatureDataDTO对象
     *
     * @param
     * @return
     */
    private RiskFeatureDataDTO fromRiskFeatureTestDataVO(RiskFeatureTestDataVO dataVO) {
        RiskFeatureDataDTO riskFeatureDataDTO = new RiskFeatureDataDTO();
        riskFeatureDataDTO.setLongitude(dataVO.getLongitude());
        riskFeatureDataDTO.setLatitude(dataVO.getLatitude());
        riskFeatureDataDTO.setDistanceToNextJunction(dataVO.getDistanceToNextJunction());
        riskFeatureDataDTO.setTrafficLightType(dataVO.getTrafficLightType());
        riskFeatureDataDTO.setObstacleDistance(dataVO.getObstacleDistance());
        riskFeatureDataDTO.setObstacleAngle(dataVO.getObstacleAngle());
        riskFeatureDataDTO.setRisk(dataVO.getRisk());
        riskFeatureDataDTO.setLaneType(LaneTypeEnum.UNKNOWN.getCode());
        riskFeatureDataDTO.setLanePosition(LanePositionEnum.NONE.getCode());

        riskFeatureDataDTO = computeFeature(dataVO.getHdMapVersion(), riskFeatureDataDTO);
        return riskFeatureDataDTO;
    }

    /**
     * 创建RiskFeatureTestResultVO对象
     *
     * @param data
     * @param result
     * @return
     */
    private RiskFeatureTestResultVO createRiskFeatureTestResultVO(RiskFeatureTestDataVO data,
            RiskFeatureDataDTO riskFeatureDataDTO,
            RandomForestPredictionResultDTO result) {
        RiskFeatureTestResultVO riskFeatureTestResultVO = new RiskFeatureTestResultVO();
        riskFeatureTestResultVO.setCaseLink(data.getCaseLink());
        riskFeatureTestResultVO.setSubCategory(data.getSubCategory());
        riskFeatureTestResultVO.setHdMapVersion(data.getHdMapVersion());
        riskFeatureTestResultVO.setLongitude(data.getLongitude());
        riskFeatureTestResultVO.setLatitude(data.getLatitude());
        riskFeatureTestResultVO.setDistanceToNextJunction(data.getDistanceToNextJunction());
        riskFeatureTestResultVO.setTrafficLightType(data.getTrafficLightType());
        riskFeatureTestResultVO.setObstacleDistance(data.getObstacleDistance());
        riskFeatureTestResultVO.setObstacleAngle(data.getObstacleAngle());
        riskFeatureTestResultVO.setLaneType(riskFeatureDataDTO.getLaneType());
        riskFeatureTestResultVO.setLanePosition(riskFeatureDataDTO.getLanePosition());
        riskFeatureTestResultVO.setRisk(data.getRisk());
        if (Objects.nonNull(result)) {
            riskFeatureTestResultVO.setPredictionResult(result.getIsRisky());
            riskFeatureTestResultVO.setRiskProbability(result.getRiskProbability());
        }

        return riskFeatureTestResultVO;
    }

    /**
     * 计算特征
     */
    private RiskFeatureDataDTO computeFeature(String hdMapVersion, RiskFeatureDataDTO featureDataDTO) {
        try {
            // 1 根据经纬度信息/高精地图版本,查询车道信息
            MapElementRequestDTO laneQueryRequestDTO = MapElementRequestDTO.builder()
                    .distance(30.0)
                    .mapType("lane_polygon")
                    .hdMapVersion(hdMapVersion)
                    .longitude(featureDataDTO.getLongitude())
                    .latitude(featureDataDTO.getLatitude())
                    .build();
            EveThriftResponse<List<HdMapElementGeoVO>> listEveThriftResponse =
                    iThriftLaneQueryService.searchMapElementByDistance(laneQueryRequestDTO);
            if (Objects.isNull(listEveThriftResponse) || listEveThriftResponse.getCode() != 0
                    || CollectionUtils.isEmpty(listEveThriftResponse.getData())) {
                System.out.println("查询失败");
                return featureDataDTO;
            }
            Coordinate coordinate = new Coordinate(laneQueryRequestDTO.getLongitude(),
                    laneQueryRequestDTO.getLatitude());
            // 计算当前经纬度对应的LaneId
            for (HdMapElementGeoVO geoVO : listEveThriftResponse.getData()) {
                List<List<Double>> pointList = geoVO.getPoints();
                List<Coordinate> laneArea = pointList.stream()
                        .map(x -> new Coordinate(x.get(0), x.get(1)))
                        .collect(Collectors.toList());
                // 判断当前经纬度是否在LaneId对应的范围内
                if (GeometryUtil.inPolygon(coordinate, laneArea)) {
                    // 计算车道类型
                    Map<String, String> propertiesMap = JacksonUtils.from(geoVO.getProperties(), Map.class);
                    String leftLaneId = "";
                    String rightLaneId = "";
                    if (Objects.nonNull(propertiesMap)) {
                        leftLaneId = propertiesMap.getOrDefault("left", "");
                        rightLaneId = propertiesMap.getOrDefault("right", "");
                    }
                    // 假设左右车道均存在
                    if (StringUtils.isNotBlank(leftLaneId) && StringUtils.isNotBlank(rightLaneId)) {
                        featureDataDTO.setLaneType(LaneTypeEnum.MULTI_LANE.getCode());
                        featureDataDTO.setLanePosition(LanePositionEnum.MIDDLE.getCode());
                        // 只有左侧车道存在，当前车道位于最右侧
                    } else if (StringUtils.isNotBlank(leftLaneId)) {
                        featureDataDTO.setLaneType(LaneTypeEnum.MULTI_LANE.getCode());
                        featureDataDTO.setLanePosition(LanePositionEnum.RIGHT.getCode());
                        // 只有右侧车道存在，当前车道位于最左侧
                    } else if (StringUtils.isNotBlank(rightLaneId)) {
                        featureDataDTO.setLaneType(LaneTypeEnum.MULTI_LANE.getCode());
                        featureDataDTO.setLanePosition(LanePositionEnum.LEFT.getCode());
                        //  只有当前车道存在,无左右车道
                    } else {
                        featureDataDTO.setLaneType(LaneTypeEnum.SINGLE_LANE.getCode());
                        featureDataDTO.setLanePosition(LanePositionEnum.NONE.getCode());
                    }
                    break;
                }
            }
        } catch (Exception e) {
            System.out.println(e);
        }
        return featureDataDTO;
    }

    /**
     * 计算GeoHash的风险概率
     *
     * @param geoHashtrainDataList
     * @return
     */
    private Map<String, Double> computeGeoHashRiskProbability(List<RiskFeatureTestDataVO> geoHashtrainDataList) {
        // 1. 创建数据结构来存储每个GeoHash的统计信息
        Map<String, Integer> geoHashTotalCount = new HashMap<>();
        Map<String, Integer> geoHashRiskCount = new HashMap<>();

        // 2. 遍历训练数据，计算GeoHash并统计
        for (RiskFeatureTestDataVO trainDataVO : geoHashtrainDataList) {
            try {
                //35bit 对应 7个字符
                GeoHash geoHash = GeoHash.withBitPrecision(trainDataVO.getLatitude(), trainDataVO.getLongitude(), 35);
                String geoHashStr = geoHash.toBase32();

                // 更新总计数
                geoHashTotalCount.put(geoHashStr, geoHashTotalCount.getOrDefault(geoHashStr, 0) + 1);

                // 如果是风险事件，更新风险计数
                if (trainDataVO.getRisk() == 1) {
                    geoHashRiskCount.put(geoHashStr, geoHashRiskCount.getOrDefault(geoHashStr, 0) + 1);
                }
            } catch (Exception e) {
                System.out.println(e);
            }
        }

        // 3. 计算每个GeoHash的风险概率并输出
        Map<String, Double> riskAreaMap = geoHashTotalCount.keySet().stream()
                .map(geoHash -> {
                    int total = geoHashTotalCount.get(geoHash);
                    int riskCount = geoHashRiskCount.getOrDefault(geoHash, 0);
                    double probability = (double) riskCount / total;
                    return new AbstractMap.SimpleEntry<>(geoHash, probability);
                })
                // todo：可以考虑更改为可配置的
                .filter(entry -> geoHashTotalCount.get(entry.getKey()) >= 5)
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,  // 如果有重复键，保留第一个
                        LinkedHashMap::new  // 使用LinkedHashMap保持排序
                ));

        return riskAreaMap;

    }

    /**
     * 获取GeoHash
     *
     * @param longitude
     * @param latitude
     * @return
     */
    private String getGeoHash(double longitude, double latitude) {
        try {
            return GeoHash.withBitPrecision(latitude, longitude, 35).toBase32();
        } catch (Exception e) {
            System.out.println(e);

        }
        return "";
    }


}