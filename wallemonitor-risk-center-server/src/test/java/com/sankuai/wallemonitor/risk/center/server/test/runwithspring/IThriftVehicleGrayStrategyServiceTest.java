package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftVehicleGrayStrategyService;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;


/**
 * 车辆灰度策略过滤接口测试类
 */
@Slf4j
public class IThriftVehicleGrayStrategyServiceTest extends SpringTestBase {

    @Resource
    private IThriftVehicleGrayStrategyService vehicleGrayStrategyService;

 
    /**
     * 测试车辆灰度策略过滤接口
     * 输出测试结果，验证灰度策略过滤逻辑
     */
    @Test
    public void testGetVehicleGrayStrategyFilter() {
        long maxTime = 0;

        for (int i = 0; i < 100; i++) {
            long startTime = System.currentTimeMillis();
            // 执行接口调用
            EveThriftResponse<List<String>> response = vehicleGrayStrategyService.getVehicleGrayStrategyFilter();
            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;
            log.info("第{}次调用灰度策略过滤接口，耗时{}ms", i, costTime);
            if (costTime > maxTime) {
                maxTime = costTime;
            }  
        }
        log.info("最大耗时:{}ms", maxTime);
    }
    

}
