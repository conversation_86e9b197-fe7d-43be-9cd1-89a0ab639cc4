package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.result.ISTrafficLightResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.infra.dto.MarkMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckingExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskMarkRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskStrandingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.server.consumer.AutoMarkConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;

public class AutoMarkConsumerTest extends SpringTestBase {

    //

    @Resource
    private AutoMarkConsumer autoMarkConsumer;

    @Resource
    private RiskCheckQueueRepository checkQueueRepository;

    @Resource
    private RiskMarkRepository riskMarkRepository;

    @Resource
    private CaseMarkInfoRepository caseMarkRepository;

    @Resource
    private RiskStrandingRecordRepository strandingRecordRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;


    @Before
    public void setUp() {

        RiskCheckingQueueItemDO markItem =
                JacksonUtils.from(
                        "{\"vin\":\"LSTM00001322\",\"tmpCaseId\":\"1231231231231231\",\"eventId\":\"123123123\",\"type\":\"VEHICLE_STAND_STILL\",\"source\":\"BEACON_TOWER\",\"checking\":true,\"round\":0}",
                        RiskCheckingQueueItemDO.class);
        markItem.setOccurTime(DatetimeUtil.convertDatetimeStr2Date("2024-10-29 16:18:20"));
        markItem.setVin("LMTZSV025NC040897");
        markItem.setTmpCaseId("MT0383320241029161820S03T01");
        markItem.setMaxRound(5);
        markItem.setCheckResult(RiskCheckResultDO.builder()
                .extra(ReflectUtils.getNonNullFieldAndValue(ISCheckActionResult.<ISTrafficLightResult>builder()
                        .categoryEnum(ISCheckCategoryEnum.IN_JUNCTION)
                        .actionResult(
                                ISTrafficLightResult.builder().nowTrafficLightType(TrafficLightTypeEnum.RED).build())
                        .build())).build());
        markItem.setExtInfo(RiskCheckingExtInfoDO.builder().lastCheckResult(new ArrayList<>()).build());
        markItem.setChecking(false);
        CaseMarkInfoDO caseMarkInfoDO = RiskCaseFactory.initMarkInfo(markItem.getTmpCaseId());
        strandingRecordRepository.save(
                RiskStrandingRecordDO.builder().occurTime(markItem.getOccurTime()).tmpCaseId(markItem.getEventId())
                        .vin(markItem.getVin()).status(DetectRecordStatusEnum.CONFIRMED).build());

        riskCaseRepository.save(RiskCaseDO.builder()
                //
                .eventId(markItem.getEventId())
                //
                .caseId(markItem.getEventId())
                //
                .status(RiskCaseStatusEnum.NO_DISPOSAL)
                //
                .type(RiskCaseTypeEnum.STRANDING)
                //
                .source(RiskCaseSourceEnum.BEACON_TOWER)

                .build());
        caseMarkRepository.save(caseMarkInfoDO);
        checkQueueRepository.save(markItem);
        riskMarkRepository.saveMarkItem(Lists.newArrayList(markItem), "version_one");
    }

    @Test
    @SneakyThrows
    public void test() {
        MarkMessageDTO markMessageDTO = JacksonUtils.from(
                "{}",
                MarkMessageDTO.class);
        markMessageDTO.setItemCaseId("MT0383320241029161820S03T01");
        autoMarkConsumer.receive(
                JacksonUtils.to(markMessageDTO)
        );

    }

    @Test
    @SneakyThrows
    public void testMultiVersion() {
        MarkMessageDTO markMessageDTO = JacksonUtils.from("{}", MarkMessageDTO.class);
        markMessageDTO.setItemCaseId("MT0383320241029161820S03T01");
        markMessageDTO.setVersion("version_one");
        autoMarkConsumer.receive(JacksonUtils.to(markMessageDTO));

    }


}