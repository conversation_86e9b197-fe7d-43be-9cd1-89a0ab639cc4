package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventBaseInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventCreateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventDetermineNatureInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventDetermineReasonInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventHandleInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventListRequest;
import com.sankuai.wallemonitor.risk.center.api.request.NegativePublicEventReviewInfoUpdateRequest;
import com.sankuai.wallemonitor.risk.center.api.response.NegativePublicEventListResponse;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftNegativePublicEventService;
import com.sankuai.wallemonitor.risk.center.domain.process.NegativePublicEventCreateProcess;
import com.sankuai.wallemonitor.risk.center.domain.process.NegativePublicEventStatusUpdateProcess;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleStatusAdapter;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleRealtimeStatusVTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024-09-15
 */
public class VehicleStatusAdaptorTest extends SpringTestBase {

    @Resource
    private VehicleStatusAdapter vehicleStatusAdapter;

    @Resource
    private IThriftNegativePublicEventService iThriftNegativePublicEventService;

    @Resource
    private NegativePublicEventCreateProcess createProcess;

    @Resource
    private NegativePublicEventStatusUpdateProcess statusUpdateProcess;

    @Test
    public void testQuerySuccess() {
        List<VehicleRealtimeStatusVTO> vehicleRealtimeStatusVTOList = vehicleStatusAdapter.queryByVinList(-1,
                Collections.singletonList("LMTZSV027NC054431"));
        System.out.printf(Locale.ENGLISH, "查询车辆状态列表: %s%n", JacksonUtils.to(vehicleRealtimeStatusVTOList));
    }

    @Test
    public void testNegativeCreate() {
        NegativePublicEventCreateRequest request = new NegativePublicEventCreateRequest();
        request.setSourceList(Arrays.asList(1, 2, 3));
        request.setCity("北京");
        request.setDistrict("朝阳");
        request.setType(1);
        request.setProvince("北京");
        request.setEventDesc("测试");
        request.setLocation("望京");
        request.setOccurTime("2024-09-15 10:00:00");
        request.setPerceiveTime("2024-09-15 10:00:00");
        request.setRelatedFileLinks(Arrays.asList("test", "test2"));

        iThriftNegativePublicEventService.create(request);
    }

    @Test
    public void testList() {
        NegativePublicEventListRequest request = new NegativePublicEventListRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setCreateTimeStart("2024-12-30 00:00:00");
        request.setCreateTimeEnd("2025-12-31 00:00:00");
        request.setStatusList(Arrays.asList(1, 2));
        request.setSourceList(Arrays.asList(1, 2, 3));
        EveThriftPageResponse<List<NegativePublicEventListResponse>> response = iThriftNegativePublicEventService.list(
                request);
        System.out.println("testList: " + response);
    }

    @Test
    public void testCreate() {
        NegativePublicEventDetermineNatureInfoUpdateRequest request = new NegativePublicEventDetermineNatureInfoUpdateRequest();
        request.setEventId("test");
        request.setNature(1);
        iThriftNegativePublicEventService.updateDetermineNatureInfo(request);
    }

    @Test
    public void testUpdateBaseInfo() {
        NegativePublicEventBaseInfoUpdateRequest request = new NegativePublicEventBaseInfoUpdateRequest();
        request.setEventId("b21211f785774b299e91bbcdbc33f204");
        request.setCity("菏泽");
        request.setSourceList(Arrays.asList(3, 4));
        request.setRelatedFileLinks(Arrays.asList("test", "test2", "test3"));
        iThriftNegativePublicEventService.updateBaseInfo(request);
    }

    @Test
    public void testUpdateDetermineNatureInfo() {
        NegativePublicEventDetermineNatureInfoUpdateRequest request = new NegativePublicEventDetermineNatureInfoUpdateRequest();
        request.setEventId("43b94faeecc348fd9ebf5d2aab86facf");
        request.setNature(2);
        request.setHandlers(Arrays.asList("zdc"));
        request.setConditionDescRelatedFileLinkList(Arrays.asList("test4"));
        iThriftNegativePublicEventService.updateDetermineNatureInfo(request);
    }

    @Test
    public void testUpdateDetermineReasonInfo() {
        NegativePublicEventDetermineReasonInfoUpdateRequest request = new NegativePublicEventDetermineReasonInfoUpdateRequest();
        request.setEventId("fc088eda215a44aa84840b0d8d364ff9");
        request.setReasonList(Arrays.asList("1", "2"));
        iThriftNegativePublicEventService.updateDetermineReasonInfo(request);
    }

    @Test
    public void testUpdateHandleInfo() {
        NegativePublicEventHandleInfoUpdateRequest request = new NegativePublicEventHandleInfoUpdateRequest();
        request.setEventId("a630400e72394aeda835e07744efbec8");
        request.setHandleDegree(1);
        request.setHandleResultDescFileLinkList(Arrays.asList("test5"));
        iThriftNegativePublicEventService.updateHandleInfo(request);
    }

    @Test
    public void testQuery() {
        NegativePublicEventReviewInfoUpdateRequest request = new NegativePublicEventReviewInfoUpdateRequest();
        request.setEventId("0bcfcf0a1e50429f84490fee90501c60");
        request.setReviewInfo("");
        iThriftNegativePublicEventService.updateReviewInfo(request);
    }

    @Test
    public void testCreateProcess() {
        List<NegativePublicEventDO> eventDOList = new ArrayList<>();
        NegativePublicEventDO eventDO = new NegativePublicEventDO();
        eventDO.setEventId("9f7f6eeff12c47d2b97a4df8a4d78ce8");
        eventDO.setReporter("zhaoduancai");
        eventDOList.add(eventDO);
        createProcess.handleProcessMsg(eventDOList);
    }

    @Test
    public void testStatusUpdateProcess() {
        List<NegativePublicEventDO> eventDOList = new ArrayList<>();
        NegativePublicEventDO eventDO = new NegativePublicEventDO();
        eventDO.setEventId("99a14319cd0d4d10827058aace6b982f");
        eventDO.setStatus(NegativePublicEventStatusEnum.LOCATED);
        eventDO.setGroupId("64013915624");
        eventDOList.add(eventDO);
        statusUpdateProcess.handleProcessMsg(eventDOList);
    }


}
