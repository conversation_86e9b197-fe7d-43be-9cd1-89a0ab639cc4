package com.sankuai.wallemonitor.risk.center.server.test.unit.utiltest;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import org.junit.Test;

public class RiskAutoCheckConfigDTOTest {

    @Test
    public void testRiskAutoMark() {
        RiskAutoCheckConfigDTO build = RiskAutoCheckConfigDTO.builder()
                .markTimePeriodList(Lists.newArrayList("00:00:00-23:59:59")).build();

        boolean inMarkTime = build.isInMarkTime();
        System.out.println(inMarkTime);
    }
}
