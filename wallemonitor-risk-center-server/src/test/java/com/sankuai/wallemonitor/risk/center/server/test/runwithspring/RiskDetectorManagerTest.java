package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.AdminAddSafetyAreaRequest;
import com.sankuai.wallemonitor.risk.center.api.request.AdminDeleteSafetyAreaRequest;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftAdminService;
import com.sankuai.wallemonitor.risk.center.domain.component.DetectorCommonCompute.VehicleLaneAndPositionComputeDTO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetectorManager;
import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetectorManager.DetectorParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CommonSearchAdaptor;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.HdMapRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.RiskDetectBaseConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.junit.Test;

public class RiskDetectorManagerTest extends SpringTestBase {

    @Resource
    private RiskDetectorManager riskDetectorManager;

    @Resource
    private VehicleRuntimeInfoContextRepository contextRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private HdMapRepository repository;
    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @Resource
    private CommonSearchAdaptor commonSearchAdaptor;

    @Resource
    private IThriftAdminService thriftAdminService;

    @Test
    public void init() {
        String polygonStr = "[[116.54177966289802,40.07193864382964],[116.5418406831609,40.07187193673789],[116.5418614702835,40.071880659978234],[116.54179441504948,40.07194531453635]]";
        List<List<Double>> list = JacksonUtils.from(polygonStr, new TypeReference<List<List<Double>>>() {});
        EveThriftResponse<String> thriftResponse = thriftAdminService
                .addSafetyArea(AdminAddSafetyAreaRequest.builder().polygon(list).build());
        thriftAdminService.deleteSafetyArea(
                AdminDeleteSafetyAreaRequest.builder().areaId(Lists.newArrayList(thriftResponse.getData())).build());
    }

    @Test
    public void process() {

        RiskDetectBaseConfig detectBaseConfig = lionConfigRepository
                .getDetectConfig(RiskCaseTypeEnum.STRANDING);
        VehicleRuntimeInfoContextDO context = contextRepository.getByVin("LMTZSV027MC042469");
        // 116.5446050628,39.7794187599
        context.setLng("114.03524750263816");
        context.setLat("22.689952317065718");
        context.setVin("LMTZSV023NC059755");
        context.setTrafficLightType(TrafficLightTypeEnum.GREEN);
        context.setSpeed(0D);
        DetectorParamDTO detectorParamDTO = DetectorParamDTO.builder().build();
        detectorParamDTO.setVinRunTimeList(Collections.singletonList(context));
        List<VehicleEveInfoVTO> list = vehicleAdapter.queryRuntimeVehicleInfo(
                VehicleRuntimeInfoParamVTO.builder().vinList(Collections.singletonList("LMTZSV027MC042469")).build());
        list.get(0).setHdMapVersion("shenzhenlonghua_hdmap_v5.185.0.r");
        detectorParamDTO.setEveInfoList(list);
        DetectContextDTO detectContext = DetectContextDTO.builder().detectConfig(detectBaseConfig)
                .runtimeContext(context).eveInfo(list.get(0)).build();
        Date date = new Date();
        //        SpELUtil.evaluateWithVariables(
        //                "@hdMapRepositoryImpl.inRestrictParking(#new('RiskRestrictQueryDTO',{'DISTRICT_ENTRANCE'},#runtimeInfo.getLocation(),#eveInfo.hdMapVersion,20,2))",
        //                detectContext.getSpelContext(), Object.class);

        // System.out.println(end.getTime() - date.getTime());
        //        repository.inRestrictParking(RiskRestrictQueryDTO.builder().hdMapVersion("shenzhenlonghua_hdmap_v5.185.0.r")
        //                .expandMeter(1).meter(20).restrictTypes(Lists.newArrayList(HdMapAreaEnum.DISTRICT_ENTRANCE.getValue()))
        //                .position(PositionDO.getPosition(116.5446050628, 39.7794187599, CoordinateSystemEnum.WGS84)).build());
        //        SpELUtil.evaluateWithVariables(
        //                "#runtimeInfo?.speed <= 0.0000036 and #runtimeInfo?.trafficLightType?.code != 1",
        //                detectContext.getSpelContext(), Object.class);
        // 模拟获取高精地图区域
        //        Mockito.doReturn("shenzhenlonghua").when(vehicleAdapter)
        //                .getVehicleHdMapArea(Mockito.anyString());
        Object obj = SpELUtil.evaluateWithVariables(
                "@detectorCommonCompute.buildVehicleLaneAndPosition(#runtimeInfo)",
                detectContext.getSpelContext(), Object.class);
        Map<String, Object> variables = new HashMap<>();
        variables.put("detectorContext", new Object());
        VehicleLaneAndPositionComputeDTO computeDTO = new VehicleLaneAndPositionComputeDTO();
        Object obj1 = SpELUtil.evaluateWithVariables(
                "commonCompute.hasObstacleV2(#detectorContext.get('laneAndObsAndPositionContext'),12,15)",
                variables, Object.class);
        //        Object obj2 = SpELUtil.evaluateWithVariables(
        //                "@detectorCommonCompute.isHasObstacle(#runtimeInfo,#record)",
        //                detectContext.getSpelContext(), Object.class);

        riskDetectorManager.process(detectorParamDTO);
        list.get(0).setDriveMode(1);
        list.get(0).setGear("D");
        context.setBizStatus("");
        // riskDetectorManager.handleDetector(context, list.get(0), "ErrorBypassDetector");
        Date end = new Date();
        System.out.println(end.getTime() - date.getTime());
    }

    @Test
    public void test() {
        PositionDO positionDO = PositionDO.getPosition(118.9681, 39.2147, CoordinateSystemEnum.WGS84);
        PositionDO positionDO1 = GeoToolsUtil.transferCoordinateSystemEnum(positionDO,
                CoordinateSystemEnum.GCJ02);

        safetyAreaRepository.verifyInParkingArea(positionDO1);
    }

    @Test
    public void test1() {
        List<String> result = commonSearchAdaptor.getBusinessCarVinList(Arrays.asList("业务运营", "业务探索"));
        System.out.println("result: " + result);
    }

    @Test
    public void test2() {
        commonSearchAdaptor.querySwitchPowerStatus("LMTZSV023NC092853", new Date(0L));
    }
}