package com.sankuai.wallemonitor.risk.center.server.test.unit.crane;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleStatusAdapter;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.RiskCheckQueueRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.server.crane.SecurityCallCloudControlCrane;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import javafx.util.Pair;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class SecurityCallCloudControlCraneTest extends ServiceTestBase {

    @Spy
    @InjectMocks
    SecurityCallCloudControlCrane securityCallCloudControlCrane;

    @Spy
    @InjectMocks
    VehicleStatusAdapter vehicleStatusAdapter;

    @Spy
    @InjectMocks
    RiskCheckQueueRepositoryImpl riskCheckQueueRepository;


    @Before
    public void setup() {
        // mock riskDO
        List<RiskCaseDO> riskCaseDOList = new ArrayList<>();
        riskCaseDOList.add(RiskCaseDO.builder().caseId("111")
                .source(RiskCaseSourceEnum.BEACON_TOWER).type(RiskCaseTypeEnum.VEHICLE_STAND_STILL).occurTime(new Date()).build());

        riskCaseDOList.add(RiskCaseDO.builder().caseId("222")
                .source(RiskCaseSourceEnum.BEACON_TOWER).type(RiskCaseTypeEnum.VEHICLE_STAND_STILL).occurTime(new Date()).build());

        riskCaseDOList.add(RiskCaseDO.builder().caseId("333")
                .source(RiskCaseSourceEnum.BEACON_TOWER).type(RiskCaseTypeEnum.VEHICLE_STAND_STILL).occurTime(new Date()).build());

        // mock risk vechicle Relation
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = new ArrayList<>();
        riskCaseVehicleRelationDOList.add(RiskCaseVehicleRelationDO.builder().caseId("111").vin("aaa").build());
        riskCaseVehicleRelationDOList.add(RiskCaseVehicleRelationDO.builder().caseId("222").vin("aaa").build());
        riskCaseVehicleRelationDOList.add(RiskCaseVehicleRelationDO.builder().caseId("333").vin("bbb").build());

        String securityConfigString = "{\"1\":{\"source\":5,\"type\":1,\"reason\":6001,\"requestSource\":11,\"filters\":[\"#vhr != '>1'\",\"#riskDuration == null || #riskDuration < 30\",\"#mrmStatus != 0 && #mrmStatus != 1 && #mrmStatus != 3\",\"#hasAccident == true\",\"#hasReOrder == true\",\"#hasRescueOrder == true\",\"#improperStrandingReason?.withRescueOrder == true\",\"#improperStrandingReason?.withAccidentOrder == true\",\"#improperStrandingReason?.withReOrder == true\",\"#improperStrandingReason?.withManualParking == true\",\"#businessType != '路测' && #purpose != '业务运营'\",\"!{'IN_MIDDLE_ROAD','OPPSITE_ROAD_DIRECTION','CONFLICT_WITH_PASSAGER','STOP_BY_OBSTACLE','IN_JUNCTION','IN_VALID_DRIVING_AREA'}.contains(#firstSubCategory)\",\"#firstSubCategory == 'IN_JUNCTION' and #vehicleRuntimeInfo.findTrafficLightType() != null and  #vehicleRuntimeInfo.findTrafficLightType().code == 1\",\"#firstSubCategory == 'STOP_BY_OBSTACLE' and #item?.checkResult?.extra?.get('crossLine') == true\",\"#vehicleRuntimeInfo.speed > 0.2\",\"(#vehicleRuntimeInfo.findCounterDuration('greenLightCounter') >= 0 and #vehicleRuntimeInfo.findCounterDuration('greenLightCounter') <= 3) or (#vehicleRuntimeInfo.findCounterDuration('greenLightCounter') < 0 and #vehicleRuntimeInfo.findTrafficLightType() != null and #vehicleRuntimeInfo.findTrafficLightType().code == 3)\"]},\"2\":{\"source\":5,\"type\":13,\"reason\":6003,\"requestSource\":11,\"filters\":[\"#vhr != '>1'\",\"!{'路测-提速测试-北京','云辅助专项','路测-AB后沙峪测试','路测-云辅助beta-深圳'}.contains(#purpose)\"]},\"3\":{\"source\":5,\"type\":15,\"reason\":6004,\"requestSource\":11,\"filters\":[\"#vhr != '>1'\",\"!{'路测-提速测试-北京','云辅助专项','路测-AB后沙峪测试','路测-云辅助beta-深圳'}.contains(#purpose)\"]},\"4\":{\"source\":5,\"type\":12,\"reason\":6005,\"requestSource\":11,\"filters\":[\"#vhr != '>1'\",\"#mrmStatus != 0 && #mrmStatus != 1 && #mrmStatus != 3\",\"#hasAccident == true\",\"#hasReOrder == true\",\"#hasRescueOrder == true\",\"#improperStrandingReason?.withRescueOrder == true\",\"#improperStrandingReason?.withAccidentOrder == true\",\"#improperStrandingReason?.withReOrder == true\",\"#improperStrandingReason?.withManualParking == true\",\"#businessType != '路测' && #purpose != '业务运营'\"]},\"5\":{\"source\":5,\"type\":16,\"reason\":6006,\"requestSource\":11,\"filters\":[\"#vin != 'LA71AUB12S0508666'\"]}}";

        Map<Pair<Integer, Integer>, LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO> collect = JacksonUtils.from(securityConfigString, new TypeReference<Map<Integer, LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO>>() {
        }).values().stream().collect(Collectors.toMap(x -> new Pair<>(x.getSource(), x.getType()), Function.identity(), (oldV, newV) -> oldV));

        // MOck vehicleInfo
        List<VehicleInfoDO> vehicleInfoDOList = new ArrayList<>();
        vehicleInfoDOList.add(VehicleInfoDO.builder().vin("aaa").build());
        vehicleInfoDOList.add(VehicleInfoDO.builder().vin("bbb").build());
        vehicleInfoDOList.add(VehicleInfoDO.builder().vin("ccc").build());

        // Mock 坐席
        Map<String, Integer> seatMap = new HashMap<>();
        seatMap.put("aaa", 1);
        seatMap.put("bbb", 1);
        seatMap.put("ccc", 1);

        // Mock case Mark info
        Map<String, CaseMarkInfoDO> caseMarkInfoDOMap = new HashMap<>();
        caseMarkInfoDOMap.put("111", CaseMarkInfoDO.builder().improperStrandingReason(ImproperStrandingReason.builder().build()).build());
        caseMarkInfoDOMap.put("222", CaseMarkInfoDO.builder().improperStrandingReason(ImproperStrandingReason.builder().build()).build());
        caseMarkInfoDOMap.put("333", CaseMarkInfoDO.builder().improperStrandingReason(ImproperStrandingReason.builder().build()).build());

        // Mock Risk checkQueueRepo
        List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList = new ArrayList<>();
        riskCheckingQueueItemDOList.add(RiskCheckingQueueItemDO.builder().tmpCaseId("111").build());
        riskCheckingQueueItemDOList.add(RiskCheckingQueueItemDO.builder().tmpCaseId("222").build());
        riskCheckingQueueItemDOList.add(RiskCheckingQueueItemDO.builder().tmpCaseId("333").build());

        Mockito.doReturn(riskCaseDOList).when(riskCaseRepository).queryByParam(Mockito.any());

        Mockito.doReturn(riskCaseDOList).when(riskCaseQueryService).queryRiskCaseDispose(Mockito.any(), Mockito.any());

        Mockito.doReturn(collect).when(lionConfigRepository).getCallSecurityStrategyConfig();

        Mockito.doReturn(riskCaseVehicleRelationDOList).when(riskCaseVehicleRelationRepository).queryByParam(Mockito.any());


        Mockito.doReturn(vehicleInfoDOList).when(vehicleInfoRepository).queryByVinList(Mockito.any());

        Mockito.doReturn(seatMap).when(vehicleStatusAdapter).getVin2MrmStatusMap(Mockito.any());

        Mockito.doReturn(caseMarkInfoDOMap).when(caseMarkInfoRepository).queryMapByParam(Mockito.any());

        Mockito.doReturn(riskCheckingQueueItemDOList).when(riskCheckQueueRepository).queryByParam(Mockito.any());

        ReflectionTestUtils.setField(securityCallCloudControlCrane, "riskCaseCallSecuritySystemQueryTimeRange", 180);

    }

    @Test
    public void test() throws Exception {
        securityCallCloudControlCrane.run();
    }
}
