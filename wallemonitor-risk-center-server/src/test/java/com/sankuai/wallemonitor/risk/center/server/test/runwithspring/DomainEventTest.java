package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.server.StartApp;
import com.sankuai.wallemonitor.risk.center.server.consumer.DomainEventConsumer;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public class DomainEventTest {

    private String message = "{\"entry\":{\"domainClassName\":\"RiskCaseVehicleRelationDO\",\"operateEntry\":\"VEHICLE_DATA_CONSUMER_ENTER\"},\"timestamp\":1718710181682,\"operator\":\"\",\"traceId\":\"a73220a5-2f6b-4238-8c8c-0c47dbc44095\",\"extInfo\":{},\"before\":[],\"after\":[{\"caseId\":\"015a397c6d0a4a8a857da122b12820c1\",\"type\":\"EXAM_OBSTACLE_STRANDING\",\"placeCode\":\"hualikan\",\"status\":\"NO_DISPOSAL\",\"eventId\":\"20240617184839021_common398_s20-173\",\"source\":\"BEACON_TOWER\",\"extInfo\":{\"city\":\"北京市\",\"are\":\"顺义区\",\"poi\":\"莫奈花园\"},\"isDeleted\":\"NOT_DELETED\"}]}";
    @Resource
    private DomainEventConsumer domainEventConsumer;

    @Test
    public void test() {
        domainEventConsumer.receive(message);
    }

}
