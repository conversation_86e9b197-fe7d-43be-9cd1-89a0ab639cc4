package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseUploadAutoCarDataProcess;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

public class RiskCaseUploadAutoCarDataProcessTest extends SpringTestBase {

    @Resource
    private RiskCaseUploadAutoCarDataProcess riskCaseUploadAutoCarDataProcess;

    @Test
    public void test() {
        List<RiskCaseDO> riskCaseDOList = new ArrayList<>();
        Date occurTime = DatetimeUtil.convertDatetimeStr2Date("2024-10-29 17:09:15");
        riskCaseDOList.add(RiskCaseDO.builder().caseId("M603320241029170915S01T01").occurTime(occurTime).build());

//        riskCaseUploadAutoCarDataProcess.process(riskCaseDOList);
    }
}
