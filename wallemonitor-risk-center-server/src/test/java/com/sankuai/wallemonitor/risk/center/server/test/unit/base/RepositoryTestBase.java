package com.sankuai.wallemonitor.risk.center.server.test.unit.base;


import static org.mockito.ArgumentMatchers.anyList;

import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.GisAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.PlaceAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.OperateEnterContext;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.UserInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.convert.GisInfoVTO2DOConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.convert.PositionConvert;
import com.sankuai.wallemonitor.risk.center.infra.convert.VehicleVTO2DOConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.DomainEventProcessRecordRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.GisInfoRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.IDGenerateRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.VehicleInfoRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.CaseMarkInfoRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.FeedbackRecordRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.RiskCaseRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.RiskCaseVehicleRelationRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.UserNoticeReadRecordRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.utils.VelocityUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.SpringUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.lion.LionConfigUtils;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleBasicVTO;
import java.util.ArrayList;
import java.util.List;
import org.apache.logging.log4j.core.util.ExecutorServices;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(MockitoJUnitRunner.Silent.class)
@PrepareForTest({TraceExecutors.class, ExecutorServices.class, UserInfoContext.class, SpringUtils.class,
        LionConfigUtils.class, VelocityUtils.class, HttpUtils.class, OperateEnterContext.class, EveHttpResponse.class
})
@PowerMockIgnore({"com.fasterxml.*",
        "com.dianping.cat.*",
        "com.dianping.pigeon.*",
        "com.meituan.scribe.*",
        "com.meituan.service.*",
        "com.meituan.service.inf.kms.*",
        "com.meituan.mtrace.*",
        "com.sankuai.inf.*",
        "com.sun.*",
        "javax.*",
        "javax.management.*",
        "javax.net.ssl.*",
        "org.apache.logging.*",
        "org.apache.*",
        "org.slf4j.*",
        "org.w3c.*",
        "org.xml.*",
        "sun.*"})
@Ignore
public abstract class RepositoryTestBase {

    /*****************************************工具层**********************************************/

    @Mock
    public PositionConvert positionDOConvert;

    @InjectMocks
    @Spy
    public GisInfoVTO2DOConvertImpl convert;

    @InjectMocks
    @Spy
    public GisAdapter gisAdapter;

    @InjectMocks
    @Spy
    public VehicleVTO2DOConvertImpl vehicleInfoConvert;
    /*****************************************仓储层**********************************************/

    @InjectMocks
    @Spy
    public GisInfoRepositoryImpl gisInfoRepository;

    @InjectMocks
    @Spy
    public LionConfigRepositoryImpl lionConfigRepository;

    @InjectMocks
    @Spy
    public IDGenerateRepositoryImpl idGenerateRepository;

    @InjectMocks
    public VehicleInfoRepositoryImpl vehicleInfoRepository;

    @InjectMocks
    @Spy
    public DomainEventProcessRecordRepositoryImpl domainEventProcessRecordRepository;

    @InjectMocks
    @Spy
    public RiskCaseVehicleRelationRepositoryImpl riskCaseVehicleRelationRepository;

    @InjectMocks
    @Spy
    public RiskCaseRepositoryImpl riskCaseRepository;

    @InjectMocks
    @Spy
    public CaseMarkInfoRepositoryImpl caseMarkInfoRepository;

    @InjectMocks
    @Spy
    public FeedbackRecordRepositoryImpl feedbackRecordRepository;

    @InjectMocks
    @Spy
    public UserNoticeReadRecordRepositoryImpl userNoticeReadRecordRepository;

    /*****************************************DAO层**********************************************/

    @InjectMocks
    @Spy
    public VehicleAdapter vehicleAdapter;

    @InjectMocks
    @Spy
    public SquirrelAdapter squirrelAdapter;

    @InjectMocks
    @Spy
    public DxNoticeAdapter dxNoticeAdapter;

    @InjectMocks
    @Spy
    public PlaceAdapter placeAdapter;

    @Before
    public void setUp() {
        List<VehicleBasicVTO> vehicleBasicVTOS = new ArrayList<>();
        vehicleBasicVTOS.add(VehicleBasicVTO.builder().vehicleId("vehicleId").vehicleName("vehicleName").build());

        Mockito.doReturn(vehicleBasicVTOS).when(vehicleAdapter).queryVehicleBasicInfoList(anyList());
    }
}
