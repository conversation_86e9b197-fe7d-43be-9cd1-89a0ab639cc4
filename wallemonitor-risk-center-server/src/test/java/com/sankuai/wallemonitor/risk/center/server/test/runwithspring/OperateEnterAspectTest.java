package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.server.aop.OperateEnterAspect.DomainEventMessageProducerProvider;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.junit.Test;

public class OperateEnterAspectTest extends SpringTestBase {


    @Resource
    private DomainEventMessageProducerProvider domainEventMessageProducerProvider = new DomainEventMessageProducerProvider();

    @Test
    public void test() {
        domainEventMessageProducerProvider.getDomainMessageProducer(RiskCheckingQueueItemDO.class.getSimpleName());
    }

}