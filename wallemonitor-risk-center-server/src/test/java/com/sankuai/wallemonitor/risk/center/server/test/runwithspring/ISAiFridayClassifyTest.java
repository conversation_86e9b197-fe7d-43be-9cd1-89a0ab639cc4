package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISAiFridayClassify;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISEveAndRuntimeInfoFilterAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISInParkingArea;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.junit.Test;

public class ISAiFridayClassifyTest extends SpringTestBase {

    @Resource
    private ISAiFridayClassify isAiFridayClassify;

    @Resource
    private ISInParkingArea isInParkingArea;

    @Resource
    private ISEveAndRuntimeInfoFilterAction isEveAndRuntimeInfoFilterAction;

    @Test
    public void testClassify() {

        isAiFridayClassify.execute(ISCheckActionContext.builder()
                .item(RiskCheckingQueueItemDO.builder()
                        .tmpCaseId("MT0846920241218143654S05T09")
                        .occurTime(DatetimeUtil.convertDatetimeStr2Date("2024-10-29 16:18:20"))
                        .vin("LMTZSV021NC056238")
                        .build())
                .vehicleRunTimeContext(
                        VehicleRuntimeInfoContextDO.builder().trafficLightType(TrafficLightTypeEnum.RED).build())
                .build());

    }

    @Test
    public void testInPark() {
        isInParkingArea.execute(ISCheckActionContext.builder()
                .item(RiskCheckingQueueItemDO.builder()
                        .tmpCaseId("MT0383320241029161820S03T01")
                        .occurTime(DatetimeUtil.convertDatetimeStr2Date("2024-11-18 21:27:00"))
                        .vin("LMTZSV025NC091137")
                        .build())
                .build());
    }

    @Test
    public void testFilter() {
        Object object = isEveAndRuntimeInfoFilterAction.execute(ISCheckActionContext.builder()
                .item(RiskCheckingQueueItemDO.builder()
                        .tmpCaseId("MT0383320241029161820S03T01")
                        .occurTime(DatetimeUtil.convertDatetimeStr2Date("2024-10-29 16:18:20"))
                        .vin("LMTZSV025NC040897")
                        .build())
                .build());
        System.out.println(object);
    }

}