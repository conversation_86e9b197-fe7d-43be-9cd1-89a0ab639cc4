package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmFilterDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.server.crane.CheckRiskCaseCallMrmCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.CheckRiskCaseCallMrmStatusCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.CheckRiskCaseLongTimeCallMrmCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.ImproperStrandingTerminalStatusCheckCrane;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Map;
import javax.annotation.Resource;
import org.junit.Test;

public class CheckRiskCaseCallMrmStatusCraneTest extends SpringTestBase {

    @Resource
    private CheckRiskCaseCallMrmCrane checkRiskCaseCallMrmCrane;

    @Resource
    private CheckRiskCaseCallMrmStatusCrane checkRiskCaseCallMrmStatusCrane;

    @Resource
    private CheckRiskCaseLongTimeCallMrmCrane checkRiskCaseLongTimeCallMrmCrane;

    @Resource
    private ImproperStrandingTerminalStatusCheckCrane improperStrandingTerminalStatusCheckCrane;


    @Test
    public void testRiskCaseCallMrmCrane() throws Exception {
        checkRiskCaseCallMrmCrane.run();
    }

    @Test
    public void testRiskCaseLongTimeCallMrmCrane() throws Exception {
        RiskCaseCallMrmFilterDTO riskCaseCallMrmFilterDTO = RiskCaseCallMrmFilterDTO.builder()
                .firstSubCategory("SPECIAL_AREA_STRANDING")
                .riskDuration(1L).build();
        Map<String, Object> filterDTOMap = ReflectUtils.getNonNullFieldAndValue(riskCaseCallMrmFilterDTO);
        Object obj = SpELUtil.evaluateWithVariables(
                "(!T(java.util.Arrays).asList('RETROGRADE', 'SPECIAL_AREA_STRANDING', 'DRIVE_ON_TRAFFIC_LINE').contains(#category) && (#riskDuration == null || #riskDuration < 0.5)) || (T(java.util.Arrays).asList('RETROGRADE', 'SPECIAL_AREA_STRANDING', 'DRIVE_ON_TRAFFIC_LINE').contains(#category) && (#riskDuration == null || #riskDuration < 3))",
                filterDTOMap, Object.class);
        System.out.println(obj);
    }

    @Test
    public void testQueryFail() throws Exception {
        checkRiskCaseCallMrmStatusCrane.run();
    }

    @Test
    public void testLongTimeCallMrm() throws Exception {
        checkRiskCaseLongTimeCallMrmCrane.run();
    }

    @Test
    public void testCancelStatus() throws Exception {
        improperStrandingTerminalStatusCheckCrane.run();
    }
}
