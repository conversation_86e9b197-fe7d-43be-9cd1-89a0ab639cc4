package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.dbrepo;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCaseMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCase;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;

public class RiskCaseRepositoryTest extends RepositoryTestBase {

    @Spy
    private RiskCaseMapper mapper;

    @Spy
    private RiskCaseConvertImpl covert;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(riskCaseRepository, "mapper", mapper);
        ReflectionTestUtils.setField(riskCaseRepository, "covert", covert);


    }

    @Test
    public void testGetByCaseId() {
        // mock
        PowerMockito.when(mapper.selectOne(any())).thenReturn(RiskCase.builder().build());
        // 运行
        RiskCaseDO result = riskCaseRepository.getByCaseId("caseId");

        // 验证
        assertEquals(result, RiskCaseDO.builder().isDeleted(IsDeleteEnum.NOT_DELETED).build());
    }

    @Test
    public void testQueryByParam() {
        // mock
        PowerMockito.when(mapper.selectList(any(), any(), any()))
                .thenReturn(Collections.singletonList(RiskCase.builder().build()));
        // 运行
        List<RiskCaseDO> RiskCaseDOList = riskCaseRepository.queryByParam(
                CaseMarkInfoDOQueryParamDTO.builder().caseIdList(Collections.singletonList("caseId")).build());

        //验证
        assertEquals(RiskCaseDOList,
                Collections.singletonList(RiskCaseDO.builder().isDeleted(IsDeleteEnum.NOT_DELETED).build()));
    }

    @Test
    public void testQueryByParamByPage() {
        // mock
        PowerMockito.when(mapper.selectList(any(), any(), any()))
                .thenReturn(Collections.singletonList(RiskCase.builder().caseId("caseId").isDeleted(false).build()));
        // 运行
        RiskCaseDOQueryParamDTO param = RiskCaseDOQueryParamDTO.builder()
                .caseIdList(Collections.singletonList("caseId")).build();
        Paging<RiskCaseDO> result = riskCaseRepository.queryByParamByPage(param, 0, 1);
        // 验证
        assertEquals(result.getElements(), Collections.EMPTY_LIST);

    }

    @Test
    public void testSave() {
        // 运行
        riskCaseRepository.save(RiskCaseDO.builder().caseId("caseId").build());
        // 验证
        Mockito.verify(mapper, Mockito.times(1)).save(Mockito.any());
    }

    @Test
    public void testBatchSave() {
        // mock
        riskCaseRepository.batchSave(Collections.singletonList(RiskCaseDO.builder().caseId("caseId").build()));
        // 验证
        Mockito.verify(mapper, Mockito.times(1)).save(Mockito.any());

    }

}
