package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.List;
import javax.annotation.Resource;
import org.assertj.core.util.Lists;
import org.junit.Test;

/**
 * @<PERSON> kong<PERSON>an
 * @Date 2024/7/2
 */
public class CaseMarkInfoRepositoryTest extends SpringTestBase {

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Test
    public void test() {
        CaseMarkInfoDOQueryParamDTO paramDTO = CaseMarkInfoDOQueryParamDTO.builder()
                .caseIdList(Lists.newArrayList("111"))
                .build();
        List<CaseMarkInfoDO> caseMarkInfoDOList = caseMarkInfoRepository.queryByParam(paramDTO);
        System.out.println(JacksonUtils.to(caseMarkInfoDOList));
    }
}
