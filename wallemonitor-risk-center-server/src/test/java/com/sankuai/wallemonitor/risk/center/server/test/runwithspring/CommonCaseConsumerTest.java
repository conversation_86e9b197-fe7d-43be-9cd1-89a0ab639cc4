package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.dto.CommonEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.LocationInfo;
import com.sankuai.wallemonitor.risk.center.server.consumer.CommonCaseConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
public class CommonCaseConsumerTest extends SpringTestBase {

    @Resource
    private CommonCaseConsumer commonCaseConsumer;

    @Test
    public void testAbnormalCircleRoutingCase() {
        //"latitude":40.**************,"longitude":116.**************
        CommonEventDTO commonEventDTO = CommonEventDTO.builder()
                .vin("LA71AUB17R0515977")
                .eventId("123")
                .caseType(16)
                .occurTime(new Date().getTime())
                .source(7)
                .location(LocationInfo.builder().longitude("40.**************").latitude("116.**************").build())
                .description("测试")
                .build();

        ConsumeStatus receive = commonCaseConsumer.receive(JacksonUtils.to(commonEventDTO));

    }
}
