package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskStrandingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskStrandingRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.Before;
import org.junit.Test;

public class RiskStrandingRecordRepositoryImplTest extends SpringTestBase {


    @Resource
    private RiskStrandingRecordRepository riskStrandingRecordRepository;

    @Before
    public void setUp() throws Exception {

    }

    @Test
    public void queryByParam() {
        riskStrandingRecordRepository.getByTmpCaseId("1");
        riskStrandingRecordRepository.queryByParamByPage(RiskStrandingRecordDOQueryParamDTO.builder()
                .createTimeBelowTo(new Date())
                .statusList(RiskCaseStatusEnum.getUnTerminal())
                .build(), 1, 10);
    }

    @Test
    public void queryByParamByPage() {
    }
}