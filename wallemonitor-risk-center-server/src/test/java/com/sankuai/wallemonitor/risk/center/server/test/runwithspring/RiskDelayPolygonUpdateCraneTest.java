package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.LongWaitAreaConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.MenderOperationTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.SafetyAreaInfoSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lion.LionConfigUtils;
import com.sankuai.wallemonitor.risk.center.server.crane.RiskDelayPolygonUpdateCrane;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

public class RiskDelayPolygonUpdateCraneTest extends SpringTestBase {

    @Resource
    RiskDelayPolygonUpdateCrane riskDelayPolygonUpdateCrane;

    @Resource
    SafetyAreaRepository safetyAreaRepository;





    @Test
    public void testRun() throws Exception {
        riskDelayPolygonUpdateCrane.run();
    }


    @Test
    public void testUpdateArea() {
        Map<String, RiskAutoCheckConfigDTO> groupAsMap = LionConfigUtils.getGroupAsMap(LionKeyConstant.LION_GROUP_MARK,
                new TypeReference<RiskAutoCheckConfigDTO>() {
                });
        System.out.println(groupAsMap);
        SafetyAreaDO build = SafetyAreaDO.builder().areaId("1d53fe91c88e4a55bc63a5a20a2c16d6")
                .type(MenderOperationTypeEnum.IMPROPER_STRANDING_DELAY_RECALL.getName())
                .source(SafetyAreaInfoSourceEnum.BEACON_TOWER).isDeleted(IsDeleteEnum.DELETED).build();

        safetyAreaRepository.batchSave(Lists.newArrayList(build));
    }


}
