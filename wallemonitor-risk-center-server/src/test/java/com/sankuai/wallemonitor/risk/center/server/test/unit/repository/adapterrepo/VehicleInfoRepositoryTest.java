package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.adapterrepo;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walle.cms.response.RecordHistoryRep;
import com.sankuai.walle.cms.service.ReservationService;
import com.sankuai.walledelivery.basic.client.response.deliverer.DelivererResponse;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.DelivererQueryThriftService;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter.EveBusData;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter.EveBusResult;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

public class VehicleInfoRepositoryTest extends RepositoryTestBase {

    @Mock
    private DelivererQueryThriftService delivererQueryThriftService;

    @Mock
    private ReservationService reservationService;

    private final String eveHttpResponse = "{\"code\":200,\"message\":\"OK\",\"data\":{\"code\":200,\"msg\":\"success\",\"data\":[{\"vin\":\"LMTZSV000MC000031\",\"monitor\":{}}]}}";

    private final String delivererResponse = "{\"code\":0,\"message\":\"ok\",\"data\":[{\"id\":1955,\"name\":\"T0031\",\"account\":\"t20-031\",\"type\":1,\"status\":1,\"identifyNum\":\"LMTZSV000MC000031\",\"partner\":\"\",\"positionId\":\"2\",\"positionName\":\"自动车\",\"businessStationName\":\"hualikan\",\"businessStationType\":\"花梨坎\",\"placeId\":\"hualikan\",\"placeName\":\"花梨坎\"}]}";

    private final String recordHistoryRep = "{\"msg\":\"ok\",\"code\":0,\"data\":[]}";

    @Before
    @SneakyThrows
    public void setUp() {
        // mock eveHttpResponse
        PowerMockito.mockStatic(HttpUtils.class);
        EveHttpResponse<EveBusResult<List<EveBusData>>> mockedEveHttpResponse = JacksonUtils.from(eveHttpResponse,
                new TypeReference<EveHttpResponse<EveBusResult<List<EveBusData>>>>() {
                });

        PowerMockito.when(
                        HttpUtils.postJson(any(), any(), any(), any(), any(TypeReference.class)))
                .thenReturn(mockedEveHttpResponse);

        // Mock delivererQueryThriftService.queryDelivererWithPlace
        ThriftResponse<List<DelivererResponse>> mockedThriftResponse = JacksonUtils.from(delivererResponse,
                new TypeReference<ThriftResponse<List<DelivererResponse>>>() {
                });
        PowerMockito.when(delivererQueryThriftService.queryDelivererWithPlace(any())).thenReturn(mockedThriftResponse);

        // Mock reservationService.getVresvRecordHistoryByVins
        RecordHistoryRep mockedRecordHistoryRep = JacksonUtils.from(recordHistoryRep,
                new TypeReference<RecordHistoryRep>() {
                });
        PowerMockito.when(reservationService.getVresvRecordHistoryByVins(anyList(), anyString(), anyString()))
                .thenReturn(mockedRecordHistoryRep);
    }

    @Test
    public void testQueryByVinList() {
        // 运行
        List<VehicleInfoDO> vehicleInfoDOList = vehicleInfoRepository.queryByVinList(
                Arrays.asList("LMTZSV000MC000031"));

        // 验证
        List<VehicleInfoDO> expectedVehicleInfoDOList = Collections.singletonList(
                VehicleInfoDO.builder()
                        .vin("LMTZSV000MC000031")
                        .vehicleId("T0031")
                        .vehicleName("t20-031")
                        .placeCode("hualikan")
                        .driveMode(-1)
                        .withAccidentOrder(false)
                        .withMaintenanceOrder(false)
                        .withRescueOrder(false)
                        .autocarVersion("").hdMapVersion(null).build());
        assertEquals(expectedVehicleInfoDOList.size(), vehicleInfoDOList.size());
    }
}
