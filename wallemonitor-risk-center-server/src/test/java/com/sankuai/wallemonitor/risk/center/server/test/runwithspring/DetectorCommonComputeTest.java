package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.response.VehicleDataInfoVO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.component.DetectorCommonCompute;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.Position;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDetectorRecordBaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO.VehicleObstacleContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import com.sankuai.wallemonitor.risk.center.server.StartApp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public class DetectorCommonComputeTest {

    @Resource
    private DetectorCommonCompute detectorCommonCompute;

    @MockBean
    private VehicleAdapter vehicleAdapter;

    @Resource
    private VehicleRuntimeInfoContextRepository contextRepository;

    @Test
    public void test() {

        VehicleRuntimeInfoContextDO runtimeInfoContextDO = VehicleRuntimeInfoContextDO.builder()
                .lat("22.689952317065718")
                .lng("114.03524750263816")
                .vin("LMTZSV027MC042469").build();

        // 模拟获取高精地图区域
        Mockito.doReturn("shenzhenlonghua").when(vehicleAdapter)
                .getVehicleHdMapArea(Mockito.anyString());

        // detectorCommonCompute.isInFCRouteLane(runtimeInfoContextDO);
    }

    @Test
    public void test1() {
        String vehicleRuntimeStr = "{\"fcLaneIdList\":[],\"pathOverlapWithConstructionZone\":false,\"obstacleAbstracts\":[],\"vehicleCounterInfo\":{\"redLightCounter\":{\"rule\":\"#context.findTrafficLightType()?.code == 1\",\"startTime\":\"2025-05-23 15:46:47\",\"endTime\":\"2025-05-23 15:46:59\",\"duration\":12,\"countFinished\":true},\"lowTrafficFlowSpeedCounter\":{\"rule\":\"#context.trafficFlowSpeed != null && #context.trafficFlowSpeed <= 0.0000036\",\"startTime\":\"2025-04-27 15:05:33\",\"duration\":2580365,\"countFinished\":false},\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-05-27 11:45:26\",\"duration\":372,\"countFinished\":false},\"greenLightCounter\":{\"rule\":\"#context.findTrafficLightType()?.code == 3\",\"startTime\":\"2025-05-23 15:46:59\",\"endTime\":\"2025-05-23 15:47:23\",\"duration\":24,\"countFinished\":true}},\"drivingOnTrafficLineType\":\"NONE\",\"speed\":0,\"distanceToJunction\":-1,\"refinedLineList\":[{\"latitude\":39.7393745,\"longitude\":116.4577274,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577274,39.7393745]},{\"latitude\":39.7393701,\"longitude\":116.4577285,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577285,39.7393701]},{\"latitude\":39.739189,\"longitude\":116.4577753,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577753,39.739189]},{\"latitude\":39.7390079,\"longitude\":116.4578224,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4578224,39.7390079]},{\"latitude\":39.7388268,\"longitude\":116.4578696,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4578696,39.7388268]},{\"latitude\":39.7386458,\"longitude\":116.457917,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.457917,39.7386458]},{\"latitude\":39.7384647,\"longitude\":116.4579639,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4579639,39.7384647]},{\"latitude\":39.7382836,\"longitude\":116.4580109,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4580109,39.7382836]},{\"latitude\":39.7381025,\"longitude\":116.4580579,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4580579,39.7381025]},{\"latitude\":39.7379258,\"longitude\":116.4581038,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4581038,39.7379258]}],\"bizStatus\":\"NO_BIZ_TRIP\",\"lat\":\"39.739248\",\"lng\":\"116.457758\",\"distanceToFrontConstructionZone\":-1,\"prePositionList\":[{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577575,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577575,39.739248]},\"time\":\"2025-05-27 11:45:13\",\"distance\":0.042752326156186196},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577575,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577575,39.739248]},\"time\":\"2025-05-27 11:45:14\",\"distance\":0.042752326156186196},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577575,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577575,39.739248]},\"time\":\"2025-05-27 11:45:15\",\"distance\":0.042752326156186196},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577575,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577575,39.739248]},\"time\":\"2025-05-27 11:45:16\",\"distance\":0.042752326156186196},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577575,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577575,39.739248]},\"time\":\"2025-05-27 11:45:17\",\"distance\":0.042752326156186196},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577575,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577575,39.739248]},\"time\":\"2025-05-27 11:45:18\",\"distance\":0.042752326156186196},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577575,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577575,39.739248]},\"time\":\"2025-05-27 11:45:19\",\"distance\":0.042752326156186196},{\"position\":{\"latitude\":39.7392481,\"longitude\":116.4577575,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577575,39.7392481]},\"time\":\"2025-05-27 11:45:20\",\"distance\":0.044174704204405925},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577574,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577574,39.739248]},\"time\":\"2025-05-27 11:45:21\",\"distance\":0.05130279090138576},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577574,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577574,39.739248]},\"time\":\"2025-05-27 11:45:22\",\"distance\":0.05130279090138576},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577574,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577574,39.739248]},\"time\":\"2025-05-27 11:45:23\",\"distance\":0.05130279090138576},{\"position\":{\"latitude\":39.7392481,\"longitude\":116.4577574,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577574,39.7392481]},\"time\":\"2025-05-27 11:45:24\",\"distance\":0.052493994445900305},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577574,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577574,39.739248]},\"time\":\"2025-05-27 11:45:25\",\"distance\":0.05130279090138576},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577574,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577574,39.739248]},\"time\":\"2025-05-27 11:45:26\",\"distance\":0.05130279090138576},{\"position\":{\"latitude\":39.739248,\"longitude\":116.4577574,\"coordinateSystem\":\"WGS84\",\"pointList\":[116.4577574,39.739248]},\"time\":\"2025-05-27 11:45:27\",\"distance\":0.05130279090138576}],\"routePoints\":[],\"monitorMetricsInfo\":{\"scene\":\"UNKNOWN_SCENE\",\"batterySwitching\":false,\"distanceToJunction\":\"Infinity\",\"junctionId\":\"\",\"inHomeArea\":false,\"queuing\":false},\"fenceContext\":{\"fenceAndFieldMetas\":[{\"perceptionId\":\"195802\",\"stopDistanceToEgo\":0.0000000000000002220446049250313,\"position\":{\"x\":453547.3042607906,\"y\":4398953.121786941},\"constraintSourceType\":\"STATIONARY\"},{\"perceptionId\":\"195802\",\"position\":{\"x\":453547.3042607906,\"y\":4398953.121786941},\"constraintSourceType\":\"STATIONARY\"}]},\"driveMode\":\"NO_CONTROL\",\"oppositeWithRoad\":false,\"obstacleContext\":{\"perceptionObstacle\":[{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.05190861869847496,\"y\":0.9986518388831515,\"z\":0},\"height\":1.1206040978431702,\"id\":\"217027\",\"length\":0.45789248682558537,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":453538.850887799,\"y\":4398947.052170549,\"z\":21.875921490876348},\"theta\":1.6227282847919746,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.006744537968188524},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.999995037829489,\"y\":-0.0031502883015741764,\"z\":0},\"height\":1.2370980381965637,\"id\":\"217000\",\"length\":0.5904959815670736,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_SIGN\"},\"position\":{\"x\":453536.90448405646,\"y\":4398944.278366958,\"z\":22.997983568339794},\"theta\":-0.003150293641404257,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.13814283069223166},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.15231994656781447,\"y\":0.9883312369206074,\"z\":0},\"height\":1.1464945673942566,\"id\":\"240682\",\"length\":1.8542767185717821,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":453539.2,\"y\":4398945.100000001,\"z\":21.87570520379866},\"theta\":1.7237115155483298,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.07564643956720829},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":0.6886329650878906,\"id\":\"200865\",\"length\":0.4001100957393646,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_CONE\"},\"position\":{\"x\":453551.38359258306,\"y\":4398950.579553528,\"z\":21.638563362456598},\"theta\":-2.9944147237882754,\"type\":\"TRAFFIC_CONE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.36006009578704834},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":0.8681790232658386,\"id\":\"195800\",\"length\":0.3643043637275696,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_CONE\"},\"position\":{\"x\":453549.13911634183,\"y\":4398948.977687382,\"z\":21.565931195460035},\"theta\":-2.9944147237882754,\"type\":\"TRAFFIC_CONE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.3297650218009949},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":1.0000686645507812,\"id\":\"196019\",\"length\":0.3555178940296173,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER\"},\"position\":{\"x\":453546.3881726144,\"y\":4398948.386123583,\"z\":21.502425225551097},\"theta\":-2.9944147237882754,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.32212603092193604},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":1.0162127017974854,\"id\":\"195802\",\"length\":0.4405090808868408,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_CONE\"},\"position\":{\"x\":453547.8966815849,\"y\":4398948.643994832,\"z\":21.527217061709653},\"theta\":-2.9944147237882754,\"type\":\"TRAFFIC_CONE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.39122337102890015},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.11601881421002651,\"y\":0.9932470159763537,\"z\":0},\"height\":1.7887964248657227,\"id\":\"195900\",\"length\":4.879100799560547,\"obstacleType\":{\"coarseType\":\"CAR\",\"fineType\":\"FINE_CAR\"},\"position\":{\"x\":453533.9273195877,\"y\":4398946.52766323,\"z\":22.0282509254085},\"theta\":1.6870770070352463,\"type\":\"VEHICLE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":2.0974507331848145},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":0.8620538711547852,\"id\":\"195801\",\"length\":0.45895248651504517,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_CONE\"},\"position\":{\"x\":453542.844190666,\"y\":4398947.766752593,\"z\":21.605406192121077},\"theta\":-2.9944147237882754,\"type\":\"TRAFFIC_CONE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.4110007882118225},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.63312197000564,\"y\":0.7740520467594102,\"z\":0},\"height\":1.130730390548706,\"id\":\"217918\",\"length\":0.8402361869812012,\"obstacleType\":{},\"position\":{\"x\":453525.73333333334,\"y\":4398958.816666667,\"z\":21.369616728778617},\"theta\":0.8852164517984995,\"type\":\"PEDESTRIAN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.7674757838249207},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.001537043818623172,\"y\":-0.9999988187453811,\"z\":0},\"height\":1.746997058391571,\"id\":\"249764\",\"length\":0.36265583615750074,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":453533.9992617459,\"y\":4398970.183714824,\"z\":22.157543827081067},\"theta\":-1.5692592841746504,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.2018299278570339},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.989223604098544,\"y\":0.14641263979166447,\"z\":0},\"height\":0,\"id\":\"216905\",\"length\":7.199618634302169,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_CONSTRUCTION_ZONE\"},\"position\":{\"x\":453546.50472709455,\"y\":4398948.720689501,\"z\":21.77031426180058},\"theta\":0.14694085175876223,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.5999381681904197},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.06041434727598836,\"y\":-0.9981733850577662,\"z\":0},\"height\":1.1860685348510742,\"id\":\"215710\",\"length\":0.47049362678080797,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":453538.45163111325,\"y\":4398949.051495306,\"z\":21.828301551876688},\"theta\":-1.5103451663291545,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.02015679737087339},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.19505029774854435,\"y\":0.9807932408757895,\"z\":0},\"height\":1.2482250928878784,\"id\":\"249907\",\"length\":3.9418263072147965,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":453538.5423052261,\"y\":4398947.788437601,\"z\":21.87156179321929},\"theta\":1.7671050595460773,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.21643069200217724},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9858615229713511,\"y\":0.16756210050089082,\"z\":0},\"height\":0.7166550159454346,\"id\":\"249989\",\"length\":0.337740650982596,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":453549.1619538983,\"y\":4398965.680769961,\"z\":21.966405145643144},\"theta\":2.973236369465341,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.1486317701637745},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9953842269992018,\"y\":-0.0959699986404969,\"z\":0},\"height\":1.5826115608215332,\"id\":\"217052\",\"length\":0.31143393146339804,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"516\"},\"position\":{\"x\":453537.3554662178,\"y\":4398944.254956134,\"z\":22.819512720738345},\"theta\":-3.0454747217953377,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.10029950272291899},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.19851496172253216,\"y\":-0.9800978573448085,\"z\":0},\"height\":1.1351688504219055,\"id\":\"249902\",\"length\":5.047376078553498,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":453537.25545638986,\"y\":4398955.811647529,\"z\":21.955586127202707},\"theta\":-1.370953832388323,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.28463904187083244},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.24316122577511665,\"y\":0.9699858856072279,\"z\":0},\"height\":0.5013231039047241,\"id\":\"249992\",\"length\":0.38443027809262276,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":453528.85887170595,\"y\":4398946.401720143,\"z\":21.64321682746631},\"theta\":1.3251727576868537,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.12925942125730217},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":1,\"y\":0,\"z\":0},\"height\":0.6606495380401611,\"id\":\"249991\",\"length\":0.20000000001164153,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":453528.6,\"y\":4398944.9,\"z\":21.62658362113589},\"theta\":0,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":1.2000000001862645},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":0.6090532541275024,\"id\":\"249771\",\"length\":0.34898218512535095,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_CONE\"},\"position\":{\"x\":453550.7969574273,\"y\":4398949.807138546,\"z\":21.61046566518157},\"theta\":-2.9944147237882754,\"type\":\"TRAFFIC_CONE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.3186841607093811},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":0.897489070892334,\"id\":\"195799\",\"length\":0.425291508436203,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_CONE\"},\"position\":{\"x\":453544.1866632087,\"y\":4398947.990973745,\"z\":21.597060255997892},\"theta\":-2.9944147237882754,\"type\":\"TRAFFIC_CONE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.38328197598457336},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":0.8956285715103149,\"id\":\"195879\",\"length\":0.4538270831108093,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_CONE\"},\"position\":{\"x\":453545.2192085344,\"y\":4398948.040894804,\"z\":21.544527172527307},\"theta\":-2.9944147237882754,\"type\":\"TRAFFIC_CONE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.41120150685310364},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.3807498040580931,\"y\":0.9246780989661805,\"z\":0},\"height\":0.8837923407554626,\"id\":\"249974\",\"length\":3.938040843233466,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":453547.38313518185,\"y\":4398968.169517768,\"z\":21.882823830136964},\"theta\":1.961403370575402,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.794135311152786},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.0026109172522406515,\"y\":-0.9999965915495018,\"z\":0},\"height\":1.1372761726379395,\"id\":\"250034\",\"length\":0.7307623326778412,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":453537.8502376022,\"y\":4398952.378602281,\"z\":21.91818218299516},\"theta\":-1.5681854072773216,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.029552365595009178},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.0026758225829520144,\"y\":-0.999996419980166,\"z\":0},\"height\":1.2235117554664612,\"id\":\"250039\",\"length\":1.0978860864415765,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":453537.60000000003,\"y\":4398952.8,\"z\":21.851211228367934},\"theta\":-1.5681205016238804,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.22346286929678172},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.8944969091773091,\"y\":0.44707413196484264,\"z\":0},\"height\":1.341875433921814,\"id\":\"214517\",\"length\":0.6365281762555242,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"516\"},\"position\":{\"x\":453535.07999984385,\"y\":4398955.939998486,\"z\":22.69577537187903},\"theta\":0.46349169046342814,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.2730867569334805},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":0.8722155690193176,\"id\":\"196047\",\"length\":0.4292554557323456,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_CONE\"},\"position\":{\"x\":453539.9785441063,\"y\":4398947.006345934,\"z\":21.61613732322144},\"theta\":-2.9944147237882754,\"type\":\"TRAFFIC_CONE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.3898884952068329},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.9891888649895764,\"y\":-0.14664715946618048,\"z\":0},\"height\":0.9749289751052856,\"id\":\"196046\",\"length\":0.4221879541873932,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_TRAFFIC_CONE\"},\"position\":{\"x\":453541.3429944407,\"y\":4398947.243670195,\"z\":21.611647631758238},\"theta\":-2.9944147237882754,\"type\":\"TRAFFIC_CONE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.3808567523956299},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":-0.0460784336453826,\"y\":0.9989378248683823,\"z\":0},\"height\":1.0448409914970398,\"id\":\"211964\",\"length\":0.40161109901964664,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":453538.6502549715,\"y\":4398948.05458531,\"z\":21.896718757304445},\"theta\":1.6168910819485043,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.00680341140832752},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.2680122539464176,\"y\":-0.963415503160391,\"z\":0},\"height\":1.308407038450241,\"id\":\"249470\",\"length\":8.971578356809914,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":453537.11176269245,\"y\":4398954.152921688,\"z\":21.8793413272574},\"theta\":-1.2994671193168532,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":1.3163941293023527}]},\"distanceToNextJunction\":-1,\"lastUpdateTime\":\"2025-05-27 11:51:46\"}";
        VehicleRuntimeInfoContextDO runtimeInfoContextDO = JacksonUtils.from(vehicleRuntimeStr,
                VehicleRuntimeInfoContextDO.class);
        Mockito.doReturn(VehicleEveInfoVTO.builder().hdMapVersion("yizhuang_123123").build()).when(vehicleAdapter)
                .queryRuntimeVehicleInfoByVin(Mockito.anyString());
        String dataList = "[{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:13\",\"longitude\":116.4577575,\"latitude\":39.739248,\"drive_status_enum\":6,\"drive_status\":\"人驾状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:14\",\"longitude\":116.4577575,\"latitude\":39.739248,\"drive_status_enum\":6,\"drive_status\":\"人驾状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:15\",\"longitude\":116.4577575,\"latitude\":39.739248,\"drive_status_enum\":6,\"drive_status\":\"人驾状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:16\",\"longitude\":116.4577575,\"latitude\":39.739248,\"drive_status_enum\":6,\"drive_status\":\"人驾状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:17\",\"longitude\":116.4577575,\"latitude\":39.739248,\"drive_status_enum\":6,\"drive_status\":\"人驾状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:18\",\"longitude\":116.4577575,\"latitude\":39.739248,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:19\",\"longitude\":116.4577575,\"latitude\":39.739248,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:20\",\"longitude\":116.4577575,\"latitude\":39.7392481,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:21\",\"longitude\":116.4577574,\"latitude\":39.739248,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:22\",\"longitude\":116.4577574,\"latitude\":39.739248,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:23\",\"longitude\":116.4577574,\"latitude\":39.739248,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:24\",\"longitude\":116.4577574,\"latitude\":39.7392481,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:25\",\"longitude\":116.4577574,\"latitude\":39.739248,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:26\",\"longitude\":116.4577574,\"latitude\":39.739248,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB18R0515969\",\"time\":\"2025-05-27 11:45:27\",\"longitude\":116.4577574,\"latitude\":39.739248,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"}]";
        List<VehicleDataInfoVO> vehicleDataInfoVOS = JacksonUtils.from(dataList,
                new TypeReference<List<VehicleDataInfoVO>>() {});
        Mockito.doReturn(vehicleDataInfoVOS).when(vehicleAdapter)
                .queryVehicleHistoryDataFromEveReplay(Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong());
        detectorCommonCompute.buildVehicleLaneAndPosition(runtimeInfoContextDO);
    }

    @Test
    public void test2() {
        // 1. 创建测试所需的上下文数据
        VehicleRuntimeInfoContextDO runtimeInfoContextDO = VehicleRuntimeInfoContextDO.builder()
                .lat("22.689952317065718")
                .lng("114.03524750263816")
                .vin("LMTZSV028NC008087")
                .build();

        // 2. 创建障碍物信息 - 根据 PerceptionObstacleDTO 格式创建
        String perceptionObstacleDTOStr = "{\"perceptionObstacle\":[{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.01565505911712093,\"y\":0.9998774520529039,\"z\":0},\"height\":0.0005221366882324219,\"id\":\"4770312\",\"length\":0.2864589130049797,\"obstacleType\":{\"coarseType\":\"CAR\",\"fineType\":\"FINE_CAR\"},\"position\":{\"x\":461256.93584242987,\"y\":4437531.220559789,\"z\":21.57869356555014},\"theta\":1.5551406276576785,\"type\":\"VEHICLE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.06872160272159533}]}";
        VehicleObstacleContextDO vehicleObstacleContextDO = JacksonUtils.from(perceptionObstacleDTOStr,
                VehicleObstacleContextDO.class);

        // 设置障碍物位置为接近车辆的位置，确保可以被检测到
        PositionDO perceptionObstaclePosition = GeoToolsUtil.wgs84ToUtm(114.02129395039469, 22.72636473217512);
        vehicleObstacleContextDO.getPerceptionObstacle().get(0).setPosition(Position.builder()
                .x(perceptionObstaclePosition.getLongitude())
                .y(perceptionObstaclePosition.getLatitude())
                .build());
        runtimeInfoContextDO.setObstacleContext(vehicleObstacleContextDO);

        LocalDateTime localDateTime = LocalDateTime.parse("2025-04-15 14:29:06",
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

        // 5. 创建风险检测记录
        RiskDetectorRecordBaseDO recordBaseDO = RiskDetectorRecordBaseDO.builder()
                .tmpCaseId("test-case-id")
                .occurTime(date)
                .vin(runtimeInfoContextDO.getVin())
                .build();

        // 6. 使用 Mockito 模拟依赖服务
        // 模拟获取高精地图区域
        Mockito.doReturn("shenzhenlonghua").when(vehicleAdapter)
                .getVehicleHdMapArea(Mockito.anyString());

        // 模拟车辆历史数据，用于确定行驶方向
        Mockito.doReturn(java.util.Collections.singletonList(
                com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.response.VehicleDataInfoVO.builder()
                        .vin(runtimeInfoContextDO.getVin())
                        .latitude(Double.parseDouble(runtimeInfoContextDO.getLat()) - 0.0001) // 稍微偏移位置来模拟运动
                        .longitude(Double.parseDouble(runtimeInfoContextDO.getLng()) - 0.0001)
                        .build()
        )).when(vehicleAdapter).queryVehicleHistoryDataFromEveReplay(Mockito.any(), Mockito.any(), Mockito.any());

        // 7. 调用被测试方法
        // Boolean hasObstacle = detectorCommonCompute.isHasObstacle(runtimeInfoContextDO, recordBaseDO);

        // 8. 验证结果
        // log.info("检测结果: 是否存在障碍物 = {}", hasObstacle);

        // 根据实际配置和预期结果添加断言
        // org.junit.Assert.assertEquals(true/false, hasObstacle);
    }

    @Test
    public void test3() {
        VehicleRuntimeInfoContextDO runtimeInfoContextDO = VehicleRuntimeInfoContextDO.builder()
                .lat("22.689952317065718")
                .lng("114.03524750263816")
                .vin("LMTZSV027MC042469").build();

        // 模拟获取高精地图区域
        Mockito.doReturn("shenzhenlonghua").when(vehicleAdapter)
                .getVehicleHdMapArea(Mockito.anyString());

        // Boolean result = detectorCommonCompute.isCrossLine(runtimeInfoContextDO);
        // log.info("检测结果: 是否存在交叉 = {}", result);
    }
}
