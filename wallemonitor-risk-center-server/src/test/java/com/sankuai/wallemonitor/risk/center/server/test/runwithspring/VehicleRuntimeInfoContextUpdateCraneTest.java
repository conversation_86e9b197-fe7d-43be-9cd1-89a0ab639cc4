package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficData;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficData.TrafficHmiSignal.HmiSignal;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.server.consumer.TrafficSignalConsumer;
import com.sankuai.wallemonitor.risk.center.server.crane.RiskCaseDisposedInfoUpdateCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.VehicleRuntimeInfoContextUpdateCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.VehicleRuntimeInfoContextUpdateFromDataBusCrane;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Date;
import java.util.Optional;
import java.util.Set;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class VehicleRuntimeInfoContextUpdateCraneTest extends SpringTestBase {

    @Resource
    private VehicleRuntimeInfoContextUpdateCrane vehicleRuntimeInfoContextUpdateCrane;

    @Resource
    private VehicleRuntimeInfoContextUpdateFromDataBusCrane vehicleRuntimeInfoContextUpdateFromDataBusCrane;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private TrafficSignalConsumer trafficSignalConsumer;

    @Resource
    private RiskCaseDisposedInfoUpdateCrane riskCaseDisposedInfoUpdateCrane;

    @Test
    @SneakyThrows
    public void testUpdate() {
        TimePeriod lastUpdateTimeRange = TimePeriod.builder()
                .beginDate(DatetimeUtil.getNSecondsBeforeDateTime(CommonConstant.SECONDS_PER_DAY)).endDate(new Date())
                .build();
        Set<String> lastUpdatedVin = vehicleRuntimeInfoContextRepository.queryFromCache(lastUpdateTimeRange);
        lastUpdatedVin.forEach((vin) -> {
            VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = Optional
                    .ofNullable(vehicleRuntimeInfoContextRepository.getByVin(vin))
                    .orElseGet(() -> vehicleRuntimeInfoContextRepository.getFromCache(vin));
            vehicleRuntimeInfoContextDO.setSpeed(0.0);
            vehicleRuntimeInfoContextRepository.save(vehicleRuntimeInfoContextDO);
            vehicleRuntimeInfoContextRepository.updateCache(vehicleRuntimeInfoContextDO, System.currentTimeMillis());
        });

    }

    @Test
    @SneakyThrows
    public void testWhenUpdated() {
        String trafficData = "{\"timestamp\":1729075187773,\"trafficHmiSignal\":{\"currentReflineId\":\"1\",\"targetReflineId\":\"1\"},\"vin\":\"LMTZSV028NC050243\"}";
        String trafficData2 = "{\"containLights\":true,\"timestamp\":1731392277124,\"trafficLightList\":[{\"bulbs\":[{\"color\":\"GREEN\",\"type\":\"CIRCLE\"}],\"color\":\"GREEN\",\"criticalLevel\":\"TrafficLightCriticalLevel_HIGH\",\"id\":\"s_cc570cf82135\",\"isBlinking\":false,\"source\":\"DETECTION\",\"status\":\"NORMAL\"}],\"vin\":\"LMTZSV028NC050243\"}";
        TrafficData trafficData1 = JacksonUtils.from(trafficData, TrafficData.class);
        TrafficData trafficData2Obj = JacksonUtils.from(trafficData2, TrafficData.class);
        trafficData1.setTimestamp(System.currentTimeMillis());
        trafficData2Obj.setTimestamp(System.currentTimeMillis());
        trafficData1.getTrafficHmiSignal().setHmiSignal(HmiSignal.builder().finalSignalId("s_cc570cf82135").build());
        trafficSignalConsumer.receive(JacksonUtils.to(trafficData1));
        trafficSignalConsumer.receive(JacksonUtils.to(trafficData2Obj));
        vehicleRuntimeInfoContextUpdateCrane.run();

    }

    @Test
    @SneakyThrows
    public void testGetEveInfoUpdate() {
        vehicleRuntimeInfoContextUpdateFromDataBusCrane.run();
    }

    @Test
    @SneakyThrows
    public void testRiskCaseDisposedInfoUpdate() {
        riskCaseDisposedInfoUpdateCrane.run();
    }

}