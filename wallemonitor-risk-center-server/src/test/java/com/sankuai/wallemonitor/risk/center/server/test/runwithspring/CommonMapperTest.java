package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCase;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.junit.Test;

public class CommonMapperTest extends SpringTestBase {

    @Resource
    private CommonMapper<RiskCase> commonMapper;

    @Resource
    private RiskCaseRepository repository;

    @Test
    public void testSelectById() {
        RiskCase riskCase = commonMapper
                .selectByUk(Lists.newArrayList(UniqueKeyDTO.builder().columnPOName("caseId").value("1231312").build()));
        System.out.println(riskCase);
        repository.getByCaseId("123123");
    }

}