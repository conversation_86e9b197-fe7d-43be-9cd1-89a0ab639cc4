package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.sankuai.walleeve.commons.exception.SystemException;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.response.AdminGetRiskCaseDetailResponse;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListRiskCaseVO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISAiFridayClassify.FridayCheckActionConfig;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.FridayOpenAiAdapter;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.FridayConfig;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamWithMessageVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.FridayVerifyResultVTO;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

@Slf4j
public class FridayOpenAiAdapterSmpleTest {

    private FridayConfig config = new FridayConfig();


    private RiskCaseAdapter riskCaseAdapter = new RiskCaseAdapter();

    //accessToken
    private static final String accessToken = "eAGFzitLQ1EcAHAOkzGWxpLxBsO2IPf8z9uke2H0hYJF7nkZhC1cwbLgVkRFFJMaRGVoEBQFwWkwaRgY_BC6mQ0GQcRg9gv8-KVQpvvykQjWOjdP9wBp6hVzJLTejQQSC8488ZIKRo3A2gIuOBWFwlvsAYpfKJufc3rauJprlHmlikuKVMuyyLjAkpWKIEGpEha0GlaCncPnzQfIIfgXlr-l0eT459Z79w4m9ravWx1ooUI6OTlVqluXzb62z_pXx2_r572jZv-i3b9sDg4Eq7f7-dzjd--0A7so9Rc7QMORUJhgIF4Jpx3m1AD12klNlZBe6AUsCAEFXBBG2QkaWnG6QSEykY-YAm2pUFRaziIM1nDLFAY_H3gwhHPiaVhQlDqtSSik00oKriMc6hbKLLrlMWNcHM_Ul1xtFjZQIo7rP79qegc**eAEFwQEBwCAMAzBLwMY35JS2-JfwZDbgVi1ipIKRd25ueZ0O1aO93Dc5cpalOokrwiyMD_oBX3ITJw**ghX0A7cVka4IZENrX95-vt3j3seC9CwYxAl9-8jeWsN7l9PfNtCN6ncLYjyZ8Yt-9Y6DDCS2zrVTbY8dgPXYMQ**ODE4NjA1OCxsaWFvbGluZ2ZlaSzlu5blh4zpo54sbGlhb2xpbmdmZWlAbWVpdHVhbi5jb20sMSwzNDAxNDkxMywxNzMzOTcyMjg2NTk5";

    @Test
    public void testCase() {
        FridayOpenAiAdapter adapter = new FridayOpenAiAdapter();
        List<String> caseIdList = Arrays.stream(getFromFile("case.txt").split("\n")).map(String::trim).collect(
                Collectors.toList());
        Map<String, AdminGetRiskCaseDetailResponse> riskCaseDetailResponseMap = riskCaseAdapter.getByCaseIdList(
                caseIdList, accessToken);
        FridayCheckActionConfig fridayCheckActionConfig = new FridayCheckActionConfig();
        fridayCheckActionConfig.setStepPrompt(getFromFile("step.txt"));
        fridayCheckActionConfig.setUserPrompt(getFromFile("user.txt"));
        fridayCheckActionConfig.setSystemPrompt(getFromFile("system.txt"));

        List<FridayModelParamWithMessageVTO> fridayModelParamWithMessageVTOS = build(caseIdList,
                riskCaseDetailResponseMap, fridayCheckActionConfig);
        Map<String, FridayVerifyResultVTO> fridayVerifyResultVTOMap = ParallelExecutor.executeParallelTasksAndGetResult(
                        "is_check_action", fridayModelParamWithMessageVTOS, adapter::verify).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(FridayVerifyResultVTO::getCaseId, Function.identity(), (o1, o2) -> o1));

        Map<String, Map<String, AdminGetRiskCaseDetailResponse>> riskCaseDetailResponseMapGroupBy = riskCaseDetailResponseMap.values()
                .stream()
                .collect(Collectors.groupingBy(r -> r.getBase().getCaseId(),
                        Collectors.toMap(r -> r.getBase().getCaseId(), Function.identity(), (o, n) -> n)));
        riskCaseDetailResponseMapGroupBy.forEach((key1, value1) -> value1.forEach((key, value) -> {
            FridayVerifyResultVTO vto = fridayVerifyResultVTOMap.get(key);
            log.info("caseId:{},activity:{},category:{},platform:{},信息:{}", key,
                    Optional.ofNullable(vto).map(FridayVerifyResultVTO::getActivity).orElse(""),
                    value.getMarkInfo().getSubCategory(),
                    "'" + "https://eve.meituan.com/fe-panel-risk/index.html#/risk/caseList?&openCaseId="
                            + key + "'",
                    Optional.ofNullable(vto).map(FridayVerifyResultVTO::getAllInfo).orElse(""));
        }));

    }

    @Test
    public void testCaseRound() {
        FridayOpenAiAdapter adapter = new FridayOpenAiAdapter();
        List<String> caseIdList = Arrays.stream(getFromFile("new/case.txt").split("\n")).map(String::trim).collect(
                Collectors.toList());
        Map<String, AdminGetRiskCaseDetailResponse> riskCaseDetailResponseMap = riskCaseAdapter.getByCaseIdList(
                caseIdList, accessToken);
        FridayCheckActionConfig fridayCheckActionConfig = new FridayCheckActionConfig();
        fridayCheckActionConfig.setSystemPrompt(getFromFile("new/system.txt"));
        fridayCheckActionConfig.setUserPrompt(getFromFile("new/user.txt"));
        List<FridayModelParamVTO> fridayMessageVTOS = buildByRound(caseIdList,
                riskCaseDetailResponseMap, fridayCheckActionConfig);
        Map<String, FridayVerifyResultVTO> fridayVerifyResultVTOMap = ParallelExecutor.executeParallelTasksAndGetResult(
                        "is_check_action", fridayMessageVTOS,
                        (fridayModelParamVTO) -> adapter.verifyByMultiRound(fridayModelParamVTO, (openApiContext) -> {
                            String path = String.format("new/round%d/step.txt", openApiContext.getTimes());
                            //取次数
                            Integer times = openApiContext.getTimes();
                            switch (times) {
                                case 1:
                                case 2:
                                case 3: {
                                    fridayCheckActionConfig.setStepPrompt(getFromFile(path));
                                    return fridayCheckActionConfig.getStepPrompt();
                                }
                                default: {
                                    return new ArrayList<>();
                                }
                            }
                        })).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(FridayVerifyResultVTO::getCaseId, Function.identity(), (o1, o2) -> o1));
        Map<String, Map<String, AdminGetRiskCaseDetailResponse>> riskCaseDetailResponseMapGroupBy = riskCaseDetailResponseMap.values()
                .stream()
                .collect(Collectors.groupingBy(r -> r.getBase().getCaseId(),
                        Collectors.toMap(r -> r.getBase().getCaseId(), Function.identity(), (o, n) -> n)));
        riskCaseDetailResponseMapGroupBy.forEach((key1, value1) -> value1.forEach((key, value) -> {
            FridayVerifyResultVTO vto = fridayVerifyResultVTOMap.get(key);
            log.info("caseId:{},activity:{},category:{},platform:{},信息:{}", key,
                    Optional.ofNullable(vto).map(FridayVerifyResultVTO::getActivity).orElse(""),
                    value.getMarkInfo().getSubCategory(),
                    "'" + "https://eve.meituan.com/fe-panel-risk/index.html#/risk/caseList?&openCaseId="
                            + key + "'",
                    Optional.ofNullable(vto).map(FridayVerifyResultVTO::getAllInfo).orElse(""));
        }));


    }

    /**
     * 从resources目录下获取case文件的内容,每行是一个caseId
     *
     * @return
     */
    private String getFromFile(String fileName) {
        try {
            ClassPathResource resource = new ClassPathResource(fileName);
            return FileCopyUtils.copyToString(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("读取case文件失败", e);
            return "";
        }
    }

    private List<FridayModelParamWithMessageVTO> build(List<String> list,
            Map<String, AdminGetRiskCaseDetailResponse> caseDetailResponseMap,
            FridayCheckActionConfig fridayCheckActionConfig) {
        return list.stream().filter(caseDetailResponseMap::containsKey).map(caseId -> {
            Date oTime = DatetimeUtil.convertDatetimeStr2Date(
                    caseDetailResponseMap.get(caseId).getBase().getOccurTime());
            String vin = caseDetailResponseMap.get(caseId).getVehicleList().get(0).getVin();
            FridayModelParamWithMessageVTO paramVTO = FridayModelParamWithMessageVTO.builder()
                    .caseId(caseId)
                    //这里取当前的检测时间
                    .occurTime(oTime)
                    .vin(vin)
                    .userStepPrompt(fridayCheckActionConfig.getStepPrompt())
                    .userPrompt(fridayCheckActionConfig.getUserPrompt(vin, oTime, config.getBeforeTime()))
                    .systemPrompt(fridayCheckActionConfig.getSystemPrompt())
                    .modelName(config.getModelName())
                    .appId(config.getAppId())
                    .timeout(config.getTimeoutSecond())
                    .build();
            return paramVTO;
        }).collect(Collectors.toList());
    }

    public List<FridayModelParamVTO> buildByRound(List<String> list,
            Map<String, AdminGetRiskCaseDetailResponse> caseDetailResponseMap,
            FridayCheckActionConfig fridayCheckActionConfig) {
        return list.stream().filter(caseDetailResponseMap::containsKey).map(caseId -> {
            Date oTime = DatetimeUtil.convertDatetimeStr2Date(
                    caseDetailResponseMap.get(caseId).getBase().getOccurTime());
            String vin = caseDetailResponseMap.get(caseId).getVehicleList().get(0).getVin();
            FridayModelParamVTO paramVTO = FridayModelParamVTO.builder()
                    .caseId(caseId)
                    //这里取当前的检测时间
                    .occurTime(oTime)
                    .vin(vin)
                    .systemPrompt(fridayCheckActionConfig.getSystemPrompt())
                    .userPrompt(fridayCheckActionConfig.getUserPrompt(vin, oTime, 1))
                    .modelName(config.getModelName())
                    .appId(config.getAppId())
                    .timeout(config.getTimeoutSecond())
                    .build();
            return paramVTO;
        }).collect(Collectors.toList());
    }


    /**
     * case请求
     */
    @Component
    @Slf4j
    public static class RiskCaseAdapter {

        private static final String CASE_URL = "https://eve.meituan.com/risk/api/admin/listCase";

        private static final String CASE_DETAIL_URL = "https://eve.meituan.com/risk/api/admin/getCaseDetail?caseId=";


        /**
         * 查询风险case
         *
         * @return
         */
        public Page<AdminListRiskCaseVO> queryRiskCaseList(String jsonParam, Integer pageNum, Integer pageSize,
                String accessToken) {
            try {
                Map<String, Object> postBody = JacksonUtils.fromMap(jsonParam);
                addPageNums(postBody, pageNum, pageSize);
                EveHttpResponse<EveThriftPageResponse<List<AdminListRiskCaseVO>>> response = HttpUtils.postJson(
                        JacksonUtils.to(postBody), CASE_URL, "",
                        buildHeader(accessToken),
                        new TypeReference<EveThriftPageResponse<List<AdminListRiskCaseVO>>>() {
                        });
                if (response == null || response.getData() == null || response.getCode() != HttpStatus.OK.value()) {
                    log.error(JacksonUtils.to(response), new SystemException("查询风险case失败"));
                }
                EveThriftPageResponse<List<AdminListRiskCaseVO>> eveThriftPageResponse = response.getData();
                Page<AdminListRiskCaseVO> page = new Page<>();
                long totalRecords = eveThriftPageResponse.getPaging().getTotal();
                int totalPages = eveThriftPageResponse.getPaging().getTotalPages();
                page.setPages(totalPages);
                page.pageNum(eveThriftPageResponse.getPaging().getPageNum());
                page.pageSize(pageSize);
                page.setTotal(totalRecords);
                page.addAll(eveThriftPageResponse.getData());
                return page;
            } catch (Exception e) {
                log.error("请求风险列表查询", e);
            }
            return null;
        }

        public AdminGetRiskCaseDetailResponse getByCaseId(String caseId, String accessToken) {
            try {
                EveHttpResponse<EveThriftResponse<AdminGetRiskCaseDetailResponse>> response = HttpUtils.get(
                        new HashMap<>(),
                        CASE_DETAIL_URL + caseId,
                        buildHeader(accessToken),
                        new TypeReference<EveThriftResponse<AdminGetRiskCaseDetailResponse>>() {
                        });
                if (Objects.isNull(response) || Objects.isNull(response.getData())
                        || response.getCode() != HttpStatus.OK.value()) {
                    log.error("caseId:" + caseId, new SystemException("获取单个case异常"));
                }
                return response.getData().getData();
            } catch (Exception e) {
                log.error("获取单个case异常,caseId:" + caseId, new SystemException("获取单个case异常"));
            }
            return null;
        }

        public Map<String, AdminGetRiskCaseDetailResponse> getByCaseIdList(List<String> caseIdList,
                String accessToken) {
            return ParallelExecutor.executeParallelTasksAndGetResult("is_check_action", caseIdList,
                            (caseId) -> getByCaseId(caseId, accessToken)).stream()
                    .collect(Collectors.toMap(r -> r.getBase().getCaseId(), Function.identity(), (v1, v2) -> v1));

        }

        /**
         * 构建header
         *
         * @return
         */
        private Map<String, String> buildHeader(String accessToken) {
            Map<String, String> header = new HashMap<>();
            header.put("access-token", accessToken);
            return header;
        }

        /**
         * 构建请求体
         *
         * @param pageNumber
         * @param pageSize
         * @return
         */

        private Map<String, Object> addPageNums(Map<String, Object> postBody, Integer pageNumber,
                Integer pageSize) {
            postBody.put("pageNum", pageNumber);
            postBody.put("pageSize", pageSize);//每100个查询
            return postBody;

        }


    }

}