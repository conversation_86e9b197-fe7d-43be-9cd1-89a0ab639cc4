package com.sankuai.wallemonitor.risk.center.server.test.unit.utiltest;

import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import org.junit.Test;

import java.util.Date;
import java.util.concurrent.TimeUnit;

public class DateTimeUtilsTest {

    @Test
    public void testDateAfter() {
        Date endDate = new Date();
        Date beforeDate = DatetimeUtil.getBeforeTime(endDate, TimeUnit.DAYS, 1);

        boolean after = DatetimeUtil.isAfter(endDate, beforeDate);

        System.out.println(after);
    }
}
