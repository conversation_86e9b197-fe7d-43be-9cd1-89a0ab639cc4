package com.sankuai.wallemonitor.risk.center.server.test.unit.consumer;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mtrace.instrument.util.JsonUtil;
import com.meituan.xframe.config.vo.ConfigChangedEvent;
import com.sankuai.walledelivery.sample.message.MqCommonMessage;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.server.consumer.RiskCaseEventConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.DataTestBase;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

public class RiskCaseEventConsumerTest extends ServiceTestBase {

    @InjectMocks
    public RiskCaseEventConsumer riskCaseEventConsumer;

    private String message = "{\n"
            + "    \"type\": 20,\n"
            + "    \"body\": {\n"
            + "        \"eventId\": \"1719191170_LMTZSV026MC093297\",\n"
            + "        \"source\": 3,\n"
            + "        \"type\": 1,\n"
            + "        \"status\": 10,\n"
            + "        \"vinList\": [\n"
            + "            \"LMTZSV026MC093297\"\n"
            + "        ],\n"
            + "        \"traceId\": \"\"\n"
            + "    },\n"
            + "    \"timestamp\": 1719191170850\n"
            + "}";
    private String s_end = "{\"type\":20,\"body\":{\"eventId\":\"1719191170_LMTZSV026MC093297\",\"source\":3,\"type\":1,\"status\":30,\"vinList\":[\"LMTZSV026MC093297\"],\"traceId\":\"\"},\"timestamp\":1719191170850}";

    @Before
    public void setUp() {
        // 初始化数据
        DataTestBase dataTestBase = new DataTestBase();

        // RiskCase工厂模式代码初始化
        RiskCaseFactory.idGenerateRepository = idGenerateRepository;

        // mock 分布式锁
        mockRedisAndLock();

        // mock idGenerateRepository
        mockIdGenerateRepository();

        ConfigChangedEvent configChangedEvent = new ConfigChangedEvent();
        configChangedEvent.setValue(dataTestBase.getCaseBroadCaseStrategyConfig());
        lionConfigRepository.configListener(configChangedEvent);

        // mock 消息推送配置
        Mockito.when(lionConfigRepository.getCaseType2BroadCastStrategyConfig()).thenReturn(dataTestBase.map);
        Mockito.when(lionConfigRepository.getByCaseType(any(RiskCaseTypeEnum.class))).thenAnswer(invocation -> {
            RiskCaseTypeEnum riskCaseTypeEnum = invocation.getArgument(0);
            return dataTestBase.map.get(riskCaseTypeEnum);
        });

        // mock 查询车辆信息，模拟根据list参数中的列表来查询已有数据中的符合条件的数据
        Mockito.when(vehicleInfoRepository.queryByVinList(anyList())).thenAnswer(
                invocation -> {
                    List<String> vinlist = invocation.getArgument(0);
                    return dataTestBase.vehicleInfoDOList.stream().filter(
                            v -> vinlist.contains(v.getVin())
                    ).collect(Collectors.toList());

                });
        Mockito.when(vehicleInfoRepository.getByVin(anyString())).thenAnswer(
                invocation -> {
                    String vin = invocation.getArgument(0);
                    return dataTestBase.vehicleInfoDOList.stream().filter(
                            v -> vin.equals(v.getVin())
                    ).findFirst().orElse(null);
                }
        );
        Mockito.when(vehicleRuntimeInfoContextRepository.getFromCache(anyList()))
                .thenAnswer(invocation -> new ArrayList<VehicleRuntimeInfoContextDO>());

        // mock 根据经纬度查询地理信息
        Mockito.when(gisInfoRepository.queryByPosition(any(PositionDO.class))).thenAnswer(invocation -> {
            PositionDO position = invocation.getArgument(0);
            return dataTestBase.gisInfoDOList.stream().filter(gisInfo -> {
                return Objects.equals(gisInfo.getLatitude(), position.getLatitude()) && Objects.equals(
                        gisInfo.getLongitude(), position.getLongitude());
            }).findFirst().orElse(null);
        });

    }

    /**
     * 测试消息为null的情况
     * 结果是空指针异常返回失败
     */
    @Test
    public void testReceiveMessageIsNull() {
        assertEquals(ConsumeStatus.CONSUME_FAILURE, riskCaseEventConsumer.receive(null));
        Mockito.verify(riskCaseRepository, Mockito.times(0)).save(any(RiskCaseDO.class));
        Mockito.verify(riskCaseVehicleRelationRepository, Mockito.times(0)).batchSave(anyList());
    }

    /**
     * 测试消息为空字符串的情况
     * 结果是空指针异常返回失败
     */
    @Test
    public void testReceiveMessageIsBlank() {
        assertEquals(ConsumeStatus.CONSUME_FAILURE, riskCaseEventConsumer.receive(""));
        Mockito.verify(riskCaseRepository, Mockito.times(0)).save(any(RiskCaseDO.class));
        Mockito.verify(riskCaseVehicleRelationRepository, Mockito.times(0)).batchSave(anyList());
    }

    /**
     * 测试消息体为空的情况
     */
    @Test
    public void testReceiveMessageBodyIsNull() {
        message = JsonUtil.serialize(new MqCommonMessage<RiskCaseMessageDTO>());
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, riskCaseEventConsumer.receive(message));
        Mockito.verify(riskCaseRepository, Mockito.times(0)).save(any(RiskCaseDO.class));
        Mockito.verify(riskCaseVehicleRelationRepository, Mockito.times(0)).batchSave(anyList());
    }

    /**
     * 测试消息体正常的情况
     */
    @Test
    public void testShouldConsume() {
        // mock 风险关系查询
        Mockito.when(riskCaseVehicleRelationRepository.queryByParam(Mockito.any(RiderCaseVehicleRelationDOParamDTO.class)))
                .thenReturn(Collections.singletonList(new RiskCaseVehicleRelationDO()));

        // mock start消息不存在
        Mockito.doReturn(Collections.EMPTY_LIST).when(riskCaseRepository).queryByParam(any());

        // 验证
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, riskCaseEventConsumer.receive(message));
        verify(riskCaseRepository).save(any());
        verify(riskCaseVehicleRelationRepository, times(2)).batchSave(anyList());

    }

    /**
     * 测试 end 消息先来，抛出异常的形式返回重试
     */
    @Test
    public void testReceiveEndMessageFirstException() {
        // 调用
        EveMqCommonMessage<RiskCaseMessageDTO> message = JacksonUtils.from(s_end,
                new TypeReference<EveMqCommonMessage<RiskCaseMessageDTO>>() {
                });
        message.setTimestamp(System.currentTimeMillis());
        ConsumeStatus consumeStatus = riskCaseEventConsumer.receive(JacksonUtils.to(message));

        // 验证：因为需要重新消费，所以下面的仓储层不会执行到
        assertEquals(ConsumeStatus.CONSUME_FAILURE, consumeStatus);
        verify(riskCaseRepository, times(0)).save(any());
        verify(riskCaseVehicleRelationRepository, times(0)).batchSave(anyList());
    }

    /**
     * 超出最大可重试时间，停止消费该消息，返回CONSUME_SUCCESS
     */
    @Test
    public void testReceiveEndMessageFirstNormal() {
        // 调用
        EveMqCommonMessage<RiskCaseMessageDTO> message = JacksonUtils.from(s_end,
                new TypeReference<EveMqCommonMessage<RiskCaseMessageDTO>>() {
                });
        ConsumeStatus consumeStatus = riskCaseEventConsumer.receive(JacksonUtils.to(message));

        // 验证：因为超出最大可重试秒数，停止消费，所以下面的仓储层不会执行到
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, consumeStatus);
        verify(riskCaseRepository, times(0)).save(any());
        verify(riskCaseVehicleRelationRepository, times(0)).batchSave(anyList());
    }
}
