package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.server.crane.SecurityCallCloudControlCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.SecurityCancelCallCloudControlCrane;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.junit.Test;

public class SecurityCallCloudControlCraneTest extends SpringTestBase {

    @Resource
    SecurityCallCloudControlCrane securityCallCloudControlCrane;

    @Resource
    private SecurityCancelCallCloudControlCrane securityCancelCallCloudControlCrane;


    @Test
    public void testSecurityCallControlRun() throws Exception {
        securityCallCloudControlCrane.run();
    }

    @Test
    public void testSecurityCancelCallControlRun() throws Exception {
        securityCancelCallCloudControlCrane.run();
    }
}
