package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;

/**
 * 切面测试类
 */
public class AspectTest extends SpringTestBase {

//    @Resource
//    private WaybillAdapter waybillAdapter;
//
//    @MessageProducer(topic = MessageTopicEnum.WAYBILL_TRANSFER_APPLY)
//    private CommonMessageProducer<RiderTransferWaybillMessageDTO> messageProducer;
//
//    @Resource
//    private IThriftAppDeliveryService deliveryService;
//
//    @Resource
//    private VehicleTaskConsumer vehicleTaskConsumer;
//
//    @Resource
//    private DomainEventService domainEventService;
//
//    @Test
//    public void testLog() {
//        //
//        waybillAdapter.getWaybillInfo(new HashSet<>(), new HashSet<>());
//        //
//        messageProducer.sendMqCommonMessage(RiderTransferWaybillMessageDTO.builder().build(),
//                MessageType.RIDER_TRANSFER, "operator");
//        //
//        try {
//            deliveryService.arriveStation(new DeliveryArriveStationRequest());
//        } catch (Exception e) {
//
//        }
//        //
//        try {
//            vehicleTaskConsumer.receive("");
//        } catch (Exception e) {
//
//        }
//        //
//        try {
//            domainEventService.process(new DomainEventChangeDTO<>());
//        } catch (Exception e) {
//
//        }
//
//
//    }


}
