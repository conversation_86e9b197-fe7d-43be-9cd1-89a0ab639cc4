package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.domain.process.CaseMarkTransformProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import java.util.Date;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;

public class CaseMarkTransformProcessTest extends SpringTestBase {

    @Resource
    private CaseMarkTransformProcess caseMarkTransformProcess;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Before
    public void setUp() throws Exception {}

    @Test
    @SneakyThrows
    public void process() {
        // 创建一个停滞的case + 一个停滞不当的case
        RiskCaseDO strandingRiskCase = createStrandingRiskCase();
        RiskCaseDO vehicleStandStillRiskCase = createVehicleStandStillRiskCase();
        riskCaseRepository.save(strandingRiskCase);
        riskCaseRepository.save(vehicleStandStillRiskCase);
        // strading
        CaseMarkInfoDO caseMarkInfoDO = CaseMarkInfoDO.builder().caseId(strandingRiskCase.getCaseId())
                .subCategory(ISCheckCategoryEnum.RED_LIGHT.getSubcategory())
                .category(ISCheckCategoryEnum.RED_LIGHT.getCategory())
                .improperStrandingReason(ImproperStrandingReason.builder().withAccidentOrder(true).build()).build();
        caseMarkInfoRepository.save(caseMarkInfoDO);
        DomainEventDTO<CaseMarkInfoDO> caseMarkInfoDODomainEventDTO = DomainEventDTO.<CaseMarkInfoDO>builder().build();
        caseMarkInfoDODomainEventDTO.setTimestamp(System.currentTimeMillis());
        caseMarkInfoDODomainEventDTO.setBefore(new ArrayList<>());
        caseMarkInfoDODomainEventDTO
                .setEntry(DomainEventEntryDTO.builder().domainClassName(CaseMarkInfoDO.class.getSimpleName())
                        .operateEntry(OperateEnterActionEnum.MARK_TRANSFORM_ENTRY).build());
        caseMarkInfoDODomainEventDTO.setAfter(Lists.newArrayList(caseMarkInfoDO));
        caseMarkTransformProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(caseMarkInfoDODomainEventDTO, CaseMarkInfoDO.class));
    }

    @Test
    @SneakyThrows
    public void processFirstSubCategoryChange() {
        // 创建一个停滞的case + 一个停滞不当的case
        RiskCaseDO strandingRiskCase = createStrandingRiskCase();
        RiskCaseDO vehicleStandStillRiskCase = createVehicleStandStillRiskCase();
        riskCaseRepository.save(strandingRiskCase);
        riskCaseRepository.save(vehicleStandStillRiskCase);
        // strading
        CaseMarkInfoDO caseMarkInfoDO = CaseMarkInfoDO.builder().caseId(strandingRiskCase.getCaseId())
                .firstCategory(ISCheckCategoryEnum.RED_LIGHT.getCategory())
                .firstSubCategory(ISCheckCategoryEnum.RED_LIGHT.getSubcategory()).updateTime(new Date())
                .improperStrandingReason(ImproperStrandingReason.builder().withAccidentOrder(true).build()).build();
        caseMarkInfoRepository.save(caseMarkInfoDO);
        DomainEventDTO<CaseMarkInfoDO> caseMarkInfoDODomainEventDTO = DomainEventDTO.<CaseMarkInfoDO>builder().build();
        caseMarkInfoDODomainEventDTO.setTimestamp(System.currentTimeMillis());
        caseMarkInfoDODomainEventDTO.setBefore(new ArrayList<>());
        caseMarkInfoDODomainEventDTO
                .setEntry(DomainEventEntryDTO.builder().domainClassName(CaseMarkInfoDO.class.getSimpleName())
                        .operateEntry(OperateEnterActionEnum.MARK_TRANSFORM_ENTRY).build());
        caseMarkInfoDODomainEventDTO.setAfter(Lists.newArrayList(caseMarkInfoDO));
        caseMarkTransformProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(caseMarkInfoDODomainEventDTO, CaseMarkInfoDO.class));
    }

    private RiskCaseDO createStrandingRiskCase() {

        return RiskCaseDO.builder().caseId("123").eventId("123111").type(RiskCaseTypeEnum.STRANDING)
                .status(RiskCaseStatusEnum.DISPOSED).build();

    }

    private RiskCaseDO createVehicleStandStillRiskCase() {
        return RiskCaseDO.builder().caseId("456").eventId("123").type(RiskCaseTypeEnum.STRANDING)
                .status(RiskCaseStatusEnum.DISPOSED).build();

    }
}