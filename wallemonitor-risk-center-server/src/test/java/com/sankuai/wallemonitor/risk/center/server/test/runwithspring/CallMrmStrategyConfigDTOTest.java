package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;


import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallMrmStrategyConfigDTO;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class CallMrmStrategyConfigDTOTest {

    @Mock
    private CallMrmStrategyConfigDTO callMrmStrategyConfigDTO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 isNeedFilter 方法，当上下文为空时
     */
    @Test
    public void isNeedFilterTest() {
        // arrange
        Map<String, Object> context = new HashMap<>();
        context.put("vhr", ">1");
        context.put("mrmStatus", 3);
        context.put("hasReOrder", false);
        context.put("duration", null);

        CallMrmStrategyConfigDTO callMrmStrategyConfigDTO = CallMrmStrategyConfigDTO.builder()
                .filters(Arrays.asList("#vhr == '>1'", "#mrmStatus == null || #mrmStatus == 1 || #mrmStatus == 2",
                        "#hasAccident == true",
                        "#hasReOrder == true",
                        "#hasRescueOrder == true",
                        "#duration == null || #duration > 5"))
                .build();

        boolean result = callMrmStrategyConfigDTO.isNeedFilter(context, "123");
        System.out.println("result" + result);
    }
}
