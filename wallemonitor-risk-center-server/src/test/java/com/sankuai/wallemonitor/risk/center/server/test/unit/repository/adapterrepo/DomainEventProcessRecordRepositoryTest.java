package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.adapterrepo;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisDefaultClient;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventProcessResultDO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

public class DomainEventProcessRecordRepositoryTest extends RepositoryTestBase {

    @Mock
    private DomainEventEntryDTO entry;

    @Mock
    private DomainEventProcessResultDO resultDO;

    private Long eventTime;

    private List<String> processNameList;

    private List<DomainEventProcessResultDO> resultDOList;

    private List<RedisStoreClient> redisStoreClients;

    @Mock
    private RedisDefaultClient redisStoreClient;

    private Map<StoreKey, Object> storeKeyObjectMap;

    @Before
    public void setUp() {
        eventTime = 1721702359274L;
        processNameList = Arrays.asList("process1", "process2");
        resultDOList = Arrays.asList(
                DomainEventProcessResultDO.builder()
                        .processName("process1")
                        .entry(DomainEventEntryDTO.builder()
                                .domainClassName("RiskCaseDO")
                                .operateEntry(OperateEnterActionEnum.RISK_CASE_CREATE_OR_UPDATE_MESSAGE_ENTRY)
                                .build())
                        .traceId("traceId1")
                        .eventTime(eventTime)
                        .build(),
                DomainEventProcessResultDO.builder()
                        .processName("process2")
                        .entry(DomainEventEntryDTO.builder()
                                .domainClassName("RiskCaseDO")
                                .operateEntry(OperateEnterActionEnum.RISK_CASE_CREATE_OR_UPDATE_MESSAGE_ENTRY)
                                .build())
                        .traceId("traceId2")
                        .eventTime(eventTime)
                        .build()
        );
        storeKeyObjectMap = new HashMap<>();
        storeKeyObjectMap.put(
                new StoreKey("RISK_CENTER_DOMAIN_EVENT", "process1#entry#traceId#1721702359274"),
                resultDOList.get(0));
        storeKeyObjectMap.put(
                new StoreKey("RISK_CENTER_DOMAIN_EVENT", "process2#entry#traceId#1721702359274"),
                resultDOList.get(1));

        // mock squirrelAdapter
        when(redisStoreClient.getClusterName()).thenReturn("redis-wproject-system_qa");
        redisStoreClients = Collections.singletonList(redisStoreClient);
        when(redisStoreClient.multiGet(anyList())).thenAnswer(
                invocation -> {
                    List<StoreKey> storeKeyList = invocation.getArgument(0);
                    Map<StoreKey, Object> answer = new HashMap<>();
                    storeKeyList.forEach(storeKey -> {
                        answer.put(storeKey, storeKeyObjectMap.get(storeKey));
                    });
                    return answer;
                }
        );
        ReflectionTestUtils.setField(squirrelAdapter, "redisStoreClients", redisStoreClients);
        squirrelAdapter.init();

    }

    /**
     * 测试正常 获取某个入口和时间下的领域事件结果 场景
     */
    @Test
    public void testGetProcessResultNormal() {
        // 调用
        List<DomainEventProcessResultDO> results = domainEventProcessRecordRepository.getProcessResult(processNameList,
                entry, eventTime,
                "traceId");

        // 验证
        assertEquals(resultDOList, results);
        verify(squirrelAdapter).multiGet(any(), anyList());
    }

    /**
     * 测试异常场景
     */
    @Test
    public void testGetProcessResultException() throws Throwable {
        // 调用
        List<DomainEventProcessResultDO> results = domainEventProcessRecordRepository.getProcessResult(null,
                null, null, null);

        // 验证
        assertTrue(results.isEmpty());
        verify(squirrelAdapter).multiGet(any(), anyList());
    }

    @Test
    public void testBatchSave() {
        // 运行
        domainEventProcessRecordRepository.batchSave(resultDOList);

        // 验证
        verify(squirrelAdapter, times(2)).set(any(), anyString(), any(), any());
    }
}
