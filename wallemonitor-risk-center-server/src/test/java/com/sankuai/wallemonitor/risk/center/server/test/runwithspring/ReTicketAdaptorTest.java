package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.response.VehicleDataInfoVO;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.EventPlatSearchAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.ReTicketAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.WorkstationAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.WorkstationCreateRequest;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DriveModeRecordQueryParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DriveModeRecordQueryParamVTO.SingleVinQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.DriveModeRecordVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.ReTicketVTO;
import com.sankuai.wallemonitor.risk.center.server.crane.HistoryRiskCaseRelatedWorkstationCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.ImproperStrandingReasonUpdateCrane;
import com.sankuai.wallemonitor.risk.center.server.crane.NegativePublicEventUnHandleNoticeCrane;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024-09-15
 */
public class ReTicketAdaptorTest extends SpringTestBase {

    @Resource
    private ReTicketAdapter reTicketAdaptor;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private ImproperStrandingReasonUpdateCrane improperStrandingReasonUpdateCrane;

    @Resource
    private NegativePublicEventUnHandleNoticeCrane negativePublicEventUnHandleNoticeCrane;

    @Resource
    EventPlatSearchAdapter eventPlatSearchAdapter;

    @Resource
    private WorkstationAdapter workstationAdapter;

    @Resource
    private HistoryRiskCaseRelatedWorkstationCrane historyRiskCaseRelatedWorkstationCrane;

    @Test
    public void testQuerySuccess() {
        List<ReTicketVTO> reTicketVTOList = reTicketAdaptor.queryByVehicleNameList(
                Arrays.asList("s20-123", "s20-320", "s20-321", "s20-322", "s20-323", "s20-324"));
        System.out.printf(Locale.ENGLISH, "RE 工单列表: %s", JacksonUtils.to(reTicketVTOList));
    }

    @Test
    public void queryDriveModeRecord() {
        // mock queryVehicleHistoryDataFromEveReplay 返回指定数据
        String json = "[{\"vin\":\"LA71AUB16S0501770\",\"time\":\"2025-05-20 22:20:36\",\"longitude\":116.5533476,\"latitude\":40.0962619,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LA71AUB16S0501770\",\"time\":\"2025-05-20 22:20:37\",\"longitude\":116.5533475,\"latitude\":40.0962619,\"drive_status_enum\":5,\"drive_status\":\"无控制状态\",\"position_source\":\"autocar_utm\"}]";
        List<VehicleDataInfoVO> mockList = JacksonUtils.from(json, new TypeReference<List<VehicleDataInfoVO>>() {
        });
        DriveModeRecordQueryParamVTO param = new DriveModeRecordQueryParamVTO();
        List<SingleVinQueryParam> vinQueryParamList = new ArrayList<>();
        SingleVinQueryParam singleVinQueryParam = new SingleVinQueryParam();
        singleVinQueryParam.setVin("LA71AUB16S0501770");
        singleVinQueryParam.setStartTime(DatetimeUtil.getBeforeTime(new Date(1747750836), TimeUnit.MINUTES, 10));
        singleVinQueryParam.setEndTime(new Date(1747751477));
        vinQueryParamList.add(singleVinQueryParam);
        param.setVinQueryParamList(vinQueryParamList);
        Map<String, DriveModeRecordVTO> result = vehicleAdapter.queryDriveModeRecord(param);

        System.out.println(result);
    }

    @Test
    public void testQueryDriveModeRecord() throws Exception {
        improperStrandingReasonUpdateCrane.run();
    }

    @Test
    public void testQueryEventPlatform() {
        eventPlatSearchAdapter.queryLatestDriveModeChange("LMTZSV025NC091137");
    }

    @Test
    public void testQueryLatestUpdateToMrmDriveModeChange() {
        eventPlatSearchAdapter.queryLatestUpdateToMrmDriveModeChange("LMTZSV023NC065524",
                new Date(1736306882000L - 1000L), new Date(1736306882000L + 1000L));
    }

    @Test
    public void testQueryEventPlatformByVin() {
        eventPlatSearchAdapter.queryLatestAutoCarUtmLocationInfoChange("LMTZSV025NC091137");
    }

    @Test
    public void testQueryWorkstation() {

        Date startTime = DatetimeUtil.parseDate("2024-11-16 00:13:33", "yyyy-MM-dd HH:mm:ss");

        Date endTime = DatetimeUtil.parseDate("2024-11-16 23:13:33", "yyyy-MM-dd HH:mm:ss");

        workstationAdapter.getWorkstationCaseId("LMTZSV022NC017593",
                "2023-08-04 00:00:00", "2023-11-29 23:59:59");
    }

    @Test
    public void testCreateWorkstationCase() {

        WorkstationCreateRequest request = new WorkstationCreateRequest();
        request.setSource("intervention");
        request.setVin("LMTZSV022NC017593");
        request.setCaseTime(DatetimeUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:sss"));
        request.setCaseType("intervention");
        workstationAdapter.createWorkstationCase(request);
    }

    @Test
    public void testHistoryRiskCaseRelatedWorkstation() throws Exception {
        historyRiskCaseRelatedWorkstationCrane.run();
    }

    @Test
    public void testNegativePublicEventUnHandleNotice() throws Exception {
        negativePublicEventUnHandleNoticeCrane.run();
    }

    @Test
    public void testQueryByVinList() {
        Map<String, Boolean> hasReOrder = reTicketAdaptor.queryByVinList(Lists.newArrayList("LMTZSV027NC027844"));
        System.out.println(hasReOrder);
    }
}
