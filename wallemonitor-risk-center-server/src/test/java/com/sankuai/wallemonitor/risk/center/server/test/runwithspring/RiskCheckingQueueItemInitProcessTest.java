package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.domain.process.RiskCheckingQueueItemTriggerAutoMarkProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Collections;
import java.util.UUID;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class RiskCheckingQueueItemInitProcessTest extends SpringTestBase {

    @Resource
    private RiskCheckingQueueItemTriggerAutoMarkProcess riskCheckingQueueItemTriggerAutoMarkProcess;

    @Test
    @SneakyThrows
    public void testInitRiskCheckingQueueItem() {
        DomainEventDTO<RiskCheckingQueueItemDO> eventDTO = DomainEventDTO.<RiskCheckingQueueItemDO>builder()
                .after(Collections.singletonList(RiskCheckingQueueItemDO.builder()
                        .checking(true)
                        .eventId(UUID.randomUUID().toString())
                        .tmpCaseId("1231231231231231")
                        .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                        .round(0)
                        .source(RiskCaseSourceEnum.BEACON_TOWER)
                        .occurTime(null)
                        .vin("LSTM00001322")
                        .build()))
                .build();

        riskCheckingQueueItemTriggerAutoMarkProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCheckingQueueItemDO.class));
    }


}