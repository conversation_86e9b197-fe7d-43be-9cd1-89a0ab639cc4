package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.domain.process.TrafficChangeToGreenTriggerDynamicCheckingProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCheckingQueueItemFactory;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCheckingQueueItemFactory.CreateRiskCheckingQueueDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskMarkRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import java.util.UUID;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class TrafficChangeToGreenTriggerDynamicCheckingProcessTest extends SpringTestBase {

    @Resource
    private TrafficChangeToGreenTriggerDynamicCheckingProcess trafficChangeToGreenTriggerDynamicCheckingProcess;

    @Resource
    private VehicleRuntimeInfoContextRepository runtimeInfoContextRepository;

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    @Resource
    private RiskMarkRepository riskMarkRepository;

    @Test
    @SneakyThrows
    public void process() {
        DomainEventDTO<VehicleRuntimeInfoContextDO> domainEventChangeDTO = new DomainEventDTO<>();
        domainEventChangeDTO.setEntry(DomainEventEntryDTO.builder()
                .operateEntry(OperateEnterActionEnum.RISK_CASE_CREATE_OR_UPDATE_MESSAGE_ENTRY)
                .domainClassName(VehicleRuntimeInfoContextDO.class.getSimpleName()).build());
        domainEventChangeDTO.setTimestamp(System.currentTimeMillis());
        domainEventChangeDTO.setBefore(new ArrayList<>());
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = runtimeInfoContextRepository
                .getByVin("LMTZSV023MC063495");
        vehicleRuntimeInfoContextDO.setTrafficLightType(TrafficLightTypeEnum.GREEN);
        domainEventChangeDTO.setAfter(Lists.newArrayList(vehicleRuntimeInfoContextDO));
        RiskCheckingQueueItemDO checkingQueueItemDO = RiskCheckingQueueItemFactory.createRiskCheckingQueueItem(
                CreateRiskCheckingQueueDOParamDTO.builder().type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                        .caseId(UUID.randomUUID().toString()).vin(vehicleRuntimeInfoContextDO.getVin())
                        .source(RiskCaseSourceEnum.BEACON_TOWER).recallTime(System.currentTimeMillis())
                        .occurTime(System.currentTimeMillis() - 2 * 60 * 1000).eventId(UUID.randomUUID().toString())
                        .build());
        vehicleRuntimeInfoContextDO.setLat("0");
        vehicleRuntimeInfoContextDO.setLng("0");
        riskCheckQueueRepository.save(checkingQueueItemDO);
        riskMarkRepository.saveMarkItem(Lists.newArrayList(checkingQueueItemDO), "");
        trafficChangeToGreenTriggerDynamicCheckingProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(domainEventChangeDTO, VehicleRuntimeInfoContextDO.class));
        checkingQueueItemDO.setStatus(RiskQueueStatusEnum.CONFIRMED_RISK);
        riskCheckQueueRepository.save(checkingQueueItemDO);
        riskMarkRepository.deleteMarkItem(Lists.newArrayList(checkingQueueItemDO), "");

    }
}