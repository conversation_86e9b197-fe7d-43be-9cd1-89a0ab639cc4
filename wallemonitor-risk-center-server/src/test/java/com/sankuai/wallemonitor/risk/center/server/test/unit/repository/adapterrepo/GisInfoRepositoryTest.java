package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.adapterrepo;

import static groovy.util.GroovyTestCase.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

import com.sankuai.map.open.platform.api.regeo.RegeoResponse;
import com.sankuai.map.open.platform.api.regeo.Regeocode;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GisInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import java.util.Arrays;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;

public class GisInfoRepositoryTest extends RepositoryTestBase {

    private PositionDO position;

    @Before
    @SneakyThrows
    public void setUp() {
        // mock
        position = PositionDO.builder().longitude(120.0).latitude(23.5).coordinateSystem(
                CoordinateSystemEnum.WGS84).build();
        // 模拟转换后的位置数据
        ReflectionTestUtils.setField(gisAdapter, "positionDOConvert", positionDOConvert);
        when(positionDOConvert.toPositionDO(position, CoordinateSystemEnum.GCJ02)).thenReturn(position);

        // mock gisAdapter lion配置
        ReflectionTestUtils.setField(gisAdapter, "LBS_DOMAIN", "http://lbsapi.map.test.sankuai.com");
        ReflectionTestUtils.setField(gisAdapter, "mapKey", "lbsmap_key");

        // mock HttpUtils.get
        RegeoResponse regeoResponse = new RegeoResponse(200, "ok");
        regeoResponse.setRegeocode(Arrays.asList(new Regeocode()));
        PowerMockito.mockStatic(HttpUtils.class);
        when(HttpUtils.get(anyMap(), anyString(), anyString(), anyMap(), any(Class.class))).thenReturn(
                new EveHttpResponse<>(200, "success", regeoResponse));

    }

    /**
     * 测试正常情况下根据经纬度查询地理信息
     */
    @Test
    @SneakyThrows
    public void testQueryByPositionNormal() {
        // 调用
        GisInfoDO gisInfoDO = gisInfoRepository.queryByPosition(position);
        // 验证
        assertEquals(GisInfoDO.builder().area("").city("").poi("").position(position).build(), gisInfoDO);
    }
}
