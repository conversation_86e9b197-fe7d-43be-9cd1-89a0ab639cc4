package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskDetectRecordStatusUpdatedProcess;
import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetectorManager;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskSpecialAreaStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.apache.thrift.TException;
import org.junit.Test;


public class RiskDetectRecordStatusUpdatedProcessTest extends SpringTestBase {

    @Resource
    private RiskDetectRecordStatusUpdatedProcess processor;

    @Resource
    private RiskDetectorManager riskDetectorManager;

    @Test
    public void test() throws TException {
        String s = "{\"entry\":{\"domainClassName\":\"RiskSpecialAreaStrandingRecordDO\",\"operateEntry\":\"RISK_CHECKING_QUEUE_STATUS_ENTRY\"},\"timestamp\":1729085971357,\"operator\":\"\",\"traceId\":\"12937de6-08b6-4004-998f-a8bc269cab3f\",\"extInfo\":{},\"before\":[],\"after\":[{\"tmpCaseId\":\"M472120250415125045S05T12\",\"type\":\"SPECIAL_AREA_STRANDING\",\"vin\":\"LMTZSV022NC017593\",\"duration\":103379,\"status\":\"CONFIRMED\",\"vehicleRuntimeInfoSnapshot\":{\"vin\":\"LMTZSV022NC017593\",\"driveMode\":\"AUTONOMOUS_DRIVING\",\"speed\":0.3,\"lng\":\"116.407526\",\"lat\":\"40.057008\",\"batterySwitching\":false,\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"\",\"trafficLightType\":\"NONE\",\"distanceToNextJunction\":-1,\"waitingGatePole\":false,\"lastUpdateTime\":\"2024-10-15 16:55:45\",\"createTime\":\"2024-10-15 15:42:59\",\"updateTime\":\"2024-10-16 15:29:29\",\"isDeleted\":\"NOT_DELETED\"},\"occurTime\":\"2024-10-16 21:38:44\",\"recallTime\":\"2024-10-16 21:39:30\",\"areaType\":\"CONSTRUCTION_AREA\"}]}";
        DomainEventDTO<RiskSpecialAreaStrandingRecordDO> eventDTO = JacksonUtils.from(s,
                new TypeReference<DomainEventDTO<RiskSpecialAreaStrandingRecordDO>>() {
                });
        RiskSpecialAreaStrandingRecordDO recordDO = eventDTO.getAfter().get(0);
        recordDO.setVin("LMTZSV020MC042359");
        recordDO.setStatus(DetectRecordStatusEnum.CANCELLED);
        AbstractMapperSingleRepository repo = riskDetectorManager
                .getRepositoryByType(RiskSpecialAreaStrandingRecordDO.class);
        repo.save(eventDTO.getAfter().get(0));
        processor.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskSpecialAreaStrandingRecordDO.class));
    }
}
