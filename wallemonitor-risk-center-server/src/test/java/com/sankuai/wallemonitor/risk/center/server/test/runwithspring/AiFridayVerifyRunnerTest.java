package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.sankuai.walleeve.commons.exception.SystemException;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.thrift.response.EveThriftPageResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.AdminListRiskCaseRequest;
import com.sankuai.wallemonitor.risk.center.api.request.CreateExperimentalResultRequest;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultMissCaseDetailRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultMissCaseDetailRequestDTO.IdentifyProcessDetailDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultMissCaseDetailRequestDTO.IdentifyProcessInfoDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultOverviewRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultSceneDataRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.ExperimentalResultSceneDataRequestDTO.MissCaseAndTimes;
import com.sankuai.wallemonitor.risk.center.api.response.AdminGetRiskCaseDetailResponse;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListRiskCaseVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseBaseInfoVO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISAiFridayClassify.FridayCheckActionConfig;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.FridayOpenAiAdapter;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.FridayConfig;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamWithMessageVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.FridayVerifyResultVTO;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.FileCopyUtils;

@Slf4j
public class AiFridayVerifyRunnerTest {

    private final FridayConfig config = new FridayConfig();

    private final RiskCaseAdapter riskCaseAdapter = new RiskCaseAdapter();

    // 发起识别
    private final FridayOpenAiAdapter adapter = new FridayOpenAiAdapter();

    private static final List<String> desc = Lists.newArrayList(
            "[验证线上][验证有风险 + 无风险case集合][替换前方车辆和路口的描述，增加无灯描述]多轮调用 + 修改prompt + 图片重组",
            "修改prompt + 调整约束",
            "多轮调用优化二轮到三轮之间的转换错误，替换文字为要求，增加上下文关联的描述", "第三轮不关注图片信息；",
            "修改uuid", "增加上下文");

    private static final String operator = "廖凌飞";

    private static final String mis = "liaolingfei";

    private final static String RESULT_FORMATTER = "%25s > total: %-3s recall: %-3s miss: %-3s failed: %-3s recallRate: %6.3f precisionRate: %.3f";

    private final static Integer empId = 8186058;

    private static final String accessToken = "eAGFzitLg1EYAGAOkzGWxpLxCwa3IDvvuZt0N4zeULDId77zHoOwhQmWBbciswimKQgqU4OgaHK4IIKGYTH6A-bWjYKIwewfeHgSJNV_-4wFx-33px5AknsjkOWcx-lAUyWFZ15zJXikqHVAs2jCnPKOeoD8F0lnVtEuRVjBelGWyrRgWLmo80IqqkUhDxqMKVDFy7lSMHht7T7CJIF_Yf1bmonP9XvnLw8w396_a3ahSbLJ-MJioeownR50Lke3px-tq-FJY3TdGd00xseCnfvDzOTz9_CiCwck8Rc7IlOhMpRRYN4otEglj4B7i9pyo7RXdp0qxsCAVExwcUYmttHWOYRR6ENhwDquDNdOipCCi6QThoJfCxwzTiAyabKWo9FaA8vJELwMUTqwTZLawK3ZKMJabbm6iZUV2COxWq36A-7EelU**eAEFwQkBwDAIA0BLW_nlEEj9S-id7XG9PKMSlh2zX3kBGOPVlin08s8Wcce0wZvLYaVd03hbtBLj**TWDHGc7L3TfrRB8FUH3YTzFsWzsN8A_zTG4FzuKtD99XFxDqlZl9zpWbbXfRIENhZuhcUVEIzT-903yzmtg-gw**ODE4NjA1OCxsaWFvbGluZ2ZlaSzlu5blh4zpo54sbGlhb2xpbmdmZWlAbWVpdHVhbi5jb20sMSwzNDAxNDkxMywxNzM0MjMyOTE5MDY5";
    private static final String dataset = "小号有风险";

    @Test
    public void testCase() {
        boolean useFile = false;
        CaseDetailDTO caseDetailDTO;
        Set<String> caseIdList = new HashSet<>();
        caseDetailDTO =
                useFile ? buildFromFile("case.txt") : buildRiskLevel("2024-11-15 00:00:00", "2024-12-10 00:00:00",
                        Lists.newArrayList(2),
                        new ArrayList<>(),
                        150);
        Map<String, AdminGetRiskCaseDetailResponse> riskCaseDetailResponseMap = new HashMap<>(
                caseDetailDTO.getCaseDetailResponseMap());
        caseIdList.addAll(riskCaseDetailResponseMap.keySet());
        caseDetailDTO =
                useFile ? buildFromFile("case.txt") : buildRiskLevel("2024-11-15 00:00:00", "2024-12-10 00:00:00",
                        Lists.newArrayList(1),
                        new ArrayList<>(),
                        150);
        riskCaseDetailResponseMap.putAll(caseDetailDTO.getCaseDetailResponseMap());
        caseIdList.addAll(riskCaseDetailResponseMap.keySet());
        if (CollectionUtils.isEmpty(riskCaseDetailResponseMap)) {
            log.error("riskCaseDetailResponseMap is empty.");
            return;
        }

        FridayCheckActionConfig fridayCheckActionConfig = new FridayCheckActionConfig();
        fridayCheckActionConfig.setStepPrompt(getFromFile("step.txt"));
        fridayCheckActionConfig.setUserPrompt(getFromFile("user.txt"));
        fridayCheckActionConfig.setSystemPrompt(getFromFile("system.txt"));

        // 发起识别
        FridayOpenAiAdapter adapter = new FridayOpenAiAdapter();
        List<FridayModelParamWithMessageVTO> fridayModelParamWithMessageVTOS = build(new ArrayList<>(caseIdList),
                riskCaseDetailResponseMap, fridayCheckActionConfig);
        Map<String, FridayVerifyResultVTO> fridayVerifyResultVTOMap = ParallelExecutor.executeParallelTasksAndGetResult(
                        "is_check_action", fridayModelParamWithMessageVTOS, (param) -> {
                            try {
                                return adapter.verify(param);
                            } catch (Exception e) {
                                log.error("friday verify failed.", e);
                                return null;
                            }
                        }).stream()
                .collect(Collectors.toMap(FridayVerifyResultVTO::getCaseId, Function.identity(), (o1, o2) -> o1));
        summaryResult(riskCaseDetailResponseMap, fridayVerifyResultVTOMap);
    }

    @Test
    public void testMuplitRoundCase() {
        boolean useFile = true;
        CaseDetailDTO caseDetailDTO;
        List<String> caseIdList = new ArrayList<>();
        caseDetailDTO =
                useFile ? buildFromFile("new/case.txt") : buildRiskLevel("2024-11-15 00:00:00", "2024-12-10 00:00:00",
                        Lists.newArrayList(2),
                        new ArrayList<>(),
                        150);
        Map<String, AdminGetRiskCaseDetailResponse> riskCaseDetailResponseMap = new HashMap<>(
                caseDetailDTO.getCaseDetailResponseMap());
        caseIdList.addAll(riskCaseDetailResponseMap.keySet());
        caseDetailDTO =
                useFile ? buildFromFile("new/case.txt") : buildRiskLevel("2024-11-15 00:00:00", "2024-12-10 00:00:00",
                        Lists.newArrayList(1),
                        new ArrayList<>(),
                        150);
        riskCaseDetailResponseMap.putAll(caseDetailDTO.getCaseDetailResponseMap());
        caseIdList.addAll(riskCaseDetailResponseMap.keySet());
        if (CollectionUtils.isEmpty(riskCaseDetailResponseMap)) {
            log.error("riskCaseDetailResponseMap is empty.");
            return;
        }
        FridayCheckActionConfig fridayCheckActionConfig = new FridayCheckActionConfig();
        fridayCheckActionConfig.setSystemPrompt(getFromFile("new/system.txt"));
        fridayCheckActionConfig.setUserPrompt(getFromFile("new/user.txt"));
        List<FridayModelParamVTO> fridayMessageVTOS = buildByRound(caseIdList,
                riskCaseDetailResponseMap, fridayCheckActionConfig);
        Map<String, FridayVerifyResultVTO> fridayVerifyResultVTOMap = ParallelExecutor.executeParallelTasksAndGetResult(
                        "is_check_action", fridayMessageVTOS,
                        (fridayModelParamVTO) -> {
                            try {
                                return adapter.verifyByMultiRound(fridayModelParamVTO, (openApiContext) -> {
                                    String path = String.format("new/round%d/step.txt", openApiContext.getTimes());
                                    //取次数
                                    Integer times = openApiContext.getTimes();
                                    switch (times) {
                                        case 1: {
                                            fridayCheckActionConfig.setStepPrompt(getFromFile(path));
                                            return fridayCheckActionConfig.getStepPrompt();
                                        }
                                        case 2: {
                                            Map<String, Object> paramMap = new HashMap<>();
                                            paramMap.put("front_result",
                                                    JacksonUtils.to(openApiContext.getResultVTO()
                                                            .getObjectNameByView("front")));
                                            paramMap.put("left_result",
                                                    JacksonUtils.to(
                                                            openApiContext.getResultVTO().getObjectNameByView("left")));
                                            paramMap.put("right_result",
                                                    JacksonUtils.to(openApiContext.getResultVTO()
                                                            .getObjectNameByView("right")));
                                            paramMap.put("loop_result",
                                                    JacksonUtils.to(
                                                            openApiContext.getResultVTO().getObjectNameByView("loop")));
                                            fridayCheckActionConfig.setStepPrompt(getFromFile(path));
                                            return fridayCheckActionConfig.getStepPrompt(paramMap);
                                        }
                                        case 3: {
                                            Map<String, Object> paramMap = new HashMap<>();
                                            paramMap.put("scene_result",
                                                    JacksonUtils.to(openApiContext.getResultVTO().getSceneName()));
                                            fridayCheckActionConfig.setStepPrompt(getFromFile(path));
                                            return fridayCheckActionConfig.getStepPrompt(paramMap);
                                        }
                                        default: {
                                            return new ArrayList<>();
                                        }
                                    }
                                });
                            } catch (Exception e) {
                                return null;
                            }
                        }).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(FridayVerifyResultVTO::getCaseId, Function.identity(), (o1, o2) -> o1));
        // 汇总输出结果
        summaryResult(riskCaseDetailResponseMap, fridayVerifyResultVTOMap);
    }


    private CaseDetailDTO buildFromFile(String fileName) {
        List<String> caseIdList = Arrays.stream(getFromFile(fileName).split("\n")).map(String::trim).collect(
                Collectors.toList());
        Map<String, AdminGetRiskCaseDetailResponse> riskCaseDetailResponseMap = riskCaseAdapter.getByCaseIdList(
                caseIdList, accessToken);
        return CaseDetailDTO.builder()
                .caseDetailResponseMap(riskCaseDetailResponseMap)
                .caseIdList(caseIdList)
                .build();

    }

    private CaseDetailDTO buildRiskLevel(String startTime, String endTime,
            List<Integer> levelList, List<String> subCategoryList, Integer pageSize) {
//        String startTime = "2024-11-25 00:00:00";
//        String endTime = "2024-12-10 00:00:00";
        AdminListRiskCaseRequest request = AdminListRiskCaseRequest.builder().build();
        request.setPageNum(1);
        request.setPageSize(pageSize);
        request.setCreateTimeEnd(endTime);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(subCategoryList)) {
            request.setSubCategoryList(subCategoryList);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(levelList)) {
            request.setLevelList(levelList);
        }
        //request.setSubCategoryList(Arrays.asList(ISCheckCategoryEnum.RED_LIGHT.getSubcategory()));
        request.setCreateTimeStart(startTime);
        List<String> caseIdList = new ArrayList<>();
        Map<String, AdminGetRiskCaseDetailResponse> riskCaseDetailResponseMap = riskCaseAdapter.queryRiskCaseList(
                        JacksonUtils.to(request), 1, pageSize, accessToken)
                .stream()
                .peek(listVo -> caseIdList.add(listVo.getCaseId()))
                .collect(Collectors.toMap(r -> r.getCaseId(), listVo -> AdminGetRiskCaseDetailResponse.builder()
                        .vehicleList(listVo.getVehicleList())
                        .base(RiskCaseBaseInfoVO.builder()
                                .occurTime(listVo.getOccurTime())
                                .closeTime(listVo.getCloseTime())
                                .caseId(listVo.getCaseId())
                                .extInfo(listVo.getExtInfo())
                                .type(listVo.getType())
                                .build())
                        .markInfo(listVo.getMarkInfo())
                        .build(), (o, n) -> n));
        return CaseDetailDTO.builder()
                .caseDetailResponseMap(riskCaseDetailResponseMap)
                .caseIdList(caseIdList)
                .build();
    }


    public List<FridayModelParamVTO> buildByRound(List<String> list,
            Map<String, AdminGetRiskCaseDetailResponse> caseDetailResponseMap,
            FridayCheckActionConfig fridayCheckActionConfig) {
        return list.stream().filter(caseDetailResponseMap::containsKey).map(caseId -> {
            Date oTime = DatetimeUtil.convertDatetimeStr2Date(
                    caseDetailResponseMap.get(caseId).getBase().getOccurTime());
            String vin = caseDetailResponseMap.get(caseId).getVehicleList().get(0).getVin();
            FridayModelParamVTO paramVTO = FridayModelParamVTO.builder()
                    .caseId(caseId)
                    //这里取当前的检测时间
                    .occurTime(oTime)
                    .vin(vin)
                    .systemPrompt(fridayCheckActionConfig.getSystemPrompt())
                    .userPrompt(fridayCheckActionConfig.getUserPrompt(vin, oTime, 1))
                    .modelName(config.getModelName())
                    .appId(config.getAppId())
                    .timeout(config.getTimeoutSecond())
                    .build();
            return paramVTO;
        }).collect(Collectors.toList());
    }

    /**
     * 汇总输出结果
     *
     * @param caseDetailMap
     * @param verifyResultMap
     */
    private void summaryResult(Map<String, AdminGetRiskCaseDetailResponse> caseDetailMap,
            Map<String, FridayVerifyResultVTO> verifyResultMap) {
        VerifyResult result = VerifyResult.builder().build();

        Map<String, VerifyResult> resultContainer = new HashMap<>();
        caseDetailMap.forEach((caseId, detail) -> {
            String groundTruth = detail.getMarkInfo().getSubCategory();
            if (!resultContainer.containsKey(groundTruth)) {
                resultContainer.put(groundTruth, VerifyResult.builder().build());
            }

            FridayVerifyResultVTO vto = verifyResultMap.get(caseId);
            result.setTotal(result.getTotal() + 1);
            resultContainer.get(groundTruth).setTotal(resultContainer.get(groundTruth).getTotal() + 1);

            if (vto == null) {
                result.getFailedItemList().add(VerifyResultItem.builder().caseId(caseId).build());
                resultContainer.get(groundTruth).getFailedItemList()
                        .add(VerifyResultItem.builder().caseId(caseId).build());
                return;
            }

            String activity = Optional.ofNullable(vto).map(FridayVerifyResultVTO::getActivity).orElse("");
            ISCheckCategoryEnum prediction = ISCheckCategoryEnum.getByName(activity);
            VerifyResultItem resultItem = VerifyResultItem.builder().caseId(caseId).predict(prediction)
                    .groundTruth(groundTruth)
                    .predictContent(Optional.ofNullable(vto).map(FridayVerifyResultVTO::getAllInfo).orElse(""))
                    .build();
            //识别的大类一致
            if (prediction != null && prediction.getCategory().equals(detail.getMarkInfo().getCategory())) {
                result.getRecallItemList().add(resultItem);
                resultContainer.get(groundTruth).getRecallItemList().add(resultItem);
            } else if (prediction != null && !prediction.getCategory()
                    .equals(detail.getMarkInfo().getCategory())) {
                // 识别的大类不一致
                result.getMissItemList().add(resultItem);
                resultContainer.get(groundTruth).getMissItemList().add(resultItem);
            } else if (prediction != null && prediction.getSubcategory()
                    .equals(detail.getMarkInfo().getSubCategory())) {
                //识别的结果一致
                result.getEqualItemList().add(resultItem);
                resultContainer.get(groundTruth).getEqualItemList().add(resultItem);
            } else {
                log.warn("caseId: {} 识别不出有无风险", caseId);
                result.getFailedItemList().add(resultItem);
                resultContainer.get(groundTruth).getFailedItemList().add(resultItem);
            }
        });

        ExperimentalResultOverviewRequestDTO overview = ExperimentalResultOverviewRequestDTO.builder()
                .experimentalTime(System.currentTimeMillis())
                .personName(operator)
                .personMis(mis)
                .experimentalContent(desc)
                .dataset(dataset)
                .datasetDataCount(caseDetailMap.keySet().size())
                .round(1)
                .recallCount(result.getRecallItemList().size())
                .missCount(result.getMissItemList().size())
                .failureCount(result.getFailedItemList().size())
                .accuracyCount(result.getEqualItemList().size())
                .build();
        List<ExperimentalResultSceneDataRequestDTO> sceneDataList = new ArrayList<>();
        resultContainer.entrySet().stream()
                .sorted((e1, e2) -> {
                    double rate1 = 1.0 * e1.getValue().getRecallItemList().size() / e1.getValue().getTotal();
                    double rate2 = 1.0 * e2.getValue().getRecallItemList().size() / e2.getValue().getTotal();
                    return Double.compare(rate2, rate1); // 降序排列
                }).forEach(entry -> {
                    VerifyResult subResult = entry.getValue();
                    List<MissCaseAndTimes> missed = subResult.getMissItemList().stream()
                            .map(x -> MissCaseAndTimes.builder().caseId(x.getCaseId()).times(1).build())
                            .collect(Collectors.toList());
                    ExperimentalResultSceneDataRequestDTO sceneDataRequestDTO = ExperimentalResultSceneDataRequestDTO.builder()
                            .categoryName(entry.getKey())
                            .actualCaseCount(subResult.getTotal())
                            .recallCaseCount(subResult.getRecallItemList().size())
                            .missedEvents(missed)
                            .build();
                    sceneDataList.add(sceneDataRequestDTO);
                });

        List<ExperimentalResultMissCaseDetailRequestDTO> missCaseList = new ArrayList<>();
        List<ExperimentalResultMissCaseDetailRequestDTO> recallCaseList = new ArrayList<>();
        recallCaseList.add(ExperimentalResultMissCaseDetailRequestDTO.builder()
                .caseId("-----")
                .trueCategory("-----")
                .missCount(0)
                .identifyProcessInfo(new ArrayList<>())
                .build());
        resultContainer.values().forEach(x -> {
            List<VerifyResultItem> missed = x.getMissItemList();
            List<VerifyResultItem> recalled = x.getRecallItemList();
            missed.forEach(item -> {
                FridayVerifyResultVTO vto = verifyResultMap.get(item.getCaseId());
                List<IdentifyProcessInfoDTO> verifyResult = new ArrayList<>();
                verifyResult.add(IdentifyProcessInfoDTO.builder()
                        .category(item.getPredict().getSubcategory())
                        .count(1)
                        .detail(buildDetailMap(vto))
                        .build());
                missCaseList.add(
                        ExperimentalResultMissCaseDetailRequestDTO.builder()
                                .caseId(item.caseId)
                                .trueCategory(item.getGroundTruth())
                                .missCount(1)
                                .identifyProcessInfo(verifyResult)
                                .build()
                );
            });
            recalled.forEach(item -> {
                FridayVerifyResultVTO vto = verifyResultMap.get(item.getCaseId());
                List<IdentifyProcessInfoDTO> verifyResult = new ArrayList<>();
                verifyResult.add(IdentifyProcessInfoDTO.builder()
                        .category(item.getPredict().getSubcategory())
                        .count(1)
                        .detail(buildDetailMap(vto))
                        .build());
                recallCaseList.add(
                        ExperimentalResultMissCaseDetailRequestDTO.builder()
                                .caseId(item.caseId)
                                .trueCategory(item.getGroundTruth())
                                .missCount(1)
                                .identifyProcessInfo(verifyResult)
                                .build()
                );
            });
        });
        List<ExperimentalResultMissCaseDetailRequestDTO> missCaseDetailList = new ArrayList<>();
        missCaseDetailList.addAll(missCaseList);
        missCaseDetailList.addAll(recallCaseList);
        CreateExperimentalResultRequest request = CreateExperimentalResultRequest.builder()
                .operatorEmpId(empId)
                .title(desc.get(0))
                .overview(overview)
                .sceneDataList(sceneDataList)
                .missCaseDetailList(missCaseDetailList)
                .build();
        String kmId = riskCaseAdapter.createResultDoc(request, "1111111");
        log.info("实验报告地址:" + kmId);
        log.info("============================================ 实验报告 ============================================");
        log.info(String.format(RESULT_FORMATTER, "ALL",
                result.getTotal(), result.getRecallItemList().size(), result.getMissItemList().size(),
                result.getFailedItemList().size(), 1.0 * result.getRecallItemList().size() / result.getTotal(),
                1.0 * result.getEqualItemList().size() / result.getTotal()));
        log.info("-------------------------------------------------------------------------------------------------");
        resultContainer.entrySet().stream()
                .sorted((e1, e2) -> {
                    double rate1 = 1.0 * e1.getValue().getRecallItemList().size() / e1.getValue().getTotal();
                    double rate2 = 1.0 * e2.getValue().getRecallItemList().size() / e2.getValue().getTotal();
                    return Double.compare(rate2, rate1); // 降序排列
                }).forEach(entry -> {
                    String category = entry.getKey();
                    VerifyResult subResult = entry.getValue();
                    double recallRate = 1.0 * subResult.getRecallItemList().size() / subResult.getTotal();
                    double precision = 1.0 * subResult.getEqualItemList().size() / subResult.getTotal();
                    log.info(String.format(RESULT_FORMATTER,
                            category,
                            subResult.getTotal(),
                            subResult.getRecallItemList().size(), subResult.getMissItemList().size(),
                            subResult.getFailedItemList().size(),
                            recallRate, precision));
//            subResult.missItemList.forEach(i -> log.info("missItemList: {}", i));
                });
    }

    private Map<String, IdentifyProcessDetailDTO> buildDetailMap(FridayVerifyResultVTO vto) {
        Map<String, IdentifyProcessDetailDTO> detail = new LinkedHashMap<>();
        addToDetailIfNotEmpty(detail, "前视", vto.getFrontObjectList(),
                vto.getImageList().stream().findFirst().orElse(null));
        addToDetailIfNotEmpty(detail, "左视", vto.getLeftObjectList(), null);
        addToDetailIfNotEmpty(detail, "右视", vto.getRightObjectList(), null);
        addToDetailIfNotEmpty(detail, "后视", vto.getBackObjectList(), null);
        addToDetailIfNotEmpty(detail, "环视", vto.getLoopObjectList(), null);
        addToDetailIfNotEmpty(detail, "场景", vto.getSceneList(), null);
        addToDetailIfNotEmpty(detail, "行为", vto.getActivityList(), null);
        return detail;
    }

    private void addToDetailIfNotEmpty(Map<String, IdentifyProcessDetailDTO> detail, String key, List<String> list,
            String picUrl) {
        if (!CollectionUtils.isEmpty(list) && StringUtils.isNotBlank(list.get(0))) {
            detail.put(key, IdentifyProcessDetailDTO.builder()
                    .info(String.join("#", list))
                    .picUrl(picUrl)
                    .build());
        }
    }

    /**
     * 验证结果
     */
    @Data
    @Builder
    private static class VerifyResult {

        /**
         * 总数
         */
        @Builder.Default
        private Integer total = 0;

        /**
         * 召回结果
         */
        @Builder.Default
        private List<VerifyResultItem> recallItemList = new ArrayList<>();

        /**
         * 漏召结果
         */
        @Builder.Default
        private List<VerifyResultItem> missItemList = new ArrayList<>();

        /**
         * 误召结果
         */
        @Builder.Default
        private List<VerifyResultItem> failedItemList = new ArrayList<>();

        /**
         * 准确结果
         */
        @Builder.Default
        private List<VerifyResultItem> equalItemList = new ArrayList<>();
    }

    /**
     * 验证结果项
     */
    @Data
    @Builder
    private static class VerifyResultItem {

        private String caseId;
        /**
         * 预测结果
         */
        private ISCheckCategoryEnum predict;
        /**
         * 真值
         */
        private String groundTruth;
        /**
         * 预测过程信息
         */
        private String predictContent;

        @Override
        public String toString() {
            return "caseId='" + caseId + '\'' +
                    ", groundTruth='" + groundTruth + '\'' +
                    ", predict=" + predict +
                    ", predictContent='" + predictContent + '\'';
        }
    }

    /**
     * 从resources目录下获取case文件的内容,每行是一个caseId
     *
     * @return
     */
    private String getFromFile(String fileName) {
        try {
            ClassPathResource resource = new ClassPathResource(fileName);
            return FileCopyUtils.copyToString(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("读取case文件失败", e);
            return "";
        }
    }

    private List<FridayModelParamWithMessageVTO> build(List<String> list,
            Map<String, AdminGetRiskCaseDetailResponse> caseDetailResponseMap,
            FridayCheckActionConfig fridayCheckActionConfig) {
        return list.stream().filter(caseDetailResponseMap::containsKey).map(caseId -> {
            Date oTime = DatetimeUtil.convertDatetimeStr2Date(
                    caseDetailResponseMap.get(caseId).getBase().getOccurTime());
            String vin = caseDetailResponseMap.get(caseId).getVehicleList().get(0).getVin();
            FridayModelParamWithMessageVTO paramVTO = FridayModelParamWithMessageVTO.builder()
                    .caseId(caseId)
                    //这里取当前的检测时间
                    .occurTime(oTime)
                    .vin(vin)
                    .userStepPrompt(fridayCheckActionConfig.getStepPrompt())
                    .userPrompt(fridayCheckActionConfig.getUserPrompt(vin, oTime, config.getBeforeTime()))
                    .systemPrompt(fridayCheckActionConfig.getSystemPrompt())
                    .modelName(config.getModelName())
                    .appId(config.getAppId())
                    .timeout(config.getTimeoutSecond())
                    .build();
            return paramVTO;
        }).collect(Collectors.toList());
    }


    /**
     * case请求
     */
    @Component
    @Slf4j
    public static class RiskCaseAdapter {

        private static final String CASE_URL = "https://eve.meituan.com/risk/api/admin/listCase";

        private static final String CASE_DETAIL_URL = "https://eve.meituan.com/risk/api/admin/getCaseDetail?caseId=";

        private static final String createExperimentalResultUrl = "https://eve.meituan.com/risk/api/admin/createExperimentalResultContent";

        @SneakyThrows
        public String createResultDoc(CreateExperimentalResultRequest request, String accessToken) {
            EveHttpResponse<EveThriftPageResponse<String>> response = HttpUtils.postJson(JacksonUtils.to(request),
                    createExperimentalResultUrl, "",
                    buildHeader(accessToken),
                    new TypeReference<EveThriftPageResponse<String>>() {
                    });

            return response.getData().getData();
        }

        /**
         * 查询风险case
         *
         * @return
         */
        public Page<AdminListRiskCaseVO> queryRiskCaseList(String jsonParam, Integer pageNum, Integer pageSize,
                String accessToken) {
            try {
                Map<String, Object> postBody = JacksonUtils.fromMap(jsonParam);
                addPageNums(postBody, pageNum, pageSize);
                EveHttpResponse<EveThriftPageResponse<List<AdminListRiskCaseVO>>> response = HttpUtils.postJson(
                        JacksonUtils.to(postBody), CASE_URL, "",
                        buildHeader(accessToken),
                        new TypeReference<EveThriftPageResponse<List<AdminListRiskCaseVO>>>() {
                        });
                if (response == null || response.getData() == null || response.getCode() != HttpStatus.OK.value()) {
                    log.error(JacksonUtils.to(response), new SystemException("查询风险case失败"));
                }
                EveThriftPageResponse<List<AdminListRiskCaseVO>> eveThriftPageResponse = response.getData();
                Page<AdminListRiskCaseVO> page = new Page<>();
                long totalRecords = eveThriftPageResponse.getPaging().getTotal();
                int totalPages = eveThriftPageResponse.getPaging().getTotalPages();
                page.setPages(totalPages);
                page.pageNum(eveThriftPageResponse.getPaging().getPageNum());
                page.pageSize(pageSize);
                page.setTotal(totalRecords);
                page.addAll(eveThriftPageResponse.getData());
                return page;
            } catch (Exception e) {
                log.error("请求风险列表查询", e);
            }
            return null;
        }

        public AdminGetRiskCaseDetailResponse getByCaseId(String caseId, String accessToken) {
            try {
                EveHttpResponse<EveThriftResponse<AdminGetRiskCaseDetailResponse>> response = HttpUtils.get(
                        new HashMap<>(),
                        CASE_DETAIL_URL + caseId,
                        buildHeader(accessToken),
                        new TypeReference<EveThriftResponse<AdminGetRiskCaseDetailResponse>>() {
                        });
                if (Objects.isNull(response) || Objects.isNull(response.getData())
                        || response.getCode() != HttpStatus.OK.value()) {
                    log.error("caseId:" + caseId, new SystemException("获取单个case异常"));
                }
                return response.getData().getData();
            } catch (Exception e) {
                log.error("获取单个case异常,caseId:" + caseId, new SystemException("获取单个case异常"));
            }
            return null;
        }

        public Map<String, AdminGetRiskCaseDetailResponse> getByCaseIdList(List<String> caseIdList,
                String accessToken) {
            return ParallelExecutor.executeParallelTasksAndGetResult("is_check_action", caseIdList,
                            (caseId) -> getByCaseId(caseId, accessToken)).stream()
                    .collect(Collectors.toMap(r -> r.getBase().getCaseId(), Function.identity(), (v1, v2) -> v1));

        }

        /**
         * 构建header
         *
         * @return
         */
        private Map<String, String> buildHeader(String accessToken) {
            Map<String, String> header = new HashMap<>();
            header.put("access-token", accessToken);
            return header;
        }

        /**
         * 构建请求体
         *
         * @param pageNumber
         * @param pageSize
         * @return
         */

        private Map<String, Object> addPageNums(Map<String, Object> postBody, Integer pageNumber,
                Integer pageSize) {
            postBody.put("pageNum", pageNumber);
            postBody.put("pageSize", pageSize);//每100个查询
            return postBody;

        }


    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class CaseDetailDTO {

        private Map<String, AdminGetRiskCaseDetailResponse> caseDetailResponseMap;

        private List<String> caseIdList;


    }
}