package com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCheckingQueueItemTriggerAutoMarkProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import java.util.Collections;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

public class RiskCheckingQueueItemTriggerAutoMarkProcessTest extends SpringTestBase {

    @Resource
    private RiskCheckingQueueItemTriggerAutoMarkProcess riskCheckingQueueItemTriggerAutoMarkProcess;

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Test
    @SneakyThrows
    public void testProcess() {
        String strandingCaseId = "123123123123S05T09";
        String strandingEventId = "123124124567_stranding";
        String improveStrandCaseId = "123124124567S05T01";
        String vin = "LSTM123123213";
        RiskCaseTypeEnum strandingType = RiskCaseTypeEnum.STRANDING;

        DomainEventDTO<RiskCheckingQueueItemDO> domainEventDTO = new DomainEventDTO<>();
        RiskCheckingQueueItemDO riskCheckingQueueItemDO = RiskCheckingQueueItemDO.builder().vin(vin)
                .tmpCaseId(improveStrandCaseId).eventId(strandingCaseId).type(strandingType)
                .source(RiskCaseSourceEnum.BEACON_TOWER).round(0).checking(true).build();
        riskCheckQueueRepository.save(riskCheckingQueueItemDO);
        RiskCaseDO riskCaseDO = RiskCaseDO.builder().caseId(strandingCaseId).eventId(strandingEventId)
                .type(strandingType).source(RiskCaseSourceEnum.BEACON_TOWER).build();
        riskCaseRepository.save(riskCaseDO);
        domainEventDTO.setBefore(Collections.emptyList());
        domainEventDTO.setAfter(Collections.singletonList(riskCheckingQueueItemDO));
        domainEventDTO.setEntry(DomainEventEntryDTO.builder()
                .operateEntry(OperateEnterActionEnum.RISK_AUTO_MARK_TRIGGER_ENTRY)
                .domainClassName(RiskCheckingQueueItemDO.class.getSimpleName()).build());
        domainEventDTO.setTimestamp(System.currentTimeMillis());
        domainEventDTO.setTraceId("123123123123");
        ReflectionTestUtils.setField(lionConfigRepository, "riskAutoMarkConfigDTO",
                RiskAutoCheckConfigDTO.builder().actionChain(Collections.emptyMap())
                        .markTimePeriodList(Lists.newArrayList("00:00:00-21:00:00")).build());
        riskCheckingQueueItemTriggerAutoMarkProcess
                .process(DomainEventFactory.createDomainEventChangeDTO(domainEventDTO, RiskCheckingQueueItemDO.class));
    }
}
