package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.dbrepo;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseVehicleRelationConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCaseVehicleRelationMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseVehicleRelation;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;

public class RiskCaseVehicleRelationRepositoryTest extends RepositoryTestBase {

    @Spy
    private RiskCaseVehicleRelationMapper mapper;

    @Spy
    private RiskCaseVehicleRelationConvertImpl covert;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(riskCaseVehicleRelationRepository, "mapper", mapper);
        ReflectionTestUtils.setField(riskCaseVehicleRelationRepository, "covert", covert);
    }

    @Test
    public void testQueryByParam() {
        // mock selectList
        PowerMockito.when(mapper.selectList(any(), any(), any())).thenReturn(Collections.emptyList());

        // 运行
        RiderCaseVehicleRelationDOParamDTO param = RiderCaseVehicleRelationDOParamDTO.builder().caseId("caseId")
                .build();
        List<RiskCaseVehicleRelationDO> list = riskCaseVehicleRelationRepository.queryByParam(param);

        // 验证
        assertEquals(list, Collections.emptyList());
    }

    @Test
    public void testGetByEventIdAndVin() {
        // mock
        PowerMockito.when(mapper.selectOne(any())).thenReturn(RiskCaseVehicleRelation.builder().build());

        // 运行
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = riskCaseVehicleRelationRepository.getByEventIdAndVin(
                "caseId", "vin");
        // 验证
        assertEquals(riskCaseVehicleRelationDO,
                RiskCaseVehicleRelationDO.builder().isDeleted(IsDeleteEnum.NOT_DELETED).build());

    }

    @Test
    public void testSave() {
        // 运行
        riskCaseVehicleRelationRepository.save(RiskCaseVehicleRelationDO.builder().caseId("caseId").build());
        // 验证
        Mockito.verify(mapper, Mockito.times(1)).save(Mockito.any());
    }

    @Test
    public void testBatchSave() {
        // mock
        riskCaseVehicleRelationRepository.batchSave(
                Collections.singletonList(RiskCaseVehicleRelationDO.builder().caseId("caseId").build()));
        // 验证
        Mockito.verify(mapper, Mockito.times(1)).save(Mockito.any());

    }

}
