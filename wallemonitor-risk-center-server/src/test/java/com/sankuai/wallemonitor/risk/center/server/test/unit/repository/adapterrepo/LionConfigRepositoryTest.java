package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.adapterrepo;

import static groovy.util.GroovyTestCase.assertEquals;

import com.dianping.lion.client.ConfigEventType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.xframe.config.vo.ConfigChangedEvent;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.BroadCastCalcConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import org.junit.Test;

public class LionConfigRepositoryTest extends RepositoryTestBase {

    private final String caseBroadCaseStrategyConfig = "{\n"
            + "    \"1\": {\n"
            + "        \"delaySeconds\": 0,\n"
            + "        \"sourceDelaySeconds\": {\n"
            + "            \"1\": 0,\n"
            + "            \"2\": 0,\n"
            + "            \"3\": 0\n"
            + "        },\n"
            + "        \"createTimeQueryStartOffsetMinutes\": 1,\n"
            + "        \"blackAutocarVersionList\": [],\n"
            + "        \"groupTemplateList\": [\n"
            + "            {\n"
            + "                \"groupIdList\": [\n"
            + "                    64012247676\n"
            + "                ],\n"
            + "                \"groupTemplateName\": \"withImageAndSimple\",\n"
            + "                \"templateId\": 4725,\n"
            + "                \"templateValues\": {\n"
            + "                    \"caseTitleWithStatusAndDurationText\": \"$!{caseTitleWithStatusAndDurationText}\",\n"
            + "                    \"cityAndRoadName\": \"$!{city}-$!{roadName}\",\n"
            + "                    \"caseTitle\": \"$!{caseTitle}\",\n"
            + "                    \"themeColor\": \"$!{themeColor}\",\n"
            + "                    \"occurTime\": \"$!{occurTime}\",\n"
            + "                    \"nowTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop\",\n"
            + "                    \"vehicleNameLink\": \"$!{vehicleNameLink}\",\n"
            + "                    \"vhrAndPurpose\": \"$!{purpose}($!{vhrMode})\",\n"
            + "                    \"goodCaseLink\": \"https://eve.mad.test.sankuai.com/risk/api/admin/markAndDispose?caseId=$!{firstVehicle.caseId}&category=GOOD&subCategory=GOOD_OTHER\",\n"
            + "                    \"badCaseLink\": \"https://eve.mad.test.sankuai.com/risk/api/admin/markAndDispose?caseId=$!{firstVehicle.caseId}&category=BAD&subCategory=OTHER&closeCase=true\",\n"
            + "                    \"occurTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop&occurTime=$!{DatetimeUtil.formatDate($!{firstVehicle.occurTime},\\\"yyyyMMddHHmmss\\\")}\"\n"
            + "                }\n"
            + "            },\n"
            + "            {\n"
            + "                \"groupIdList\": [\n"
            + "                    64012247676\n"
            + "                ],\n"
            + "                \"groupTemplateName\": \"withOutImageAndSimple\",\n"
            + "                \"templateId\": 4662,\n"
            + "                \"templateValues\": {\n"
            + "                    \"roadName\": \"$!{roadName}\",\n"
            + "                    \"caseTitle\": \"$!{caseTitle}\",\n"
            + "                    \"themeColor\": \"$!{themeColor}\",\n"
            + "                    \"occurTime\": \"$!{occurTime}\",\n"
            + "                    \"vehicleNames\": \"$!{vehicleNames}\",\n"
            + "                    \"vhrMode\": \"$!{vhrMode}\",\n"
            + "                    \"purpose\": \"$!{purpose}\",\n"
            + "                    \"city\": \"$!{city}\",\n"
            + "                    \"replayLink\": \"https://walle.sankuai.com/m/csm/vehicle/$!{vin}\",\n"
            + "                    \"replyLink\": \"\",\n"
            + "                    \"btnDisabled\": \"$!{btnDisabled}\",\n"
            + "                    \"btnText\": \"$!{btnText}\",\n"
            + "                    \"occurTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop&occurTime=$!{DatetimeUtil.formatDate($!{firstVehicle.occurTime},\\\"yyyyMMddHHmmss\\\")}\"\n"
            + "                }\n"
            + "            }\n"
            + "        ],\n"
            + "        \"placeCodeWhiteList\": [\n"
            + "            \"ALL\"\n"
            + "        ],\n"
            + "        \"retryMaxSeconds\": 60\n"
            + "    },\n"
            + "    \"2\": {\n"
            + "        \"delaySeconds\": 0,\n"
            + "        \"sourceDelaySeconds\": {\n"
            + "            \"1\": 0,\n"
            + "            \"2\": 0,\n"
            + "            \"3\": 0\n"
            + "        },\n"
            + "        \"createTimeQueryStartOffsetMinutes\": 1,\n"
            + "        \"groupTemplateList\": [\n"
            + "            {\n"
            + "                \"templateId\": 4662,\n"
            + "                \"groupTemplateName\": \"withOutImageAndSimple\",\n"
            + "                \"groupIdList\": [\n"
            + "                    64012247676,\n"
            + "                    64012250440\n"
            + "                ],\n"
            + "                \"templateValues\": {\n"
            + "                    \"roadName\": \"$!{roadName}\",\n"
            + "                    \"caseTitle\": \"$!{caseTitle}\",\n"
            + "                    \"themeColor\": \"$!{themeColor}\",\n"
            + "                    \"occurTime\": \"$!{occurTime}\",\n"
            + "                    \"vehicleNames\": \"$!{vehicleNames}\",\n"
            + "                    \"vhrMode\": \"$!{vhrMode}\",\n"
            + "                    \"purpose\": \"$!{purpose}\",\n"
            + "                    \"city\": \"$!{city}\",\n"
            + "                    \"replayLink\": \"https://walledata.mad.test.sankuai.com/m/csm/vehicle/$!{vin}#perspectives=loop&startTime=$!{DatetimeUtil.formatTime($!{DatetimeUtil.getNSecondsBeforeDateTime($!DatetimeUtil.convertDatetimeStr2Date($!{occurTime}),5)})}\",\n"
            + "                    \"replyLink\": \"\",\n"
            + "                    \"btnDisabled\": \"$!{btnDisabled}\",\n"
            + "                    \"btnText\": \"$!{btnText}\",\n"
            + "                    \"occurTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop&occurTime=$!{DatetimeUtil.formatDate($!{firstVehicle.occurTime},\\\"yyyyMMddHHmmss\\\")}\"\n"
            + "                }\n"
            + "            }\n"
            + "        ],\n"
            + "        \"blackAutocarVersionList\": [\n"
            + "            \"\"\n"
            + "        ],\n"
            + "        \"placeCodeWhiteList\": [\n"
            + "            \"ALL\"\n"
            + "        ],\n"
            + "        \"retryMaxSeconds\": \"20\"\n"
            + "    },\n"
            + "    \"3\": {\n"
            + "        \"delaySeconds\": 0,\n"
            + "        \"sourceDelaySeconds\": {\n"
            + "            \"1\": 0,\n"
            + "            \"2\": 0,\n"
            + "            \"3\": 0\n"
            + "        },\n"
            + "        \"createTimeQueryStartOffsetMinutes\": 1,\n"
            + "        \"groupTemplateList\": [\n"
            + "            {\n"
            + "                \"groupIdList\": [\n"
            + "                    64012247676,\n"
            + "                    64012250440\n"
            + "                ],\n"
            + "                \"templateId\": 4663,\n"
            + "                \"groupTemplateName\": \"withOutImageAndSimple\",\n"
            + "                \"templateValues\": {\n"
            + "                    \"roadName\": \"$!{roadName}\",\n"
            + "                    \"caseTitle\": \"$!{caseTitle}\",\n"
            + "                    \"themeColor\": \"$!{themeColor}\",\n"
            + "                    \"occurTime\": \"$!{occurTime}\",\n"
            + "                    \"vehicleNames\": \"$!{vehicleNames}\",\n"
            + "                    \"vhrMode\": \"$!{vhrMode}\",\n"
            + "                    \"purpose\": \"$!{purpose}\",\n"
            + "                    \"city\": \"$!{city}\",\n"
            + "                    \"replayLink\": \"https://walledata.mad.test.sankuai.com/m/csm/vehicle/$!{vin}#perspectives=loop&startTime=$!{DatetimeUtil.formatTime($!{DatetimeUtil.getNSecondsBeforeDateTime($!DatetimeUtil.convertDatetimeStr2Date($!{occurTime}),5)})}\",\n"
            + "                    \"replyLink\": \"\",\n"
            + "                    \"btnDisabled\": \"$!{btnDisabled}\",\n"
            + "                    \"btnText\": \"$!{btnText}\",\n"
            + "                    \"vehicleInfoStr\": \"$!{vehicleInfoStr}\",\n"
            + "                    \"vehicleNumber\": \"$!{vehicleNumber}\",\n"
            + "                    \"vehicleId\": \"$!{vehicleId}\",\n"
            + "                    \"occurTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop&occurTime=$!{DatetimeUtil.formatDate($!{firstVehicle.occurTime},\\\"yyyyMMddHHmmss\\\")}\"\n"
            + "                }\n"
            + "            }\n"
            + "        ],\n"
            + "        \"blackAutocarVersionList\": [\n"
            + "            \"\"\n"
            + "        ],\n"
            + "        \"placeCodeWhiteList\": [\n"
            + "            \"ALL\"\n"
            + "        ],\n"
            + "        \"retryMaxSeconds\": \"20\"\n"
            + "    }\n"
            + "}";

    @Test
    public void testInitBroadCastCalcConfigDTO() {
        // 运行
        String value = "\n{\"groupIdList\":[64012247676,64012250440],\"placeCodeList\":[\"hualikan\",\"mapo\"],\"templateId\":\"4422\"}";
        lionConfigRepository.initBroadCastCalcConfigDTO(
                new ConfigChangedEvent(LionKeyConstant.LION_KEY_AUTO_CAR_EVENT_CODE, "group", "oldValue",
                        value,
                        ConfigEventType.CONFIG_CHANGE)
        );

        // 验证
        assertEquals(JacksonUtils.from(value, BroadCastCalcConfigDTO.class),
                lionConfigRepository.getBroadCastCalcConfig());

    }

    /**
     * 使用反射测试configListener私有方法
     */
    @Test
    public void testConfigListenerWithBlankValue() throws Exception {
        // 修改方法访问权限
        ConfigChangedEvent configEvent = new ConfigChangedEvent(LionKeyConstant.LION_KEY_RISK_CASE_BROADCAST_CONFIG, "",
                "", caseBroadCaseStrategyConfig, ConfigEventType.CONFIG_CHANGE);
        Method method = LionConfigRepositoryImpl.class.getDeclaredMethod("configListener", ConfigChangedEvent.class);
        method.setAccessible(true);
        method.invoke(lionConfigRepository, configEvent);

        // 验证结果
        Map<String, BroadCastStrategyConfigDTO> config = JacksonUtils.from(caseBroadCaseStrategyConfig,
                new TypeReference<Map<String, BroadCastStrategyConfigDTO>>() {
                });
        Map<RiskCaseTypeEnum, BroadCastStrategyConfigDTO> expectedMap = config.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> RiskCaseTypeEnum.findByValue(Integer.valueOf(entry.getKey())),
                        Entry::getValue));
        assertEquals(expectedMap, lionConfigRepository.getCaseType2BroadCastStrategyConfig());

    }

}


