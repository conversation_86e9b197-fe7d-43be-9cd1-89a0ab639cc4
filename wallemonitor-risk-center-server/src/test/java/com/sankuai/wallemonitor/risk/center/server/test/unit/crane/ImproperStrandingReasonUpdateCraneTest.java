package com.sankuai.wallemonitor.risk.center.server.test.unit.crane;

import com.sankuai.wallemonitor.risk.center.infra.dto.ImproperStrandingReasonUpdateCraneConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ManualParkingMarkConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import com.sankuai.wallemonitor.risk.center.server.crane.ImproperStrandingReasonUpdateCrane;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

public class ImproperStrandingReasonUpdateCraneTest extends ServiceTestBase {

    @InjectMocks
    @Spy
    private ImproperStrandingReasonUpdateCrane improperStrandingReasonUpdateCrane;

    @Test
    public void testRun() throws Exception {
        List<RiskCaseDO> riskCaseDOList = new ArrayList<>();
        RiskCaseDO riskCaseDO = RiskCaseDO.builder()
                .caseId("123")
                .status(RiskCaseStatusEnum.IN_DISPOSAL)
                .source(RiskCaseSourceEnum.STATUS_MONITOR)
                .occurTime(new Date())
                .recallTime(new Date())
                .build();
        riskCaseDOList.add(riskCaseDO);
        Mockito.when(riskCaseRepository.queryByParam(Mockito.any())).thenReturn(riskCaseDOList);

        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = new ArrayList<>();
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = RiskCaseVehicleRelationDO.builder()
                .caseId("123")
                .vin("vin1")
                .occurTime(new Date())
                .vehicleSnapshotInfo(VehicleInfoDO.builder()
                        .vin("vin1")
                        .withAccidentOrder(true)
                        .withRescueOrder(true)
                        .vhr(VHRModeEnum.VHR_GREAT_THAN_ONE)
                        .build())
                .build();
        riskCaseVehicleRelationDOList.add(riskCaseVehicleRelationDO);
        Mockito.when(riskCaseVehicleRelationRepository.queryByParam(Mockito.any()))
                .thenReturn(riskCaseVehicleRelationDOList);

        ManualParkingMarkConfigDTO manualParkingMarkConfigDTO = new ManualParkingMarkConfigDTO();
        manualParkingMarkConfigDTO.setTimeoutCancelCheckManualParkingMins(5);
        manualParkingMarkConfigDTO.setQueryDriveModeNSecondsBeforeStranding(10);
        Mockito.when(lionConfigRepository.getManualParkingMarkConfig())
                .thenReturn(manualParkingMarkConfigDTO);

        List<VehicleEveInfoVTO> vehicleEveInfoVTOList = new ArrayList<>();
        VehicleEveInfoVTO vehicleEveInfoVTO = VehicleEveInfoVTO.builder()
                .vin("vin1")
                .withAccidentOrder(true)
                .withRescueOrder(true)
                .build();
        vehicleEveInfoVTOList.add(vehicleEveInfoVTO);
        Mockito.when(vehicleAdapter.queryRuntimeVehicleInfo(Mockito.any())).thenReturn(vehicleEveInfoVTOList);

        Map<String, Boolean> vehicleReTicketMap = new HashMap<>();
        vehicleReTicketMap.put("vin1", true);
        Mockito.when(reTicketAdapter.queryByVinList(Mockito.any())).thenReturn(vehicleReTicketMap);

        Mockito.when(caseMarkInfoRepository.queryByParam(Mockito.any())).thenReturn(new ArrayList<>());

        ReflectionTestUtils.setField(improperStrandingReasonUpdateCrane,
                "queryBeforeTimeMins",
                120);

        // 模拟 ImproperStrandingReasonUpdateCraneConfigDTO
        ImproperStrandingReasonUpdateCraneConfigDTO configDTO = new ImproperStrandingReasonUpdateCraneConfigDTO();
        configDTO.setSourceList(Arrays.asList(3, 5));
        configDTO.setTypeList(Collections.singletonList(9));
        ReflectionTestUtils.setField(improperStrandingReasonUpdateCrane, "configDTO", configDTO);
        improperStrandingReasonUpdateCrane.run();
        Mockito.verify(caseMarkInfoRepository).save(Mockito.any());

    }

}
