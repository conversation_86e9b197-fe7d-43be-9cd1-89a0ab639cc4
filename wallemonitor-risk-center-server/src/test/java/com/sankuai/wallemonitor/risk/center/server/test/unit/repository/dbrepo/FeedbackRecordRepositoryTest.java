package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.convert.FeedbackRecordConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.FeedbackRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.model.core.FeedbackRecordDO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;

public class FeedbackRecordRepositoryTest extends RepositoryTestBase {

    @Spy
    private FeedbackRecordMapper mapper;

    @Spy
    private FeedbackRecordConvertImpl covert;

    private FeedbackRecordDO feedbackRecordDO;

    @Before
    @SneakyThrows
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 注入 mapper 和 covert
        ReflectionTestUtils.setField(feedbackRecordRepository, "mapper", mapper);
        ReflectionTestUtils.setField(feedbackRecordRepository, "covert", covert);
        feedbackRecordDO = FeedbackRecordDO.builder().id(1L).build();


    }

    @Test
    @SneakyThrows
    public void testSave() {
        // mock
        feedbackRecordRepository.save(feedbackRecordDO);
        Mockito.verify(mapper, Mockito.times(1)).save(Mockito.any());
    }

}
