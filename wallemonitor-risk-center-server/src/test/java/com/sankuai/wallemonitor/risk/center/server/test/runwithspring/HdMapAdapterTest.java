package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter.SearchNearbyRequestVTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.MrmCallFilterRuleHitLogDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.HdMapRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.MrmCallFilterRuleHitLogRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskRestrictQueryDTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

public class HdMapAdapterTest extends SpringTestBase {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private HdMapRepository hdMapRepository;

    @Resource
    private MrmCallFilterRuleHitLogRepository mrmCallFilterRuleHitLogRepository;

    @Test
    public void testGetDistance() {
        //lon=116.54358368870372&lat=40.10001387859567&version=hualikan_hdmap_v5.185.0.r&type=LIVING_AREA

        //
        //        System.out.println(JacksonUtils.to(hdMapAdapter.queryHdMapAreaWgs84(Lists.newArrayList("LIVING_AREA"),
        //                new PositionDO(40.10001387859567, 116.54358368870,
        //                        CoordinateSystemEnum.WGS84),
        //                vehicleAdapter.getVehicleHdMapVersion("LNBJ111111111111111"), "LNBJ111111111111111", 5D)));
        // "{"area":"shunyi","restrictType":["DISTRICT_ENTRANCE","CLEAR_AREA","BUS_STATION","CROSSWALK","DIVERSION_ZONE","REFUGE_ISLAND","TRAM_DRIVE_AREA"],"positionDO":{"latitude":40.196225,"longitude":116.633862,"coordinateSystem":"WGS84"},"range":20.0}
        List<String> typeList = JacksonUtils.from(
                "[\"DISTRICT_ENTRANCE\",\"CLEAR_AREA\",\"BUS_STATION\",\"CROSSWALK\",\"DIVERSION_ZONE\",\"REFUGE_ISLAND\",\"TRAM_DRIVE_AREA\"]",
                new TypeReference<List<String>>() {
                });
        PositionDO positionDO = JacksonUtils.from(
                "{\"latitude\":40.173502,\"longitude\":116.573389,\"coordinateSystem\":\"WGS84\"}",
                new TypeReference<PositionDO>() {
                });
        SearchNearbyRequestVTO searchNearbyRequestVTO = SearchNearbyRequestVTO.builder().hdMapEnum(HdMapEnum.OBJECT)
                .area("shunyi").positionDO(positionDO).restrictType(typeList).range(20D).build();

        hdMapAdapter.searchNearby(searchNearbyRequestVTO);
//
//        hdMapAdapter.queryHdMapAreaWgs84(, new PositionDO(40.10001387859567, 116.54358368870,
//                CoordinateSystemEnum.WGS84),
        RiskRestrictQueryDTO riskRestrictQueryDTO = RiskRestrictQueryDTO.builder().restrictTypes(typeList)
                .position(positionDO).meter(20).expandMeter(2).hdMapVersion("shunyi_hdmap_v5.103.1.t").build();

        hdMapRepository.inRestrictParking(riskRestrictQueryDTO);

    }

    @Test
    public void testQueryHdMapArea() {

        hdMapAdapter.getUtmZoneByArea("shenzhenlonghua");

    }

    @Test
    public void testQueryHdMapAreaWgs84() {
        MrmCallFilterRuleHitLogDO hitLogDO = MrmCallFilterRuleHitLogDO.builder()
                .caseId("11111111")
                .filterRule("22222222")
                .build();
        mrmCallFilterRuleHitLogRepository.save(hitLogDO);
    }

    @Test
    public void testSearchNearBy() {
        PositionDO location1 = PositionDO.builder().latitude(22.734180410022503)
                .longitude(114.37002779212429).coordinateSystem(CoordinateSystemEnum.WGS84).build();

        PositionDO location2 = PositionDO.builder().latitude(22.734010068296094)
                .longitude(114.37016641859951).coordinateSystem(CoordinateSystemEnum.WGS84).build();

        List<String> ristrictTypeList = Lists.newArrayList("CITY_DRIVING",
                "MIXED",
                "BIKING");

        List<HdMapElementGeoDO> lane1 = hdMapAdapter.searchNearby(SearchNearbyRequestVTO.builder().positionDO(location1).hdMapEnum(HdMapEnum.LANE_POLYGON)
                .range(20.0).area("shenzhenpingshan").restrictType(ristrictTypeList).build());

        List<HdMapElementGeoDO> lane2 = hdMapAdapter.searchNearby(SearchNearbyRequestVTO.builder().positionDO(location2).hdMapEnum(HdMapEnum.LANE_POLYGON)
                .range(20.0).area("shenzhenpingshan").restrictType(ristrictTypeList).build());

        System.out.println(lane1);

        System.out.println(lane2);

    }

}