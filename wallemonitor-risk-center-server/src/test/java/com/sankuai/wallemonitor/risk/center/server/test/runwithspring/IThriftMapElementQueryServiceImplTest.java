package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.LaneQueryRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.request.MapElementRequestDTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import com.sankuai.wallemonitor.risk.center.server.thrift.IThriftMapElementQueryServiceImpl;
import javax.annotation.Resource;
import org.junit.Test;

public class IThriftMapElementQueryServiceImplTest extends SpringTestBase {

    @Resource
    private IThriftMapElementQueryServiceImpl iThriftLaneQueryService;

    @Test
    public void testQueryLane() {
        MapElementRequestDTO laneQueryRequestDTO = JacksonUtils.from("{\n" + "  \"distance\": 300,\n"
                + "  \"hdMapVersion\": \"shenzhenpingshan_admap_v5.403.0.r\",\n" + "  \"latitude\": 22.73354,\n"
                + "  \"longitude\": 114.370596,\n" + "  \"mapType\": \"junction\"\n" + "}", MapElementRequestDTO.class);
        iThriftLaneQueryService.searchMapElementByDistance(laneQueryRequestDTO);

    }

    public static void main(String[] args) {
        LaneQueryRequestDTO laneQueryRequestDTO = new LaneQueryRequestDTO();
        laneQueryRequestDTO.setDistance(100.0);
        laneQueryRequestDTO.setLongitude(114.05);
        laneQueryRequestDTO.setLatitude(22.54);
        laneQueryRequestDTO.setHdMapVersion("hualikan_hdmap_v5.185.0.r");
        System.out.println(JacksonUtils.to(laneQueryRequestDTO));
    }

}