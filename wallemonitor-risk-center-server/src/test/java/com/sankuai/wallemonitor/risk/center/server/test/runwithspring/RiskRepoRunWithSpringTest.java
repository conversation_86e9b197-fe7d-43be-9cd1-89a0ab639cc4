package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MrmRoleEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.UuidUtil;
import com.sankuai.wallemonitor.risk.center.server.StartApp;
import java.util.Arrays;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public class RiskRepoRunWithSpringTest {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository vehicleRelationDORepository;

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Test
    public void testTaskRepoTest() {
        Date date = new Date();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, Arrays.asList(""),
                RiskCaseSourceEnum.SAFEGUARD_SYSTEM, RiskCaseTypeEnum.VEHICLE_STAND_STILL, System.currentTimeMillis());
        String eventId = UuidUtil.uuid();
        String vin = UuidUtil.uuid();
        RiskCaseDO taskDO = RiskCaseDO.builder()
                .caseId(caseId)
                .type(RiskCaseTypeEnum.VEHICLE_CONGESTION)
                .eventId(eventId)
                .status(RiskCaseStatusEnum.NO_DISPOSAL)
                .build();
        riskCaseRepository.save(taskDO);
        Assert.assertTrue("任务不可以为空", riskCaseRepository.getByCaseId(taskDO.getCaseId()) != null);
        Assert.assertTrue("任务不可以为空", CollectionUtils.isNotEmpty(riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder()
                        .caseIdList(Arrays.asList(caseId))
                        .eventId(eventId)
                        .createTimeRange(TimePeriod.builder().beginDate(date).endDate(new Date())
                                .build()).build())));
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = RiskCaseVehicleRelationDO.builder()
                .caseId(caseId)
                .status(RiskCaseVehicleStatusEnum.ASSIGNED)
                .eventId(eventId)
                .vin(vin)
                .vehicleSnapshotInfo(VehicleInfoDO.builder().build())
                .extInfo(RiskVehicleExtInfoDO.builder().role(MrmRoleEnum.MRM_DRIVER).build())
                .build();
        vehicleRelationDORepository.save(riskCaseVehicleRelationDO);
        Assert.assertTrue("关联关系不可以为空", vehicleRelationDORepository.getByEventIdAndVin(eventId, vin) != null);
        Assert.assertTrue("关联关系不可以为空", CollectionUtils.isNotEmpty(vehicleRelationDORepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder()
                        .caseIdList(Arrays.asList(caseId))
                        .eventId(eventId)
                        .vinList(Arrays.asList(vin)).build())));
    }

}
