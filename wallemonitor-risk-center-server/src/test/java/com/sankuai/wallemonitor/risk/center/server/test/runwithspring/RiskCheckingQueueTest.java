package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.domain.process.RiskCheckingQueueItemCheckingProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCheckingQueueItemFactory;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCheckingQueueItemFactory.CreateRiskCheckingQueueDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Collections;
import java.util.Date;
import javax.annotation.Resource;
import org.apache.thrift.TException;
import org.junit.Test;


public class RiskCheckingQueueTest extends SpringTestBase {

    @Resource
    private RiskCheckingQueueItemCheckingProcess checkingQueueItemCheckingProcess;

    @Resource
    private RiskCheckQueueRepository checkQueueRepository;

    @Test
    public void test() throws TException {
        checkQueueRepository.save(
                RiskCheckingQueueItemFactory.createRiskCheckingQueueItem(CreateRiskCheckingQueueDOParamDTO.builder()
                        .caseId("M324820341211115337S03T01").eventId("45645114").occurTime(new Date().getTime())
                        .recallTime(new Date().getTime()).type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                        .source(RiskCaseSourceEnum.BEACON_TOWER).vin("LMTZSV023MC023286").build()));
        DomainEventDTO<RiskCheckingQueueItemDO> eventDTO = DomainEventDTO.<RiskCheckingQueueItemDO>builder()
                .after(Collections.singletonList(RiskCheckingQueueItemDO.builder()
                        .checking(true)
                        .eventId("25645114")
                        .tmpCaseId("M324820241211115337S03T01")
                        .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                        .round(0)
                        .source(RiskCaseSourceEnum.BEACON_TOWER)
                        .occurTime(null)
                        .vin("LMTZSV023MC023286")
                        .build()))
                .build();
        checkingQueueItemCheckingProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCheckingQueueItemDO.class));
    }
}
