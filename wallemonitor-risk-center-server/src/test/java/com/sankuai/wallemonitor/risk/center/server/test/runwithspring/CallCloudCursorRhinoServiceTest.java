package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.domain.service.impl.CallCloudCursorRhinoService;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class CallCloudCursorRhinoServiceTest extends SpringTestBase {

    @Resource
    private CallCloudCursorRhinoService callCloudCursorRhinoService;

    @Test
    @SneakyThrows
    public void testCallCloudCursorRhinoService() {
        while (true) {
            try{
                callCloudCursorRhinoService.callCloudCursor(CallSecuritySystemStrategyConfigDTO.builder().build(),
                        "testVin", "A_POI");
            }catch (Exception e){

            }

            try{
                callCloudCursorRhinoService.callCloudCursor(CallSecuritySystemStrategyConfigDTO.builder().build(),
                        "testVin", "B_POI");
            }catch (Exception e){

            }

            Thread.sleep(1000L);
        }

    }

}