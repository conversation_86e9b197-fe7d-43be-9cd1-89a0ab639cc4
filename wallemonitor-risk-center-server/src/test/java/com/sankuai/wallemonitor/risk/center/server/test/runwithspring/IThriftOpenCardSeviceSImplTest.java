package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import com.sankuai.wallemonitor.risk.center.server.thrift.IThriftOpenCardSeviceSImpl;
import com.sankuai.xm.openplatform.api.entity.PullCardOperator;
import com.sankuai.xm.openplatform.api.entity.PullCardReq;
import com.sankuai.xm.openplatform.api.entity.PullCardRequestParam;
import javax.annotation.Resource;
import org.junit.Test;

public class IThriftOpenCardSeviceSImplTest extends SpringTestBase {

    @Resource
    private IThriftOpenCardSeviceSImpl iThriftOpenCardSeviceSImpl;

    @Test
    public void testCreateCard() throws Exception {
        //PullCardReq
        PullCardReq pullCardReq = new PullCardReq();
        PullCardRequestParam pullCardRequestParam = new PullCardRequestParam();
        pullCardRequestParam.setRequestId("M603320241104222901S01T01" + "_" + System.currentTimeMillis());
        PullCardOperator pullCardOperator = new PullCardOperator();
        pullCardOperator.setEmpId(123456L);
        pullCardReq.setOperator(pullCardOperator);
        pullCardReq.setRequestParam(pullCardRequestParam);
        System.out.println(JacksonUtils.to(iThriftOpenCardSeviceSImpl.pullCard(pullCardReq)));
    }


}