package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;


import com.google.common.collect.Lists;
import com.sankuai.walle.operator.client.util.CoordinateTransferUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskMarkService;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;


@Slf4j
public class RiskMarkServiceTest extends SpringTestBase {

    @Resource
    RiskMarkService riskMarkService;

    @Resource
    VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;


    @Test
    public void testTriggerAutoMark() {
        // 存簇车辆的runtime信息
        String vin = "agent";
        String version = "delay_recall_area";

        Map<String, Double> stringDoubleMap = CoordinateTransferUtils.gcj02ToWgs84(114.37499084763228, 22.731505886554388);
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = VehicleRuntimeInfoContextDO
                .builder().vin(vin).lat(stringDoubleMap.get(("lat")) + "").lng(stringDoubleMap.get("lon") + "").build();
        vehicleRuntimeInfoContextRepository.updateCache(vehicleRuntimeInfoContextDO, System.currentTimeMillis());

        RiskCheckingQueueItemDO riskCheckingQueueItemDO = RiskCheckingQueueItemDO.builder().vin(vin).round(0).occurTime(new Date()).tmpCaseId("asasasaas").eventId("babababab").build();


        riskMarkService.triggerAutoMark(Lists.newArrayList(riskCheckingQueueItemDO), version);
    }
}
