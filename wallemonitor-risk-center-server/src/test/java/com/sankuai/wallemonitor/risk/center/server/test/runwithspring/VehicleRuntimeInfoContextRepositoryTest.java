package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.enums.SquirrelCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class VehicleRuntimeInfoContextRepositoryTest extends SpringTestBase {

    @Resource
    private VehicleRuntimeInfoContextRepository repository;

    @Resource
    private SquirrelAdapter squirrelAdapter;

    @Test
    @SneakyThrows
    public void test() {
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = repository.getByVin("LMTZSV023MC023286");
        vehicleRuntimeInfoContextDO.setLastUpdateTime(new Date());
        vehicleRuntimeInfoContextDO.setSpeed(0.0);
        repository.updateCache(vehicleRuntimeInfoContextDO, System.currentTimeMillis());
        Thread.sleep(71000);
        vehicleRuntimeInfoContextDO = repository.getByVin("LMTZSV023MC023286");
        vehicleRuntimeInfoContextDO.setLastUpdateTime(new Date());
        vehicleRuntimeInfoContextDO.setSpeed(10.0);
        repository.updateCache(vehicleRuntimeInfoContextDO, System.currentTimeMillis());
    }

    @Test
    @SneakyThrows
    public void test1() {
        Map<String, Long> vin2Timestamp = squirrelAdapter.hAllGet(SquirrelCategoryEnum.VEHICLE_RUNTIME_CONTEXT,
                "ALL_VIN");
        vin2Timestamp.forEach((vin, timestamp) -> {
            squirrelAdapter.hsetObject(SquirrelCategoryEnum.VEHICLE_RUNTIME_CONTEXT, "ALL_VIN", vin,
                    new Date().getTime());
            VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = Optional.ofNullable(repository.getByVin(vin))
                    .orElseGet(() -> repository.getFromCache(vin));
            vehicleRuntimeInfoContextDO.setSpeed(0.0);
            repository.updateCache(vehicleRuntimeInfoContextDO, System.currentTimeMillis());
        });

    }

}
