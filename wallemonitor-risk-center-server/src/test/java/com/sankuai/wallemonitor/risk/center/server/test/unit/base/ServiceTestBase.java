package com.sankuai.wallemonitor.risk.center.server.test.unit.base;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.walleeve.utils.JwtUtil;
import com.sankuai.wallemonitor.risk.center.domain.component.RiskHandleCommonCompute;
import com.sankuai.wallemonitor.risk.center.domain.component.WechatAuthAdminService;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseMessageNoticeProcess;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseQueryService;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.DomainEventServiceImpl;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.DomainEventServiceImpl.DomainEventProcessProvider;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.FeedbackRecordServiceImpl;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.RiskCaseMessageServiceImpl;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.RiskCaseOperateServiceImpl;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.UserNoticeAdminServiceImpl;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.GisAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.PlaceAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.ReTicketAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.UserInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.GisInfoVTO2DOConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.convert.PositionConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseMessageDTOConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.convert.VehicleVTO2DOConvertImpl;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SSOLogInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.DomainEventProcessRecordRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.GisInfoRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.IDGenerateRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.VehicleInfoRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRelatedServiceRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.CaseSortDataRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.FeedbackRecordRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.RiskCaseRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.SafetyAreaRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.UserNoticeReadRecordRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.VehicleTrafficRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.utils.UuidUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.VelocityUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.SpringUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.lion.LionConfigUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils.LockUtil;
import com.sankuai.wallemonitor.risk.center.server.aop.OperateEnterAspect.DomainEventMessageProducerProvider;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.logging.log4j.core.util.ExecutorServices;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.MockitoRule;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(MockitoJUnitRunner.Silent.class)
@PrepareForTest({TraceExecutors.class, ExecutorServices.class, UserInfoContext.class, SpringUtils.class,
        LionConfigUtils.class, Lion.class, DomainEventServiceImpl.class, CommonConstant.class, VelocityUtils.class,
        JwtUtil.class})
@PowerMockIgnore({"com.fasterxml.*",
        "com.dianping.cat.*",
        "com.dianping.pigeon.*",
        "com.meituan.scribe.*",
        "com.meituan.service.*",
        "com.meituan.service.inf.kms.*",
        "com.meituan.mtrace.*",
        "com.sankuai.inf.*",
        "com.sun.*",
        "javax.*",
        "javax.management.*",
        "javax.net.ssl.*",
        "org.apache.logging.*",
        "org.apache.*",
        "org.slf4j.*",
        "org.w3c.*",
        "org.xml.*",
        "sun.*"})
@Ignore
public abstract class ServiceTestBase {

    /*****************************************服务层**********************************************/
    @InjectMocks
    @Spy
    public RiskCaseOperateServiceImpl riskCaseOperateService;

    @InjectMocks
    @Spy
    public DomainEventServiceImpl domainEventService;

    @InjectMocks
    @Spy
    public DomainEventProcessProvider eventProcessAdapter;

    @InjectMocks
    @Spy
    public DomainEventMessageProducerProvider producerProvider;

    @InjectMocks
    @Spy
    public RiskCaseMessageServiceImpl riskCaseMessageService;

    @InjectMocks
    @Spy
    public RiskCaseMessageNoticeProcess riskCaseMessageNoticeProcess;

    @InjectMocks
    @Spy
    private FeedbackRecordServiceImpl feedbackRecordService;

    @InjectMocks
    @Spy
    private UserNoticeAdminServiceImpl userNoticeAdminService;

    @InjectMocks
    @Spy
    private WechatAuthAdminService wechatAuthAdminService;

    /*****************************************工具类**********************************************/
    @InjectMocks
    @Spy
    public LockUtils redisLock;

    @Mock
    public RedisStoreClient redisStoreClient;

    @Mock
    public LockUtil lockService;

    @InjectMocks
    @Spy
    public VehicleVTO2DOConvertImpl vehicleInfoConvert;

    @InjectMocks
    @Spy
    public GisInfoVTO2DOConvertImpl convert;

    @InjectMocks
    @Spy
    public RiskCaseFactory riskCaseFactory;

    @InjectMocks
    @Spy
    public RiskCaseMessageDTOConvertImpl riskCaseMessageDTOConvert;

    @InjectMocks
    @Spy
    public PositionConvertImpl positionDOConvert;

    @InjectMocks
    @Spy
    public RiskHandleCommonCompute riskHandleCommonCompute;



    /*****************************************仓储层**********************************************/
    @Mock
    public IDGenerateRepositoryImpl idGenerateRepository;

    @Mock
    public SafetyAreaRepositoryImpl safetyAreaRepository;

    @Mock
    public VehicleInfoRepositoryImpl vehicleInfoRepository;

    @Mock
    public RiskCaseRepositoryImpl riskCaseRepository;

    @Mock
    public CaseSortDataRepositoryImpl sortDataRepository;

    @Mock
    public RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Mock
    public VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Mock
    public RiskCaseRelatedServiceRecordRepository riskCaseRelatedServiceRecordRepository;

    @Mock
    public GisInfoRepositoryImpl gisInfoRepository;

    @Mock
    public DomainEventProcessRecordRepositoryImpl eventProcessRecordRepository;

    @Mock
    public LionConfigRepositoryImpl lionConfigRepository;

    @Mock
    public FeedbackRecordRepositoryImpl feedbackRecordRepository;

    @Mock
    public VehicleTrafficRepositoryImpl vehicleContextRepository;

    @Mock
    public UserNoticeReadRecordRepositoryImpl userNoticeReadRecordRepository;

    @Mock
    public CaseMarkInfoRepository caseMarkInfoRepository;


    /*****************************************DAO层**********************************************/

    @Mock
    public VehicleAdapter vehicleAdapter;

    @Mock
    public ReTicketAdapter reTicketAdapter;

    @Mock
    public SquirrelAdapter squirrelAdapter;

    @Mock
    public DxNoticeAdapter dxNoticeAdapter;

    @Mock
    public PlaceAdapter placeAdapter;

    @Mock
    public RiskCaseQueryService riskCaseQueryService;

    @Spy
    public GisAdapter gisAdapter;

    @BeforeClass
    @SneakyThrows
    public static void setUpBeforeClass() {
        PowerMockito.mockStatic(TraceExecutors.class);
        PowerMockito.when(TraceExecutors.getTraceExecutorService(Mockito.any())).thenReturn(null);

        PowerMockito.mockStatic(ExecutorServices.class);

    }

    @Before
    public void setUpBeforeMethod() {
        mockRedisAndLock();
        mockUserInfo("test");
    }


    @Rule
    public MockitoRule rule = MockitoJUnit.rule();

    /**
     * mock sso用户mis 场景：web页、app用户
     *
     * @param mis
     */
    public void mockUserInfo(String mis) {
        PowerMockito.mockStatic(UserInfoContext.class);
        PowerMockito.when(UserInfoContext.getUserInfo()).thenReturn(SSOLogInfoDO.builder().login(mis).build());
        PowerMockito.when(UserInfoContext.getUserMis()).thenReturn(mis);
    }

    /**
     * mock 分布式锁
     */
    public void mockRedisAndLock() {
        ReflectionTestUtils.setField(redisLock, "batchLockExpireSeconds", 2);
        ReflectionTestUtils.setField(redisLock, "batchLockSleepRetryMilliseconds", 100);
        Mockito.doReturn(true).when(lockService).lockWithoutRetry(anyString(), anyString(), anyInt());
    }

    /**
     * mock SpringUtils 获取AppKey
     */
    public void mockSpringUtilsGetAppKey() {
        PowerMockito.mockStatic(SpringUtils.class);
        PowerMockito.when(SpringUtils.getPropertiesValue("app.key"))
                .thenReturn("com.sankuai.wallemonitor.risk.center");
    }

    public <T> void injectField(T object, Class<T> objectClass, List<String> fieldNameList, List<Object> fieldValue)
            throws Exception {
        Field[] fields = objectClass.getDeclaredFields();
        Map<String, Field> fieldMap = Arrays.stream(fields)
                .collect(Collectors.toMap(Field::getName, x -> x, (o1, o2) -> o1));
        for (int i = 0; i < fieldNameList.size(); i++) {
            String fileName = fieldNameList.get(i);
            Object fileValue = fieldValue.get(i);
            Field field = fieldMap.get(fileName);
            if (field == null) {
                continue;
            }
            field.setAccessible(true);
            field.set(object, fileValue);
        }

    }

    /**
     * mock 生成分布式id
     */
    public void mockGenerateByKey() {
        when(idGenerateRepository.generateByKey(any(), Arrays.asList(""), RiskCaseSourceEnum.SAFEGUARD_SYSTEM,
                RiskCaseTypeEnum.VEHICLE_STAND_STILL,
                System.currentTimeMillis())).thenReturn(
                String.valueOf(RandomUtils.nextLong(10000000L, 99999999L)));
    }

    /**
     * mock idGenerateRepository
     */
    public void mockIdGenerateRepository() {
        Mockito.when(idGenerateRepository.generateByKey(any(), anyList(), any(),
                any(), any())).thenAnswer(invocation -> {
            IDBizEnum idBizEnum = invocation.getArgument(0);
            switch (idBizEnum) {
                case RISK_CASE_ID: {
                    return UuidUtil.uuid();
                }
                default: {
                    return null;
                }
            }
        });
    }

    /**
     * mock 风险关系查询 queryByParam
     */
    public void mockRiskCaseVehicleRelation(DataTestBase dataTestBase) {
        Mockito.when(riskCaseVehicleRelationRepository
                .queryByParam(Mockito.any(RiderCaseVehicleRelationDOParamDTO.class))).thenAnswer(
                invocation -> {
                    RiderCaseVehicleRelationDOParamDTO param = invocation.getArgument(0);
                    Stream<RiskCaseVehicleRelationDO> stream = dataTestBase.riskCaseVehicleRelationDOList.stream();
                    // eventId 条件
                    if (StringUtils.isNotBlank(param.getEventId())) {
                        stream = stream.filter(r -> param.getEventId().equals(r.getEventId()));
                    }
                    // eventIdList 条件
                    if (CollectionUtils.isNotEmpty(param.getEventIdList())) {
                        stream = stream.filter(r -> param.getEventIdList().contains(r.getEventId()));
                    }
                    // vin 条件
                    if (StringUtils.isNotBlank(param.getVin())) {
                        stream = stream.filter(r -> param.getVin().equals(r.getVin()));
                    }
                    // vinList 条件
                    if (CollectionUtils.isNotEmpty(param.getVinList())) {
                        stream = stream.filter(r -> param.getVinList().contains(r.getVin()));
                    }
                    // sideBySideTimestamp 条件发生时间
                    if (StringUtils.isNotBlank(param.getSideBySideTimestamp())) {
                        stream = stream.filter(
                                r -> param.getSideBySideTimestamp().contains(r.getSideBySideTimestamp()));
                    }
                    // caseId 条件
                    if (StringUtils.isNotBlank(param.getCaseId())) {
                        stream = stream.filter(r -> param.getCaseId().equals(r.getCaseId()));
                    }
                    // caseIdList 条件
                    if (CollectionUtils.isNotEmpty(param.getCaseIdList())) {
                        stream = stream.filter(r -> param.getCaseIdList().contains(r.getCaseId()));
                    }
                    // milliBeginTimeBelow 条件
                    if (param.getMilliBeginTimeBelow() != null) {
                        stream = stream.filter(r -> r.getMilliBeginTime() != null && r.getMilliBeginTime()
                                .before(param.getMilliBeginTimeBelow()));
                    }
                    // createTimeGrateTo 条件
                    if (param.getCreateTimeGrateTo() != null) {
                        stream = stream.filter(r -> r.getCreateTime() != null && r.getCreateTime()
                                .after(param.getCreateTimeGrateTo()));
                    }
                    // traceId 条件
                    if (StringUtils.isNotBlank(param.getTraceId())) {
                        stream = stream.filter(r -> param.getTraceId().equals(r.getTraceId()));
                    }
                    // type 条件
                    if (param.getType() != null) {
                        stream = stream.filter(
                                r -> r.getType() != null && param.getType().equals(r.getType().getCode()));
                    }
                    // limit 条件
                    if (param.getLimit() != null) {
                        stream = stream.limit(param.getLimit());
                    }
                    return stream.collect(Collectors.toList());
                }
        );
    }

    /**
     * mock 风险事件查询 queryByParam
     */
    public void mockRiskCase(DataTestBase dataTestBase) {
        // mock 查询事件的状态
        Mockito.when(riskCaseRepository.queryByParam(Mockito.any(RiskCaseDOQueryParamDTO.class)))
                .thenAnswer(invocation -> {
                            RiskCaseDOQueryParamDTO param = invocation.getArgument(0);
                            Stream<RiskCaseDO> stream = dataTestBase.riskCaseDOList.stream();
                            // caseId 条件
                            if (StringUtils.isNotBlank(param.getEventId())) {
                                stream = stream.filter(r -> param.getEventId().equals(r.getEventId()));
                            }
                            // caseIdList 条件
                            if (CollectionUtils.isNotEmpty(param.getCaseIdList())) {
                                stream = stream.filter(r -> param.getCaseIdList().contains(r.getCaseId()));
                            }
                            // eventId 条件
                            if (StringUtils.isNotBlank(param.getEventId())) {
                                stream = stream.filter(r -> param.getEventId().equals(r.getEventId()));
                            }
                            // placeCode 条件
                            if (StringUtils.isNotBlank(param.getPlaceCode())) {
                                stream = stream.filter(r -> param.getPlaceCode().equals(r.getPlaceCode()));
                            }
                            // placeCodeList 条件
                            if (CollectionUtils.isNotEmpty(param.getPlaceCodeList())) {
                                stream = stream.filter(r -> param.getPlaceCodeList().contains(r.getPlaceCode()));
                            }
                            // caseTypeList 条件
                            if (CollectionUtils.isNotEmpty(param.getCaseTypeList())) {
                                stream = stream.filter(r -> r.getType() != null && param.getCaseTypeList()
                                        .contains(r.getType().getCode()));
                            }
                            // type 条件
                            if (param.getType() != null) {
                                stream = stream.filter(
                                        r -> r.getType() != null && param.getType().equals(r.getType().getCode()));
                            }
                            // statusList 条件
                            if (CollectionUtils.isNotEmpty(param.getStatusList())) {
                                stream = stream.filter(r -> r.getStatus() != null && param.getStatusList()
                                        .contains(r.getStatus().getCode()));
                            }
                            // messageId 条件
                            if (StringUtils.isNotBlank(param.getMessageId())) {
                                stream = stream.filter(r -> param.getMessageId().equals(r.getMessageId()));
                            }
                            // messageIdList 条件
                            if (CollectionUtils.isNotEmpty(param.getMessageIdList())) {
                                stream = stream.filter(r -> param.getMessageIdList().contains(r.getMessageId()));
                            }
                            // createTimeRange 条件
                            if (param.getCreateTimeRange() != null) {
                                Date start = param.getCreateTimeRange().getBeginDate();
                                Date end = param.getCreateTimeRange().getEndDate();
                                if (start != null && end != null) {
                                    stream = stream.filter(r -> r.getCreateTime() != null && !r.getCreateTime().before(start)
                                            && !r.getCreateTime().after(end));
                                }
                            }
                            // createTimeCreateTo 条件
                            if (param.getCreateTimeCreateTo() != null) {
                                stream = stream.filter(r -> r.getCreateTime() != null && r.getCreateTime()
                                        .after(param.getCreateTimeCreateTo()));
                            }
                            // source 条件
                            if (param.getSource() != null) {
                                stream = stream.filter(r -> r.getSource() != null && param.getSource()
                                        .equals(r.getSource().getCode()));
                            }
                            return stream.collect(Collectors.toList());
                        }
                );

    }
}
