package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.server.consumer.DomainEventConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;

public class DomainEventConsumerTest extends SpringTestBase {

    @Resource
    private DomainEventConsumer domainEventConsumer;

    @org.junit.Test
    public void testDomainEventConsumer() {
        //        DomainEventDTO<TaskDO> domainEventDTO = new DomainEventDTO<>();
        //        domainEventDTO.setDomainEventEntry(
        //                DomainEventEntryDTO.builder().operateEntry(OperateEnterActionEnum.CANCEL_TERMINAL_TASK)
        //                        .domainClassName(TaskDO.class.getName())
        //                        .build());
        //        domainEventDTO.setBefore(new ArrayList<>());
        //        domainEventDTO.setAfter(new ArrayList<>());
        //        domainEventDTO.setOperator("");
        //        domainEventDTO.setTimestamp(System.currentTimeMillis());
        //        domainEventDTO.setExtInfo(DomainEventExtInfoDTO.builder().build());
        //        domainEventDTO.setTraceId("12312312");
        String str = "{\"entry\":{\"domainClassName\":\"RiskCaseVehicleRelationDO\",\"operateEntry\":\"RISK_EVENT_CONSUMER_ENTER\"},\"domainEventList\":[{\"entry\":{\"domainClassName\":\"RiskCaseDO\",\"operateEntry\":\"RISK_EVENT_CONSUMER_ENTER\"},\"timestamp\":1718697235401,\"operator\":\"\",\"traceId\":\"a805a193-9fa9-4e3e-af5d-06184dbc829f\",\"extInfo\":{},\"before\":[],\"after\":[{\"caseId\":\"b91f5701244e4237964d0f3ab1cda0d2\",\"type\":\"VEHICLE_SIDE_BY_SIDE\",\"status\":\"NO_DISPOSAL\",\"eventId\":\"20240617184839021_common398_s20-173\",\"source\":\"PNC\",\"extInfo\":{\"city\":\"鞍山市\",\"are\":\"0412\",\"poi\":\"\"},\"isDeleted\":\"NOT_DELETED\"}]},{\"entry\":{\"domainClassName\":\"RiskCaseVehicleRelationDO\",\"operateEntry\":\"RISK_EVENT_CONSUMER_ENTER\"},\"timestamp\":1718697235401,\"operator\":\"\",\"traceId\":\"a805a193-9fa9-4e3e-af5d-06184dbc829f\",\"extInfo\":{},\"before\":[],\"after\":[{\"caseId\":\"b91f5701244e4237964d0f3ab1cda0d2\",\"eventId\":\"20240617184839021_common398_s20-173\",\"vin\":\"LMTZSV026NC099389\",\"vehicleSnapshotInfo\":{\"vin\":\"LMTZSV026NC099389\",\"vehicleName\":\"S20-268\",\"position\":{\"latitude\":40.104492,\"longitude\":116.53077,\"coordinateSystem\":\"WGS84\",\"locationStr\":\"122.45051557494428,41.247487315539516\"}},\"status\":\"INIT\",\"occurTime\":\"2024-06-17 18:48:39\",\"isDeleted\":\"NOT_DELETED\"}]}]}";
        String str1 = "{\"entry\":{\"domainClassName\":\"NEGATIVE_PUBLIC_EVENT_DO\",\"operateEntry\":\"NEGATIVE_PUBLIC_EVENT_CREATE\"},\"timestamp\":1736432789874,\"operator\":\"\",\"traceId\":\"8674375802845619235#0\",\"extInfo\":{},\"before\":[],\"after\":[{\"eventId\":\"fcc8aeed1d974aee8ff6c0f58702b727\",\"type\":\"PUBLIC_OPINION\",\"status\":\"CREATED\",\"eventDesc\":\"1234\",\"reporter\":\"unknown\",\"occurTime\":\"2025-01-09 22:26:19\",\"perceiveTime\":\"2025-01-09 22:26:21\",\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"东城区\",\"extInfo\":{\"location\":\"12345\"}}]}";
        domainEventConsumer.receive(str);
    }

    @org.junit.Test
    public void test() {
        String message = "{\"entry\":{\"domainClassName\":\"RiskCheckingQueueItemDO\",\"operateEntry\":\"RISK_EVENT_CONSUMER_ENTER\"},\"timestamp\":1730379309222,\"operator\":\"\",\"traceId\":\"ba430905-e1fd-4e86-a683-80a958a6bf7c#0\",\"extInfo\":{},\"before\":[],\"after\":[{\"vin\":\"LMTZSV023NC031552\",\"tmpCaseId\":\"MT0288920241031205509S03T01\",\"eventId\":\"1730379309_LMTZSV023NC031552\",\"type\":\"VEHICLE_STAND_STILL\",\"source\":\"STATUS_MONITOR\",\"occurTime\":\"2024-10-31 20:55:09\",\"checking\":false,\"status\":\"VALIDATING\",\"round\":0,\"maxRound\":1,\"isDeleted\":false}]}";
        domainEventConsumer.receiveAsync(message);

    }

}
