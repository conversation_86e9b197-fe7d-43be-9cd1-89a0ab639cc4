package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.HashMap;
import org.junit.Test;

public class SpELUtilTestWithSpringTest extends SpringTestBase {

    @Test
    public void testParseExpression3() {

        VehicleRuntimeInfoContextDO contextDO = new VehicleRuntimeInfoContextDO();
        SpELUtil.evaluateBoolean(
                "@contextCounterCommonCompute.isWalkerWaitInCrossWalk(#context,30.0,20.0,10.0,{''},3,15)",
                new HashMap<>());

    }
}