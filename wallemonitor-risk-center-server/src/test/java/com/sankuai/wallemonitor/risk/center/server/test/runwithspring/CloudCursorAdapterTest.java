package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CloudCursorAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.CloudCursorResourceRequest;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallCloudCursorTypeEnum;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

@Slf4j
public class CloudCursorAdapterTest extends SpringTestBase {

    @Resource
    private CloudCursorAdapter cloudCursorAdapter;

    @Test
    public void test() {
        CloudCursorResourceRequest request = CloudCursorResourceRequest.builder()
                .action(CallCloudCursorTypeEnum.CALL.name().toLowerCase())
                .reason(6001)
                .timestamp(System.currentTimeMillis())
                .vin("LMTZSV024MC048701")
                .source(CommonConstant.BEACON_TOWER_CALL_CLOUD_CONTROL_SOURCE)
                .needCancelCommand(true)
                .build();
        cloudCursorAdapter.callCloudCursor(request);
    }
}
