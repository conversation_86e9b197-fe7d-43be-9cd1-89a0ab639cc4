package com.sankuai.wallemonitor.risk.center.server.test.unit.crane;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.RiskCaseNotifyDetectServiceImpl;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.VelocityUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxNoticeParamVTO;
import com.sankuai.wallemonitor.risk.center.server.crane.ReportRiskCaseStatCrane;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.DataTestBase;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import java.util.Collections;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;

public class ReportRiskCaseStatCraneTest extends ServiceTestBase {

    @InjectMocks
    @Spy
    private RiskCaseNotifyDetectServiceImpl riskCaseNotifyDetectService;

    @InjectMocks
    @Spy
    private ReportRiskCaseStatCrane reportRiskCaseStatCrane;

    // 业务代码中命名不同于以往，再写一次
    @Mock
    public RiskCaseRepository caseRepository;

    private final String riskCaseDOStr = "{\n"
            + "    \"caseId\": \"015a397c6d0a4a8a857da122b12820c1\",\n"
            + "    \"type\": \"VEHICLE_SIDE_BY_SIDE\",\n"
            + "    \"placeCode\": \"hualikan\",\n"
            + "    \"status\": \"NO_DISPOSAL\",\n"
            + "    \"eventId\": \"20240617184839021_common398_s20-173\",\n"
            + "    \"source\": \"PNC\",\n"
            + "    \"extInfo\": {\n"
            + "        \"city\": \"北京市\",\n"
            + "        \"are\": \"顺义区\",\n"
            + "        \"poi\": \"莫奈花园\"\n"
            + "    },\n"
            + "    \"isDeleted\": \"NOT_DELETED\",\n"
            + "    \"createTime\": \"2024-07-12 20:14:45\"\n"
            + "}";

    @Before
    public void setUp() {
        // 初始化数据
        DataTestBase dataTestBase = new DataTestBase();

        // mock 风险关系查询
        mockRiskCaseVehicleRelation(dataTestBase);

        // mock 风险查询当前一天时间范围内的记录(直接mock返回，不然需要重设时间，可能会导致流水线执行不稳定)
        RiskCaseDO riskCaseDO = JacksonUtils.from(riskCaseDOStr, RiskCaseDO.class);
        riskCaseDO.setOccurTime(DatetimeUtil.convertDatetimeStr2Date("2024-07-12 20:14:45"));
        Mockito.doReturn(Collections.singletonList(riskCaseDO)).when(caseRepository).queryByParam(Mockito.any(
                RiskCaseDOQueryParamDTO.class));
        Mockito.doReturn(Collections.singletonList(riskCaseDO)).when(riskCaseRepository).queryByParam(Mockito.any(
                 RiskCaseDOQueryParamDTO.class));

        // mock lion消息推送配置
        Mockito.when(lionConfigRepository.getCaseType2BroadCastStrategyConfig()).thenReturn(dataTestBase.map);
        Mockito.when(lionConfigRepository.getByCaseType(any(RiskCaseTypeEnum.class))).thenAnswer(invocation -> {
            RiskCaseTypeEnum riskCaseTypeEnum = invocation.getArgument(0);
            return dataTestBase.map.get(riskCaseTypeEnum);
        });

        // mock VelocityUtils
        PowerMockito.mockStatic(VelocityUtils.class);
        Mockito.when(VelocityUtils.render(Mockito.anyString(), Mockito.anyMap())).thenReturn("hello render");

        // mock 车辆vin不在黑名单
        BroadCastStrategyConfigDTO strategyConfig = Mockito.mock(BroadCastStrategyConfigDTO.class);
        Mockito.when(strategyConfig.isAutocarVersionInBlackList(anyString())).thenReturn(false);

        // mock 大象发消息
        PowerMockito.when(dxNoticeAdapter.createOrUpdateDxMessage(Mockito.any(DxNoticeParamVTO.class))).thenReturn("msgId");

    }

    @Test
    public void testShouldRun() throws Exception {

        // 运行
        reportRiskCaseStatCrane.run();

        // 验证大象发消息
        Mockito.verify(dxNoticeAdapter).createOrUpdateDxMessage(Mockito.any(DxNoticeParamVTO.class));
        // 验证风险事件保存
        Mockito.verify(riskCaseRepository).batchSave(Mockito.anyList());
    }
}
/*

{
    "caseId": "015a397c6d0a4a8a857da122b12820c1",
    "type": "VEHICLE_SIDE_BY_SIDE",
    "placeCode": "hualikan",
    "status": "NO_DISPOSAL",
    "eventId": "20240617184839021_common398_s20-173",
    "source": "PNC",
    "extInfo": {
        "city": "北京市",
        "are": "顺义区",
        "poi": "莫奈花园"
    },
    "isDeleted": "NOT_DELETED"
}
 */