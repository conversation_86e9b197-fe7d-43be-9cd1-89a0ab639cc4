package com.sankuai.wallemonitor.risk.center.server.test.unit.repository.dbrepo;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

import com.sankuai.wallemonitor.risk.center.infra.convert.CaseMarkInfoConverterImpl;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.CaseMarkInfoMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseMarkInfo;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.RepositoryTestBase;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;

public class CaseMarkInfoRepositoryTest extends RepositoryTestBase {

    @Spy
    private CaseMarkInfoMapper mapper;

    @Spy
    private CaseMarkInfoConverterImpl covert;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(caseMarkInfoRepository, "mapper", mapper);
        ReflectionTestUtils.setField(caseMarkInfoRepository, "covert", covert);
    }

    /**
     * 测试getByCaseId方法，传入有效的caseId
     */
    @Test
    public void testGetByCaseIdValidCaseId() {
        // mock
        PowerMockito.when(mapper.selectOne(any())).thenReturn(CaseMarkInfo.builder().build());
        // 运行
        CaseMarkInfoDO result = caseMarkInfoRepository.getByCaseId("caseId");

        // 验证
        assertEquals(result, CaseMarkInfoDO.builder().isDeleted(IsDeleteEnum.NOT_DELETED).build());
    }

    /**
     * 测试根据参数查询风险事件
     */
    @Test
    public void testQueryByParam() {
        // mock
        PowerMockito.when(mapper.selectList(any(), any(), any()))
                .thenReturn(Collections.singletonList(CaseMarkInfo.builder().build()));
        // 运行
        List<CaseMarkInfoDO> caseMarkInfoDOList = caseMarkInfoRepository.queryByParam(
                CaseMarkInfoDOQueryParamDTO.builder().caseIdList(Collections.singletonList("caseId")).build());

        //验证
        assertEquals(caseMarkInfoDOList,
                Collections.singletonList(CaseMarkInfoDO.builder().isDeleted(IsDeleteEnum.NOT_DELETED).build()));
    }
}
