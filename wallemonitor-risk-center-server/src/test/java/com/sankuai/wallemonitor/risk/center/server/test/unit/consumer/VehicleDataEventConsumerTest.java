package com.sankuai.wallemonitor.risk.center.server.test.unit.consumer;

import static groovy.util.GroovyTestCase.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleEventDataDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.server.consumer.VehicleDataEventConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.DataTestBase;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import groovy.util.logging.Slf4j;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

@Slf4j
public class VehicleDataEventConsumerTest extends ServiceTestBase {

    @InjectMocks
    private VehicleDataEventConsumer vehicleDataEventConsumer;

    private String msg = "{\n"
            + "    \"event_id\": \"20240624140413471_common398_s20-178\",\n"
            + "    \"eventCode\": 398,\n"
            + "    \"eventName\": \"SIDE_BY_SIDE_START\",\n"
            + "    \"eventTimestamp\": 1719209053471,\n"
            + "    \"senderTimestamp\": 1719209053517,\n"
            + "    \"receiverTimestamp\": 1719209053827,\n"
            + "    \"vin\": \"LMTZSV023MC063495\",\n"
            + "    \"vehicleId\": \"M2326\",\n"
            + "    \"vehicleName\": \"s20-178\",\n"
            + "    \"recordName\": \"20240624_122526_s20-178\",\n"
            + "    \"utmZone\": 50,\n"
            + "    \"utmX\": \"231094.61068511268\",\n"
            + "    \"utmY\": \"2514981.7924595727\",\n"
            + "    \"datasource\": \"autocar\",\n"
            + "    \"content\": {\n"
            + "        \"start_timestamp\": \"1719209043458009344\",\n"
            + "        \"end_timestamp\": \"1719209053458042112\",\n"
            + "        \"append_message\": \"s20 side by side start\"\n"
            + "    }\n"
            + "}";

    private final String trafficJamStartMsg = "{\n"
            + "    \"event_id\": \"20240703194625040_traffic-jam_s20-191\",\n"
            + "    \"event_code\": 5003,\n"
            + "    \"event_name\": \"TRAFFIC_JAM\",\n"
            + "    \"event_timestamp\": 1720007185040,\n"
            + "    \"sender_timestamp\": 1720007185040,\n"
            + "    \"receiver_timestamp\": 1720007185064,\n"
            + "    \"vin\": \"LMTZSV029MC063825\",\n"
            + "    \"vehicle_id\": \"M9216\",\n"
            + "    \"vehicle_name\": \"s20-191\",\n"
            + "    \"record_name\": \"\",\n"
            + "    \"utm_zone\": -1,\n"
            + "    \"utm_x\": \"\",\n"
            + "    \"utm_y\": \"\",\n"
            + "    \"datasource\": \"sceneranking\",\n"
            + "    \"content\": {\n"
            + "        \"need_handle\": true,\n"
            + "        \"stuck_vehicle_set\": [\n"
            + "            \"s20-191\",\n"
            + "            \"s20-194\",\n"
            + "            \"s20-250\"\n"
            + "        ],\n"
            + "        \"source_event_code\": 1003,\n"
            + "        \"source_event_id\": \"20240703194555037_traffic-jam_755964c6-7a9f-4d6c-a646-5b2570a9df64\"\n"
            + "    }\n"
            + "}";

    private final String trafficJamEndMsg = "{\n"
            + "    \"event_id\": \"20240703194700062_traffic-jam-end_s20-191\",\n"
            + "    \"event_code\": 5004,\n"
            + "    \"event_name\": \"TRAFFIC_JAM_END\",\n"
            + "    \"event_timestamp\": 1720007220062,\n"
            + "    \"sender_timestamp\": 1720007220062,\n"
            + "    \"receiver_timestamp\": 1720007220084,\n"
            + "    \"vin\": \"LMTZSV029MC063825\",\n"
            + "    \"vehicle_id\": \"M9216\",\n"
            + "    \"vehicle_name\": \"s20-191\",\n"
            + "    \"record_name\": \"\",\n"
            + "    \"utm_zone\": -1,\n"
            + "    \"utm_x\": \"\",\n"
            + "    \"utm_y\": \"\",\n"
            + "    \"datasource\": \"sceneranking\",\n"
            + "    \"content\": {\n"
            + "        \"start_time\": 1720007155037,\n"
            + "        \"recall_time\": 1720007185040,\n"
            + "        \"end_time\": 1720007185040,\n"
            + "        \"in_junction\": false,\n"
            + "        \"x\": 669832.4680385906,\n"
            + "        \"y\": 4342641.579783007,\n"
            + "        \"initial_vehicle_set\": [\n"
            + "            \"s20-191\",\n"
            + "            \"s20-194\",\n"
            + "            \"s20-250\"\n"
            + "        ],\n"
            + "        \"recall_vehicle_set\": [\n"
            + "            \"s20-191\",\n"
            + "            \"s20-194\",\n"
            + "            \"s20-250\"\n"
            + "        ],\n"
            + "        \"accumulate_vehicle_set\": [\n"
            + "            \"s20-191\",\n"
            + "            \"s20-194\",\n"
            + "            \"s20-250\"\n"
            + "        ],\n"
            + "        \"stuck_vehicle_set\": [\n"
            + "            \"s20-191\",\n"
            + "            \"s20-194\",\n"
            + "            \"s20-250\"\n"
            + "        ],\n"
            + "        \"source_event_code\": 1004,\n"
            + "        \"source_event_id\": \"20240703194555037_traffic-jam_755964c6-7a9f-4d6c-a646-5b2570a9df64\"\n"
            + "    }\n"
            + "}";

    private String standStillStartMsg = "{\n"
            + "    \"event_id\": \"20240711101433957_adc-stagnant-recall_MKZ-08\",\n"
            + "    \"event_code\": 5006,\n"
            + "    \"event_name\": \"ADC_STAGNANT_RECALL\",\n"
            + "    \"event_timestamp\": 1720664073957,\n"
            + "    \"sender_timestamp\": 1720664073957,\n"
            + "    \"receiver_timestamp\": 1720664073975,\n"
            + "    \"vin\": \"3LN6L5SU2LR601635\",\n"
            + "    \"vehicle_id\": \"12000016\",\n"
            + "    \"vehicle_name\": \"MKZ-08\",\n"
            + "    \"record_name\": \"\",\n"
            + "    \"utm_zone\": -1,\n"
            + "    \"utm_x\": \"\",\n"
            + "    \"utm_y\": \"\",\n"
            + "    \"datasource\": \"sceneranking\",\n"
            + "    \"content\": {\n"
            + "        \"trace_id\": \"MKZ-08_1720663763992\",\n"
            + "        \"recall_type\": 2,\n"
            + "        \"stagnant_time_in_auto\": 50076,\n"
            + "        \"stagnant_time_in_any_mode\": 50076,\n"
            + "        \"scene_type\": 0,\n"
            + "        \"source_event_code\": 1006,\n"
            + "        \"source_event_id\": \"20240711101433957_adc-stagnant-recall_MKZ-08\"\n"
            + "    }\n"
            + "}";

    private String standStillEndMsg = "{\n"
            + "    \"event_id\": \"20240711101433957_adc-stagnant-recall_MKZ-08\",\n"
            + "    \"event_code\": 5007,\n"
            + "    \"event_name\": \"ADC_STAGNANT_END\",\n"
            + "    \"event_timestamp\": 1720664101453,\n"
            + "    \"sender_timestamp\": 1720664101453,\n"
            + "    \"receiver_timestamp\": 1720664101471,\n"
            + "    \"vin\": \"3LN6L5SU2LR601635\",\n"
            + "    \"vehicle_id\": \"12000016\",\n"
            + "    \"vehicle_name\": \"MKZ-08\",\n"
            + "    \"record_name\": \"\",\n"
            + "    \"utm_zone\": -1,\n"
            + "    \"utm_x\": \"\",\n"
            + "    \"utm_y\": \"\",\n"
            + "    \"datasource\": \"sceneranking\",\n"
            + "    \"content\": {\n"
            + "        \"trace_id\": \"MKZ-08_1720663763992\",\n"
            + "        \"recall_type\": 1,\n"
            + "        \"stagnant_time_in_auto\": -1,\n"
            + "        \"stagnant_time_in_any_mode\": -1,\n"
            + "        \"scene_type\": 0,\n"
            + "        \"source_event_code\": 1007,\n"
            + "        \"source_event_id\": \"20240711101433957_adc-stagnant-recall_MKZ-08\"\n"
            + "    }\n"
            + "}";

    @Before
    public void setUp() throws Exception {
        // 初始化数据
        DataTestBase dataTestBase = new DataTestBase();

        MockitoAnnotations.initMocks(this);

        // RiskCase工厂模式代码初始化
        RiskCaseFactory.idGenerateRepository = idGenerateRepository;

        // mock 分布式锁
        mockRedisAndLock();

        // mock lion车辆事件平台的事件关注的列表
        Mockito.when(lionConfigRepository.getFocusedAutocarEventCodeList())
                .thenReturn(dataTestBase.focusedAutocarEventCodeList);

        // mock lion消息推送配置
        Mockito.when(lionConfigRepository.getCaseType2BroadCastStrategyConfig()).thenReturn(dataTestBase.map);
        Mockito.when(lionConfigRepository.getByCaseType(any(RiskCaseTypeEnum.class))).thenAnswer(invocation -> {
            RiskCaseTypeEnum riskCaseTypeEnum = invocation.getArgument(0);
            return dataTestBase.map.get(riskCaseTypeEnum);
        });

        // mock 风险关系查询 queryByParam
        mockRiskCaseVehicleRelation(dataTestBase);

        // mock 风险事件查询
        mockRiskCase(dataTestBase);

        Mockito.when(vehicleRuntimeInfoContextRepository.getFromCache(anyList()))
                .thenAnswer(invocation -> new ArrayList<VehicleRuntimeInfoContextDO>());

        // mock 根据VIN列表查询车辆信息
        Mockito.when(vehicleInfoRepository.queryByVinList(anyList())).thenAnswer(
                invocation -> {
                    List<String> vinlist = invocation.getArgument(0);
                    return dataTestBase.vehicleInfoDOList.stream().filter(
                            v -> vinlist.contains(v.getVin())
                    ).collect(Collectors.toList());

                });
        Mockito.when(vehicleInfoRepository.getByVin(anyString())).thenAnswer(
                invocation -> {
                    String vin = invocation.getArgument(0);
                    return dataTestBase.vehicleInfoDOList.stream().filter(
                            v -> vin.equals(v.getVin())
                    ).findFirst().orElse(null);
                }
        );

        // mock 地理信息
        Mockito.when(gisInfoRepository.queryByPosition(any(PositionDO.class))).thenAnswer(invocation -> {
            PositionDO position = invocation.getArgument(0);
            return dataTestBase.gisInfoDOList.stream().filter(gisInfo -> {
                return Objects.equals(gisInfo.getLatitude(), position.getLatitude()) && Objects.equals(
                        gisInfo.getLongitude(), position.getLongitude());
            }).findFirst().orElse(null);
        });

        // mock 车辆名查询车辆vin，返回通过carNameList查询vinList
        Mockito.when(vehicleInfoRepository.getVinByCarAccountList(anyList())).thenAnswer(
                invocation -> {
                    List<String> carNameList = invocation.getArgument(0);
                    return carNameList.stream().map(dataTestBase.carNameToVinMap::get).collect(Collectors.toList());
                }
        );

        // mock idGenerateRepository
        mockIdGenerateRepository();
    }

    /**
     * 测试空消息
     */
    @Test
    public void testReceiveEmptyMessage() {
        msg = "";

        // 消费消息
        ConsumeStatus result = vehicleDataEventConsumer.receive(msg);

        // 验证
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试消息解析失败
     */
    @Test
    public void testReceiveMessageParseFailed() {
        msg = "invalid json";
        // 消费消息
        ConsumeStatus result = vehicleDataEventConsumer.receive(msg);
        // 验证
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试消息解析成功但VIN为空
     */
    @Test
    public void testReceiveMessageWithEmptyVin() {
        String msg = "{\"eventCode\":100,\"vin\":\"\"}";
        // 消费消息
        ConsumeStatus result = vehicleDataEventConsumer.receive(msg);
        // 验证
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试消息正常解析
     */
    @Test
    public void testShouldConsume() {
        // 消费消息
        ConsumeStatus result = vehicleDataEventConsumer.receive(msg);
        // 验证
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试车辆扎堆正常开始消息
     */
    @Test
    public void testTrafficJamStart() {
        // mock vehicleAdapter.queryVinByCarAccountList，返回通过carNameList查询vinList
        List<String> vinList = Arrays.asList("LMTZSV029MC063825", "LMTZSV029MC063826", "LMTZSV029MC063827");
        Mockito.when(vehicleAdapter.queryVinByCarAccountList(anyList())).thenReturn(vinList);

        // 消费消息
        ConsumeStatus result = vehicleDataEventConsumer.receive(trafficJamStartMsg);
        // 验证
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试车辆扎堆正常结束消息
     */
    @Test
    @SneakyThrows
    public void testTrafficJamEnd() {
        // 消费消息
        ConsumeStatus result = vehicleDataEventConsumer.receive(trafficJamEndMsg);

        // 验证
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        Mockito.verify(riskCaseVehicleRelationRepository, Mockito.times(2)).batchSave(anyList());
        Mockito.verify(riskCaseRepository).save(any());
    }

    /**
     * 测试车辆扎堆结束消息，消费失败后重新消费，然后消费成功
     */
    @Test
    public void testTrafficJamEndDeadLetter() {
        // 返回无风险事件，让第一次查询结果失败，消费消息，必然失败
        VehicleEventDataDO vehicleEventDataDO = JacksonUtils.from(trafficJamEndMsg,
                new TypeReference<VehicleEventDataDO>() {
                });
        vehicleEventDataDO.getContent().put("source_event_id", "不存在的Id");

        // 修改事件时间戳为现在，可以重试
        long originTimestamp = vehicleEventDataDO.getEventTimestamp();
        vehicleEventDataDO.setEventTimestamp(System.currentTimeMillis());
        ConsumeStatus result = vehicleDataEventConsumer.receive(JacksonUtils.to(vehicleEventDataDO));

        // 验证失败
        assertEquals(ConsumeStatus.CONSUME_FAILURE, result);

        /*************分界线***************/

        // 恢复正常逻辑，必然成功
        vehicleEventDataDO.setEventTimestamp(originTimestamp);

        // 模拟死信重新消费
        result = vehicleDataEventConsumer.receive(JacksonUtils.to(vehicleEventDataDO));

        // 验证成功
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试车辆停滞不当开始消息
     */
    @Test
    public void testStandStillStart() {
        // 运行
        ConsumeStatus result = vehicleDataEventConsumer.receive(standStillStartMsg);

        // 验证
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        Mockito.verify(riskCaseVehicleRelationRepository, Mockito.times(2)).batchSave(anyList());
        Mockito.verify(riskCaseRepository).save(any());
    }

    /**
     * 测试车辆停滞不当结束消息
     */
    @Test
    public void testStandStillEnd() {
        // 返回已有风险事件，模拟上面的结果
        // 运行
        ConsumeStatus result = vehicleDataEventConsumer.receive(standStillEndMsg);

        // 验证
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        Mockito.verify(riskCaseVehicleRelationRepository, Mockito.times(2)).batchSave(anyList());
        Mockito.verify(riskCaseRepository).save(any());
    }
}
