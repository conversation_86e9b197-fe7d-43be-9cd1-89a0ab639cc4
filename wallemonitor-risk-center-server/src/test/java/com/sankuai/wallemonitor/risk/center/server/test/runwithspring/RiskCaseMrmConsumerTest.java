package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMrmMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.RiskCaseRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl.RiskCaseVehicleRelationRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.server.StartApp;
import javax.annotation.Resource;

import com.sankuai.wallemonitor.risk.center.server.consumer.RiskCaseMrmConsumer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public class RiskCaseMrmConsumerTest {

    private String msg = "{\"type\":30,\"body\":{\"vin\":\"LMTZSV024MC048701\",\"traceId\":\"s20-175_1720407707572\",\"mrmSeatMisId\":\"limuyong\",\"status\":40,\"mrmRole\":1,\"mrmSeatNo\":\"sz-91\",\"requestSeatTime\":1720407718000,\"seatExitTime\":1720407741874},\"timestamp\":1720407741879}";

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private RiskCaseRepositoryImpl riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepositoryImpl riskCaseVehicleRelationRepository;

    @Resource
    private RiskCaseMrmConsumer riskCaseMrmConsumer;

    /**
     * 执行前`seat_exit_time` = '1970-01-01 08:00:01' 执行后`seat_exit_time` = '2024-07-08 11:02:22'
     */
    @Test
    public void test() {
        EveMqCommonMessage<RiskCaseMrmMessageDTO> message = JacksonUtils.from(msg,
                new TypeReference<EveMqCommonMessage<RiskCaseMrmMessageDTO>>() {
                });

    }
}
