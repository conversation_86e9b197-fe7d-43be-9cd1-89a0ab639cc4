package com.sankuai.wallemonitor.risk.center.server.test.unit.consumer;


import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.DataContainerConsumeConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.DataContainerDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.server.consumer.OnboardMessageConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;


@Slf4j
public class DataContainerConsumerTest extends ServiceTestBase {
    @InjectMocks
    private OnboardMessageConsumer onboardMessageConsumer;

    @Mock
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    private static final String TRAFFIC_FLOW = "{\n" +
            "    \"plannerMasterState\": {\n" +
            "        \"plannerWorldState\": {\n" +
            "            \"plannerHistory\": {\n" +
            "                \"trafficFlowStateBuffer\": [\n" +
            "                    {\n" +
            "                        \"trafficFlowObstacleFeatures\": [\n" +
            "                            {\n" +
            "                                \"trafficFlowObstacles\": [\n" +
            "                                    {\n" +
            "                                        \"isOn\": true,\n" +
            "                                        \"obstacleId\": 1174360,\n" +
            "                                        \"onTrafficFlowEvents\": [\n" +
            "                                            {\n" +
            "                                                \"endDeltaRouteSToEgo\": 18.60804664576553,\n" +
            "                                                \"isVisible\": true,\n" +
            "                                                \"minLateralDistance\": 72.7199998666816,\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786375.5049698,\n" +
            "                                                    \"start\": 1744786375.5049698\n" +
            "                                                }\n" +
            "                                            },\n" +
            "                                            {\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786379.9049335,\n" +
            "                                                    \"start\": 1744786375.604923\n" +
            "                                                }\n" +
            "                                            }\n" +
            "                                        ]\n" +
            "                                    },\n" +
            "                                    {\n" +
            "                                        \"isOn\": true,\n" +
            "                                        \"obstacleId\": 1174343,\n" +
            "                                        \"onTrafficFlowEvents\": [\n" +
            "                                            {\n" +
            "                                                \"endDeltaRouteSToEgo\": 29.567405930194514,\n" +
            "                                                \"isVisible\": true,\n" +
            "                                                \"minLateralDistance\": 72.55444839806486,\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786375.5049698,\n" +
            "                                                    \"start\": 1744786375.5049698\n" +
            "                                                }\n" +
            "                                            },\n" +
            "                                            {\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786379.9049335,\n" +
            "                                                    \"start\": 1744786375.604923\n" +
            "                                                }\n" +
            "                                            }\n" +
            "                                        ]\n" +
            "                                    },\n" +
            "                                    {\n" +
            "                                        \"isOn\": true,\n" +
            "                                        \"obstacleId\": 1174846,\n" +
            "                                        \"onTrafficFlowEvents\": [\n" +
            "                                            {\n" +
            "                                                \"endDeltaRouteSToEgo\": 10.641280140821591,\n" +
            "                                                \"isVisible\": true,\n" +
            "                                                \"minLateralDistance\": 72.57360042256005,\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786379.6049562,\n" +
            "                                                    \"start\": 1744786379.6049562\n" +
            "                                                }\n" +
            "                                            },\n" +
            "                                            {\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786379.9049335,\n" +
            "                                                    \"start\": 1744786379.7049232\n" +
            "                                                }\n" +
            "                                            }\n" +
            "                                        ]\n" +
            "                                    },\n" +
            "                                    {\n" +
            "                                        \"isOn\": true,\n" +
            "                                        \"obstacleId\": 1171431,\n" +
            "                                        \"onTrafficFlowEvents\": [\n" +
            "                                            {\n" +
            "                                                \"endDeltaRouteSToEgo\": -9.42180299182686,\n" +
            "                                                \"endSpeed\": 0.7073805167643163,\n" +
            "                                                \"isVisible\": true,\n" +
            "                                                \"minLateralDistance\": 72.26036353836105,\n" +
            "                                                \"startSpeed\": 0.7073805167643163,\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786347.0049381,\n" +
            "                                                    \"start\": 1744786347.0049381\n" +
            "                                                }\n" +
            "                                            },\n" +
            "                                            {\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786379.9049335,\n" +
            "                                                    \"start\": 1744786347.1049168\n" +
            "                                                }\n" +
            "                                            }\n" +
            "                                        ]\n" +
            "                                    },\n" +
            "                                    {\n" +
            "                                        \"isOn\": true,\n" +
            "                                        \"obstacleId\": 1170858,\n" +
            "                                        \"onTrafficFlowEvents\": [\n" +
            "                                            {\n" +
            "                                                \"endDeltaRouteSToEgo\": 29.606347597716237,\n" +
            "                                                \"isVisible\": true,\n" +
            "                                                \"minLateralDistance\": 72.58125362928158,\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786337.9049332,\n" +
            "                                                    \"start\": 1744786337.9049332\n" +
            "                                                }\n" +
            "                                            },\n" +
            "                                            {\n" +
            "                                                \"timeRange\": {\n" +
            "                                                    \"end\": 1744786379.9049335,\n" +
            "                                                    \"start\": 1744786338.004922\n" +
            "                                                }\n" +
            "                                            }\n" +
            "                                        ]\n" +
            "                                    }\n" +
            "                                ]\n" +
            "                            }\n" +
            "                        ]\n" +
            "                    }\n" +
            "                ]\n" +
            "            }\n" +
            "        }\n" +
            "    }\n" +
            "}";


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // Mock VehicleRuntimeInfoContextDO

        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = new VehicleRuntimeInfoContextDO();
        List<VehicleRuntimeInfoContextDO.ObstacleAbstract> obstacleAbstractList = new ArrayList<>();

        VehicleRuntimeInfoContextDO.ObstacleAbstract obstacleAbstract1 = new VehicleRuntimeInfoContextDO.ObstacleAbstract();
        obstacleAbstract1.setObstacleId("111");
        obstacleAbstract1.setObstacleId("1174846");
        obstacleAbstract1.setPosture(GeoElementTypeKeyConstant.SUCCESSOR);
        obstacleAbstract1.setVelocity(PerceptionObstacleDTO.PerceptionObstacle.Velocity.builder().x(0.0).y(1.0).build());


        VehicleRuntimeInfoContextDO.ObstacleAbstract obstacleAbstract2 = new VehicleRuntimeInfoContextDO.ObstacleAbstract();
        obstacleAbstract2.setObstacleId("222");
        obstacleAbstract2.setObstacleId("1171431");
        obstacleAbstract2.setPosture(GeoElementTypeKeyConstant.SUCCESSOR);
        obstacleAbstract2.setVelocity(PerceptionObstacleDTO.PerceptionObstacle.Velocity.builder().x(1.0).y(1.0).build());

        obstacleAbstractList.add(obstacleAbstract1);
        obstacleAbstractList.add(obstacleAbstract2);

        vehicleRuntimeInfoContextDO.setObstacleAbstracts(obstacleAbstractList);

        Mockito.when(vehicleRuntimeInfoContextRepository.getFromCache(Mockito.any(String.class))).thenReturn(vehicleRuntimeInfoContextDO);


        // Mock Lion
        Mockito.when(lionConfigRepository.getDataContainerConsumeConfigDTO())
                .thenReturn(DataContainerConsumeConfigDTO.builder().enable(true).open(true).topKTrafficFlowBuffer(5L).build());

        ReflectionTestUtils.setField(onboardMessageConsumer, "vehicleRuntimeInfoContextRepository", vehicleRuntimeInfoContextRepository);
        ReflectionTestUtils.setField(onboardMessageConsumer, "lionConfigRepository", lionConfigRepository);






    }

    @Test
    public void testConsumerDataContainerMessage() {
        DataContainerDTO dataContainerDTO = JacksonUtils.from(TRAFFIC_FLOW, new TypeReference<DataContainerDTO>() {
        });

        log.info("data container, {}", dataContainerDTO);

        onboardMessageConsumer.consumeDataContainerMessage(dataContainerDTO, "111", 0L);
    }
}
