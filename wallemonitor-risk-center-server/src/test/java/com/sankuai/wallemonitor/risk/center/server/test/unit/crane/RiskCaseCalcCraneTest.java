package com.sankuai.wallemonitor.risk.center.server.test.unit.crane;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.impl.RiskCaseNotifyDetectServiceImpl;
import com.sankuai.wallemonitor.risk.center.infra.dto.BroadCastCalcConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PlaceInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.server.crane.RiskCaseCalcCrane;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

public class RiskCaseCalcCraneTest extends ServiceTestBase {

    @InjectMocks
    private RiskCaseCalcCrane riskCaseCalcCrane;

    @InjectMocks
    @Spy
    private RiskCaseNotifyDetectServiceImpl riskCaseNotifyDetectService;

    @Mock
    private RiskCaseRepository caseRepository;

    private String lionConfig = "{"
            + "\"groupIdList\":[64012247676,64012250440],"
            + "\"placeCodeList\":[\"hualikan\",\"mapo\"],"
            + "\"templateId\":\"4422\""
            + "}";

    private final String riskCaseDOStr = "{\n"
            + "    \"caseId\": \"015a397c6d0a4a8a857da122b12820c1\",\n"
            + "    \"type\": \"VEHICLE_SIDE_BY_SIDE\",\n"
            + "    \"placeCode\": \"hualikan\",\n"
            + "    \"status\": \"NO_DISPOSAL\",\n"
            + "    \"eventId\": \"20240617184839021_common398_s20-173\",\n"
            + "    \"source\": \"PNC\",\n"
            + "    \"extInfo\": {\n"
            + "        \"city\": \"北京市\",\n"
            + "        \"are\": \"顺义区\",\n"
            + "        \"poi\": \"莫奈花园\"\n"
            + "    },\n"
            + "    \"isDeleted\": \"NOT_DELETED\"\n"
            + "}";

    private final RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = RiskCaseVehicleRelationDO.builder()
            .id(1L)
            .caseId("**********")
            .eventId("EV20231002")
            .vin("LMTZSV023MC063496")
            .traceId("a73220a5-2f6b-4238-8c8c-0c47dbc44096")
            .vehicleSnapshotInfo(VehicleInfoDO.builder()
                    .vehicleId("V123456")
                    .build())
            .extInfo(RiskVehicleExtInfoDO.builder().build())
            .status(RiskCaseVehicleStatusEnum.ASSIGNED) // 已分配
            .sideBySideTimestamp("20231002123000")
            .isDeleted(IsDeleteEnum.NOT_DELETED)
            .build();

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // mock lionConfig
        BroadCastCalcConfigDTO broadCastCalcConfigDTO = JacksonUtils.from(lionConfig, BroadCastCalcConfigDTO.class);
        Mockito.when(lionConfigRepository.getBroadCastCalcConfig()).thenReturn(broadCastCalcConfigDTO);

        // mock queryByPlaceCode
        List<PlaceInfoDO> placeIdList = new ArrayList<>();
        placeIdList.add(PlaceInfoDO.builder().placeCode("hualikan").placeName("hualikan").city("beijing").build());
        Mockito.when(placeAdapter.queryByPlaceCode(Mockito.anyList())).thenReturn(placeIdList);

        // mock queryReserveVehicleByTimeAndPlace
        HashSet<String> placeCodeVinList = new HashSet<>();
        placeCodeVinList.add("LMTZSV023MC063496");
        Mockito.when(vehicleAdapter.queryReserveVehicleByTimeAndPlace(Mockito.any(), Mockito.anyString()))
                .thenReturn(placeCodeVinList);

        // mock 风险查询
        RiskCaseDO riskCaseDO = JacksonUtils.from(riskCaseDOStr, RiskCaseDO.class);
        Mockito.when(caseRepository.queryByParam(Mockito.any())).thenReturn(Lists.newArrayList(riskCaseDO));

        // mock 风险关系查询
        Mockito.when(riskCaseVehicleRelationRepository.queryByParam(Mockito.any(RiderCaseVehicleRelationDOParamDTO.class)))
                .thenReturn(Collections.singletonList(riskCaseVehicleRelationDO));
    }

    @Test
    public void testShouldRun() throws Exception {
        // 运行
        riskCaseCalcCrane.run();

        // 验证
        Mockito.verify(caseRepository, Mockito.times(4)).queryByParam(Mockito.any());
        Mockito.verify(riskCaseVehicleRelationRepository, Mockito.times(4)).queryByParam(Mockito.any());
        Mockito.verify(caseRepository, Mockito.times(4)).queryByParam(Mockito.any());
        Mockito.verify(dxNoticeAdapter).createOrUpdateDxMessage(Mockito.any());
    }
}
