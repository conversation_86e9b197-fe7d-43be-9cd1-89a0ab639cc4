package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;


import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.dto.OnboardCommonMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.WaitInQueueConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.ControlArbitrationInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.server.consumer.OnboardMessageConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Slf4j
public class OnboardMessageConsumerTest extends SpringTestBase {

    @Resource
    private OnboardMessageConsumer onboardMessageConsumer;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Test
    public void test() {
        WaitInQueueConfigDTO waitInQueueConfigDTO = lionConfigRepository.getWaitInQueueConfig();
        log.info("wauInQueueConfigDTO:{}", waitInQueueConfigDTO);

        String msg = "{\"data\":{\"arbitrationAlert\":{\"type\":14},\"changedEvent\":{},\"evaluationAlert\":{},\"evaluationResult\":{\"score\":[{\"evaluationAlert\":{},\"score\":1,\"strategyName\":\"is_bumper_trigger_in_parking\",\"system\":\"PARKING\"},{\"evaluationAlert\":{},\"score\":100,\"strategyName\":\"replace_battery_starting_teleoperation\",\"system\":\"TELEOPERATION\"},{\"evaluationAlert\":{},\"score\":100,\"strategyName\":\"replace_battery_starting_auto\",\"system\":\"AUTO\"},{\"evaluationAlert\":{},\"score\":100,\"strategyName\":\"replace_battery_starting_unknown\"}]},\"monitorMetrics\":{\"areaOccludedRate\":0,\"batteryRequestSwitch\":false,\"batterySwitching\":false,\"bcmDoorUnlocked\":false,\"bicycleNumber\":11,\"blockScenario\":24,\"blockTime\":1368100.004922,\"boundaryCollisionRisk\":false,\"cameraDelayInMs\":86,\"cameraTriggerDelayInMs\":46,\"canbusDelayInMs\":9,\"canbusLocalizationVDiff\":-0.0011455478097067739,\"chassisBrakePercentage\":0,\"chassisHz\":50,\"chassisSteeringPercentage\":0.02631678618490696,\"chassisThrottlePercentage\":0,\"collectedLidarPacketDelayInMs\":29,\"collisionPosition\":\"NONE_COLLISION\",\"controlBrakePercentage\":0,\"controlDelayInMs\":3,\"controlStatus\":\"READY\",\"cpuUsagePercent\":27.309999465942383,\"cumulativeDisplacementInLastSecond\":0.003136682826489242,\"currentAreaType\":\"NORMAL\",\"currentPositionStatus\":\"UNKNOWN_STATUS\",\"dataDiskUsagePercent\":76.5999984741211,\"diskWriteStatus\":0,\"distanceToBusStation\":\"Infinity\",\"distanceToCrosswalk\":\"Infinity\",\"distanceToElectricFence\":\"Infinity\",\"distanceToJunction\":\"Infinity\",\"distanceToNextDistrictEntrance\":\"Infinity\",\"distanceToNextElectricFence\":\"Infinity\",\"distanceToNextJunction\":\"Infinity\",\"distanceToNextRoad\":\"Infinity\",\"distanceToNextTurn\":\"Infinity\",\"distanceToOcclusionArea\":1.7976931348623157e+308,\"distanceToStopElectricFence\":\"Infinity\",\"drivingMode\":\"COMPLETE_MANUAL\",\"engineRpm\":0,\"epbState\":\"EPB_PARK_APPLIED\",\"exchangeDelayInMs\":85,\"freespaceParkingDurationInMs\":0,\"freespaceStatus\":\"NONE\",\"gearPosition\":\"GEAR_PARKING\",\"gnssImuDelayInMs\":9,\"gnssImuDivergeTimeInMs\":4,\"gnssImuTopicFrequency\":100.0073005329389,\"gpuTemperature\":43,\"gpuUsagePercent\":36,\"imuXAngularVelocity\":-0.***********,\"imuYAngularVelocity\":-0.0015516493055555557,\"imuZAngularVelocity\":-0.0011067708333333333,\"isAdcStagnant\":true,\"isArbitrationMonitorAbnormal\":false,\"isBatteryDoorOpened\":false,\"isBlockingTraffic\":false,\"isBoundaryCollision\":false,\"isBusinessArrivedStation\":false,\"isCameraOutOfOrder\":false,\"isCurvatureOverLimit\":false,\"isGnssImuAbnormal\":false,\"isHighVoltageBatteryDoorOpened\":false,\"isHomeEntranceAllowed\":true,\"isInCollisionLowRiskArea\":true,\"isInDeliveryStationArea\":false,\"isInHomeArea\":false,\"isInHomeQueue\":false,\"isInParkingQueueArea\":false,\"isInRemoteMonitor\":false,\"isInUnevenGroundArea\":false,\"isIpcDoorOpened\":false,\"isLidarPointCloudOutOfOrder\":false,\"isLidarPointCloudQualityAbnormal\":false,\"isObstacleCollision\":false,\"isPerceptionEmergency\":false,\"isPlanningFarFromLocalization\":false,\"isPlgDoorOpened\":false,\"isPuddleAhead\":false,\"isQueuing\":false,\"isRefLineCurvatureOverLimit\":false,\"isRoutingOk\":true,\"isSchedulingAbnormal\":false,\"isSensorUncalibrated\":false,\"isSnowCoverAhead\":false,\"isVehicleArrivedParkingPlot\":false,\"isVehicleOpenDoorAhead\":false,\"isWsuStateAbnormal\":false,\"lidarFrontLeftDelayInMs\":31,\"lidarFrontRightDelayInMs\":31,\"lidarMainDelayInMs\":31,\"lidarMainStatus\":0,\"linearAccelerationVrfY\":-0.0009099685116930308,\"linearAccelerationZ\":-0.0032629646553401104,\"linearVelocityFromLocalization\":0.001569463375227095,\"localizationPoseDelayInMs\":6,\"localizationReliable\":true,\"mapAccessOutOfRange\":false,\"mapLoadError\":false,\"mapLoadTimeInMs\":8,\"memoryStatus\":0,\"memoryUsagePercent\":57.***************,\"noiseCategory\":\"NOISE_CATEGORY_LOW\",\"pedestrianNumber\":0,\"perceptionCameraDelayInMs\":18,\"perceptionDelayInMs\":74,\"planningDelayInMs\":98,\"predictHeadingDiff\":-0.00001852136853491837,\"predictPositionDiff\":-0.0007893352047490943,\"predictSpeedDiff\":0.0005408365898427356,\"predictionDelayInMs\":66,\"quietForPowersaveInMs\":9223372036854776000,\"rearEndCollisionRisk\":false,\"referenceLinesMatchRouting\":false,\"sataLinkSpeedStatus\":0,\"scenarioDuration\":0,\"sceneInfo\":{\"scene\":\"UNKNOWN_SCENE\"},\"sequenceId\":\"138379\",\"slabTemperature\":45.***************,\"specialObstacleType\":-1,\"speedInMps\":0,\"stagnantTimeInMs\":0,\"systemType\":\"UNKNOWN\",\"teleoperationAvailability\":\"AVAILABLE\",\"topicChassisDetailLatencyInMs\":10,\"topicChassisLatencyInMs\":10,\"topicCollectedLidarPacketLatencyInMs\":99,\"topicControlLatencyInMs\":9,\"topicExchangeLatencyInMs\":101,\"topicLocalizationPoseLatencyInMs\":9,\"topicPerceptionCameraLatencyInMs\":105,\"topicPerceptionObstacleLatencyInMs\":107,\"topicPlanningTrajectoryLatencyInMs\":100,\"topicPredictionLatencyInMs\":105,\"topicTrafficLightLatencyInMs\":100,\"totalObstacleNumber\":58,\"trafficLightBroken\":false,\"trafficLightDelayInMs\":25,\"trafficLightOccluded\":false,\"trajectoryLengthInMeter\":0,\"unknowNumber\":27,\"vehicleNumber\":4,\"weather\":\"WEATHER_NORMAL\"},\"operationAlert\":{},\"options\":{\"enableAutoToParking\":true,\"enableStartAutoCheck\":true,\"enableTakeoverByBrake\":true,\"enableTeleopToParking\":true},\"systemState\":{}},\"timestamp\":\"1741228425539811917\",\"topic\":\"/walle/control_arbitration/info\",\"vin\":\"LMTZSV029NC063499\"}";
        OnboardCommonMessageDTO<ControlArbitrationInfoDTO> commonMessageDTO = JacksonUtils.from(msg,
                new TypeReference<OnboardCommonMessageDTO<ControlArbitrationInfoDTO>>() {
                });
        //        onboardMessageConsumer.consume(msg);
    }

    /**
     * 测试修复后的ConstraintSourceTypeEnum反序列化 验证能够正确解析数字类型的constraintSourceType字段
     */
    @Test
    public void testConstraintSourceTypeEnumDeserialize() {
        // 创建一个包含数字类型constraintSourceType的JSON字符串
        // 这个JSON与错误日志中的相似，包含数字类型的constraintSourceType (值为20)
        String message = "{\"data\":{\"highNegativeEventMeta\":{\"closestFenceAndFieldMetas\":[" +
                "{\"constraintSourceType\":\"STATIONARY\",\"perceptionId\":382888,\"position\":{\"x\":230038.3159222872,\"y\":2516165.7567808274}},"
                +
                "{\"constraintSourceType\":20,\"perceptionId\":453136,\"position\":{\"x\":230038.3159222872,\"y\":2516165.7567808274}},"
                +
                "{\"constraintSourceType\":20,\"perceptionId\":453136,\"position\":{\"x\":230038.3159222872,\"y\":2516165.7567808274}}"
                +
                "],\"commonMeta\":{\"distanceToNextJunction\":{\"hasValue\":true,\"value\":18.716853146579567}}," +
                "\"constructionZoneMeta\":{},\"isValid\":true,\"touchLineMeta\":{}}}," +
                "\"timestamp\":\"1744076775033758771\",\"topic\":\"/walle/planning/planner_result_meta\",\"vin\":\"LMTZSV023NC048772\"}";

        try {
            // 尝试使用消费者消费消息
            onboardMessageConsumer.consume(message);
            log.info("消息消费测试成功！");
        } catch (Exception e) {
            log.error("反序列化测试失败", e);
            Assert.fail("反序列化应该成功，但失败了: " + e.getMessage());
        }
    }

//    @Test
//    public void testConsumerDataContainer() {
//        final int perBatch = 100;
//        final int epoch = 10;
//        try {
//            EveHttpResponse<List> listEveHttpResponse = HttpUtils.get(null, "https://eve.sankuai.com/eve/cmdb/rest/access/api/allvins", null, List.class);
//            List allVin = listEveHttpResponse.getData();
//            for(int i = 0; i < epoch; i ++) {
//                long start = System.currentTimeMillis();
//                Collections.shuffle(allVin);
//                for(int j  = 0; j < perBatch; j ++) {
//                    log.info("vin = {} start" , allVin.get(j) );
//                    List<VehicleRuntimeInfoContextDO.ObstacleAbstract> obstacleAbstracts = onboardMessageConsumer.queryObstacleAbstract((String) allVin.get(j));
//                }
//                long end = System.currentTimeMillis();
//
//                log.info("Batch = {}, cost = {} " , i,  end - start);
//
//
//            }
//            System.out.println(listEveHttpResponse);
//
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//    }
}
