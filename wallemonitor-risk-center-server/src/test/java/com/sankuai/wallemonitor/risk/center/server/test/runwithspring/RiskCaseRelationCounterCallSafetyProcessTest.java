package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseRelationCounterCallSafetyProcess;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Date;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;

public class RiskCaseRelationCounterCallSafetyProcessTest extends SpringTestBase {


    @Resource
    private RiskCaseRelationCounterCallSafetyProcess riskCaseRelationCounterCallSafetyProcess;

    @Resource
    private RiskCaseRepository caseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository vehicleRelationRepository;


    @Resource
    private RiskCaseOperateService riskCaseOperateService;


    @Before
    @SneakyThrows
    public void init() {

        String riskDoStr = "{\"id\":1612299,\"caseId\":\"MT0845220241115161416S01T01\",\"type\":\"VEHICLE_STAND_STILL\",\"placeCode\":\"shenzhenlonghua\",\"status\":\"NO_DISPOSAL\",\"messageId\":\"\",\"messageVersion\":\"\",\"eventId\":\"20241115161416734_adc-stagnant-recall_s20-186\",\"source\":\"SAFEGUARD_SYSTEM\",\"occurTime\":\"2024-11-15 16:14:17\",\"closeTime\":\"1970-01-01 08:00:01\",\"createTime\":\"2024-11-15 16:14:16\",\"updateTime\":\"2024-11-15 16:14:16\",\"extInfo\":{\"city\":\"深圳市\",\"are\":\"龙华区\",\"poi\":\"深圳协威雅铁塑制品有限公司\",\"position\":{\"latitude\":22.704744628869364,\"longitude\":114.06175320881607,\"coordinateSystem\":\"GCJ02\"}},\"level\":0,\"isDeleted\":\"NOT_DELETED\",\"mrmCalled\":\"NO_CALL\",\"disposalTime\":81,\"groupMessageMap\":{}}";
        RiskCaseDO riskCaseDO = JacksonUtils.from(riskDoStr, RiskCaseDO.class);
        riskCaseDO.setType(RiskCaseTypeEnum.RETROGRADE);
        riskCaseDO.setStatus(RiskCaseStatusEnum.NO_DISPOSAL);
        caseRepository.save(riskCaseDO);
    }

    @Test
    @SneakyThrows
    public void testProcess() {
        String domainEventStr = "{\"entry\":{\"domainClassName\":\"RiskCaseVehicleRelationDO\",\"operateEntry\":\"VEHICLE_DATA_CONSUMER_ENTER\"},\"timestamp\":1731658456924,\"operator\":\"\",\"traceId\":\"8932632580869671795#0\",\"extInfo\":{},\"before\":[],\"after\":[{\"caseId\":\"MT0845220241115161416S01T01\",\"eventId\":\"20241115161416734_adc-stagnant-recall_s20-186\",\"vin\":\"LMTZSV023MC023286\",\"vehicleSnapshotInfo\":{\"vin\":\"LMTZSV023MC023286\",\"vehicleId\":\"MT08452\",\"vehicleName\":\"s20-186\",\"purpose\":\"路测-AB深圳测试\",\"vhr\":\"VHR_GREAT_THAN_ONE\",\"position\":{\"latitude\":22.707436,\"longitude\":114.05663,\"coordinateSystem\":\"WGS84\"},\"placeCode\":\"shenzhenlonghua\",\"autocarVersion\":\"65.11.3\",\"driveMode\":1,\"withRescueOrder\":false,\"withAccidentOrder\":false,\"withMaintenanceOrder\":false,\"isWaitingRed\":false},\"status\":\"INIT\",\"type\":\"VEHICLE_STAND_STILL\",\"occurTime\":\"2024-11-15 16:14:16\",\"milliBeginTime\":\"2024-11-15 16:14:16\",\"purpose\":\"路测-AB深圳测试\",\"vhrMode\":\">1\",\"isDeleted\":\"NOT_DELETED\",\"extInfoStr\":\"\"}]}";
        DomainEventDTO<RiskCaseVehicleRelationDO> eventDTO = JacksonUtils.from(domainEventStr,
                new TypeReference<DomainEventDTO<RiskCaseVehicleRelationDO>>() {
                });
        eventDTO.setTimestamp(new Date().getTime());
        VehicleCounterInfoDO vehicleCounterInfoDO = VehicleCounterInfoDO.builder()
                .build();
        vehicleCounterInfoDO.setDuration(15);
        vehicleCounterInfoDO.setStartTime(
                DatetimeUtil.getNSecondsBeforeDateTime(new Date(), 15));
        eventDTO.getAfter().get(0).setStagnationCounter(Lists.newArrayList(vehicleCounterInfoDO));
        eventDTO.getAfter().get(0).setType(RiskCaseTypeEnum.RETROGRADE);
        riskCaseRelationCounterCallSafetyProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCaseVehicleRelationDO.class));
    }

}