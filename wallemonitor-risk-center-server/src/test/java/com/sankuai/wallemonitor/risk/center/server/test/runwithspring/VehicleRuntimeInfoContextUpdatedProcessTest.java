package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.VehicleRuntimeInfoContextUpdatedProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Date;
import javax.annotation.Resource;
import org.apache.thrift.TException;
import org.junit.Test;

public class VehicleRuntimeInfoContextUpdatedProcessTest extends SpringTestBase {

    @Resource
    private VehicleRuntimeInfoContextUpdatedProcess updatedProcess;

    @Test
    public void test() throws TException {
        String vehicleRuntimeInfoUpdateString = "{\"entry\":{\"domainClassName\":\"VehicleRuntimeInfoContextDO\",\"operateEntry\":\"RISK_CASE_CALC_CRANE\"},\"timestamp\":1728982548137,\"operator\":\"\",\"traceId\":\"50730a49-4bd8-4206-8032-baca84a51008\",\"extInfo\":{},\"before\":[{\"vin\":\"LMTZSV022NC017593\",\"driveMode\":\"AUTONOMOUS_DRIVING\",\"speed\":0.2,\"lng\":\"116.407526\",\"lat\":\"40.057008\",\"batterySwitching\":false,\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"\",\"trafficLightType\":\"NONE\",\"distanceToNextJunction\":-1,\"waitingGatePole\":false,\"lastUpdateTime\":\"2024-10-15 15:42:56\",\"createTime\":\"2024-10-15 15:42:59\",\"updateTime\":\"2024-10-15 15:42:59\",\"isDeleted\":\"NOT_DELETED\"}],\"after\":[{\"vin\":\"LMTZSV022NC017593\",\"driveMode\":\"AUTONOMOUS_DRIVING\",\"speed\":0.2,\"lng\":\"116.407526\",\"lat\":\"40.057008\",\"batterySwitching\":false,\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"\",\"trafficLightType\":\"NONE\",\"distanceToNextJunction\":-1,\"waitingGatePole\":false,\"lastUpdateTime\":\"2024-10-15 16:55:44\",\"createTime\":\"2024-10-15 15:42:59\",\"updateTime\":\"2024-10-15 15:42:59\",\"isDeleted\":\"NOT_DELETED\"}]}";
        DomainEventDTO<VehicleRuntimeInfoContextDO> eventDTO = JacksonUtils.from(vehicleRuntimeInfoUpdateString,
                new TypeReference<DomainEventDTO<VehicleRuntimeInfoContextDO>>() {
                });
        eventDTO.getAfter().get(0).setVin("LMTZSV020MC042359");
        eventDTO.getAfter().get(0).setLastUpdateTime(new Date());
        updatedProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, VehicleRuntimeInfoContextDO.class));
    }
}
