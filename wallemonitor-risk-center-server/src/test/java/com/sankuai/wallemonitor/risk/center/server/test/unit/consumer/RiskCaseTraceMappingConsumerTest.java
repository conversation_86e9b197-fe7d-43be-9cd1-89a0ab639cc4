package com.sankuai.wallemonitor.risk.center.server.test.unit.consumer;

import static groovy.util.GroovyTestCase.assertEquals;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseTraceMappingMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.server.consumer.RiskCaseTraceMappingConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.DataTestBase;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import java.util.Arrays;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

public class RiskCaseTraceMappingConsumerTest extends ServiceTestBase {

    @InjectMocks
    private RiskCaseTraceMappingConsumer consumer;


    private String msg = "{\n"
            + "  \"body\": {\n"
            + "    \"vin\": \"vin1\",\n"
            + "    \"traceId\": \"traceId1\",\n"
            + "    \"eventIdList\": [\"event1\", \"event2\"]\n"
            + "  }\n"
            + "}";

    @Before
    public void setUp() {
        // 初始化数据
        DataTestBase dataTestBase = new DataTestBase();

        // mock 分布式锁
        mockRedisAndLock();

        // mock riskCaseVehicleRelationRepository.queryByParam
        mockRiskCaseVehicleRelation(dataTestBase);

        // mock riskCaseRepository.queryByParam
        mockRiskCase(dataTestBase);
    }

    /**
     * 测试消息正常处理的场景
     */
    @Test
    public void testReceiveNormalCase() throws Throwable {
        EveMqCommonMessage<RiskCaseTraceMappingMessageDTO> message = JacksonUtils.from(msg,
                new TypeReference<EveMqCommonMessage<RiskCaseTraceMappingMessageDTO>>() {
                });
        // 调用
        ConsumeStatus status = consumer.receive(JacksonUtils.to(message));

        // 验证
        Mockito.verify(riskCaseVehicleRelationRepository).batchSave(Arrays.asList(
                RiskCaseVehicleRelationDO.builder().caseId("caseId2").eventId("event2").traceId("traceId1").vin("vin1")
                        .isDeleted(IsDeleteEnum.NOT_DELETED).status(RiskCaseVehicleStatusEnum.INIT).build()
        ));
        Mockito.verify(riskCaseRepository).batchSave(Arrays.asList(RiskCaseDO.builder()
                .caseId("caseId2")
                .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                .status(RiskCaseStatusEnum.NO_DISPOSAL)
                .isDeleted(IsDeleteEnum.NOT_DELETED).build()));
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
    }
}
