package com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base;

import com.sankuai.wallemonitor.risk.center.server.StartApp;
import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/1/12
 */
@Slf4j
@ActiveProfiles("test")
@SpringBootTest(classes = StartApp.class)
@RunWith(SpringRunner.class)
public abstract class SpringTestBase {

}
