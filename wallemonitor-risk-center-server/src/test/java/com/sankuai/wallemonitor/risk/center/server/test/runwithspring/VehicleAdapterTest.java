package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

@Slf4j
public class VehicleAdapterTest extends SpringTestBase {

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Test
    public void test() {

        vehicleAdapter.queryRuntimeVehicleInfo(VehicleRuntimeInfoParamVTO.builder()
                .vinList(Arrays.asList("LMTZSV000MC000037")).build());

    }


    @Test
    public void queryHdMapVersion() {

        vehicleAdapter.getVehicleHdMapVersion("LMTZSV022NC080001");
    }
}
