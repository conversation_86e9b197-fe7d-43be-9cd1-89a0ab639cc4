package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.junit.Test;

public class NegativePublicEventRepositoryImplTest extends SpringTestBase {

    @Resource
    private NegativePublicEventRepository negativePublicEventRepository;

    @Test
    public void save() {}

    @Test
    public void queryByParam() {
        negativePublicEventRepository
                .queryByParam(NegativePublicEventDOQueryParamDTO.builder().eventIdLike("1").build());
    }
}