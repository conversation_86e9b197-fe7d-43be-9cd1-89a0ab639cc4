package com.sankuai.wallemonitor.risk.infra.dto.onboardmessage;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.map.open.platform.api.drivingmapmatch.Coordinate;
import com.sankuai.map.open.platform.api.drivingmapmatch.DrivingMapMatchRequest;
import com.sankuai.map.open.platform.api.drivingmapmatch.DrivingMapMatchResponse;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;

import java.util.ArrayList;
import java.util.List;

public class OnboardMessageTest {
    public static void main(String[] args) {

    }
}
