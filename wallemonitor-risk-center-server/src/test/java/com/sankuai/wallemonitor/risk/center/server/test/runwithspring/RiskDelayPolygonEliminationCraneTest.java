package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;


import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.server.crane.RiskDelayPolygonEliminationCrane;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import com.sankuai.wallemonitor.risk.center.server.test.unit.base.ServiceTestBase;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class RiskDelayPolygonEliminationCraneTest extends SpringTestBase {

    @Resource
    private RiskDelayPolygonEliminationCrane riskDelayPolygonEliminationCrane;


    @Test
    public void testQueryDateByMinimumMarkCase() {
        Date endDate = new Date();
        int minimumCase = 1;
        int k = 7;
        Date date = riskDelayPolygonEliminationCrane.queryDateByMinimumMarkCase( endDate, minimumCase, k);

        System.out.println(date);
    }
}
