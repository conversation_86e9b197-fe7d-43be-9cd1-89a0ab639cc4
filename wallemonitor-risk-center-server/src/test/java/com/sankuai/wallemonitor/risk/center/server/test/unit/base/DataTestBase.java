package com.sankuai.wallemonitor.risk.center.server.test.unit.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventProcessResultDO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GisInfoVTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Builder
@AllArgsConstructor
@Data
public class DataTestBase {

    public List<DomainEventProcessResultDO> resultDOlist = Arrays.asList(DomainEventProcessResultDO.builder()
            .traceId("traceId123")
            .entry(DomainEventEntryDTO.builder().operateEntry(OperateEnterActionEnum.RISK_EVENT_CONSUMER_ENTER)
                    .domainClassName("RiskCaseVehicleRelationDO").build())
            .eventTime(System.currentTimeMillis())
            .processName("RiskCaseMessageNoticeProcess")
            .processResult(false)
            .retriedTime(0) // 已重试次数，默认最多重试三次
            .build());

    private final String riskCaseDOStr = "{\n"
            + "    \"caseId\": \"015a397c6d0a4a8a857da122b12820c1\",\n"
            + "    \"type\": \"VEHICLE_SIDE_BY_SIDE\",\n"
            + "    \"placeCode\": \"hualikan\",\n"
            + "    \"status\": \"NO_DISPOSAL\",\n"
            + "    \"eventId\": \"20240617184839021_common398_s20-173\",\n"
            + "    \"source\": \"PNC\",\n"
            + "    \"occurTime\": \"2024-06-17 18:00:00\",\n"
            + "    \"closeTime\": \"2024-06-17 18:48:39\",\n"
            + "    \"extInfo\": {\n"
            + "        \"city\": \"北京市\",\n"
            + "        \"are\": \"顺义区\",\n"
            + "        \"poi\": \"莫奈花园\"\n"
            + "    },\n"
            + "    \"isDeleted\": \"NOT_DELETED\"\n"
            + "}";
    public final List<RiskCaseDO> riskCaseDOList = new ArrayList<>(Arrays.asList(
            RiskCaseDO.builder()
                    .caseId("caseId1")
                    .type(RiskCaseTypeEnum.VEHICLE_SIDE_BY_SIDE)
                    .status(RiskCaseStatusEnum.NO_DISPOSAL)
                    .build(),
            RiskCaseDO.builder()
                    .caseId("caseId2")
                    .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                    .status(RiskCaseStatusEnum.IN_DISPOSAL)
                    .build(),
            RiskCaseDO.builder()
                    .caseId("123456")
                    .status(RiskCaseStatusEnum.IN_DISPOSAL)
                    .source(RiskCaseSourceEnum.SAFEGUARD_SYSTEM)
                    .eventId("20240711101433957_adc-stagnant-recall_MKZ-08")
                    .build(),
            RiskCaseDO.builder()
                    .caseId("678910")
                    .status(RiskCaseStatusEnum.IN_DISPOSAL)
                    .eventId("20240703194555037_traffic-jam_755964c6-7a9f-4d6c-a646-5b2570a9df64")
                    .build()
    ));

    public final List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = Arrays.asList(
            RiskCaseVehicleRelationDO.builder()
                    .id(1L)
                    .caseId("RC20231002")
                    .eventId("EV20231002")
                    .vin("LMTZSV023MC063496")
                    .traceId("a73220a5-2f6b-4238-8c8c-0c47dbc44096")
                    .vehicleSnapshotInfo(VehicleInfoDO.builder()
                            .vehicleId("V123456")
                            .build())
                    .extInfo(RiskVehicleExtInfoDO.builder().build())
                    .status(RiskCaseVehicleStatusEnum.ASSIGNED) // 表示已分配
                    .sideBySideTimestamp("20231002123000")
                    .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                    .isDeleted(IsDeleteEnum.NOT_DELETED)
                    .build(),
            RiskCaseVehicleRelationDO.builder()
                    .id(2L)
                    .caseId("015a397c6d0a4a8a857da122b12820c1")
                    .eventId("EV20231002")
                    .vin("LMTZSV024MC048701")
                    .traceId("s20-175_1720407707572")
                    .vehicleSnapshotInfo(VehicleInfoDO.builder()
                            .vehicleId("V123456")
                            .vehicleName("S20-123")
                            .vin("LMTZSV024MC048701")
                            .build())
                    .extInfo(RiskVehicleExtInfoDO.builder().build())
                    .status(RiskCaseVehicleStatusEnum.ASSIGNED) // 表示已分配
                    .sideBySideTimestamp("20231002123000")
                    .type(RiskCaseTypeEnum.VEHICLE_SIDE_BY_SIDE)
                    .occurTime(new Date(1720654101453L))
                    .isDeleted(IsDeleteEnum.NOT_DELETED)
                    .build(),
            RiskCaseVehicleRelationDO.builder()
                    .vin("vin1")
                    .caseId("caseId1")
                    .eventId("event1")
                    .traceId("traceId1")
                    .status(RiskCaseVehicleStatusEnum.INIT)
                    .build(),
            RiskCaseVehicleRelationDO.builder()
                    .vin("vin1")    // 和上面在同一辆车
                    .caseId("caseId2")
                    .eventId("event2")
                    .traceId("traceId2")
                    .status(RiskCaseVehicleStatusEnum.ASSIGNED)
                    .build(),
            RiskCaseVehicleRelationDO.builder()
                    .vin("3LN6L5SU2LR601635")
                    .caseId("123456")
                    .eventId("20240711101433957_adc-stagnant-recall_MKZ-08")
                    .createTime(new Date(1720654101453L))
                    .milliBeginTime(new Date(1720654101453L))
                    .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                    .status(RiskCaseVehicleStatusEnum.ASSIGNED)
                    .build()
    );

    public List<VehicleInfoDO> vehicleInfoDOList = Arrays.asList(
            VehicleInfoDO.builder()
                    .vin("LMTZSV026MC093297")
                    .vehicleId("M1234")
                    .vehicleName("S20-123")
                    .purpose("Transport")
                    .vhr(VHRModeEnum.VHR_EQUALS_ONE)
                    .position(new PositionDO(30.0, 120.0, CoordinateSystemEnum.WGS84))
                    .placeCode("001")
                    .autocarVersion("v1.0")
                    .driveMode(1)
                    .build(),
            VehicleInfoDO.builder()
                    .vin("3LN6L5SU2LR601635")
                    .vehicleId("M5678")
                    .vehicleName("S20-789")
                    .purpose("Transport")
                    .vhr(VHRModeEnum.VHR_EQUALS_ONE)
                    .position(new PositionDO(30.0, 120.0, CoordinateSystemEnum.WGS84))
                    .placeCode("001")
                    .autocarVersion("v1.0")
                    .driveMode(1)
                    .build(),
            VehicleInfoDO.builder().vin("LMTZSV029MC063825").position(PositionDO.builder().build()).build(),
            VehicleInfoDO.builder().vin("LMTZSV029MC063826").position(PositionDO.builder().build()).build(),
            VehicleInfoDO.builder().vin("LMTZSV029MC063827").position(PositionDO.builder().build()).build()
    );

    public List<GisInfoVTO> gisInfoDOList = Arrays.asList(
            GisInfoVTO.builder()
                    .poi("aaa").city("beijing").area("hualikan").latitude(30.123456).longitude(120.123456)
                    .coordinateSystem(CoordinateSystemEnum.WGS84)
                    .build()
    );

    // lion key: case.broadcast.strategy.config
    private final String caseBroadCaseStrategyConfig = "{\n"
            + "    \"1\": {\n"
            + "        \"delaySeconds\": 0,\n"
            + "        \"sourceDelaySeconds\": {\n"
            + "            \"1\": 0,\n"
            + "            \"2\": 0,\n"
            + "            \"3\": 0\n"
            + "        },\n"
            + "        \"createTimeQueryStartOffsetMinutes\": 1,\n"
            + "        \"blackAutocarVersionList\": [],\n"
            + "        \"groupTemplateList\": [\n"
            + "            {\n"
            + "                \"groupIdList\": [\n"
            + "                    64012247676\n"
            + "                ],\n"
            + "                \"groupTemplateName\": \"withImageAndSimple\",\n"
            + "                \"templateId\": 4725,\n"
            + "                \"templateValues\": {\n"
            + "                    \"caseTitleWithStatusAndDurationText\": \"$!{caseTitleWithStatusAndDurationText}\",\n"
            + "                    \"cityAndRoadName\": \"$!{city}-$!{roadName}\",\n"
            + "                    \"caseTitle\": \"$!{caseTitle}\",\n"
            + "                    \"themeColor\": \"$!{themeColor}\",\n"
            + "                    \"occurTime\": \"$!{occurTime}\",\n"
            + "                    \"nowTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop\",\n"
            + "                    \"vehicleNameLink\": \"$!{vehicleNameLink}\",\n"
            + "                    \"vhrAndPurpose\": \"$!{purpose}($!{vhrMode})\",\n"
            + "                    \"goodCaseLink\": \"https://eve.mad.test.sankuai.com/risk/api/admin/markAndDispose?caseId=$!{firstVehicle.caseId}&category=GOOD&subCategory=GOOD_OTHER\",\n"
            + "                    \"badCaseLink\": \"https://eve.mad.test.sankuai.com/risk/api/admin/markAndDispose?caseId=$!{firstVehicle.caseId}&category=BAD&subCategory=OTHER&closeCase=true\",\n"
            + "                    \"occurTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop&occurTime=$!{DatetimeUtil.formatDate($!{firstVehicle.occurTime},\\\"yyyyMMddHHmmss\\\")}\"\n"
            + "                }\n"
            + "            },\n"
            + "            {\n"
            + "                \"groupIdList\": [\n"
            + "                    64012247676\n"
            + "                ],\n"
            + "                \"groupTemplateName\": \"withOutImageAndSimple\",\n"
            + "                \"templateId\": 4662,\n"
            + "                \"templateValues\": {\n"
            + "                    \"roadName\": \"$!{roadName}\",\n"
            + "                    \"caseTitle\": \"$!{caseTitle}\",\n"
            + "                    \"themeColor\": \"$!{themeColor}\",\n"
            + "                    \"occurTime\": \"$!{occurTime}\",\n"
            + "                    \"vehicleNames\": \"$!{vehicleNames}\",\n"
            + "                    \"vhrMode\": \"$!{vhrMode}\",\n"
            + "                    \"purpose\": \"$!{purpose}\",\n"
            + "                    \"city\": \"$!{city}\",\n"
            + "                    \"replayLink\": \"https://walle.sankuai.com/m/csm/vehicle/$!{vin}\",\n"
            + "                    \"replyLink\": \"\",\n"
            + "                    \"btnDisabled\": \"$!{btnDisabled}\",\n"
            + "                    \"btnText\": \"$!{btnText}\",\n"
            + "                    \"occurTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop&occurTime=$!{DatetimeUtil.formatDate($!{firstVehicle.occurTime},\\\"yyyyMMddHHmmss\\\")}\"\n"
            + "                }\n"
            + "            }\n"
            + "        ],\n"
            + "        \"placeCodeWhiteList\": [\n"
            + "            \"ALL\"\n"
            + "        ],\n"
            + "        \"retryMaxSeconds\": 60\n"
            + "    },\n"
            + "    \"2\": {\n"
            + "        \"delaySeconds\": 0,\n"
            + "        \"sourceDelaySeconds\": {\n"
            + "            \"1\": 0,\n"
            + "            \"2\": 0,\n"
            + "            \"3\": 0\n"
            + "        },\n"
            + "        \"createTimeQueryStartOffsetMinutes\": 1,\n"
            + "        \"groupTemplateList\": [\n"
            + "            {\n"
            + "                \"templateId\": 4662,\n"
            + "                \"groupTemplateName\": \"withOutImageAndSimple\",\n"
            + "                \"groupIdList\": [\n"
            + "                    64012247676,\n"
            + "                    64012250440\n"
            + "                ],\n"
            + "                \"templateValues\": {\n"
            + "                    \"roadName\": \"$!{roadName}\",\n"
            + "                    \"caseTitle\": \"$!{caseTitle}\",\n"
            + "                    \"themeColor\": \"$!{themeColor}\",\n"
            + "                    \"occurTime\": \"$!{occurTime}\",\n"
            + "                    \"vehicleNames\": \"$!{vehicleNames}\",\n"
            + "                    \"vhrMode\": \"$!{vhrMode}\",\n"
            + "                    \"purpose\": \"$!{purpose}\",\n"
            + "                    \"city\": \"$!{city}\",\n"
            + "                    \"replayLink\": \"https://walledata.mad.test.sankuai.com/m/csm/vehicle/$!{vin}#perspectives=loop&startTime=$!{DatetimeUtil.formatTime($!{DatetimeUtil.getNSecondsBeforeDateTime($!DatetimeUtil.convertDatetimeStr2Date($!{occurTime}),5)})}\",\n"
            + "                    \"replyLink\": \"\",\n"
            + "                    \"btnDisabled\": \"$!{btnDisabled}\",\n"
            + "                    \"btnText\": \"$!{btnText}\",\n"
            + "                    \"occurTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop&occurTime=$!{DatetimeUtil.formatDate($!{firstVehicle.occurTime},\\\"yyyyMMddHHmmss\\\")}\"\n"
            + "                }\n"
            + "            }\n"
            + "        ],\n"
            + "        \"blackAutocarVersionList\": [\n"
            + "            \"\"\n"
            + "        ],\n"
            + "        \"placeCodeWhiteList\": [\n"
            + "            \"ALL\"\n"
            + "        ],\n"
            + "        \"retryMaxSeconds\": \"20\"\n"
            + "    },\n"
            + "    \"3\": {\n"
            + "        \"delaySeconds\": 0,\n"
            + "        \"sourceDelaySeconds\": {\n"
            + "            \"1\": 0,\n"
            + "            \"2\": 0,\n"
            + "            \"3\": 0\n"
            + "        },\n"
            + "        \"createTimeQueryStartOffsetMinutes\": 1,\n"
            + "        \"groupTemplateList\": [\n"
            + "            {\n"
            + "                \"groupIdList\": [\n"
            + "                    64012247676,\n"
            + "                    64012250440\n"
            + "                ],\n"
            + "                \"templateId\": 4663,\n"
            + "                \"groupTemplateName\": \"withOutImageAndSimple\",\n"
            + "                \"templateValues\": {\n"
            + "                    \"roadName\": \"$!{roadName}\",\n"
            + "                    \"caseTitle\": \"$!{caseTitle}\",\n"
            + "                    \"themeColor\": \"$!{themeColor}\",\n"
            + "                    \"occurTime\": \"$!{occurTime}\",\n"
            + "                    \"vehicleNames\": \"$!{vehicleNames}\",\n"
            + "                    \"vhrMode\": \"$!{vhrMode}\",\n"
            + "                    \"purpose\": \"$!{purpose}\",\n"
            + "                    \"city\": \"$!{city}\",\n"
            + "                    \"replayLink\": \"https://walledata.mad.test.sankuai.com/m/csm/vehicle/$!{vin}#perspectives=loop&startTime=$!{DatetimeUtil.formatTime($!{DatetimeUtil.getNSecondsBeforeDateTime($!DatetimeUtil.convertDatetimeStr2Date($!{occurTime}),5)})}\",\n"
            + "                    \"replyLink\": \"\",\n"
            + "                    \"btnDisabled\": \"$!{btnDisabled}\",\n"
            + "                    \"btnText\": \"$!{btnText}\",\n"
            + "                    \"vehicleInfoStr\": \"$!{vehicleInfoStr}\",\n"
            + "                    \"vehicleNumber\": \"$!{vehicleNumber}\",\n"
            + "                    \"vehicleId\": \"$!{vehicleId}\",\n"
            + "                    \"occurTimeImg\": \"https://walledata.mad.test.sankuai.com/replay/video/occurTime?vin=$!{firstVehicle.vin}&view=loop&occurTime=$!{DatetimeUtil.formatDate($!{firstVehicle.occurTime},\\\"yyyyMMddHHmmss\\\")}\"\n"
            + "                }\n"
            + "            }\n"
            + "        ],\n"
            + "        \"blackAutocarVersionList\": [\n"
            + "            \"\"\n"
            + "        ],\n"
            + "        \"placeCodeWhiteList\": [\n"
            + "            \"ALL\"\n"
            + "        ],\n"
            + "        \"retryMaxSeconds\": \"20\"\n"
            + "    }\n"
            + "}";

    // mock 消息推送配置
    public Map<String, BroadCastStrategyConfigDTO> config;
    public Map<RiskCaseTypeEnum, BroadCastStrategyConfigDTO> map;

    // lion key:risk.center.autocar.eventcode.whitelist
    public List<Integer> focusedAutocarEventCodeList = Arrays.asList(398, 399, 5003, 5004, 5006, 5007);

    // 车辆名称与VIN的映射关系，原本需要通过thrift来查询，这里直接写个map存数据
    public Map<String, String> carNameToVinMap = new HashMap<String, String>() {{
        put("s20-191", "LMTZSV029MC063825");
        put("s20-194", "LMTZSV029MC063826");
        put("s20-250", "LMTZSV029MC063827");
    }};

    // 构造函数内可以存放修改数据逻辑，可以放例如现在的瞬时时间等等
    public DataTestBase() {
        RiskCaseDO riskCaseDO = JacksonUtils.from(riskCaseDOStr, RiskCaseDO.class);
        riskCaseDO.setCreateTime(new Date());
        riskCaseDOList.add(riskCaseDO);
        config = JacksonUtils.from(
                caseBroadCaseStrategyConfig,
                new TypeReference<Map<String, BroadCastStrategyConfigDTO>>() {
                });
        map = config.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> RiskCaseTypeEnum.findByValue(Integer.valueOf(entry.getKey())), Entry::getValue));
    }

}
