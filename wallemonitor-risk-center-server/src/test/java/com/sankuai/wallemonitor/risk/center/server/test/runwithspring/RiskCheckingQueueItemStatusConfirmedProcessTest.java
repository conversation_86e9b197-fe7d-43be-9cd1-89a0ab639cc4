package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCheckingQueueItemStatusConfirmedProcess;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Collections;
import javax.annotation.Resource;
import org.apache.thrift.TException;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024/12/11
 */
public class RiskCheckingQueueItemStatusConfirmedProcessTest extends SpringTestBase {

    @Resource
    private RiskCheckingQueueItemStatusConfirmedProcess riskCheckingQueueItemStatusConfirmedProcess;

    @Test
    public void test() throws TException {
        DomainEventDTO<RiskCheckingQueueItemDO> eventDTO = DomainEventDTO.<RiskCheckingQueueItemDO>builder()
                .after(Collections.singletonList(RiskCheckingQueueItemDO.builder()
                        .checking(true)
                        .status(RiskQueueStatusEnum.CONFIRMED_RISK)
                        .eventId("123123123")
                        .tmpCaseId("M603320250426151500S01T01")
                        .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                        .round(0)
                        .source(RiskCaseSourceEnum.BEACON_TOWER)
                        .occurTime(null)
                        .vin("LMTZSV025NC040897")
                        .build()))
                .build();
        riskCheckingQueueItemStatusConfirmedProcess.process(
                DomainEventFactory.createDomainEventChangeDTO(eventDTO, RiskCheckingQueueItemDO.class));
    }

    public static void main(String[] args) {
        RiskCheckingQueueItemDO build = RiskCheckingQueueItemDO.builder()
                .checking(true)
                .status(RiskQueueStatusEnum.CONFIRMED_RISK)
                .eventId("25645114")
                .tmpCaseId("M324820241211115337S03T01")
                .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                .round(0)
                .source(RiskCaseSourceEnum.BEACON_TOWER)
                .occurTime(null)
                .vin("LSTM00001322")
                .build();
        System.out.println(JacksonUtils.to(build));

    }

}
