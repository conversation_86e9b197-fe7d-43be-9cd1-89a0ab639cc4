package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.enums.SquirrelCategoryEnum;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.junit.Test;

public class SquirrelAdapterTest extends SpringTestBase {


    @Resource
    private SquirrelAdapter squirrelAdapter;


    @Test
    public void test() {
        squirrelAdapter.get(SquirrelCategoryEnum.TABLE_FIELD_UPDATED_TIME_CATEGORY, "123");
    }


}
