package com.sankuai.wallemonitor.risk.center.server.test.unit.utiltest;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.UrlEncodeUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024/1/25
 */
public class CheckUtilTest {

    @Test(expected = ParamInputErrorException.class)
    public void testIsAllNotNullStringNull() {
        Map<String, String> testMap = new HashMap<>();
        testMap.put("1", "1");
        TestClass testClass = TestClass.builder()
                .stringField("")
                .integerField(1)
                .listField(Lists.newArrayList("1"))
                .mapField(testMap)
                .customField(new CustomClass()).build();
        CheckUtil.isAllNotEmpty(testClass, "Fields cannot be null");
    }

    @Test(expected = ParamInputErrorException.class)
    public void testIsAllNotNullIntegerNull() {
        Map<String, String> testMap = new HashMap<>();
        testMap.put("1", "1");
        TestClass testClass = TestClass.builder()
                .stringField("not null")
                .listField(Lists.newArrayList("1"))
                .mapField(testMap)
                .customField(new CustomClass()).build();
        CheckUtil.isAllNotEmpty(testClass, "Fields cannot be null");
    }

    @Test(expected = ParamInputErrorException.class)
    public void testIsAllNotNullListNull() {
        Map<String, String> testMap = new HashMap<>();
        testMap.put("1", "1");
        TestClass testClass = TestClass.builder()
                .stringField("not null")
                .integerField(1)
                .listField(Lists.newArrayList())
                .mapField(testMap)
                .customField(new CustomClass()).build();
        CheckUtil.isAllNotEmpty(testClass, "Fields cannot be null");
    }

    @Test(expected = ParamInputErrorException.class)
    public void testIsAllNotNullMapNull() {
        TestClass testClass = TestClass.builder()
                .stringField("not null")
                .integerField(1)
                .listField(Lists.newArrayList("1"))
                .mapField(new HashMap<>())
                .customField(new CustomClass()).build();
        CheckUtil.isAllNotEmpty(testClass, "Fields cannot be null");
    }

    @Test(expected = ParamInputErrorException.class)
    public void testIsAllNotNullCustomNull() {
        TestClass testClass = TestClass.builder()
                .stringField("not null")
                .integerField(1)
                .listField(Lists.newArrayList("1"))
                .mapField(new HashMap<>())
                .build();
        CheckUtil.isAllNotEmpty(testClass, "Fields cannot be null");
    }

    @Test
    public void testIsAllNotNullPass() {
        Map<String, String> testMap = new HashMap<>();
        testMap.put("1", "1");
        TestClass notNull = TestClass.builder()
                .stringField("not null")
                .integerField(1)
                .listField(Lists.newArrayList("1"))
                .mapField(testMap)
                .customField(new CustomClass()).build();

        CheckUtil.isAllNotEmpty(notNull, "Fields cannot be null");
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TestClass {

        private String stringField;
        private Integer integerField;
        private List<String> listField;
        private Map<String, String> mapField;
        private CustomClass customField;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    public static class CustomClass {

        private String field;
    }

    @Test
    public void testFormatUrlParam() {
        Date date = new Date(1674508800000L);
        String time = DatetimeUtil.formatTime(date);
        String url = "https://walledata.mad.test.sankuai.com/app/monitor/v2?playType=backup&vin=LMTZSV024MC062730&time=";
        System.out.println(url + time);
        System.out.println(url + UrlEncodeUtil.formatUrlParam(time));
    }
}
