package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.api.request.AdminMarkRequest;
import com.sankuai.wallemonitor.risk.center.api.thrift.IThriftAdminService;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
public class IThriftManualRiskCaseServiceTest extends SpringTestBase {

    @Resource
    private IThriftAdminService service;

    @Test
    public void test() {
        AdminMarkRequest request = new AdminMarkRequest();
        request.setCaseId("5b94c96737de452a9d266adf34cb92ad");
        request.setCategory("BAD");
        request.setLevel(4);
        request.setSubCategory("ON_TRAILER");
        request.setDesc("123");
        service.mark(request);
    }

    @Test
    public void test1() {
        AdminMarkRequest request = new AdminMarkRequest();
        request.setCaseId("M523020241011011231S01T01");
        request.setCategory("BAD");
        request.setSubCategory("ON_TRAILER");
        service.mark(request);
    }
}
