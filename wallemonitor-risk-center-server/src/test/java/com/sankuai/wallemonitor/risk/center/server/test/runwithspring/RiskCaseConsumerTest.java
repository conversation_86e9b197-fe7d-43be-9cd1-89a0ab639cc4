package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walledelivery.sample.message.MqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleTrafficRepository;
import com.sankuai.wallemonitor.risk.center.server.consumer.RiskCaseEventConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Collections;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.junit.Test;

public class RiskCaseConsumerTest extends SpringTestBase {

    private String s_start = "{\"type\":20,\"body\":{\"eventId\":\"25645114\",\"source\":3,\"type\":1,\"status\":10,\"vinList\":[\"LMTZSV022NC017593\"],\"traceId\":\"\"},\"timestamp\":1728712604000}";
    private String s_end = "{\"type\":20,\"body\":{\"eventId\":\"25645114\",\"source\":3,\"type\":1,\"status\":30,\"vinList\":[\"LMTZSV022NC017593\"],\"traceId\":\"\"},\"timestamp\":1728712804000}";

    @Resource
    private RiskCaseEventConsumer riskCaseEventConsumer;

    @Resource
    private VehicleTrafficRepository vehicleContextRepository;

    @Test
    @SneakyThrows
    public void test_vehicle_stagnation() {
        MqCommonMessage<RiskCaseMessageDTO> startMqCommonMessage = JacksonUtils.from(s_start,
                new TypeReference<MqCommonMessage<RiskCaseMessageDTO>>() {
                });
        MqCommonMessage<RiskCaseMessageDTO> endMqCommonMessage = JacksonUtils.from(s_end,
                new TypeReference<MqCommonMessage<RiskCaseMessageDTO>>() {
                });
        startMqCommonMessage.setTimestamp(System.currentTimeMillis());
        endMqCommonMessage.setTimestamp(System.currentTimeMillis() + 10000);
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        riskCaseEventConsumer.receive(JacksonUtils.to(startMqCommonMessage));
        // 提交一个延时执行的任务，延时3秒执行，并返回Future对象
//        ConsumeStatus consumeStatus = riskCaseEventConsumer.receive(JacksonUtils.to(endMqCommonMessage));
//        Future<ConsumeStatus> resultFuture = scheduler.schedule(() -> {
//            ConsumeStatus consumeStatus = riskCaseEventConsumer.receive(JacksonUtils.to(endMqCommonMessage));
//            return consumeStatus;
//        }, 10, TimeUnit.SECONDS);
//        resultFuture.get();
//        //拦截逻辑
//         ConsumeStatus consumeStatus = vehicleDataEventConsumer.receive(JacksonUtils.to(s_vehicleEventDataDO));
    }

    @Test
    public void test_end_before_start_retry() {
        MqCommonMessage<RiskCaseMessageDTO> endMqCommonMessage = JacksonUtils.from(s_end,
                new TypeReference<MqCommonMessage<RiskCaseMessageDTO>>() {
                });
        // 直接存肯定会没落库，因为对应的开始消息不存在
        riskCaseEventConsumer.receive(JacksonUtils.to(endMqCommonMessage));
    }

    @Test
    @SneakyThrows
    public void test_end_before_start_normal() {
        MqCommonMessage<RiskCaseMessageDTO> startMqCommonMessage = JacksonUtils.from(s_start,
                new TypeReference<MqCommonMessage<RiskCaseMessageDTO>>() {
                });
        MqCommonMessage<RiskCaseMessageDTO> endMqCommonMessage = JacksonUtils.from(s_end,
                new TypeReference<MqCommonMessage<RiskCaseMessageDTO>>() {
                });
        startMqCommonMessage.setTimestamp(System.currentTimeMillis() - 1000);
        endMqCommonMessage.setTimestamp(System.currentTimeMillis());
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1000);
        // 测试同时并发模拟，最后成功落库start，并落库end修改start消息的情况
        Future<ConsumeStatus> resultFuture1 = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = riskCaseEventConsumer.receive(JacksonUtils.to(endMqCommonMessage));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = riskCaseEventConsumer.receive(JacksonUtils.to(endMqCommonMessage));
                Thread.sleep(1000);
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        }, 0, TimeUnit.SECONDS);

        Future<ConsumeStatus> resultFuture2 = scheduler.schedule(() -> {
            ConsumeStatus consumeStatus = riskCaseEventConsumer.receive(JacksonUtils.to(startMqCommonMessage));
            while (consumeStatus != ConsumeStatus.CONSUME_SUCCESS) {
                consumeStatus = riskCaseEventConsumer.receive(JacksonUtils.to(startMqCommonMessage));
                Thread.sleep(1000);
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        }, 0, TimeUnit.SECONDS);
        resultFuture1.get();
        resultFuture2.get();
    }


    @Test
    public void testVehicleTraffic() {

        vehicleContextRepository.batchGetVehicleTrafficContext(
                Collections.singletonList("LMTZSV026NC007729"));
    }
}
