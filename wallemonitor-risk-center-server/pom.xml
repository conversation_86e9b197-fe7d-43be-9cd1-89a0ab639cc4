<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <artifactId>wallemonitor-risk-center-server</artifactId>
  <build>
    <plugins>
      <plugin>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <groupId>org.springframework.boot</groupId>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <annotationProcessorPaths>
            <path>
              <artifactId>lombok</artifactId>
              <groupId>org.projectlombok</groupId>
              <version>1.18.24</version>
            </path>
            <path>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <groupId>org.projectlombok</groupId>
              <version>0.2.0</version>
            </path>
            <path>
              <artifactId>mapstruct-processor</artifactId>
              <groupId>org.mapstruct</groupId>
              <version>1.5.2.Final</version>
            </path>
          </annotationProcessorPaths>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
        <groupId>org.apache.maven.plugins</groupId>
        <version>3.8.1</version>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>ch.hsr</groupId>
      <artifactId>geohash</artifactId>
    </dependency>
    <dependency>
      <groupId>com.meituan.databus</groupId>
      <artifactId>dbusUtils_thrift0.9.2</artifactId>
    </dependency>
    <dependency>
      <artifactId>xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
      <artifactId>open-sdk</artifactId>
    </dependency>
    <dependency>
      <artifactId>thrift-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>web-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>threadpool-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>crane-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>

    <dependency>
      <artifactId>walle-eve-domain</artifactId>
      <groupId>com.sankuai.walleeve</groupId>
    </dependency>
    <dependency>
      <artifactId>mapstruct</artifactId>
      <groupId>org.mapstruct</groupId>
    </dependency>
    <dependency>
      <artifactId>mapstruct-processor</artifactId>
      <groupId>org.mapstruct</groupId>
      <scope>provided</scope>
    </dependency>
    <!-- 单测相关 -->
    <dependency>
      <artifactId>mockito-core</artifactId>
      <groupId>org.mockito</groupId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <artifactId>runtime</artifactId>
      <groupId>com.meituan.ut.toolkit</groupId>
      <scope>test</scope>
      <version>1.0.10</version>
    </dependency>
    <!--powermock依赖-->
    <dependency>
      <artifactId>powermock-module-junit4</artifactId>
      <groupId>org.powermock</groupId>
      <scope>test</scope>
      <version>2.0.2</version>
    </dependency>
    <dependency>
      <artifactId>powermock-api-mockito2</artifactId>
      <groupId>org.powermock</groupId>
      <scope>test</scope>
      <version>2.0.2</version>
    </dependency>
    <dependency>
      <groupId>com.sankuai.wallemonitor</groupId>
      <artifactId>wallemonitor-risk-center-infra</artifactId>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-commons</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sankuai.wallemonitor</groupId>
      <artifactId>wallemonitor-risk-center-domain</artifactId>
    </dependency>
    <!-- 单测相关 end-->

  </dependencies>

  <groupId>com.sankuai.wallemonitor</groupId>
  <modelVersion>4.0.0</modelVersion>
  <name>wallemonitor-risk-center-server</name>
  <packaging>jar</packaging>

  <parent>
    <artifactId>wallemonitor-risk-center</artifactId>
    <groupId>com.sankuai.wallemonitor</groupId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <profiles>
    <profile>
      <build>
        <resources>
          <resource>
            <directory>src/main/profiles/dev</directory>
          </resource>
          <resource>
            <directory>src/main/resources</directory>
            <filtering>true</filtering>
          </resource>
        </resources>
      </build>
      <id>dev</id>
      <properties>
        <active-profile>dev</active-profile>
      </properties>
    </profile>
    <profile>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <build>
        <resources>
          <resource>
            <directory>src/main/profiles/test</directory>
          </resource>
          <resource>
            <directory>src/main/resources</directory>
            <filtering>true</filtering>
          </resource>
        </resources>
      </build>
      <id>test</id>
      <properties>
        <active-profile>test</active-profile>
      </properties>
    </profile>
    <profile>
      <build>
        <resources>
          <resource>
            <directory>src/main/profiles/staging</directory>
          </resource>
          <resource>
            <directory>src/main/resources</directory>
            <filtering>true</filtering>
          </resource>
        </resources>
      </build>
      <id>staging</id>
      <properties>
        <active-profile>staging</active-profile>
      </properties>
    </profile>
    <profile>
      <build>
        <resources>
          <resource>
            <directory>src/main/profiles/prod</directory>
          </resource>
          <resource>
            <directory>src/main/resources</directory>
            <filtering>true</filtering>
          </resource>
        </resources>
      </build>
      <id>prod</id>
      <properties>
        <active-profile>prod</active-profile>
      </properties>
    </profile>
  </profiles>

  <version>1.0.0-SNAPSHOT</version>

</project>