<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <artifactId>wallemonitor-risk-center-domain</artifactId>
  <build>
    <plugins>
      <!--            <plugin>-->
      <!--                <groupId>com.meituan.xframe.generator</groupId>-->
      <!--                <artifactId>mybatis-generator-maven-plugin</artifactId>-->
      <!--                <configuration>-->
      <!--                    <overwrite>true</overwrite>-->
      <!--                </configuration>-->
      <!--            </plugin>-->
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>com.dianping.rhino</groupId>
      <artifactId>rhino-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dianping.rhino</groupId>
      <artifactId>rhino-redis-squirrel</artifactId>
    </dependency>
    <dependency>
      <groupId>com.opencsv</groupId>
      <artifactId>opencsv</artifactId>
    </dependency>

    <dependency>
      <groupId>nz.ac.waikato.cms.weka</groupId>
      <artifactId>weka-stable</artifactId>
    </dependency>
    <dependency>
      <artifactId>mapstruct</artifactId>
      <groupId>org.mapstruct</groupId>
    </dependency>
    <dependency>
      <artifactId>mapstruct-processor</artifactId>
      <groupId>org.mapstruct</groupId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <artifactId>zebra-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>wallemonitor-risk-center-infra</artifactId>
      <groupId>com.sankuai.wallemonitor</groupId>
    </dependency>
    <dependency>
      <artifactId>lombok</artifactId>
      <groupId>org.projectlombok</groupId>
    </dependency>
    <dependency>
      <artifactId>commons-collections</artifactId>
      <groupId>commons-collections</groupId>
    </dependency>
    <dependency>
      <artifactId>commons-lang3</artifactId>
      <groupId>org.apache.commons</groupId>
    </dependency>
    <dependency>
      <artifactId>walle-eve-utils</artifactId>
      <groupId>com.sankuai.walleeve</groupId>
    </dependency>
    <dependency>
      <artifactId>transmittable-thread-local</artifactId>
      <groupId>com.alibaba</groupId>
    </dependency>
    <dependency>
      <artifactId>walle-eve-domain</artifactId>
      <groupId>com.sankuai.walleeve</groupId>
    </dependency>
    <dependency>
      <artifactId>leaf-idl</artifactId>
      <groupId>com.sankuai.inf.leaf</groupId>
    </dependency>
    <dependency>
      <groupId>com.meituan.xframe</groupId>
      <artifactId>rhino-xframe-boot-starter</artifactId>
    </dependency>
    <!--  podam 不使用时可去除, 本框架中仅用作模拟用户  -->
  </dependencies>

  <modelVersion>4.0.0</modelVersion>
  <name>wallemonitor-risk-center-domain</name>
  <packaging>jar</packaging>
  <parent>
    <artifactId>wallemonitor-risk-center</artifactId>
    <groupId>com.sankuai.wallemonitor</groupId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <version>1.0.0-SNAPSHOT</version>
</project>
