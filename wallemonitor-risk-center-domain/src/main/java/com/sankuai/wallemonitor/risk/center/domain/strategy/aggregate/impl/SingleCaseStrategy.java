package com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.impl;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateStrategy;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertPolicyEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 单事件策略，适用于不需要聚合的单个事件告警
 */
@Component
@Slf4j
public class SingleCaseStrategy implements AggregateStrategy {

    @Override
    public AlertPolicyEnum getAlertPolicy() {
        return AlertPolicyEnum.SINGLE_CASE;
    }

    @Override
    public List<AggregateResultDTO> process(RiskCaseDO triggerRiskCase, AggregateStrategyConfigDTO config,
            AggregateAlertContext context) {
        if (triggerRiskCase == null) {
            return Collections.emptyList();
        }

        AggregateResultDTO result = buildSingleCaseResult(triggerRiskCase, config);
        return Collections.singletonList(result);
    }

    /**
     * 构建单个事件的聚合结果
     */
    private AggregateResultDTO buildSingleCaseResult(RiskCaseDO riskCase, AggregateStrategyConfigDTO config) {
        // 构建聚合维度（单事件策略主要基于事件本身的属性）
        AggregateResultDTO.AggregateDimensions dimensions = AggregateResultDTO.AggregateDimensions.builder()
                .poiName(riskCase.getPoiName())
                .type(riskCase.getType().getCode())
                .typeDesc(riskCase.getType().getDesc())
                .placeCode(riskCase.getPlaceCode())
                .build();

        // 构建统计信息
        AggregateResultDTO.AggregateStatistics statistics = AggregateResultDTO.AggregateStatistics.builder()
                .typeDistribution(Collections.singletonMap(riskCase.getType().getDesc(), 1L))
                .earliestTime(riskCase.getOccurTime())
                .latestTime(riskCase.getOccurTime())
                .timeSpanSeconds(0L)
                .build();

        return AggregateResultDTO.builder()
                .aggregateKey(buildAggregateKey(riskCase))
                .aggregateDimensions(dimensions)
                .caseCount(1)
                .riskCaseList(Collections.singletonList(riskCase))
                .timeWindowStart(riskCase.getOccurTime())
                .timeWindowEnd(riskCase.getOccurTime())
                .statistics(statistics)
                .strategyId(config.getAlertPolicy())
                .build();
    }

    /**
     * 构建聚合键（单事件策略使用事件ID作为唯一标识）
     */
    private String buildAggregateKey(RiskCaseDO riskCase) {
        return "single_case_" + riskCase.getCaseId();
    }
} 