package com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RandomForestPredictionResultDTO {

    /**
     * 是否风险
     */
    private Boolean isRisky;
    /**
     * 风险概率
     */
    private Double riskProbability;

    /**
     * 策略判定是否有风险
     */
    private Boolean strategyRisky;



    public boolean isRisky() {
        return isRisky;
    }
}