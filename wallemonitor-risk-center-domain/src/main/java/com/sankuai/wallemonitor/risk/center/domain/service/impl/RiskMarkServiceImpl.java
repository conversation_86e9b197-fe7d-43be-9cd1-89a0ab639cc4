package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import static com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum.CANT_FOUND_ANY;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleeve.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.domain.config.MultiMarkMessageConfigDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskMarkService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionManager;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.MarkMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckingExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.MultiVersionMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.MultiVersionMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskMarkRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskMarkServiceImpl implements RiskMarkService {

    @Resource
    private ISCheckActionManager iSCheckActionManager;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskMarkRepository riskMarkRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;
    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private MultiMarkMessageConfigDTO multiMarkMessageConfigDTO;

    @Resource
    private MultiVersionMarkInfoRepository multiVersionMarkInfoRepo;

    @Resource
    private CaseMarkInfoRepository markInfoRepository;

    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @MessageProducer(topic = MessageTopicEnum.WALLEMONITOR_RISK_AUTO_MARK_MESSAGE)
    private CommonMessageProducer<MarkMessageDTO> messageProducer;

    @Override
    public void updateOneRiskCheckingQueueItem(RiskCheckingQueueItemDO queueItemDO, ISCheckActionResult resultDTO) {
        // 取action
        String finalCheckActionName = resultDTO.getActionName();
        ISCheckCategoryEnum checkCategoryEnum = resultDTO.getCategoryEnum();
        RiskCheckResultDO checkResult = RiskCheckResultDO.builder().category(checkCategoryEnum)
                // 来源设置为actionName
                .checkSource(finalCheckActionName)
                // 检查开始时间
                .checkTime(resultDTO.getStartCheckTime())
                // 检查持续时间
                .duration(resultDTO.getDuration())
                // 获取执行的结果
                .build();
        // 更新
        queueItemDO.updateCheckResult(checkResult);
        if (resultDTO.withRisk() || resultDTO.cantVerify()) {
            // 确认风险
            queueItemDO.confirm(RiskQueueStatusEnum.CONFIRMED_RISK, finalCheckActionName, checkCategoryEnum);
        } else if (resultDTO.getNeedReCheck()) {
            // 需要重试，do nothing
        } else {
            // 取消
            queueItemDO.cancel(new Date(), finalCheckActionName, checkCategoryEnum);
        }
        // 轮次增加
        queueItemDO.increaseRound();

    }

    @Override
    public ISCheckActionResult calcRiskMarkResult(RiskCheckingQueueItemDO riskCheckingQueueItemDO, String version) {
        // 执行自动标注链
        return iSCheckActionManager.doMark(riskCheckingQueueItemDO, version);
    }

    /**
     * 批量计算检查结果
     *
     * @param version
     * @return
     */
    @Override
    public void triggerAutoMark(List<RiskCheckingQueueItemDO> queueItemList, String version) {
        // 获取自动标注的配置
        RiskAutoCheckConfigDTO config = lionConfigRepository.getRiskAutoMarkConfigByVersion(version);
        if (Objects.isNull(config)) {
            return;
        }
        Long checkIntervalSeconds = config.getRoundIntervalSeconds().longValue();
        // 发送标注消息，上游保障不重复
        queueItemList.stream().peek(queueItem -> {
            String vin = queueItem.getVin();
            VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = vehicleRuntimeInfoContextRepository.getFromCache(vin);
            PositionDO gcj02Location = vehicleRuntimeInfoContextDO.getGCJ02Location();
            Integer maxCheckRound = config.getMaxCheckRound();
            // 使用delay recall area 并且 坐标在safety area内，使用延迟maxRound
            if(config.getUseDelayRecallArea()
                    && safetyAreaRepository.isInDelayRecallPolygon(gcj02Location, queueItem.getOccurTime())
            ) {
                maxCheckRound = config.getMaxDelayRound();
            }
            queueItem.setMaxRound(maxCheckRound);
        }).forEach(queueItem -> {
            try {
                // 如果不是首轮,取最新的mark 进行查看
                RiskCaseDO latestRisk = riskCaseRepository.getByCaseId(queueItem.getEventId());
                if (latestRisk != null && RiskCaseStatusEnum.isTerminal(latestRisk.getStatus())) {
                    // 如果是终态，直接标记
                    this.markCaseByCheckItem(queueItem, version);
                    // 并且删除，不需要再触发
                    riskMarkRepository.deleteMarkItem(Lists.newArrayList(queueItem), version);
                    return;
                }
                if (queueItem.getRound() != 0) {
                    // 如果不是首轮，直接发延时消息
                    sendMarkMessage(queueItem, checkIntervalSeconds, false, version);
                    return;
                }
                // 如果是首轮，直接判断
                ISCheckActionResult actionResult = calcRiskMarkResult(queueItem, version);
                // 更新结果
                updateOneRiskCheckingQueueItem(queueItem, actionResult);
                // 保存
                if (!RiskQueueStatusEnum.isTerminatedStatus(queueItem.getStatus())) {
                    // 如果首次检出非风险，延时校验
                    riskMarkRepository.saveMarkItem(Lists.newArrayList(queueItem), version);
                    sendMarkMessage(queueItem, checkIntervalSeconds, false, version);
                    return;
                }
                if (Boolean.TRUE.equals(actionResult.isDynamicReCheck())) {
                    // 如果检出风险 ，但是在动态轮次里面，重置后延迟检
                    resetQueueItemForDynamicCheck(queueItem, null);
                    // 保存
                    riskMarkRepository.saveMarkItem(Lists.newArrayList(queueItem), version);
                    // 发动态轮次的消息
                    sendMarkMessage(queueItem,
                            config.getDynamicCheckDelayTime(actionResult.getActionName(),
                                    actionResult.getCategoryEnum(),
                                    vehicleRuntimeInfoContextRepository.getFullByVin(queueItem.getVin())),
                            true, version);
                } else {
                    // 如果检查风险，但是非动态轮次范围，直接标注结果
                    markCaseByCheckItem(queueItem, version);
                    // 删除
                    riskMarkRepository.deleteMarkItem(Lists.newArrayList(queueItem), version);
                }
            } catch (Exception e) {
                log.error(StringMessageFormatter.replaceMsg("triggerAutoMark failed. queueItem: {}",
                        JacksonUtils.to(queueItem)), e);
            }
        });
    }

    @Override
    public void resetQueueItemForDynamicCheck(RiskCheckingQueueItemDO queueItem, RiskCheckResultDO lastCheckResultDO) {
        queueItem.setStatus(RiskQueueStatusEnum.VALIDATING);
        queueItem.setCheckResult(lastCheckResultDO);
        // 清除前一轮的结果
        List<RiskCheckResultDO> lastCheckResultList = Optional.ofNullable(queueItem.getExtInfo())
                .map(RiskCheckingExtInfoDO::getLastCheckResult).orElse(null);
        if (CollectionUtils.isNotEmpty(lastCheckResultList)) {
            lastCheckResultList.remove(lastCheckResultList.size() - 1);
        }
        queueItem.setRound(Math.max(queueItem.getRound() - 1, 0));
    }

    @Override
    public void sendMarkMessage(RiskCheckingQueueItemDO queueItem, Long checkIntervalSeconds, boolean isDynamic,
            String version) {
        try {
            MarkMessageDTO messageDTO = MarkMessageDTO.builder().itemCaseId(queueItem.getTmpCaseId())
                    .dynamicRecheck(isDynamic).version(version).build();
            if (StringUtils.isBlank(version)) {
                // 如果是主标注链路
                messageProducer.sendDelayCustomMessage(messageDTO, checkIntervalSeconds);
            } else {
                // 多套
                CommonMessageProducer<MarkMessageDTO> multiProducer = multiMarkMessageConfigDTO
                        .getMarkMessageProducerByVersion(version);
                if (Objects.isNull(multiProducer)) {
                    // 如果是
                    log.error(version, new SystemException("多版本找不到消息触发者"));
                    return;
                }
                multiProducer.sendDelayCustomMessage(messageDTO, checkIntervalSeconds);
            }
        } catch (Exception e) {
            log.error(StringMessageFormatter.replaceMsg("triggerAutoMark failed. queueItem: {}",
                    JacksonUtils.to(queueItem)), e);
        }
    }

    @Override
    public void markCaseByCheckItem(RiskCheckingQueueItemDO itemDO, String version) {
        if (Objects.isNull(itemDO)) {
            return;
        }
        String otherCaseId = itemDO.getEventId();
        // 获取checkResult
        Map<String, Object> checkResult = ReflectUtils.getNonNullFieldAndValue(itemDO.getCheckResult());
        if (Objects.isNull(itemDO.getCheckResult()) || Objects.isNull(itemDO.getCheckResult().getCategory())) {
            // 什么结果都没有
            return;
        }
        // 取值
        String operator = itemDO.getCheckResult().getCheckSource();
        if (CommonConstant.NONE_ACTION.equals(operator)) {
            //
            return;
        }
        ISCheckCategoryEnum checkResultEnum;
        if (!RiskQueueStatusEnum.CONFIRMED_TIMEOUT.equals(itemDO.getStatus())) {
            // 不为检测超时的状态时，才能使用check出来的数据
            checkResultEnum = itemDO.getCheckResult().getCategory();
        } else {
            // 超时使用
            checkResultEnum = CANT_FOUND_ANY;
        }
        if (StringUtils.isNotBlank(version)) {
            // 子标注链路
            // 获取主标注的caseMarkInfo
            MultiVersionMarkInfoDO multiVersionMarkInfoDO = multiVersionMarkInfoRepo.getByCaseIdAndVersion(otherCaseId,
                    version);
            multiVersionMarkInfoDO = Optional.ofNullable(multiVersionMarkInfoDO)
                    .orElseGet(() -> RiskCaseFactory.initMultiVersionMarkInfo(otherCaseId, version));
            // 更新
            multiVersionMarkInfoDO.autoMark(checkResultEnum, itemDO.getCheckResult(), operator, itemDO.getRound());
            // 保存
            multiVersionMarkInfoRepo.save(multiVersionMarkInfoDO);
        } else {
            // 主标注链路
            // 获取停滞标注的caseMarkInfo
            CaseMarkInfoDO caseMarkInfoDO = markInfoRepository.getByCaseId(otherCaseId);
            // 构造新的标注结果
            CaseMarkInfoDO markInfo = Optional.ofNullable(caseMarkInfoDO)
                    .orElse(RiskCaseFactory.initMarkInfo(otherCaseId));  // 新建或者获取
            if (StringUtils.isNotBlank(markInfo.getFirstSubCategory())) {
                // 标注已完成
                return;
            }
            // 保存mark
            markInfo.autoMark(checkResultEnum, checkResult, operator, itemDO.getRound());
            // 更新markInfo
            markInfoRepository.save(markInfo);
        }
    }
}
