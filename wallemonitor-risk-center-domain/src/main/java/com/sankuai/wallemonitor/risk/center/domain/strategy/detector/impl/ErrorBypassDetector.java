package com.sankuai.wallemonitor.risk.center.domain.strategy.detector.impl;

import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetector;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskErrorBypassRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 错误绕行检测
 */
@Slf4j
@Service
public class ErrorBypassDetector extends RiskDetector<RiskErrorBypassRecordDO> {

    /**
     * 获取检测的风险类型
     *
     * @return
     */
    @Override
    public RiskCaseTypeEnum getDetectRiskType() {
        return RiskCaseTypeEnum.ERROR_BYPASS;
    }
}