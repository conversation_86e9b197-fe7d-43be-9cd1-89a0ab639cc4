package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseMessageNotifyParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseMessageNoticeProcess.MessageCreateOrUpdatedDTO;
import com.sankuai.wallemonitor.risk.center.domain.result.RiskCaseMessageNotifyResultDTO;
import java.util.List;
import java.util.Map;

/**
 * 风险事件操作服务
 */
public interface RiskCaseMessageService {


    /**
     * 创建或更新风险事件的消息推送
     *
     * @param paramDTO
     * @return
     */
    RiskCaseMessageNotifyResultDTO updateMessageVersionAndSendMessage(RiskCaseMessageNotifyParamDTO paramDTO);


    /**
     * 计算风险事件需要创建或更新的消息列表
     *
     * @param caseIdList
     * @return
     */
    Map<String, MessageCreateOrUpdatedDTO> calcCaseNeedGreatOrUpdateMessage(List<String> caseIdList);

    /**
     * 获取指定的group的渲染数据
     */
    Map<String, Map<String, String>> getCaseRenderData(List<String> caseIdList);

}
