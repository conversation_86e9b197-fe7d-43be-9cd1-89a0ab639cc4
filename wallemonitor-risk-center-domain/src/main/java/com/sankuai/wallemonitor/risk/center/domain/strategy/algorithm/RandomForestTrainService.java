package com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import weka.classifiers.Evaluation;
import weka.classifiers.trees.RandomForest;
import weka.core.Attribute;
import weka.core.DenseInstance;
import weka.core.Instance;
import weka.core.Instances;
import weka.core.SerializationHelper;

@Slf4j
public class RandomForestTrainService {

    /**
     * 随机森林模型
     */
    private RandomForest model;
    private Instances dataStructure;
    public RandomForestTrainService(RiskClassifierConfigDTO configDTO) {
        // 初始化特征和标签
        ArrayList<Attribute> attributes = new ArrayList<>();

        //todo: 关于离散值需要设置可选范围， 连续值则不需要

        // 经度 （连续值）
        attributes.add(new Attribute("longitude"));
        //  维度 (连续值)
        attributes.add(new Attribute("latitude"));

        // 路口距离特征（连续值）
        attributes.add(new Attribute("distanceToNextJunction"));

        // 红灯信号特征（离散值）
        attributes.add(new Attribute("trafficLightType", configDTO.getTrafficLightTypeValues()));

        //  障碍物距离 (连续值)
        attributes.add(new Attribute("obstacleDistance"));

        // 障碍物夹角 （连续值）
        attributes.add(new Attribute("obstacleAngle"));

        // 车道ID （离散值）
        attributes.add(new Attribute("laneType", new ArrayList<>(Arrays.asList("-1", "1", "2"))));

        // 车道ID （离散值）
        attributes.add(new Attribute("lanePosition", new ArrayList<>(Arrays.asList("-1", "1", "2", "3"))));

        // 风险标签（离散值）
        ArrayList<String> classValues = new ArrayList<>(Arrays.asList("0", "1"));
        attributes.add(new Attribute("risk", classValues));

        // 创建数据结构
        dataStructure = new Instances("RiskFeatureDataDTO", attributes, 0);
        // 区分特征（features）和标签（label）,即需要告诉模型要预测哪个变量
        dataStructure.setClassIndex(8);

        // 初始化随机森林模型
        model = new RandomForest();
    }

    /**
     * 训练模型
     *
     * @param trainingData
     * @throws Exception
     */
    public void train(List<RiskFeatureDataDTO> trainingData) throws Exception {
        if (CollectionUtils.isEmpty(trainingData)) {
            log.error("RandomForestTrainService, train", new IllegalArgumentException("训练数据为空"));
            return;
        }
        // 准备训练数据
        Instances trainInstances = new Instances(dataStructure);

        for (RiskFeatureDataDTO data : trainingData) {
            try {
                Instance instance = createInstance(data);
                trainInstances.add(instance);
            } catch (Exception e) {
                log.error("RandomForestTrainService, train", e);
            }
        }

        // 设置随机森林参数
        model.setMaxDepth(100);
        model.setNumFeatures(3);  // 使用的特征数量
        model.setSeed(42);        // 随机种子

        // 启用模型重要性的计算
        model.setComputeAttributeImportance(true);
        // 训练模型
        model.buildClassifier(trainInstances);

        String modelInfo = model.toString();
        log.info("RandomForestTrainService, train: {}", modelInfo);

    }


    public void trainV2(List<RiskFeatureDataDTO> trainingData) throws Exception {
        if (CollectionUtils.isEmpty(trainingData)) {
            log.error("RandomForestTrainService, train", new IllegalArgumentException("训练数据为空"));
            return;
        }
        // 准备训练数据
        Instances trainInstances = new Instances(dataStructure);

        // 将数据分为风险和无风险两类
        List<Instance> riskyInstances = new ArrayList<>();
        List<Instance> nonRiskyInstances = new ArrayList<>();

        for (RiskFeatureDataDTO data : trainingData) {
            try {
                Instance instance = createInstance(data);
                if (data.getRisk() != null && data.getRisk() == 1) {
                    riskyInstances.add(instance);
                } else {
                    nonRiskyInstances.add(instance);
                }
            } catch (Exception e) {
                log.error("RandomForestTrainService, train", e);
            }
        }

        // 对无风险数据进行随机降采样，使其数量与风险数据相当
        int targetSize = riskyInstances.size();
        Collections.shuffle(nonRiskyInstances, new Random(42));
        List<Instance> sampledNonRiskyInstances = nonRiskyInstances.subList(0,
                Math.min(targetSize, nonRiskyInstances.size()));

        // 合并数据
        trainInstances.addAll(riskyInstances);
        trainInstances.addAll(sampledNonRiskyInstances);

        // 设置随机森林参数
        model.setMaxDepth(100);
        model.setNumFeatures(3);
        model.setSeed(42);

        // 启用模型重要性的计算
        model.setComputeAttributeImportance(true);
        // 训练模型
        model.buildClassifier(trainInstances);

        log.info("训练数据统计 - 风险样本: {}, 无风险样本: {}", riskyInstances.size(), sampledNonRiskyInstances.size());
        String modelInfo = model.toString();
        log.info("RandomForestTrainService, train: {}", modelInfo);
    }


    /**
     * 预测风险
     *
     * @param data
     * @return
     * @throws Exception
     */
    public RandomForestPredictionResultDTO predict(RiskFeatureDataDTO data) throws Exception {

        try {
            Instance instance = createInstance(data);

            // 获取预测概率分布
            double[] probabilityDistribution = model.distributionForInstance(instance);

            return new RandomForestPredictionResultDTO(
                    probabilityDistribution[1] >= 0.4,  // isRisky
                    probabilityDistribution[1],         // riskProbability
                    null
            );
        } catch (Exception e) {
            log.error("RandomForestTrainService, predict", e);
        }
        return null;
    }

    /**
     * 评估模型性能
     *
     * @param testData 测试数据集
     * @return ModelEvaluationResult 评估结果
     * @throws Exception
     */
    public ModelEvaluationResultDTO evaluate(List<RiskFeatureDataDTO> testData) throws Exception {
        if (CollectionUtils.isEmpty(testData)) {
            log.error("RandomForestTrainService, evaluate: test data is empty");
            return null;
        }

        // 准备测试数据
        Instances testInstances = new Instances(dataStructure);
        for (RiskFeatureDataDTO data : testData) {
            Instance instance = createInstance(data);
            testInstances.add(instance);
        }

        // 创建评估器
        Evaluation eval = new Evaluation(dataStructure);

        // 评估模型
        eval.evaluateModel(model, testInstances);

        // 构建评估结果
        return ModelEvaluationResultDTO.builder()
                .accuracy(eval.pctCorrect() / 100.0)
                .precision(eval.precision(1))
                .recall(eval.recall(1))
                .f1Score(eval.fMeasure(1))
                .confusionMatrix(eval.confusionMatrix())
                .build();
    }

    /**
     * 保存模型到文件
     *
     * @param modelPath         模型保存路径
     * @param dataStructurePath 数据结构保存路径
     * @throws Exception IO异常或序列化异常
     */
    public void saveModel(String modelPath, String dataStructurePath) throws Exception {
        try {
            // 保存随机森林模型
            SerializationHelper.write(modelPath, model);
            // 保存数据结构
            SerializationHelper.write(dataStructurePath, dataStructure);
            log.info("模型保存成功，路径: {}, {}", modelPath, dataStructurePath);
        } catch (Exception e) {
            log.error("保存模型失败", e);
            throw e;
        }
    }

    /**
     * 从文件或URL加载模型
     *
     * @param modelPath         模型文件路径或URL
     * @param dataStructurePath 数据结构文件路径或URL
     * @throws Exception IO异常或反序列化异常
     */
    public void loadModel(String modelPath, String dataStructurePath) throws Exception {
        try {
            // 判断是否为URL
            if (isValidUrl(modelPath) && isValidUrl(dataStructurePath)) {
                // 从URL加载
                model = (RandomForest) SerializationHelper.read(new URL(modelPath).openStream());
                dataStructure = (Instances) SerializationHelper.read(new URL(dataStructurePath).openStream());
            } else {
                // 从本地文件加载
                model = (RandomForest) SerializationHelper.read(modelPath);
                dataStructure = (Instances) SerializationHelper.read(dataStructurePath);
            }
            log.info("模型加载成功，路径: {}, {}", modelPath, dataStructurePath);
        } catch (Exception e) {
            log.error("加载模型失败", e);
            throw e;
        }
    }

    /**
     * 验证字符串是否为合法URL
     */
    private boolean isValidUrl(String urlString) {
        try {
            new URL(urlString);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }


    /**
     * 创建实例
     *
     * @param data
     * @return
     */
    private Instance createInstance(RiskFeatureDataDTO data) {
        Instance instance = new DenseInstance(9);
        instance.setDataset(dataStructure);

        instance.setValue(0, data.getLongitude());
        instance.setValue(1, data.getLatitude());
        instance.setValue(2, data.getDistanceToNextJunction());
        instance.setValue(3, data.getTrafficLightType());
        instance.setValue(4, data.getObstacleDistance());
        instance.setValue(5, data.getObstacleAngle());
        instance.setValue(6, String.valueOf(data.getLaneType()));
        instance.setValue(7, String.valueOf(data.getLanePosition()));
        if (data.getRisk() != null) {
            instance.setValue(8, String.valueOf(data.getRisk()));
        }
        return instance;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class RiskTrainDataVO {

        /**
         * 经度 WGS84坐标系
         */
        private Double longitude;

        /**
         * 维度 WGS84坐标系
         */
        private Double latitude;

        /**
         * 高精地图版本
         */
        private String hdMapVersion;

        /**
         * 距离下个路口的距离
         */
        private Double distanceToNextJunction;

        /**
         * 红灯类型
         */
        private String trafficLightType;

        /**
         * 障碍物距离
         */
        private Double obstacleDistance;

        /**
         * 障碍物夹角
         */
        private Double obstacleAngle;

        /**
         * 风险
         */
        private Integer risk;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class RiskClassifierConfigDTO {

        /**
         * 位置信息
         */
        ArrayList<String> positionValues;

        /**
         * 车道信息
         */
        ArrayList<String> laneIdValues;

        /**
         * 信号灯信息
         */
        ArrayList<String> trafficLightTypeValues;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class ModelEvaluationResultDTO {

        /**
         * 准确率：所有预测正确的样本比例 accuracy = (TP + TN) / (TP + TN + FP + FN)
         */
        private double accuracy;

        /**
         * 精确率：预测为正例中真实正例的比例 precision = TP / (TP + FP)
         */
        private double precision;

        /**
         * 召回率：真实正例被正确预测的比例 recall = TP / (TP + FN)
         */
        private double recall;

        /**
         * F1分数：精确率和召回率的调和平均 f1Score = 2 * (precision * recall) / (precision + recall)
         */
        private double f1Score;

        /**
         * 混淆矩阵结构： [[TN, FP], [FN, TP]]
         * <p>
         * TN: True Negative - 正确预测为负例 FP: False Positive - 错误预测为正例 FN: False Negative - 错误预测为负例 TP: True Positive  -
         * 正确预测为正例
         */

        private double[][] confusionMatrix;

        @Override
        public String toString() {
            return String.format(
                    "模型评估结果:\n" +
                            "准确率: %.2f%%\n" +
                            "精确率: %.2f\n" +
                            "召回率: %.2f\n" +
                            "F1分数: %.2f\n" +
                            "混淆矩阵:\n" +
                            "[[%.0f, %.0f]\n" +
                            " [%.0f, %.0f]]",
                    accuracy * 100,
                    precision,
                    recall,
                    f1Score,
                    confusionMatrix[0][0], confusionMatrix[0][1],
                    confusionMatrix[1][0], confusionMatrix[1][1]
            );
        }
    }
}
