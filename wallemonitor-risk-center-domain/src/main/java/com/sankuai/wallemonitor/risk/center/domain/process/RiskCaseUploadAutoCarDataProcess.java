package com.sankuai.wallemonitor.risk.center.domain.process;


import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.RISK_CASE_FAST_UPLOAD_PROCESS_MESSAGE_ENTRY;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DataPlatformAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.DataPlatformFastUploadRequest;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.FastUploadAutoCarDataConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskCaseUploadAutoCarDataProcess implements DomainEventProcess {

    @Resource
    private DataPlatformAdapter dataPlatformAdapter;

    @Resource
    private RiskCaseVehicleRelationRepository repository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(RISK_CASE_FAST_UPLOAD_PROCESS_MESSAGE_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != RiskCaseDO.class) {
            return true;
        }


        FastUploadAutoCarDataConfig config = lionConfigRepository.getFastUploadAutocarDataConfig();
        if (!config.getEnable()) {  // 开关关闭，不执行
            return true;
        }

        DomainEventChangeDTO<RiskCaseDO> typedDomainEvent = (DomainEventChangeDTO<RiskCaseDO>) eventDTO;
        List<RiskCaseDO> targetRiskCaseDO = typedDomainEvent.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                .stream()
                .filter(riskCaseDO -> RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus()))  // 结束的
                .filter(riskCaseDO -> config.isInTypeList(riskCaseDO.getType())) // 满足回捞的配置要求的
                .filter(riskCaseDO -> RiskCaseSourceEnum.recallBySelf(riskCaseDO.getSource())) // 状态监控
                .filter(riskCaseDO -> riskCaseDO.getRiskDurationTime() >= config.getMinDurationSeconds()) // 持续时长大于配置值
                .collect(Collectors.toList());
        log.info("RiskCaseUploadAutoCarDataProcess targetRiskCaseDO = {}", targetRiskCaseDO);
        if (CollectionUtils.isEmpty(targetRiskCaseDO)) {
            return true;
        }

        return process(targetRiskCaseDO, config);
    }

    /**
     * 处理快速回传数据
     *
     * @param riskCaseDOList
     * @return
     */
    private boolean process(List<RiskCaseDO> riskCaseDOList, FastUploadAutoCarDataConfig config) {
        RiderCaseVehicleRelationDOParamDTO paramDTO = RiderCaseVehicleRelationDOParamDTO.builder()
                .caseIdList(riskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList()))
                .build();
        List<RiskCaseVehicleRelationDO> vehicleRelationDOList = repository.queryByParam(paramDTO);
        Map<String, RiskCaseVehicleRelationDO> vehicleRelationDOMap = vehicleRelationDOList.stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, item -> item,
                        (oldValue, newValue) -> oldValue));
        riskCaseDOList.forEach(item -> {
            try {
                RiskCaseVehicleRelationDO relationDO = vehicleRelationDOMap.get(item.getCaseId());
                if (Objects.isNull(relationDO)) {
                    log.error("caseId: {} cant find any RiskCaseVehicleRelationDO", item.getCaseId());
                    return;
                }

                // 只有VHR>1才需要触发数据回传
                if (!VHRModeEnum.VHR_GREAT_THAN_ONE.getCode().equals(relationDO.getVhrMode())) {
                    return;
                }

                Date actualStrandingTime = item.getOccurTime();
                DataPlatformFastUploadRequest request = DataPlatformFastUploadRequest.builder()
                        .vin(relationDO.getVin())
                        .begin(DatetimeUtil.getNSecondsBeforeDateTime(actualStrandingTime, config.getBeforeSeconds()))
                        .end(DatetimeUtil.getNSecondsAfterDateTime(actualStrandingTime, config.getAfterSeconds()))
                        .modules(config.getModules())
                        .source(RiskCaseTypeEnum.VEHICLE_STAND_STILL.name())
                        .build();
                log.info("start call data platform fast upload, case: {}, request: {}", item, request);
                String taskId = dataPlatformAdapter.fastUploadAutoCarData(request);
                log.info("task id: {}", taskId);
                item.setFastUploadId(taskId);
                riskCaseRepository.save(item);
            } catch (Exception e) {
                log.error("process error", e);
            }
        });
        return true;
    }
}
