package com.sankuai.wallemonitor.risk.center.domain.strategy.detector.impl;

import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetector;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskSpecialAreaStrandingRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 特殊区域滞留风险检测 1.路口施工区域
 */
@Slf4j
@Service
public class SpecialAreaStrandingDetector extends RiskDetector<RiskSpecialAreaStrandingRecordDO> {

    /**
     * 获取检测的风险类型
     *
     * @return
     */
    @Override
    public RiskCaseTypeEnum getDetectRiskType() {
        return RiskCaseTypeEnum.SPECIAL_AREA_STRANDING;
    }

}