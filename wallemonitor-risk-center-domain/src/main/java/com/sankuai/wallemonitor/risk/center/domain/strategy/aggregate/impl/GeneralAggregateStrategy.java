package com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateStrategy;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateAlertGrayConfigDTO.AttributesGrayConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.TriggerConditionDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AggregateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertPolicyEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseParkingPlotRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseParkingPlotRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseParkingPlotRelationDOQueryParamDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 通用聚合策略，支持POI类型、泊位等多维度聚合
 */
@Component
@Slf4j
public class GeneralAggregateStrategy implements AggregateStrategy {

    @Resource
    private RiskCaseParkingPlotRelationRepository riskCaseParkingPlotRelationRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private VehicleInfoRepository vehicleInfoRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Override
    public AlertPolicyEnum getAlertPolicy() {
        return AlertPolicyEnum.GENERAL_AGGREGATION;
    }

    @Override
    public List<AggregateResultDTO> process(RiskCaseDO triggerRiskCase, AggregateStrategyConfigDTO config,
            AggregateAlertContext context) {
        if (triggerRiskCase == null || config == null) {
            return new ArrayList<>();
        }

        // 查询时间窗口内的事件
        List<RiskCaseDO> riskCases = queryCasesInTimeWindow(config);
        if (CollectionUtils.isEmpty(riskCases)) {
            return new ArrayList<>();
        }
        // 补充事件窗口内所有事件的上下文信息
        updateContext(context, riskCases);

        // 执行聚合分析
        List<AggregateResultDTO> allResults = aggregate(riskCases, config, context);
        log.info("时间窗口内的所有事件聚合结果：{}", JacksonUtils.to(allResults));

        // 检查触发条件，只返回满足告警条件的结果
        return allResults.stream()
                .filter(result -> shouldTriggerAlert(result, config.getTriggerConditions()))
                .collect(Collectors.toList());
    }

    private void updateContext(AggregateAlertContext context, List<RiskCaseDO> riskCaseList) {
        List<String> caseIdList = riskCaseList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        Map<String, String> caseId2VinMap = riskCaseVehicleRelationRepository.queryByParam(
                        RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIdList).build()).stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, RiskCaseVehicleRelationDO::getVin,
                        (o1, o2) -> o1));
        Map<String, VehicleInfoDO> vehicleInfoMap = vehicleInfoRepository.queryByVinList(
                        Lists.newArrayList(caseId2VinMap.values())).stream()
                .collect(Collectors.toMap(VehicleInfoDO::getVin, Function.identity(), (o1, o2) -> o1));
        Map<String, VehicleInfoDO> caseId2VehicleInfoMap = caseId2VinMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> vehicleInfoMap.get(entry.getValue())));

        context.updateCaseId2VehicleInfoMap(caseId2VehicleInfoMap);
    }

    /**
     * 执行聚合分析
     */
    private List<AggregateResultDTO> aggregate(List<RiskCaseDO> riskCases, AggregateStrategyConfigDTO config,
            AggregateAlertContext context) {
        // 补充聚合上下文
        List<String> aggregateBy = config.getAggregateBy();
        if (aggregateBy != null && aggregateBy.contains(AggregateFieldEnum.PARKING_PLOT_ID.getCode())) {
            Map<String, String> caseIdToParkingPlotId = loadParkingPlotMapping(riskCases);
            context.updateCaseIdToParkingPlotIdMap(caseIdToParkingPlotId);
        }
        // 按聚合维度分组
        Map<String, List<RiskCaseDO>> grouped = riskCases.stream().collect(
                Collectors.groupingBy(riskCase -> buildAggregateKey(riskCase, aggregateBy, context)));

        // 构建聚合结果
        Date now = new Date();
        long windowSeconds = config.getTimeWindow().getWindowSeconds();
        Date windowStart = DatetimeUtil.getNSecondsBeforeDateTime(now, (int) windowSeconds);

        return grouped.entrySet().stream()
                .map(entry -> buildAggregateResult(entry.getKey(), entry.getValue(), config, windowStart, now,
                        aggregateBy, context)).collect(Collectors.toList());
    }

    /**
     * 查询时间窗口内的事件
     */
    private List<RiskCaseDO> queryCasesInTimeWindow(AggregateStrategyConfigDTO strategyConfig) {
        Date now = new Date();
        long windowSeconds = strategyConfig.getTimeWindow().getWindowSeconds();
        Date windowStart = DatetimeUtil.getNSecondsBeforeDateTime(now, (int) windowSeconds);

        RiskCaseDOQueryParamDTO queryParam = RiskCaseDOQueryParamDTO.builder()
                .occurTimeRange(TimePeriod.builder()
                        .beginDate(windowStart)
                        .endDate(now)
                        .build())
                .build();

        // 基于配置的事件类型过滤
        if (strategyConfig.getCaseType() != null) {
            queryParam.setCaseTypeList(Collections.singletonList(strategyConfig.getCaseType()));
        }

        // 添加灰度过滤条件
        if (strategyConfig.getGrayRelease() != null) {
            // 位置和车辆维度灰度过滤
            if (strategyConfig.getGrayRelease().getAttributesGrayConfig() != null) {
                AttributesGrayConfigDTO locationConfig = strategyConfig.getGrayRelease()
                        .getAttributesGrayConfig();

                // POI灰度过滤
                if (CollectionUtils.isNotEmpty(locationConfig.getPoiGrayList())
                        && !locationConfig.getPoiGrayList().contains("ALL")) {
                    queryParam.setPoiNameList(locationConfig.getPoiGrayList());
                }

                // 场地灰度过滤
                if (CollectionUtils.isNotEmpty(locationConfig.getPlaceCodeGrayList())
                        && !locationConfig.getPlaceCodeGrayList().contains("ALL")) {
                    queryParam.setPlaceCodeList(locationConfig.getPlaceCodeGrayList());
                }

                // VIN灰度过滤
                if (CollectionUtils.isNotEmpty(locationConfig.getVinGrayList())
                        && !locationConfig.getVinGrayList().contains("ALL")) {
                    queryParam.setVinList(locationConfig.getVinGrayList());
                }
            }

            // 用车目的灰度过滤
            if (CollectionUtils.isNotEmpty(strategyConfig.getGrayRelease().getPurposeGrayList())
                    && !strategyConfig.getGrayRelease().getPurposeGrayList().contains("ALL")) {
                queryParam.setPurposeList(strategyConfig.getGrayRelease().getPurposeGrayList());
            }
        }

        return riskCaseRepository.queryByParam(queryParam);
    }

    /**
     * 加载案例与泊位的映射关系
     */
    private Map<String, String> loadParkingPlotMapping(List<RiskCaseDO> riskCases) {
        List<String> caseIdList = riskCases.stream()
                .map(RiskCaseDO::getCaseId)
                .collect(Collectors.toList());

        List<RiskCaseParkingPlotRelationDO> relationDOList = riskCaseParkingPlotRelationRepository.queryByParam(
                RiskCaseParkingPlotRelationDOQueryParamDTO.builder().caseIdList(caseIdList).build());

        return relationDOList.stream()
                .filter(r -> StringUtils.isNotBlank(r.getCaseId()))
                .collect(Collectors.toMap(
                        RiskCaseParkingPlotRelationDO::getCaseId,
                        r -> StringUtils.defaultString(r.getParkingPlotId(), CommonConstant.UNKNOWN),
                        (o1, o2) -> o1));
    }

    /**
     * 判断是否触发告警
     */
    private boolean shouldTriggerAlert(AggregateResultDTO result, List<TriggerConditionDTO> conditions) {
        if (result == null || CollectionUtils.isEmpty(conditions)) {
            return false;
        }
        for (TriggerConditionDTO condition : conditions) {
            if (!condition.isValid()) {
                continue;
            }
            Integer actualValue = result.getCaseCount();
            if (condition.isSatisfied(actualValue)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 构建聚合键
     */
    private String buildAggregateKey(RiskCaseDO riskCase, List<String> aggregateBy, AggregateAlertContext context) {
        if (CollectionUtils.isEmpty(aggregateBy)) {
            return "default";
        }
        List<String> keyParts = new ArrayList<>();
        for (String dimension : aggregateBy) {
            AggregateFieldEnum field = AggregateFieldEnum.getByCode(dimension);
            if (field == null) {
                continue;
            }

            switch (field) {
                case POI_NAME:
                    keyParts.add(StringUtils.defaultString(riskCase.getPoiName(), CommonConstant.UNKNOWN));
                    break;
                case TYPE:
                    keyParts.add(String.valueOf(riskCase.getType().getCode()));
                    break;
                case PLACE_CODE:
                    keyParts.add(StringUtils.defaultString(riskCase.getPlaceCode(), CommonConstant.UNKNOWN));
                    break;
                case PARKING_PLOT_ID:
                    keyParts.add(context.getParkingPlotId(riskCase.getCaseId()));
                    break;
                default:
                    keyParts.add(CommonConstant.UNKNOWN);
                    break;
            }
        }
        return Joiner.on(CharConstant.CHAR_XH).skipNulls().join(keyParts);
    }

    /**
     * 构建聚合结果
     */
    private AggregateResultDTO buildAggregateResult(String aggregateKey, List<RiskCaseDO> events,
            AggregateStrategyConfigDTO config, Date windowStart, Date windowEnd, List<String> aggregateBy,
            AggregateAlertContext context) {
        AggregateResultDTO.AggregateDimensions dimensions = buildDimensions(events, aggregateBy, context);
        AggregateResultDTO.AggregateStatistics statistics = buildStatistics(events);
        return AggregateResultDTO.builder()
                .aggregateKey(aggregateKey)
                .aggregateDimensions(dimensions)
                .caseCount(events.size())
                .riskCaseList(events)
                .timeWindowStart(windowStart)
                .timeWindowEnd(windowEnd)
                .statistics(statistics)
                .strategyId(config.getAlertPolicy())
                .build();
    }

    /**
     * 构建聚合维度
     */
    private AggregateResultDTO.AggregateDimensions buildDimensions(List<RiskCaseDO> events, List<String> aggregateBy,
            AggregateAlertContext context) {
        AggregateResultDTO.AggregateDimensions.AggregateDimensionsBuilder builder =
                AggregateResultDTO.AggregateDimensions.builder();
        if (CollectionUtils.isEmpty(events) || CollectionUtils.isEmpty(aggregateBy)) {
            return builder.build();
        }

        RiskCaseDO firstCase = events.get(0);
        for (String dimension : aggregateBy) {
            AggregateFieldEnum field = AggregateFieldEnum.getByCode(dimension);
            if (field == null) {
                continue;
            }
            switch (field) {
                case POI_NAME:
                    builder.poiName(firstCase.getPoiName());
                    break;
                case TYPE:
                    builder.type(firstCase.getType().getCode()).typeDesc(firstCase.getType().getDesc());
                    break;
                case PLACE_CODE:
                    builder.placeCode(firstCase.getPlaceCode());
                    break;
                case PARKING_PLOT_ID:
                    builder.parkingPlotId(
                            context.getParkingPlotId(firstCase.getCaseId()));
                    break;
            }
        }
        return builder.build();
    }

    /**
     * 构建统计信息
     */
    private AggregateResultDTO.AggregateStatistics buildStatistics(List<RiskCaseDO> caseDOList) {
        AggregateResultDTO.AggregateStatistics.AggregateStatisticsBuilder builder =
                AggregateResultDTO.AggregateStatistics.builder();
        if (CollectionUtils.isEmpty(caseDOList)) {
            return builder.build();
        }

        // 事件类型分布
        Map<String, Long> typeDistribution = caseDOList.stream()
                .collect(Collectors.groupingBy(caseDO -> caseDO.getType().getDesc(), Collectors.counting()));
        builder.typeDistribution(typeDistribution);
        // 时间分布
        Date earliestTime = caseDOList.stream()
                .map(RiskCaseDO::getOccurTime)
                .filter(Objects::nonNull)
                .min(Date::compareTo)
                .orElse(null);
        Date latestTime = caseDOList.stream()
                .map(RiskCaseDO::getOccurTime)
                .filter(Objects::nonNull)
                .max(Date::compareTo)
                .orElse(null);
        builder.earliestTime(earliestTime).latestTime(latestTime);
        // 计算时间跨度
        if (earliestTime != null && latestTime != null) {
            long timeSpanSeconds = (latestTime.getTime() - earliestTime.getTime()) / 1000;
            builder.timeSpanSeconds(timeSpanSeconds);
        }

        return builder.build();
    }


}