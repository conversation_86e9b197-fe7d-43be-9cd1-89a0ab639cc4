package com.sankuai.wallemonitor.risk.center.domain.process;


import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCheckingQueueService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 处理预检队列项状态为「检查中」
 */
@Slf4j
@Component
public class RiskCheckingQueueItemCheckingProcess implements
        DomainEventProcess {

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    @Resource
    private RiskCheckingQueueService riskCheckingQueueService;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private LockUtils lockUtils;


    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.RISK_CHECKING_QUEUE_ITEM_BE_CHECKING_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != RiskCheckingQueueItemDO.class) {
            return true;
        }

        DomainEventChangeDTO<RiskCheckingQueueItemDO> typedDomainEvent = (DomainEventChangeDTO<RiskCheckingQueueItemDO>) eventDTO;
        List<RiskCheckingQueueItemDO> inCheckingItemList = typedDomainEvent.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "checking"))
                .stream()
                // 只要checking变更为true即可，2个入口，一个是crane查询，一个是创建即为true
                .filter(RiskCheckingQueueItemDO::getChecking)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inCheckingItemList)) {
            log.info("inCheckingItemList is empty");
            return true;
        }
        log.info("RiskCheckingQueueItemCheckingProcess inCheckingItemList: {}", JacksonUtils.to(inCheckingItemList));
        return handleQueueItemIntoChecking(inCheckingItemList);
    }

    /**
     * 处理进入「检查中」的预检项
     * - 拉最新的
     * - 获取检查结果（包括但不限于大模型检查、自动驾驶信息检查等）
     * - 上锁
     *  - 再查状态（避免预检过程中，预检项已终态）
     *  - 更新检查结果
     *  - 存库
     *  - 释放锁
     * @param itemList
     * @return
     */
    private boolean handleQueueItemIntoChecking(List<RiskCheckingQueueItemDO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return true;
        }
        // 获取检查结果
        Map<String, ISCheckActionResult> result = riskCheckingQueueService.batchCalcCheckingResult(
                itemList);
        // 上锁后更新检查结果
        try {
            lockUtils.batchLockCanWait(LockKeyPreUtil.buildLockKeys(itemList),
                    () -> {
                        // 更新最新的计算结果
                        riskCheckingQueueService.updateAndSaveRiskCheckingQueueItem(
                                itemList.stream().map(RiskCheckingQueueItemDO::getTmpCaseId)
                                        .collect(Collectors.toList()), result);
                    });
            return true;
        } catch (Exception e) {
            log.error("handleQueueItemIntoChecking error", e);
            return false;
        }
    }

}
