package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import java.util.List;
import java.util.Map;

public interface RiskCheckingQueueService {

    /**
     * 根据eventId查询风险预检项
     *
     * @param eventId
     * @return
     */
    RiskCheckingQueueItemDO getCheckingItemByEventId(String eventId);

    /**
     * 批量标注为校验中
     *
     * @param riskCheckingQueueItemDOList
     * @return
     */
    void batchUpdateItemToChecking(List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList);

    void updateAndSaveRiskCheckingQueueItem(List<String> tmpCaseIdList,
            Map<String, ISCheckActionResult> checkResultMap);

    void updateOneRiskCheckingQueueItem(RiskCheckingQueueItemDO queueItemDO,
            ISCheckActionResult resultDTO);

    /**
     * 批量计算校验结果
     *
     * @param riskCheckingQueueItemDOList
     * @return
     */
    Map<String, ISCheckActionResult> batchCalcCheckingResult(
            List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList);


    void resetQueueItemForDynamicCheck(RiskCheckingQueueItemDO queueItem, RiskCheckResultDO lastCheckResultDO);

    /**
     * 追加到风险预检队列中
     *
     * @param paramDTO
     * @return
     */
    void append(RiskCaseUpdatedParamDTO paramDTO);

    /**
     * 批量追加到风险预检队列中
     *
     * @param paramDTOList
     */
    void append(List<RiskCaseUpdatedParamDTO> paramDTOList);

    /**
     * 监听到上游的接触信号，从预检队列中取消风险事件
     *
     * @param paramDTO
     */
    void cancelItem(RiskCaseUpdatedParamDTO paramDTO);

    /**
     * 批量取消风险事件
     *
     * @param paramDTOList
     */
    void cancelItem(List<RiskCaseUpdatedParamDTO> paramDTOList);

    /**
     * 判断风险事件是否存在
     *
     * @param eventId
     * @return
     */
    Boolean checkRiskEventExists(String eventId);

}
