package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.domain.result.ISEveAndRuntimeInfoFilterActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 通用过滤action
 */
@Slf4j
@Component
public class ISEveAndRuntimeInfoFilterAction implements ISCheckAction<ISEveAndRuntimeInfoFilterActionResult> {

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    /**
     * 过滤规则
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_FILTER_ACTION_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private List<String> filterActionStrategyConfig;

    @Override
    public ISCheckActionResult<ISEveAndRuntimeInfoFilterActionResult> execute(ISCheckActionContext actionContext) {
        RiskCheckingQueueItemDO itemDO = actionContext.getItem();
        if (Objects.isNull(itemDO)) {
            return ISCheckActionResult.empty();
        }
        // 获取车辆的实时信息
        VehicleEveInfoVTO vehicleEveInfoVTO = vehicleAdapter.queryRuntimeVehicleInfoByVin(itemDO.getVin());
        // 获取车辆上下文信息
        VehicleRuntimeInfoContextDO vehicleRuntimeContext = actionContext.getVehicleRunTimeContext();

        Map<String, Object> context = new HashMap<>();
        context.put("vehicleEveInfo", vehicleEveInfoVTO);
        context.put("vehicleContext", vehicleRuntimeContext);
        String filterReason = isFilter(context);
        if (StringUtils.isNotBlank(filterReason)) {
            return ISCheckActionResult.<ISEveAndRuntimeInfoFilterActionResult>builder()
                    .categoryEnum(ISCheckCategoryEnum.EVE_AND_RUNTIME_INFO_FILTER)
                    .actionResult(ISEveAndRuntimeInfoFilterActionResult.builder().shouldFilter(true)
                            .filterReason(filterReason).build())
                    .build();

        }
        return ISCheckActionResult.empty();
    }

    /**
     * 判断是否过滤
     *
     * @param context
     * @return
     */
    private String isFilter(Map<String, Object> context) {
        if (CollectionUtils.isEmpty(filterActionStrategyConfig)) {
            return CharConstant.CHAR_EMPTY;
        }
        // 不满足准入条件
        String filterBy = filterActionStrategyConfig.stream()
                //做逻辑判断
                .filter(filter -> SpELUtil.evaluateBoolean(filter, context)).findFirst()
                //过滤
                .orElse(CharConstant.CHAR_EMPTY);

        return filterBy;
    }

}
