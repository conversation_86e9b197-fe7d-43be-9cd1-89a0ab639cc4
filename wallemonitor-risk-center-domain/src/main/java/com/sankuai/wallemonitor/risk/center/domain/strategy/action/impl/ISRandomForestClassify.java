package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.GeometryUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.MapElementRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.vo.HdMapElementGeoVO;
import com.sankuai.wallemonitor.risk.center.domain.result.ISRandomForestClassifyResult;
import com.sankuai.wallemonitor.risk.center.domain.result.ISWaitingInQueueResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RandomForestPredictService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RandomForestPredictionResultDTO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RiskFeatureDataDTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RandomForestPredictConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.LanePositionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.LaneTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 随机森林算法分类
 */
@Slf4j
@Component
public class ISRandomForestClassify implements ISCheckAction<ISRandomForestClassifyResult> {

    /**
     * 地图类型
     */
    private static final String MAP_TYPE = "lane_polygon";

    /**
     * 障碍物相关特征默认值
     */
    private static final Double DEFAULT_OBSTACLE_DISTANCE = 100.0;

    /**
     * 距离路口距离的默认值
     */
    private static final Double DEFAULT_DISTANCE_TO_NEXT_JUNCTION = -1.0;

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private RandomForestPredictService predictService;

    @Resource
    private RiskCaseVehicleRelationRepository relationRepository;

    @Resource
    private ISWaitingInQueueV2 isWaitingInQueueV2;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RANDOM_FOREST_PREDICT_CONFIG, defaultValue = "{}")
    private RandomForestPredictConfigDTO configDTO;

    /**
     * 执行预检
     *
     * @param actionContext
     */
    @Override
    public ISCheckActionResult<ISRandomForestClassifyResult> execute(ISCheckActionContext actionContext) {
        VehicleRuntimeInfoContextDO contextDO = actionContext.getVehicleRunTimeContext();
        RiskCheckingQueueItemDO itemDO = actionContext.getItem();
        // 1 入参校验
        if (contextDO == null || itemDO == null) {
            return ISCheckActionResult.empty();
        }
        // 配置校验
        CheckUtil.isNotNull(configDTO, "configDTO is not null");
        // 2 构建特征
        String trafficLightType = Objects.isNull(contextDO.getTrafficLightType()) ? TrafficLightTypeEnum.UNKNOWN.name()
                : contextDO.getTrafficLightType().name();
        Double distanceToNextJunction = Objects.isNull(contextDO.getDistanceToNextJunction()) || Objects.equals(
                contextDO.getDistanceToNextJunction(), -1) ? DEFAULT_DISTANCE_TO_NEXT_JUNCTION
                : Double.valueOf(contextDO.getDistanceToNextJunction());

        RiskFeatureDataDTO riskFeatureDataDTO = RiskFeatureDataDTO.builder()
                // WGS84格式
                .longitude(Double.valueOf(contextDO.getLng()))
                .latitude(Double.valueOf(contextDO.getLat()))
                .trafficLightType(trafficLightType)
                // 先设置默认值， 然后在computeObstacleFeatures 中更新障碍物相关信息
                .obstacleDistance(DEFAULT_OBSTACLE_DISTANCE)
                .obstacleAngle(DEFAULT_OBSTACLE_DISTANCE)
                .distanceToNextJunction(distanceToNextJunction)
                // 先设置默认值，然后在 computeLaneFeatures 中更新车道相关特征的取值
                .laneType(LaneTypeEnum.UNKNOWN.getCode())
                .lanePosition(LanePositionEnum.NONE.getCode())
                .isNeighborLaneAvailable(false)
                .build();
        // 计算障碍物相关信息
        computeObstacleFeatures(actionContext, riskFeatureDataDTO);
        // todo : item中的eventId对应 relation表中的 caseId
        // 计算车道相关特征信息
        computeLaneFeatures(getHdMapVersion(itemDO.getEventId()), riskFeatureDataDTO);
        // 3 随机森林模型预测
        RandomForestPredictionResultDTO resultDTO = predictService.predict(riskFeatureDataDTO);
        log.info("ISRandomForestClassify,caseId:{}, featureDTO:{}, result:{}", itemDO.getTmpCaseId(),
                riskFeatureDataDTO,
                JacksonUtils.to(resultDTO));
        if (Objects.nonNull(resultDTO)) {
            ISCheckCategoryEnum categoryEnum =
                    resultDTO.getIsRisky() ? ISCheckCategoryEnum.GOOD_OTHER
                            : ISCheckCategoryEnum.BAD_OTHER;
            return ISCheckActionResult.<ISRandomForestClassifyResult>builder()
                    .categoryEnum(categoryEnum)
                    .actionResult(ISRandomForestClassifyResult.builder()
                            .isRisky(resultDTO.getIsRisky())
                            .riskProbability(resultDTO.getRiskProbability())
                            .featureDataDTO(riskFeatureDataDTO).build()).build();

        }
        return ISCheckActionResult.empty();
    }


    /**
     * 计算车道相关特征
     */
    private RiskFeatureDataDTO computeLaneFeatures(String hdMapVersion, RiskFeatureDataDTO featureDataDTO) {
        try {
            // 1 根据美团地图查询指定经纬度/范围内的地图元素
            MapElementRequestDTO laneQueryRequestDTO = MapElementRequestDTO.builder()
                    .distance(configDTO.getMapElementQueryDistance())
                    .mapType(MAP_TYPE)
                    .hdMapVersion(hdMapVersion)
                    .longitude(featureDataDTO.getLongitude())
                    .latitude(featureDataDTO.getLatitude())
                    .build();
            List<HdMapElementGeoVO> elementGeoVOList = hdMapAdapter.searchMapElementByMapElementRequestDTO(
                    laneQueryRequestDTO);
            if (CollectionUtils.isEmpty(elementGeoVOList)) {
                return featureDataDTO;
            }

            Coordinate coordinate = new Coordinate(laneQueryRequestDTO.getLongitude(),
                    laneQueryRequestDTO.getLatitude());
            for (HdMapElementGeoVO geoVO : elementGeoVOList) {
                List<List<Double>> pointList = geoVO.getPoints();
                if (CollectionUtils.isEmpty(pointList)) {
                    continue;
                }
                List<Coordinate> laneArea = pointList.stream().map(x -> new Coordinate(x.get(0), x.get(1)))
                        .collect(Collectors.toList());
                // 判断当前点所在的车道
                if (GeometryUtil.inPolygon(coordinate, laneArea)) {
                    // 计算车道类型
                    Map<String, String> propertiesMap = JacksonUtils.from(geoVO.getProperties(), Map.class);
                    String leftLaneId = "";
                    String rightLaneId = "";
                    if (Objects.nonNull(propertiesMap)) {
                        leftLaneId = propertiesMap.getOrDefault("left", "");
                        rightLaneId = propertiesMap.getOrDefault("right", "");
                    }
                    // 假设左右车道均存在
                    if (StringUtils.isNotBlank(leftLaneId) && StringUtils.isNotBlank(rightLaneId)) {
                        featureDataDTO.setLaneType(LaneTypeEnum.MULTI_LANE.getCode());
                        featureDataDTO.setLanePosition(LanePositionEnum.MIDDLE.getCode());
                        // 只有左侧车道存在，当前车道位于最右侧
                    } else if (StringUtils.isNotBlank(leftLaneId)) {
                        featureDataDTO.setLaneType(LaneTypeEnum.MULTI_LANE.getCode());
                        featureDataDTO.setLanePosition(LanePositionEnum.RIGHT.getCode());
                        // 只有右侧车道存在，当前车道位于最左侧
                    } else if (StringUtils.isNotBlank(rightLaneId)) {
                        featureDataDTO.setLaneType(LaneTypeEnum.MULTI_LANE.getCode());
                        featureDataDTO.setLanePosition(LanePositionEnum.LEFT.getCode());
                        //  只有当前车道存在,无左右车道
                    } else {
                        featureDataDTO.setLaneType(LaneTypeEnum.SINGLE_LANE.getCode());
                        featureDataDTO.setLanePosition(LanePositionEnum.NONE.getCode());
                    }
                    break;
                }
            }
        } catch (Exception e) {
            log.error("ISRandomForestClassify.computeFeature error", e);
        }
        return featureDataDTO;
    }

    /**
     * 计算障碍物相关特征
     *
     * @param actionContext
     * @param featureDataDTO
     * @return
     */
    private RiskFeatureDataDTO computeObstacleFeatures(ISCheckActionContext actionContext,
            RiskFeatureDataDTO featureDataDTO) {

        // 执行排队通行策略
        ISCheckActionResult<ISWaitingInQueueResult> actionResult = isWaitingInQueueV2.execute(actionContext);
        if (Objects.isNull(actionResult) || Objects.isNull(actionResult.getActionResult())) {
            return featureDataDTO;
        }
        ISWaitingInQueueResult result = actionResult.getActionResult();
        if (Objects.isNull(result.getFrontObstacle())) {
            return featureDataDTO;
        }
        // 设置当前自车距离障碍物的距离, 只有不为 NULL 的时候才进行赋值
        if (Objects.nonNull(result.getFrontObstacle().getDistance())) {
            featureDataDTO.setObstacleDistance(result.getFrontObstacle().getDistance());
        }
        // 设置当前自车与前方障碍物之间的角度, 亦然
        if (Objects.nonNull(result.getFrontObstacle().getAngle())) {
            featureDataDTO.setObstacleAngle(result.getFrontObstacle().getAngle());
        }
        // 设置当前自车前方是否存在可用车道
        if (!CollectionUtils.isEmpty(result.getUsableLaneIds())) {
            featureDataDTO.setIsNeighborLaneAvailable(true);
        }

        return featureDataDTO;

    }

    /**
     * 获取高精地图版本
     *
     * @return
     */
    private String getHdMapVersion(String caseId) {
        RiskCaseVehicleRelationDO relationDO = relationRepository.getByCaseId(caseId);
        if (Objects.isNull(relationDO) || Objects.isNull(relationDO.getVehicleSnapshotInfo())) {
            return "";
        }

        return relationDO.getVehicleSnapshotInfo().getHdMapVersion();
    }

}
