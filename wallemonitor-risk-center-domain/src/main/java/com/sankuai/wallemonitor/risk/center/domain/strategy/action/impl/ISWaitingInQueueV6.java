package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import static com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant.SECTION_TYPE_MIDDLE;
import static com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant.SECTION_TYPE_RIGHT;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.result.ISWaitingInQueueResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.SeatInterventionConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleObstacleInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.BehindObstacleCheckConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.CrossLineWaitCheckConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.ExtendObstacleCheckConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.JunctionWaitConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.MultiCarInSameDistanceConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.NonTrafficLightWaitInLineDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.SwitchObstacleCheckConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.WaitInQueueConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GeoQueryDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapLaneDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.ActionChainResultLogDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseLocationRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeLocationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.ActionChainResultLogRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseLocationRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseLocationRelationRepository.CaseLocationRelationDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoLocationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.LogActionChainResultQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeLocationDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.common.math.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 停滞不当预检 - 是否在排队通行v3
 * - 相对v2版本，优化静态车辆绕行场景提召回，检查后方是否有社会车辆
 */
@Slf4j
@Component
public class ISWaitingInQueueV6 implements ISCheckAction<ISWaitingInQueueResult> {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;


    @Resource
    private VehicleRuntimeInfoLocationRepository vehicleRuntimeInfoLocationRepository;

    /**
     * 历史action的执行记录
     */
    @Resource
    private ActionChainResultLogRepository actionChainResultLogRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository runtimeInfoContextRepository;

    @Resource
    private CaseLocationRelationRepository caseLocationRelationRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Override
    public ISCheckActionResult<ISWaitingInQueueResult> execute(ISCheckActionContext actionContext) {
        RiskCheckingQueueItemDO itemDO = actionContext.getItem();
        // 车辆上下文
        VehicleRuntimeInfoContextDO runTimeContext = actionContext.getVehicleRunTimeContext();
        if (Objects.isNull(itemDO) || Objects.isNull(runTimeContext)) {
            return ISCheckActionResult.empty();
        }
        ISWaitingInQueueResult result = ISWaitingInQueueResult.builder().build();
        // 排队配置
        WaitInQueueConfigDTO waitInQueueConfig = actionContext.getCurrentActionConfig(WaitInQueueConfigDTO.class);
        // 当前车道
        HdMapElementGeoDO vehicleCurLane = hdMapAdapter.getLaneById(runTimeContext.getVehicleCurLaneId());
        // 其他当前车道
        List<HdMapElementGeoDO> vehicleCurLaneList = hdMapAdapter
                .queryByLaneIdList(runTimeContext.getVehicleCurLaneIdList());
        // 车辆周围车道
        Map<String, List<HdMapElementGeoDO>> vehicleAroundLane = new HashMap<>();
        if (MapUtils.isNotEmpty(runTimeContext.getVehicleAroundLaneId())) {
            runTimeContext.getVehicleAroundLaneId().forEach((laneType, laneIdList) -> {
                List<String> laneIds = Arrays.asList(StringUtils.split(laneIdList, CharConstant.CHAR_COMMA));
                if (CollectionUtils.isEmpty(laneIds)) {
                    return;
                }
                // 如果车道不为空
                vehicleAroundLane.computeIfAbsent(laneType, k -> hdMapAdapter.queryByLaneIdList(laneIds));
            });
        }
        // 可用车道
        List<String> usableLaneIds = runTimeContext.getUsableLaneIds();
        boolean farOrWithoutJunction = Objects.equals(runTimeContext.getDistanceToJunction(),
                NumberUtils.DOUBLE_MINUS_ONE)
                || runTimeContext.getDistanceToJunction() >= waitInQueueConfig.getThresholdOpenUsableLaneChecking();
        if (Objects.isNull(runTimeContext.getFrontObstacle())) {
            // 前方无车，非排队场景
            return ISCheckActionResult.empty(result);
        }
        if (isSwitchObstacle(actionContext, result, waitInQueueConfig.getSwitchObstacleWaitCheckConfig())) {
            // 前后障碍物换帧：当车辆处于排队，但是前后障碍物发生变化
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(result.getSwitchObstacleCategory()).build();
        }
        if (isInJunctionWait(runTimeContext, result, waitInQueueConfig.getJunctionWaitConfig())) {
            // 路口内排队
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.IN_JUNCTION).build();
        }
        if (isWaitCrossLine(runTimeContext, result,
                waitInQueueConfig.getCrossLineWaitCheckConfig())) {
            // 压线排队：当车辆排队，但是距离车道边界的距离，过近
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.IN_MIDDLE_ROAD).build();
        }
        if (isFirstAutoCarInRisk(result, vehicleCurLaneList,  // 本车道
                vehicleAroundLane.get(GeoElementTypeKeyConstant.SUCCESSOR),  // 后继
                waitInQueueConfig.getExtendObstacleCheckConfig(), // 配置
                actionContext)) {
            // 自车风险传递：当自车排队时，查看头车的风险情况，前车有风险的情况下，算是被堵了
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.STOP_BY_OBSTACLE).build();
        }
        if (isSeatInterventionInHistory(runTimeContext, result, waitInQueueConfig.getSeatInterventionConfig())) {
            // 如果存在历史接管case，算作风险召回
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.STOP_BY_OBSTACLE).build();
        }
        if (isMultiCarInSameDistance(runTimeContext,result, waitInQueueConfig.getMultiCarInSameDistanceConfig())) {
            // 前方同距双车：同种类型，前方不能出现距离无差异的两个，存在就不是绕行，以堵路方式召回
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.STOP_BY_OBSTACLE).build();
        }
        if (isNonTrafficLightInRightWait(result, runTimeContext, waitInQueueConfig.getNonTrafficLightWaitConfig())) {
            // 最右侧/次右侧车道，无灯 且 障碍物靠边
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.STOP_BY_INACTIVE_VEHICLE).build();
        }

        if (isUsableLane(farOrWithoutJunction, result, runTimeContext, usableLaneIds)) {
            // 非路口可借道判定： 暂无路口 或者 距离路口过远,有可使用车道，则返回障碍物堵路
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.STOP_BY_INACTIVE_VEHICLE).build();
        }
        if (isNonBehindObs(farOrWithoutJunction, runTimeContext,
                waitInQueueConfig.getBehindObstacleCheckConfig())) {
            // 非路口后方无社会车辆判定：【当前车辆无后车，不在路口且在最右】，则返回静态区域
            result.setNonBehindObs(true);
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.STOP_BY_INACTIVE_VEHICLE).build();
        }
        if (Objects.nonNull(vehicleCurLane)
                && isInMixedBikingLane(runTimeContext, vehicleCurLaneList, waitInQueueConfig)) {
            // 非路口敏感车道排队：检测是否在辅路（mixed/biking车道）且非路口内
            result.setInMixedBikingLane(true);
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.STOP_BY_OBSTACLE).build();
        }
        // 排队
        return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                .categoryEnum(ISCheckCategoryEnum.WAITING_FRONT_PASSAGER).build();
    }

    /**
     * 判断地点周围是否发生过坐席干预
     *
     * @param runTimeContext
     * @param result
     * @param seatInterventionConfig
     * @return
     */
    private boolean isSeatInterventionInHistory(VehicleRuntimeInfoContextDO runTimeContext,
            ISWaitingInQueueResult result, SeatInterventionConfigDTO seatInterventionConfig) {
        if (seatInterventionConfig == null || !seatInterventionConfig.isEnable()) {
            return false;
        }
        PositionDO positionDO = runTimeContext.getLocation();
        if (positionDO == null || seatInterventionConfig.getSearchRange() == null
                || seatInterventionConfig.getBeforeSeconds() == null) {
            return false;
        }
        Date end = new Date();
        Date start = DatetimeUtil.getNSecondsBeforeDateTime(end, seatInterventionConfig.getBeforeSeconds());
        List<CaseLocationRelationDO> seatInterventionRecordDOS = caseLocationRelationRepository
                .queryByParam(CaseLocationRelationDOQueryParamDTO.builder()
                        .createTimeRange(TimePeriod.build(start, end)).locationQuery(GeoQueryDO.builder()
                                .distance(seatInterventionConfig.getSearchRange()).point(positionDO.toPoint()).build())
                        .build());
        if (CollectionUtils.isEmpty(seatInterventionRecordDOS)) {
            log.info("isSeatInterventionInHistory: no seat intervention record");
            return false;
        }
        List<String> caseIds = seatInterventionRecordDOS.stream().map(CaseLocationRelationDO::getCaseId)
                .collect(Collectors.toList());
        // 查询关联的车信息
        List<RiskCaseVehicleRelationDO> caseVehicleRelationDOS = riskCaseVehicleRelationRepository
                .queryByParam(RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIds).build()).stream()
                .filter(relation -> Objects.nonNull(relation.getSeatInterventionTime())
                        && !DatetimeUtil.isDbZeroTime(relation.getSeatInterventionTime()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(caseVehicleRelationDOS)) {
            log.info("isSeatInterventionInHistory: no case vehicle relation record");
        }
        List<String> seatInterventionCaseId = caseVehicleRelationDOS.stream().map(RiskCaseVehicleRelationDO::getCaseId)
                .collect(Collectors.toList());
        // 记录接管caseId
        result.setSeatInterventionCaseId(seatInterventionCaseId);
        // 允许召回 且 存在接管的caseId
        return Boolean.TRUE.equals(seatInterventionConfig.isRecallWhenIntervention())
                && CollectionUtils.isNotEmpty(seatInterventionCaseId);

    }

    private boolean isUsableLane(boolean farOrWithoutJunction, ISWaitingInQueueResult result,
            VehicleRuntimeInfoContextDO runTimeContext, List<String> usableLaneIds) {
        if (CollectionUtils.isEmpty(usableLaneIds)) {
            return false;
        }
        // 非路口 且 不是中间车道 且 有可用道路
        boolean nonJunctionHasUsableLane = farOrWithoutJunction
                && !SECTION_TYPE_MIDDLE.equals(runTimeContext.getVehicleLaneSectionType())
                && CollectionUtils.isNotEmpty(usableLaneIds);
        result.setNonJunctionUsableLane(nonJunctionHasUsableLane);
        return nonJunctionHasUsableLane;
    }

    /**
     * 前方同距双车
     *
     * @param runTimeContext
     * @param result
     * @param multiCarInSameDistanceConfig
     * @return
     */
    private boolean isMultiCarInSameDistance(VehicleRuntimeInfoContextDO runTimeContext, ISWaitingInQueueResult result,
            MultiCarInSameDistanceConfigDTO multiCarInSameDistanceConfig) {
        if (multiCarInSameDistanceConfig == null || !multiCarInSameDistanceConfig.isEnable()) {
            return false;
        }
        if (CollectionUtils.isEmpty(runTimeContext.getFrontObstacleList())) {
            return false;
        }
        Map<String, List<VehicleObstacleInfoDTO>> fineTypeMap = runTimeContext.getFrontObstacleList().stream()
                .collect(Collectors.groupingBy(VehicleObstacleInfoDTO::getFineType, Collectors.toList()));
        if (MapUtils.isEmpty(fineTypeMap)) {
            // fineType
            return false;
        }
        for (Map.Entry<String, List<VehicleObstacleInfoDTO>> entry : fineTypeMap.entrySet()) {
            List<List<String>> sameDistanceObstacles = new ArrayList<>();
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            String fineType = entry.getKey();
            List<VehicleObstacleInfoDTO> obstacleInfoDTOList = entry.getValue();
            Map<String, List<VehicleObstacleInfoDTO>> lane2ObstacleMap = obstacleInfoDTOList.stream()
                    .filter(obstacleInfoDTO -> StringUtils.isNotBlank(obstacleInfoDTO.getLaneId()))
                    .collect(Collectors.groupingBy(VehicleObstacleInfoDTO::getLaneRelation2Vehicle));
            // 车道必须是当前或者后继
            for (Map.Entry<String, List<VehicleObstacleInfoDTO>> laneRelation2ObstacleEntry : lane2ObstacleMap
                    .entrySet()) {
                String laneRelation = laneRelation2ObstacleEntry.getKey();
                List<VehicleObstacleInfoDTO> laneObstacles = laneRelation2ObstacleEntry.getValue();
                if (!GeoElementTypeKeyConstant.SUCCESSOR.equals(laneRelation)
                        && !GeoElementTypeKeyConstant.CUR.equals(laneRelation)) {
                    // 非当前车道和本车道，不进行处理
                    continue;
                }
                // 看车道里面相聚比较近的
                Integer count = 0;
                for (int i = 0; i < laneObstacles.size() - 1; i++) {
                    // 每两个为一组，做判定
                    VehicleObstacleInfoDTO obstacleInfoDTO = laneObstacles.get(i);
                    VehicleObstacleInfoDTO nextObstacleInfoDTO = laneObstacles.get(i + 1);
                    if (obstacleInfoDTO == null || nextObstacleInfoDTO == null) {
                        continue;
                    }
                    Double distance = GeoToolsUtil.distance(obstacleInfoDTO.getPosition(),
                            nextObstacleInfoDTO.getPosition());
                    if (distance == null) {
                        continue;
                    }
                    if (multiCarInSameDistanceConfig.inSameDistance(fineType, distance)) {
                        // 如果同距
                        List<String> obstacleIdList = new ArrayList<>();
                        obstacleIdList.add(obstacleInfoDTO.getObstacleId());
                        obstacleIdList.add(nextObstacleInfoDTO.getObstacleId());
                        sameDistanceObstacles.add(obstacleIdList);
                        count++;
                    }
                }
                if (count >= multiCarInSameDistanceConfig.getInSameDistanceCount()) {
                    // 满足个数限制
                    result.setMultiCarInSameDistanceCount(count);
                    result.setInSameDistanceObstacleIdList(sameDistanceObstacles);
                    return true; // 直接返回结果
                }
            }
        }
        return false;
    }

    /**
     * 非路口后方无社会车辆判定
     *
     * @param farOrWithoutJunction
     * @param runtimeInfo
     * @param behindObstacleCheckConfig
     * @return
     */
    private boolean isNonBehindObs(boolean farOrWithoutJunction,
            VehicleRuntimeInfoContextDO runtimeInfo, BehindObstacleCheckConfig behindObstacleCheckConfig) {
        return farOrWithoutJunction && behindObstacleCheckConfig != null
                && behindObstacleCheckConfig.isEnable()
                // 无后方障碍物
                && Objects.isNull(runtimeInfo.getBehindObstacle())
                // 属于最右车道
                && GeoElementTypeKeyConstant.RIGHT.equals(runtimeInfo.getVehicleLaneSectionType())
                // 可借道
                && CollectionUtils.isNotEmpty(runtimeInfo.getUsableLaneIds());
    }

    /**
     * 是否最右侧或者次右车道最右为bike无灯排队
     *
     * @param runTimeContext
     * @param nonTrafficLightWaitConfig
     * @return
     */
    private boolean isNonTrafficLightInRightWait(ISWaitingInQueueResult result,
            VehicleRuntimeInfoContextDO runTimeContext, NonTrafficLightWaitInLineDTO nonTrafficLightWaitConfig) {
        if (nonTrafficLightWaitConfig == null || !nonTrafficLightWaitConfig.isEnable()
        ) {
            return false;
        }
        VehicleObstacleInfoDTO obstacleInfoDTO = Optional.ofNullable(runTimeContext.getFrontObstacle())
                .orElseGet(() -> {
                    if (CollectionUtils.isEmpty(runTimeContext.getFrontObstacleList())) {
                        return null;
                    }
                    return runTimeContext.getFrontObstacleList().stream()
                            .min(Comparator.comparing(VehicleObstacleInfoDTO::getDistance))
                            .orElse(null);
                });
        if (obstacleInfoDTO == null || obstacleInfoDTO.getDistanceToNearCurb() == null) {
            return false;
        }
        HdMapElementGeoDO vehicleCurLane = hdMapAdapter.getLaneById(runTimeContext.getVehicleCurLaneId());
        if (SECTION_TYPE_RIGHT.equals(runTimeContext.getVehicleLaneSectionType())
                || Boolean.TRUE.equals(runTimeContext.getSingleLane())) {
            // 如果是最右 或者 单车道
            // 检索出多转向车道需要的次数
            Integer multiTurnCount = countMultiTurn(vehicleCurLane, nonTrafficLightWaitConfig.getCheckMultiTurnCount());
            boolean isNonTrafficLightWait = runTimeContext.hasNonTrafficLight()
                    && nonTrafficLightWaitConfig.isWaitNormal(obstacleInfoDTO.getWidth(),
                    obstacleInfoDTO.getDistanceToNearCurbList());
            result.setNonTrafficLightWait(isNonTrafficLightWait);
            result.setMultiTurnCount(multiTurnCount);
            // 检查
            return isNonTrafficLightWait && (
                    // 最大次数里面检测不出
                    multiTurnCount == -1
                            // 大于最大检测次数
                            || multiTurnCount > nonTrafficLightWaitConfig.getCheckMultiTurnCount());
        }
        if (SECTION_TYPE_MIDDLE.equals(runTimeContext.getVehicleLaneSectionType())) {
            // 检索出多转向车道需要的次数
            Integer multiTurnCount = countMultiTurn(vehicleCurLane, nonTrafficLightWaitConfig.getCheckMultiTurnCount());
            // 如果是中间车道
            String laneId = runTimeContext.getVehicleCurLaneId();
            HdMapElementGeoDO hdMapElementGeoDO = hdMapAdapter.getLaneById(laneId);
            if (hdMapElementGeoDO == null) {
                return false;
            }
            HdMapLaneDO hdMapLaneDO = hdMapElementGeoDO.toLandDO();
            if (hdMapLaneDO == null) {
                return false;
            }

            HdMapLaneDO lastRightLane = null;
            int rightCount = 0;
            String rightId = hdMapLaneDO.getRight();
            while (hdMapLaneDO != null) {
                rightCount++;
                lastRightLane = hdMapLaneDO;
                hdMapLaneDO = Optional.ofNullable(hdMapAdapter.getLaneById(rightId)).map(HdMapElementGeoDO::toLandDO)
                        .orElse(null);
                rightId = Optional.ofNullable(hdMapLaneDO).map(HdMapLaneDO::getRight).orElse(CharConstant.CHAR_EMPTY);
            }
            if (rightCount > 1) {
                // 有超过一个车道
                return false;
            }
            // 有一个右侧车道，且车辆类型为指定
            boolean isNonTrafficLightWait = rightCount == 1
                    // 无灯
                    && runTimeContext.hasNonTrafficLight()
                    // 右侧车道可用
                    && nonTrafficLightWaitConfig.rightLaneCanUse(lastRightLane.getLaneType())
                    // 和车道近似平行线的距离差
                    && nonTrafficLightWaitConfig.isWaitNormal(obstacleInfoDTO.getWidth(),
                    obstacleInfoDTO.getDistanceToNearCurbList())
                    // 在三次计数内检索到多转向车道 （猜测其他障碍物的转向）
                    && (
                    // 最大次数里面检测不出
                    multiTurnCount == -1
                            // 大于最大检测次数
                            || multiTurnCount > nonTrafficLightWaitConfig.getCheckMultiTurnCount());
            result.setNonTrafficLightWait(isNonTrafficLightWait);
            result.setMultiTurnCount(multiTurnCount);
            return isNonTrafficLightWait;
        }
        // 其他都不算
        return false;
    }

    /**
     * 是否路口内排队
     *
     * @param runTimeContext
     * @param result
     * @param junctionWaitConfig
     * @return
     */
    private boolean isInJunctionWait(VehicleRuntimeInfoContextDO runTimeContext,
            ISWaitingInQueueResult result, JunctionWaitConfigDTO junctionWaitConfig) {
        if (junctionWaitConfig == null || !junctionWaitConfig.isEnable()) {
            return false;
        }
        boolean inJunctionWait = runTimeContext.hasInJunction();
        result.setInJunctionWait(inJunctionWait);
        return inJunctionWait;

    }

    /**
     * 是否前后帧更换障碍物
     *
     * @param isCheckActionContext
     * @param result
     * @param switchObstacleCheckConfig
     * @return
     */
    private boolean isSwitchObstacle(ISCheckActionContext isCheckActionContext,
            ISWaitingInQueueResult result,
            SwitchObstacleCheckConfig switchObstacleCheckConfig) {
        if (switchObstacleCheckConfig == null || !switchObstacleCheckConfig.isEnable()) {
            return false;
        }
        RiskCheckingQueueItemDO checkingQueueItemDO = isCheckActionContext.getItem();
        String currentActionName = isCheckActionContext.getCurrentActionName();
        String scene = isCheckActionContext.getCheckScene();
        String markVersion = isCheckActionContext.getMarkVersion();
        VehicleRuntimeInfoContextDO contextDO = isCheckActionContext.getVehicleRunTimeContext();
        // 查上一次
        List<ActionChainResultLogDO> lastLogList = actionChainResultLogRepository
                .queryByParam(LogActionChainResultQueryParamDTO.builder().actionName(currentActionName) // 同类型
                        // 预检
                        .caseId(checkingQueueItemDO.findCaseId(isCheckActionContext.getCheckScene())) // 同case
                        .scene(scene) // 同场景
                        .markVersion(markVersion) // 同版本
                        .orderByCreateTime(OrderEnum.DESC).build());
        if (CollectionUtils.isEmpty(lastLogList)) {
            // 没有记录，不需要考虑换帧
            return false;
        }
        // 查历史记录
        List<VehicleRuntimeInfoContextDO> lastCheckResultList = lastLogList.stream()
                .map(log -> JacksonUtils.from(log.getVehicleRuntimeInfoSnapshot(),
                        new TypeReference<VehicleRuntimeInfoContextDO>() {}))
                .collect(Collectors.toList());
        // 本次，作为最新的一个，加入的列表里面，开始做计算
        lastCheckResultList.add(0, contextDO);
        // 前、后障碍物更换次数
        int frontObsSwitchTotalCount = 0;
        int behindObsSwitchTotalCount = 0;
        // 前障碍物远离
        double frontAwayTotalDistance = 0D;
        // 前障碍物靠近
        double frontCloseTotalDistance = 0D;
        // 后障碍物远离
        double behindObsAwayTotalDistance = 0D;
        // 后障碍物靠近
        double behindCloseTotalDistance = 0D;
        // 前方障碍物换帧：
        // 后方障碍物换帧：
        for (int i = 0; i < lastCheckResultList.size() - 1; i++) {
            VehicleRuntimeInfoContextDO lastCheckResult = lastCheckResultList.get(i);
            VehicleRuntimeInfoContextDO currentCheckResult = lastCheckResultList.get(i + 1);
            List<VehicleObstacleInfoDTO> curFront = currentCheckResult.getFrontObstacleList();
            List<VehicleObstacleInfoDTO> curBehind = currentCheckResult.getBehindObstacleList();
            List<VehicleObstacleInfoDTO> lastFront = Optional.ofNullable(lastCheckResult)
                    .map(VehicleRuntimeInfoContextDO::getFrontObstacleList).orElse(new ArrayList<>());
            List<VehicleObstacleInfoDTO> lastBehind = Optional.ofNullable(lastCheckResult)
                    .map(VehicleRuntimeInfoContextDO::getBehindObstacleList).orElse(new ArrayList<>());

            // 判断前方范围障碍物是否发生切换、后方范围障碍物是否发生更换
            Integer frontSwitchCount = isAheadOrBehindSwitch(curFront, lastFront);
            Integer behindSwitchCount = isAheadOrBehindSwitch(curBehind, lastBehind);
            frontObsSwitchTotalCount += frontSwitchCount;
            behindObsSwitchTotalCount += behindSwitchCount;

            // 前、后范围远离的最长距离
            frontAwayTotalDistance += countAwayOrCloseDistance(curFront, lastFront, true);
            behindObsAwayTotalDistance += countAwayOrCloseDistance(curBehind, lastBehind, true);
            // 前、后范围接近的最长距离
            frontCloseTotalDistance += countAwayOrCloseDistance(curFront, lastFront, false);
            behindCloseTotalDistance += countAwayOrCloseDistance(curBehind, lastBehind, false);
        }
        // 把6个放在result里面
        result.setFrontObsSwitchCount(frontObsSwitchTotalCount);
        result.setBehindObsSwitchCount(behindObsSwitchTotalCount);
        result.setFrontAwayTotalDistance(frontAwayTotalDistance);
        result.setFrontCloseTotalDistance(frontCloseTotalDistance);
        result.setBehindObsAwayTotalDistance(behindObsAwayTotalDistance);
        result.setBehindCloseTotalDistance(behindCloseTotalDistance);
        Map<String, Object> params = new HashMap<>();
        params.put("frontAwayTotalDistance", frontAwayTotalDistance);
        params.put("frontCloseTotalDistance", frontCloseTotalDistance);
        params.put("behindObsAwayTotalDistance", behindObsAwayTotalDistance);
        params.put("behindCloseTotalDistance", behindCloseTotalDistance);
        params.put("frontObsSwitchCount", frontObsSwitchTotalCount);
        params.put("behindObsSwitchCount", behindObsSwitchTotalCount);
        params.put("context", contextDO);
        log.info(
                "frontAwayTotalDistance:{},frontCloseTotalDistance:{},behindObsAwayTotalDistance:{},behindCloseTotalDistance:{}",
                frontAwayTotalDistance, frontCloseTotalDistance, behindObsAwayTotalDistance, behindCloseTotalDistance);
        ISCheckCategoryEnum isCheckCategoryEnum = switchObstacleCheckConfig.aimCategory(contextDO, params);
        result.setSwitchObstacleCategory(isCheckCategoryEnum);
        if (isCheckCategoryEnum == null) {
            return false;
        }
        return true;

    }

    private double countAwayOrCloseDistance(List<VehicleObstacleInfoDTO> curFront, List<VehicleObstacleInfoDTO> lastFront,
            boolean away) {
        if (CollectionUtils.isEmpty(curFront) && CollectionUtils.isEmpty(lastFront)) {
            // 都为空，不存在远离或者靠近
            return 0D;
        }
        Map<String, VehicleObstacleInfoDTO> curFrontMap = curFront.stream()
                .collect(Collectors.toMap(VehicleObstacleInfoDTO::getObstacleId, Function.identity(), (v1, v2) -> v1));
        Map<String, VehicleObstacleInfoDTO> lastFrontMap = lastFront.stream()
                .collect(Collectors.toMap(VehicleObstacleInfoDTO::getObstacleId, Function.identity(), (v1, v2) -> v1));
        // 远离，就是cur > last 了
        AtomicReference<Double> maxDistance = new AtomicReference<>(0D);
        curFrontMap.forEach((curId, curObs) -> {
            if (!lastFrontMap.containsKey(curId)) {
                // 不存在的不考虑
                return;
            }
            VehicleObstacleInfoDTO lastObs = lastFrontMap.get(curId);
            if (lastObs.getDistance() == null || curObs.getDistance() == null) {
                // 不合法时不考虑
                return;
            }
            double distance = curObs.getDistance() - lastObs.getDistance();
            if (away && distance > 0 && distance > maxDistance.get()) {
                // 是远离, 取更远的
                maxDistance.set(distance);
            } else if (!away && distance < 0 && Math.abs(distance) > maxDistance.get()) {
                // 是靠近，取更近的
                maxDistance.set(Math.abs(distance));
            }
        });
        return maxDistance.get();
    }

    /**
     * 判断前方障碍物是否发生切换
     *
     * @param curList
     * @param lastList
     * @return
     */
    private Integer isAheadOrBehindSwitch(List<VehicleObstacleInfoDTO> curList, List<VehicleObstacleInfoDTO> lastList) {
        if (CollectionUtils.isEmpty(curList) && CollectionUtils.isEmpty(lastList)) {
            // 都为空不需要看
            return 0;
        }
        // 总交换的个数
        int switchCount = 0;
        Map<String, VehicleObstacleInfoDTO> curFrontMap = curList.stream()
                .collect(Collectors.toMap(VehicleObstacleInfoDTO::getObstacleId, Function.identity(), (v1, v2) -> v1));
        Map<String, VehicleObstacleInfoDTO> lastFrontMap = lastList.stream()
                .collect(Collectors.toMap(VehicleObstacleInfoDTO::getObstacleId, Function.identity(), (v1, v2) -> v1));
        // 本次里面 ，前次没有的，
        for (VehicleObstacleInfoDTO obstacleInfoDTO : curList) {
            String obstacleId = obstacleInfoDTO.getObstacleId();
            if (!lastFrontMap.containsKey(obstacleId)) {
                switchCount++;
            }
        }
        // 前次里面，本次没有的
        for (VehicleObstacleInfoDTO obstacleInfoDTO : lastList) {
            String obstacleId = obstacleInfoDTO.getObstacleId();
            if (!curFrontMap.containsKey(obstacleId)) {
                switchCount++;
            }
        }
        return switchCount;
    }

    /**
     * 是否压线排队
     *
     * @return
     */
    private boolean isWaitCrossLine(VehicleRuntimeInfoContextDO runtimeInfoContextDO, ISWaitingInQueueResult result,
            CrossLineWaitCheckConfig crossLineWaitCheckConfig) {
        if (crossLineWaitCheckConfig == null || !crossLineWaitCheckConfig.isEnable() ||  runtimeInfoContextDO.getDistanceToNearCurb() == null) {
            return false;
        }
        Double distanceToNearCurb = runtimeInfoContextDO.getDistanceToNearCurb();
        // 先配置默认值
        boolean isCrossLine = crossLineWaitCheckConfig.isCrossLine(runtimeInfoContextDO.getVehicleType(), distanceToNearCurb);
        result.setCrossLine(isCrossLine);
        return isCrossLine;
    }

    /**
     * * 自车排队的传递性判定，当发生连续排队，寻找前继 * 是否需要考虑中间隔几个车的情况？暂时不需要
     *
     * @param curLane
     * @return
     */
    private Boolean isFirstAutoCarInRisk(ISWaitingInQueueResult result,// next车道
            List<HdMapElementGeoDO> curLane, List<HdMapElementGeoDO> nextLaneList,
            ExtendObstacleCheckConfig extendObstacleCheckConfig, // 配置
            ISCheckActionContext actionContext
    ) {
        // 位置
        VehicleRuntimeInfoContextDO contextDO = actionContext.getVehicleRunTimeContext();
        PositionDO curPosition = contextDO.getVehiclePosition();
        PositionDO prePosition = Optional.ofNullable(contextDO.getPreVehiclePosition())
                .map(VehicleInQueuePositionDTO::getPosition).orElse(null);
        VehicleObstacleInfoDTO aheadObstacle = contextDO.getFrontObstacle();
        if (extendObstacleCheckConfig == null || aheadObstacle == null || !extendObstacleCheckConfig.isEnable()) {
            return false;
        }
        if (curPosition == null || prePosition == null) {
            log.warn("curPosition or prePosition is null");
            return false;

        }
        // 判断前方是否是自车，保留
        Boolean frontIsAutoCar = extendObstacleCheckConfig.isAutoCar(aheadObstacle.getFineType());
        if (!frontIsAutoCar) {
            log.warn("frontIsAutoCar is false");
            // 非自车
            return false;
        }
        // 如果是连续的，之间的距离不超过10m，车身长 + 间隔 + 车身长
        Double autoCarMinInQueueDistance = extendObstacleCheckConfig.getSearchDistance();
        // 确定前方是自车,接下来找最近的头车
        // 查询Xm范围内的自车，落在当前车辆的前方（自车车道或者后继车道），且距离最远的，
        List<VehicleRuntimeLocationDO> vehicleInNextLaneNoneInQueue = vehicleRuntimeInfoLocationRepository
                .queryByParam(VehicleRuntimeLocationDOQueryParamDTO.builder().locationQuery(GeoQueryDO.builder()
                        // 坐标
                        .point(curPosition.toPoint())
                        // 距离
                        .distance(autoCarMinInQueueDistance).build()).build())
                .stream()
                // 跨三个lane的咋办？，先不考虑
                .filter(vehicleRuntimeLocationDO ->
                // 非本车
                !Objects.equals(vehicleRuntimeLocationDO.getVin(), actionContext.getVehicleRunTimeContext().getVin())
                        // 在本车道或者下一个车道
                        && (inAnyLine(curLane, vehicleRuntimeLocationDO.getLocation())
                                || inAnyLine(nextLaneList, vehicleRuntimeLocationDO.getLocation())))
                // 过滤反向的
                .filter(vehicleRuntimeLocationDO -> {
                    // 夹角： 【pre、cur】 和 【cur、障碍物】
                    Double angle = GeoToolsUtil.angleCalc(prePosition, curPosition, curPosition,
                            vehicleRuntimeLocationDO.getLocation());
                    return angle != null && angle < 90;
                })
                // 按照距离进行排序，正序
                .sorted(Comparator.comparing(vehicleRuntimeLocationDO -> GeoToolsUtil
                        .distance(vehicleRuntimeLocationDO.getLocation(), curPosition)))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleInNextLaneNoneInQueue)) {
            // 不存在
            return false;
        }
        String vin = actionContext.getVehicleRunTimeContext().getVin();
        // 连续车辆
        List<VehicleRuntimeLocationDO> vehicleInNextLane = filterOneByOne(vin, curPosition,
                vehicleInNextLaneNoneInQueue, extendObstacleCheckConfig.getDistance());
        if (CollectionUtils.isEmpty(vehicleInNextLane)) {
            // 不存在
            return false;
        }
        List<String> vinList = vehicleInNextLane.stream().map(VehicleRuntimeLocationDO::getVin)
                .collect(Collectors.toList());
        // 如果存在，找距离最远的一个，近似认为是排队的第一个（如果社会车插入怎么办，先不考虑）
        VehicleRuntimeLocationDO maxDistanceVehicle = vehicleInNextLane.stream()
                // 按照距离，倒序排序
                .sorted(Comparator.comparing((VehicleRuntimeLocationDO vehicleRuntimeLocationDO) -> GeoToolsUtil
                        .distance(vehicleRuntimeLocationDO.getLocation(), curPosition)).reversed())
                .filter(vehicleRuntimeLocationDO -> {
                    VehicleRuntimeInfoContextDO aheadVehicle = runtimeInfoContextRepository
                            .getByVin(vehicleRuntimeLocationDO.getVin());
                    // 车依然停着
                    return aheadVehicle != null && aheadVehicle.getSpeed() != null && aheadVehicle.getSpeed() < 1D;
                }).peek(vehicleRuntimeLocationDO -> {
                    vinList.add(vehicleRuntimeLocationDO.getVin());
                }).collect(Collectors.toList()).stream().findFirst().orElse(null);
        // 设置返回值
        result.setAutoCarInQueueList(vinList);
        if (maxDistanceVehicle == null) {
            // 找不到 或者车走了
            return false;
        }
        // 找到了，看下这个车的风险判定是否正常
        // 找这个车【最近】的一次的判定结果，如果有风险，则认为是风险
        // 最近范围： 1分钟
        Date endTime = new Date();
        Date startTime = DatetimeUtil.getNSecondsBeforeDateTime(endTime, DateTimeConstant.ONE_MINUTE_SECOND);

        ActionChainResultLogDO lastLog = actionChainResultLogRepository
                .queryByParam(LogActionChainResultQueryParamDTO.builder()
                        // 版本
                        .markVersion(actionContext.getMarkVersion())
                        // 车辆
                        .vin(maxDistanceVehicle.getVin())
                        // 场景
                        .scene(actionContext.getCheckScene())
                        // actionName
                        .actionName(actionContext.getCurrentActionName())
                        // 一分钟
                        .createTimeRange(TimePeriod.builder().beginDate(startTime).endDate(endTime).build())
                        // 时间倒序
                        .orderByCreateTime(OrderEnum.DESC)
                        .build())
                .stream()
                // 取第一个
                .findFirst()
                // 找不到留空
                .orElse(null);
        ISCheckActionResult<ISWaitingInQueueResult> firstResult = Optional.ofNullable(lastLog)
                .map(ActionChainResultLogDO::getResultDetail)
                .map(actionResult -> JacksonUtils.from(actionResult,
                        new TypeReference<ISCheckActionResult<ISWaitingInQueueResult>>() {}))
                .orElse(null);
        if (firstResult == null) {
            return false;
        }
        // 找类型
        ISCheckCategoryEnum firstCarCategory = Optional.of(firstResult)
                .map(ISCheckActionResult::getCategoryEnum).orElse(null);
        // 传递
        result.setFirstAutoCarInRisk(firstCarCategory);
        if (firstCarCategory != null
                && extendObstacleCheckConfig.getCheckCategory().contains(firstCarCategory.name())) {
            // 只有特殊类型的才会传递： 路口停滞、路中央停滞
            result.setFirstAutoCarInRisk(firstCarCategory);
            return true;
        }
        return false;

    }

    private boolean inAnyLine(List<HdMapElementGeoDO> nextLaneList, PositionDO curPosition) {
        if (CollectionUtils.isEmpty(nextLaneList)) {
            return false;
        }
        return nextLaneList.stream().anyMatch(nextLane -> nextLane.isInPolygon(curPosition));
    }

    /**
     * 获取一个接一个排队的
     * 
     * @param vehicleInNextLaneNoneInQueue
     * @param distance
     * @return
     */
    private List<VehicleRuntimeLocationDO> filterOneByOne(String vin, PositionDO vehicleCurLocation,
            List<VehicleRuntimeLocationDO> vehicleInNextLaneNoneInQueue, Double distance) {
        if (CollectionUtils.isEmpty(vehicleInNextLaneNoneInQueue) || distance == null) {
            return new ArrayList<>();
        }
        if (vehicleInNextLaneNoneInQueue.size() == 1) {
            return vehicleInNextLaneNoneInQueue;
        }
        Map<String, VehicleRuntimeLocationDO> vin2Vehicle = new HashMap<>();
        // 距离正序的车辆
        List<Pair<PositionDO, VehicleRuntimeLocationDO>> pairList = vehicleInNextLaneNoneInQueue.stream()
                // 保存
                .peek(vehicleRuntimeLocationDO -> vin2Vehicle.put(vehicleRuntimeLocationDO.getVin(),
                        vehicleRuntimeLocationDO))
                .map(vehicleRuntimeLocationDO -> new Pair<>(vehicleRuntimeLocationDO.getLocation(),
                        vehicleRuntimeLocationDO))
                .collect(Collectors.toList());
        // 将自车当前坐标算作第一个
        pairList.add(0, new Pair<>(vehicleCurLocation,
                VehicleRuntimeLocationDO.builder().vin(vin).location(vehicleCurLocation).build()));
        List<VehicleRuntimeLocationDO> vehicleRuntimeLocationDOS = new ArrayList<>();
        for (int i = 0; i < vehicleInNextLaneNoneInQueue.size() - 1; i++) {
            Pair<PositionDO, VehicleRuntimeLocationDO> currentPair = pairList.get(i);
            Pair<PositionDO, VehicleRuntimeLocationDO> nextPair = pairList.get(i + 1);
            PositionDO currentLocation = currentPair.getKey();
            VehicleRuntimeLocationDO nextVehicle = nextPair.getValue();
            PositionDO nextLocation = nextPair.getKey();
            // 计算 连续两个车的距离连续性
            Double carDistance = GeoToolsUtil.distance(currentLocation, nextLocation);
            if (carDistance <= distance && vin2Vehicle.containsKey(nextVehicle.getVin())) {
                // 如果满足，则添加进来
                vehicleRuntimeLocationDOS.add(nextVehicle);
            } else {
                // 不满足，连续性中断
                break;
            }
        }
        return vehicleRuntimeLocationDOS;
    }

    /**
     * 判断车辆是否在混合或者非机动车道内
     *
     * @param waitInQueueConfig
     * @return
     */
    private Boolean isInMixedBikingLane(VehicleRuntimeInfoContextDO runtimeContext,
            List<HdMapElementGeoDO> vehicleCurLaneList,
            WaitInQueueConfigDTO waitInQueueConfig) {
        if (CollectionUtils.isEmpty(vehicleCurLaneList)) {
            return false;
        }
        if (Objects.equals(runtimeContext.getDistanceToJunction(), NumberUtils.DOUBLE_MINUS_ONE)
                || runtimeContext.getDistanceToJunction() >= waitInQueueConfig.getThresholdOpenBikingLaneChecking()) {
            // 任意当前车道，满足敏感车道要求
            return vehicleCurLaneList.stream().anyMatch((vehicleCurLane) -> {
                String laneType = vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.LANE_TYPE);
                return waitInQueueConfig.getSensitiveLaneTypeList().contains(laneType);
            });
        } else {
            return false;
        }
    }

    /**
     * 检索出多转向车道需要的次数
     * @param vehicleCurLane
     * @return
     */
    private Integer countMultiTurn(HdMapElementGeoDO vehicleCurLane, Integer maxCount) {
        if (vehicleCurLane == null || maxCount == null) {
            return -1;
        }
        int count = 0;
        // 找到一个允许双后继车道的次数
        boolean foundMultiTurnLane = false;
        do {
            // 算第一次
            count++;
            List<String> successorList = vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.SUCCESSOR);
            if (CollectionUtils.isEmpty(successorList)) {
                // 如果找不到
                break;
            }
            // 如果可以找到
            if (successorList.size() > 1) {
                // 有多个后继,跳出
                foundMultiTurnLane = true;
                break;
            } else {
                // 没多个后继
                // 往后找
                vehicleCurLane = hdMapAdapter.getLaneById(successorList.get(0));
            }
        } while (vehicleCurLane != null && count < maxCount);
        if (foundMultiTurnLane) {
            // 如果找到了，返回次数
            return count;
        }
        // 找不到就是-1
        return -1;
    }

}
