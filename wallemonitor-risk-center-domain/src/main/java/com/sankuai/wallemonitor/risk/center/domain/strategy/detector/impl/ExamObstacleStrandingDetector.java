package com.sankuai.wallemonitor.risk.center.domain.strategy.detector.impl;

import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetector;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskExamObstacleStrandingRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 考试障碍物停滞事件检测器
 */
@Slf4j
@Service
public class ExamObstacleStrandingDetector extends RiskDetector<RiskExamObstacleStrandingRecordDO> {

    @Override
    public RiskCaseTypeEnum getDetectRiskType() {
        return RiskCaseTypeEnum.EXAM_OBSTACLE_STRANDING;
    }
}