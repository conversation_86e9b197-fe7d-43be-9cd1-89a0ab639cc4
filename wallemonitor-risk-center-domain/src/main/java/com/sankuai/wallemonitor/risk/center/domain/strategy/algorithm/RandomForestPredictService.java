package com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm;

import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RandomForestTrainService.ModelEvaluationResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RandomForestPredictConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import weka.classifiers.Evaluation;
import weka.classifiers.trees.RandomForest;
import weka.core.DenseInstance;
import weka.core.Instance;
import weka.core.Instances;
import weka.core.SerializationHelper;

/**
 * 随机森林算法预测服务
 */
@Slf4j
@Component
public class RandomForestPredictService {

    /**
     * 随机森林模型
     */
    private RandomForest model;
    private Instances dataStructure;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RANDOM_FOREST_PREDICT_CONFIG, defaultValue = "{}")
    private RandomForestPredictConfigDTO predictConfigDTO;

    /**
     * 构造函数
     */
    public RandomForestPredictService() {
    }

    @PostConstruct
    public void init() {
        CheckUtil.isNotNull(predictConfigDTO, "RandomForestPredictConfigDTO is null");
        String modelPath = predictConfigDTO.getModelPath();
        String modelDataPath = predictConfigDTO.getModelStructurePath();

        // 加载模型
        loadModel(modelPath, modelDataPath);
    }

    /**
     * 预测风险
     *
     * @param data
     * @return
     * @throws Exception
     */
    public RandomForestPredictionResultDTO predict(RiskFeatureDataDTO data) {

        try {
            Instance instance = createInstance(data);
            // 获取预测概率分布
            double[] probabilityDistribution = model.distributionForInstance(instance);

            return new RandomForestPredictionResultDTO(
                    probabilityDistribution[1] >= predictConfigDTO.getThreshold(),  // isRisky
                    probabilityDistribution[1],// riskProbability
                    null
            );
        } catch (Exception e) {
            log.error("RandomForestTrainService, predict", e);
        }
        return null;
    }

    /**
     * 评估模型性能
     *
     * @param testData 测试数据集
     * @return ModelEvaluationResult 评估结果
     * @throws Exception
     */
    public ModelEvaluationResultDTO evaluate(List<RiskFeatureDataDTO> testData) throws Exception {
        if (CollectionUtils.isEmpty(testData)) {
            log.error("RandomForestTrainService, evaluate: test data is empty");
            return null;
        }

        // 准备测试数据
        Instances testInstances = new Instances(dataStructure);
        for (RiskFeatureDataDTO data : testData) {
            Instance instance = createInstance(data);
            testInstances.add(instance);
        }

        // 创建评估器
        Evaluation eval = new Evaluation(dataStructure);

        // 评估模型
        eval.evaluateModel(model, testInstances);

        // 构建评估结果
        return ModelEvaluationResultDTO.builder()
                .accuracy(eval.pctCorrect() / 100.0)
                .precision(eval.precision(1))
                .recall(eval.recall(1))
                .f1Score(eval.fMeasure(1))
                .confusionMatrix(eval.confusionMatrix())
                .build();
    }


    /**
     * 从文件或URL加载模型
     *
     * @param modelPath         模型文件路径或URL
     * @param dataStructurePath 数据结构文件路径或URL
     * @throws Exception IO异常或反序列化异常
     */
    private void loadModel(String modelPath, String dataStructurePath) {
        try {
            // 判断是否为URL
            if (isValidUrl(modelPath) && isValidUrl(dataStructurePath)) {
                // 从URL加载
                model = (RandomForest) SerializationHelper.read(new URL(modelPath).openStream());
                dataStructure = (Instances) SerializationHelper.read(new URL(dataStructurePath).openStream());
            } else {
                // 从本地文件加载
                model = (RandomForest) SerializationHelper.read(modelPath);
                dataStructure = (Instances) SerializationHelper.read(dataStructurePath);
            }
            log.info("随机森林算法模型加载成功，路径: {}, {}", modelPath, dataStructurePath);
        } catch (Exception e) {
            log.error("随机森林算法模型加载失败", e);
        }
    }

    /**
     * 验证字符串是否为合法URL
     */
    private boolean isValidUrl(String urlString) {
        try {
            new URL(urlString);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }


    /**
     * 创建实例
     *
     * @param data
     * @return
     */
    private Instance createInstance(RiskFeatureDataDTO data) {
        Instance instance = new DenseInstance(9);
        instance.setDataset(dataStructure);

        instance.setValue(0, data.getLongitude());
        instance.setValue(1, data.getLatitude());
        instance.setValue(2, data.getDistanceToNextJunction());
        instance.setValue(3, data.getTrafficLightType());
        instance.setValue(4, data.getObstacleDistance());
        instance.setValue(5, data.getObstacleAngle());
        instance.setValue(6, String.valueOf(data.getLaneType()));
        instance.setValue(7, String.valueOf(data.getLanePosition()));
        if (data.getRisk() != null) {
            instance.setValue(8, String.valueOf(data.getRisk()));
        }
        return instance;
    }
}
