package com.sankuai.wallemonitor.risk.center.domain.process;


import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.domain.enums.MessageType;
import com.sankuai.walleeve.domain.message.dto.CloudTriageEventMessageDTO;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.HighNegativeCallSafetyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallSafetyEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.CloudEventTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 处理舆情风险的停滞信息，满足要求时，触发云安全
 */
@Slf4j
@Component
public class RiskCaseRelationCounterCallSafetyProcess implements DomainEventProcess {

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @MessageProducer(topic = MessageTopicEnum.CLOUD_TRIAGE_EVENT_MESSAGE)
    private CommonMessageProducer<CloudTriageEventMessageDTO> cloudTriageEventProducer;

    @ConfigValue(LionKeyConstant.SEND_CLOUD_TRIAGE_GRAY_CONFIG)
    private Map<Integer, Set<String>> carPurposeGrayConfig;

    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.RISK_TRIGGER_SAFETY_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (!RiskCaseVehicleRelationDO.class.isAssignableFrom(eventDTO.getDomainClass())) {
            return true;
        }
        // 获取停滞发生变更的数据
        DomainEventChangeDTO<RiskCaseVehicleRelationDO> typedDomainEvent = (DomainEventChangeDTO<RiskCaseVehicleRelationDO>) eventDTO;
        Map<RiskCaseTypeEnum, HighNegativeCallSafetyConfigDTO> riskCallSafetyConfigDTOMap = lionConfigRepository.getRiskCallSafetyConfig();
        if (MapUtils.isEmpty(riskCallSafetyConfigDTOMap)) {
            return true;
        }
        //发生过变更
        List<RiskCaseVehicleRelationDO> stagnationCounterChanged = typedDomainEvent.getBySingleField(
                //停滞信息发生变更
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "stagnationCounter")
                        //不是变更成空
                        && StringUtils.isNotBlank(entityFieldChangeRecordDTO.getAfter())
        ).stream().filter(relation -> {
            HighNegativeCallSafetyConfigDTO callSafetyConfigDTO = riskCallSafetyConfigDTOMap.get(relation.getType());
            if (Objects.isNull(callSafetyConfigDTO)) {
                //没配置，不处理
                return false;
            }
            return callSafetyConfigDTO.canCall(relation);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stagnationCounterChanged)) {
            //如果没有停滞信息发生变更的。
            return true;
        }
        //更新风险上的停滞信息
        handleTriggerSafety(new Date(eventDTO.getTimestamp()), stagnationCounterChanged);
        return true;
    }

    /**
     * 记录里面的每一段停滞
     *
     * @param date
     * @param stagnationCounterChanged
     */
    private void handleTriggerSafety(Date date, List<RiskCaseVehicleRelationDO> stagnationCounterChanged) {
        //取这些case对应的do，不过滤已完结的，只要存在，就去更新
        Set<String> eventIdSet = new HashSet<>();
        Map<String, List<RiskCaseVehicleRelationDO>> eventId2Relation = new HashMap<>();
        stagnationCounterChanged.forEach(riskCaseVehicleRelationDO -> {
            eventIdSet.add(riskCaseVehicleRelationDO.getEventId());
            eventId2Relation.computeIfAbsent(riskCaseVehicleRelationDO.getEventId(), k -> new ArrayList<>())
                    .add(riskCaseVehicleRelationDO);
        });

        eventIdSet.forEach(eventId -> {
            List<RiskCaseVehicleRelationDO> relationDOS = eventId2Relation.get(eventId);
            lockUtils.batchLockCanWait(LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(eventId)), () -> {
                //查询case
                RiskCaseDO riskCaseDO = riskCaseRepository.getByEventId(eventId);
                if (Objects.isNull(riskCaseDO) || !CallSafetyEnum.NOT_CALLED.equals(riskCaseDO.getCallSafety())) {
                    //如果不存在或者 已经呼叫/取消呼叫
                    return;
                }
                List<RiskCaseVehicleRelationDO> thisCaseRelation = Optional.ofNullable(eventId2Relation.get(riskCaseDO.getEventId()))
                .orElse(Lists.newArrayList()).stream().filter(riskCaseVehicleRelationDO -> {
                    // 事件类型
                    Integer type = Optional.ofNullable(riskCaseVehicleRelationDO.getType())
                            .map(RiskCaseTypeEnum::getCode).orElse(-1);
                    // 用车目的
                    String curPurpose = riskCaseVehicleRelationDO.getPurpose();
                    return Optional.ofNullable(carPurposeGrayConfig.get(type))
                            // 命中 curPurpose  或者 curPurpose 配置为ALL，拦截, 返回false
                            .map(curPurposeSet -> {
                                boolean isBlocked = curPurposeSet.contains(curPurpose) || curPurposeSet.contains("ALL");
                                if (isBlocked) {
                                    log.info("命中灰度配置，拦截处理 - eventId: {}, vin: {}, type: {}, purpose: {}, grayConfig: {}", 
                                            riskCaseVehicleRelationDO.getEventId(), 
                                            riskCaseVehicleRelationDO.getVin(),
                                            type, 
                                            curPurpose, 
                                            curPurposeSet);
                                }
                                return !isBlocked;
                            })
                            // type未找到配置，不拦截
                            .orElse(true);
                }).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(relationDOS)) {
                    return;
                }
                CloudEventTypeEnum cloudEventTypeEnum = CloudEventTypeEnum.getByRiskCaseType(riskCaseDO.getType());
                if (Objects.isNull(cloudEventTypeEnum)) {
                    return;
                }

                if (CollectionUtils.isEmpty(thisCaseRelation)) {
                    // 所有的case均命中灰度逻辑或者thisCaseRelation 查询结果本身为空，返回
                    return;
                }

                //发送消息
                thisCaseRelation
                        .forEach(relation -> {
                            List<VehicleCounterInfoDO> counterList = relation.getStagnationCounter();
                            //去最后一个停滞的开始时间，如果没有则使用occurTime,这里一定会有
                            Date stagnationStartTime = Optional.ofNullable(counterList)
                                    .map(list -> list.get(counterList.size() - 1))
                                    .map(VehicleCounterInfoDO::getStartTime).orElse(
                                            riskCaseDO.getOccurTime());

                            String msgId = cloudTriageEventProducer.sendMqCommonMessage(
                                    CloudTriageEventMessageDTO.builder()
                                            .eventId(relation.getEventId())
                                            .vin(relation.getVin())
                                            //设置为停滞的开始时间
                                            .eventTime(stagnationStartTime)
                                            .reporter(RiskCaseSourceEnum.BEACON_TOWER.getDesc())
                                            .eventType(cloudEventTypeEnum.getCode())
                                            .build(), MessageType.CLOUD_TRIAGE_EVENT);
                            //确保消息可以呼叫出去
                            CheckUtil.isNotBlank(msgId, "发送消息失败");
                            relation.callSafety(date);
                        });
                //更新呼叫消息
                riskCaseDO.setCallSafety(CallSafetyEnum.CALLED);
                riskCaseRepository.save(riskCaseDO);
                riskCaseVehicleRelationRepository.batchSave(thisCaseRelation);
            });
        });
        //批量加锁

    }

}
