package com.sankuai.wallemonitor.risk.center.domain.strategy.action;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


/**
 * 停滞不当预检action上下文
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Slf4j
public class ISCheckActionContext {

    /**
     * 唯一ID
     */
    private String id;

    /**
     * 预检项原始数据
     */
    private RiskCheckingQueueItemDO item;
    
    /**
     * 上一次处理的actionName
     */
    private String lastCheckActionName;

    /**
     * 当前的action
     */
    private String currentActionName;

    /**
     * 上一次处理的actionResult
     */
    private ISCheckActionResult lastCheckResult;

    /**
     * 车辆实时信息
     */
    private VehicleRuntimeInfoContextDO vehicleRunTimeContext;


    /**
     * 历史执行结果
     */
    @Builder.Default
    private List<ISCheckActionResult> checkResultHistory = new ArrayList<>();

    /**
     * 是否要继续往后检查
     */
    private Boolean continueCheck = true;

    /**
     * 检查场景
     */
    private String checkScene;

    /**
     * 版本
     */
    private String markVersion;

    /**
     * 解析
     */
    private Map<String, Map<String, Object>> actionConfigMap;

    /**
     * 更新上次执行结果
     *
     * @param lastCheckActionResult
     * @param lastCheckActionName
     */
    public void updateAction(ISCheckActionResult lastCheckActionResult, String lastCheckActionName) {
        if (lastCheckActionResult == null) {
            //上次无结果
            return;
        }
        if (checkResultHistory == null) {
            checkResultHistory = new ArrayList<>();
        }
        if (this.lastCheckResult != null) {
            checkResultHistory.add(this.lastCheckResult);
        }
        //设置caseId 和 基础信息
        this.lastCheckActionName = lastCheckActionName;
        this.lastCheckResult = lastCheckActionResult;
        //TODO 放在这里是否合适
        this.lastCheckResult.setCaseId(this.item.getTmpCaseId());
    }

    /**
     * 获取action的配置
     * 
     * @return
     * @param <T>
     */
    public <T> T getCurrentActionConfig(Class<T> tClass) {
        if (actionConfigMap == null) {
            return null;
        }
        Map<String, Object> thisActionConfig = actionConfigMap.get(this.currentActionName);
        if (thisActionConfig == null) {
            return null;
        }
        try {
            return JacksonUtils.from(JacksonUtils.to(thisActionConfig), tClass);
        } catch (Exception e) {
            log.error("解析action配置失败,actionName:{},actionConfig:{}", this.currentActionName, thisActionConfig, e);
            return null;
        }
    }

    /**
     * 获取actionResult
     *
     * @param actionName
     * @return
     * @param <T>
     */
    public <T> ISCheckActionResult<T> getActionResult(String actionName) {
        if (StringUtils.isBlank(actionName)) {
            return null;
        }
        if (lastCheckResult != null && (lastCheckResult.getActionName().equals(actionName)
                || lastCheckResult.getActionName().startsWith(actionName))) {
            // 上一次的结果还没加入进去，还在Last
            return lastCheckResult;
        }
        if (checkResultHistory == null) {
            return null;
        }
        return checkResultHistory.stream().filter(
                result -> actionName.equals(result.getActionName()) || result.getActionName().startsWith(actionName))
                .findFirst().orElse(null);
    }
}
