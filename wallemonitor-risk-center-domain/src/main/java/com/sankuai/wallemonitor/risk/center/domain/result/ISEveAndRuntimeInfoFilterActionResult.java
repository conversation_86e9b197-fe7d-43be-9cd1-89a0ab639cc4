package com.sankuai.wallemonitor.risk.center.domain.result;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ISEveAndRuntimeInfoFilterActionResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否过滤
     */
    private Boolean shouldFilter;

    /**
     * 过滤原因
     */
    private String filterReason;

}
