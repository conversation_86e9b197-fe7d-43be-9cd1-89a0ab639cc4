package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.infra.model.core.UserNoticeReadRecordDO;

public interface UserNoticeAdminService {

    void insertUserNoticeReadRecord(UserNoticeReadRecordDO userNoticeReadRecordDO);

    Long queryLatestNoticeVersionByUserId(String userId);

    void queryThenUpdateUserNotice(String userId, Long versionId);
}
