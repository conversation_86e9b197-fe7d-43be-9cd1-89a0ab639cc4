package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.sankuai.wallemonitor.risk.center.domain.process.DomainEventProcess;
import com.sankuai.wallemonitor.risk.center.domain.service.DomainEventService;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventProcessResultDO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.DomainEventProcessRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.DomainEventConfig;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.SpringUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 领域事件处理类
 */
@Component
@Slf4j
public class DomainEventServiceImpl implements DomainEventService {

    /**
     * 领域处理器
     */
    @Resource
    private DomainEventProcessProvider eventProcessAdapter;



    @Resource
    private DomainEventProcessRecordRepository eventProcessRecordRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    /**
     * 领域事件处理 处理的领域实体
     *
     * @param eventDTO
     * @param async
     * @return
     */
    @Override
    public DomainEventResultDTO process(DomainEventChangeDTO<?> eventDTO, Boolean async) {
        //处理领域事件
        CheckUtil.isNotNull(eventDTO, "领域事件为空");
        DomainEventConfig domainEventConfig = lionConfigRepository.getDomainEventConfig();
        //1、找对应的领域处理器
        if (domainEventConfig == null) {
            log.warn("领域未配置对应的处理器,兜底返回");
            return DomainEventResultDTO.builder().build();
        }
        List<String> processNameList = domainEventConfig.getProcessName(eventDTO.getEntry(), async);
        if (CollectionUtils.isEmpty(processNameList)) {
            log.info("领域变更入口{}无对应的处理器", eventDTO.getEntry());
            return DomainEventResultDTO.builder().build();
        }
        //2.获取后处理器，并过滤掉本入口的
        List<DomainEventProcess> domainEventProcessList = eventProcessAdapter.getInstanceByProcessName(processNameList,
                eventDTO.getEntry().getOperateEntry());
        processNameList = domainEventProcessList.stream().map(DomainEventProcess::getProcessName)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processNameList)) {
            //如果过滤本入口后，又为空了
            log.info("领域变更入口过滤后{}无对应的处理器", eventDTO.getEntry());
            return DomainEventResultDTO.builder().build();
        }
        if (CollectionUtils.isEmpty(processNameList)) {
            log.warn("processNameList:{},找不到处理器实例", processNameList);
            return DomainEventResultDTO.builder().build();
        }
        //3、找到本次事件执行器的执行记录，看下是否可以继续执行
        Map<String, DomainEventProcessResultDO> processResultMap = eventProcessRecordRepository.getProcessResult(
                        processNameList, eventDTO.getEntry(), eventDTO.getTimestamp(), eventDTO.getTraceId())
                .stream()
                .collect(Collectors.toMap(DomainEventProcessResultDO::getProcessName, p -> p));
        //4、根据重试的次数，获取结果
        domainEventProcessList = domainEventProcessList.stream().filter(process -> {
            DomainEventProcessResultDO processResultDO = processResultMap.get(process.getProcessName());
            //如果不存在执行记录 或者 失败且小于最大重试次数，才能执行
            return processResultDO == null || !processResultDO.getProcessResult() && domainEventConfig.canRetry(
                    process.getProcessName(), processResultDO.getRetriedTime());
        }).collect(Collectors.toList());
        //5、获取处理结果
        Map<String, Boolean> domainEventResultMap = new HashMap<>();
        //6、批量执行
        domainEventProcessList
                .forEach(domainEventProcess -> {
                    domainEventResultMap.put(domainEventProcess.getProcessName(),
                            handleProcess(domainEventProcess, eventDTO));
                });
        return handleProcessResult(domainEventConfig, domainEventResultMap, processResultMap, eventDTO);


    }

    /**
     * @param domainEventResultMap
     * @param processResultMap
     * @return
     */
    private DomainEventResultDTO handleProcessResult(DomainEventConfig domainEventConfig,
            Map<String, Boolean> domainEventResultMap,
            Map<String, DomainEventProcessResultDO> processResultMap, DomainEventDTO eventDTO) {
        if (MapUtils.isEmpty(domainEventResultMap)) {
            return DomainEventResultDTO.builder().build();
        }
        Map<String, DomainEventProcessResultDO> resultDOMap = new HashMap<>();
        domainEventResultMap.forEach((processName, result) -> {
            DomainEventProcessResultDO resultDO = processResultMap.computeIfAbsent(processName,
                    key -> DomainEventProcessResultDO.builder()
                            .processResult(result)
                            .eventTime(eventDTO.getTimestamp())
                            .traceId(eventDTO.getTraceId())
                            .entry(eventDTO.getEntry())
                            .retriedTime(0)
                            .processName(processName)
                            .build());
            resultDO.process(result);
            //设置是否可以重试
            resultDO.setCanRetry(domainEventConfig.canRetry(
                    processName, resultDO.getRetriedTime()));
            //有变更的放进去
            resultDOMap.put(processName, resultDO);
        });
        //保存
        handleSaveProcessResult(resultDOMap);
        //返回结果
        return DomainEventResultDTO.builder().processResult(new ArrayList<>(resultDOMap.values()))
                .build();
    }

    /**
     * 保存执行器的执行记录
     *
     * @param resultDOMap
     */
    private void handleSaveProcessResult(Map<String, DomainEventProcessResultDO> resultDOMap) {
        if (MapUtils.isEmpty(resultDOMap)) {
            return;
        }
        //保存
        eventProcessRecordRepository.batchSave(new ArrayList<>(resultDOMap.values()));
    }

    /**
     * 处理领域结果
     *
     * @param domainEventProcess
     * @param eventDTO
     * @return
     */
    private boolean handleProcess(DomainEventProcess domainEventProcess, DomainEventChangeDTO eventDTO) {
        long startTime = System.currentTimeMillis();
        boolean processResult = false;
        try {
            //执行结果
            processResult = domainEventProcess.process(eventDTO);
            log.info("[traceId:{}][processName:{}][entry:{}][domainClass:{}][result:{}][cost:{}]"
                    , eventDTO.getTraceId(), domainEventProcess.getProcessName(),
                    eventDTO.getEntry().getOperateEntry(),
                    eventDTO.getEntry().getDomainClassName(),
                    processResult, System.currentTimeMillis() - startTime
            );
        } catch (Exception e) {
            log.warn("领域事件处理异常", e);
            log.info("[traceId:{}][processName:{}][entry:{}][domainClass:{}][result:{}][cost:{}]"
                    , eventDTO.getTraceId(), domainEventProcess.getProcessName(),
                    eventDTO.getEntry().getOperateEntry(),
                    eventDTO.getEntry().getDomainClassName(),
                    false, System.currentTimeMillis() - startTime
            );
        }
        return processResult;
    }

    @Slf4j
    @Component
    public static class DomainEventProcessProvider implements ApplicationRunner {

        /**
         * 注入process类
         */
        private static final Map<String, DomainEventProcess> domainEventProcessesMap = new HashMap<>();

        /**
         * process到OperateEnterActionEnum的映射
         */
        private static final Map<String, OperateEnterActionEnum> processName2entry = new HashMap<>();


        /**
         * 获取领域处理器实例
         *
         * @return
         */
        public List<DomainEventProcess> getInstanceByProcessName(List<String> processNameList,
                OperateEnterActionEnum thisEntry) {
            if (CollectionUtils.isEmpty(processNameList) || MapUtils.isEmpty(domainEventProcessesMap)) {
                return new ArrayList<>();
            }
            return processNameList.stream()
                    //过滤掉
                    .filter(processName -> {
                        OperateEnterActionEnum operateEnterActionEnum = processName2entry.get(processName);
                        if (operateEnterActionEnum == null) {
                            log.error("processName:" + processName,
                                    new SystemException("领域处理器配置异常,process无注解"));
                            return false;
                        }
                        //非本身即可
                        return !operateEnterActionEnum.equals(thisEntry);
                    })
                    .map(domainEventProcessesMap::get)
                    .filter(Objects::nonNull).collect(Collectors.toList());
        }


        @Override
        public void run(ApplicationArguments args) throws Exception {
            List<DomainEventProcess> domainEventProcesses = SpringUtils.getBeanList(DomainEventProcess.class);
            domainEventProcesses.forEach(domainEventProcess -> {
                processName2entry.put(domainEventProcess.getProcessName(), domainEventProcess.getOperateEntry());
                domainEventProcessesMap.put(domainEventProcess.getProcessName(), domainEventProcess);
            });

        }

    }



}
