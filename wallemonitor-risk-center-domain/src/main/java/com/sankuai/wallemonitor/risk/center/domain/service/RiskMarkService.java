package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import java.util.List;

public interface RiskMarkService {

    void updateOneRiskCheckingQueueItem(RiskCheckingQueueItemDO queueItemDO, ISCheckActionResult resultDTO);

    ISCheckActionResult calcRiskMarkResult(RiskCheckingQueueItemDO riskCheckingQueueItemDO, String version);

    /**
     * 批量计算检查结果
     *
     * @return
     */
    void triggerAutoMark(List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList, String version);

    void resetQueueItemForDynamicCheck(RiskCheckingQueueItemDO queueItem, RiskCheckResultDO lastCheckResultDO);

    void sendMarkMessage(RiskCheckingQueueItemDO queueItem, Long checkIntervalSeconds, boolean isDynamic,
            String version);

    void markCaseByCheckItem(RiskCheckingQueueItemDO checkingQueueItemDO, String version);

}
