package com.sankuai.wallemonitor.risk.center.domain.process;

import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.SpringUtils;
import java.lang.reflect.Method;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 领域事件处理器
 *
 * @param <
 */
public interface DomainEventProcess {

    Logger logger = LoggerFactory.getLogger(DomainEventProcess.class);

    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    boolean process(DomainEventChangeDTO<?> eventDTO) throws TException;


    /**
     * 获取处理器的名称
     *
     * @return
     */
    default String getProcessName() {
        return SpringUtils.getBeanName(this);
    }

    default OperateEnterActionEnum getOperateEntry() {
        OperateEnterActionEnum operateEnter = null;
        try {
            Method method = this.getClass().getMethod("process", DomainEventChangeDTO.class);
            if (method.isAnnotationPresent(OperateEnter.class)) {
                //如果存在，放入
                operateEnter = method.getAnnotation(OperateEnter.class).value();
            }
            return operateEnter;
        } catch (Exception e) {
            throw new SystemException("处理器异常，未有OperateEnter注解配置");
        }
    }


}
