package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.google.common.collect.ImmutableMap;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseMessageNotifyParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseMessageNoticeProcess.MessageCreateOrUpdatedDTO;
import com.sankuai.wallemonitor.risk.center.domain.result.RiskCaseMessageNotifyResultDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseMessageService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppPropertiesConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.ConfirmType;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageUpdateEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ThemeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCaseExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO.GroupTemplateDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.PushContentUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.PushContentUtils.ConfirmStyleDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.UrlEncodeUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.VelocityUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxNoticeParamVTO;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 *
 */
@Component
@Slf4j
public class RiskCaseMessageServiceImpl implements RiskCaseMessageService {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;


    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Value(AppPropertiesConstant.CONFIG_VALUE_EVE_RISK_DOMAIN)
    private String EVE_HOST;

    private static final String OPEN_CASE_URL = "/fe-panel-risk/index.html#/risk/caseList?openCaseId=%s";

    private static final String MANUAL_CALL_MRM_PATH = "/risk/api/admin/manualMrmCall";

    // monitor 备份视频的api
    private static final String MONITOR_BACKUP_VIDEO_API = "%s?playType=backup&vin=%s&backupTime=%s";

    // monitor 备份视频详情的api
    private static final String MONITOR_BACKUP_VIDEO_DETAIL_API = "%s?vin=%s&startTime=%s&vehicleName=%s&vehicleId=%s&view=concat";

    // monitor 备份视频的URL地址
    @Value(AppPropertiesConstant.CONFIG_VALUE_MONITOR_BACKUP_VIDEO_URL)
    private String MONITOR_BACKUP_VIDEO_URL = StringUtils.EMPTY;

    // monitor 备份视频详情的URL地址
    @Value(AppPropertiesConstant.CONFIG_VALUE_MONITOR_BACKUP_VIDEO_DETAIL_URL)
    private String MONITOR_BACKUP_VIDEO_DETAIL_URL = StringUtils.EMPTY;

    // monitor 根据事件上报时间，回放时间提前10s
    private static final int MONITOR_PLAY_BACK_TIME_ADVANCE = 10;

    private static final String DURATION_TEXT = "持续时间";

    /**
     * 创建或更新风险事件
     *
     * @param paramDTO
     * @return
     */
    @Override
    public RiskCaseMessageNotifyResultDTO updateMessageVersionAndSendMessage(RiskCaseMessageNotifyParamDTO paramDTO) {
        if (paramDTO == null || CollectionUtils.isEmpty(paramDTO.getMessageCreateOrUpdatedDTOList())) {
            return RiskCaseMessageNotifyResultDTO.builder().build();
        }
        //查询全部的风险事件
        List<String> allCaseId = paramDTO.getMessageCreateOrUpdatedDTOList().stream()
                .map(MessageCreateOrUpdatedDTO::getRiskCaseId).collect(
                        Collectors.toList());
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(allCaseId).build());
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            log.warn("无相关的风险事件");
            return RiskCaseMessageNotifyResultDTO.builder().build();
        }
        //查询车辆关联事件
        List<RiskCaseVehicleRelationDO> vehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(allCaseId).build());
        //list 转 map
        Map<String, RiskCaseDO> caseDOMap = riskCaseDOList.stream()
                .collect(Collectors.toMap(RiskCaseDO::getCaseId, Function.identity(), (o1, o2) -> o1));
        Map<String, List<RiskCaseVehicleRelationDO>> caseId2VehicleRelationMap = vehicleRelationDOList.stream()
                .collect(Collectors.groupingBy(RiskCaseVehicleRelationDO::getCaseId));
        //构建每个风险事件的参数上下文
        Map<String, Map<String, Object>> caseId2ContextMap = buildParamContext(caseDOMap, caseId2VehicleRelationMap);
        Map<String, Map<String, String>> caseId2GroupMessageMap = new HashMap<>();
        Map<RiskCaseTypeEnum, BroadCastStrategyConfigDTO> caseTypeEnumBroadCastStrategyConfigDTOMap = lionConfigRepository.getCaseType2BroadCastStrategyConfig();
        //区分更新和新建的
        paramDTO.getMessageCreateOrUpdatedDTOList().forEach(messageCreateOrUpdatedDTO -> {
            if (messageCreateOrUpdatedDTO == null || MapUtils.isEmpty(
                    messageCreateOrUpdatedDTO.getGroupName2UpdateEnumMap())) {
                return;
            }
            RiskCaseDO riskCaseDO = caseDOMap.get(messageCreateOrUpdatedDTO.getRiskCaseId());
            if (riskCaseDO == null) {
                return;
            }
            BroadCastStrategyConfigDTO broadCastStrategyConfigDTO = caseTypeEnumBroadCastStrategyConfigDTOMap.get(
                    riskCaseDO.getType());
            if (broadCastStrategyConfigDTO == null) {
                return;
            }
            //获取每个群组的消息
            Map<String, String> groupName2MessageId = riskCaseDO.getGroupMessageMap();
            //放入更新后的map
            groupName2MessageId.putAll(
                    handleNotice(broadCastStrategyConfigDTO, paramDTO.getMessageVersion(), riskCaseDO.getCaseId(),
                            riskCaseDO.getPlaceCode(), groupName2MessageId,
                            messageCreateOrUpdatedDTO.getGroupName2UpdateEnumMap(),
                            caseId2ContextMap.get(riskCaseDO.getCaseId())));
            //放入每个case最新的groupMessageMap
            caseId2GroupMessageMap.put(riskCaseDO.getCaseId(), groupName2MessageId);
        });
        List<String> failedCaseIdList = new ArrayList<>();
        List<String> successCaseIdList = new ArrayList<>();
        //更新messageId和版本
        handleUpdatedMessageVersion(paramDTO.getMessageVersion(),
                caseId2GroupMessageMap,
                caseDOMap, failedCaseIdList, successCaseIdList);
        //构建返回值
        return RiskCaseMessageNotifyResultDTO.builder().failedCaseIdList(failedCaseIdList)
                .successCaseIdList(successCaseIdList).build();
    }

    /**
     * 计算风险事件需要创建或更新的消息列表
     *
     * @param caseIdList
     * @return
     */
    @Override
    public Map<String, MessageCreateOrUpdatedDTO> calcCaseNeedGreatOrUpdateMessage(List<String> caseIdList) {
        //收到变更的消息了,判断是否要走创建还是更新,更新
        if (CollectionUtils.isEmpty(caseIdList)) {
            return new HashMap<>();
        }
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(caseIdList).build());
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return new HashMap<>();
        }
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIdList).build());
        Map<RiskCaseTypeEnum, BroadCastStrategyConfigDTO> caseTypeEnumBroadCastStrategyConfigDTOMap = lionConfigRepository.getCaseType2BroadCastStrategyConfig();
        return handleCaseNeedGreatOrUpdateMessage(riskCaseDOList, riskCaseVehicleRelationDOList,
                caseTypeEnumBroadCastStrategyConfigDTOMap);
    }

    @Override
    public Map<String, Map<String, String>> getCaseRenderData(List<String> caseIdList) {
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(caseIdList).build());
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return new HashMap<>();
        }
        Map<String, Map<String, String>> result = new HashMap<>();
        //查询车辆关联事件
        List<RiskCaseVehicleRelationDO> vehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIdList).build());
        //list 转 map
        Map<String, RiskCaseDO> caseDOMap = riskCaseDOList.stream()
                .collect(Collectors.toMap(RiskCaseDO::getCaseId, Function.identity(), (o1, o2) -> o1));
        Map<String, List<RiskCaseVehicleRelationDO>> caseId2VehicleRelationMap = vehicleRelationDOList.stream()
                .collect(Collectors.groupingBy(RiskCaseVehicleRelationDO::getCaseId));
        //构建每个风险事件的参数上下文
        Map<String, Map<String, Object>> caseId2ContextMap = buildParamContext(caseDOMap, caseId2VehicleRelationMap);
        Map<String, String> templateValues = lionConfigRepository.getLinkDataTemplateValues();
        caseId2ContextMap.forEach((caseId, context) -> {
            Map<String, String> resultMap = result.computeIfAbsent(caseId, key -> new HashMap<>());
            templateValues.forEach((key, valueTemplate) -> {
                resultMap.put(key, StringUtils.defaultString(VelocityUtils.render(valueTemplate, context),
                        CharConstant.CHAR_SPACE));
            });
        });
        return result;
    }

    private Map<String, MessageCreateOrUpdatedDTO> handleCaseNeedGreatOrUpdateMessage(List<RiskCaseDO> riskCaseDOList,
            List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList,
            Map<RiskCaseTypeEnum, BroadCastStrategyConfigDTO> caseTypeEnumBroadCastStrategyConfigDTOMap) {
        Map<String, MessageCreateOrUpdatedDTO> caseId2MessageCreateOrUpdatedDTOMap = new HashMap<>();
        riskCaseDOList.forEach(riskCaseDO -> {
            BroadCastStrategyConfigDTO broadCastStrategyConfigDTO = caseTypeEnumBroadCastStrategyConfigDTOMap.get(
                    riskCaseDO.getType());
            if (broadCastStrategyConfigDTO == null) {
                return;
            }
            //从群的角度，看这个消息是否要更新，还是要创建
            List<GroupTemplateDTO> groupTemplateList = broadCastStrategyConfigDTO.getGroupTemplateList();
            //风险和车辆的关系
            Map<String, List<RiskCaseVehicleRelationDO>> caseId2VehicleRelationMap = riskCaseVehicleRelationDOList.stream()
                    .collect(Collectors.groupingBy(RiskCaseVehicleRelationDO::getCaseId));
            groupTemplateList.forEach(groupTemplateDTO -> {
                //获取群组和消息Id的映射
                Map<String, String> groupName2MessageId = riskCaseDO.getGroupMessageMap();
                //这个更新要跟新的信息集合
                MessageCreateOrUpdatedDTO createOrUpdatedDTO = caseId2MessageCreateOrUpdatedDTOMap.computeIfAbsent(
                        riskCaseDO.getCaseId(),
                        k -> MessageCreateOrUpdatedDTO.builder().riskCaseId(riskCaseDO.getCaseId()).build());
                //获取对应的群组消息
                String groupTemplateMessageId = groupName2MessageId.get(groupTemplateDTO.getGroupTemplateName());
                if (StringUtils.isBlank(groupTemplateMessageId)) {
                    if (!groupTemplateDTO.isNeedBroadcast(riskCaseDO.getOccurTime(), riskCaseDO.getSource())) {
                        //如果不需要播报，则不处理
                        return;
                    }
                    //构建这个参数
                    Map<String, Object> context = buildParamContext(riskCaseDO,
                            caseId2VehicleRelationMap.get(riskCaseDO.getCaseId()));
                    if (groupTemplateDTO.isNeedFilter(context)) {
                        //如果被过滤，也不处理创建
                        return;
                    }
                    //需要添加
                    createOrUpdatedDTO.addGroupUpdateOrCreated(groupTemplateDTO.getGroupTemplateName(),
                            MessageUpdateEnum.CREATE);
                } else {
                    //需要更新
                    createOrUpdatedDTO.addGroupUpdateOrCreated(groupTemplateDTO.getGroupTemplateName(),
                            MessageUpdateEnum.UPDATE);
                }
            });
        });
        return caseId2MessageCreateOrUpdatedDTOMap;
    }

    /**
     * 构建参数
     *
     * @param riskCaseDO
     * @param riskCaseVehicleRelationDOS
     * @return
     */
    private Map<String, Object> buildParamContext(RiskCaseDO riskCaseDO,
            List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOS) {
        Map<String, Object> context = new HashMap<>();
        context.put("riskCase", riskCaseDO);
        context.put("riskCaseExtInfo", riskCaseDO);
        if (CollectionUtils.isNotEmpty(riskCaseVehicleRelationDOS)) {
            RiskCaseVehicleRelationDO firstVehicle = riskCaseVehicleRelationDOS.get(0);
            context.put("vehicle", firstVehicle);
            context.put("snapshot", firstVehicle.getVehicleSnapshotInfo());
        }
        return context;
    }

    /**
     * 更新messageId和版本
     *
     * @param messageVersion
     * @param caseId2GroupMessageMap
     * @param caseDOMap
     * @param failRiskCaseIdList
     */
    private void handleUpdatedMessageVersion(Long messageVersion,
            Map<String, Map<String, String>> caseId2GroupMessageMap, Map<String, RiskCaseDO> caseDOMap,
            List<String> failRiskCaseIdList, List<String> successRiskCaseIdList) {
        //把更新成功的，放回数据库
        if (MapUtils.isEmpty(caseId2GroupMessageMap)) {
            return;
        }
        List<RiskCaseDO> updatedRiskCaseList = new ArrayList<>();
        Set<String> updatedCaseId = new HashSet<>();
        caseId2GroupMessageMap.forEach((caseId, group2MessageId) -> {
            RiskCaseDO riskCaseDO = caseDOMap.get(caseId);
            if (riskCaseDO == null) {
                return;
            }
            List<String> finalMessageIdList = new ArrayList<>();
            group2MessageId.forEach((groupName, pureMessageId) -> {
                finalMessageIdList.add(pureMessageId + CharConstant.CHAR_JH + groupName);
            });
            //更新消息id
            riskCaseDO.setMessageId(finalMessageIdList.stream().collect(Collectors.joining(CharConstant.CHAR_COMMA)));
            riskCaseDO.setMessageVersion(String.valueOf(messageVersion));
            updatedRiskCaseList.add(riskCaseDO);
            updatedCaseId.add(caseId);
        });
        if (CollectionUtils.isNotEmpty(updatedRiskCaseList)) {
            riskCaseRepository.batchSave(updatedRiskCaseList);
        }
        //失败的加回来
        failRiskCaseIdList.addAll(caseDOMap.keySet().stream().filter(caseId -> !updatedCaseId.contains(caseId)).collect(
                Collectors.toList()));
        successRiskCaseIdList.addAll(updatedCaseId);
    }

    /**
     * 处理消息通知
     *
     * @param broadCastStrategyConfigDTO
     * @param caseNoticeContext
     * @return
     */
    private Map<String, String> handleNotice(BroadCastStrategyConfigDTO broadCastStrategyConfigDTO, Long messageVersion,
            String caseId, String placeCode, Map<String, String> groupName2MessageId,
            Map<String, MessageUpdateEnum> groupName2MessageUpdate, Map<String, Object> caseNoticeContext) {
        if (broadCastStrategyConfigDTO == null || broadCastStrategyConfigDTO.isIllegal() || MapUtils.isEmpty(
                groupName2MessageUpdate)) {
            log.error(StringMessageFormatter.replaceMsg("风险{}无配置或者配置异常", caseId),
                    new SystemException("无播报配置"));
            return new HashMap<>();
        }
        if (!broadCastStrategyConfigDTO.isPlaceCodeInWhiteList(placeCode)) {
            log.warn(StringMessageFormatter.replaceMsg("场地{}不在需要播报的白名单内", placeCode));
            return new HashMap<>();
        }
        Map<String, String> updatedGroupName2MessageId = new HashMap<>();
        groupName2MessageUpdate.forEach((groupName, messageUpdateEnum) -> {
            //群组和消息更新方式
            GroupTemplateDTO groupTemplateDTO = broadCastStrategyConfigDTO.getGroupTemplateByGroupTemplateName(
                    groupName);
            if (groupTemplateDTO == null) {
                //未找到更新的配置
                log.error(StringMessageFormatter.replaceMsg(
                                "groupTemplateName未找到对应的groupTemplateDTO,groupTemplateName:{}", groupName),
                        new SystemException("groupTemplateName未找到对应的groupTemplateDTO"));
                return;
            }
            if (messageUpdateEnum == MessageUpdateEnum.CREATE && StringUtils.isEmpty(
                    groupName2MessageId.get(groupName))) {
                //更新
                String outBizId = caseId + CharConstant.CHAR_JH + groupName;
                Map<String, String> finalParam = broadCastStrategyConfigDTO.render(caseNoticeContext,
                        groupTemplateDTO.getTemplateValues());
                //根据group走不一样
                DxNoticeParamVTO dxNoticeParamVTO = DxNoticeParamVTO.builder().params(finalParam)
                        .outBizId(outBizId)
                        .version(messageVersion)
                        .templateId(groupTemplateDTO.getTemplateId())
                        .groupIdList(groupTemplateDTO.getGroupIdList()).build();
                //发送消息,失败则为null
                String messageId = dxNoticeAdapter.createOrUpdateDxMessage(dxNoticeParamVTO);
                //放入进去
                updatedGroupName2MessageId.put(groupName, messageId);

            } else if (messageUpdateEnum == MessageUpdateEnum.UPDATE && StringUtils.isNotEmpty(
                    groupName2MessageId.get(groupName))) {
                //取纯messageId
                String pureMessageId = groupName2MessageId.get(groupName);
                //需要拼接参数
                String outBizId = caseId + CharConstant.CHAR_JH + groupName;
                Map<String, String> groupTemplateValues = groupTemplateDTO.getTemplateValues();
                //渲染参数
                Map<String, String> finalParams = broadCastStrategyConfigDTO.render(caseNoticeContext,
                        groupTemplateValues);
                //结果
                DxNoticeParamVTO dxNoticeParamVTO = DxNoticeParamVTO.builder().params(finalParams)
                        .messageId(pureMessageId)
                        .outBizId(outBizId)
                        .version(messageVersion)
                        .templateId(groupTemplateDTO.getTemplateId())
                        .groupIdList(groupTemplateDTO.getGroupIdList())
                        .build();
                //发送消息,失败则为null
                dxNoticeAdapter.createOrUpdateDxMessage(dxNoticeParamVTO);
                updatedGroupName2MessageId.put(groupName, pureMessageId);
            }
        });
        return updatedGroupName2MessageId;
    }

    /**
     * 将需要渲染的参数构建到上下文中
     *
     * @param riskCaseDOMap
     * @param vehicleRelationDOMap
     * @return
     */
    private Map<String, Map<String, Object>> buildParamContext(Map<String, RiskCaseDO> riskCaseDOMap,
            Map<String, List<RiskCaseVehicleRelationDO>> vehicleRelationDOMap) {
        Map<String, Map<String, Object>> perCaseContextMap = new HashMap<>();
        if (MapUtils.isEmpty(riskCaseDOMap)) {
            return perCaseContextMap;
        }
        riskCaseDOMap.forEach((caseId, riskCaseDO) -> {
            Map<String, Object> context = new HashMap<>();
            //获取车辆信息
            List<RiskCaseVehicleRelationDO> vehicleRelationDOList = vehicleRelationDOMap.getOrDefault(caseId,
                    new ArrayList<>());
            RiskCaseVehicleRelationDO firstVehicle = vehicleRelationDOList.stream().findFirst().orElse(null);
            String vehicleId = Optional.ofNullable(firstVehicle).map(RiskCaseVehicleRelationDO::getVehicleSnapshotInfo)
                    .map(VehicleInfoDO::getVehicleId).orElse(CharConstant.CHAR_EMPTY);
            List<String> vehicleIdList = vehicleRelationDOList.stream()
                    .map(riskCaseVehicleRelationDO -> Optional.ofNullable(
                                    riskCaseVehicleRelationDO.getVehicleSnapshotInfo())
                            .map(VehicleInfoDO::getVehicleId).orElse(CharConstant.CHAR_EMPTY))
                    .collect(Collectors.toList());
            //关联的车数量
            Integer vehicleNum = vehicleRelationDOList.size();
            String vehicleName = Optional.ofNullable(firstVehicle)
                    .map(RiskCaseVehicleRelationDO::getVehicleSnapshotInfo).map(VehicleInfoDO::getVehicleName)
                    .orElse(CharConstant.CHAR_EMPTY);
            String purpose = Optional.ofNullable(firstVehicle).map(RiskCaseVehicleRelationDO::getVehicleSnapshotInfo)
                    .map(VehicleInfoDO::getPurpose).orElse(CharConstant.CHAR_EMPTY);
            String vhrDesc = Optional.ofNullable(firstVehicle).map(RiskCaseVehicleRelationDO::getVehicleSnapshotInfo)
                    .map(VehicleInfoDO::getVhr).map(VHRModeEnum::getCode).orElse(CharConstant.CHAR_EMPTY);
            String poi = Optional.ofNullable(riskCaseDO.getExtInfo()).map(RiskCaseExtInfoDO::getPoi)
                    .orElse(CharConstant.CHAR_EMPTY);
            String city = Optional.ofNullable(riskCaseDO.getExtInfo()).map(RiskCaseExtInfoDO::getCity)
                    .orElse(CharConstant.CHAR_EMPTY);
            String area = Optional.ofNullable(riskCaseDO.getExtInfo()).map(RiskCaseExtInfoDO::getAre)
                    .orElse(CharConstant.CHAR_EMPTY);
            String vin = Optional.ofNullable(firstVehicle).map(RiskCaseVehicleRelationDO::getVehicleSnapshotInfo)
                    .map(VehicleInfoDO::getVin).orElse(CharConstant.CHAR_EMPTY);
            String btnDisabled = BooleanUtils.toString(RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus()),
                    CharConstant.CHAR_TRUE, CharConstant.CHAR_FALSE);
            String joinedVehicleIds = String.join(CharConstant.CHAR_DT, vehicleIdList);
            String joinedVehicleMonitorLinks = vehicleRelationDOList.stream()
                    .map(relationDO -> getMonitorLinkByRelation(relationDO, riskCaseDO))
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining(CharConstant.CHAR_DT));
            String joinedVehicleIdLinks = getVehicleLinked(vehicleIdList, caseId);
            String vhrMode = StringUtils.isNotBlank(vhrDesc) ? "VHR" + vhrDesc : CharConstant.CHAR_EMPTY;
            String vehicleFullName =
                    Objects.equals(RiskCaseTypeEnum.VEHICLE_CONGESTION, riskCaseDO.getType()) ? vehicleId
                            + CharConstant.CHAR_XX + vehicleNum : vehicleId + CharConstant.CHAR_XX + vehicleName;
            String monitorLink = getMonitorLinkStrByRelation(firstVehicle, riskCaseDO);
            String callMrmLink = buildMrmLink(caseId);
            //上下文包含的信息
            context.put("roadName", poi);
            context.put("caseId", caseId);
            context.put("caseTitle", Optional.ofNullable(riskCaseDO.getType()).map(RiskCaseTypeEnum::getDesc).get());
            context.put("themeColor", ThemeEnum.findThemeByValue(riskCaseDO.getStatus()));
            context.put("occurTime", DatetimeUtil.formatTime(riskCaseDO.getOccurTime()));
            context.put("recallTime", DatetimeUtil.formatTime(riskCaseDO.getRecallTime()));
            //车辆扎堆，需要跟进
            context.put("vehicleNames", vehicleFullName);
            context.put("purpose", purpose);
            context.put("vhrMode", vhrMode);
            context.put("city", city + CharConstant.CHAR_XH + area);
            context.put("vin", vin);
            context.put("vehicleNumber", vehicleRelationDOList.size());
            context.put("btnDisabled", btnDisabled);
            context.put("btnText", riskCaseDO.getStatus().getDesc());
            //展示里面，第一辆车的信息
            context.put("firstVehicle", vehicleRelationDOList.stream().findFirst().orElse(null));
            context.put("vehicleList", vehicleRelationDOList);
            context.put("riskCase", riskCaseDO);
            // monitor视频超链接列表字符串，例如 [M2345|https://xxxx]、[M3456|https://xxxx]、[M6789|https://xxxx]
            context.put("vehicleMonitorLinkStr", joinedVehicleMonitorLinks);
            // 车辆扎堆信息串，例如 M2345、M3456、M6789
            context.put("vehicleInfoStr", joinedVehicleIds);
            context.put("vehicleInfoLinkStr", joinedVehicleIdLinks);
            context.put("vehicleId", vehicleId);
            //v2
            context.put("caseTitleWithStatusAndDurationText", getCaseTitleWithStatusAndDurationText(riskCaseDO));
            context.put("vehicleNameLink", getVehicleLinked(vehicleFullName, caseId));
            context.put("getVehicleLinkedWithExtInfo",
                    getVehicleLinkedWithExtInfo(vehicleFullName,
                            Optional.ofNullable(firstVehicle).map(RiskCaseVehicleRelationDO::getExtInfoStr)
                                    .orElse(CharConstant.CHAR_EMPTY), caseId));
            // 新增monitor V2.0 车辆回放的首页链接
            context.put("monitorLink", monitorLink);
            context.put("callMrmLink", callMrmLink);
            // 判断卡片状态是否已完成，已完成再展示持续时间
            context.put("durationText", StringUtils.EMPTY);
            context.put("durationValue", StringUtils.EMPTY);
            if (RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus())) {
                context.put("durationText", DURATION_TEXT);
                context.put("durationValue", getDurationValue(riskCaseDO));
            }
            perCaseContextMap.put(caseId, context);
        });
        return perCaseContextMap;
    }

    /**
     * 构建mrm link
     *
     * @param caseId
     * @return
     */
    private String buildMrmLink(String caseId) {
        ConfirmStyleDTO confirmStyleDTO = new ConfirmStyleDTO();
        confirmStyleDTO.setPromptType(ConfirmType.TOAST);
        confirmStyleDTO.setNeedConfirm(true);
        confirmStyleDTO.setConfirmContent("确认呼叫坐席？");
        confirmStyleDTO.setConfirmTitle("注意");
        return PushContentUtils.buildContent("呼叫坐席❗️", EVE_HOST + MANUAL_CALL_MRM_PATH,
                ImmutableMap.of("caseId", caseId), confirmStyleDTO);
    }

    /**
     * 获取
     *
     * @param vehicleIdList
     * @return
     */
    private String getVehicleLinked(List<String> vehicleIdList, String caseId) {
        return vehicleIdList.stream().map(vehicleId -> getVehicleLinked(vehicleId, caseId))
                .collect(Collectors.joining(CharConstant.CHAR_DT));
    }

    /**
     * 获取
     *
     * @param vehicleId
     * @return
     */
    private String getVehicleLinked(String vehicleId, String caseId) {
        return String.format("[%s|%s]", vehicleId, EVE_HOST + String.format(OPEN_CASE_URL, caseId));
    }

    /**
     * 获取
     *
     * @param vehicleId
     * @return
     */
    private String getVehicleLinkedWithExtInfo(String vehicleId, String extInfo, String caseId) {
        return String.format("[%s %s|%s]", vehicleId, extInfo, EVE_HOST + String.format(OPEN_CASE_URL, caseId));
    }

    /**
     * 获取停滞不当，带状态和结束时间的
     *
     * @param riskCaseDO
     * @return
     */
    private String getCaseTitleWithStatusAndDurationText(RiskCaseDO riskCaseDO) {
        if (RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus())) {
            //如果完结
            return String.format("%s-%s(%s)", riskCaseDO.getType().getDesc(), riskCaseDO.getStatus().getDesc(),
                    DatetimeUtil.getDurationTime(riskCaseDO.getRiskDurationTime().longValue()));

        } else {
            //没有
            return String.format("%s-%s", riskCaseDO.getType().getDesc(),
                    riskCaseDO.getStatus().getDesc());
        }

    }

    /**
     * 通过RiskCaseVehicleRelationDO获取monitor超链接
     *
     * @param relationDO
     */
    private String getMonitorLinkByRelation(RiskCaseVehicleRelationDO relationDO, RiskCaseDO riskCaseDO) {
        return Optional.ofNullable(relationDO)
                .map(RiskCaseVehicleRelationDO::getVehicleSnapshotInfo)
                .map(info -> {
                    String link = getMonitorLinkStrByRelation(relationDO, riskCaseDO);
                    if (StringUtils.isBlank(link)) {
                        return StringUtils.EMPTY;
                    }
                    return String.format("[%s|%s]", info.getVehicleId(), link);
                })
                .orElse(StringUtils.EMPTY);
    }

    /**
     * 通过RiskCaseVehicleRelationDO获取monitor链接字符串
     *
     * @param relationDO
     */
    private String getMonitorLinkStrByRelation(RiskCaseVehicleRelationDO relationDO, RiskCaseDO riskCaseDO) {
        if (relationDO == null || relationDO.getVehicleSnapshotInfo() == null) {
            return StringUtils.EMPTY;
        }
        String vehicleId = relationDO.getVehicleSnapshotInfo().getVehicleId();
        String vehicleName = relationDO.getVehicleSnapshotInfo().getVehicleName();
        return getMonitorLink(relationDO.getVin(), riskCaseDO.getOccurTime(), vehicleName, vehicleId,
                relationDO.getType());
    }

    /**
     * 获取monitor跳转链接
     *
     * @param vin
     * @param time
     * @param vehicleName
     * @param vehicleId
     * @param riskCaseTypeEnum
     * @return
     */
    private String getMonitorLink(String vin, Date time, String vehicleName, String vehicleId,
            RiskCaseTypeEnum riskCaseTypeEnum) {
        if (riskCaseTypeEnum == null || time == null) {
            return StringUtils.EMPTY;
        }
        String formatTime = DatetimeUtil.formatTime(time);
        String monitorLink = StringUtils.EMPTY;
        switch (riskCaseTypeEnum) {
            case VEHICLE_STAND_STILL:
                monitorLink = String.format(MONITOR_BACKUP_VIDEO_DETAIL_API, MONITOR_BACKUP_VIDEO_DETAIL_URL, vin,
                        UrlEncodeUtil.formatUrlParam(formatTime), vehicleName, vehicleId);
                break;
            case VEHICLE_SIDE_BY_SIDE:
                // 需要放提前10秒的视频
                formatTime = DatetimeUtil.formatTime(
                        DatetimeUtil.getNSecondsBeforeDateTime(time, MONITOR_PLAY_BACK_TIME_ADVANCE));
                monitorLink = String.format(MONITOR_BACKUP_VIDEO_API, MONITOR_BACKUP_VIDEO_URL, vin,
                        UrlEncodeUtil.formatUrlParam(formatTime));
                break;
            case VEHICLE_CONGESTION:
                monitorLink = String.format(MONITOR_BACKUP_VIDEO_API, MONITOR_BACKUP_VIDEO_URL, vin,
                        UrlEncodeUtil.formatUrlParam(formatTime));
                break;
        }
        return monitorLink;
    }

    /**
     * 获取持续时间
     *
     * @param riskCaseDO
     * @return
     */
    private String getDurationValue(RiskCaseDO riskCaseDO) {
        if (riskCaseDO == null || !RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus())) {
            return StringUtils.EMPTY;
        }
        return DatetimeUtil.getDurationTime(riskCaseDO.getRiskDurationTime().longValue());
    }
}
