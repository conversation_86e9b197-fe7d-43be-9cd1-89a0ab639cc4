package com.sankuai.wallemonitor.risk.center.domain.result;

import com.sankuai.wallemonitor.risk.center.infra.vto.result.FridayVerifyResultVTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCheckingQueueItemCheckResultDTO {

    private String tmpCaseId;

    /**
     * 大模型校验结果
     */
    private FridayVerifyResultVTO fridayVerifyResultVTO;

    /**
     * 是否在区域适配停车区域范围内
     */
    @Builder.Default
    private Boolean inMenderParkingArea = false;

}
