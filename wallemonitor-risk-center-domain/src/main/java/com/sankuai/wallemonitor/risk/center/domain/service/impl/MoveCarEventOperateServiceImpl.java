package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walledelivery.basic.client.response.deliverer.DelivererResponse;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.MoveCarEventReportRequest;
import com.sankuai.wallemonitor.risk.center.api.vo.MoveCarEventStatusVO;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.result.RiskCaseUpdatedResultDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.MoveCarEventOperateService;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DelivererQueryThriftServiceAdaptor;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.MoveCarEventConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.MoveCarEventExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.DuplicateMoveCarEventException;
import com.sankuai.wallemonitor.risk.center.infra.exception.ReportLimitExceededException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseMoveCarRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseMoveCarRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseMoveCarRelationDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Slf4j
public class MoveCarEventOperateServiceImpl implements MoveCarEventOperateService {

    @Resource
    private DelivererQueryThriftServiceAdaptor delivererQueryThriftServiceAdaptor;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private RiskCaseMoveCarRelationRepository riskCaseMoveCarRelationRepository;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private IDGenerateRepository iDGenerateRepository;

    @ConfigValue(key = LionKeyConstant.LION_KEY_MOVE_CAR_EVENT_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private MoveCarEventConfigDTO moveCarEventConfigDTO;


    /**
     * 根据车牌号获取车辆挪车事件状态
     *
     * @param vehicleId
     * @return
     * @throws Exception
     */
    @Override
    public MoveCarEventStatusVO getMoveCarStatusByVehicleId(String vehicleId) {
        //1 车牌号校验
        String vehicleIdUpper = vehicleId.toUpperCase();
        String vin = delivererQueryThriftServiceAdaptor.getVinByVehicleId(vehicleIdUpper);
        if (StringUtils.isBlank(vin)) {
            return new MoveCarEventStatusVO();
        }
        //2 查询车辆是否存在挪车事件
        List<RiskCaseDO> riskCaseList = getUnprocessedMoveCarEventList(vin);
        return MoveCarEventStatusVO.builder().vehicleId(vehicleIdUpper)
                .isMoving(CollectionUtils.isEmpty(riskCaseList) ? false : true).build();
    }

    /**
     * 校验并创建挪车事件
     *
     * @param request
     * @param openId
     * @throws Exception
     */
    @Override
    public void checkAndCreateMoveCarEvent(MoveCarEventReportRequest request, String openId) throws Exception {
        //1 统一转成大写
        String vehicleId = request.getVehicleId().toUpperCase();
        //2 车牌号校验, 调用basic服务接口查询是否有该车数据 -- 写redis
        DelivererResponse delivererResponse = delivererQueryThriftServiceAdaptor.getDelivererInfoByVehicleId(vehicleId);
        if (Objects.isNull(delivererResponse) || StringUtils.isBlank(delivererResponse.getIdentifyNum())) {
            throw new IllegalArgumentException("车辆不存在");
        }
        String vin = delivererResponse.getIdentifyNum();
        String vehicleName = delivererResponse.getAccount();
        //3 加锁创建工单
        String eventId = iDGenerateRepository.generateEventId(new Date(), RiskCaseTypeEnum.MOVE_CAR_EVENT.name(),
                vehicleName);

        // 4 加锁创建工单 - 防抖
        lockUtils.lockWaitNoRelease(
                LockKeyPreUtil.buildVinAndType(vin, RiskCaseTypeEnum.MOVE_CAR_EVENT),
                1,
                TimeUnit.SECONDS,
                () -> {
                    // 3.1 工单去重
                    List<RiskCaseDO> eventList = getUnprocessedMoveCarEventList(vin);
                    if (!CollectionUtils.isEmpty(eventList)) {
                        throw new DuplicateMoveCarEventException("已有挪车工单");
                    }
                    // 3.2 挪车次数校验
                    Long ordersNum = getReportedMoveCarEventCount(openId);
                    if (ordersNum >= moveCarEventConfigDTO.getMaxReportNum()) {
                        log.info("openId = {} 今日上报次数 {} 次超过最大值 {}", openId, ordersNum,
                                moveCarEventConfigDTO.getMaxReportNum());
                        throw new ReportLimitExceededException("今日上报数已达上线");
                    }
                    //3.3 创建挪车事件
                    createMoveCarEvent(request, openId, vin, eventId);
                });
    }

    /**
     * 创建挪车事件
     *
     * @param request
     * @param openId
     * @param vin
     * @param eventId
     */
    private void createMoveCarEvent(MoveCarEventReportRequest request, String openId, String vin, String eventId) {
        //1 插入风险数据库 以及 风险事件车辆信息关联数据库
        RiskCaseUpdatedParamDTO riskCaseUpdatedParamDTO = new RiskCaseUpdatedParamDTO();
        riskCaseUpdatedParamDTO.setEventId(eventId);
        riskCaseUpdatedParamDTO.setStatus(RiskCaseStatusEnum.NO_DISPOSAL);
        riskCaseUpdatedParamDTO.setVinList(Arrays.asList(vin));
        riskCaseUpdatedParamDTO.setSource(RiskCaseSourceEnum.APPLET_OF_WECHAT);
        riskCaseUpdatedParamDTO.setType(RiskCaseTypeEnum.MOVE_CAR_EVENT);
        riskCaseUpdatedParamDTO.setTimestamp(new Date().getTime());
        riskCaseUpdatedParamDTO.setRecallTime(new Date().getTime());
        RiskCaseUpdatedResultDTO resultDTO = riskCaseOperateService.createOrUpdateRiskCase(riskCaseUpdatedParamDTO);
        if (Objects.isNull(resultDTO) || Objects.isNull(resultDTO.getRiskCaseDO())) {
            log.error("createMoveCarEvent error, riskCaseUpdatedParamDTO = {}", riskCaseUpdatedParamDTO);
            return;
        }
        //2 插入扫码挪车风险相关信息库
        RiskCaseMoveCarRelationDO riskCaseMoveCarRelationDO = new RiskCaseMoveCarRelationDO();
        riskCaseMoveCarRelationDO.setEventId(eventId);
        riskCaseMoveCarRelationDO.setReporter(openId);
        riskCaseMoveCarRelationDO.setCaseId(resultDTO.getRiskCaseDO().getCaseId());
        riskCaseMoveCarRelationDO.setExtInfo(JacksonUtils.to(buildMoveCarEventExtInfo(request, openId, vin)));
        riskCaseMoveCarRelationRepository.save(riskCaseMoveCarRelationDO);
    }


    /**
     * 获取指定车辆未处理完成的挪车事件列表
     *
     * @param vin
     * @return
     */
    private List<RiskCaseDO> getUnprocessedMoveCarEventList(String vin) {
        RiskCaseDOQueryParamDTO param = new RiskCaseDOQueryParamDTO();
        param.setLeftJoinRelation(true);
        param.setVinList(Arrays.asList(vin));
        // 设置类型
        param.setType(RiskCaseTypeEnum.MOVE_CAR_EVENT.getCode());
        // 设置数据源
        param.setSource(RiskCaseSourceEnum.APPLET_OF_WECHAT.getCode());
        // 设置状态列表
        param.setStatusList(RiskCaseStatusEnum.getUnTerminal());
        //  设置创建时间范围
        param.setCreateTimeRange(
                TimePeriod.builder().beginDate(DatetimeUtil.getBeforeTime(new Date(), TimeUnit.HOURS,
                                moveCarEventConfigDTO.getEventReportValidDurationHour()))
                        .endDate(new Date()).build());
        List<RiskCaseDO> riskCaseList = riskCaseRepository.queryByParam(param);
        log.info("getUnprocessedMoveCarEventList, vin = {}, riskCaseList:{}", vin, riskCaseList);
        return riskCaseList;
    }

    /**
     * 获取指定用户上报挪车工单的数量
     *
     * @param openId
     * @return
     */
    private Long getReportedMoveCarEventCount(String openId) {
        RiskCaseMoveCarRelationDOQueryParamDTO param = new RiskCaseMoveCarRelationDOQueryParamDTO();
        param.setReporter(openId);
        param.setCreateTimeGrateTo(DatetimeUtil.getBeforeTime(new Date(), TimeUnit.HOURS,
                moveCarEventConfigDTO.getEventReportValidDurationHour()));
        List<RiskCaseMoveCarRelationDO> riskCaseMoveCarRelationDOList = riskCaseMoveCarRelationRepository.queryByParam(
                param);
        if (CollectionUtils.isEmpty(riskCaseMoveCarRelationDOList)) {
            return 0L;
        }
        return (long) riskCaseMoveCarRelationDOList.size();
    }

    /**
     * 构建挪车请求的备注信息DTO。 此方法主要用于将挪车请求的相关信息（如挪车原因、车辆位置、联系电话等）以及用户的openId封装成RemarkDTO对象。
     *
     * @param request 挪车请求对象，包含了挪车的详细信息。
     * @param openId  用户的唯一标识，用于追踪用户的挪车请求。
     * @return 封装好的RemarkDTO对象，包含了挪车请求的所有信息。
     */
    private MoveCarEventExtInfoDTO buildMoveCarEventExtInfo(MoveCarEventReportRequest request, String openId,
            String vin) {
        // 设置基础信息
        MoveCarEventExtInfoDTO moveCarEventExtInfoDTO = MoveCarEventExtInfoDTO.builder().openId(openId)
                .moveCarReason(request.getMoveCarReason())
                .carPosition(request.getCarPosition())
                .carPositionGps(request.getCarPositionGps())
                .urlList(request.getUrlList())
                .phoneNumber(request.getPhoneNumber()).build();

        // 获取vhr信息，以及云控安全员信息
        List<VehicleEveInfoVTO> vehicleEveInfoVTOList = vehicleAdapter.queryRuntimeVehicleInfo(
                VehicleRuntimeInfoParamVTO.builder().vinList(Arrays.asList(vin)).build());
        if (!CollectionUtils.isEmpty(vehicleEveInfoVTOList)) {
            VehicleEveInfoVTO vehicleEveInfoVTO = vehicleEveInfoVTOList.get(0);
            VHRModeEnum vhrModeEnum = vehicleEveInfoVTO.getVhr();
            String telecontrol = vehicleEveInfoVTO.getTelecontrol();
            if (Objects.nonNull(vhrModeEnum) && Objects.equals(vhrModeEnum, VHRModeEnum.VHR_EQUALS_ONE)
                    && StringUtils.isNotBlank(telecontrol)) {
                moveCarEventExtInfoDTO.setCloudSecurity(telecontrol);
            }
        }
        return moveCarEventExtInfoDTO;
    }
}
