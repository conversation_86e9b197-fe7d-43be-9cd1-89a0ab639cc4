package com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate;

import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertPolicyEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import java.util.List;

/**
 * 聚合策略接口
 */
public interface AggregateStrategy {

    /**
     * 获取告警策略
     */
    AlertPolicyEnum getAlertPolicy();

    /**
     * 处理策略逻辑
     * 包含数据查询、聚合分析、触发条件判断等完整的策略处理流程
     * 
     * @param triggerRiskCase 触发策略的风险事件
     * @param config 策略配置
     * @param context 聚合上下文
     * @return 需要发送告警的聚合结果列表
     */
    List<AggregateResultDTO> process(RiskCaseDO triggerRiskCase, AggregateStrategyConfigDTO config,
            AggregateAlertContext context);

    /**
     * 事件是否命中策略
     */
    default boolean isHit(RiskCaseDO riskCase, AggregateStrategyConfigDTO config, VehicleInfoDO vehicleInfoDO) {
        if (riskCase == null || config == null || vehicleInfoDO == null) {
            return false;
        }
        // 基于配置灰度过滤
        if (!config.getGrayRelease().isGrayHit(riskCase.getPoiName(), riskCase.getPlaceCode(), vehicleInfoDO.getVin(),
                vehicleInfoDO.getPurpose())) {
            return false;
        }
        // 基于配置的事件类型过滤
        if (config.getCaseType() != null && !config.getCaseType().equals(riskCase.getType().getCode())) {
            return false;
        }
        return true;
    }
} 