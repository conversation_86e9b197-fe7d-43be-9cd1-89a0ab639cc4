package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import java.util.Date;
import java.util.List;

/**
 * 风险事件查询服务
 */
public interface RiskCaseQueryService {

    List<RiskCaseDO> queryRiskCaseDispose(Date beginTime, Date endTime);

    List<RiskCaseInfoDTO> queryRiskCaseByVinList(List<String> vinList, Integer source, Integer type);
}
