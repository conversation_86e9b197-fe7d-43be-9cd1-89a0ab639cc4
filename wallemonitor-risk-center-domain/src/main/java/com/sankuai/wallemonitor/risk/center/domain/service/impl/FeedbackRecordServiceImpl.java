package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.sankuai.map.open.platform.api.iplocate.IpReverseAddress;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleeve.domain.enums.MessageType;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMessageDTO;
import com.sankuai.wallemonitor.risk.center.api.request.UserFeedbackReportRequest;
import com.sankuai.wallemonitor.risk.center.domain.service.FeedbackRecordService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DelivererQueryThriftServiceAdaptor;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.GisAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.OperateEnterContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseMessageDTOConvert.VehicleEventDataMessageExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.FeedbackRecordExtraDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.model.core.FeedbackRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.FeedbackRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.PhoneNumberEncryptUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FeedbackRecordServiceImpl implements FeedbackRecordService {

    @Resource
    private FeedbackRecordRepository feedbackRecordRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private GisAdapter gisAdapter;

    @Resource
    private DelivererQueryThriftServiceAdaptor delivererQueryThriftServiceAdaptor;

    @MessageProducer(topic = MessageTopicEnum.WALLEMONITOR_RISK_EVENT_MESSAGE)
    private CommonMessageProducer<RiskCaseMessageDTO> riskCaseMessageProducer;


    /**
     * 需要上锁的秒数
     */
    private int expireSeconds = 3;

    /**
     * 保存用户反馈记录
     *
     * @param request 用户反馈报告请求对象
     */
    public void saveFeedbackRecord(UserFeedbackReportRequest request, String userId) {

        FeedbackRecordDO feedbackRecordDO = new FeedbackRecordDO();
        feedbackRecordDO.setUserId(userId);
        feedbackRecordDO.setFeedbackContent(request.getFeedbackContent());
        feedbackRecordDO.setFeedbackChannel(request.getFeedbackChannel());
        feedbackRecordDO.setFeedbackType(request.getFeedbackType());
        // 处理附件url
        if (CollectionUtils.isNotEmpty(request.getUrlList())) {
            String attachmentUrl = String.join(",", request.getUrlList());
            feedbackRecordDO.setAttachmentUrl(attachmentUrl);
        }
        // 处理扩展字段
        FeedbackRecordExtraDTO extraDTO = buildFeedbackRecordExtraDTO(request);
        feedbackRecordDO.setExtra(JacksonUtils.to(extraDTO));
        // 存储反馈记录
        // 如果 expireSeconds 时间内未获取到锁资源，响应提交成功
        try {
            lockUtils.lockNoRelease(userId, expireSeconds,
                    () -> insertFeedbackRecord(feedbackRecordDO, extraDTO));
        } catch (UnableGetLockException e) {
            log.error("用户 {} 在 {} 秒内重复提交", userId, expireSeconds);
        }
    }

    /**
     * 插入反馈记录
     *
     * @param feedbackRecordDO 反馈记录对象
     */
    private void insertFeedbackRecord(FeedbackRecordDO feedbackRecordDO, FeedbackRecordExtraDTO extraDTO) {
        feedbackRecordRepository.save(feedbackRecordDO);
        // 同步数据到风险事件topic
        reportFeedbackRecord(feedbackRecordDO, extraDTO);
    }

    /**
     * 构建反馈记录扩展字段
     *
     * @param request 用户反馈报告请求对象
     * @return 反馈记录扩展字段对象
     */
    private FeedbackRecordExtraDTO buildFeedbackRecordExtraDTO(UserFeedbackReportRequest request) {
        FeedbackRecordExtraDTO extraDTO = new FeedbackRecordExtraDTO();
        // 处理手机号 - 需要对手机号进行加密处理
        Optional.ofNullable(request.getPhoneNumber())
                .map(PhoneNumberEncryptUtil::encryptPhone)
                .ifPresent(extraDTO::setPhoneNumber);
        // 用户地址
        Optional.ofNullable(getUserAddress()).ifPresent(extraDTO::setAddress);
        // 车牌号
        Optional.ofNullable(request.getVehicleId()).ifPresent(extraDTO::setVehicleId);
        return extraDTO;
    }

    /**
     * 获取用户地址
     */
    private String getUserAddress() {
        // 1 根据上下文获取用户IP
        String ip = OperateEnterContext.getClientIp();
        if (StringUtils.isBlank(ip)) {
            return null;
        }
        // 2 根据IP获取用户地址
        IpReverseAddress ipReverseAddress = gisAdapter.getAddressByIp(ip);

        // 3 取城市 + 区进行拼接返回
        if (Objects.nonNull(ipReverseAddress)) {
            return ipReverseAddress.getCity() + ipReverseAddress.getDistrict();
        }
        return null;
    }

    /**
     * 同步用户反馈信息到风险事件平台
     *
     * @param extraDTO
     */
    private void reportFeedbackRecord(FeedbackRecordDO feedbackRecordDO, FeedbackRecordExtraDTO extraDTO) {
        RiskCaseMessageDTO riskCaseMessageDTO = new RiskCaseMessageDTO();
        // 使用用户id + 反馈类型 + 反馈渠道 + 当前时间 组装eventId
        String eventId = feedbackRecordDO.getUserId() + CharConstant.CHAR_XH
                + feedbackRecordDO.getFeedbackType() + CharConstant.CHAR_XH
                + feedbackRecordDO.getFeedbackChannel() + CharConstant.CHAR_XH + new Date();
        riskCaseMessageDTO.setEventId(eventId);
        riskCaseMessageDTO.setType(RiskCaseTypeEnum.USER_FEEDBACK.getCode());
        riskCaseMessageDTO.setSource(RiskCaseSourceEnum.BEACON_TOWER.getCode());
        // 不存在状态变化，设置为开始
        riskCaseMessageDTO.setStatus(RiskCaseStatusEnum.NO_DISPOSAL.getCode());

        List<String> vinList = new ArrayList<>();
        //将车牌号转化成vin
        if (StringUtils.isNotBlank(extraDTO.getVehicleId())) {
            String vehicleId = extraDTO.getVehicleId();
            String vin = delivererQueryThriftServiceAdaptor.getVinByVehicleId(vehicleId);
            if (StringUtils.isNotBlank(vin)) {
                vinList.add(vin);
            }
        }
        riskCaseMessageDTO.setVinList(vinList);

        // 填充 extInfo
        VehicleEventDataMessageExtInfoDTO extInfoDTO = VehicleEventDataMessageExtInfoDTO.builder().build();
        extInfoDTO.setRiskEventDesc(feedbackRecordDO.getFeedbackContent());
        extInfoDTO.setRiskEventLocation(extraDTO.getAddress());
        riskCaseMessageDTO.setExtInfo(JacksonUtils.to(extInfoDTO));
        riskCaseMessageProducer.sendMqCommonMessage(riskCaseMessageDTO, MessageType.RISK_CASE_MESSAGE);

    }
}
