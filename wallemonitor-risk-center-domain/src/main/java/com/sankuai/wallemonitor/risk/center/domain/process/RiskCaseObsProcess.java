package com.sankuai.wallemonitor.risk.center.domain.process;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskObstacleContextInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseObstacleDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseObstacleDORepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskStrandingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskStrandingRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 风险障碍物转换保存
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RiskCaseObsProcess implements DomainEventProcess {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private RiskCaseObstacleDORepository riskCaseObstacleDORepository;

    @Resource
    private RiskStrandingRecordRepository riskStrandingRecordRepository;

    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.CASE_OBS_UPDATE)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != VehicleRuntimeInfoContextDO.class) {
            return true;
        }
        DomainEventChangeDTO<VehicleRuntimeInfoContextDO> typedDomainEvent = (DomainEventChangeDTO<VehicleRuntimeInfoContextDO>) eventDTO;
        // 过滤出状态为未处置的停滞事件
        List<VehicleRuntimeInfoContextDO> vehicleRuntimeInfoContextDOS = typedDomainEvent.getAfter();
        if (CollectionUtils.isEmpty(vehicleRuntimeInfoContextDOS)) {
            return true;
        }
        // 记录障碍物信息
        return handleRiskCaseObsV2(typedDomainEvent.getTimestamp(), vehicleRuntimeInfoContextDOS);
    }

    /**
     * 处理障碍物
     *
     * @param timestamp
     * @param vehicleRuntimeInfoContextDOS
     * @return
     */
    public boolean handleRiskCaseObsV2(Long timestamp,
            List<VehicleRuntimeInfoContextDO> vehicleRuntimeInfoContextDOS) {
        // 1. 获取此时上报障碍物信息的车辆列表
        List<String> vinList = vehicleRuntimeInfoContextDOS.stream().map(VehicleRuntimeInfoContextDO::getVin)
                .collect(Collectors.toList());
        // 2. 根据车辆列表查询此时是否存在 未取消 的停滞检测记录
        RiskStrandingRecordDOQueryParamDTO paramDTO = RiskStrandingRecordDOQueryParamDTO.builder()
                .vinList(vinList)
                // todo: 5分钟之前
                .createTimeGreatTo(DatetimeUtil.getNSecondsBeforeDateTime(new Date(timestamp),
                        5 * DateTimeConstant.ONE_MINUTE_SECOND))
                .statusList(DetectRecordStatusEnum.getUnCancel()).build();
        List<RiskStrandingRecordDO> recordDOList = riskStrandingRecordRepository.queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(recordDOList)) {
            return true;
        }

        // 3. 获取需要记录的车辆列表
        List<String> needRecordVinList = recordDOList.stream().map(RiskStrandingRecordDO::getVin)
                .collect(Collectors.toList());
        // 4. 取缓存，并构建 vin - 车辆上下文的映射map
        List<VehicleRuntimeInfoContextDO> runtimeInfoContextDOS = vehicleRuntimeInfoContextRepository
                .getFromCache(needRecordVinList);
        if (CollectionUtils.isEmpty(runtimeInfoContextDOS)) {
            return true;
        }
        Map<String, VehicleRuntimeInfoContextDO> vin2RuntimeInfoContextDOMap = runtimeInfoContextDOS.stream()
                .collect(Collectors.toMap(VehicleRuntimeInfoContextDO::getVin, Function.identity(), (v1, v2) -> v1));

        List<RiskCaseObstacleDO> obstacleList = recordDOList.stream()
                .map(recordDO -> {
                    VehicleRuntimeInfoContextDO runtimeInfo = vin2RuntimeInfoContextDOMap.get(recordDO.getVin());
                    if (runtimeInfo == null) {
                        return null;
                    }
                    return convert2RiskCaseObstacleDO(timestamp, recordDO, runtimeInfo);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        riskCaseObstacleDORepository.batchSave(obstacleList);
        return true;
    }

    /**
     * 构建风险障碍物DO
     *
     * @param timestamp
     * @param recordDO
     * @param runtimeInfoContextDO
     * @return
     */
    private RiskCaseObstacleDO convert2RiskCaseObstacleDO(Long timestamp, RiskStrandingRecordDO recordDO,
            VehicleRuntimeInfoContextDO runtimeInfoContextDO) {
        if (recordDO == null || runtimeInfoContextDO == null
                || CollectionUtils.isEmpty(runtimeInfoContextDO.getObstacleAbstracts())) {
            // 没有摘要就不保存了
            return null;
        }
        return RiskCaseObstacleDO.builder().caseId(recordDO.getTmpCaseId())
                .obstacle(RiskObstacleContextInfoDTO.builder()
                        .obstacleAbstractList(runtimeInfoContextDO.getObstacleAbstracts()).build())
                .createTime(new Date(timestamp)).build();
    }

}
