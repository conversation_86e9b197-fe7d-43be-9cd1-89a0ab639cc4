package com.sankuai.wallemonitor.risk.center.domain.process;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCheckingQueueService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.EntityKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCaseExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/12
 */
@Component
@Slf4j
public class ImproperStrandingCaseTransformProcess implements DomainEventProcess {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCheckingQueueService riskCheckingQueueService;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;


    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.IMPROPER_STRANDING_CASE_TRANSFORM_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != RiskCaseDO.class) {
            return true;
        }

        DomainEventChangeDTO<RiskCaseDO> typedDomainEvent = (DomainEventChangeDTO<RiskCaseDO>) eventDTO;
        // 过滤出状态为结束的停滞事件
        List<RiskCaseDO> terminalCaseDOList = typedDomainEvent.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                .stream()
                .filter(item -> Objects.equals(item.getType(), RiskCaseTypeEnum.STRANDING))
                .filter(item -> RiskCaseStatusEnum.isTerminal(item.getStatus()))
                .collect(Collectors.toList());
        // 过滤出状态为未处置的停滞事件
        List<RiskCaseDO> createdCaseDOList = typedDomainEvent.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status")
                                && Objects.equals(entityFieldChangeRecordDTO.getChangeType(),
                                EntityKeyConstant.ENTITY_EMPTY_TO_VALUE))
                .stream()
                .filter(item -> Objects.equals(item.getType(), RiskCaseTypeEnum.STRANDING))
                .filter(item -> Objects.equals(item.getStatus(), RiskCaseStatusEnum.NO_DISPOSAL))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(terminalCaseDOList) && CollectionUtils.isEmpty(createdCaseDOList)) {
            return true;
        }
        boolean handleTerminalCaseResult = handleCases(terminalCaseDOList, this::handleTerminalCase);
        boolean handleCreatedCaseResult = true;
        if (lionConfigRepository.isImproperStrandingCaseTransformSwitchOn()) {
            log.info("停滞不当事件转换开关开启");
            handleCreatedCaseResult = handleCases(createdCaseDOList, this::handleCreatedCase);
        }

        return handleTerminalCaseResult && handleCreatedCaseResult;
    }

    private boolean handleCases(List<RiskCaseDO> strandingCaseDOList, Function<List<String>, Boolean> handler) {
        if (CollectionUtils.isEmpty(strandingCaseDOList)) {
            return true;
        }
        //
        Set<String> importersStrandingEventIdSet = strandingCaseDOList.stream().map(RiskCaseDO::getCaseId)
                .collect(Collectors.toSet());

        return lockUtils.batchLockCanWait(
                LockKeyPreUtil.buildKeyWithEventId(importersStrandingEventIdSet),
                () -> handler
                        .apply(strandingCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList()))
        );
    }

    private boolean handleTerminalCase(List<String> terminalCaseIdList) {
        if (CollectionUtils.isEmpty(terminalCaseIdList)) {
            return true;
        }
        try {
            List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                            RiskCaseDOQueryParamDTO.builder().caseIdList(terminalCaseIdList).build()).stream()
                    .filter(item -> RiskCaseStatusEnum.isTerminal(item.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                return true;
            }
            Map<String, RiskCaseVehicleRelationDO> caseId2Relation = riskCaseVehicleRelationRepository.queryByParam(
                            RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(terminalCaseIdList).build())
                    .stream().collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, Function.identity(),
                            (v1, v2) -> v1));
            if (MapUtils.isEmpty(caseId2Relation)) {
                return true;
            }
            List<RiskCaseUpdatedParamDTO> paramDTOList = new ArrayList<>();
            riskCaseDOList.forEach(strandingRiskCase -> {
                //转换eventId是原case的caseId
                String transferEventId = strandingRiskCase.getCaseId();
                //取停滞事件的strandingRelation
                RiskCaseVehicleRelationDO strandingRelation = caseId2Relation.get(strandingRiskCase.getCaseId());
                paramDTOList.add(RiskCaseUpdatedParamDTO.builder()
                        //转换后的eventId
                        .eventId(transferEventId)
                        .source(RiskCaseSourceEnum.BEACON_TOWER)
                        //时间戳
                        .timestamp(strandingRiskCase.getCloseTime().getTime())
                        //关闭
                        .status(RiskCaseStatusEnum.DISPOSED)
                        //车辆
                        .vinList(Collections.singletonList(
                                Optional.ofNullable(strandingRelation).map(RiskCaseVehicleRelationDO::getVin)
                                        .orElse(null)))
                        .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL).build());
            });
            // 对riskCase进行解除
            riskCheckingQueueService.cancelItem(paramDTOList);
            List<String> eventIdList = paramDTOList.stream().map(RiskCaseUpdatedParamDTO::getEventId)
                    .collect(Collectors.toList());
            // 查询已经转换出来的case
            Set<String> improperStrandingEventIdSet = riskCaseRepository
                    .queryByParam(RiskCaseDOQueryParamDTO.builder().eventIdList(eventIdList).build()).stream()
                    .map(RiskCaseDO::getEventId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(improperStrandingEventIdSet)) {
                // 对已存在的风险，进行解除更新
                paramDTOList.stream()
                        .filter(riskCaseUpdatedParamDTO -> improperStrandingEventIdSet
                                .contains(riskCaseUpdatedParamDTO.getEventId()))
                        .forEach(paramDTO -> riskCaseOperateService.createOrUpdateRiskCase(paramDTO));
            }
        } catch (Exception e) {
            log.error("handleTerminalCase error", e);
            return false;
        }
        return true;
    }

    private boolean handleCreatedCase(List<String> createdCaseIdList) {
        if (CollectionUtils.isEmpty(createdCaseIdList)) {
            return true;
        }
        try {
            List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                            RiskCaseDOQueryParamDTO.builder().caseIdList(createdCaseIdList).build()).stream()
                    .filter(item -> Objects.equals(item.getStatus(), RiskCaseStatusEnum.NO_DISPOSAL)).collect(
                            Collectors.toList());
            if (CollectionUtils.isEmpty(riskCaseDOList)) {
                return true;
            }
            Map<String, RiskCaseVehicleRelationDO> eventId2RelationMap = riskCaseVehicleRelationRepository.queryByParam(
                            RiderCaseVehicleRelationDOParamDTO.builder()
                                    .eventIdList(riskCaseDOList.stream().map(RiskCaseDO::getEventId).collect(
                                            Collectors.toList())).build()).stream()
                    .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getEventId, Function.identity(),
                            (o1, o2) -> o1));
            if (MapUtils.isEmpty(eventId2RelationMap)) {
                log.error("未查询到关联车辆信息");
                return false;
            }
            List<RiskCaseUpdatedParamDTO> paramDTOList = riskCaseDOList.stream().map(strandingRiskCaseDO -> {
                RiskCaseVehicleRelationDO relationDO = eventId2RelationMap.get(strandingRiskCaseDO.getEventId());
                if (Objects.isNull(relationDO)
                        || !VHRModeEnum.VHR_GREAT_THAN_ONE.getCode().equals(relationDO.getVhrMode())) {
                    // 为空或者vhr!= '>1',无需进入预检队列
                    return null;
                }
                String vin = relationDO.getVin();
                if (StringUtils.isBlank(vin)) {
                    log.error("关联车辆信息为空");
                    return null;
                }
                String transferredCaseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID,
                        Collections.singletonList(vin), RiskCaseSourceEnum.BEACON_TOWER,
                        RiskCaseTypeEnum.VEHICLE_STAND_STILL,
                        strandingRiskCaseDO.getOccurTime().getTime());
                //caseID即为转换后的eventId
                String transferredEventId = strandingRiskCaseDO.getCaseId();
                return RiskCaseUpdatedParamDTO.builder()
                        .caseId(transferredCaseId)
                        .eventId(transferredEventId)
                        .source(RiskCaseSourceEnum.BEACON_TOWER)
                        .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL)
                        .positionDO(strandingRiskCaseDO.getLocation())
                        .poiName(strandingRiskCaseDO.getPoiName())
                        //其他
                        .status(strandingRiskCaseDO.getStatus())
                        .timestamp(strandingRiskCaseDO.getOccurTime().getTime())
                        .recallTime(strandingRiskCaseDO.getRecallTime().getTime())
                        .vinList(Collections.singletonList(vin))
                        .build();
            }).filter(Objects::nonNull).collect(Collectors.toList());

            riskCheckingQueueService.append(paramDTOList);
        } catch (Exception e) {
            log.error("handleCreatedCase error", e);
            return false;
        }
        return true;
    }
}
