package com.sankuai.wallemonitor.risk.center.domain.strategy.detector;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.constant.CatMonitorConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskDetectConfirmDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector.RiskDetectorRecordFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDetectorRecordBaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.RiskDetectBaseConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.lang.reflect.Type;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.ResolvableType;
import org.springframework.stereotype.Service;

/**
 * 风险检查器管理服务
 * s
 *
 * <AUTHOR>
 * @Date 2024/10/14
 */
@Slf4j
@Service
public class RiskDetectorManager implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @ConfigValue(key = "risk.detector.opened.list", value = "", defaultValue = "", allowBlankValue = true)
    private HashSet<String> openedDetectorList;

    protected HashMap<String, RiskDetector> DETECTOR_MAP = new HashMap<>();

    /**
     * 对应的仓储
     */
    private Map<Class<?>, AbstractMapperSingleRepository> repoMap = new HashMap<>();

    private Map<Class<?>, RiskDetectorRecordFactory> factoryMap = new HashMap<>();

    @Resource
    private LockUtils lockUtils;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @ConfigValue(
            key = LionKeyConstant.LION_KEY_DETECTOR_IGNORE_FIELDS, value = "",
            defaultValue = "[\"obstacleContext\",\n"
                    + "                \"monitorMetricsInfo\", \"fenceContext\", \"obstacleAbstracts\"]",
            allowBlankValue = true
    )
    private List<String> ignoreFields;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    protected void init() {
        initDetector();
        initializeRecordRepoBeanMap();
        initializeFactoryMap();

    }

    /**
     * 风险检测
     *
     * @param detectorParamDTO
     */
    public void process(DetectorParamDTO detectorParamDTO) {
        if (detectorParamDTO == null || detectorParamDTO.isEmpty()) {
            return;
        }
        // 获取检测器RiskDetector的泛型，
        // 获取检测器仓储
        HashSet<String> openedDetectorSet = openedDetectorList;
        if (CollectionUtils.isEmpty(openedDetectorSet)) {
            log.info("opened detector list is empty");
            return;
        }
        List<VehicleRuntimeInfoContextDO> vehicleContextList = detectorParamDTO.getVinRunTimeList();
        Map<String, VehicleEveInfoVTO> vehicleEveInfoVTOMap = detectorParamDTO.getEveInfoList().stream()
                .collect(Collectors.toMap(VehicleEveInfoVTO::getVin, Function.identity(), (v1, v2) -> v1));
        openedDetectorSet.forEach(detectorName -> {
            try {
                ParallelExecutor.executeParallelTasks("risk_detector", vehicleContextList, context -> {
                    VehicleEveInfoVTO eveInfo = vehicleEveInfoVTOMap.get(context.getVin());
                    this.handleDetectorWithLock(context, eveInfo, detectorName);
                });
            } catch (Exception e) {
                log.error("detectorName process error", e);
            }
        });
    }

    /**
     * 处理加锁
     *
     * @param vehicleRuntimeInfoContextDO
     * @param eveInfo
     * @param detectorName
     */

    private void handleDetectorWithLock(VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO,
            VehicleEveInfoVTO eveInfo, String detectorName) {
        DetectRecordStatusEnum detectResult = DetectRecordStatusEnum.UNKNOWN;
        String vin = vehicleRuntimeInfoContextDO.getVin();
        Long startTime = System.currentTimeMillis();
        Boolean error = false;
        try {
            detectResult = lockUtils.batchLockCanWait(LockKeyPreUtil.buildRiskDetectorManager(vin, detectorName),
                    () -> handleDetector(vehicleRuntimeInfoContextDO, eveInfo, detectorName));
        } catch (UnableGetLockException e) {
            error = true;
            log.warn(StringMessageFormatter.replaceMsg("执行风险检测器获取锁失败，detectorName:{},vin:{}", detectorName, vin,
                    vehicleRuntimeInfoContextDO), e);
        } catch (Exception e) {
            error = true;
            log.error(StringMessageFormatter.replaceMsg("执行风险检测器失败，detectorName:{}, vin: {},context:{}", detectorName,
                    vin, vehicleRuntimeInfoContextDO), e);
        } finally {
            Long endTime = System.currentTimeMillis();
            Transaction transaction = Cat.newTransactionWithDuration(CatMonitorConstant.DETECTOR, detectorName,
                    endTime - startTime);
            transaction.setStatus(error ? "-1" : "0");
            transaction.complete();
            log.info("[detectorName:{}][vin:{}][startTime:{}][cost:{}][result:{}]", detectorName, vin,
                    DatetimeUtil.formatTime(new Date(startTime)), endTime - startTime, detectResult);
        }
    }

    /**
     * 处理单车检测
     *
     * @param eveInfo
     * @param detectorName
     */
    private DetectRecordStatusEnum handleDetector(VehicleRuntimeInfoContextDO runtimeContextDO,
            VehicleEveInfoVTO eveInfo,
            String detectorName) {
        DetectRecordStatusEnum detectResult = DetectRecordStatusEnum.UNKNOWN;
        String vin = runtimeContextDO.getVin();
        // 获取处理类型
        RiskDetector riskDetector = getDetectorByName(detectorName);
        ResolvableType resolvableType = ResolvableType.forClass(riskDetector.getClass()).as(RiskDetector.class);
        // 取第一个
        Type recordType = resolvableType.hasGenerics() ? resolvableType.getGeneric(0).getType() : null;
        CheckUtil.isNotNull(recordType, "检测器异常,无对应的检查类");
        // 1.获取记录仓储
        AbstractMapperSingleRepository repository = repoMap.get(recordType);
        // 获取检测器的返回类型
        RiskDetectBaseConfig detectConfig = lionConfigRepository.getDetectConfig(riskDetector.getDetectRiskType());
        // 构建检测器的上下文
        DetectContextDTO detectContext = DetectContextDTO.builder().eveInfo(eveInfo)
                .runtimeContext(runtimeContextDO)
                .ignoreFields(ignoreFields)
                .detectConfig(detectConfig).build();
        // 2.查询进行中的
        List<? extends RiskDetectorRecordBaseDO> latest = repository
                .queryByParam(DetectorRecordQueryParamDTO.builder().vin(vin)
                        // 取进行中和确认中的
                        .statusList(Lists.newArrayList(DetectRecordStatusEnum.PROCESSING.getCode(),
                                DetectRecordStatusEnum.CONFIRMED.getCode()))
                        .build());
        if (latest.size() > 1) {
            // 多个进行报警
            log.error("riskDetector {},vin:{} has two processing!,will use latest one", detectorName, vin,
                    new SystemException("riskDetector has two processing"));
        }
        // 取最新的一个
        RiskDetectorRecordBaseDO latestRecord = latest.stream()
                .max(Comparator.comparing(RiskDetectorRecordBaseDO::getOccurTime)).orElse(null);
        if (latestRecord == null) {
            // 不存在，需要满足准入条件且不满足排除条件
            boolean entered = riskDetector.isEntered(detectContext) && !riskDetector.filter(detectContext);
            if (entered) {
                // 如果准入，创建新的
                latestRecord = factoryMap.get(recordType).init(detectContext);
            }
        } else {
            // 如果不为空
            // 先积累数据
            latestRecord.update(runtimeContextDO);
            // 做区域排除
            boolean filtered = riskDetector.filter(detectContext);
            // 再做校验
            detectResult = filtered ? DetectRecordStatusEnum.CANCELLED
                    : riskDetector.detect(detectContext, latestRecord);
            // 做计数的积累
            handleCounter(latestRecord, runtimeContextDO);
            // 不同校验结果不同处理
            switch (detectResult) {
                case PROCESSING:
                    break;
                case CONFIRMED:
                    RiskDetectConfirmDTO confirmDTO = RiskDetectConfirmDTO.builder()
                            .detectProcessContext(detectContext.getDetectProcessContext()).eveInfo(eveInfo)
                            .runtimeContext(runtimeContextDO).build();
                    latestRecord.confirm(confirmDTO, ignoreFields);
                    break;
                case CANCELLED:
                    latestRecord.cancel();
                    break;
            }
        }
        if (latestRecord != null) {
            // 保存
            repository.save(latestRecord);
        }
        return detectResult;
    }

    /**
     * 获取仓储
     * 
     * @param recordType
     * @return
     */
    public AbstractMapperSingleRepository getRepositoryByType(Class<?> recordType) {
        if (recordType == null) {
            return null;
        }
        return repoMap.get(recordType);

    }

    /**
     * 做计数的积累
     *
     * @param latestRecord
     * @param contextDO
     */
    private void handleCounter(RiskDetectorRecordBaseDO latestRecord, VehicleRuntimeInfoContextDO contextDO) {
        if (latestRecord == null) {
            return;
        }
        Date recordOccurTime = latestRecord.getOccurTime();
        Date recordNowDate = new Date();
        VehicleCounterInfoDO vehicleCounter = contextDO.getStagnationCounter();
        VehicleCounterInfoDO recordStagnationCounter;
        if (vehicleCounter == null) {
            // 如果不存在，或者结束在召回时间之前
            recordStagnationCounter = null;
        } else {
            // 存在 且 (未结束 或者 结束时间在召回时间之后)
            recordStagnationCounter = vehicleCounter.getSubCounter(recordOccurTime, recordNowDate);
        }
        // 更新，这里不担心覆盖
        latestRecord.setStagnationCounter(recordStagnationCounter);
    }

    /**
     * 根据action名获取Action
     *
     * @param name
     * @return
     */
    private RiskDetector getDetectorByName(String name) {
        return DETECTOR_MAP.get(name);
    }

    /**
     * 初始化仓储对应的bean
     */
    private void initializeRecordRepoBeanMap() {
        // 获取所有AbstractMapperSingleRepository的Bean
        Map<String, AbstractMapperSingleRepository> beans = applicationContext
                .getBeansOfType(AbstractMapperSingleRepository.class);
        for (AbstractMapperSingleRepository bean : beans.values()) {
            ResolvableType resolvableType = ResolvableType.forClass(bean.getClass())
                    .as(AbstractMapperSingleRepository.class);
            if (resolvableType.hasGenerics() && resolvableType.getGenerics().length >= 4) {
                // 获取第四个泛型参数的类型
                Class<?> fourthGenericType = resolvableType.getGeneric(3).resolve();
                if (fourthGenericType != null && RiskDetectorRecordBaseDO.class.isAssignableFrom(fourthGenericType)) {
                    // 检查第四个泛型参数是否继承自BaseClass
                    // 将第四个泛型参数的类型和Bean添加到Map中
                    repoMap.put(fourthGenericType, bean);
                }
            }
        }
    }

    /**
     * 初始化仓储对应的bean
     */
    private void initializeFactoryMap() {
        // 获取所有AbstractMapperSingleRepository的Bean
        Map<String, RiskDetectorRecordFactory> beans = applicationContext
                .getBeansOfType(RiskDetectorRecordFactory.class);
        for (RiskDetectorRecordFactory bean : beans.values()) {
            ResolvableType resolvableType = ResolvableType.forClass(bean.getClass())
                    .as(RiskDetectorRecordFactory.class);
            if (resolvableType.hasGenerics() && resolvableType.getGenerics().length >= 1) {
                // 获取第四个泛型参数的类型
                Class<?> fourthGenericType = resolvableType.getGeneric(0).resolve();
                if (fourthGenericType != null && RiskDetectorRecordBaseDO.class.isAssignableFrom(fourthGenericType)) {
                    // 检查第四个泛型参数是否继承自BaseClass
                    // 将第四个泛型参数的类型和Bean添加到Map中
                    factoryMap.put(fourthGenericType, bean);
                }
            }
        }
    }

    private void initDetector() {
        Map<String, RiskDetector> beansMap = this.applicationContext.getBeansOfType(RiskDetector.class);
        beansMap.values().forEach(item -> {
            DETECTOR_MAP.put(item.getClass().getSimpleName(), item);
        });
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class DetectorRecordQueryParamDTO {

        /**
         * 车辆
         */
        private String vin;

        /**
         * 状态
         */
        @InQuery(field = "status")
        private List<Integer> statusList;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectorParamDTO {

        private List<VehicleRuntimeInfoContextDO> vinRunTimeList;

        private List<VehicleEveInfoVTO> eveInfoList;

        /**
         * 是否为空
         * 
         * @return
         */
        public boolean isEmpty() {
            return CollectionUtils.isEmpty(vinRunTimeList);
        }
    }

}
