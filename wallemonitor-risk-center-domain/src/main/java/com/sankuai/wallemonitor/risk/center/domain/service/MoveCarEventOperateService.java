package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.api.request.MoveCarEventReportRequest;
import com.sankuai.wallemonitor.risk.center.api.vo.MoveCarEventStatusVO;

public interface MoveCarEventOperateService {

    /**
     * 根据车辆id获取车辆状态
     *
     * @param vehicleId
     * @return
     */
    MoveCarEventStatusVO getMoveCarStatusByVehicleId(String vehicleId);

    /**
     * 校验并创建工单
     *
     * @param request
     * @param openId
     * @throws Exception
     */
    void checkAndCreateMoveCarEvent(MoveCarEventReportRequest request, String openId) throws Exception;
}
