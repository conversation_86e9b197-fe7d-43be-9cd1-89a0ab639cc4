package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.response.VehicleDataInfoVO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.result.ISInParkingAreaResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.EventPlatSearchAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.PositionConvert;
import com.sankuai.wallemonitor.risk.center.infra.dto.LocationInfo;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleStatusChangeInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.LocationSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 停滞不当预检 - 是否在停车区域内
 */
@Slf4j
@Component
public class ISInParkingArea implements ISCheckAction<ISInParkingAreaResult> {

    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @Resource
    private PositionConvert positionDOConvert;

    @Resource
    private EventPlatSearchAdapter eventPlatSearchAdapter;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @ConfigValue(key = LionKeyConstant.LION_KEY_IS_IN_PARKING_AREA_IN_NO_CONTROL_DURATION, value = "", defaultValue = "0", allowBlankValue = true)
    private Integer inNoControlDuration;


    @Override
    public ISCheckActionResult<ISInParkingAreaResult> execute(ISCheckActionContext actionContext) {
        RiskCheckingQueueItemDO itemDO = actionContext.getItem();
        if (Objects.isNull(itemDO)) {
            return ISCheckActionResult.empty();
        }
        // 查询车辆最近一次驾驶模式变更
        VehicleStatusChangeInfoDTO<Integer> driveModeChange = eventPlatSearchAdapter.queryLatestDriveModeChange(
                itemDO.getVin());
        if (Objects.isNull(driveModeChange)) {
            log.error("ISInParkingArea,itemDO: {}", JacksonUtils.to(itemDO),
                    new Exception("预检项没有关联车辆最近一次驾驶模式变更"));
            return ISCheckActionResult.empty();
        }
        // 判断车辆最近一次驾驶模式变更是否在停车区
        Boolean resultByDriveMode = verifyInParkingAreaByLatestDriveMode(driveModeChange, itemDO);
        // 判断车辆最近一次定位是否在停车区
        String parkingId = verifyInParkingAreaByLatestLocation(itemDO);
        log.info("ISInParkingArea, vin:{}, caseId:{}, resultByDriveMode: {}, parkingId: {}", itemDO.getVin(),
                itemDO.getTmpCaseId(),
                resultByDriveMode, parkingId);
        if (resultByDriveMode && StringUtils.isNotBlank(parkingId)) {
            return ISCheckActionResult.<ISInParkingAreaResult>builder()
                    .categoryEnum(ISCheckCategoryEnum.IN_PARKING_AREA)
                    .actionResult(ISInParkingAreaResult.builder().matchParkingId(parkingId).build())
                    .build();
        }
        return ISCheckActionResult.empty();
    }

    /**
     * 判断车辆最近一次定位是否在停车区
     *
     * @param itemDO
     * @return
     */
    private String verifyInParkingAreaByLatestLocation(RiskCheckingQueueItemDO itemDO) {

        // 查询车辆最近一次定位数据源变更(oldValue = autocar_utm)
        VehicleStatusChangeInfoDTO<LocationInfo> locationInfoChange = eventPlatSearchAdapter.queryLatestAutoCarUtmLocationInfoChange(
                itemDO.getVin());
        if (Objects.isNull(locationInfoChange)) {
            log.warn(String.format("vin: %s, 查询不到最近一次定位数据源变更", itemDO.getVin()), new Exception());
            return null;
        }
        LocationInfo locationInfo = JacksonUtils.from(
                JacksonUtils.to(locationInfoChange.getBeforeChangeInfo()),
                new TypeReference<LocationInfo>() {
                });

        PositionDO lastAutoCarPosition = PositionDO.builder().latitude(Double.valueOf(locationInfo.getLatitude()))
                .longitude(Double.valueOf(locationInfo.getLongitude()))
                .coordinateSystem(CoordinateSystemEnum.WGS84).build();
        PositionDO positionDO = positionDOConvert.toPositionDO(lastAutoCarPosition, CoordinateSystemEnum.GCJ02);
        log.info("vin:{}, caseId:{}, locationInfoChange: {}, positionDO = {}", itemDO.getVin(),
                itemDO.getTmpCaseId(),
                JacksonUtils.to(locationInfoChange), JacksonUtils.to(positionDO));
        return safetyAreaRepository.verifyInParkingArea(positionDO);
    }

    /**
     * 判断车辆最近一次定位是否在停车区
     *
     * @return
     */
    private boolean verifyInParkingAreaByLatestDriveMode(VehicleStatusChangeInfoDTO<Integer> driveModeChange,
            RiskCheckingQueueItemDO itemDO) {
        // 变更前驾驶模式
        DriverModeEnum beforeDriverMode = DriverModeEnum.fromCode(driveModeChange.getBeforeChangeInfo());
        // 变更后驾驶模式
        DriverModeEnum afterDriverMode = DriverModeEnum.fromCode(driveModeChange.getAfterChangeInfo());

        Boolean isInParkingArea = false;
        // 当前为无控制，且变更前为unknown，则不需要计算时间 - 刚开机
        if (Objects.equals(beforeDriverMode, DriverModeEnum.UNKNOWN) && Objects.equals(afterDriverMode,
                DriverModeEnum.NO_CONTROL)) {
            isInParkingArea = true;
        }
        // 当前状态为无控制， 且最后一次驾驶模式变更时间要早于停滞时间 N 分钟以上
        else if (Objects.equals(afterDriverMode, DriverModeEnum.NO_CONTROL)
                && DatetimeUtil.diff(itemDO.getOccurTime(), driveModeChange.getChangeTime()) / 1000 / 60
                >= inNoControlDuration) {
            isInParkingArea = true;
        }

        return isInParkingArea;
    }

    /**
     * 获取车辆历史数据
     *
     * @param vin
     * @param startTime
     * @param endTime
     * @return
     */
    private VehicleDataInfoVO getVehicleDataInfoVO(String vin, Long startTime, Long endTime) {
        List<VehicleDataInfoVO> vehicleDataInfoVOList = vehicleAdapter.queryVehicleHistoryDataFromEveReplay(
                vin, startTime, endTime);
        return CollectionUtils.isEmpty(vehicleDataInfoVOList) ? null : vehicleDataInfoVOList.get(0);
    }

    /**
     * 停车区域判定
     *
     * @param vehicleDataInfoVO
     * @return
     */
    private Boolean verifyInParkingArea(VehicleDataInfoVO vehicleDataInfoVO) {
        // 格式转化
        PositionDO positionDO = PositionDO.builder().longitude(vehicleDataInfoVO.getLongitude())
                .latitude(vehicleDataInfoVO.getLatitude()).coordinateSystem(CoordinateSystemEnum.WGS84)
                .build();
        // 停车区域判定
        String parkingAreaId = safetyAreaRepository.verifyInParkingArea(
                positionDOConvert.toPositionDO(positionDO, CoordinateSystemEnum.GCJ02));
        return StringUtils.isNotBlank(parkingAreaId);
    }

    /**
     * 停车区域判定策略V2
     *
     * @param itemDO
     */
    private void isInParkingAreaV2(RiskCheckingQueueItemDO itemDO, Date driveModeChangeTime) {
        try {
            // 1 计算真实停滞时间
            Date startTime = itemDO.getOccurTime();
            // 2 查询真实停滞时刻的车辆数据 【startTime， startTime + 1】
            VehicleDataInfoVO vehicleDataInfoVO = getVehicleDataInfoVO(itemDO.getVin(), startTime.getTime() / 1000,
                    DatetimeUtil.addMinutes(startTime, 1) / 1000);

            if (Objects.isNull(vehicleDataInfoVO)) {
                return;
            }
            // 2 停车场策略V2判定 - 根据定位数据源判定
            Boolean isInParkingArea = isInParkingAreaByLocation(itemDO.getVin(), vehicleDataInfoVO,
                    driveModeChangeTime);
            log.info("ISInParkingArea, tmpCaseId = {},startTime = {}, positionSource = {}, isInParkingArea = {}",
                    itemDO.getTmpCaseId(), startTime, vehicleDataInfoVO.getPositionSource(),
                    isInParkingArea);

        } catch (Exception e) {
            log.error("停车区域判定策略V2异常", e);
        }
    }

    /**
     * 根据定位数据源判断停车区
     *
     * @param vin
     * @param vehicleDataInfoVO
     * @param driveModeChangeTime
     * @return
     */
    private Boolean isInParkingAreaByLocation(String vin, VehicleDataInfoVO vehicleDataInfoVO,
            Date driveModeChangeTime) {
        String positionSource = vehicleDataInfoVO.getPositionSource();
        Boolean isInParkingArea = false;

        if (Objects.equals(positionSource, LocationSourceEnum.AUTO_CAR_UTM.getSource())) {// 1 当停滞时刻的定位数据源为高精融合定位
            isInParkingArea = verifyInParkingArea(vehicleDataInfoVO);
        } else if (LocationSourceEnum.isGnssLocation(positionSource)) {// 2 当停滞时刻的数据源为 GNSS定位
            //获取最近一次驾驶模式变更时的定位数据 【driveModeChangeTime， driveModeChangeTime + 1】
            VehicleDataInfoVO temp = getVehicleDataInfoVO(vin,
                    driveModeChangeTime.getTime() / 1000,
                    DatetimeUtil.addMinutes(driveModeChangeTime, 1) / 1000);
            isInParkingArea = Optional.ofNullable(temp).map(this::verifyInParkingArea).orElse(false);

        } else if (Objects.equals(positionSource, LocationSourceEnum.AUTO_CAR_UTM_CACHED.getSource())) { // 3 缓存定位
            // 暂时忽略
        }
        return isInParkingArea;
    }
}
