package com.sankuai.wallemonitor.risk.center.domain.process;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walledelivery.basic.client.response.deliverer.DelivererResponse;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DelivererQueryThriftServiceAdaptor;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventNatureEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventRelatedFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventRelatedDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventDetailRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventRelatedRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventRelatedDOQueryParamDTO;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NegativePublicEventStatusUpdateProcess implements DomainEventProcess {

    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    @Resource
    private NegativePublicEventRelatedRepository relatedRepository;

    @Resource
    private NegativePublicEventDetailRepository detailRepository;

    @Resource
    private DelivererQueryThriftServiceAdaptor delivererQueryThriftServiceAdaptor;

    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_STATUS_UPDATE_NOTICE_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != NegativePublicEventDO.class) {
            return true;
        }
        log.info("NegativePublicEventStatusUpdateProcess#process, eventDTO:{}", eventDTO);
        //状态发生变化的
        List<NegativePublicEventDO> eventDOList = ((DomainEventChangeDTO<NegativePublicEventDO>) eventDTO).getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status")
        );
        log.info("NegativePublicEventStatusUpdateProcess#process, eventDOList:{}", eventDOList);
        if (CollectionUtils.isEmpty(eventDOList)) {
            return true;
        }
        return handleProcessMsg(eventDOList);
    }

    /**
     * 处理消息
     *
     * @param eventDOList
     * @return
     */
    public boolean handleProcessMsg(List<NegativePublicEventDO> eventDOList) {
        eventDOList.forEach(eventDO -> {
            try {
                //发送大象消息
                sendDxMsg(eventDO);
            } catch (Exception e) {
                log.error("handleProcessMsg# error", e);
            }
        });
        return true;
    }

    /**
     * 发送大象消息
     *
     * @param eventDO
     */
    private void sendDxMsg(NegativePublicEventDO eventDO) {
        // 需要根据状态的变更进行不同的推送
        switch (eventDO.getStatus()) {
            case CREATED:
                break;
            case LOCATED:
                buildDetermineNatureNotice(eventDO);
                break;
            case CAUSE_IDENTIFIED:
                buildDetermineReasonNotice(eventDO);
                break;
            case DISPOSED:
                buildHandleNotice(eventDO);
                break;
        }
    }

    /**
     * 构建确定事件性质通知
     *
     * @param eventDO
     */
    private void buildDetermineNatureNotice(NegativePublicEventDO eventDO) {
        NegativePublicEventDetailDO detailDO = detailRepository.getByEventId(eventDO.getEventId());
        CheckUtil.isNotNull(detailDO, "事件详情不能为空");
        // 问题分类字段进行拼接
        String categoryStr = detailDO.getCategory();
        if (StringUtils.isNotBlank(detailDO.getOtherCategoryDesc())) {
            categoryStr += "/" + detailDO.getOtherCategoryDesc();
        }
        String vinStr = buildVinStr(eventDO);
        String text = "定位详细信息如下\n"
                + "【事件标题】:" + (StringUtils.isBlank(detailDO.getTitle()) ? CommonConstant.EMPTY
                : detailDO.getTitle()) + "\n"
                + "【事件性质】:" + (detailDO.getNature() == null ? CommonConstant.EMPTY : detailDO.getNature().getDesc())
                + "\n"
                + "【问题分类】:" + (StringUtils.isBlank(categoryStr) ? CommonConstant.EMPTY : categoryStr) + "\n"
                + "【车辆】:" + (StringUtils.isBlank(vinStr) ? CommonConstant.EMPTY : vinStr) + "\n"
                + "【情况说明】:" + (StringUtils.isBlank(detailDO.getConditionDesc()) ? CommonConstant.EMPTY
                : detailDO.getConditionDesc());
        dxNoticeAdapter.sendDxMessage(Long.valueOf(eventDO.getGroupId()), text);
    }

    /**
     * 构建确定原因通知
     *
     * @param eventDO
     */
    private void buildDetermineReasonNotice(NegativePublicEventDO eventDO) {
        NegativePublicEventDetailDO detailDO = detailRepository.getByEventId(eventDO.getEventId());
        CheckUtil.isNotNull(detailDO, "事件详情不能为空");
        String reasonStr = buildReasonStr(eventDO, detailDO);
        String text = "定因详细信息如下\n"
                + "【问题归因】:" + (StringUtils.isBlank(reasonStr) ? CommonConstant.EMPTY : reasonStr) + "\n"
                + "【问题排查结果】:" + (StringUtils.isBlank(detailDO.getCheckResult()) ? CommonConstant.EMPTY
                : detailDO.getCheckResult());
        dxNoticeAdapter.sendDxMessage(Long.valueOf(eventDO.getGroupId()), text);
    }

    /**
     * 构建处置通知
     *
     * @param eventDO
     */
    private void buildHandleNotice(NegativePublicEventDO eventDO) {
        NegativePublicEventDetailDO detailDO = detailRepository.getByEventId(eventDO.getEventId());
        CheckUtil.isNotNull(detailDO, "事件详情不能为空");
        String text = "";
        //  因为当事件被定位为正面/旧闻时会直接变更状态为已处置，跳过了定因/定位
        //  所以当事件性质为正面/旧闻时，不需要发送处置信息，只要发送缺少的定位信息即可
        if (NegativePublicEventNatureEnum.isNoNeedSendHandleInfo(detailDO.getNature())) {
            String categoryStr = detailDO.getCategory();
            if (StringUtils.isNotBlank(detailDO.getOtherCategoryDesc())) {
                categoryStr += "/" + detailDO.getOtherCategoryDesc();
            }
            String vinStr = buildVinStr(eventDO);
            text = "定位详细信息如下\n"
                    + "【事件标题】:" + (StringUtils.isBlank(detailDO.getTitle()) ? CommonConstant.EMPTY
                    : detailDO.getTitle()) + "\n"
                    + "【事件性质】:" + (detailDO.getNature() == null ? CommonConstant.EMPTY
                    : detailDO.getNature().getDesc()) + "\n"
                    + "【问题分类】:" + (StringUtils.isBlank(categoryStr) ? CommonConstant.EMPTY : categoryStr) + "\n"
                    + "【车辆】:" + (StringUtils.isBlank(vinStr) ? CommonConstant.EMPTY : vinStr) + "\n"
                    + "【情况说明】:" + (StringUtils.isBlank(detailDO.getConditionDesc()) ? CommonConstant.EMPTY
                    : detailDO.getConditionDesc());
        } else {
            text = "处置详细信息如下\n"
                    + "【解决程度】:" + (detailDO.getHandleDegree() == null ? CommonConstant.EMPTY
                    : detailDO.getHandleDegree().getDesc()) + "\n"
                    + "【处置结果说明】:" + (StringUtils.isBlank(detailDO.getHandleResultDesc()) ? CommonConstant.EMPTY
                    : detailDO.getHandleResultDesc());
        }
        dxNoticeAdapter.sendDxMessage(Long.valueOf(eventDO.getGroupId()), text);

    }

    /**
     * 构建车辆信息
     *
     * @param eventDO
     * @return
     */
    private String buildVinStr(NegativePublicEventDO eventDO) {
        // 查询关联表
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(eventDO.getEventId())
                .fieldName(NegativePublicEventRelatedFieldEnum.RELATED_VINS.getName()).build();
        List<NegativePublicEventRelatedDO> relatedDOList = relatedRepository.queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(relatedDOList)) {
            return "";
        }
        // 获取vin列表
        List<String> vinList = relatedDOList.stream().map(NegativePublicEventRelatedDO::getFieldValue).collect(
                Collectors.toList());
        // 调用运力接口查询车辆的关联信息
        List<DelivererResponse> delivererResponseList = delivererQueryThriftServiceAdaptor.getDelivererResponseByVinList(
                vinList);

        // 构建展示信息 MT00123/S20-123
        return delivererResponseList.stream()
                .map(delivererResponse -> delivererResponse.getName() + "/" + delivererResponse.getAccount()
                ).collect(Collectors.joining(","));
    }

    /**
     * 构建原因信息
     *
     * @param eventDO
     * @return
     */
    private String buildReasonStr(NegativePublicEventDO eventDO, NegativePublicEventDetailDO detailDO) {
        // 查询关联表
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(eventDO.getEventId())
                .fieldName(NegativePublicEventRelatedFieldEnum.REASON.getName()).build();
        List<NegativePublicEventRelatedDO> relatedDOList = relatedRepository.queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(relatedDOList)) {
            return "";
        }
        // 构建输出
        return relatedDOList.stream()
                .map(relatedDO -> {
                    String fieldValue = relatedDO.getFieldValue();
                    return "其他".equals(fieldValue) ? fieldValue + "/" + detailDO.getOtherReasonDesc() : fieldValue;
                })
                .collect(Collectors.joining(","));
    }


}
