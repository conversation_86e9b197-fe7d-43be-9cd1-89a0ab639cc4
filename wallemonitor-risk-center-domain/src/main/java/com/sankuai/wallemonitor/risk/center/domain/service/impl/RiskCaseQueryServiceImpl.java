package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseQueryService;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCaseMapper;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RiskCaseQueryServiceImpl implements RiskCaseQueryService {

    /**
     * 风险事件仓储
     */
    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private RiskCaseMapper riskCaseMapper;

    @ConfigValue(key = LionKeyConstant.LION_KEY_VEHICLE_RISK_STATUS_QUERY_TIME_RANGE, value = "", defaultValue = "24", allowBlankValue = true)
    private Integer vehicleRiskStatusQueryRange;


    /**
     * 查询待处置的风险事件
     *
     * @return
     */
    @Override
    public List<RiskCaseDO> queryRiskCaseDispose(Date beginTime, Date endTime) {
        RiskCaseDOQueryParamDTO paramDTO = RiskCaseDOQueryParamDTO.builder().build();
        // 设置状态
        paramDTO.setStatusList(RiskCaseStatusEnum.getUnTerminal());
        // 设置创建时间范围
        TimePeriod createTimeRange = new TimePeriod();
        createTimeRange.setBeginDate(beginTime);
        createTimeRange.setEndDate(endTime);
        paramDTO.setCreateTimeRange(createTimeRange);
        // 设置呼叫云控状态
        paramDTO.setMrmCalledList(RiskCaseMrmCalledStatusEnum.getNotMrmCalledList());
        return riskCaseRepository.queryByParam(paramDTO);
    }

    /**
     * 根据vin查询风险事件
     *
     * @param vinList
     * @param source
     * @param type
     * @return
     */
    @Override
    public List<RiskCaseInfoDTO> queryRiskCaseByVinList(List<String> vinList, Integer source, Integer type) {

        // 1 先查询车辆对应的风险事件实体
        Date now = new Date();
        RiskCaseDOQueryParamDTO riskCaseDOQueryParamDTO = RiskCaseDOQueryParamDTO.builder().vinList(vinList)
                .leftJoinRelation(true)
                .source(source).type(type)
                // 只查询近两天内的风险事件
                .createTimeCreateTo(DatetimeUtil.getBeforeTime(now, TimeUnit.HOURS, vehicleRiskStatusQueryRange))
                // 只查询发起呼叫云控的风险事件
                .mrmCalledList(Arrays.asList(RiskCaseMrmCalledStatusEnum.CALLING.getCode()))
                .callMrmReasonList(Arrays.asList("", "10086"))
                .leftJoinRelation(true)
                .build();
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(riskCaseDOQueryParamDTO);
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return Collections.emptyList();
        }
        log.info("queryRiskCaseByVinList riskCaseDOList:{}", JacksonUtils.to(riskCaseDOList));
        // 2 构建 caseId -> vin 的映射map
        RiderCaseVehicleRelationDOParamDTO riskCaseVehicleRelationDOParamDTO = RiderCaseVehicleRelationDOParamDTO.builder()
                .vinList(vinList)
                // 同样只查询近两天内的数据
                .createTimeGrateTo(DatetimeUtil.getBeforeTime(now, TimeUnit.HOURS, vehicleRiskStatusQueryRange))
                .build();
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                riskCaseVehicleRelationDOParamDTO);
        Map<String, String> caseIdToVinMap = riskCaseVehicleRelationDOList.stream()
                .collect(Collectors.toMap(
                        RiskCaseVehicleRelationDO::getCaseId,
                        RiskCaseVehicleRelationDO::getVin,
                        (existing, replacement) -> existing
                ));

        // 3 组装响应,获取 vin -> 风险事件实体的 关系
        List<RiskCaseInfoDTO> riskCaseInfoDTOList = new ArrayList<>();
        for (RiskCaseDO riskCaseDO : riskCaseDOList) {
            String caseId = riskCaseDO.getCaseId();
            String vin = caseIdToVinMap.get(caseId);
            if (StringUtils.isBlank(vin)) {
                continue;
            }
            riskCaseInfoDTOList.add(buildRiskCaseInfoDO(riskCaseDO, vin));
        }
        log.info("queryRiskCaseByVinList riskCaseInfoDOList:{}", JacksonUtils.to(riskCaseInfoDTOList));
        return riskCaseInfoDTOList;
    }

    /**
     * 构建风险事件信息
     *
     * @param riskCaseDO
     * @param vin
     * @return
     */
    private RiskCaseInfoDTO buildRiskCaseInfoDO(RiskCaseDO riskCaseDO, String vin) {
        RiskCaseInfoDTO riskCaseInfoDTO = new RiskCaseInfoDTO();
        riskCaseInfoDTO.setCaseId(riskCaseDO.getCaseId());
        riskCaseInfoDTO.setVin(vin);
        riskCaseInfoDTO.setId(riskCaseDO.getId());
        riskCaseInfoDTO.setSource(Optional.ofNullable(riskCaseDO.getSource())
                .map(RiskCaseSourceEnum::getCode)
                .orElse(null));
        riskCaseInfoDTO.setType(Optional.ofNullable(riskCaseDO.getType())
                .map(RiskCaseTypeEnum::getCode)
                .orElse(null));
        riskCaseInfoDTO.setStatus(Optional.ofNullable(riskCaseDO.getStatus())
                .map(RiskCaseStatusEnum::getCode)
                .orElse(null));
        riskCaseInfoDTO.setPlaceCode(riskCaseDO.getPlaceCode());
        riskCaseInfoDTO.setEventId(riskCaseDO.getEventId());
        riskCaseInfoDTO.setMessageId(riskCaseDO.getMessageId());
        riskCaseInfoDTO.setMessageVersion(riskCaseDO.getMessageVersion());
        riskCaseInfoDTO.setOccurTime(riskCaseDO.getOccurTime());
        riskCaseInfoDTO.setCloseTime(riskCaseDO.getCloseTime());
        riskCaseInfoDTO.setExtInfo(JacksonUtils.to(riskCaseDO.getExtInfo()));

        riskCaseInfoDTO.setMrmCalled(Optional.ofNullable(riskCaseDO.getMrmCalled())
                .map(RiskCaseMrmCalledStatusEnum::getCode)
                .orElse(null));
        riskCaseInfoDTO.setIsDeleted(riskCaseDO.getIsDeleted());

        return riskCaseInfoDTO;
    }
}
