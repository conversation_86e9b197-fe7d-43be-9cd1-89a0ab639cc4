package com.sankuai.wallemonitor.risk.center.domain.result;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseUpdatedResultDTO {


    /**
     * 更新过后的风险case
     */
    private RiskCaseDO riskCaseDO;

    /**
     * 更新后的关联关系
     */
    private List<RiskCaseVehicleRelationDO> relationDOList;

}
