package com.sankuai.wallemonitor.risk.center.domain.result;

import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ISTrafficLightResult implements Serializable {

    private static final long serialVersionUID = 1L;

    private TrafficLightTypeEnum nowTrafficLightType;

    private String junctionId;

    private Double distanceToJunction;

    private Double nearByJunction;

    /**
     * 前方有障碍物
     */
    private boolean hasFrontObstacle;
    /**
     * 是否跨线
     */
    private boolean crossLine;
    /**
     * 是否远离路口
     */
    private boolean awayFromJunction;

    /**
     * 到前方人行横道的距离
     */
    private Double distance2CrossWalk;

    /**
     * 是否远离人行横道或者路口
     */
    private boolean awayFromJunctionOrCrossWalk;
}
