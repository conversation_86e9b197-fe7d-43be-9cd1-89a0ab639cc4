package com.sankuai.wallemonitor.risk.center.domain.strategy.detector.impl;

import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetector;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskRetrogradeRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 逆行预检探测器
 */
@Slf4j
@Service
public class RetrogradeDetector extends RiskDetector<RiskRetrogradeRecordDO> {



    /**
     * 获取检测的风险类型
     *
     * @return
     */
    @Override
    public RiskCaseTypeEnum getDetectRiskType() {
        return RiskCaseTypeEnum.RETROGRADE;
    }



}
