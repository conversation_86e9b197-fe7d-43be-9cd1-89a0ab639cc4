package com.sankuai.wallemonitor.risk.center.domain.process;


import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.TRIGGER_DYNAMIC_RECHECK_WHEN_TURNED_GREEN_PROCESSOR_ENTRY;

import com.google.common.collect.Sets;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskMarkService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskMarkRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCheckQueueQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 当变为绿灯的时候，触发动态轮次预检
 * <p/>
 * 相关逻辑：
 * <p/>
 * 1. 读取变更为绿灯的车辆信息
 * <p/>
 * 2. 查询未发生预检的ISChecking
 * <p/>
 * 3. 做动态预检，在动态预检过程中，产出的信息将不再做动态预检
 * <p/>
 */
@Slf4j
@Component
public class TrafficChangeToGreenTriggerDynamicCheckingProcess implements DomainEventProcess {

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private SquirrelAdapter squirrelAdapter;

    @Resource
    private RiskCheckQueueRepository checkQueueRepository;

    @Resource
    private RiskMarkRepository riskMarkRepository;

    @Resource
    private RiskMarkService riskMarkService;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;

    @Resource
    private LockUtils lockUtils;

    private static final String TRAFFIC_ACTION_NAME = "ISTrafficLightAction";


    @Override
    @ZebraForceMaster
    @OperateEnter(TRIGGER_DYNAMIC_RECHECK_WHEN_TURNED_GREEN_PROCESSOR_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != VehicleRuntimeInfoContextDO.class) {
            return true;
        }
        DomainEventChangeDTO<VehicleRuntimeInfoContextDO> typedDomainEvent = (DomainEventChangeDTO<VehicleRuntimeInfoContextDO>) eventDTO;
        //取出变为绿灯的车辆
        List<VehicleRuntimeInfoContextDO> trafficLightTypeChanged = typedDomainEvent.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "trafficLightType"))
                // 过滤出变为绿灯的车辆
                .stream().filter(contextDO -> TrafficLightTypeEnum.GREEN.equals(contextDO.getTrafficLightType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trafficLightTypeChanged)) {
            return true;
        }
        // 取相关的车辆
        List<String> vinList = trafficLightTypeChanged.stream().map(VehicleRuntimeInfoContextDO::getVin)
                .collect(Collectors.toList());
        // 查询db里面，还未结束的标注项
        List<RiskCheckingQueueItemDO> vinRelatedCheckingQueueItemList = checkQueueRepository.queryByParam(
                RiskCheckQueueQueryParam.builder()
                        //取停滞不前
                        .typeList(Collections.singletonList(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode()))
                        .vinList(vinList)
                        // 取过去3分钟
                        .createTimeCreateThan(DatetimeUtil.getNSecondsBeforeDateTime(new Date(),
                                3 * DateTimeConstant.ONE_MINUTE_SECOND))
                        .build());
        if (CollectionUtils.isEmpty(vinRelatedCheckingQueueItemList)) {
            return true;
        }

        try {
            handleTriggerMessageForChecking(vinRelatedCheckingQueueItemList);
        } catch (Exception e) {
            log.error("handleTriggerMessageForChecking failed.", e);
        }

        return handleTriggerMessageForMark(vinRelatedCheckingQueueItemList);
    }

    /**
     * 触发动态预检
     *
     * @param checkingQueueItemList
     */
    private void handleTriggerMessageForChecking(List<RiskCheckingQueueItemDO> checkingQueueItemList) {
        RiskAutoCheckConfigDTO config = lionConfigRepository.getRiskCheckingConfig();
        if (config == null) {
            return;
        }
        lockUtils.batchLockCanWait(LockKeyPreUtil.buildLockKeys(checkingQueueItemList), () -> {
            List<String> tempCaseIdList = checkingQueueItemList.stream().map(RiskCheckingQueueItemDO::getTmpCaseId)
                    .collect(Collectors.toList());
            List<RiskCheckingQueueItemDO> newestCheckingQueueItemList = checkQueueRepository.queryByParam(
                    RiskCheckQueueQueryParam.builder().tmpCaseIdList(tempCaseIdList).build());
            List<RiskCheckingQueueItemDO> triggeredItem = newestCheckingQueueItemList.stream()
                    .filter(x -> !RiskQueueStatusEnum.isTerminatedStatus(x.getStatus()) && !x.getChecking())
                    .map(x -> updateNextRoundTime(x, config))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            log.info("handleTriggerMessageForChecking triggeredItemList: {}", JacksonUtils.to(triggeredItem));
            if (CollectionUtils.isEmpty(triggeredItem)) {
                return;
            }
            checkQueueRepository.batchSave(triggeredItem);
        });
    }

    /**
     * 设置预检项延迟时长
     *
     * @param checkingQueueItemDO
     * @param config
     * @return
     */
    private RiskCheckingQueueItemDO updateNextRoundTime(RiskCheckingQueueItemDO checkingQueueItemDO,
            RiskAutoCheckConfigDTO config) {
        VehicleRuntimeInfoContextDO contextDO = vehicleRuntimeInfoContextRepository.getFullByVin(
                checkingQueueItemDO.getVin());
        Long delaySeconds = config.getDynamicCheckDelayTime(TRAFFIC_ACTION_NAME, ISCheckCategoryEnum.IN_JUNCTION,
                contextDO);
        if (delaySeconds == null) {
            return null;
        }
        Date newNextRoundTime = DatetimeUtil.getNSecondsAfterDateTime(new Date(), delaySeconds.intValue());
        checkingQueueItemDO.setNextRoundTime(newNextRoundTime);
        checkingQueueItemDO.setNextRoundDynamic(true);
//        checkingQueueItemDO.setChecking(true);
        return checkingQueueItemDO;
    }

    /**
     * 触发动态轮次
     *
     * @param vinNotTerminalQueueItem
     * @return
     */
    private boolean handleTriggerMessageForMark(List<RiskCheckingQueueItemDO> vinNotTerminalQueueItem) {
        Map<String, RiskAutoCheckConfigDTO> autoMarkConfigDTOList = lionConfigRepository.getAllAutoMarkConfig();
        if (MapUtils.isEmpty(autoMarkConfigDTOList)) {
            return true;
        }

        vinNotTerminalQueueItem.forEach(
                //发起一次动态检查
                checkingQueueItemDO -> {
                    try {
                        String strandingCaseId = checkingQueueItemDO.getEventId();
                        RiskCaseDO strandingCase = riskCaseRepository.getByCaseId(strandingCaseId);
                        if (strandingCase == null) {
                            return;
                        }
                        // 并行处理
                        ParallelExecutor.executeParallelTasks("multiAutoMark", autoMarkConfigDTOList,
                                (version, autoMarkConfigDTO) -> {
                                    lockUtils.batchLockNoWait(LockKeyPreUtil.buildMultiMarkWithEventId(
                                            Sets.newHashSet(strandingCase.getEventId()), version), () -> {
                                                if (!autoMarkConfigDTO.isInMarkTime()) {
                                                    // 不在时段内不进行处理
                                                    return;
                                                }
                                                RiskCheckingQueueItemDO latestQueueItemDO = riskMarkRepository
                                                        .getMarkItem(checkingQueueItemDO.getTmpCaseId(),
                                                                version);
                                                if (latestQueueItemDO == null) {
                                                    // 如果已经清除，无需发送消息
                                                    return;
                                                }
                                                VehicleRuntimeInfoContextDO contextDO = vehicleRuntimeInfoContextRepository
                                                        .getFullByVin(checkingQueueItemDO.getVin());
                                                // 发送动态轮次的预检消息
                                                riskMarkService.sendMarkMessage(latestQueueItemDO,
                                                        autoMarkConfigDTO.getDynamicCheckDelayTime(TRAFFIC_ACTION_NAME,
                                                                ISCheckCategoryEnum.IN_JUNCTION, contextDO),
                                                        true,
                                                        // 获取版本
                                                        version);
                                            });
                                });
                    } catch (Exception e) {
                        log.warn("变绿灯时刻正在校验中，触发动态轮次预检失败", e);
                    }
                });
        return true;
    }

}
