package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.RISK_CASE_CALCULATE_SEAT_INTERVENTION_TIME_PROCESS;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.EventPlatSearchAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.CalculateSeatInterventionTimeProcessConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleStatusChangeInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskCaseCalculateSeatInterventionTimeProcess implements
        DomainEventProcess {

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private EventPlatSearchAdapter eventPlatSearchAdapter;

    @ConfigValue(key = LionKeyConstant.LION_KEY_CALCULATE_SEAT_INTERVENTION_TIME_PROCESS_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private CalculateSeatInterventionTimeProcessConfigDTO configDTO;


    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(RISK_CASE_CALCULATE_SEAT_INTERVENTION_TIME_PROCESS)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        CheckUtil.isNotNull(eventDTO, "eventDTO不能为空");
        if (eventDTO.getDomainClass() == RiskCaseDO.class) {
            return handleProcessRisk((DomainEventChangeDTO<RiskCaseDO>) eventDTO);
        }
        return true;
    }

    /**
     * 处理风险事件
     *
     * @param eventDTO
     * @return
     */
    private boolean handleProcessRisk(DomainEventChangeDTO<RiskCaseDO> eventDTO) {
        //TODO 只要是状态为取消的case都查询介入时间，添加上类型控制
        if (CollectionUtils.isEmpty(configDTO.getCaseTypeList()) || CollectionUtils.isEmpty(
                configDTO.getCaseSourceList()) || Objects.isNull(
                configDTO.getQueryRangeDelaySeconds())) {
            log.error("RiskCaseCalculateSeatInterventionTimeProcess.handleProcessRisk config error");
            return true;
        }
        Set<Integer> caseTypeSet = new HashSet<>(configDTO.getCaseTypeList());
        Set<Integer> caseSourceSet = new HashSet<>(configDTO.getCaseSourceList());
        List<RiskCaseDO> riskCaseDOList = eventDTO.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                .stream().filter(riskCaseDO -> RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus())
                        && caseTypeSet.contains(riskCaseDO.getType().getCode())
                        && caseSourceSet.contains(riskCaseDO.getSource().getCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return true;
        }
        //这里是异步处理，所以不信任变更的实体的桩体
        handleUpdateMessage(riskCaseDOList);
        return true;

    }

    /**
     * 处理消息通知
     *
     * @param riskCaseDOList
     * @return
     */
    private void handleUpdateMessage(List<RiskCaseDO> riskCaseDOList) {
        // 并发处置
        ParallelExecutor.executeParallelTasks("vehicle_runtime_info_biz_status", riskCaseDOList, riskCaseDO -> {
            try {
                lockUtils.batchLockNoWait(
                        //对eventId加锁
                        LockKeyPreUtil.buildKeyWithEventId(Collections.singleton(riskCaseDO.getEventId())),
                        () -> {
                            RiskCaseVehicleRelationDO relationDO = riskCaseVehicleRelationRepository.getByCaseId(
                                    riskCaseDO.getCaseId());
                            if (Objects.isNull(relationDO)) {
                                return;
                            }
                            // 根据 vin 开始时间 结束时间 查询驾驶模式的变更，获取介入时间
                            // 结束时间 = 关单时间 + 延迟时间 （考虑到信息链路可能存在一定程度的延迟）
                            Date endTime = DatetimeUtil.getAfterTime(riskCaseDO.getCloseTime(), TimeUnit.SECONDS,
                                    configDTO.getQueryRangeDelaySeconds());
                            VehicleStatusChangeInfoDTO changeInfoDTO = eventPlatSearchAdapter.queryLatestUpdateToMrmDriveModeChange(
                                    relationDO.getVin(), riskCaseDO.getOccurTime(), endTime);
                            if (Objects.isNull(changeInfoDTO)) {
                                return;
                            }
                            relationDO.setSeatInterventionTime(changeInfoDTO.getChangeTime());
                            riskCaseVehicleRelationRepository.save(relationDO);
                        });

            } catch (Exception e) {
                log.error("RiskCaseCalculateSeatInterventionTimeProcess.handleUpdateMessage error", e);
            }
        });
    }

}
