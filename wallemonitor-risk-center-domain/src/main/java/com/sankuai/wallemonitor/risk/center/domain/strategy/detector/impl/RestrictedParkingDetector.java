package com.sankuai.wallemonitor.risk.center.domain.strategy.detector.impl;

import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetector;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskRestrictedParkingRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 禁停区域风险检测
 */
@Slf4j
@Component
public class RestrictedParkingDetector extends RiskDetector<RiskRestrictedParkingRecordDO> {

    @Override
    public RiskCaseTypeEnum getDetectRiskType() {
        return RiskCaseTypeEnum.RESTRICTED_PARKING;
    }

}