package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.RISK_CASE_AGGREGATE_ALERT_PROCESS_ENTRY;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskAggregateAlertService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.EntityKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 风险事件聚合告警处理器
 * 负责在风险事件保存后触发聚合告警检查
 */
@Component
@Slf4j
public class RiskCaseAggregateAlertProcess implements DomainEventProcess {

    @Resource
    private RiskAggregateAlertService riskAggregateAlertService;

    /**
     * 处理领域事件
     *
     * @param eventDTO 领域事件变更DTO
     * @return 处理是否成功
     * @throws TException 处理异常
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(RISK_CASE_AGGREGATE_ALERT_PROCESS_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != RiskCaseDO.class) {
            log.debug("非风险事件类型，跳过聚合告警处理");
            return true;
        }
        try {
            DomainEventChangeDTO<RiskCaseDO> typedDomainEvent = (DomainEventChangeDTO<RiskCaseDO>) eventDTO;

            // 过滤新增的风险事件列表
            List<RiskCaseDO> riskCaseList = new ArrayList<>(typedDomainEvent.getBySingleField(
                    entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status")
                            && Objects.equals(entityFieldChangeRecordDTO.getChangeType(),
                            EntityKeyConstant.ENTITY_EMPTY_TO_VALUE)));
            if (CollectionUtils.isEmpty(riskCaseList)) {
                return true;
            }
            // 处理聚合告警
            riskAggregateAlertService.processAggregateAlert(riskCaseList);
            return true;
        } catch (Exception e) {
            log.error("处理风险事件聚合告警时发生异常，traceId: {}", eventDTO.getTraceId(), e);
            return true;
        }
    }
} 