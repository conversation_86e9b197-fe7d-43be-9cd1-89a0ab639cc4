package com.sankuai.wallemonitor.risk.center.domain.process;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.compare.EntityFieldChangeRecordDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 标注转换（停滞的标注到停滞不当，停滞不当的标注到停滞）
 */
@Component
@Slf4j
public class CaseMarkTransformProcess implements DomainEventProcess {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private LockUtils lockUtils;



    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.MARK_TRANSFORM_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != CaseMarkInfoDO.class) {
            return true;
        }
        Boolean transferMark = lionConfigRepository.isTransferMarkSwitchOn();
        if (!transferMark) {
            // 未开启时，不做处理
            return true;
        }
        //获取sub_category 变更的停滞的case
        DomainEventChangeDTO<CaseMarkInfoDO> changedSubCategoryStrandingDomainEvent = (DomainEventChangeDTO<CaseMarkInfoDO>)eventDTO;
        // 过滤出状态为结束的停滞事件
        // 标注发生变化
        List<CaseMarkInfoDO> changedCaseMarkInfo = new ArrayList<>(
                changedSubCategoryStrandingDomainEvent.getBySingleField(
                        // 任意字段发生变化
                        EntityFieldChangeRecordDTO::isChanged));
        if (CollectionUtils.isEmpty(changedCaseMarkInfo)) {
            return true;
        }
        //按照类型区分
        Map<RiskCaseTypeEnum, List<RiskCaseDO>> changedCaseMap = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder()
                        .caseIdList(
                                changedCaseMarkInfo.stream().map(CaseMarkInfoDO::getCaseId)
                                        .collect(Collectors.toList()))
                        .build()
        ).stream().collect(Collectors.groupingBy(RiskCaseDO::getType));
        // 取停滞的标注
        List<RiskCaseDO> changedStrandingCaseList = changedCaseMap.getOrDefault(RiskCaseTypeEnum.STRANDING,
                Collections.emptyList());
        boolean transferStranding2ImproperStrandingMark = true;
        if (CollectionUtils.isNotEmpty(changedStrandingCaseList)) {
            // 如果stranding的标注发生了变化，要修改ImproperStranding
            //获取ImproperStranding的eventId
            Set<String> improperStrandingEventIdList = changedStrandingCaseList.stream()
                    .map(RiskCaseDO::getCaseId).collect(Collectors.toSet());
            //加锁处理
            transferStranding2ImproperStrandingMark = lockUtils.batchLockCanWait(
                    LockKeyPreUtil.buildKeyWithEventId(improperStrandingEventIdList),
                    () -> handleTransferStranding2ImproperStrandingMark(
                            changedStrandingCaseList));
        }
        return transferStranding2ImproperStrandingMark;
    }


    /**
     * 处理停滞不当的标注到停滞
     *
     * @param changedStrandingCaseList
     * @return
     */
    private boolean handleTransferStranding2ImproperStrandingMark(List<RiskCaseDO> changedStrandingCaseList) {
        if (CollectionUtils.isEmpty(changedStrandingCaseList)) {
            return true;
        }
        //获取停滞的CaseIdList和停滞不当的eventIdList
        List<String> strandingCaseIdList = changedStrandingCaseList.stream()
                .map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        List<String> improperStrandingEventIdList = new ArrayList<>(strandingCaseIdList);
        //根据eventId查询
        List<RiskCaseDO> improperStrandingCaseList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().eventIdList(improperStrandingEventIdList).build());
        if (CollectionUtils.isEmpty(improperStrandingCaseList)) {
            return true;
        }
        //停滞不当
        List<String> improperStrandingCaseIdList = new ArrayList<>();
        Map<String, RiskCaseDO> improperStrandingCaseEventMap = improperStrandingCaseList.stream()
                //保存最新的停滞不当
                .peek(improperStrandingCase -> improperStrandingCaseIdList.add(improperStrandingCase.getCaseId()))
                .collect(Collectors.toMap(RiskCaseDO::getEventId, Function.identity(), (a, b) -> a));
        // 拿到最新的停滞不当的mark信息
        Map<String, CaseMarkInfoDO> improperStrandingCaseMarkInfoMap = caseMarkInfoRepository.queryMapByParam(
                CaseMarkInfoDOQueryParamDTO.builder().caseIdList(improperStrandingCaseIdList).build());
        // 再查询最新的停滞的mark信息
        Map<String, CaseMarkInfoDO> strandingCaseMarkInfoMap = caseMarkInfoRepository.queryMapByParam(
                CaseMarkInfoDOQueryParamDTO.builder().caseIdList(strandingCaseIdList).build());
        List<CaseMarkInfoDO> updateCaseMarkInfoList = new ArrayList<>();
        if (MapUtils.isEmpty(strandingCaseMarkInfoMap)) {
            return true;
        }
        // 更新
        strandingCaseMarkInfoMap.forEach((strandingCaseId, caseMarkInfoDO) -> {
            RiskCaseDO riskCaseDO = improperStrandingCaseEventMap.get(
                    strandingCaseId);
            if (Objects.isNull(riskCaseDO)) {
                return;
            }
            // 取已存在的或者新建
            CaseMarkInfoDO improperStrandingCaseMarkInfo = improperStrandingCaseMarkInfoMap
                    .computeIfAbsent(riskCaseDO.getCaseId(), RiskCaseFactory::initMarkInfo);
            // 转移
            improperStrandingCaseMarkInfo.copyFrom(caseMarkInfoDO);
            updateCaseMarkInfoList.add(improperStrandingCaseMarkInfo);
        });
        if (CollectionUtils.isNotEmpty(updateCaseMarkInfoList)) {
            caseMarkInfoRepository.batchSave(updateCaseMarkInfoList);
        }
        return true;
    }

}
