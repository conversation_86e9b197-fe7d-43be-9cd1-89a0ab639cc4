package com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 风险数据特征集合类
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RiskFeatureTestDataVO {

    /**
     * case 链接
     */
    private String caseLink;

    /**
     * 风险类别
     */
    private String subCategory;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 维度
     */
    private Double latitude;

    /**
     * 高精地图版本
     */
    private String hdMapVersion;

    /**
     * 距离下个路口的距离
     */
    private Double distanceToNextJunction;

    /**
     * 红灯类型
     */
    private String trafficLightType;

    /**
     * 障碍物距离
     */
    private Double obstacleDistance;

    /**
     * 障碍物夹角
     */
    private Double obstacleAngle;


    /**
     * 风险
     */
    private Integer risk;
}
