package com.sankuai.wallemonitor.risk.center.domain.strategy.detector.impl;

import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetector;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDriveOnTrafficLineRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 压交通线风险检测
 */
@Slf4j
@Service
public class DriveOnTrafficLineDetector extends RiskDetector<RiskDriveOnTrafficLineRecordDO> {


    @Override
    public RiskCaseTypeEnum getDetectRiskType() {
        return RiskCaseTypeEnum.DRIVE_ON_TRAFFIC_LINE;
    }


}
