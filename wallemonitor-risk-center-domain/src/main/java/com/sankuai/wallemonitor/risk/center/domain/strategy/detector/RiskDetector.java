package com.sankuai.wallemonitor.risk.center.domain.strategy.detector;

import com.sankuai.walleeve.utils.GeometryUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.convert.PositionConvert;
import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapLaneAreaTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDetectorRecordBaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.HdMapRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.RiskDetectBaseConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.SafetyAreaQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.CacheUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.locationtech.jts.geom.Coordinate;

/**
 * 风险检测器基类
 */
@Slf4j
public abstract class RiskDetector<T extends RiskDetectorRecordBaseDO> {


    @Resource
    protected SafetyAreaRepository safetyAreaRepository;

    @Resource
    protected HdMapRepository hdMapRepository;


    @Resource
    protected VehicleAdapter vehicleAdapter;

    @Resource
    protected LionConfigRepository lionConfigRepository;

    @Resource
    protected PositionConvert positionConvert;

    /**
     * @param detectContext
     * @return
     */
    public boolean filter(DetectContextDTO detectContext) {
        RiskDetectBaseConfig detectConfig = detectContext.getDetectConfig();
        //获取需要过滤的区域类型
        List<String> areaList = detectConfig.getFilterAreaType();
        if (CollectionUtils.isEmpty(areaList)) {
            return false;
        }
        VehicleRuntimeInfoContextDO contextDO = detectContext.getRuntimeContext();
        PositionDO vehicleLocationWgs84 = contextDO.getLocation();
        List<SafetyAreaDO> safetyAreaDOList = CacheUtils.doCache(safetyAreaRepository, 3, TimeUnit.MINUTES)
                .queryByParam(SafetyAreaQueryParamDTO.builder()
                        .typeList(areaList)
                        .build());
        //是否在安全区
        Boolean isInSafetyArea = safetyAreaDOList.stream()
                .anyMatch(safetyAreaDO -> verifyInParkingArea(positionConvert.toPositionDO(vehicleLocationWgs84,
                        CoordinateSystemEnum.GCJ02), safetyAreaDO));
        Boolean isInLivingArea = false;
        HdMapLaneAreaTypeEnum livingAreaType = HdMapLaneAreaTypeEnum.LIVING_AREA;
        if (areaList.contains(livingAreaType.getValue())) {
            String hdVersion = vehicleAdapter.getVehicleHdMapVersion(contextDO.getVin());
            //如果需要过滤内部道路，判断坐标点是否在内部道路上
            isInLivingArea = CacheUtils.doCache(hdMapRepository, 1000, 5, TimeUnit.MINUTES)
                    .isInHdMapAreaWGS84(livingAreaType, vehicleLocationWgs84,
                            hdVersion, contextDO.getVin());
        }
        //在安全区或者在内部道路，则过滤
        return isInSafetyArea || isInLivingArea;

    }


    /**
     * 是否准入
     *
     * @param detectContext
     */
    public boolean isEntered(DetectContextDTO detectContext) {
        RiskDetectBaseConfig detectConfig = detectContext.getDetectConfig();
        Map<String, Object> spELParamContext = detectContext.getSpelContext();
        // 校验准入条件
        return SpELUtil.evaluateBoolean(detectConfig.getEnterCondition(), spELParamContext);
    }

    /**
     * 持续监测
     *
     * @param riskDetectorRecordBaseDO
     */
    public DetectRecordStatusEnum detect(DetectContextDTO detectContext,
            RiskDetectorRecordBaseDO riskDetectorRecordBaseDO) {
        RiskDetectBaseConfig detectConfig = detectContext.getDetectConfig();
        Map<String, Object> spELParamContext = detectContext.getSpelContext();
        spELParamContext.put("record", riskDetectorRecordBaseDO);
        Boolean isCancel = SpELUtil.evaluateBoolean(detectConfig.getCancelCondition(), spELParamContext);
        if (isCancel) {
            return DetectRecordStatusEnum.CANCELLED;
        }
        Boolean isConfirmed = SpELUtil.evaluateBoolean(detectConfig.getConfirmCondition(), spELParamContext);
        if (isConfirmed) {
            return DetectRecordStatusEnum.CONFIRMED;
        }
        return DetectRecordStatusEnum.PROCESSING;
    }


    /**
     * 获取检测的风险类型
     *
     * @return
     */
    public abstract RiskCaseTypeEnum getDetectRiskType();


    /**
     * 判断位置是否在停车区
     *
     * @param location
     * @return
     */
    private boolean verifyInParkingArea(PositionDO location, SafetyAreaDO safetyAreaDO) {
        try {
            if (Objects.isNull(location)) {
                return false;
            }
            Coordinate coordinate = new Coordinate(location.getLongitude(), location.getLatitude());
            if (Objects.isNull(safetyAreaDO.getPolygon()) || CollectionUtils.isEmpty(
                    safetyAreaDO.getPolygon().getPointGcjList())) {
                return false;
            }
            List<Coordinate> parkingArea = safetyAreaDO.getPolygon().getPointGcjList().stream()
                    .map(x -> new Coordinate(x.getLongitude(), x.getLatitude()))
                    .collect(Collectors.toList());
            parkingArea.add(parkingArea.get(0)); //
            return GeometryUtil.inPolygon(coordinate, parkingArea);
        } catch (Exception e) {
            log.error("区域判断异常", e);
            return false;
        }
    }


}
