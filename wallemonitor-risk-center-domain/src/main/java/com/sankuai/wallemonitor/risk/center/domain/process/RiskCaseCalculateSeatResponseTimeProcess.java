package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.RISK_CASE_CALCULATE_SEAT_RESPONSE_TIME_PROCESS;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.EventPlatSearchAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.MrmConnectionInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskCaseCalculateSeatResponseTimeProcess implements
        DomainEventProcess {

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private EventPlatSearchAdapter eventPlatSearchAdapter;


    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(RISK_CASE_CALCULATE_SEAT_RESPONSE_TIME_PROCESS)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() == RiskCaseDO.class) {
            return handleProcessRisk((DomainEventChangeDTO<RiskCaseDO>) eventDTO);
        }
        //其他情况
        return true;
    }

    /**
     * 处理风险事件
     *
     * @param eventDTO
     * @return
     */
    private boolean handleProcessRisk(DomainEventChangeDTO<RiskCaseDO> eventDTO) {
        // 状态为取消
        // 呼叫云控字段为已呼叫（呼叫中/已取消）
        List<RiskCaseDO> riskCaseDOList = eventDTO.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                .stream().filter(riskCaseDO -> RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus())
                        && !Objects.equals(riskCaseDO.getMrmCalled(), RiskCaseMrmCalledStatusEnum.NO_CALL))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return true;
        }
        //这里是异步处理，所以不信任变更的实体的桩体
        handleUpdateMessage(riskCaseDOList);
        return true;
    }

    /**
     * 处理消息通知
     *
     * @param riskCaseDOList
     * @return
     */
    private void handleUpdateMessage(List<RiskCaseDO> riskCaseDOList) {
        // 并发处置
        ParallelExecutor.executeParallelTasks("vehicle_runtime_info_biz_status", riskCaseDOList, riskCaseDO -> {
            try {
                lockUtils.batchLockNoWait(
                        //对eventId加锁
                        LockKeyPreUtil.buildKeyWithEventId(Collections.singleton(riskCaseDO.getEventId())),
                        () -> {
                            RiskCaseVehicleRelationDO relationDO = riskCaseVehicleRelationRepository.getByCaseId(
                                    riskCaseDO.getCaseId());
                            if (Objects.isNull(relationDO)) {
                                return;
                            }
                            // 根据 vin 开始呼叫时间 / 结束时间
                            MrmConnectionInfoDTO infoDTO = eventPlatSearchAdapter.queryFirstMrmConnectionInTimeRange(
                                    relationDO.getVin(),
                                    relationDO.getRequestSeatTime(), riskCaseDO.getCloseTime());
                            if (Objects.isNull(infoDTO)) {
                                return;
                            }
                            relationDO.setSeatResponseTime(infoDTO.getConnectTime());
                            riskCaseVehicleRelationRepository.save(relationDO);
                        });

            } catch (Exception e) {
                log.error("RiskCaseCalculateSeatResponseTimeProcess.handleUpdateMessage error", e);
            }
        });
    }

}
