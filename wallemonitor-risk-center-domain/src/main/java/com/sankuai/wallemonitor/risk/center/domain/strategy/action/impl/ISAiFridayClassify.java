package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.FridayOpenAiAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ViewEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteCallFridayApiTimeoutException;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.FridayConfig;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.VelocityUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.lion.LionConfigUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeTemplateConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayPromptVTO.FridayPromptPartDTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.FridayVerifyResultVTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 停滞不当预检-大模型
 */
@Slf4j
@Component
public class ISAiFridayClassify implements ISCheckAction<FridayVerifyResultVTO> {

    @Resource
    private FridayOpenAiAdapter fridayOpenAiAdapter;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private FridayCheckActionConfig fridayCheckActionConfig;

    //"front_result"
    private static final String FRONT_RESULT = "front_result";
    private static final String LEFT_RESULT = "left_result";
    private static final String RIGHT_RESULT = "right_result";
    private static final String LOOP_RESULT = "loop_result";

    private static final String SCENE_RESULT = "scene_result";


    private static final String ROUND_PROMPT_TEMPLATE = "risk.friday.round%d.prompt";

    /**
     * 执行预检
     *
     * @param actionContext
     */
    @Override
    public ISCheckActionResult<FridayVerifyResultVTO> execute(ISCheckActionContext actionContext) {
        FridayConfig fridayConfig = lionConfigRepository.getFridayConfig();
        if (fridayConfig == null) {
            // 无配置，无法核验
            return ISCheckActionResult.empty();
        }
        RiskCheckingQueueItemDO queueItemDO = actionContext.getItem();
        Date checkTime = new Date();
        String caseId = queueItemDO.getTmpCaseId();
        String vin = queueItemDO.getVin();
        // 构建参数
        try {
            //构建基础的参数
            FridayModelParamVTO requestDTO = FridayModelParamVTO.builder()
                    .caseId(caseId)
                    //这里取当前的检测时间
                    .occurTime(checkTime)
                    .vin(vin)
                    //基础的prompt
                    .systemPrompt(fridayCheckActionConfig.getSystemPrompt())
                    .userPrompt(fridayCheckActionConfig.getUserPrompt(vin, checkTime, fridayConfig.getBeforeTime()))
                    .modelName(fridayConfig.getModelName())
                    .topP(fridayConfig.getTopP())
                    .appId(fridayConfig.getAppId())
                    .beforeTime(fridayConfig.getBeforeTime())
                    .timeout(fridayConfig.getTimeoutSecond())
                    .build();
            //最少一轮会话
            Integer times = fridayCheckActionConfig.getRound();
            if (times == null) {
                //最少必须咨询一次
                times = 1;
            }
            Integer finalTimes = times;
            FridayVerifyResultVTO resultVTO = fridayOpenAiAdapter.verifyByMultiRound(requestDTO, (openApiContext) -> {
                //取次数
                Integer curRequestRound = openApiContext.getTimes();
                if (curRequestRound > finalTimes) {
                    //如果大于当前的轮次
                    return new ArrayList<>();
                }
                String promptKey = String.format(ROUND_PROMPT_TEMPLATE, curRequestRound);
                String promptText = LionConfigUtils.getString(promptKey);
                if (StringUtils.isBlank(promptText)) {
                    log.error(curRequestRound.toString(), new SystemException("prompt轮次未配置内容!"));
                    return new ArrayList<>();
                }
                //做场景推理
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put(FRONT_RESULT,
                        JacksonUtils.to(openApiContext.getResultVTO()
                                .getObjectNameByView(ViewEnum.FRONT.getCode())));
                paramMap.put(LEFT_RESULT,
                        JacksonUtils.to(
                                openApiContext.getResultVTO().getObjectNameByView(ViewEnum.LEFT.getCode())));
                paramMap.put(RIGHT_RESULT,
                        JacksonUtils.to(openApiContext.getResultVTO()
                                .getObjectNameByView(ViewEnum.RIGHT.getCode())));
                paramMap.put(LOOP_RESULT,
                        JacksonUtils.to(
                                openApiContext.getResultVTO().getObjectNameByView(ViewEnum.LOOP.getCode())));
                paramMap.put(SCENE_RESULT,
                        JacksonUtils.to(openApiContext.getResultVTO().getSceneName()));
                return FridayCheckActionConfig.getPromptWithContext(promptText, paramMap);
            }); // 调用大模型验证
            ISCheckCategoryEnum categoryEnum = calcISCheckCategoryEnum(resultVTO, fridayConfig);
            if (categoryEnum == null) {
                return ISCheckActionResult.empty();
            }
            TrafficLightTypeEnum trafficLightFromSignal = actionContext.getVehicleRunTimeContext()
                    .getTrafficLightType();
            if (categoryEnum.equals(ISCheckCategoryEnum.RED_LIGHT)) {
                //如果是红灯，大模型红灯和信号红灯做double check,如果是了再用,否则不可识别
                boolean isSignalRed = TrafficLightTypeEnum.RED.equals(trafficLightFromSignal);
                if (!isSignalRed) {
                    log.warn(StringMessageFormatter.replaceMsg("vin:{}", actionContext.getItem().getVin()),
                            new SystemException("大模型为红灯，但是信号非红灯"));
                }
                categoryEnum = isSignalRed ? ISCheckCategoryEnum.RED_LIGHT : ISCheckCategoryEnum.CANT_FOUND_ANY;
            }
            return ISCheckActionResult.<FridayVerifyResultVTO>builder()
                    .categoryEnum(categoryEnum)
                    .actionResult(resultVTO)
                    .build();
        } catch (RemoteCallFridayApiTimeoutException e) {
            log.warn("调用大模型超时", e);
            return ISCheckActionResult.empty();
        }
    }

    /**
     * 通过Lion配置 + 本地硬编码 共同计算一个categoryEnum
     * @param resultVTO
     * @return
     */
    private ISCheckCategoryEnum calcISCheckCategoryEnum(FridayVerifyResultVTO resultVTO, FridayConfig fridayConfig) {
        if (resultVTO == null) {
            return null;
        }
        ISCheckCategoryEnum categoryEnum = fridayConfig.mapToCategory(resultVTO);
        if (categoryEnum == null) {
            //如果没有映射规则的结果，根据名字获取结果
            categoryEnum = ISCheckCategoryEnum.getByName(resultVTO.getActivity());
        }
        return categoryEnum;
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Configuration
    public static class FridayCheckActionConfig {

        private static final String IMAGE_TEMPLATE = "https://walle.meituan.com/replay/video/avatarV2?time=%s&vin=%s&view=%s";
        private static final String ROLE_USER = "user";
        private static final String ROLE_SYSTEM = "system";

        private static final String CONSTANT_IMAGE = "image";

        private static final String ROLE_ASSISTANT = "assistant";
        private static final String TYPE_PLAINTEXT = "PLAINTEXT";
        private static final String TYPE_IMAGE = "IMAGE";
        private static final String TYPE_RULE = "RULE";
        private static final String KEY_CHECK_TIME = "checkTime";
        private static final String PREFIX_FIRST = "first_";
        private static final String PREFIX_SECOND = "second_";


        /**
         * 系统
         */
        @ConfigValue(key = "risk.friday.system.prompt", defaultValue = "")
        private String systemPrompt;
        /**
         * 举例
         */
        @ConfigValue(key = "risk.friday.step.prompt", defaultValue = "")
        private String stepPrompt;
        /**
         * 用户
         */
        @ConfigValue(key = "risk.friday.user.prompt", defaultValue = "")
        private String userPrompt;

        /**
         * 1轮咨询
         */
        @ConfigValue(key = "risk.friday.round1.prompt", defaultValue = "")
        private String round1Prompt;
        /**
         * 2轮咨询
         */
        @ConfigValue(key = "risk.friday.round2.prompt", defaultValue = "")
        private String round2Prompt;
        /**
         * 3轮咨询
         */
        @ConfigValue(key = "risk.friday.round3.prompt", defaultValue = "")
        private String round3Prompt;

        /**
         * 轮次
         */
        @ConfigValue(key = "risk.friday.round", defaultValue = "")
        private Integer round = 3;


        @ConfigValue(key = "risk.friday.top_p", defaultValue = "")
        private Double topP;

        public static List<FridayPromptPartDTO> getPromptWithContext(String prompt, Map<String, Object> paramMap) {
            if (StringUtils.isBlank(prompt)) {
                return new ArrayList<>();
            }
            List<String> allPrompt = Arrays.asList(prompt.split("\n"));
            return handleBuildPart(allPrompt, ROLE_USER, paramMap);
        }

        /**
         * 获取用户提示
         *
         * @return
         */
        public List<FridayPromptPartDTO> getUserPrompt(String vin, Date checkTime,
                Integer beforeSecond) {
            //分割出来
            List<String> allSystemPrompt = Arrays.asList(userPrompt.split("\n"));
            Date beforeDate = DatetimeUtil.getNSecondsBeforeDateTime(checkTime, beforeSecond);
            String beforeDateStr = DatetimeUtil.formatDate(beforeDate, DateTimeTemplateConstant.YYYYMMDDHHMMSS);
            String checkTimeStr = DatetimeUtil.formatDate(checkTime, DateTimeTemplateConstant.YYYYMMDDHHMMSS);
            Map<String, Object> context = new HashMap<>();
            context.put(KEY_CHECK_TIME, DatetimeUtil.formatTime(checkTime));
            ViewEnum.getViewEnums()
                    .forEach(viewEnum -> {
                                context.put(PREFIX_FIRST + viewEnum.getCode(),
                                        String.format(IMAGE_TEMPLATE, checkTimeStr, vin,
                                                viewEnum.getCode()));
                                context.put(PREFIX_SECOND + viewEnum.getCode(),
                                        String.format(IMAGE_TEMPLATE, beforeDateStr, vin,
                                                viewEnum.getCode()));
                            }
                    );
            return handleBuildPart(allSystemPrompt, ROLE_USER, context);
        }

        /**
         * 获取系统提示
         *
         * @return
         */
        public List<FridayPromptPartDTO> getSystemPrompt() {
            //分割出来
            List<String> allSystemPrompt = Arrays.asList(systemPrompt.split("\n"));
            return handleBuildPart(allSystemPrompt, ROLE_SYSTEM, null);
        }

        /**
         * 获取步骤提示
         *
         * @return
         */
        public List<FridayPromptPartDTO> getStepPrompt() {
            //分割出来
            List<String> allStepPrompt = Arrays.asList(stepPrompt.split("\n"));
            return handleBuildPart(allStepPrompt, ROLE_USER, null);
        }

        public List<FridayPromptPartDTO> getStepPrompt(Map<String, Object> param) {
            //分割出来
            List<String> allStepPrompt = Arrays.asList(stepPrompt.split("\n"));
            return handleBuildPart(allStepPrompt, ROLE_USER, param);
        }

        private static List<FridayPromptPartDTO> handleBuildPart(List<String> allSystemPrompt, String role,
                Map<String, Object> context) {
            return allSystemPrompt.stream().map(s -> {
                String prefix = StringUtils.substringBefore(s, CharConstant.CHAR_MH);
                if (StringUtils.isBlank(prefix)) {
                    //非特殊的,就是纯文本
                    return FridayPromptPartDTO.buildPart(
                            role,
                            s,
                            CharConstant.CHAR_EMPTY,
                            TYPE_PLAINTEXT,
                            null,
                            context
                    );
                } else {
                    //含有冒号就是特殊的，再看有没有[]标识
                    if (!prefix.startsWith(CharConstant.CHAR_LEFT_BRACKET)) {
                        //不是[打头的，非特殊规则
                        return FridayPromptPartDTO.buildPart(
                                role,
                                s,
                                CharConstant.CHAR_EMPTY,
                                TYPE_PLAINTEXT,
                                null,
                                context
                        );
                    }
                    //[xxx]bbb
                    //取: [xxx
                    String nameWithBefore = StringUtils.substringBefore(prefix, CharConstant.CHAR_RIGHT_BRACKET);
                    //取:bbb
                    String imageName = StringUtils.substringAfter(prefix, CharConstant.CHAR_RIGHT_BRACKET);
                    //取: xxx
                    String name = StringUtils.substringAfter(nameWithBefore, CharConstant.CHAR_LEFT_BRACKET);
                    if (StringUtils.isBlank(name)) {
                        //如果名称为空
                        return FridayPromptPartDTO.buildPart(
                                role,
                                s,
                                CharConstant.CHAR_EMPTY,
                                TYPE_PLAINTEXT,
                                null,
                                context
                        );
                    }
                    //否则,先获取suffix
                    String suffix = StringUtils.substringAfter(s, CharConstant.CHAR_MH);
                    if (StringUtils.equals(name, CONSTANT_IMAGE)) {
                        //如果是image,:后面的用逗号分割
                        List<String> imageList = Arrays.asList(suffix.split(CharConstant.CHAR_DD));
                        if (context != null) {
                            imageName = VelocityUtils.render(imageName, context);
                        }
                        return FridayPromptPartDTO.buildPart(
                                role,
                                imageName,
                                CharConstant.CHAR_EMPTY,
                                TYPE_IMAGE,
                                imageList,
                                context
                        );
                    }
                    return FridayPromptPartDTO.buildPart(
                            role,
                            suffix,
                            name,
                            TYPE_RULE,
                            null,
                            context
                    );

                }
            }).collect(Collectors.toList());
        }
    }


}
