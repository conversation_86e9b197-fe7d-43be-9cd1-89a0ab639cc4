package com.sankuai.wallemonitor.risk.center.domain.result;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseMessageNotifyResultDTO {


    /**
     * 更新失败的caseId
     */
    @Builder.Default
    private List<String> failedCaseIdList = new ArrayList<>();


    /**
     * 更新失败的caseId
     */
    @Builder.Default
    private List<String> successCaseIdList = new ArrayList<>();


}
