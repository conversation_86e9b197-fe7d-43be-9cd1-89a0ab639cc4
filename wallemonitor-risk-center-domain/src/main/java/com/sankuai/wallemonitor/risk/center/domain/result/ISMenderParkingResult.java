package com.sankuai.wallemonitor.risk.center.domain.result;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class ISMenderParkingResult implements Serializable {

    private static final long serialVersionUID = 1L;


    private String matchParkingId;

}
