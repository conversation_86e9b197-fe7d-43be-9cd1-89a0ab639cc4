package com.sankuai.wallemonitor.risk.center.domain.strategy;


import com.sankuai.wallemonitor.risk.center.api.response.vo.VehicleRiskStatusVO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmFilterDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO;

public class HandleStrategy {

   public void process(CallMrmStrategyConfigDTO callMrmStrategyConfigDTO, RiskCaseCallMrmFilterDTO filterDTO,
           RiskCaseDO riskCaseDO) {

   }

   public void process(String vin, ReleaseMrmStrategyConfigDTO releaseMrmStrategyConfigDTO, RiskCaseDO riskCaseDO) {

   }

   public void process(CallSecuritySystemStrategyConfigDTO callSecuritySystemStrategyConfigDTO, RiskCaseCallMrmFilterDTO filterDTO,
                       RiskCaseDO riskCaseDO) {

   }

   public void buildVehicleRiskStatus(String vin, RiskCaseInfoDTO riskCaseInfoDTO,
           VehicleRiskStatusVO vehicleRiskStatusVO) {

   }

   public void process(String vin, LionConfigRepositoryImpl.ReleaseSecuritySystemConfigDTO releaseSecuritySystemConfigDTO, RiskCaseDO riskCaseDO) {

   }
}

