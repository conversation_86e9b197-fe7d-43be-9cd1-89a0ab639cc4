package com.sankuai.wallemonitor.risk.center.domain.process;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.EntityKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmFilterDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ExamObstacleStrandingEventCreateProcess implements DomainEventProcess {

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LockUtils lockUtils;

    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.EXAM_OBSTACLE_STRANDING_CREATE_HANDLE_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        log.info("ExamObstacleStrandingEventCreateProcess# process, eventDTO = {}", eventDTO);
        if (eventDTO.getDomainClass() != RiskCaseVehicleRelationDO.class) {
            return true;
        }
        //状态发生变化的
        DomainEventChangeDTO<RiskCaseVehicleRelationDO> typedDomainEvent = (DomainEventChangeDTO<RiskCaseVehicleRelationDO>) eventDTO;
        List<RiskCaseVehicleRelationDO> initItemList = new ArrayList<>(typedDomainEvent.getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getChangeType(),
                        EntityKeyConstant.ENTITY_EMPTY_TO_VALUE))).stream()
                .filter(riskCaseVehicleRelationDO -> Objects.equals(riskCaseVehicleRelationDO.getType(),
                        RiskCaseTypeEnum.EXAM_OBSTACLE_STRANDING)).collect(Collectors.toList());

        log.info("ExamObstacleStrandingEventCreateProcess# process, initItemList = {}", initItemList);
        if (CollectionUtils.isEmpty(initItemList)) {
            return true;
        }
        return handleProcessMsg(initItemList);
    }

    /**
     * 处理消息
     *
     * @param eventDOList
     * @return
     */
    public boolean handleProcessMsg(List<RiskCaseVehicleRelationDO> eventDOList) {
        Map<Pair<Integer, Integer>, CallSecuritySystemStrategyConfigDTO> callSecurityStrategyConfig = lionConfigRepository.getCallSecurityStrategyConfig();
        if (MapUtils.isEmpty(callSecurityStrategyConfig)) {
            log.error("ExamObstacleStrandingEventCreateProcess# handleProcessMsg, callSecurityStrategyConfig is empty");
            return true;
        }
        eventDOList.forEach(eventDO -> {
            try {
                lockUtils.batchLockCanWait(
                        LockKeyPreUtil.buildKeyWithEventId(Collections.singleton(eventDO.getEventId())), () ->
                        {
                            RiskCaseCallMrmFilterDTO filterDTO = RiskCaseCallMrmFilterDTO.builder()
                                    .vin(eventDO.getVin()).build();
                            RiskCaseDO riskCaseDO = riskCaseRepository.getByCaseId(eventDO.getCaseId());
                            if (Objects.isNull(riskCaseDO)) {
                                return;
                            }
                            Pair<Integer, Integer> riskCaseTypePair = new Pair<>(
                                    riskCaseDO.getSource().getCode(),
                                    riskCaseDO.getType().getCode());
                            Map<String, Object> filterDTOMap = ReflectUtils.getNonNullFieldAndValue(filterDTO);
                            CallSecuritySystemStrategyConfigDTO callSecuritySystemStrategyConfigDTO = callSecurityStrategyConfig.get(
                                    riskCaseTypePair);
                            if (!callSecuritySystemStrategyConfigDTO.isNeedFilter(filterDTOMap,
                                    riskCaseDO.getCaseId())) {
                                riskCaseOperateService.updateRiskCaseAndCallCloudCursor(
                                        callSecuritySystemStrategyConfigDTO, filterDTO,
                                        riskCaseDO);
                            }

                        }
                );
            } catch (Exception e) {
                log.error("handleProcessMsg# error", e);
            }
        });
        return true;
    }
}
