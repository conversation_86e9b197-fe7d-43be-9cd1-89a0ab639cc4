package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.result.ISPreHandleDataResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter.SearchNearbyRequestVTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.ConnectedLaneCalcResultDO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ObstacleInfoGroupDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleObstacleInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.PreHandleDataConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.ObstacleFineTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.ObstacleType;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RoadTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapLaneDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO.VehicleObstacleContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.CacheUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

/**
 * 通用过滤action
 */
@Slf4j
@Component
public class ISPreHandleDataAction implements ISCheckAction<ISPreHandleDataResult> {


    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;


    @Override
    public ISCheckActionResult<ISPreHandleDataResult> execute(ISCheckActionContext actionContext) {
        RiskCheckingQueueItemDO itemDO = actionContext.getItem();
        VehicleRuntimeInfoContextDO runtimeInfo = actionContext.getVehicleRunTimeContext();
        if (Objects.isNull(itemDO) || Objects.isNull(runtimeInfo)) {
            return ISCheckActionResult.empty();
        }
        // 填充车辆的当前位置和前序位置信息，方便计算车辆方向
        // 允许使用多版本数据
        PreHandleDataConfig handleDataConfig = Optional.ofNullable(
                        actionContext.getCurrentActionConfig(PreHandleDataConfig.class))
                .orElseGet(() -> lionConfigRepository.getPreHandleDataConfig());
        if (Objects.isNull(handleDataConfig)) {
            // 无任何配置
            return ISCheckActionResult.empty();
        }
        PreDataContextDTO preDataContextDTO = PreDataContextDTO.builder().build();
        VehicleRuntimeInfoContextDO runTimeContext = actionContext.getVehicleRunTimeContext();
        // 只使用了基本信息，加缓存
        buildEveData(runTimeContext);
        // 构建车辆位置信息
        buildVehiclePositionInfo(runtimeInfo, itemDO.getOccurTime(), handleDataConfig);
        // 构建车道信息
        buildVehicleLane(itemDO, preDataContextDTO, runtimeInfo, handleDataConfig);
        // 构建障碍物信息
        buildObs(preDataContextDTO, runTimeContext, handleDataConfig);
        // 构建可用车道信息
        buildUsableLane(preDataContextDTO, runTimeContext, handleDataConfig);
        return ISCheckActionResult.empty();

    }


    /**
     * 构建eveData
     * @param runtimeInfo
     */
    private void buildEveData(VehicleRuntimeInfoContextDO runtimeInfo) {
        VehicleEveInfoVTO vehicleEveInfoVTO = CacheUtils.doCache(vehicleAdapter, 2, TimeUnit.MINUTES)
                .queryRuntimeVehicleInfoByVin(runtimeInfo.getVin());
        String hdMapArea = Optional.ofNullable(vehicleEveInfoVTO).map(VehicleEveInfoVTO::findHdMapArea)
                .orElse(CharConstant.CHAR_EMPTY);
        String vehicleType = Optional.ofNullable(vehicleEveInfoVTO).map(VehicleEveInfoVTO::getVehicleType)
                .orElse(CharConstant.CHAR_EMPTY);
        runtimeInfo.setVehicleType(vehicleType);
        runtimeInfo.setHdMapArea(hdMapArea);
    }


    private void buildUsableLane(PreDataContextDTO preDataContextDTO, VehicleRuntimeInfoContextDO runtimeInfo,
            PreHandleDataConfig handleDataConfig) {
        HdMapElementGeoDO vehicleCurLane = preDataContextDTO.getVehicleCurLane();
        List<HdMapElementGeoDO> nearbyLaneList = preDataContextDTO.getNearbyLaneList();
        List<VehicleObstacleInfoDTO> obstacleList = runtimeInfo.getAllObstacleList();

        // 搜索自车当前可绕行车道
        List<String> usableLaneIds = calcUsableLane(vehicleCurLane, nearbyLaneList, obstacleList, handleDataConfig);
        runtimeInfo.setUsableLaneIds(usableLaneIds);
    }

    /**
     * 计算可借道通行空间
     *
     * @param vehicleCurLane
     * @param nearbyLaneList
     * @param obstacleList
     * @param waitInQueueConfig
     * @return
     */
    private List<String> calcUsableLane(HdMapElementGeoDO vehicleCurLane, List<HdMapElementGeoDO> nearbyLaneList,
            List<VehicleObstacleInfoDTO> obstacleList, PreHandleDataConfig waitInQueueConfig) {
        if (Objects.isNull(vehicleCurLane) || CollectionUtils.isEmpty(nearbyLaneList)) {
            return Collections.emptyList();
        }

        try {
            // 计算当前可用左侧及其后继车道
            List<HdMapLaneDO> laneDOList = nearbyLaneList.stream().map(HdMapElementGeoDO::toLandDO)
                    .collect(Collectors.toList());
            ConnectedLaneCalcResultDO connectedLanes = findLeftAndSuccessorLanes(vehicleCurLane.getId(), laneDOList);
            if (Objects.isNull(connectedLanes)) {
                return Collections.emptyList();
            }
            // 左侧车道已经被障碍物堵路了 不再继续检索
            HdMapLaneDO left = connectedLanes.getLeft();
            if (Objects.isNull(left) || hasObstacleInLane(obstacleList, left, waitInQueueConfig)) {
                return Collections.emptyList();
            } else {
                return Collections.singletonList(left.getLaneId());
            }
        } catch (Exception e) {
            log.error("calcUsableLane error", e);
            return Collections.emptyList();
        }
    }


    /**
     * 判断是否有障碍物在车道内
     */
    private boolean hasObstacleInLane(List<VehicleObstacleInfoDTO> obstacleList, HdMapLaneDO lane,
            PreHandleDataConfig preHandleDataConfig) {
        List<String> focusFineTypeList = preHandleDataConfig.getFineTypeList();
        PolygonDO lanePolygon = lane.getPolygonDO();
        return obstacleList.stream().anyMatch(obstacle -> lanePolygon.isInPolygon(obstacle.getPosition()) // 在车道内
                // 命中障碍物类型
                && focusFineTypeList.contains(obstacle.getFineType())
                // 角度符合阈值
                && obstacle.getAngle() < preHandleDataConfig.getUsableLaneObstacleAngleThreshold()
                // 距离符合阈值
                && obstacle.getDistance() < preHandleDataConfig.getUsableLaneObstacleDistanceThreshold());
    }


    /**
     * 计算可借道通行空间
     *
     * @param currentLaneId
     * @param laneDOList
     * @return
     */
    private ConnectedLaneCalcResultDO findLeftAndSuccessorLanes(String currentLaneId, List<HdMapLaneDO> laneDOList) {
        if (StringUtils.isBlank(currentLaneId) || CollectionUtils.isEmpty(laneDOList)) {
            return null;
        }

        Map<String, HdMapLaneDO> laneIdMap = laneDOList.stream()
                .collect(Collectors.toMap(HdMapLaneDO::getLaneId, Function.identity(), (o1, o2) -> o1));
        HdMapLaneDO currentLane = laneIdMap.get(currentLaneId);
        if (Objects.isNull(currentLane)) {
            return null;
        }

        // 找到左侧车道对象
        String leftLaneId = currentLane.getLeft();
        HdMapLaneDO leftLane = laneIdMap.get(leftLaneId);
        if (Objects.isNull(leftLane)) {
            log.warn("left lane is null, please increase search range.");
            return null;
        }
        ConnectedLaneCalcResultDO result = ConnectedLaneCalcResultDO.builder().left(leftLane).build();

        List<String> successorIds = leftLane.getSuccessor();
        if (CollectionUtils.isEmpty(successorIds)) {
            return result;
        }
        List<HdMapLaneDO> successorLanes = successorIds.stream().map(laneIdMap::get)
                .filter(lane -> Objects.nonNull(lane) && Objects.equals(lane.getRoadId(),
                        currentLane.getRoadId())) // 需要找同road的？
                .collect(Collectors.toList());
        result.setLeftSuccessorList(successorLanes);
        return result;
    }


    /**
     * 构建车辆的车道信息
     *
     * @param preDataContextDTO
     * @param runtimeInfo
     * @param handleDataConfig
     */
    private void buildVehicleLane(RiskCheckingQueueItemDO itemDO, PreDataContextDTO preDataContextDTO,
            VehicleRuntimeInfoContextDO runtimeInfo,
            PreHandleDataConfig handleDataConfig) {

        PositionDO curPosition = runtimeInfo.getVehiclePosition();
        PositionDO preVehiclePosition = Optional.ofNullable(runtimeInfo.getPreVehiclePosition())
                .map(VehicleInQueuePositionDTO::getPosition).orElse(null);
        List<PositionDO> preVehiclePositionList = Optional.ofNullable(runtimeInfo.getPreVehiclePositionList())
                .orElse(new ArrayList<>()).stream().map(VehicleInQueuePositionDTO::getPosition)
                .collect(Collectors.toList());
        // 查询车辆周围的车道信息
        SearchNearbyRequestVTO param = SearchNearbyRequestVTO.builder().hdMapEnum(HdMapEnum.LANE_POLYGON)
                .positionDO(curPosition).area(runtimeInfo.getHdMapArea())
                .restrictType(handleDataConfig.getLaneTypeList())
                .range(handleDataConfig.getRange())  // 取配置距离范围内的
                .build();
        List<HdMapElementGeoDO> nearbyLaneList = hdMapAdapter.searchNearby(param);
        if (CollectionUtils.isEmpty(nearbyLaneList)) {
            log.warn("buildVehicleLane nearbyLaneList is empty, param = {}", JacksonUtils.to(param));
        }
        //更新车道信息
        preDataContextDTO.setNearbyLaneList(nearbyLaneList);
        List<HdMapElementGeoDO> vehicleInLaneList = nearbyLaneList.stream()
                // 确保车辆所在的车道
                .filter(hdMapElementGeoDO -> hdMapElementGeoDO.isInPolygon(runtimeInfo.getLocation()))
                .collect(Collectors.toList());

        // todo: 记录当前匹配到的车道Id列表, 根据这个列表的大小可以判断车道是否重叠
        List<String> inPolygonLaneIdList = vehicleInLaneList.stream().map(HdMapElementGeoDO::getId)
                .collect(Collectors.toList());
        runtimeInfo.setInPolygonLaneIdList(inPolygonLaneIdList);
        // todo: 如果不匹配，则考虑区域中点距离最近的一个
        // 一个时候，只要不反向即可
        Double maxAngle = CollectionUtils.isNotEmpty(vehicleInLaneList) && vehicleInLaneList.size() > 1
                ? handleDataConfig.getLaneSameDirectionTheta() : 90D;
        // 查询车辆所在的车道
        List<HdMapElementGeoDO> vehicleCurLaneList = vehicleInLaneList.stream()
                // 算行径同向： 落在当前lane和前序lane 的 前序点，和 当前定位构成的向量，和当前lane同向，则判定成功
                .filter(hdMapElementGeoDO -> findLaneSameWithVehicleDirection(hdMapElementGeoDO, preVehiclePositionList,
                        curPosition, maxAngle))
                .collect(Collectors.toList());  // 找第最后一个 fixme: 这里可能会出现重叠车道
        if (CollectionUtils.isEmpty(vehicleCurLaneList)) {
            // todo：输出与每个车道的夹角, 排查上述逻辑为什么会被过滤为空
            Map<String, List<Double>> laneId2AngleListMap = new HashMap<>();
            for (HdMapElementGeoDO lane : vehicleInLaneList) {
                List<Double> angleList = findLaneSameWithVehicleDirectionWithAngle(lane, preVehiclePositionList,
                        curPosition, maxAngle);
                laneId2AngleListMap.put(lane.getId(), angleList);
            }
            runtimeInfo.setLaneId2AngleListMap(laneId2AngleListMap);
            log.warn("buildVehicleLane vehicleCurLaneList is empty, vehicleInLaneList size = {}",
                    CollectionUtils.isEmpty(vehicleInLaneList) ? 0 : vehicleCurLaneList.size());
        }

        // 确定车辆当前所处车道,取和行径方向夹角最小的车道
        HdMapElementGeoDO vehicleCurLane = vehicleCurLaneList.stream().min(Comparator.comparing(lane -> {
            // 取最近的两个点
            Pair<PositionDO, PositionDO> direction = lane.getMiddleLineTwoNearPoint(curPosition);
            // 计算和行径线的夹角
            Double angle = GeoToolsUtil.angleCalc(preVehiclePosition, curPosition, direction.getKey(),
                    direction.getValue());
            // 返回进行比较
            return Optional.ofNullable(angle).orElse(Double.MAX_VALUE);
        })).orElse(null);
        if (Objects.isNull(vehicleCurLane)) {
            log.warn("buildVehicleLane vehicleCurLane is null, vehicleCurLaneList = {}",
                    CollectionUtils.isEmpty(vehicleCurLaneList));
        }
        // 更新周围车道
        Map<String, List<HdMapElementGeoDO>> vehicleAroundLane = getVehicleAroundLane(vehicleCurLaneList);
        List<String> vehicleCurLaneIdList = vehicleCurLaneList.stream().map(HdMapElementGeoDO::getId)
                .collect(Collectors.toList());
        // 更新车道列表(满足要求的都算)
        runtimeInfo.setVehicleCurLaneIdList(vehicleCurLaneIdList);
        // 取和车最近的中心线投影点，2个
        if (Objects.nonNull(vehicleCurLane)) {
            // 确定自车所在的车道，更新数据
            runtimeInfo.setVehicleCurLaneId(vehicleCurLane.getId());
            // 用中心线做两个点
            Pair<PositionDO, PositionDO> direction = vehicleCurLane.getMiddleLineTwoNearPoint(curPosition);
            // 和车道的最近距离 (排除不满足要求的距离)
            Pair<Double, Double> parallelSide = GeoToolsUtil.pointToPolygonParallelSideDistance(curPosition,
                    vehicleCurLane.getPolygonDO().getPoints(), direction, handleDataConfig.getDistance2NearByAngle());
            if (Objects.nonNull(parallelSide) && parallelSide.getKey() != null && parallelSide.getValue() != null) {
                Double distanceToNearCurb = Math.min(parallelSide.getKey(), parallelSide.getValue());
                runtimeInfo.setDistanceToNearCurb(distanceToNearCurb);
                runtimeInfo.setDistanceToNearCurbList(
                        Lists.newArrayList(parallelSide.getKey(), parallelSide.getValue()));
            }
            // 更新车道的位姿关系
            runtimeInfo.updateLaneSelectionType(vehicleCurLane, nearbyLaneList);
            // 更新车辆的前后左右四个车道
            runtimeInfo.updateAroundLane(vehicleAroundLane);
            // 更新车辆的向量
            runtimeInfo.setDirection(direction);
            // ---- 其他计算需要的上下文
            preDataContextDTO.setVehicleAroundLane(vehicleAroundLane);
            preDataContextDTO.setVehicleCurLane(vehicleCurLane);
        } else {
            log.warn("vehicleCurLane is null, caseId = {}", itemDO.getTmpCaseId());
            // todo: 当上述逻辑获取不到车道类型时
            computeVehicleLaneSectionType(itemDO, runtimeInfo, nearbyLaneList, curPosition);
        }
    }

    /**
     * 计算车辆所在的车道类型
     *
     * @param nearbyLaneList
     * @param curPosition
     */
    private void computeVehicleLaneSectionType(RiskCheckingQueueItemDO itemDO, VehicleRuntimeInfoContextDO runtimeInfo,
            List<HdMapElementGeoDO> nearbyLaneList, PositionDO curPosition) {
        if (CollectionUtils.isEmpty(nearbyLaneList) || Objects.isNull(curPosition)) {
            log.error("computeVehicleLaneSectionType failed, nearbyLaneList is empty or curPosition is null");
            return;
        }
        Map<String, HdMapElementGeoDO> laneMap = nearbyLaneList.stream()
                .collect(Collectors.toMap(HdMapElementGeoDO::getId, lane -> lane, (v1, v2) -> v1));

        Boolean isMatch = false;
        for (HdMapElementGeoDO elementGeoDO : nearbyLaneList) {
            // 判断当前点所在的车道
            if (elementGeoDO.isInPolygon(curPosition)) {
                // 计算车道类型
                Map<String, Object> propertiesMap = elementGeoDO.getProperties();
                if (Objects.isNull(propertiesMap)) {
                    log.error("lane properties is null, laneId:{}", elementGeoDO.getId());
                    continue;
                }
                boolean hasLeft = StringUtils.isNotBlank(elementGeoDO.getPropertyByKey(GeoElementTypeKeyConstant.LEFT));
                boolean hasRight = StringUtils.isNotBlank(
                        elementGeoDO.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT));
                if (hasLeft && hasRight) {
                    runtimeInfo.setVehicleLaneSectionTypeV2("middle");
                    // 多车道,大于两条车道
                    runtimeInfo.setLaneCount(RoadTypeEnum.MULTI_LANE.getCode());
                } else if (hasLeft && !hasRight) {
                    runtimeInfo.setVehicleLaneSectionTypeV2("right");
                    // todo 这里不一定是两车道，需要检查其左侧车道是否存在更左侧车道
                    String leftLaneId = elementGeoDO.getPropertyByKey(GeoElementTypeKeyConstant.LEFT);
                    runtimeInfo.setLaneCount(determineRoadType(laneMap, leftLaneId));
                } else if (!hasLeft && hasRight) {
                    runtimeInfo.setVehicleLaneSectionTypeV2("left");
                    //  两车道
                    runtimeInfo.setLaneCount(RoadTypeEnum.DOUBLE_LANE.getCode());
                } else {
                    runtimeInfo.setVehicleLaneSectionTypeV2("middle");
                    //  两车道
                    runtimeInfo.setLaneCount(RoadTypeEnum.SINGLE_LANE.getCode());
                }
                isMatch = true;
                break;
            }
        }
        if (!isMatch) {
            log.warn("computeVehicleLaneSectionType failed, caseId = {}, curPosition = {} ", itemDO.getTmpCaseId(),
                    JacksonUtils.to(curPosition));
        }
    }

    /**
     * 计算车道类型
     *
     * @param laneMap    车道map
     * @param leftLaneId 左侧车道id
     * @return
     */
    private Integer determineRoadType(Map<String, HdMapElementGeoDO> laneMap, String leftLaneId) {
        // 参数校验
        if (Objects.isNull(laneMap) || StringUtils.isBlank(leftLaneId)) {
            return RoadTypeEnum.DOUBLE_LANE.getCode();
        }
        HdMapElementGeoDO lane = laneMap.get(leftLaneId);
        if (Objects.isNull(lane)) {
            return RoadTypeEnum.DOUBLE_LANE.getCode();
        }
        // 如果左侧车道匹配到更左的车道，则说明这是一个多车道
        if (StringUtils.isNotBlank(lane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT))) {
            return RoadTypeEnum.MULTI_LANE.getCode();
        }
        return RoadTypeEnum.DOUBLE_LANE.getCode();
    }

    /**
     * 计算满足的同向
     *
     * @param preVehiclePositionList
     * @param curPosition
     * @param laneSameDirectionTheta
     * @return
     */
    private boolean findLaneSameWithVehicleDirection(HdMapElementGeoDO curLane, List<PositionDO> preVehiclePositionList,
            PositionDO curPosition, Double laneSameDirectionTheta) {
        if (CollectionUtils.isEmpty(preVehiclePositionList)) {
            return false;
        }
        List<HdMapElementGeoDO> allLane = new ArrayList<>(
                hdMapAdapter.queryByLaneIdList(curLane.getPropertyByKey(GeoElementTypeKeyConstant.PREDECESSOR)));
        allLane.add(curLane);
        // 可以从过去的定位里面，找到通行合理点
        PositionDO inSamePosition = preVehiclePositionList.stream().filter(Objects::nonNull)
                // 行径轨迹里面的点，在当前车道的（漂移解除）
                .filter(position -> allLane.stream().anyMatch(lane -> lane.isInPolygon(position)))
                // 留下的，都是行径轨迹里面的合理前序点
                // 判断是否形成同向
                .filter(position -> curLane.isSameDirection(position, curPosition, laneSameDirectionTheta))
                .findFirst().orElse(null);
        log.info("findLaneSameWithVehicleDirection, laneId:{}, position:{}", curLane.getId(), inSamePosition);
        return Objects.nonNull(inSamePosition);

    }

    /**
     * 记录每个前序轨迹点与当前车道的夹角（列表）
     *
     * @param curLane                当前车道
     * @param preVehiclePositionList 车辆历史轨迹点
     * @param curPosition            当前车辆位置
     * @param laneSameDirectionTheta 阈值角度
     * @return Map<String, List < Double>> key为车道ID，value为所有夹角
     */
    private List<Double> findLaneSameWithVehicleDirectionWithAngle(
            HdMapElementGeoDO curLane,
            List<PositionDO> preVehiclePositionList,
            PositionDO curPosition,
            Double laneSameDirectionTheta) {
        if (CollectionUtils.isEmpty(preVehiclePositionList)) {
            return new ArrayList<>();
        }

        try {
            List<Double> angleList = new ArrayList<>();
            for (PositionDO position : preVehiclePositionList) {
                if (position == null) {
                    continue;
                }
                Pair<Boolean, Double> pair = curLane.isSameDirectionWithAngle(position, curPosition,
                        laneSameDirectionTheta);
                if (pair != null && pair.getValue() != null) {
                    angleList.add(pair.getValue());
                }
            }
            return angleList;
        } catch (Exception e) {
            log.error("findLaneSameWithVehicleDirectionWithAngle error", e);
        }
        return new ArrayList<>();
    }

    private void buildVehiclePositionInfo(
            VehicleRuntimeInfoContextDO runtimeContext,
            Date occurTime, PreHandleDataConfig handleDataConfig) {

        PositionDO curPosition = runtimeContext.getLocation();
        Date timeBeforeOccurTime = DatetimeUtil.getNSecondsBeforeDateTime(occurTime, handleDataConfig.getPastSecond());
        // 查询过去的两个点
        List<VehicleInQueuePositionDTO> vehicleLastLocationList = vehicleAdapter
                .queryVehicleHistoryDataFromEveReplay(runtimeContext.getVin(),
                        DatetimeUtil.getTimeInSeconds(timeBeforeOccurTime), DatetimeUtil.getTimeInSeconds(occurTime))
                .stream().map(vehicleDataInfoVO -> {
                    PositionDO positionDO = PositionDO.getPosition(vehicleDataInfoVO.getLongitude(),
                            vehicleDataInfoVO.getLatitude(), CoordinateSystemEnum.WGS84);
                    return VehicleInQueuePositionDTO.builder().position(positionDO)
                            .distance(GeoToolsUtil.distance(positionDO, curPosition))     // 计算距离
                            .time(vehicleDataInfoVO.getTime())  // 时间
                            .build();
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleLastLocationList)) {
            log.warn("无法找到车辆历史定位信息");
            return;
        }

        // 保存过去历史定位点
        runtimeContext.setPrePositionList(new ArrayList<>(vehicleLastLocationList));
        Collections.reverse(vehicleLastLocationList);   // 直接倒序
        // 取最后一个不靠近定位点的点，防止有拐弯的情况
        List<VehicleInQueuePositionDTO> preVehiclePositionList = IntStream.range(0, vehicleLastLocationList.size())
                .filter(index -> {
                    VehicleInQueuePositionDTO position = vehicleLastLocationList.get(index);
                    // 满足最小距离和最大距离，防止过近引发异常
                    return Objects.nonNull(handleDataConfig) && Objects.nonNull(position)
                            && !position.invalid() // 定位合法
                            && Objects.nonNull(position.getDistance()) // 有距离
                            && position.getDistance() > handleDataConfig.getPreMinDistance()
                            && position.getDistance() < handleDataConfig.getPreMaxDistance();
                }).mapToObj(vehicleLastLocationList::get).collect(Collectors.toList());
        // 默认选择第一个满足的，作为
        VehicleInQueuePositionDTO preVehiclePosition = CollectionUtils.isNotEmpty(preVehiclePositionList)
                ? preVehiclePositionList.get(0) : vehicleLastLocationList.get(0);
        // 保存
        runtimeContext.setVehiclePosition(curPosition);
        runtimeContext.setPreVehiclePosition(preVehiclePosition);
        runtimeContext.setPreVehiclePositionList(preVehiclePositionList);
        // 前序的position
        if (Objects.isNull(preVehiclePosition)) {
            log.warn("无法找到就近的俩近似点");
        }

    }


    /**
     * 获取车辆周围的车道
     *
     * @param vehicleCurLaneList
     * @return
     */
    private Map<String, List<HdMapElementGeoDO>> getVehicleAroundLane(List<HdMapElementGeoDO> vehicleCurLaneList) {
        if (CollectionUtils.isEmpty(vehicleCurLaneList)) {
            return new HashMap<>();
        }
        Map<String, List<HdMapElementGeoDO>> vehicleLaneMap = new HashMap<>();
        vehicleCurLaneList.forEach((vehicleCurLane) -> {
            List<String> predecessorLaneList = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> (List<String>)lane.getPropertyByKey(GeoElementTypeKeyConstant.PREDECESSOR))
                    .orElse(new ArrayList<>());
            List<String> successorLaneList = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> (List<String>)lane.getPropertyByKey(GeoElementTypeKeyConstant.SUCCESSOR))
                    .orElse(new ArrayList<>());
            String leftLane = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> lane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT)).map(String::valueOf)
                    .orElse(null);
            String rightLane = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> lane.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT)).map(String::valueOf)
                    .orElse(null);
            if (CollectionUtils.isNotEmpty(predecessorLaneList)) {
                // 前序车道不为空
                List<HdMapElementGeoDO> finalPredecessorLaneDOList = new ArrayList<>();
                List<HdMapElementGeoDO> predecessorLaneDOList = hdMapAdapter.queryByLaneIdList(predecessorLaneList);
                if (CollectionUtils.isNotEmpty(predecessorLaneDOList)) {
                    finalPredecessorLaneDOList.addAll(predecessorLaneDOList);
                    // 前序车道的前序车道不为空，也加入进去，只看一层
                    predecessorLaneDOList.forEach(lane -> {
                        if (CollectionUtils.isEmpty(lane.getPropertyByKey(GeoElementTypeKeyConstant.PREDECESSOR))) {
                            // 没有前序
                            return;
                        }
                        finalPredecessorLaneDOList.addAll(hdMapAdapter.queryByLaneIdList(
                                (List<String>)lane.getPropertyByKey(GeoElementTypeKeyConstant.PREDECESSOR)));
                    });
                }
                vehicleLaneMap.computeIfAbsent(GeoElementTypeKeyConstant.PREDECESSOR, k -> new ArrayList<>())
                        .addAll(finalPredecessorLaneDOList);
            }
            if (CollectionUtils.isNotEmpty(successorLaneList)) {
                // 后继车道不为空
                List<HdMapElementGeoDO> finalSuccessorLaneDOList = new ArrayList<>();
                List<HdMapElementGeoDO> successorLaneDOList = hdMapAdapter.queryByLaneIdList(successorLaneList);
                if (CollectionUtils.isNotEmpty(successorLaneDOList)) {
                    // 后继车道的后继车道不为空，也加入进去，只看一层
                    finalSuccessorLaneDOList.addAll(successorLaneDOList);
                    successorLaneDOList.forEach(lane -> {
                        if (CollectionUtils.isEmpty(lane.getPropertyByKey(GeoElementTypeKeyConstant.SUCCESSOR))) {
                            return;
                        }
                        finalSuccessorLaneDOList.addAll(hdMapAdapter.queryByLaneIdList(
                                (List<String>)lane.getPropertyByKey(GeoElementTypeKeyConstant.SUCCESSOR)));
                    });
                }
                vehicleLaneMap.computeIfAbsent(GeoElementTypeKeyConstant.SUCCESSOR, k -> new ArrayList<>())
                        .addAll(finalSuccessorLaneDOList);
            }
            if (StringUtils.isNotBlank(leftLane)) {
                // 左车道不为空
                vehicleLaneMap.computeIfAbsent(GeoElementTypeKeyConstant.LEFT, k -> new ArrayList<>())
                        .addAll(hdMapAdapter.queryByLaneIdList(Lists.newArrayList(leftLane)));
            }
            if (StringUtils.isNotBlank(rightLane)) {
                // 右车道不为空
                vehicleLaneMap.computeIfAbsent(GeoElementTypeKeyConstant.RIGHT, k -> new ArrayList<>())
                        .addAll(hdMapAdapter.queryByLaneIdList(Lists.newArrayList(rightLane)));
            }
        });
        return vehicleLaneMap;
    }

    private void buildObs(PreDataContextDTO handContext, VehicleRuntimeInfoContextDO runtimeContext,
            PreHandleDataConfig handleDataConfig) {
        VehicleObstacleContextDO obstacleContext = runtimeContext.getObstacleContext();
        if (Objects.isNull(obstacleContext) || CollectionUtils.isEmpty(obstacleContext.getPerceptionObstacle())) {
            // 如果不存在障碍物的信息，也不需要做任何形式的判断了
            log.warn("无法找到车辆周围障碍物信息");
            return;
        }

        if (Objects.isNull(runtimeContext.getPreVehiclePosition())) {
            return;
        }
        // 计算障碍物信息
        List<VehicleObstacleInfoDTO> obstacleList = buildObstacleInfo(handContext, runtimeContext,
                handleDataConfig.getDistance2NearByAngle());
        runtimeContext.setAllObstacleList(obstacleList);
        // 构建前方障碍物信息
        ObstacleInfoGroupDTO groupDTO = buildFrontAndBehindObstacleInfoGroup(obstacleList, handleDataConfig);
        // 保存前方、后方 障碍物信息
        runtimeContext.setFrontObstacle(groupDTO.getAheadObstacle());
        runtimeContext.setBehindObstacle(groupDTO.getBehindObstacle());
        runtimeContext.setFrontObstacleList(groupDTO.getAheadObstacleList());
        runtimeContext.setBehindObstacleList(groupDTO.getBehindObstacleList());

    }

    /**
     * 计算正前方、前方范围，正后方，后方范围
     *
     * @param obstacleInfoList
     * @param config
     * @return
     */
    public ObstacleInfoGroupDTO buildFrontAndBehindObstacleInfoGroup(List<VehicleObstacleInfoDTO> obstacleInfoList,
            PreHandleDataConfig config) {
        ObstacleInfoGroupDTO obstacleGroup = ObstacleInfoGroupDTO.builder().build();
        // 算前方障碍物
        if (CollectionUtils.isEmpty(obstacleInfoList)) {
            return obstacleGroup;
        }
        // 最接近的障碍物为正前方障碍物
        VehicleObstacleInfoDTO aheadObstacle = null;
        VehicleObstacleInfoDTO behindObstacle = null;
        // 计算
        for (VehicleObstacleInfoDTO obstacleInfoDTO : obstacleInfoList) {
            if (isAheadObstacle(obstacleInfoDTO, config)) {
                // 正前方障碍物配置
                if (Objects.isNull(aheadObstacle) || aheadObstacle.getDistance() > obstacleInfoDTO.getDistance()) {
                    // 更新正前方障碍物配置: 不存在老的，或者新的距离小于老的
                    aheadObstacle = obstacleInfoDTO;
                }
            }
            if (isBehindObstacle(obstacleInfoDTO, config)) {
                if (Objects.isNull(behindObstacle) || behindObstacle.getDistance() > obstacleInfoDTO.getDistance()) {
                    // 更新正后方障碍物配置 : 不存在老的，或者新的距离小于老的
                    behindObstacle = obstacleInfoDTO;
                }
            }
            List<String> rangeTypeList = new ArrayList<>();
            rangeTypeList.addAll(config.getFineTypeList());
            rangeTypeList.addAll(config.getRangeFineTypeList());
            if (isAheadObstacleInRange(obstacleInfoDTO, config.getFineTypeDistanceThreshold(),
                    config.getDefaultDistanceThreshold(), config.getFrontRangeMaxAngle(), rangeTypeList)) {
                // 前方范围障碍物配置
                obstacleGroup.getAheadObstacleList().add(obstacleInfoDTO);
            }
            if (isBehindObstacleInRange(obstacleInfoDTO, config.getFineTypeDistanceThreshold(),
                    config.getDefaultDistanceThreshold(), config.getBehindRangeMinAngle(), rangeTypeList)) {
                // 后方范围障碍物配置
                obstacleGroup.getBehindObstacleList().add(obstacleInfoDTO);
            }
        }
        obstacleGroup.setAheadObstacle(aheadObstacle);
        obstacleGroup.setBehindObstacle(behindObstacle);
        return obstacleGroup;
    }


    /**
     * 检查正后方是否有社会车辆
     */
    private boolean isBehindObstacle(VehicleObstacleInfoDTO obstacle, PreHandleDataConfig config) {
        if (Objects.isNull(obstacle) || Objects.isNull(config)) {
            return false;
        }

        boolean isAngleMatched = Objects.nonNull(obstacle.getAngle())
                && obstacle.getAngle() > config.getBehindRangeAngle();
        boolean isDistanceMatch = Objects.nonNull(obstacle.getDistance())
                && obstacle.getDistance() < config.getBehindDistance();
        boolean isMatchedType = Objects.nonNull(obstacle.getFineType())
                && config.getFineTypeList().contains(obstacle.getFineType());
        // 在当前车道或者前继车道
        boolean inCurLaneOrPredecessorLane = StringUtils.isNotBlank(obstacle.getLaneId())
                && (GeoElementTypeKeyConstant.PREDECESSOR.equals(obstacle.getLaneRelation2Vehicle())
                        || GeoElementTypeKeyConstant.CUR.equals(obstacle.getLaneRelation2Vehicle()));
        return isAngleMatched && isDistanceMatch && isMatchedType && inCurLaneOrPredecessorLane;
    }

    private List<VehicleObstacleInfoDTO> buildObstacleInfo(PreDataContextDTO handContext,
            VehicleRuntimeInfoContextDO runtimeInfo,
            Double distance2NearByAngle) {

        PositionDO curPosition = runtimeInfo.getVehiclePosition();
        PositionDO preVehiclePosition = Optional.ofNullable(runtimeInfo.getPreVehiclePosition())
                .map(VehicleInQueuePositionDTO::getPosition).orElse(null);
        List<PerceptionObstacle> perceptionObstacleList = Optional.ofNullable(runtimeInfo.getObstacleContext())
                .map(VehicleObstacleContextDO::getPerceptionObstacle).orElse(new ArrayList<>());
        HdMapElementGeoDO vehicleCurLane = handContext.getVehicleCurLane();
        String hdMapArea = runtimeInfo.getHdMapArea();
        Pair<PositionDO, PositionDO> direction = runtimeInfo.getDirection();
        Map<String, List<HdMapElementGeoDO>> vehicleAroundLane = handContext.getVehicleAroundLane();

        // 遍历过程常量
        // 简化障碍物的信息
        List<VehicleObstacleInfoDTO> obstacleInfoList = perceptionObstacleList.stream()
                .filter(obstacle -> Objects.nonNull(obstacle) && Objects.nonNull(obstacle.getPosition()))
                .map(obstacle -> {
                    String fineType = Optional.ofNullable(obstacle.getObstacleType()).map(ObstacleType::getFineType)
                            .map(ObstacleFineTypeEnum::transferShotName).orElse(CharConstant.CHAR_EMPTY);
                    String obstacleType = Optional.ofNullable(obstacle.getObstacleType())
                            .map(ObstacleType::getCoarseType).orElse(CharConstant.CHAR_EMPTY);
                    // 计算utm区号
                    Integer zoneId = hdMapAdapter.getUtmZoneByArea(hdMapArea);
                    // 转换坐标系
                    PositionDO wgs84Position = GeoToolsUtil.utmToWgs84WithZone(obstacle.getPosition().getX(),
                            obstacle.getPosition().getY(), zoneId);
                    return VehicleObstacleInfoDTO.builder()
                            // 障碍物ID
                            .obstacleId(obstacle.getId())
                            // 坐标
                            .position(wgs84Position)
                            // 类型
                            .fineType(fineType).obstacleType(obstacleType).type(obstacle.getType())
                            // 宽度
                            .width(obstacle.getWidth()).build();
                }).collect(Collectors.toList());

        obstacleInfoList.forEach((obstacle) -> {
            // 和 障碍物连线 和 自车行径线夹角
            Double angle = GeoToolsUtil.angleCalc(curPosition, obstacle.getPosition(), preVehiclePosition,
                    curPosition); // 计算障碍物夹角
            Double distance = GeoToolsUtil.distance(curPosition, obstacle.getPosition());
            obstacle.setDistance(distance); // 设置障碍物与当前车辆之间的距离。
            obstacle.setAngle(angle);
            // 获取离中心点最近的夹角，key是最近的一个，value是下一个
            Pair<PositionDO, PositionDO> laneCenterLine = Optional.ofNullable(vehicleCurLane)
                    .map(lane -> lane.getMiddleLineTwoNearPoint(curPosition)).orElse(null);
            // 车道中心线的夹角不为空时 算障碍物与中心线的夹角
            if (Objects.nonNull(laneCenterLine)) {
                Double middleAngle = GeoToolsUtil.angleCalc(curPosition, obstacle.getPosition(),
                        laneCenterLine.getKey(), laneCenterLine.getValue());
                obstacle.setMiddleAngle(middleAngle);
            }

            // 找到在车道内的或者后继车道的
            boolean inSameLane = vehicleCurLane != null && vehicleCurLane.isInPolygon(obstacle.getPosition());
            if (inSameLane) {
                // 只要在同车
                // 保存该障碍物所在的车道
                obstacle.updateLaneAndRelation(GeoElementTypeKeyConstant.CUR, vehicleCurLane);
                buildObstacleNearByDistance(obstacle, vehicleCurLane, direction, distance2NearByAngle);
            } else {
                // 本车周围的车道
                vehicleAroundLane.forEach((aroundType, hdGeoList) -> {
                    for (HdMapElementGeoDO hdGeo : hdGeoList) {
                        if (StringUtils.isNotBlank(obstacle.getLaneId())) {
                            // 已经找到车道，无需处理
                            break;
                        }
                        if (!hdGeo.isInPolygon(obstacle.getPosition())) {
                            // 不再范围内的，继续
                            continue;
                        }
                        // 如果是下个车道。使用下个车道的中心线
                        Pair<PositionDO, PositionDO> thisLanePair = hdGeo
                                .getMiddleLineTwoNearPoint(obstacle.getPosition());
                        // 如果这个在这个车道，则更新
                        obstacle.updateLaneAndRelation(aroundType, hdGeo);
                        // 计算障碍物相关距离
                        buildObstacleNearByDistance(obstacle, hdGeo, thisLanePair, distance2NearByAngle);
                    }
                });
            }
        });
        return obstacleInfoList;
    }


    /**
     * 计算障碍物的最近距离
     *
     * @param obstacle
     * @param lane
     * @param direction
     * @param distance2NearByAngle
     */
    private void buildObstacleNearByDistance(VehicleObstacleInfoDTO obstacle, HdMapElementGeoDO lane,
            Pair<PositionDO, PositionDO> direction, Double distance2NearByAngle) {
        if (Objects.isNull(obstacle) || Objects.isNull(lane) || Objects.isNull(direction)
                || Objects.isNull(distance2NearByAngle)) {
            return;
        }
        // 车道平行线的夹角
        Pair<Double, Double> parallelSideDistance = GeoToolsUtil.pointToPolygonParallelSideDistance(
                obstacle.getPosition(), lane.getPolygonDO().getPoints(), direction, distance2NearByAngle);
        if (Objects.isNull(parallelSideDistance) || parallelSideDistance.getValue() == null
                || parallelSideDistance.getKey() == null) {
            // 如果为空
            return;
        }
        // 计算障碍物和边界的最小距离
        Double minDistance = Math.min(parallelSideDistance.getKey(), parallelSideDistance.getValue());
        // 设置值
        obstacle.setDistanceToNearCurb(minDistance);
        obstacle.setDistanceToNearCurbList(
                Lists.newArrayList(parallelSideDistance.getKey(), parallelSideDistance.getValue()));
    }

    /**
     * 判定后方障碍物是否在范围内
     *
     * @param distanceThresholdMap
     * @param defaultDistanceThreshold
     * @param rangeTypeList
     * @return
     */
    private boolean isBehindObstacleInRange(VehicleObstacleInfoDTO obstacle, Map<String, Double> distanceThresholdMap,
            Double defaultDistanceThreshold, Double behindRangeMinAngle, List<String> rangeTypeList) {
        if (Objects.isNull(behindRangeMinAngle) || Objects.isNull(obstacle)) {
            return false;
        }
        // 判断距离是否满足
        Double distanceThreshold = distanceThresholdMap.getOrDefault(obstacle.getFineType(), defaultDistanceThreshold);
        // 角度，必须大于范围（比如必须大于 170度 ）
        boolean isAngleMatched = obstacle.getMiddleAngle() != null
                && behindRangeMinAngle <= obstacle.getMiddleAngle();
        // 类型, 必须满足类型范围
        boolean isMatchedType = CollectionUtils.isNotEmpty(rangeTypeList)
                && StringUtils.isNotBlank(obstacle.getFineType()) && rangeTypeList.contains(obstacle.getFineType());
        // 距离，必须小于车型距离
        boolean isDistanceMatch = distanceThreshold != null && obstacle.getDistance() < distanceThreshold;
        // 车道，本车道或者前继车道
        boolean inCurLaneOrPredecessor = StringUtils.isNotBlank(obstacle.getLaneId())
                && (GeoElementTypeKeyConstant.PREDECESSOR.equals(obstacle.getLaneRelation2Vehicle())
                        || GeoElementTypeKeyConstant.CUR.equals(obstacle.getLaneRelation2Vehicle()));

        return isAngleMatched && isMatchedType && isDistanceMatch && inCurLaneOrPredecessor;
    }

    /**
     * 判定前方障碍物是否在范围内
     *
     * @param obstacle
     * @param distanceThresholdMap
     * @param defaultDistanceThreshold
     * @param rangeTypeList
     * @return
     */
    private boolean isAheadObstacleInRange(VehicleObstacleInfoDTO obstacle, Map<String, Double> distanceThresholdMap,
            Double defaultDistanceThreshold, Double frontRangeMaxAngle, List<String> rangeTypeList) {
        if (Objects.isNull(frontRangeMaxAngle) || Objects.isNull(obstacle)) {
            return false;
        }
        // 判断距离是否满足
        Double distanceThreshold = distanceThresholdMap.getOrDefault(obstacle.getFineType(), defaultDistanceThreshold);
        // 角度，必须小于范围
        boolean isAngleMatched = obstacle.getMiddleAngle() != null
                && frontRangeMaxAngle >= obstacle.getMiddleAngle();
        // 类型, 必须满足类型范围
        boolean isMatchedType = CollectionUtils.isNotEmpty(rangeTypeList)
                && StringUtils.isNotBlank(obstacle.getFineType()) && rangeTypeList.contains(obstacle.getFineType());
        // 距离，必须小于车型距离
        boolean isDistanceMatch = distanceThreshold != null && obstacle.getDistance() < distanceThreshold;
        // 车道，本车道或者后继车道
        boolean inCurLaneOrSuccessor = StringUtils.isNotBlank(obstacle.getLaneId())
                && (GeoElementTypeKeyConstant.SUCCESSOR.equals(obstacle.getLaneRelation2Vehicle())
                        || GeoElementTypeKeyConstant.CUR.equals(obstacle.getLaneRelation2Vehicle()));

        return isAngleMatched && isMatchedType && isDistanceMatch && inCurLaneOrSuccessor;

    }


    /**
     * 是否为自车前方的障碍物
     *
     * @param obstacle
     * @param config
     * @return
     */
    public boolean isAheadObstacle(VehicleObstacleInfoDTO obstacle, PreHandleDataConfig config) {
        if (Objects.isNull(obstacle) || Objects.isNull(config)) {
            return false;
        }
        boolean isAngleMatched = checkAngle(obstacle, config);

        // 判断距离是否满足
        Map<String, Double> distanceThresholdMap = config.getFineTypeDistanceThreshold();
        Double distanceThreshold = distanceThresholdMap.getOrDefault(obstacle.getFineType(),
                config.getDefaultDistanceThreshold());
        boolean isDistanceMatch = distanceThreshold != null && obstacle.getDistance() < distanceThreshold;

        List<String> fineTypeList = config.getFineTypeList();
        boolean isMatchedType = CollectionUtils.isNotEmpty(fineTypeList)
                && StringUtils.isNotBlank(obstacle.getFineType()) && fineTypeList.contains(obstacle.getFineType());

        boolean inCurLaneOrSuccessor = StringUtils.isNotBlank(obstacle.getLaneId())
                && (GeoElementTypeKeyConstant.SUCCESSOR.equals(obstacle.getLaneRelation2Vehicle())
                        || GeoElementTypeKeyConstant.CUR.equals(obstacle.getLaneRelation2Vehicle()));

        return isAngleMatched && isDistanceMatch && isMatchedType && inCurLaneOrSuccessor;
    }


    /**
     * 检查障碍物与中心线的夹角是否满足条件
     *
     * @param obstacle
     * @param config
     * @return
     */
    private boolean checkAngle(VehicleObstacleInfoDTO obstacle, PreHandleDataConfig config) {
        Map<String, Double> angleThresholdByObstacleType = config.getAngleThresholdByObstacleType();
        if (MapUtils.isEmpty(angleThresholdByObstacleType)) {
            return false;
        }

        Double defaultAngleThreshold = angleThresholdByObstacleType.get("DEFAULT");
        Double angleThreshold = angleThresholdByObstacleType.getOrDefault(obstacle.getObstacleType(),
                defaultAngleThreshold);
        return obstacle.getMiddleAngle() != null && obstacle.getMiddleAngle() < angleThreshold;
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class PreDataContextDTO {

        @Builder.Default
        private List<HdMapElementGeoDO> nearbyLaneList = new ArrayList<>();
        @Builder.Default
        private Map<String, List<HdMapElementGeoDO>> vehicleAroundLane = new HashMap<>();
        private HdMapElementGeoDO vehicleCurLane;
    }

}
