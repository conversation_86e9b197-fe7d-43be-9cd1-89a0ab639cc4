package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.RISK_DISPOSED_CANCEL_SAFETY_ENTRY;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.domain.enums.CloudTriageEventStatusEnum;
import com.sankuai.walleeve.domain.enums.MessageType;
import com.sankuai.walleeve.domain.message.dto.CloudTriageEventMessageDTO;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallSafetyEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.CloudEventTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;


/**
 * 风险解除时刻取消云安全，云分诊幂等消息
 */
@Component
@Slf4j
public class RiskCaseHighNegativeCancelSafetyProcess implements
        DomainEventProcess {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @MessageProducer(topic = MessageTopicEnum.CLOUD_TRIAGE_EVENT_MESSAGE)
    private CommonMessageProducer<CloudTriageEventMessageDTO> cloudTriageEventProducer;
    @Resource
    private LockUtils lockUtils;


    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(RISK_DISPOSED_CANCEL_SAFETY_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != RiskCaseDO.class) {
            return true;
        }
        DomainEventChangeDTO<RiskCaseDO> riskCaseDOEventChangeDTO = (DomainEventChangeDTO<RiskCaseDO>) eventDTO;
        //
        List<RiskCaseDO> terminalRiskCaseList = riskCaseDOEventChangeDTO.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                .stream().filter(riskCaseDO -> RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(terminalRiskCaseList)) {
            //如果不存在到终态的
            return true;
        }
        //查询最新的数据
        Set<String> eventIdSet = new HashSet<>();
        Set<String> caseIdSet = new HashSet<>();
        terminalRiskCaseList.forEach(riskCaseDO -> {
            eventIdSet.add(riskCaseDO.getEventId());
            caseIdSet.add(riskCaseDO.getCaseId());
        });
        return lockUtils.batchLockCanWait(LockKeyPreUtil.buildKeyWithEventId(eventIdSet), 1, TimeUnit.SECONDS,
                () -> handleUpdateMessage(caseIdSet));
    }

    /**
     * 处理消息通知
     *
     * @param caseIdSet
     * @return
     */
    private boolean handleUpdateMessage(Set<String> caseIdSet) {
        //查询当前的最新状态
        List<RiskCaseDO> caseDOFromDB = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(new ArrayList<>(caseIdSet))
                        //取已经呼叫的
                        .callSafety(CallSafetyEnum.CALLED.getCode())
                        .build());
        if (CollectionUtils.isEmpty(caseDOFromDB)) {
            return true;
        }
        Map<String, RiskCaseDO> caseDOMap = caseDOFromDB.stream().collect(Collectors.toMap(RiskCaseDO::getCaseId,
                Function.identity(), (v1, v2) -> v1));
        List<RiskCaseVehicleRelationDO> relationDOList = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(new ArrayList<>(caseIdSet)).build());
        List<RiskCaseDO> updateCaseList = new ArrayList<>();
        relationDOList.forEach(caseVehicleRelationDO -> {
            CloudEventTypeEnum cloudEventTypeEnum = CloudEventTypeEnum.getByRiskCaseType(
                    caseVehicleRelationDO.getType());
            RiskCaseDO riskCaseDO = caseDOMap.get(caseVehicleRelationDO.getCaseId());
            if (Objects.isNull(cloudEventTypeEnum) || Objects.isNull(riskCaseDO)) {
                return;
            }
            //发送消息
            String messageId = cloudTriageEventProducer.sendMqCommonMessage(CloudTriageEventMessageDTO.builder()
                    .eventId(caseVehicleRelationDO.getEventId())
                    .vin(caseVehicleRelationDO.getVin())
                    .eventTime(riskCaseDO.getCloseTime())
                    .reporter(RiskCaseSourceEnum.BEACON_TOWER.getDesc())
                    .eventType(cloudEventTypeEnum.getCode())
                    .status(CloudTriageEventStatusEnum.CANCELED.getCode())
                    .build(), MessageType.CLOUD_TRIAGE_EVENT);
            CheckUtil.isNotBlank(messageId, "发送消息失败");
            caseVehicleRelationDO.cancelSafety(riskCaseDO.getCloseTime());
            riskCaseDO.setCallSafety(CallSafetyEnum.CANCELLED);
            updateCaseList.add(riskCaseDO);
        });
        riskCaseVehicleRelationRepository.batchSave(relationDOList);
        if (CollectionUtils.isNotEmpty(updateCaseList)) {
            riskCaseRepository.batchSave(updateCaseList);
        }
        return true;
    }

}
