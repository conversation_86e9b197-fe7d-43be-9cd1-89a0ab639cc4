package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.result.RiskCaseUpdatedResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmFilterDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskMappingParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskMappingResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO;
import java.util.List;
import java.util.Map;
import javafx.util.Pair;

/**
 * 风险事件操作服务
 */
public interface RiskCaseOperateService {


    /**
     * 创建或更新风险事件
     *
     * @param paramDTO
     * @return
     */
    RiskCaseUpdatedResultDTO createOrUpdateRiskCase(RiskCaseUpdatedParamDTO paramDTO);

    /**
     * 将准风险事件转换为真实风险事件
     *
     * @param riskCheckingQueueItemDOList
     */
    void transRiskQueueCaseAsTrueRiskCase(List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList);

    /**
     * 关联处置的trace
     */

    RiskMappingResultDTO mappingRiskCase(RiskMappingParamDTO paramDTO);


    /**
     * 查询 end消息 对应eventId 的start消息是否存在
     */
    boolean checkStartMessageExist(String eventId);

    void updateRiskCaseAndSendMsg(CallMrmStrategyConfigDTO callMrmStrategyConfigDTO,
            RiskCaseCallMrmFilterDTO filterDTO,
            RiskCaseDO riskCaseDO);

    /**
     * 更新坐席呼叫状态
     *
     * @param riskCaseDO
     */
    void updateRiskCaseMrmCalled(RiskCaseDO riskCaseDO);

    /**
     * 手动呼叫坐席
     *
     * @param caseId
     */
    void manualCallMrm(String caseId);

    /**
     * 风险事件取消呼叫
     */
    public void cancelCallMrm(String vin, RiskCaseDO riskCaseDO,
            Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> releaseMrmStrategyConfigDTOMap);

    void cancelCallSecuritySystem(String vin, RiskCaseDO riskCaseDO, Map<Pair<Integer, Integer>, LionConfigRepositoryImpl.ReleaseSecuritySystemConfigDTO> releaseSecuritySystemConfigDTOMap);
    /**
     * 风险事件呼叫云控
     */
    void updateRiskCaseAndCallCloudCursor(CallSecuritySystemStrategyConfigDTO callSecuritySystemStrategyConfigDTO,
                                          RiskCaseCallMrmFilterDTO filterDTO,
                                          RiskCaseDO riskCaseDO);

}
