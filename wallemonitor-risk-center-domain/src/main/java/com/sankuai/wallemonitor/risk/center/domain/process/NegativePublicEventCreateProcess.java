package com.sankuai.wallemonitor.risk.center.domain.process;

import com.google.common.collect.Sets;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.carosscan.request.GroupRoleName;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.AutoCallAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.TTRgOnCallAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.EntityKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventAutoCallConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventDxGroupConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.TTRGidConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventRelatedFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventRelatedDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventDetailRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventRelatedRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventRelatedDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NegativePublicEventCreateProcess implements DomainEventProcess {

    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    @Resource
    private TTRgOnCallAdapter ttRgOnCallAdapter;

    @Resource
    private AutoCallAdapter autoCallAdapter;

    @Resource
    private NegativePublicEventRelatedRepository relatedRepository;

    @Resource
    private NegativePublicEventDetailRepository detailRepository;

    @Resource
    private NegativePublicEventRepository eventRepository;

    @Resource
    private LockUtils lockUtils;

    /**
     * 大象群配置
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_NEGATIVE_PUBLIC_EVENT_DX_GROUP_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private NegativePublicEventDxGroupConfigDTO dxGroupConfigDTO;

    /**
     * 自动外呼配置
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_NEGATIVE_PUBLIC_EVENT_AUTO_CALL_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private NegativePublicEventAutoCallConfigDTO autoCallConfigDTO;


    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.NEGATIVE_PUBLIC_EVENT_CREATE_HANDLE_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        log.info("NegativePublicEventCreateProcess# process, eventDTO = {}", eventDTO);
        if (eventDTO.getDomainClass() != NegativePublicEventDO.class) {
            return true;
        }
        //状态发生变化的
        DomainEventChangeDTO<NegativePublicEventDO> typedDomainEvent = (DomainEventChangeDTO<NegativePublicEventDO>) eventDTO;
        log.info("NegativePublicEventCreateProcess# process, typedDomainEvent = {}", typedDomainEvent);
        List<NegativePublicEventDO> initItemList = new ArrayList<>(typedDomainEvent.getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getChangeType(),
                        EntityKeyConstant.ENTITY_EMPTY_TO_VALUE)));
        log.info("NegativePublicEventCreateProcess# process, initItemList = {}", initItemList);
        if (CollectionUtils.isEmpty(initItemList)) {
            return true;
        }
        return handleProcessMsg(initItemList);
    }

    /**
     * 处理消息
     *
     * @param eventDOList
     * @return
     */
    public boolean handleProcessMsg(List<NegativePublicEventDO> eventDOList) {
        eventDOList.forEach(eventDO -> {
            try {
                // 创建大象群聊 发送消息
                createDxGroup(eventDO);
                // 自动外呼
                autoCall(eventDO.getEventId());
            } catch (Exception e) {
                log.error("handleProcessMsg# error", e);
            }
        });
        return true;
    }

    /**
     * 创建大象群聊
     *
     * @param eventDO
     */
    private void createDxGroup(NegativePublicEventDO eventDO) {
        CheckUtil.isNotNull(dxGroupConfigDTO, "获取大象群配置失败");

        List<String> users = new ArrayList<>();
        // 上报人
        String reporter = eventDO.getReporter();
        String reporterName = reporter.contains("/") ? reporter.split("/")[1] : reporter;
        users.add(reporterName);
        // 值班人
        users.addAll(dxGroupConfigDTO.getGroupMemberList());
        //  获取值班人员信息对，包括值班人员列表和值班人员角色信息列表
        Pair<List<String>, List<GroupRoleName>> ttOncallInfoPair = getGroupMemberFromTT(
                dxGroupConfigDTO.getTtRGidConfigDTOList());
        if (CollectionUtils.isNotEmpty(ttOncallInfoPair.getKey())) {
            users.addAll(ttOncallInfoPair.getKey());
        }

        //  创建大象群聊，返回群聊ID
        Long gid = dxNoticeAdapter.createDxGroup(getTitle(eventDO.getEventId()), dxGroupConfigDTO.getOwner(),
                new ArrayList<>(), users, dxGroupConfigDTO.getBotAdmins());
        //  更新群聊ID
        updateGid(eventDO.getEventId(), gid);
        // 更新群聊角色名称
        dxNoticeAdapter.updateDxGroupRoleName(gid, ttOncallInfoPair.getValue());

        // 发送信息
        String url = dxGroupConfigDTO.getNegativePublicEventUrl();
        NegativePublicEventDO tempEventDO = eventRepository.getByEventId(null, eventDO.getEventId());
        if (Objects.nonNull(tempEventDO)) {
            url += tempEventDO.getId();
        }
        String text = "发生一起负外部性事件，详细信息如下\n"
                + "【发生地区】:" + eventDO.getCity() + "-" + eventDO.getDistrict() + "\n"
                + "【发生时间】:" + DatetimeUtil.formatTime(eventDO.getOccurTime()) + "\n"
                + "【上报人】:" + eventDO.getReporter() + "\n"
                + "【事件类型/来源】:" + buildTypeAndSourceStr(eventDO) + "\n"
                + "【问题描述】:" + (StringUtils.isBlank(eventDO.getEventDesc()) ? CommonConstant.EMPTY
                : eventDO.getEventDesc()) + "\n"
                + "【[工单地址|" + url + "]】";

        dxNoticeAdapter.sendDxMessage(gid, text);
    }

    /**
     * 自动外呼
     *
     * @param eventId
     */
    private void autoCall(String eventId) {
        // 1 获取配置
        CheckUtil.isNotNull(autoCallConfigDTO, "获取自动外呼配置失败");

        // 2 判断当前时间是否在自动外呼时间范围内
        if (!isAutoCall(autoCallConfigDTO.getAutoCallOnCallTimeRange())) {
            log.info("autoCall# not in auto call time range");
            return;
        }

        // 3 获取值班人员
        List<String> allOnCallUserList = new ArrayList<>();
        List<String> onCallList = ttRgOnCallAdapter.queryRgOnCallUserList(autoCallConfigDTO.getAutoCallRgId());
        allOnCallUserList.addAll(onCallList);
        // 2 获取备选呼叫人
        allOnCallUserList.addAll(autoCallConfigDTO.getAutoCallBackupCallerList());
        // 3 发起自动外呼
        autoCallAdapter.autoCall(eventId, autoCallConfigDTO.getAutoCallMediaBody(), allOnCallUserList);
    }


    /**
     * 判断当前时间是否在自动外呼时间范围内
     *
     * @return
     */
    private boolean isAutoCall(List<String> autoCallOnCallTimeRange) {
        // 1 lion配置有效性检查
        if (CollectionUtils.isEmpty(autoCallOnCallTimeRange) || autoCallOnCallTimeRange.size() != 2) {
            return false;
        }
        // 2 解析配置的时间范围
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalTime start = LocalTime.parse(autoCallOnCallTimeRange.get(0), formatter);
        LocalTime end = LocalTime.parse(autoCallOnCallTimeRange.get(1), formatter);

        // 3 获取当前时间, 比较当前时间是否在范围内
        LocalTime now = LocalTime.now();
        return !now.isBefore(start) && !now.isAfter(end);
    }

    /**
     * 从TT获取值班人员
     *
     * @return
     */
    private Pair<List<String>, List<GroupRoleName>> getGroupMemberFromTT(
            List<TTRGidConfigDTO> configDTOList) {
        // 1、 拉取配置
        if (CollectionUtils.isEmpty(configDTOList)) {
            log.error("getGroupMemberFromTT# configDTOList is empty", new IllegalArgumentException("TT排班配置错误"));
            return new Pair<>(new ArrayList<>(), new ArrayList<>());
        }
        // 值班人员列表
        List<String> allOnCallUserList = new ArrayList<>();
        // 值班人员mis2roleName
        List<GroupRoleName> groupRoleNamelist = new ArrayList<>();

        // 2、根据配置的TT oncall Id 获取值班人员
        configDTOList.forEach(rgidConfigDTO -> {
            if (Objects.isNull(rgidConfigDTO.getRgId())) {
                log.error("getGroupMemberFromTT# rgid is empty", new IllegalArgumentException("TT排班配置错误"));
                return;
            }
            // 根据 rgid 查询 TT值班人员
            List<String> misList = ttRgOnCallAdapter.queryRgOnCallUserList(rgidConfigDTO.getRgId());
            if (CollectionUtils.isEmpty(misList)) {
                return;
            }
            // 维护值班人员列表
            allOnCallUserList.addAll(misList);
            // 维护值班人员mis2roleName
            misList.forEach(mis -> {
                groupRoleNamelist.add(GroupRoleName.builder().misId(mis).roleName(rgidConfigDTO.getRoleName()).build());
            });
        });

        // 输出人员名单
        return new Pair<>(allOnCallUserList, groupRoleNamelist);
    }

    /**
     * 构建类型来源
     *
     * @param eventDO
     * @return
     */
    private String buildTypeAndSourceStr(NegativePublicEventDO eventDO) {
        // 查询关联表
        NegativePublicEventRelatedDOQueryParamDTO paramDTO = NegativePublicEventRelatedDOQueryParamDTO.builder()
                .eventId(eventDO.getEventId())
                .fieldName(NegativePublicEventRelatedFieldEnum.SOURCE.getName()).build();
        List<NegativePublicEventRelatedDO> relatedDOList = relatedRepository.queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(relatedDOList)) {
            return "";
        }
        // 拼接类型/来源字符串
        return relatedDOList.stream()
                .map(relatedDO -> {
                    Integer sourceCode = Integer.valueOf(relatedDO.getFieldValue());
                    NegativePublicEventSourceEnum sourceEnum = NegativePublicEventSourceEnum.fromCode(sourceCode);
                    if (Objects.isNull(sourceEnum)) {
                        return "";
                    }
                    return String.format("%s/%s", eventDO.getType() == null ? "" : eventDO.getType().getDesc(),
                            sourceEnum.getDesc());
                })
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining(","));
    }

    /**
     * 获取标题
     *
     * @param eventId
     * @return
     */
    private String getTitle(String eventId) {
        NegativePublicEventDetailDO detailDO = detailRepository.getByEventId(eventId);
        if (Objects.isNull(detailDO)) {
            return "";
        }
        return detailDO.getTitle();
    }

    /**
     * 更新群id
     *
     * @param eventId
     * @param gid
     */
    private void updateGid(String eventId, Long gid) {
        if (Objects.isNull(gid) || Objects.equals(0L, gid)) {
            return;
        }
        Set<String> lockKeys = LockKeyPreUtil.buildKeyWithEventId(Sets.newHashSet(eventId));

        lockUtils.batchLockCanWait(lockKeys, () -> {
            NegativePublicEventDO eventDO = eventRepository.getByEventId(null, eventId);
            if (Objects.isNull(eventDO)) {
                return;
            }
            eventDO.setGroupId(String.valueOf(gid));
            eventRepository.save(eventDO);
        });
    }


}
