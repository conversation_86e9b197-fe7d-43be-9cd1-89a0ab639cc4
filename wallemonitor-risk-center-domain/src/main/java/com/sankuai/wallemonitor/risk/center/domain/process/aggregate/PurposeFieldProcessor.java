package com.sankuai.wallemonitor.risk.center.domain.process.aggregate;

import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AggregateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.DxRichTextUtils;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 用车目的字段处理器
 */
@Component
public class PurposeFieldProcessor implements TemplateFieldProcessor {

    @Override
    public void processField(DxCardTemplateDO template, List<RiskCaseDO> riskCaseList, AggregateAlertContext context,
            AlertTemplateConfigDTO alertTemplate, List<String> aggregateBy) {
        if (CollectionUtils.isEmpty(riskCaseList)) {
            return;
        }

        List<String> purposeList = riskCaseList.stream().map(caseDO -> buildPurpose(caseDO, context))
                .collect(Collectors.toList());

        // 根据聚合字段进行优化
        String purposeValue = optimizeForAggregation(purposeList, aggregateBy);

        template.setPurpose(purposeValue);
        template.setShowPurpose(true);
    }

    @Override
    public boolean supports(String fieldCode) {
        return TemplateFieldEnum.PURPOSE.getCode().equals(fieldCode);
    }

    /**
     * 构建用车目的
     */
    private String buildPurpose(RiskCaseDO caseDO, AggregateAlertContext context) {
        VehicleInfoDO vehicleInfo = context.getVehicleInfo(caseDO.getCaseId());
        if (vehicleInfo != null) {
            return StringUtils.defaultString(vehicleInfo.getPurpose(), CommonConstant.UNKNOWN);
        }
        return CommonConstant.UNKNOWN;
    }

    /**
     * 根据聚合字段优化显示内容
     */
    private String optimizeForAggregation(List<String> purposeList, List<String> aggregateBy) {
        if (CollectionUtils.isEmpty(purposeList)) {
            return CommonConstant.UNKNOWN;
        }

        // VIN聚合时，所有车辆的用车目的相同，只取第一个，且加粗显示
        if (CollectionUtils.isNotEmpty(aggregateBy) && aggregateBy.contains(AggregateFieldEnum.VIN.getCode())) {
            return DxRichTextUtils.toBold(purposeList.get(0));
        }

        // 非聚合情况，显示所有用车目的
        return Joiner.on(", ").skipNulls().join(purposeList);
    }
} 