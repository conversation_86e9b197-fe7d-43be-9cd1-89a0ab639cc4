package com.sankuai.wallemonitor.risk.center.domain.result;

import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleObstacleInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ISWaitingInQueueResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 前方同lane或者后继lane的直接障碍物
     */
    private VehicleObstacleInfoDTO frontObstacle;

    /**
     * 前方同lane或者后继lane的障碍物list,满足本车、后车车道，就可以
     */
    @Default
    private List<VehicleObstacleInfoDTO> frontObstacleList = new ArrayList<>();

    /**
     * 后方同lane或者后继lane的障碍物
     */
    private VehicleObstacleInfoDTO behindObstacle;

    /**
     * 后方障碍物list,满足本车、后车车道，就可以
     */
    @Default
    private List<VehicleObstacleInfoDTO> behindObstacleList = new ArrayList<>();

    /**
     * 距离最近的车道边缘距离
     */
    private Double distanceToNearCurb;

    /**
     * 距离最近的车道边缘距离
     */
    private List<Double> distanceToNearCurbList;

    /**
     * 是否压线
     */
    private Boolean crossLine;

    /**
     * 车辆判断的所有障碍物精简信息
     */
    @Default
    private List<VehicleObstacleInfoDTO> allObstacleList = new ArrayList<>();


    /**
     * 车辆的位置
     */
    private PositionDO vehiclePosition;

    /**
     * 车辆的前序位置（配置的前序）
     */
    private VehicleInQueuePositionDTO preVehiclePosition;

    /**
     * 车辆过去的连续位置点
     */
    @Default
    private List<VehicleInQueuePositionDTO> lastPosition = new ArrayList<>();

    /**
     * 障碍物所在车道的id
     */
    private String obstacleInLaneId;

    /**
     * 自车所在车道的id
     */
    private String vehicleCurLaneId;

    /**
     * 自车所在的后继id
     */
    private String vehicleSuccessorLaneId;

    /**
     * 周围的LaneId
     */
    @Default
    private Map<String, String> vehicleAroundLaneId = new HashMap<>();

    /**
     * 自车所在的车道位姿: left / middle / right
     *
     */
    private String vehicleLaneSectionType;

    /**
     * 未来路由点的laneIdList集合
     */
    @Default
    private List<String> futureLaneIds = new ArrayList<>();

    /**
     * 当前可用车道
     */
    @Default
    private List<String> usableLaneIds = new ArrayList<>();

    /**
     * 是否是单车道
     */
    private Boolean singleLane;
    /**
     * 前方障碍物切换次数
     */
    private Integer frontObsSwitchCount;
    /**
     * 前方障碍物远离总距离
     */
    private Double frontAwayTotalDistance;
    /**
     * 后方障碍物切换次数
     */
    private Integer behindObsSwitchCount;
    /**
     * 后方障碍物靠近总距离
     */
    private Double frontCloseTotalDistance;
    /**
     * 后方障碍物远离总距离
     */
    private Double behindObsAwayTotalDistance;
    /**
     * 后方障碍物靠近总距离
     */
    private Double behindCloseTotalDistance;

    /**
     * 传递的风险类型
     */
    private ISCheckCategoryEnum firstAutoCarInRisk;
    /**
     * 切换后的风险类型
     */
    private ISCheckCategoryEnum switchObstacleCategory;
    /**
     * 自车连续排队的车
     */
    @Default
    private List<String> autoCarInQueueList = new ArrayList<>();
    /**
     * 是否在路口排队
     */
    private boolean inJunctionWait;
    /**
     * 前方多车并行个数
     */
    private Integer multiCarInSameDistanceCount;
    /**
     * 并行车辆的id
     */
    @Default
    private List<List<String>> inSameDistanceObstacleIdList = new ArrayList<>();

    private Boolean nonTrafficLightWait;
    private boolean isInMixedBikingLane;
    private boolean nonBehindObs;
    private boolean nonJunctionUsableLane;
    private Integer multiTurnCount;
    private List<String> seatInterventionCaseId;

    /**
     * 更新自车周围的LANE
     * 
     * @param lane2HdMapElementGeoDOMap
     */
    public void updateAroundLane(Map<String, HdMapElementGeoDO> lane2HdMapElementGeoDOMap) {
        if (MapUtils.isEmpty(lane2HdMapElementGeoDOMap)) {
            return;
        }
        lane2HdMapElementGeoDOMap.forEach((aroundType, hdMapElementGeoDO) -> {
            if (Objects.isNull(hdMapElementGeoDO)) {
                return;
            }
            vehicleAroundLaneId.put(aroundType, hdMapElementGeoDO.getId());
        });

    }


    /**
     * 自车所关联的全部车道id
     */
    @Default
    private List<String> vehicleInLaneIdList = new ArrayList<>();
    private Double vehiclePreToCurDistance;

    public void add(VehicleObstacleInfoDTO obstacle) {
        if (Objects.isNull(obstacle)) {
            return;
        }
        allObstacleList.add(obstacle);
    }


    /**
     * 获取所有障碍物信息
     *
     * @return
     */
    public List<String> toObstacleDetail() {
        List<String> result = new ArrayList<>();
        allObstacleList.forEach((obstacle) -> {
            // id-角度-距离-类型
            Double angle = obstacle.getAngle();
            Double distance = obstacle.getDistance();
            String type = obstacle.getType();
            String fineType = obstacle.getFineType();
            String laneId = obstacle.getLaneId();
            result.add(String.format("%s-%s-%s-%s-%s-%s", obstacle.getObstacleId(), laneId, angle, distance, type,
                    fineType));
        });
        return result;
    }


    public List<Double> preToCurDistance() {
        return lastPosition.stream().map(VehicleInQueuePositionDTO::getDistance)
                .collect(Collectors.toList());
    }

    public void updateLaneSelectionType(HdMapElementGeoDO vehicleCurLane) {
        if (Objects.isNull(vehicleCurLane)) {
            return;
        }
        if (StringUtils.isBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT))
                && StringUtils.isBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT))) {
            this.vehicleLaneSectionType = "middle";
            this.setSingleLane(true);
        }
        boolean hasLeft = StringUtils.isNotBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT));
        boolean hasRight = StringUtils.isNotBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT));
        // 如果有左、右，是中间
        // 如果有左没右，是最右
        // 如果有右没左，是最左
        if (hasLeft && hasRight) {
            this.vehicleLaneSectionType = "middle";
        } else if (hasLeft && !hasRight) {
            this.vehicleLaneSectionType = "right";
        } else if (!hasLeft && hasRight) {
            this.vehicleLaneSectionType = "left";
        }
    }


}
