package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.component.AlertTemplateBuilder;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskAggregateAlertService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateStrategy;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateAlertConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.UpgradeStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskAlertRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.UserInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskAlertRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxCardParamVTO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
/**
 * 聚合告警服务实现
 */
@Service
@Slf4j
public class RiskAggregateAlertServiceImpl implements RiskAggregateAlertService {

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private VehicleInfoRepository vehicleInfoRepository;

    @Resource
    private Map<String, AggregateStrategy> aggregateStrategyMap;

    @Resource
    private RiskAlertRecordRepository riskAlertRecordRepository;

    @Resource
    private AlertTemplateBuilder alertTemplateBuilder;

    @Resource
    private UserInfoRepository userInfoRepository;

    @Override
    public void processAggregateAlert(List<RiskCaseDO> riskCaseList) {
        if (CollectionUtils.isEmpty(riskCaseList)) {
            return;
        }
        log.info("开始处理风险事件聚合告警，caseId: {}",
                JacksonUtils.to(riskCaseList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList())));

        AggregateAlertConfigDTO config = lionConfigRepository.getAggregateAlertConfig();
        if (config == null || !config.isValid()) {
            log.debug("聚合告警配置未启用或无效");
            return;
        }

        List<AggregateStrategyConfigDTO> validStrategies = config.getValidStrategies();
        if (CollectionUtils.isEmpty(validStrategies)) {
            log.debug("没有有效的聚合策略");
            return;
        }

        AggregateAlertContext context = buildAggregateAlertContext(riskCaseList);

        for (RiskCaseDO riskCaseDO : riskCaseList) {
            for (AggregateStrategyConfigDTO validStrategy : validStrategies) {
                processStrategyAlert(riskCaseDO, validStrategy, context);
            }
        }
        log.info("风险事件聚合告警处理完成");
    }

    private AggregateAlertContext buildAggregateAlertContext(List<RiskCaseDO> riskCaseList) {
        List<String> caseIdList = riskCaseList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        Map<String, String> caseId2VinMap = riskCaseVehicleRelationRepository.queryByParam(
                        RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIdList).build()).stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, RiskCaseVehicleRelationDO::getVin,
                        (o1, o2) -> o1));
        Map<String, VehicleInfoDO> vehicleInfoMap = vehicleInfoRepository.queryByVinList(
                        Lists.newArrayList(caseId2VinMap.values())).stream()
                .collect(Collectors.toMap(VehicleInfoDO::getVin, Function.identity(), (o1, o2) -> o1));
        Map<String, VehicleInfoDO> caseId2VehicleInfoMap = caseId2VinMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> vehicleInfoMap.get(entry.getValue())));

        AggregateAlertContext context = new AggregateAlertContext();
        context.updateCaseId2VehicleInfoMap(caseId2VehicleInfoMap);
        return context;
    }

    private void processStrategyAlert(RiskCaseDO riskCaseDO, AggregateStrategyConfigDTO strategyConfig,
            AggregateAlertContext context) {
        VehicleInfoDO vehicleInfoDO = context.getVehicleInfo(riskCaseDO.getCaseId());
        if (strategyConfig == null || vehicleInfoDO == null) {
            log.warn("参数为空");
            return;
        }
        // 查找对应的策略实现
        String strategyId = strategyConfig.getAlertPolicy();
        AggregateStrategy strategy = aggregateStrategyMap.get(strategyId);
        if (strategy == null) {
            log.warn("未找到聚合策略实现: {}", strategyId);
            return;
        }
        if (!strategy.isHit(riskCaseDO, strategyConfig, vehicleInfoDO)) {
            log.info("策略未命中, policy: {}", strategy.getAlertPolicy());
            return;
        }

        try {
            log.info("策略命中, 执行策略处理, policy: {}", strategy.getAlertPolicy());
            List<AggregateResultDTO> alertResults = strategy.process(riskCaseDO, strategyConfig, context);
            if (CollectionUtils.isEmpty(alertResults)) {
                log.info("可告警的风险事件聚合结果为空，跳过处理");
                return;
            }
            log.info("可告警的风险事件聚合结果: {}", JacksonUtils.to(alertResults));

            // 发送告警
            for (AggregateResultDTO result : alertResults) {
                sendAggregateAlert(strategyConfig, result, context);
            }
        } catch (Exception e) {
            log.error("处理策略聚合告警失败: {}", strategyId, e);
        }
    }


    /**
     * 发送聚合告警
     */
    private void sendAggregateAlert(AggregateStrategyConfigDTO strategyConfig, AggregateResultDTO result,
            AggregateAlertContext context) {
        try {
            // 构建告警参数
            DxCardTemplateDO dxCardTemplateDO = alertTemplateBuilder.buildTemplate(strategyConfig,
                    result.getRiskCaseList(), context);

            if (dxCardTemplateDO == null) {
                log.error("构建告警参数失败");
                return;
            }
            long version = System.currentTimeMillis();
            // 生成唯一的业务ID
            String outBizId = strategyConfig.getAlertPolicy() + "_" + version;

            // 查询uidList
            List<Long> uidList = getUidListForAlert(result.getRiskCaseList(), context);

            DxCardParamVTO dxCardParam = DxCardParamVTO.builder()
                    .templateId(Long.valueOf(strategyConfig.getAlertTemplate().getTemplateId()))
                    .groupIdList(strategyConfig.getAlertTemplate().getGroupIdList())
                    .arguments(dxCardTemplateDO.toArguments())
                    .abstractText(dxCardTemplateDO.summarize())
                    .outBizId(outBizId)
                    .version(version)
                    .uidList(uidList)
                    .build();
            
            String messageId = dxNoticeAdapter.sendDxCard(dxCardParam);
            if (StringUtils.isNotBlank(messageId)) {
                log.info("聚合告警发送成功: outBizId={}, aggregateKey={}, messageId={}", outBizId,
                        result.getAggregateKey(), messageId);
                saveAlertRecord(strategyConfig, result, outBizId, messageId, dxCardParam);
            } else {
                log.warn("聚合告警发送失败: outBizId={}, aggregateKey={}", outBizId, result.getAggregateKey());
            }
        } catch (Exception e) {
            log.error("发送聚合告警异常: strategyId={}, aggregateKey={}", strategyConfig.getAlertPolicy(),
                    result.getAggregateKey(), e);
        }
    }

    /**
     * 保存告警记录
     */
    private void saveAlertRecord(AggregateStrategyConfigDTO strategyConfig, AggregateResultDTO result,
            String outBizId, String messageId, DxCardParamVTO dxCardParam) {
        try {
            // 获取事件ID列表
            String eventIds = result.getRiskCaseList().stream()
                    .map(RiskCaseDO::getCaseId)
                    .collect(Collectors.joining(","));

            // 构建告警记录
            RiskAlertRecordDO alertRecord = RiskAlertRecordDO.builder()
                    .alertPolicy(strategyConfig.getAlertPolicy())
                    .configName(strategyConfig.getConfigName())
                    .bizId(outBizId)
                    .messageId(messageId)
                    .groupId(Joiner.on(",").join(dxCardParam.getGroupIdList()))
                    .eventIds(eventIds)
                    .arguments(dxCardParam.getArguments())
                    .status(AlertRecordStatusEnum.UNPROCESSED)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .upgradeStatus(UpgradeStatusEnum.findByCode(
                            strategyConfig.getAlertTemplate().getIsUpdate()))
                    .build();

            riskAlertRecordRepository.save(alertRecord);
            log.info("告警记录保存成功: outBizId={}, messageId={}", outBizId, messageId);
        } catch (Exception e) {
            log.error("保存告警记录失败: outBizId={}, messageId={}", outBizId, messageId, e);
        }
    }

    /**
     * 获取告警相关的UID列表
     * 用于@人功能
     */
    @Override
    public List<Long> getUidListForAlert(List<RiskCaseDO> riskCaseList, AggregateAlertContext context) {
        if (CollectionUtils.isEmpty(riskCaseList)) {
            return Lists.newArrayList();
        }

        try {
            // 1. 获取第一个风险事件的VIN
            String firstCaseId = riskCaseList.get(0).getCaseId();
            List<RiskCaseVehicleRelationDO> vehicleRelations = riskCaseVehicleRelationRepository.queryByParam(
                    RiderCaseVehicleRelationDOParamDTO.builder()
                            .caseIdList(Lists.newArrayList(firstCaseId))
                            .build());

            if (CollectionUtils.isEmpty(vehicleRelations)) {
                return Lists.newArrayList();
            }
            List<String> vinList = Lists.newArrayList(vehicleRelations.get(0).getVin());

            if (CollectionUtils.isEmpty(vinList)) {
                return Lists.newArrayList();
            }

            // 2. VIN -> MIS
            List<String> misList = userInfoRepository.batchGetSubstituteByVin(vinList);

            if (CollectionUtils.isEmpty(misList)) {
                return Lists.newArrayList();
            }

            // 3. MIS -> UID
            List<Long> uidList = userInfoRepository.batchGetUidsByMis(misList);

            // 4. 过滤掉null值并去重
            return uidList.stream()
                    .filter(uid -> uid != null)
                    .distinct()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取UID列表失败", e);
            return Lists.newArrayList();
        }
    }

}