package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import static com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum.CANT_FOUND_ANY;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleeve.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCheckingQueueService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionManager;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.LongWaitAreaConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCheckingQueueItemFactory;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCheckingQueueItemFactory.CreateRiskCheckingQueueDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckingExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCheckQueueQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class RiskCheckingQueueServiceImpl implements RiskCheckingQueueService {

    private final static Integer BATCH_SIZE = 10;

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private ISCheckActionManager iSCheckActionManager; // 停滞不当预检Action Manager

    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @Override
    public RiskCheckingQueueItemDO getCheckingItemByEventId(String eventId) {
        List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList = riskCheckQueueRepository
                .queryByParam(RiskCheckQueueQueryParam.builder().eventId(eventId).build());
        return CollectionUtils.isEmpty(riskCheckingQueueItemDOList) ? null : riskCheckingQueueItemDOList.get(0);
    }

    /**
     * 批量标记为检查中
     *
     * @param itemList
     * @return
     */
    @Override
    public void batchUpdateItemToChecking(List<RiskCheckingQueueItemDO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        List<String> tmpCaseIdList = itemList.stream().map(RiskCheckingQueueItemDO::getTmpCaseId)
                .collect(Collectors.toList());
        List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList = riskCheckQueueRepository
                .queryByParam(RiskCheckQueueQueryParam.builder().tmpCaseIdList(tmpCaseIdList).build());
        List<RiskCheckingQueueItemDO> updatedRiskCheckingItemList = new ArrayList<>();

        // 计算下一轮执行时间
        RiskAutoCheckConfigDTO checkQueueConfig = lionConfigRepository.getRiskCheckingConfig();
        Date nextRoundTime = DatetimeUtil.getNSecondsAfterDateTime(new Date(),
                checkQueueConfig.getRoundIntervalSeconds());

        riskCheckingQueueItemDOList.stream()
                // 只过滤出来不在checking状态的
                // 锁内double check : 需要不在checking，且状态是允许checking的
                .filter(item -> !item.getChecking()
                        && RiskQueueStatusEnum.listNeedCheckingStatus().contains(item.getStatus()))
                // 添加到允许修改的list里面
                .peek(updatedRiskCheckingItemList::add)
                // 再修改成checking状态
                .forEach(item -> {
                    item.setChecking(true);
                    item.setNextRoundTime(nextRoundTime);
                });
        if (CollectionUtils.isNotEmpty(updatedRiskCheckingItemList)) {
            riskCheckQueueRepository.batchSave(updatedRiskCheckingItemList);
        }
    }

    /**
     * 使用检查结果更新检查队列
     *
     * @param tmpCaseIdList
     * @param checkResultMap
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAndSaveRiskCheckingQueueItem(List<String> tmpCaseIdList,
            Map<String, ISCheckActionResult> checkResultMap) {
        if (CollectionUtils.isEmpty(tmpCaseIdList) || MapUtils.isEmpty(checkResultMap)) {
            return;
        }
        List<RiskCheckingQueueItemDO> latest = riskCheckQueueRepository
                .queryByParam(RiskCheckQueueQueryParam.builder().tmpCaseIdList(tmpCaseIdList).build());
        List<RiskCheckingQueueItemDO> validatingQueueItemList = latest.stream()
                .filter(item -> !RiskQueueStatusEnum.isTerminatedStatus(item.getStatus())).collect(Collectors.toList());
        // 更新结果
        validatingQueueItemList
                // 判断是否需要更新
                .forEach(item -> {
                    ISCheckActionResult resultDTO = checkResultMap.get(item.getTmpCaseId());
                    log.info("runInLock, resultDTO: {}", JacksonUtils.to(resultDTO));
                    if (Objects.isNull(resultDTO)) {
                        return;
                    }
                    switch (item.getType()) {
                        case VEHICLE_STAND_STILL:
                            // 设置状态、重检标识、动态重检标识
                            updateOneRiskCheckingQueueItem(item, resultDTO);
                            // 本次非动态轮次，根据配置判断是否需要重置和动态检查
                            if (!item.getNextRoundDynamic()) {
                                // 在满足动态延时的情况下，重置队列项的状态和重检标识，根据检测结果进行更新。
                                resetForDynamicChecking(item, resultDTO);
                                break;
                            }
                            // 如果标记为 【动态轮次】，需要检出一个终态的结果
                            if (RiskQueueStatusEnum.isTerminatedStatus(item.getStatus())) {
                                // 如果是终态，不做处理
                                break;
                            }
                            // 如果检出非终态
                            if (Boolean.TRUE.equals(resultDTO.withSpeed)) {
                                // 如果车辆已经带速，则正常走下一轮
                                // 并清理动态轮次的表示
                                item.setNextRoundDynamic(false);
                                break;
                            }
                            // 如果是停滞状态，强行遏制
                            String checkSource = CommonConstant.DEFAULT_ACTION;
                            ISCheckCategoryEnum checkCategoryEnum = CANT_FOUND_ANY;
                            item.setCheckResult(RiskCheckResultDO.builder()
                                    // 来源
                                    .checkSource(checkSource)
                                    // 时间
                                    .checkTime(new Date())
                                    // 被确认
                                    .category(checkCategoryEnum)
                                    // 结果
                                    .extra(ReflectUtils.getNonNullFieldAndValue(resultDTO.getActionResult()))
                                    //
                                    .build());
                            // 确认
                            item.confirm(RiskQueueStatusEnum.CONFIRMED_RISK, checkSource, checkCategoryEnum);
                            break;
                        default:
                            log.warn("unsupported type: {}", item.getType());
                    }
                });
        log.info("runInLock, latest: {}", JacksonUtils.to(latest));
        latest.forEach(item -> item.setChecking(false));  // 把所有预检中状态复位
        riskCheckQueueRepository.batchSave(latest); // 存库
    }

    /**
     * 检查是否需要重置和动态检查
     * 1.绿灯起步
     *
     * @param queueItem
     * @param result
     */
    private void resetForDynamicChecking(RiskCheckingQueueItemDO queueItem, ISCheckActionResult result) {
        RiskAutoCheckConfigDTO config = lionConfigRepository.getRiskCheckingConfig();
        if (Objects.isNull(config)) {
            return;
        }

        // 不满足动态延迟校验
        if (!Boolean.TRUE.equals(result.isDynamicReCheck())) {
            return;
        }
        // 如果需要动态延迟检查，重置状态和结果
        resetQueueItemForDynamicCheck(queueItem, null);
        // 设置下一轮为动态轮次
        queueItem.setNextRoundDynamic(true);
    }

    /**
     * 更新停滞不当的预检结果
     *
     * @param queueItemDO
     * @param resultDTO
     */
    @Override
    public void updateOneRiskCheckingQueueItem(RiskCheckingQueueItemDO queueItemDO, ISCheckActionResult resultDTO) {
        // 取action
        String finalCheckActionName = resultDTO.getActionName();
        ISCheckCategoryEnum checkCategoryEnum = resultDTO.getCategoryEnum();
        Map<String, Object> result1 = ReflectUtils.getNonNullFieldAndValue(resultDTO.getActionResult());
        RiskCheckResultDO checkResult = RiskCheckResultDO.builder().category(checkCategoryEnum)
                // 来源设置为actionName
                .checkSource(finalCheckActionName)
                // 检查开始时间
                .checkTime(resultDTO.getStartCheckTime())
                // 检查持续时间
                .duration(resultDTO.getDuration())
                // 获取执行的结果
                .extra(result1)
                .build();
        // 更新
        queueItemDO.updateCheckResult(checkResult);
        if (resultDTO.withRisk() || resultDTO.cantVerify()) {
            // 确认风险
            queueItemDO.confirm(RiskQueueStatusEnum.CONFIRMED_RISK, finalCheckActionName, checkCategoryEnum);
        } else if (resultDTO.getNeedReCheck()) {
            // 需要重试，do nothing
        } else {
            // 取消
            queueItemDO.cancel(new Date(), finalCheckActionName, checkCategoryEnum);
        }
        // 轮次增加
        queueItemDO.increaseRound();
    }

    /**
     * 批量计算检查结果
     *
     * @param queueItemList
     * @return
     */
    public Map<String, ISCheckActionResult> batchCalcCheckingResult(List<RiskCheckingQueueItemDO> queueItemList) {
        if (CollectionUtils.isEmpty(queueItemList)) {
            return new HashMap<>();
        }

        Map<String, ISCheckActionResult> results = new HashMap<>();
        Lists.partition(queueItemList, BATCH_SIZE).forEach(subList -> {
            try {
                List<ISCheckActionResult> resultDTOList = iSCheckActionManager.doCheck(subList);
                resultDTOList.forEach(result -> results.put(result.getCaseId(), result));
            } catch (Exception e) {
                log.error("rickCheckActionManager.process failed. subList: {}", JacksonUtils.to(subList), e);
            }
        });
        return results;
    }

    @Override
    public void resetQueueItemForDynamicCheck(RiskCheckingQueueItemDO queueItem, RiskCheckResultDO lastCheckResultDO) {
        queueItem.setStatus(RiskQueueStatusEnum.VALIDATING);
        queueItem.setCheckResult(lastCheckResultDO);
        // 清除前一轮的结果
        List<RiskCheckResultDO> lastCheckResultList = Optional.ofNullable(queueItem.getExtInfo())
                .map(RiskCheckingExtInfoDO::getLastCheckResult).orElse(null);
        if (CollectionUtils.isNotEmpty(lastCheckResultList)) {
            lastCheckResultList.remove(lastCheckResultList.size() - 1);
        }
        queueItem.setRound(Math.max(queueItem.getRound() - 1, 0));
    }

    /**
     * 将停滞不当事件写入检查队列
     *
     * @param paramDTO
     * @return
     */
    @Override
    public void append(RiskCaseUpdatedParamDTO paramDTO) {
        try {
            // 检查车辆列表不能为空
            List<String> vinList = paramDTO.getVinList();
            CheckUtil.isNotEmpty(vinList, "风险事件关联的车辆列表不能为空");

            // 查询或者构建 风险事件队列实体
            RiskCheckQueueQueryParam param = RiskCheckQueueQueryParam.builder().eventId(paramDTO.getEventId()).build();
            List<RiskCheckingQueueItemDO> checkingQueueItem = riskCheckQueueRepository.queryByParam(param);
            if (CollectionUtils.isNotEmpty(checkingQueueItem)) {
                log.warn("RiskCheckingQueueItemDO of event_id exists, {}", checkingQueueItem);
                return;
            }
            if (vinList.size() > 1) {
                log.error("当前事件关联多个车辆，不符合预期但会取第一台车辆进行后续逻辑。{}", JacksonUtils.to(paramDTO), new RuntimeException());
            }

            // 3.2 创建风险事件队列实体
            RiskCheckingQueueItemDO queueItem = RiskCheckingQueueItemFactory.createRiskCheckingQueueItem(
                    CreateRiskCheckingQueueDOParamDTO.builder().caseId(paramDTO.getCaseId()).vin(vinList.get(0))  // 这里只取第一个车辆
                            .type(paramDTO.getType()).eventId(paramDTO.getEventId()).source(paramDTO.getSource())
                            .occurTime(paramDTO.getTimestamp()).recallTime(paramDTO.getRecallTime()).build());

            // 保存到数据库 3.3 保存到数据库
            riskCheckQueueRepository.save(queueItem);
        } catch (Exception e) {
            log.error("enqueueStagnantEventsToCheckQueue error, paramDTO:{}", JacksonUtils.to(paramDTO), e);
        }
    }

    @Override
    public void append(List<RiskCaseUpdatedParamDTO> paramDTOList) {
        if (CollectionUtils.isEmpty(paramDTOList)) {
            log.warn("paramDTOList is empty, nothing to append");
            return;
        }

        try {
            List<RiskCheckingQueueItemDO> queueItems = paramDTOList.stream()
                    .filter(paramDTO -> CollectionUtils.isNotEmpty(paramDTO.getVinList())).map(paramDTO -> {
                        if (paramDTO.getVinList().size() > 1) {
                            log.error("当前事件关联多个车辆，不符合预期但会取第一台车辆进行后续逻辑。. paramDTO: {}", JacksonUtils.to(paramDTO),
                                    new RuntimeException());
                        }
                        RiskAutoCheckConfigDTO riskCheckingConfig = lionConfigRepository.getRiskCheckingConfig();
                        Integer maxCheckRound = riskCheckingConfig.getMaxCheckRound();
                        LongWaitAreaConfigDTO longWaitAreaConfig = lionConfigRepository.getLongWaitAreaConfig();
                        if (longWaitAreaConfig != null && riskCheckingConfig.getUseDelayRecallArea() &&
                                safetyAreaRepository.isInDelayRecallPolygon(paramDTO.getPoiName(), paramDTO.getPositionDO(), new Date())) {
                            // 如果区域在延长等待区内，设置最大轮次为MaxDelayCheckRound
                            log.info("params = {}, 命中延迟等待区", paramDTO);
                            maxCheckRound = riskCheckingConfig.getMaxDelayRound();
                        }

                        return RiskCheckingQueueItemFactory
                                .createRiskCheckingQueueItem(CreateRiskCheckingQueueDOParamDTO.builder()
                                        .caseId(paramDTO.getCaseId()).vin(paramDTO.getVinList().get(0))
                                        .type(paramDTO.getType()).eventId(paramDTO.getEventId())
                                        .maxRound(maxCheckRound)
                                        .source(paramDTO.getSource()).occurTime(paramDTO.getTimestamp())
                                        .recallTime(paramDTO.getRecallTime()).build());
                    }).collect(Collectors.toList());

            // 查询已存在的事件
            List<String> eventIds = queueItems.stream().map(RiskCheckingQueueItemDO::getEventId)
                    .collect(Collectors.toList());

            RiskCheckQueueQueryParam param = RiskCheckQueueQueryParam.builder().eventIdList(eventIds).build();
            List<RiskCheckingQueueItemDO> existingItems = riskCheckQueueRepository.queryByParam(param);
            Set<String> existingEventIds = existingItems.stream().map(RiskCheckingQueueItemDO::getEventId)
                    .collect(Collectors.toSet());

            // 过滤出不存在的事件并保存
            List<RiskCheckingQueueItemDO> newItems = queueItems.stream()
                    .filter(item -> !existingEventIds.contains(item.getEventId())).collect(Collectors.toList());

            if (!newItems.isEmpty()) {
                riskCheckQueueRepository.batchSave(newItems);
                log.info("添加 {} 项items进入预检队列", newItems.size());
            } else {
                log.info("没有item进入预检队列");
            }
        } catch (Exception e) {
            log.error("批量插入预检队列失败, paramDTOList: " + JacksonUtils.to(paramDTOList), e);
        }
    }

    @Override
    public void cancelItem(RiskCaseUpdatedParamDTO paramDTO) {
        List<RiskCheckingQueueItemDO> checkingQueueItemList = riskCheckQueueRepository
                .queryByParam(RiskCheckQueueQueryParam.builder().eventId(paramDTO.getEventId()).build());
        if (CollectionUtils.isEmpty(checkingQueueItemList)) {
            return;
        }
        List<RiskCheckingQueueItemDO> updatingQueueItemList = checkingQueueItemList.stream().map(queueItem -> {
            if (RiskQueueStatusEnum.isTerminatedStatus(queueItem.getStatus())) {
                return null;
            }
            queueItem.cancel(new Date(paramDTO.getTimestamp()), "CANCEL_FROM_SOURCE", null);
            return queueItem;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        log.info("updatingQueueItemList: {}", JacksonUtils.to(updatingQueueItemList));

        if (CollectionUtils.isNotEmpty(updatingQueueItemList)) {
            riskCheckQueueRepository.batchSave(updatingQueueItemList);
        }
    }

    @Override
    public void cancelItem(List<RiskCaseUpdatedParamDTO> paramDTOList) {
        if (CollectionUtils.isEmpty(paramDTOList)) {
            return;
        }
        Map<String, RiskCaseUpdatedParamDTO> eventId2ParamMap = paramDTOList.stream()
                .collect(Collectors.toMap(RiskCaseUpdatedParamDTO::getEventId, Function.identity(), (a, b) -> a));
        List<RiskCheckingQueueItemDO> checkingQueueItemList = riskCheckQueueRepository.queryByParam(
                RiskCheckQueueQueryParam.builder().eventIdList(Lists.newArrayList(eventId2ParamMap.keySet())).build());
        List<RiskCheckingQueueItemDO> updatingQueueItemList = checkingQueueItemList.stream().map(queueItem -> {
            if (RiskQueueStatusEnum.isTerminatedStatus(queueItem.getStatus())) {
                return null;
            }
            Date cancelTime = DatetimeUtil.toDate(eventId2ParamMap.get(queueItem.getEventId()).getTimestamp());
            queueItem.cancel(cancelTime, "CANCEL_FROM_SOURCE", null);
            return queueItem;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        log.info("updatingQueueItemList: {}", JacksonUtils.to(updatingQueueItemList));

        if (CollectionUtils.isNotEmpty(updatingQueueItemList)) {
            riskCheckQueueRepository.batchSave(updatingQueueItemList);
        }
    }

    @Override
    public Boolean checkRiskEventExists(String eventId) {
        if (StringUtils.isBlank(eventId)) {
            return false;
        }
        List<RiskCheckingQueueItemDO> checkingQueueItemDOList = riskCheckQueueRepository
                .queryByParam(RiskCheckQueueQueryParam.builder().eventId(eventId).build());
        return CollectionUtils.isNotEmpty(checkingQueueItemDOList);
    }
}
