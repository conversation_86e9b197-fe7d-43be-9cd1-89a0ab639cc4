package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.RISK_CASE_RISK_CASE_RELEASE_MESSAGE_ENTRY;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskCaseReleaseMsgProcess implements
        DomainEventProcess {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private LockUtils lockUtils;


    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(RISK_CASE_RISK_CASE_RELEASE_MESSAGE_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() == RiskCaseDO.class) {
            return handleProcessRisk((DomainEventChangeDTO<RiskCaseDO>) eventDTO);
        }
        //其他情况
        return true;
    }

    /**
     * 处理消息通知
     *
     * @param riskCaseDOList
     * @return
     */
    private boolean handleUpdateMessage(List<RiskCaseDO> riskCaseDOList) {

        // 1 获取caseId列表
        List<String> caseIdList = riskCaseDOList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        // 2 获取策略配置
        Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> releaseMrmStrategyConfigDTOMap =
                lionConfigRepository.getReleaseMrmStrategyConfigDTO();
        log.info("handleStagnationEvent releaseMrmStrategyConfigDTOMap = {}", releaseMrmStrategyConfigDTOMap);

        // 3 查询解除风险事件对应的车辆vin信息 - caseIdToVinMap
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIdList).build());
        Map<String, String> caseIdToVinMap = riskCaseVehicleRelationDOList.stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, RiskCaseVehicleRelationDO::getVin));
        for (RiskCaseDO riskCaseDO : riskCaseDOList) {
            String vin = caseIdToVinMap.get(riskCaseDO.getCaseId());
            lockUtils.batchLockNoWait(
                    //对eventId和车进行加锁
                    LockKeyPreUtil.buildEventIdAndVin(Collections.singleton(riskCaseDO.getEventId()),
                            Collections.singleton(vin)),
                    () -> {
                        RiskCaseDO thisRiskCaseDo = riskCaseRepository.getByCaseId(riskCaseDO.getCaseId());
                        if (Objects.isNull(thisRiskCaseDo)
                                //如果非呼叫中
                                || !Objects.equals(thisRiskCaseDo.getMrmCalled(),
                                RiskCaseMrmCalledStatusEnum.CALLING)
                                //如果已经完结
                                || !RiskCaseStatusEnum.isTerminal(thisRiskCaseDo.getStatus())) {
                            return;
                        }
                        log.info("RiskCaseReleaseMsgProcess cancelCallMrm caseId = {}", riskCaseDO.getCaseId());
                        riskCaseOperateService.cancelCallMrm(caseIdToVinMap.get(riskCaseDO.getCaseId()), thisRiskCaseDo,
                                releaseMrmStrategyConfigDTOMap);
                    });
        }
        return true;
    }

    /**
     * 处理风险事件
     *
     * @param eventDTO
     * @return
     */
    private boolean handleProcessRisk(DomainEventChangeDTO<RiskCaseDO> eventDTO) {
        // 从事件DTO中获取状态变更的风险案例列表，过滤出状态为终态的风险案例
        List<RiskCaseDO> riskCaseDOList = eventDTO.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                .stream().filter(riskCaseDO -> RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus())
                        && Objects.equals(riskCaseDO.getMrmCalled(), RiskCaseMrmCalledStatusEnum.CALLING))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return true;
        }
        //这里是异步处理，所以不信任变更的实体的桩体
        return handleUpdateMessage(riskCaseDOList);

    }
}
