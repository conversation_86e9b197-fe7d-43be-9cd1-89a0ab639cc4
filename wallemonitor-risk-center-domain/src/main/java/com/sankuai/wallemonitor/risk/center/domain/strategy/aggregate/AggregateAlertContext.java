package com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate;

import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 聚合告警处理上下文
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AggregateAlertContext {

    private Map<String, VehicleInfoDO> caseId2VehicleInfoMap = new HashMap<>();

    private Map<String, String> caseIdToParkingPlotId = new HashMap<>();

    public VehicleInfoDO getVehicleInfo(String caseId) {
        return caseId2VehicleInfoMap.get(caseId);
    }

    public String getParkingPlotId(String caseId) {
        return caseIdToParkingPlotId.getOrDefault(caseId, CommonConstant.UNKNOWN);
    }

    public void updateCaseIdToParkingPlotIdMap(Map<String, String> newRelationMap) {
        caseIdToParkingPlotId.putAll(newRelationMap);
    }

    public void updateCaseId2VehicleInfoMap(Map<String, VehicleInfoDO> newRelationMap) {
        caseId2VehicleInfoMap.putAll(newRelationMap);
    }
}