package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.google.common.base.Joiner;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseMessageNotifyParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseMessageNoticeProcess.MessageCreateOrUpdatedDTO;
import com.sankuai.wallemonitor.risk.center.domain.result.RiskCaseMessageNotifyResultDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseMessageService;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseNotifyDetectService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.PlaceAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.BroadCastCalcConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageUpdateEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PlaceInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.NumberCalcUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.UuidUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeTemplateConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxNoticeParamVTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 安全风险事件通知检测服务实现类
 *
 * <AUTHOR>
 * @Date 2024/6/17
 */
@Slf4j
@Service
public class RiskCaseNotifyDetectServiceImpl implements RiskCaseNotifyDetectService {


    @Resource
    private RiskCaseRepository caseRepository;

    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseMessageService riskCaseMessageService;
    @Resource
    private PlaceAdapter placeAdapter;

    @Resource
    private VehicleAdapter vehicleAdapter;


    /**
     * 渲染模板
     */
    private static final String CALC_TEMPLATE = "%d/%d";

    /**
     * 为零的不播报
     */
    private static final String ZERO_TEMPLATE = "0/0";

    /**
     * 总计
     */
    private static final String TOTAL_FIELD_NAME = "总计";


    @Override
    public void detectAndNotifyRiskCase() {
        Map<RiskCaseTypeEnum, BroadCastStrategyConfigDTO> caseStrategyConfigDTOMap = lionConfigRepository.getCaseType2BroadCastStrategyConfig();
        caseStrategyConfigDTOMap.forEach((key, value) -> {
            try {
                Date createTimeStart = DatetimeUtil.getNSecondsBeforeDateTime(new Date(),
                        value.getCreateTimeQueryStartOffsetMinutes() * CommonConstant.SECONDS_PER_MINUTE);
                Date createTimeEnd = DatetimeUtil.getNSecondsBeforeDateTime(new Date(), value.getDelaySeconds());
                RiskCaseDOQueryParamDTO paramDTO = RiskCaseDOQueryParamDTO.builder()
                        .createTimeRange(TimePeriod.builder().beginDate(createTimeStart).endDate(createTimeEnd).build())
                        .caseTypeList(Collections.singletonList(key.getCode()))
                        .statusList(Arrays.asList(RiskCaseStatusEnum.NO_DISPOSAL.getCode(),
                                RiskCaseStatusEnum.IN_DISPOSAL.getCode()))
                        .build();

                log.info("detect notify case, query param : {}", JacksonUtils.to(paramDTO));
                List<RiskCaseDO> caseDOList = caseRepository.queryByParam(paramDTO);
                log.info("caseDOList: {}", JacksonUtils.to(caseDOList));

                if (CollectionUtils.isEmpty(caseDOList)) {
                    return;
                }
                List<String> caseIdList = caseDOList.stream()
                        .filter(caseDO -> caseDO.isNeedBroadcast(value.getSourceDelaySecondsNeeded(caseDO.getSource())))
                        .map(RiskCaseDO::getCaseId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(caseIdList)) {
                    // 没有需要播报的，停止更新
                    return;
                }
                Map<String, MessageCreateOrUpdatedDTO> map = riskCaseMessageService.calcCaseNeedGreatOrUpdateMessage(
                        caseIdList);
                if (MapUtils.isEmpty(map)) {
                    // 没有需要播报的，停止更新
                    return;
                }
                Map<String, MessageCreateOrUpdatedDTO> createMap = new HashMap<>();
                map.forEach((caseId, messageCreateOrUpdatedDTO) -> {
                    if (MapUtils.isEmpty(messageCreateOrUpdatedDTO.getGroupName2UpdateEnumMap())) {
                        return;
                    }
                    Map<String, MessageUpdateEnum> onlyCreateMap = new HashMap<>();
                    messageCreateOrUpdatedDTO.getGroupName2UpdateEnumMap().forEach((groupName, messageUpdateEnum) -> {
                        if (MessageUpdateEnum.UPDATE.equals(messageUpdateEnum)) {
                            return;
                        }
                        onlyCreateMap.put(groupName, messageUpdateEnum);
                    });
                    if (MapUtils.isEmpty(onlyCreateMap)) {
                        return;
                    }
                    messageCreateOrUpdatedDTO.setGroupName2UpdateEnumMap(onlyCreateMap);
                    createMap.put(caseId, messageCreateOrUpdatedDTO);
                });
                RiskCaseMessageNotifyParamDTO createMessageParamDTO = new RiskCaseMessageNotifyParamDTO();
                createMessageParamDTO.setMessageCreateOrUpdatedDTOList(new ArrayList<>(createMap.values()));
                RiskCaseMessageNotifyResultDTO result = riskCaseMessageService.updateMessageVersionAndSendMessage(
                        createMessageParamDTO);
                log.info("send message result: {}", JacksonUtils.to(result));

                SystemCheckUtil.isEmpty(result.getFailedCaseIdList(),
                        String.format("send message failed case list: %s",
                                JacksonUtils.to(result.getFailedCaseIdList())));
                if (result.getSuccessCaseIdList().size() != caseDOList.size()) {
                    log.warn("send success case id list size not match case do list.");
                }
            } catch (Exception e) {
                log.error("send {} case message failed.", key.getDesc(), e);
            }
        });
    }

    /**
     * 统计并播报风险事件
     */
    @Override
    public void calcAndNotifyRiskCase() {
        BroadCastCalcConfigDTO broadCastCalcConfigDTO = lionConfigRepository.getBroadCastCalcConfig();
        if (broadCastCalcConfigDTO == null || !broadCastCalcConfigDTO.validate()) {
            //不需要做什么
            return;
        }
        Date nowDate = new Date();
        //查询任务执行时刻，前一个小时的数据
        Date createTimeStart = DatetimeUtil.getPreHourStart(nowDate);
        Date createTimeEnd = DatetimeUtil.getThisHourStart(nowDate);
        //需要取开始时间所在的起始时刻
        Date todayStartDate = DatetimeUtil.getZeroDate(createTimeStart);
        List<String> placeCodeList = new ArrayList<>(broadCastCalcConfigDTO.getPlaceCodeList());
        Map<String, PlaceInfoDO> placeInfoDOMap = placeAdapter.queryByPlaceCode(
                        new ArrayList<>(placeCodeList)).stream()
                .collect(Collectors.toMap(PlaceInfoDO::getPlaceCode, Function.identity(), (o1, o2) -> o1));
        //每个场地消息维度进行播报
        List<RiskCalcResultDTO> riskCalcResultDTOS = new ArrayList<>();
        String time = StringUtils.join(
                DatetimeUtil.formatDate(createTimeStart, DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN),
                CharConstant.CHAR_BL,
                DatetimeUtil.getShortTime(createTimeEnd));
        placeCodeList.forEach((placeCode) -> {
            try {
                RiskCalcResultDTO riskCalcResultDTO = handleProcessPlaceCode(createTimeStart,
                        createTimeEnd, todayStartDate,
                        placeCode,
                        placeInfoDOMap.get(placeCode));
                if (riskCalcResultDTO != null) {
                    //添加进来
                    riskCalcResultDTOS.add(riskCalcResultDTO);
                }
            } catch (Exception e) {
                log.error("场地" + placeCode + "推送异常", e);
            }
        });
        //统一处理并发送
        handleBatchNotice(time, broadCastCalcConfigDTO, riskCalcResultDTOS);


    }

    /**
     * 实现推送
     *
     * @param time
     * @param riskCalcResultDTOS
     */
    private void handleBatchNotice(String time, BroadCastCalcConfigDTO broadCastCalcConfigDTO,
            List<RiskCalcResultDTO> riskCalcResultDTOS) {

        if (CollectionUtils.isEmpty(riskCalcResultDTOS)) {
            return;
        }

        RiskCalcResultBatchDTO riskCalcResultBatchDTO = RiskCalcResultBatchDTO.builder().time(time).build();
        //按照场地进行聚合
        Map<String, RiskCalcResultDTO> riskCalcResultDTOMap = riskCalcResultDTOS.stream()
                .collect(Collectors.toMap(RiskCalcResultDTO::getPlaceName, Function.identity(), (o1, o2) -> o1));
        riskCalcResultDTOMap.forEach((placeName, riskCalcResultDTO) -> {
            //计算每个场地的当前小时
            RiskCaseRawDTO curHourData = handleCalcCaseRow(placeName, riskCalcResultDTO, false);
            //计算每个场地的当天数据
            RiskCaseRawDTO totalTodayData = handleCalcCaseRow(placeName, riskCalcResultDTO, true);
            //添加
            riskCalcResultBatchDTO.getCurHourList().add(curHourData);
            riskCalcResultBatchDTO.getDayList().add(totalTodayData);
        });
        //再计算一个总计
        riskCalcResultBatchDTO.calcTotal(true);
        riskCalcResultBatchDTO.calcTotal(false);
        //统一化播报
        dxNoticeAdapter.createOrUpdateDxMessage(DxNoticeParamVTO.builder()
                .outBizId(UuidUtil.uuid())
                .version(System.currentTimeMillis())
                .templateId(broadCastCalcConfigDTO.getTemplateId())
                .groupIdList(broadCastCalcConfigDTO.getGroupIdList())
                .params(riskCalcResultBatchDTO.toMap())
                .build());
    }


    /**
     * 处理单个场地的数据
     *
     * @param placeName
     * @param riskCalcResultDTO
     * @return
     */
    private RiskCaseRawDTO handleCalcCaseRow(String placeName, RiskCalcResultDTO riskCalcResultDTO,
            boolean totalToday) {
        RiskCaseRawDTO curHourData = RiskCaseRawDTO.builder().placeName(placeName).city(riskCalcResultDTO.getCity())
                .build();

        String stagnationCount = totalToday ? riskCalcResultDTO.getStagnationTotalCountToday()
                : riskCalcResultDTO.getStagnationCountCurHour();
        List<Integer> stagnationDurationList = totalToday ? riskCalcResultDTO.getStagnationCaseTodayDurationList()
                : riskCalcResultDTO.getStagnationCaseCurHourDurationList();

        String sideBySideCount = totalToday ? riskCalcResultDTO.getSideBySideTotalCountToday()
                : riskCalcResultDTO.getSideBySideCountCurHour();
        List<Integer> sideBySideDurationList = totalToday ? riskCalcResultDTO.getSideBySideCaseTodayDurationList()
                : riskCalcResultDTO.getSideBySideCaseCurHourDurationList();

        String congestionCount = totalToday ? riskCalcResultDTO.getCongestionTotalCountToday()
                : riskCalcResultDTO.getCongestionCountCurHour();
        List<Integer> congestionDurationList = totalToday ? riskCalcResultDTO.getCongestionCaseTodayDurationList()
                : riskCalcResultDTO.getCongestionCaseCurHourDurationList();

        Set<String> allCaseVehicle = new HashSet<>();
        if (totalToday) {
            allCaseVehicle.addAll(riskCalcResultDTO.getStagnationCaseVehicleTotalCountToday());
            allCaseVehicle.addAll(riskCalcResultDTO.getSideBySideCaseVehicleTotalCountToday());
            allCaseVehicle.addAll(riskCalcResultDTO.getCongestionCaseVehicleTotalCountToday());
        } else {
            allCaseVehicle.addAll(riskCalcResultDTO.getStagnationCaseVehicleCurHour());
            allCaseVehicle.addAll(riskCalcResultDTO.getSideBySideCaseVehicleCurHour());
            allCaseVehicle.addAll(riskCalcResultDTO.getCongestionCaseVehicleCurHour());
        }
        Integer allVehicle =
                totalToday ? riskCalcResultDTO.getVehicleTotalCountToday() : riskCalcResultDTO.getVehicleCountCurHour();
        try {
            //1、停滞
            curHourData.setStagnationCaseCount(stagnationCount);
            int stagnationCurHour90Time = NumberCalcUtils.calculateTop90(stagnationDurationList);
            curHourData.setStagnationDurationTop90(DatetimeUtil.formatSeconds(stagnationCurHour90Time));
            curHourData.setStagnationDurationTop90Int(stagnationCurHour90Time);
            curHourData.setStagnationDurationList(stagnationDurationList);
            //2、并排
            curHourData.setSideBySideCaseCount(sideBySideCount);
            int sideBySideCurHour90Time = NumberCalcUtils.calculateTop90(sideBySideDurationList);
            curHourData.setSideBySideDurationTop90(DatetimeUtil.formatSeconds(sideBySideCurHour90Time));
            curHourData.setSideBySideDurationTop90Int(sideBySideCurHour90Time);
            curHourData.setSideBySideDurationList(sideBySideDurationList);
            //2、并排
            curHourData.setCongestionCaseCount(congestionCount);
            int congestionCurHour90Time = NumberCalcUtils.calculateTop90(congestionDurationList);
            curHourData.setCongestionDurationTop90(DatetimeUtil.formatSeconds(congestionCurHour90Time));
            curHourData.setCongestionDurationTop90Int(congestionCurHour90Time);
            curHourData.setCongestionDurationList(congestionDurationList);
            //3、车辆
            curHourData.setVehicleCount(
                    String.format(CALC_TEMPLATE, allCaseVehicle.size(), allVehicle));
            return curHourData;
        } catch (Exception e) {
            log.error("计算异常", e);
            return curHourData;
        }
    }

    /**
     * 单个场地推送
     *
     * @param createTimeStart
     * @param createTimeEnd
     * @param todayStartDate
     * @param placeCode
     * @param placeInfoDO
     */
    private RiskCalcResultDTO handleProcessPlaceCode(
            Date createTimeStart,
            Date createTimeEnd, Date todayStartDate, String placeCode,
            PlaceInfoDO placeInfoDO) {

        String placeName = Optional.ofNullable(placeInfoDO).map(PlaceInfoDO::getPlaceName)
                .orElse(CharConstant.CHAR_EMPTY);
        String cityName = Optional.ofNullable(placeInfoDO).map(PlaceInfoDO::getCity).orElse(CharConstant.CHAR_EMPTY);
        RiskCalcResultDTO riskCalcResultDTO = RiskCalcResultDTO.builder().placeName(placeName).city(cityName).build();
        handleTemplateCalcInProcessCase(placeCode, createTimeStart,
                createTimeEnd, false, riskCalcResultDTO);
        //取当前天开始，到要计算的截止时间
        handleTemplateCalcInProcessCase(placeCode, todayStartDate,
                createTimeEnd, true, riskCalcResultDTO);
        if (riskCalcResultDTO.isEmpty()) {
            return null;
        }
        return riskCalcResultDTO;
        //基础信息
    }

    /**
     * 取当前小时的数据的渲染参数
     *
     * @return
     */
    private void handleTemplateCalcInProcessCase(String placeCode, Date createTimeStart,
            Date createTimeEnd, boolean isWholeDayCount, RiskCalcResultDTO riskCalcResultDTO) {
        //查询出来所有的车辆
        Set<String> placeCodeVinList = vehicleAdapter.queryReserveVehicleByTimeAndPlace(TimePeriod.builder()
                .beginDate(createTimeStart)
                .endDate(createTimeEnd)
                .build(), placeCode);
        if (isWholeDayCount) {
            riskCalcResultDTO.setVehicleTotalCountToday(placeCodeVinList.size());
        } else {
            riskCalcResultDTO.setVehicleCountCurHour(placeCodeVinList.size());
        }
        //根据时间维度查询数据
        RiskCaseDOQueryParamDTO paramDTO = RiskCaseDOQueryParamDTO.builder()
                .placeCode(placeCode)
                .createTimeRange(TimePeriod.builder().beginDate(createTimeStart).endDate(createTimeEnd).build())
                .build();
        //TODO: 这里目前是全部查询出来 ，但是需要考虑以后保障系统的停滞不当上了，这里同一个车会查询两份停滞不前，在统计播报里面需要过滤
        //查询出来全部的riskCase
        List<RiskCaseDO> riskCaseDOList = caseRepository.queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            //场地如果数据均为空，无需播报
            return;
        }
        List<String> caseIdList = new ArrayList<>();
        Map<RiskCaseTypeEnum, List<RiskCaseDO>> caseTypeEnumListMap = new HashMap<>();
        riskCaseDOList.forEach((caseDO) -> {
            caseTypeEnumListMap.computeIfAbsent(caseDO.getType(), k -> new ArrayList<>()).add(caseDO);
            caseIdList.add(caseDO.getCaseId());
        });
        //查询caseId和车辆的关系
        Map<String, List<RiskCaseVehicleRelationDO>> caseId2VehicleRelationMap = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIdList).build()
        ).stream().collect(Collectors.groupingBy(RiskCaseVehicleRelationDO::getCaseId));
        Map<RiskCaseTypeEnum, List<RiskCaseDO>> caseType2ListMap = riskCaseDOList.stream()
                .collect(Collectors.groupingBy(RiskCaseDO::getType));
        //按照类型查看
        caseType2ListMap.forEach((caseTypeEnum, thisTypeRiskCaseDOList) -> {
            //按照类型进行统计，全部/处置完成，这里的处置包含
            //case数量
            int caseNum = thisTypeRiskCaseDOList.size();
            //case关联的事故车辆
            Set<String> typeOfVehicleSet = thisTypeRiskCaseDOList.stream()
                    .map(riskCaseDO -> caseId2VehicleRelationMap.getOrDefault(riskCaseDO.getCaseId(),
                            new ArrayList<>())).flatMap(
                            Collection::stream).map(RiskCaseVehicleRelationDO::getVin).collect(
                            Collectors.toSet());
            //看下处置时间
            List<Integer> durationList = new ArrayList<>();
            //处理总长(秒)
            Integer typeOfTotalSecond = thisTypeRiskCaseDOList.stream()
                    .mapToInt(RiskCaseDO::getRiskDurationTime)
                    //把时间进行保存
                    .peek(durationList::add)
                    .sum();
            //处置完成数量
            int disposedNum = (int) thisTypeRiskCaseDOList.stream()
                    .filter(riskCaseDO -> RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus()))
                    .map(RiskCaseDO::getCaseId).distinct().count();
            //合并看下
            String finalNum = String.format(CALC_TEMPLATE, disposedNum, caseNum);
            //根据类型查询
            switch (caseTypeEnum) {
                case VEHICLE_SIDE_BY_SIDE: {
                    //并排
                    if (isWholeDayCount) {
                        riskCalcResultDTO.setSideBySideTotalCountToday(finalNum);
                        //车辆列表 + 时间
                        riskCalcResultDTO.setSideBySideCaseVehicleTotalCountToday(typeOfVehicleSet);
                        riskCalcResultDTO.setSideBySideCountTodayDuration(typeOfTotalSecond);
                        riskCalcResultDTO.setSideBySideCaseTodayDurationList(durationList);

                    } else {
                        riskCalcResultDTO.setSideBySideCountCurHour(finalNum);
                        //车辆列表 + 时间
                        riskCalcResultDTO.setSideBySideCaseVehicleCurHour(typeOfVehicleSet);
                        riskCalcResultDTO.setSideBySideCurHourDuration(typeOfTotalSecond);
                        riskCalcResultDTO.setSideBySideCaseCurHourDurationList(durationList);
                    }
                    break;
                }
                case VEHICLE_STAND_STILL: {
                    //车辆停滞不当
                    if (isWholeDayCount) {
                        riskCalcResultDTO.setStagnationTotalCountToday(finalNum);
                        //车辆列表 + 时间 + 时间列表
                        riskCalcResultDTO.setStagnationCaseVehicleTotalCountToday(typeOfVehicleSet);
                        riskCalcResultDTO.setStagnationCountTodayDuration(typeOfTotalSecond);
                        riskCalcResultDTO.setStagnationCaseTodayDurationList(durationList);
                    } else {
                        riskCalcResultDTO.setStagnationCountCurHour(finalNum);
                        //车辆列表 + 时间 + 时间列表
                        riskCalcResultDTO.setStagnationCaseVehicleCurHour(typeOfVehicleSet);
                        riskCalcResultDTO.setStagnationCurHourDuration(typeOfTotalSecond);
                        riskCalcResultDTO.setStagnationCaseCurHourDurationList(durationList);
                    }
                    break;
                }
                case VEHICLE_CONGESTION: {
                    //车辆扎堆
                    if (isWholeDayCount) {
                        riskCalcResultDTO.setCongestionTotalCountToday(finalNum);
                        //车辆列表 + 时间 + 时间列表
                        riskCalcResultDTO.setCongestionCaseVehicleTotalCountToday(typeOfVehicleSet);
                        riskCalcResultDTO.setCongestionCountTodayDuration(typeOfTotalSecond);
                        riskCalcResultDTO.setCongestionCaseTodayDurationList(durationList);
                    } else {
                        riskCalcResultDTO.setCongestionCountCurHour(finalNum);
                        //车辆列表 + 时间 + 时间列表
                        riskCalcResultDTO.setCongestionCaseVehicleCurHour(typeOfVehicleSet);
                        riskCalcResultDTO.setCongestionCurHourDuration(typeOfTotalSecond);
                        riskCalcResultDTO.setCongestionCaseCurHourDurationList(durationList);
                    }
                    break;
                }
            }
        });
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class RiskCalcResultDTO {

        private String placeName;

        private String city;
        //并排
        @Builder.Default
        private String sideBySideCountCurHour = "0/0";
        @Builder.Default
        private String sideBySideTotalCountToday = "0/0";
        //并排涉及的车辆
        @Builder.Default
        private Set<String> sideBySideCaseVehicleTotalCountToday = new HashSet<>();
        //并排涉及的车辆（小时）
        @Builder.Default
        private Set<String> sideBySideCaseVehicleCurHour = new HashSet<>();
        //并排处理时间的列表
        @Builder.Default
        private List<Integer> sideBySideCaseTodayDurationList = new ArrayList<>();
        //并排处理时间的列表
        @Builder.Default
        private List<Integer> sideBySideCaseCurHourDurationList = new ArrayList<>();
        //并排总计处理时间，秒
        @Builder.Default
        private Integer sideBySideCountTodayDuration = 0;
        //并排总计处理时间，秒
        @Builder.Default
        private Integer sideBySideCurHourDuration = 0;

        //停滞不当
        //处置数
        @Builder.Default
        private String stagnationCountCurHour = "0/0";
        @Builder.Default
        private String stagnationTotalCountToday = "0/0";
        //停滞涉及的车辆
        @Builder.Default
        private Set<String> stagnationCaseVehicleTotalCountToday = new HashSet<>();
        //停滞涉及的车辆 （当前小时）
        @Builder.Default
        private Set<String> stagnationCaseVehicleCurHour = new HashSet<>();
        //停滞处理时间的列表 (小时)
        @Builder.Default
        private List<Integer> stagnationCaseTodayDurationList = new ArrayList<>();
        //停滞处理时间的列表 （天）
        @Builder.Default
        private List<Integer> stagnationCaseCurHourDurationList = new ArrayList<>();
        //停滞总计处理时间，秒
        @Builder.Default
        private Integer stagnationCountTodayDuration = 0;
        //停滞总计处理时间，秒
        @Builder.Default
        private Integer stagnationCurHourDuration = 0;


        //扎堆不前
        //处置数
        @Builder.Default
        private String congestionCountCurHour = "0/0";
        @Builder.Default
        private String congestionTotalCountToday = "0/0";
        //扎堆涉及的车辆
        @Builder.Default
        private Set<String> congestionCaseVehicleTotalCountToday = new HashSet<>();
        //扎堆涉及的车辆 （当前小时）
        @Builder.Default
        private Set<String> congestionCaseVehicleCurHour = new HashSet<>();
        //扎堆处理时间的列表 (小时)
        @Builder.Default
        private List<Integer> congestionCaseTodayDurationList = new ArrayList<>();
        //扎堆处理时间的列表 （天）
        @Builder.Default
        private List<Integer> congestionCaseCurHourDurationList = new ArrayList<>();
        //扎堆总计处理时间，秒
        @Builder.Default
        private Integer congestionCountTodayDuration = 0;
        //扎堆总计处理时间，秒
        @Builder.Default
        private Integer congestionCurHourDuration = 0;


        //车辆数
        //天
        @Builder.Default
        private Integer vehicleTotalCountToday = 0;
        //当前小时
        @Builder.Default
        private Integer vehicleCountCurHour = 0;

        /**
         * 判断结果是否无需播报
         *
         * @return
         */
        public boolean isEmpty() {
            return Objects.equals(sideBySideCountCurHour, ZERO_TEMPLATE) && Objects.equals(sideBySideTotalCountToday,
                    ZERO_TEMPLATE) && Objects.equals(stagnationCountCurHour, ZERO_TEMPLATE) && Objects.equals(
                    stagnationTotalCountToday, ZERO_TEMPLATE) && Objects.equals(
                    congestionTotalCountToday, ZERO_TEMPLATE) && Objects.equals(
                    congestionCountCurHour, ZERO_TEMPLATE);

        }
    }


    /**
     * 播报结果合并
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class RiskCalcResultBatchDTO {

        //时间段
        private String time;

        //播报的行 - 当天表格的行
        @Builder.Default
        private List<RiskCaseRawDTO> dayList = new ArrayList<>();

        @Builder.Default
        //播报的行- 当前小时表格的行
        private List<RiskCaseRawDTO> curHourList = new ArrayList<>();

        /**
         * 全场地再算一次
         */
        public void calcTotal(boolean today) {
            RiskCaseRawDTO allRiskCaseRawDTO = RiskCaseRawDTO.builder().placeName(TOTAL_FIELD_NAME).build();
            int allDisposalSideBySide = 0;
            int disposedSideBySide = 0;
            int allDisposalStagnation = 0;
            int disposedStagnation = 0;
            int allDisposalCongestion = 0;
            int disposedCongestion = 0;
            int caseVehicle = 0;
            int allVehicle = 0;
            List<Integer> sideBySideDurationList = new ArrayList<>();
            List<Integer> stagnationDurationList = new ArrayList<>();
            List<Integer> congestionDurationList = new ArrayList<>();
            List<RiskCaseRawDTO> riskCaseRawDTOS = today ? dayList : curHourList;
            for (RiskCaseRawDTO riskCaseRawDTO : riskCaseRawDTOS) {
                if (!riskCaseRawDTO.isEmpty()) {
                    String[] sideBySideCaseCountSplit = StringUtils.split(riskCaseRawDTO.getSideBySideCaseCount(),
                            CharConstant.CHAR_XX);
                    String[] stagnationCaseCountSplit = StringUtils.split(riskCaseRawDTO.getStagnationCaseCount(),
                            CharConstant.CHAR_XX);
                    String[] congestionCaseCountSplit = StringUtils.split(riskCaseRawDTO.getCongestionCaseCount(),
                            CharConstant.CHAR_XX);
                    //并排：处置/未处置
                    disposedSideBySide += Integer.parseInt(sideBySideCaseCountSplit[0]);
                    allDisposalSideBySide += Integer.parseInt(sideBySideCaseCountSplit[1]);
                    //停滞：处置/未处置
                    disposedStagnation += Integer.parseInt(stagnationCaseCountSplit[0]);
                    allDisposalStagnation += Integer.parseInt(stagnationCaseCountSplit[1]);
                    //扎堆：处置/未处置
                    disposedCongestion += Integer.parseInt(congestionCaseCountSplit[0]);
                    allDisposalCongestion += Integer.parseInt(congestionCaseCountSplit[1]);
                    //耗时
                    sideBySideDurationList.addAll(riskCaseRawDTO.getSideBySideDurationList());
                    stagnationDurationList.addAll(riskCaseRawDTO.getStagnationDurationList());
                    congestionDurationList.addAll(riskCaseRawDTO.getCongestionDurationList());
                }
                //车辆: 有异常/全部
                String[] vehicleSplit = StringUtils.split(riskCaseRawDTO.getVehicleCount(), CharConstant.CHAR_XX);
                caseVehicle += Integer.parseInt(vehicleSplit[0]);
                allVehicle += Integer.parseInt(vehicleSplit[1]);
            }
            //4个值：车、停滞、处置时间、并排、处置时间
            allRiskCaseRawDTO.setVehicleCount(String.format(CALC_TEMPLATE, caseVehicle, allVehicle));
            //停滞的
            allRiskCaseRawDTO.setStagnationCaseCount(
                    String.format(CALC_TEMPLATE, disposedStagnation, allDisposalStagnation));
            allRiskCaseRawDTO.setStagnationDurationTop90(
                    DatetimeUtil.formatSeconds(NumberCalcUtils.calculateTop90(stagnationDurationList)));
            //并排的
            allRiskCaseRawDTO.setSideBySideCaseCount(
                    String.format(CALC_TEMPLATE, disposedSideBySide, allDisposalSideBySide));
            allRiskCaseRawDTO.setSideBySideDurationTop90(
                    DatetimeUtil.formatSeconds(NumberCalcUtils.calculateTop90(sideBySideDurationList)));
            //扎堆的
            allRiskCaseRawDTO.setCongestionCaseCount(
                    String.format(CALC_TEMPLATE, disposedCongestion, allDisposalCongestion));
            allRiskCaseRawDTO.setCongestionDurationTop90(
                    DatetimeUtil.formatSeconds(NumberCalcUtils.calculateTop90(congestionDurationList)));
            riskCaseRawDTOS.add(allRiskCaseRawDTO);
        }

        /**
         * 转换成参数map time,
         *
         * @return
         */
        public Map<String, String> toMap() {
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("time", time);
            paramMap.put("dayList", toMarkDownTableRaw(dayList));
            paramMap.put("curHourList", toMarkDownTableRaw(curHourList));
            return paramMap;
        }

        private String toMarkDownTableRaw(List<RiskCaseRawDTO> riskCaseRawDTOS) {
            List<String> rawList = new ArrayList<>();
            rawList.add(
                    "| 场地 | 事件车辆数/运行车辆数 | 并排：事件处置数/事件数/持续时间TP90 | 停滞：事件处置数/事件数/持续时间TP90 | 扎堆：事件处置数/事件数/持续时间TP90 |");
            //居中对齐
            rawList.add("|:----:|:----:|:-----:|:-----:|:-----:|");
            for (RiskCaseRawDTO riskCaseRawDTO : riskCaseRawDTOS) {
                if (riskCaseRawDTO.isEmpty() && !riskCaseRawDTO.isTotal()) {
                    //为空且不是总计的时候，不添加
                    continue;
                }
                StringBuilder rawStringBuilder = new StringBuilder();
                //城市-场地
                rawStringBuilder.append("| ").append(riskCaseRawDTO.getCityAndPlaceName());
                rawStringBuilder.append("| ").append(riskCaseRawDTO.getVehicleCount());
                //并排
                rawStringBuilder.append("| ")
                        .append(riskCaseRawDTO.getSideBySideCaseCount() + CharConstant.CHAR_XX
                                + riskCaseRawDTO.getSideBySideDurationTop90());
                //停滞
                rawStringBuilder.append("| ")
                        .append(riskCaseRawDTO.getStagnationCaseCount() + CharConstant.CHAR_XX
                                + riskCaseRawDTO.getStagnationDurationTop90());
                //扎堆
                rawStringBuilder.append("| ")
                        .append(riskCaseRawDTO.getCongestionCaseCount() + CharConstant.CHAR_XX
                                + riskCaseRawDTO.getCongestionDurationTop90())
                        .append(" |");
                rawList.add(rawStringBuilder.toString());
            }
            return String.join("\n", rawList);
        }
    }

    /**
     * 播报的每行数据
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    private static class RiskCaseRawDTO {

        private String placeName;

        private String city;

        //并排
        @Builder.Default
        private String sideBySideCaseCount = "0/0";

        @Builder.Default
        private String sideBySideDurationTop90 = "0秒";

        @Builder.Default
        private Integer sideBySideDurationTop90Int = 0;

        @Builder.Default
        private List<Integer> sideBySideDurationList = new ArrayList<>();

        //停滞
        @Builder.Default
        private String stagnationCaseCount = "0/0";

        @Builder.Default
        private String stagnationDurationTop90 = "0秒";

        @Builder.Default
        private Integer stagnationDurationTop90Int = 0;

        @Builder.Default
        private List<Integer> stagnationDurationList = new ArrayList<>();

        //扎堆
        @Builder.Default
        private String congestionCaseCount = "0/0";

        @Builder.Default
        private String congestionDurationTop90 = "0秒";

        @Builder.Default
        private Integer congestionDurationTop90Int = 0;

        @Builder.Default
        private List<Integer> congestionDurationList = new ArrayList<>();

        //车辆
        @Builder.Default
        private String vehicleCount = "0/0";

        public String getCityAndPlaceName() {
            return Objects.equals(placeName, TOTAL_FIELD_NAME) ? placeName
                    : Joiner.on(CharConstant.CHAR_HX).join(city, placeName);
        }

        /**
         * 判断是否需要播报
         *
         * @return
         */
        public boolean isEmpty() {
            return Objects.equals(stagnationCaseCount, ZERO_TEMPLATE) && Objects.equals(sideBySideCaseCount,
                    ZERO_TEMPLATE) && Objects.equals(congestionCaseCount,
                    ZERO_TEMPLATE);
        }

        /**
         * 总计不过滤
         *
         * @return
         */
        public boolean isTotal() {
            return Objects.equals(placeName, TOTAL_FIELD_NAME);

        }
    }
}
