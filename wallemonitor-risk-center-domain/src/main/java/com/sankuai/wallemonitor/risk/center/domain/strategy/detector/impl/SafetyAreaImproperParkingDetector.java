package com.sankuai.wallemonitor.risk.center.domain.strategy.detector.impl;

import com.sankuai.wallemonitor.risk.center.domain.strategy.detector.RiskDetector;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskSafetyAreaImproperParkingRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 非配送业务状态下停车区停滞风险检测
 */
@Slf4j
@Service
public class SafetyAreaImproperParkingDetector extends RiskDetector<RiskSafetyAreaImproperParkingRecordDO> {

    @Override
    public RiskCaseTypeEnum getDetectRiskType() {
        return RiskCaseTypeEnum.SAFETY_AREA_IMPROPER_PARKING;
    }
}
