package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.RISK_CASE_CREATE_OR_UPDATE_MESSAGE_ENTRY;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseMessageNotifyParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.result.RiskCaseMessageNotifyResultDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseMessageService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.HandleStrategy;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageUpdateEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskCaseMessageNoticeProcess implements
        DomainEventProcess {

    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    @Resource
    private RiskCaseMessageService riskCaseMessageService;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private Map<String, HandleStrategy> riskHandleStrategyMap;


    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(RISK_CASE_CREATE_OR_UPDATE_MESSAGE_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() == RiskCaseDO.class) {
            return handleProcessRisk((DomainEventChangeDTO<RiskCaseDO>) eventDTO);
        } else if (eventDTO.getDomainClass() == RiskCaseVehicleRelationDO.class) {
            return handleProcessRiskVehicle((DomainEventChangeDTO<RiskCaseVehicleRelationDO>) eventDTO);
        }
        //其他情况
        return true;
    }

    /**
     * 处理风险车辆事件
     *
     * @param eventDTO
     * @return
     */
    private boolean handleProcessRiskVehicle(DomainEventChangeDTO<RiskCaseVehicleRelationDO> eventDTO) {

        // 筛选出状态发生变更的风险车辆关联记录
        List<RiskCaseVehicleRelationDO> statusChangedRiskCaseVehicleRelationDO = eventDTO.getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"));
        // 如果没有状态变更的记录，则直接返回true
        if (CollectionUtils.isEmpty(statusChangedRiskCaseVehicleRelationDO)) {
            return true;
        }
        // 提取出所有状态变更记录的风险案例ID
        List<String> caseIdList = statusChangedRiskCaseVehicleRelationDO.stream()
                .map(RiskCaseVehicleRelationDO::getCaseId)
                .collect(Collectors.toList());
        return handleUpdateMessage(caseIdList);
    }

    /**
     * 处理消息通知
     *
     * @param caseIdList
     * @return
     */
    private boolean handleUpdateMessage(List<String> caseIdList) {
        //找到对应的风险事件
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(RiskCaseDOQueryParamDTO.builder()
                .caseIdList(caseIdList)
                .build());
        if (CollectionUtils.isEmpty(riskCaseDOList)) {
            return true;
        }
        //收到变更的消息了,判断是否要走创建还是更新,更新
        Map<String, MessageCreateOrUpdatedDTO> caseId2MessageCreateOrUpdatedDTOMap = riskCaseMessageService.calcCaseNeedGreatOrUpdateMessage(
                caseIdList);
        if (MapUtils.isEmpty(caseId2MessageCreateOrUpdatedDTOMap)) {
            //如果没有任何一个风险需要更新群组的消息，则不处理
            return true;
        }
        Set<String> eventIdSet = riskCaseDOList.stream().map(RiskCaseDO::getEventId).collect(Collectors.toSet());
        return lockUtils.batchLockCanWait(
                LockKeyPreUtil.buildEventIdAndVin(eventIdSet, new HashSet<>()),
                1,
                TimeUnit.SECONDS, () -> {
                    RiskCaseMessageNotifyResultDTO riskCaseMessageNotifyResultDTO = riskCaseMessageService.updateMessageVersionAndSendMessage(
                            RiskCaseMessageNotifyParamDTO.builder()
                                    //消息创建或者更新的集合
                                    .messageCreateOrUpdatedDTOList(
                                            new ArrayList<>(caseId2MessageCreateOrUpdatedDTOMap.values()))
                                    .build());
                    //如果存在失败的,尝试重试
                    return CollectionUtils.isEmpty(riskCaseMessageNotifyResultDTO.getFailedCaseIdList());
                });
    }

    /**
     * 处理风险事件
     *
     * @param eventDTO
     * @return
     */
    private boolean handleProcessRisk(DomainEventChangeDTO<RiskCaseDO> eventDTO) {

        //状态发生变化的
        List<RiskCaseDO> statusChangedRiskCaseDO = eventDTO.getBySingleField(
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"));
        //呼叫发生变化的
        List<RiskCaseDO> mrmCalledChangedRiskCaseDO = eventDTO.getBySingleField(
                //取变化的case
                entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "mrmCalled")
                        //发生变化且更新为拨通
                        && Objects.equals(entityFieldChangeRecordDTO.getAfter(), "CALLING"));

        if (CollectionUtils.isEmpty(statusChangedRiskCaseDO) && CollectionUtils.isEmpty(mrmCalledChangedRiskCaseDO)) {
            return true;
        }
        //这里是异步处理，所以不信任变更的实体的桩体
        List<String> caseIdList = new ArrayList<>();
        caseIdList.addAll(
                statusChangedRiskCaseDO.stream().map(RiskCaseDO::getCaseId).distinct().collect(Collectors.toList()));
        caseIdList.addAll(
                mrmCalledChangedRiskCaseDO.stream().map(RiskCaseDO::getCaseId).distinct().collect(Collectors.toList()));
        return handleUpdateMessage(caseIdList);

    }

    /**
     * 消息创建或者更新
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class MessageCreateOrUpdatedDTO {

        /**
         * 风险id
         */
        private String riskCaseId;

        /**
         * 取对应的群
         */
        @Builder.Default
        private Map<String, MessageUpdateEnum> groupName2UpdateEnumMap = new HashMap<>();


        /**
         * 添加是否要更新
         */
        public void addGroupUpdateOrCreated(String groupName, MessageUpdateEnum messageUpdateEnum) {
            if (MapUtils.isEmpty(groupName2UpdateEnumMap)) {
                groupName2UpdateEnumMap = new HashMap<>();
            }
            groupName2UpdateEnumMap.put(groupName, messageUpdateEnum);
        }


    }
}
