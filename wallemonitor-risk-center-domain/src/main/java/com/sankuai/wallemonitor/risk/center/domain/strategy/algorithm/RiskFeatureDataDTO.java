package com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 风险数据特征集合类
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RiskFeatureDataDTO {

    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 维度
     */
    private Double latitude;

    /**
     * 距离下个路口的距离
     */
    private Double distanceToNextJunction;

    /**
     * 红灯类型
     */
    private String trafficLightType;

    /**
     * 障碍物距离
     */
    private Double obstacleDistance;

    /**
     * 障碍物夹角
     */
    private Double obstacleAngle;

    /**
     * 车道类型
     */
    private Integer laneType;

    /**
     * 所在车道的相对位置
     */
    private Integer lanePosition;

    /**
     * 风险
     */
    private Integer risk;

    /**
     * 相邻车道是否可用
     */
    private Boolean isNeighborLaneAvailable;
}
