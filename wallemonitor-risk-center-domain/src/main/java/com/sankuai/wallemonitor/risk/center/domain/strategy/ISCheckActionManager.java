package com.sankuai.wallemonitor.risk.center.domain.strategy;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CatMonitorConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCheckCategoryConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCheckFilterConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CheckSceneEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckingExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.ActionChainResultLogDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.ActionChainResultLogDO.ActionChainResultLogDOBuilder;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.ActionChainResultLogRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

/**
 * 停滞不当风险预检Action管理器
 * IS - Improper Stranding
 *
 * <AUTHOR>
 * @Date 2022/6/12
 */
@Slf4j
@Service
public class ISCheckActionManager implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @ConfigValue(key = "risk.improperStrandingChecking.actionList", value = "", defaultValue = "", allowBlankValue = true)
    private HashSet<String> checkingActionList;


    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;

    protected HashMap<String, ISCheckAction> ACTION_MAP = new HashMap<>();

    @Resource
    private ActionChainResultLogRepository actionChainResultLogRepository;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    protected void init() {
        Map<String, ISCheckAction> beansMap = this.applicationContext.getBeansOfType(ISCheckAction.class);
        beansMap.values().forEach(item -> {
            ACTION_MAP.put(item.getClass().getSimpleName(), item);
        });
    }

    /**
     * 处理预检项
     *
     * @param itemDOList
     */
    public List<ISCheckActionResult> doCheck(List<RiskCheckingQueueItemDO> itemDOList) {
        if (CollectionUtils.isEmpty(itemDOList)) {
            return Collections.emptyList();
        }
        RiskAutoCheckConfigDTO checkQueueConfigDTO = lionConfigRepository.getRiskCheckingConfig();
        //item做并行检测
        return ParallelExecutor.executeParallelTasksAndGetResult(
                "is_check_action", itemDOList,
                item -> this.handleActionChain(item, CheckSceneEnum.PRE_CHECK, checkQueueConfigDTO,
                        CharConstant.CHAR_EMPTY));
    }

    /**
     * 进行检查
     *
     * @param itemDO
     * @param version
     */
    public ISCheckActionResult doMark(RiskCheckingQueueItemDO itemDO, String version) {
        if (Objects.isNull(itemDO)) {
            return ISCheckActionResult.empty();
        }
        // 根据版本取
        RiskAutoCheckConfigDTO autoMarkConfigDTO = lionConfigRepository.getRiskAutoMarkConfigByVersion(version);
        //item
        return this.handleActionChain(itemDO, CheckSceneEnum.MARK, autoMarkConfigDTO, version);
    }

    /**
     * 处理预检项
     */
    public ISCheckActionResult handleActionChain(RiskCheckingQueueItemDO itemDO, CheckSceneEnum checkScene,
            RiskAutoCheckConfigDTO config, String version) {
        Map<String, String> actionChain = config.getActionChain();
        Map<String, RiskCheckCategoryConfigDTO> checkCategoryConfig = config.getCheckCategoryConfig();
        Map<String, RiskCheckFilterConfigDTO> checkFilterConfig = config.getCheckFilterConfig();
        Double skipSpeedThreshold = config.getSkipSpeedThreshold();
        Map<String, Map<String, Object>> actionCommonConfigMap = config.getActionCommonConfig();
        Map<String, String> riskReDistribution = config.getRiskReDistribution();
        if (Objects.isNull(itemDO) || MapUtils.isEmpty(actionChain)) {
            return ISCheckActionResult.empty();
        }
        // 最大的检测时间
        Date latestCheckTime = DatetimeUtil.getNSecondsAfterDateTime(itemDO.getOccurTime(),
                config.getRoundIntervalSeconds() * itemDO.getMaxRound());
        Map<String, String> actionChainConfig = new HashMap<>(actionChain);
        //item做并行检测
        //构建上下文
        VehicleRuntimeInfoContextDO runtimeInfoContextDO = vehicleRepository.getFullByVin(itemDO.getVin());
        if (runtimeInfoContextDO == null) {
            log.warn("runtimeInfoContextDO is null");
        }
        ISCheckActionContext context = ISCheckActionContext.builder()
                .id(itemDO.getTmpCaseId())
                .vehicleRunTimeContext(runtimeInfoContextDO)
                .item(itemDO)
                .actionConfigMap(actionCommonConfigMap)
                .checkScene(checkScene.name()).markVersion(version)
                .build();
        ISCheckActionResult checkActionResult;
        boolean isCheckTimeOut = latestCheckTime.compareTo(new Date()) <= 0;
        if (isCheckTimeOut) {
            // 是否超时检出
            checkActionResult = buildTimeOutResult(itemDO);
        }
        // 如果实时的速度超过阈值则表示车辆在起步中，本轮可以跳过
        else if (Objects.nonNull(runtimeInfoContextDO) && Objects.nonNull(runtimeInfoContextDO.getSpeed())
                && runtimeInfoContextDO.getSpeed() >= skipSpeedThreshold) {
            checkActionResult = buildSkipResult(itemDO);
        } else {
            //只要不为空
            while (MapUtils.isNotEmpty(actionChainConfig)) {
                //取下一次处理器
                String currentAction = getNextActionName(context, actionChainConfig);
                // 当前执行的action
                context.setCurrentActionName(currentAction);
                if (StringUtils.isBlank(currentAction)) {
                    //没有下一个
                    break;
                }
                //获取action的配置
                //允许的category配置
                RiskCheckCategoryConfigDTO currentActionCategoryConfig = Optional.ofNullable(checkCategoryConfig)
                        .orElse(new HashMap<>()).get(currentAction);
                //需要过滤的配置
                RiskCheckFilterConfigDTO currentActionFilterConfig = Optional.ofNullable(checkFilterConfig)
                        .orElse(new HashMap<>()).get(currentAction);

                // 执行结果
                ISCheckActionResult result = executeAction(checkScene, currentAction, context,
                        currentActionCategoryConfig,
                        currentActionFilterConfig, version);

                result.setActionName(currentAction);
                //更新最新的执行结果
                context.updateAction(result, currentAction);
                //是否退出本轮预检
                if (!context.getContinueCheck()) {
                    break;
                }
            }
            checkActionResult = context.getLastCheckResult();
        }
        if (config.isDynamicReCheckCategory(context.getCurrentActionName(), checkActionResult.getCategoryEnum())) {
            // 是否需要动态延时
            checkActionResult.setDynamicReCheck(true);
        }
        // 重新处理类型
        reDistributeRiskCategory(context.getVehicleRunTimeContext(), checkActionResult, riskReDistribution);
        // 记录执行日志
        logActionChainResult(context, checkActionResult, checkScene, version);

        return checkActionResult;
    }

    /**
     * 对风险进行重定位
     * 
     * @param checkActionResult
     * @param riskReDistribution
     */
    private void reDistributeRiskCategory(VehicleRuntimeInfoContextDO contextDO, ISCheckActionResult checkActionResult,
            Map<String, String> riskReDistribution) {
        if (MapUtils.isEmpty(riskReDistribution) || Objects.isNull(checkActionResult)
                || checkActionResult.withoutRisk()) {
            // 风险再分配为空，或者无风险
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("runtimeInfo", contextDO);
        params.put("actionResult", checkActionResult);
        riskReDistribution.forEach((newSubCategory, value) -> {
            ISCheckCategoryEnum isCheckCategoryEnum = ISCheckCategoryEnum.getBySubcategory(newSubCategory);
            if (Objects.isNull(isCheckCategoryEnum)) {
                return;
            }
            boolean success = SpELUtil.evaluateBoolean(value, params);
            if (success) {
                checkActionResult.setCategoryEnum(isCheckCategoryEnum);
            }
        });

    }

    /**
     * 记录action链执行结果
     *
     * @param context
     */
    private void logActionChainResult(ISCheckActionContext context, ISCheckActionResult checkResult,
            CheckSceneEnum checkScene, String version) {
        try {
            RiskCheckingQueueItemDO itemDO = context.getItem();
            ActionChainResultLogDOBuilder logDOBuilder = ActionChainResultLogDO.builder()
                    // 标注场景使用eventId从停滞视角看，预检模式使用tempCaseId从停滞不当视角看
                    .caseId(checkScene == CheckSceneEnum.MARK ? itemDO.getEventId() : itemDO.getTmpCaseId())
                    .scene(checkScene.name())
                    .markVersion(version)
                    .round(itemDO.getRound())
                    .vin(itemDO.getVin())
                    .otherActionCheckDetails(JacksonUtils.to(context.getCheckResultHistory()))
                    .itemDataSnapshot(JacksonUtils.to(itemDO))
                    .vehicleRuntimeInfoSnapshot(JacksonUtils.to(context.getVehicleRunTimeContext()));

            if (Objects.nonNull(checkResult)) {
                logDOBuilder.resultDetail(JacksonUtils.to(checkResult))
                        .checkTime(checkResult.getStartCheckTime())
                        .category(checkResult.getCategoryEnum().name())
                        .duration(checkResult.getDuration())
                        .actionName(checkResult.getActionName());
            }
            actionChainResultLogRepository.save(logDOBuilder.build());
        } catch (Exception e) {
            log.error(String.format("logActionChainResult failed. caseId: %s", context.getItem().getTmpCaseId()), e);
        }
    }

    /**
     * 构建跳过结果
     * @param itemDO
     * @return
     */
    private ISCheckActionResult buildSkipResult(RiskCheckingQueueItemDO itemDO) {
        ISCheckActionResult actionResult = ISCheckActionResult.empty();
        actionResult.setStartCheckTime(new Date());
        actionResult.setCaseId(itemDO.getTmpCaseId());

        // 继承上次检测结果
        RiskCheckResultDO checkResultDO = Optional.ofNullable(itemDO.getExtInfo())
                .map(RiskCheckingExtInfoDO::getLastCheckResult).filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(list.size() - 1)).orElse(null);
        if (Objects.nonNull(checkResultDO)) {
            actionResult.setActionName(checkResultDO.getCheckSource());
            actionResult.setCategoryEnum(checkResultDO.getCategory());
        } else {
            actionResult.setActionName(CommonConstant.NONE_ACTION);
            actionResult.setCategoryEnum(ISCheckCategoryEnum.BAD_OTHER);
        }
        actionResult.setNeedReCheck(true);
        actionResult.setWithSpeed(true);
        return actionResult;
    }

    /**
     * 超时检出时刻，记录
     * 
     * @param itemDO
     * @return
     */
    private ISCheckActionResult buildTimeOutResult(RiskCheckingQueueItemDO itemDO) {
        ISCheckActionResult actionResult = ISCheckActionResult.empty();
        actionResult.setStartCheckTime(new Date());
        actionResult.setCaseId(itemDO.getTmpCaseId());
        // 设置超时
        actionResult.setActionName(CommonConstant.DEFAULT_ACTION);
        actionResult.setCategoryEnum(ISCheckCategoryEnum.CANT_FOUND_ANY);
        actionResult.setNeedReCheck(false);
        actionResult.setWithSpeed(false);
        return actionResult;
    }
    /**
     * 处理action
     *
     * @param nextActionName
     * @param context
     * @param currentActionConfig
     * @param currentActionFilterConfig
     * @return
     */
    private ISCheckActionResult executeAction(CheckSceneEnum checkScene, String nextActionName,
            ISCheckActionContext context,
            RiskCheckCategoryConfigDTO currentActionConfig, RiskCheckFilterConfigDTO currentActionFilterConfig,
            String version) {
        if (currentActionConfig == null) {
            log.error(StringMessageFormatter.replaceMsg("action:{} 无对应的checkCategory配置", nextActionName),
                    new SystemException("执行action无subcategory配置"));
            return ISCheckActionResult.empty();
        }
        if (currentActionFilterConfig != null) {
            Map<String, Object> evalContext = new HashMap<>();
            evalContext.put("context", context);
            evalContext.put("vehicleContext", context.getVehicleRunTimeContext());
            if (currentActionFilterConfig.isFilter(evalContext)) {
                //如果需要过滤
                return ISCheckActionResult.empty();
            }
        }
        Date startTime = new Date();
        boolean error = false;
        ISCheckActionResult actionResult = null;
        try {
            Date startCheckTime = new Date();
            //获取执行结果
            actionResult = getAction(nextActionName).execute(context);
            Date endCheckTime = new Date();
            if (actionResult == null) {
                context.setContinueCheck(false);
                actionResult = ISCheckActionResult.empty();
            } else if (currentActionConfig.isConfirmedSubcategory(actionResult.getCategoryEnum())) {
                context.setContinueCheck(false);
            } else if (currentActionConfig.isCanceledSubCategory(actionResult.getCategoryEnum())) {
                context.setContinueCheck(false);
            } else if (currentActionConfig.isReCheckSubCategory(actionResult.getCategoryEnum())) {
                actionResult.setNeedReCheck(true);
                context.setContinueCheck(false);
            } else {
                actionResult = ISCheckActionResult.empty(actionResult.getActionResult());
                context.setContinueCheck(true);
            }
            actionResult.setStartCheckTime(startCheckTime);
            actionResult.setDuration(DatetimeUtil.getTimeDiff(startCheckTime, endCheckTime, ChronoUnit.SECONDS));
            return actionResult;
        } catch (Exception e) {
            error = true;
            log.error(StringMessageFormatter.replaceMsg("执行预检Action失败，actionName:{}, actionContext: {}，兜底返回",
                    nextActionName, context), e);
            return ISCheckActionResult.empty();
        } finally {
            Date endTime = new Date();
            Transaction transaction = Cat.newTransactionWithDuration(CatMonitorConstant.CHECKING, nextActionName,
                    endTime.getTime() - startTime.getTime());
            transaction.setStatus(error ? "-1" : "0");
            transaction.complete();
            log.info("[scene:{}][checkAction:{}][version:{}][caseId:{}][vin:{}][startTime:{}][cost:{}][result:{}]",
                    checkScene, nextActionName, version, context.getItem().getTmpCaseId(),
                    context.getItem().getVin(), DatetimeUtil.formatTime(startTime),
                    endTime.getTime() - startTime.getTime(), JacksonUtils.to(actionResult));
        }
    }

    /**
     * 根据上次的检测结果，获取下一个action
     *
     * @return
     */
    private String getNextActionName(ISCheckActionContext context, Map<String, String> actionChain) {
        if (MapUtils.isEmpty(actionChain)) {
            return CharConstant.CHAR_EMPTY;
        }
        //根据获取上次的action
        String lastActionName = StringUtils.defaultString(context.getLastCheckActionName(), CharConstant.CHAR_EMPTY);
        //参数
        Map<String, Object> param = new HashMap<>();
        param.put("context", context);  // 执行上下文
        String nextActionName = SpELUtil.evaluateWithVariables(actionChain.get(lastActionName), param, String.class);
        actionChain.remove(lastActionName);  // 移除上次的action
        return StringUtils.defaultString(nextActionName, CharConstant.CHAR_EMPTY);
    }

    /**
     * 根据action名获取Action
     *
     * @param name
     * @return
     */
    private ISCheckAction getAction(String name) {
        return ACTION_MAP.get(name);
    }
}
