package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import static com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum.CANT_FOUND_ANY;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.walleeve.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.domain.component.RiskHandleCommonCompute;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.result.RiskCaseUpdatedResultDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.HandleStrategy;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CloudCursorAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.ReTicketAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.CloudCursorResourceRequest;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseMessageDTOConvert.VehicleEventDataMessageExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmFilterDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskMappingParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskMappingResultDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallCloudCursorTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory.CreateRiskCaseDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCaseFactory.CreateVehicleCaseRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.factory.RiskCheckingQueueItemFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GisInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCaseExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.GisInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ManualParkingMarkConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleTrafficRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.SafetyAreaQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DriveModeRecordQueryParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DriveModeRecordQueryParamVTO.SingleVinQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.DriveModeRecordVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Slf4j
@Component
public class RiskCaseOperateServiceImpl implements RiskCaseOperateService {

    private static final Integer ONE_DAY_HOUR_SECONDS = 24 * 60 * 60;
    /**
     * 风险事件仓储
     */
    @Resource
    private RiskCaseRepository riskCaseRepository;

    /**
     * 风险事件关联关系仓储
     */
    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    /**
     * 车辆信息仓储
     */
    @Resource
    private VehicleInfoRepository vehicleInfoRepository;

    /**
     * 车辆信息仓储
     */
    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;
    /**
     * 地理信息仓储
     */
    @Resource
    private GisInfoRepository gisInfoRepository;

    @Resource
    private RiskHandleCommonCompute handleCommonCompute;

    /**
     * 锁服务
     */
    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskCaseRepository caseRepository;

    /**
     * 车辆上下文仓储
     */
    @Resource
    private VehicleTrafficRepository vehicleContextRepository;

    @Resource
    private RiskCheckingQueueItemFactory riskCheckingQueueFactory;

    @MessageProducer(topic = MessageTopicEnum.WALLEMONITOR_RISK_VEHICLE_STATUS_MESSAGE, appKey = "com.sankuai.carosscan.realtimeinfo")
    private CommonMessageProducer<RiskCaseCallMrmMessageDTO> riskCaseMessageProducer;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private Map<String, HandleStrategy> riskHandleStrategyMap;

    @Resource
    private CloudCursorAdapter cloudCursorAdapter;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private ReTicketAdapter reTicketAdapter;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private CallCloudCursorRhinoService callCloudCursorRhino;

    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    /**
     * 创建或更新风险事件
     *
     * @param paramDTO
     * @return
     */
    @Override
    @Transactional
    public RiskCaseUpdatedResultDTO createOrUpdateRiskCase(RiskCaseUpdatedParamDTO paramDTO) {
        switch (paramDTO.getType()) {
            case USER_FEEDBACK: {
                // 反馈类风险事件不一定关联车辆
                // 当没关联车辆时使用特定的处理方式
                // 处理用户反馈类风险
                if (CollectionUtils.isEmpty(paramDTO.getVinList())) {
                    return handleFeedBackCase(paramDTO);
                }
                // 当关联车辆时使用通用的处理方式
                //会创建或更新风险事件
                return handleVehicleCase(paramDTO);
            }
            default: {
                return handleVehicleCase(paramDTO);
            }
        }

    }

    /**
     * 批量处置车辆风险事件
     *
     * @param riskCheckingQueueItemDOList
     */
    @Override
    public void transRiskQueueCaseAsTrueRiskCase(List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList) {
        log.info("transRiskQueueCaseAsTrueRiskCase start,riskCheckingQueueItemDOList = {}",
                riskCheckingQueueItemDOList);
        if (CollectionUtils.isEmpty(riskCheckingQueueItemDOList)) {
            return;
        }
        // 来源的caseId == 预检队列的eventId
        List<String> sourceCaseId = riskCheckingQueueItemDOList.stream().map(RiskCheckingQueueItemDO::getEventId)
                .collect(Collectors.toList());
        // 使用eventId，查询来源的case,如果存在来源case,使用来源case的状态，否则保持现状
        Map<String, RiskCaseDO> disposedRiskCaseDO = riskCaseRepository
                .queryByParam(RiskCaseDOQueryParamDTO.builder().caseIdList(sourceCaseId).build()).stream()
                .collect(Collectors.toMap(RiskCaseDO::getCaseId, Function.identity(), (k1, k2) -> k1));
        log.info("transRiskQueueCaseAsTrueRiskCase riskCheckingQueueItemDOList size: {}",
                riskCheckingQueueItemDOList.size());

        List<String> vinList = riskCheckingQueueItemDOList.stream().map(RiskCheckingQueueItemDO::getVin)
                .collect(Collectors.toList());
        /** 3. 查询数据总线服务，得到车辆相关的救援工单和事故工单数据 */
        VehicleRuntimeInfoParamVTO vehicleRuntimeInfoParamVTO = VehicleRuntimeInfoParamVTO.builder().vinList(vinList)
                .build();
        List<VehicleEveInfoVTO> vehicleEveInfoVTOList = vehicleAdapter.queryRuntimeVehicleInfo(
                vehicleRuntimeInfoParamVTO);
        if (CollectionUtils.isEmpty(vehicleEveInfoVTOList)) {
            log.info("查询数据总线服务，没有查询到车辆信息");
        }
        Map<String, VehicleEveInfoVTO> vehicleEveInfoVTOMap = vehicleEveInfoVTOList.stream()
                .collect(Collectors.toMap(VehicleEveInfoVTO::getVin, Function.identity(),
                        (v1, v2) -> v1));
        Map<String, Boolean> vehicleReTicketMap = reTicketAdapter.queryByVinList(vinList);
        Map<String, Boolean> vehicleWithManualParkingMap = queryDriveModeRecord(riskCheckingQueueItemDOList);
        List<String> caseIdList = riskCheckingQueueItemDOList.stream().map(RiskCheckingQueueItemDO::getTmpCaseId)
                .collect(Collectors.toList());
        CaseMarkInfoDOQueryParamDTO param = CaseMarkInfoDOQueryParamDTO.builder().caseIdList(caseIdList).build();
        Map<String, CaseMarkInfoDO> caseMarkInfoDOMap = caseMarkInfoRepository.queryMapByParam(param);

        riskCheckingQueueItemDOList.forEach(riskCheckingQueueDO -> {
            // 查询预检队列对应的来源风险事件的处置状态，如果存在则使用来源风险事件的处置状态，否则使用未处置状态。
            RiskCaseStatusEnum statusEnum = Optional
                    .ofNullable(disposedRiskCaseDO.get(riskCheckingQueueDO.getEventId())).map(RiskCaseDO::getStatus)
                    .orElse(RiskCaseStatusEnum.NO_DISPOSAL);
            // 如果来源风险事件的处置状态是终态，则使用来源风险事件的关闭时间，否则使用预检队列的关闭时间。
            Date closeTime = Optional.ofNullable(disposedRiskCaseDO.get(riskCheckingQueueDO.getEventId()))
                    .filter(riskCase -> RiskCaseStatusEnum.isTerminal(riskCase.getStatus()))
                    .map(RiskCaseDO::getCloseTime).orElse(riskCheckingQueueDO.getCloseTime());
            RiskCaseUpdatedParamDTO paramDTO = RiskCaseUpdatedParamDTO.builder()
                    .caseId(riskCheckingQueueDO.getTmpCaseId())
                    .type(riskCheckingQueueDO.getType())
                    .status(statusEnum)
                    .eventId(riskCheckingQueueDO.getEventId())
                    .source(riskCheckingQueueDO.getSource())
                    .vinList(Lists.newArrayList(riskCheckingQueueDO.getVin()))
                    .timestamp(riskCheckingQueueDO.getOccurTime().getTime())
                    .recallTime(riskCheckingQueueDO.getRecallTime().getTime())
                    .closeTime(closeTime.getTime())
                    .build();
            // 创建caseMarkInfo
            CaseMarkInfoDO caseMarkInfoDO = caseMarkInfoDOMap.computeIfAbsent(riskCheckingQueueDO.getTmpCaseId(),
                    k -> CaseMarkInfoDO.builder().caseId(riskCheckingQueueDO.getTmpCaseId()).build());
            // 创建case
            handleVehicleCase(paramDTO);
            // 进行标注
            handlePreCheckCaseMark(riskCheckingQueueDO, caseMarkInfoDO);
            // 查询停滞原因并更新
            updateImproperStrandingReason(riskCheckingQueueDO, vehicleEveInfoVTOMap, vehicleReTicketMap,
                    vehicleWithManualParkingMap, caseMarkInfoDO);
        });
    }

    /**
     * 处理标注
     * 
     * @param itemDO
     * @param caseMarkInfoDO
     */
    private void handlePreCheckCaseMark(RiskCheckingQueueItemDO itemDO, CaseMarkInfoDO caseMarkInfoDO) {
        // 标注
        if (Objects.isNull(itemDO)) {
            return;
        }
        // 获取checkResult
        Map<String, Object> checkResult = ReflectUtils.getNonNullFieldAndValue(itemDO.getCheckResult());
        if (Objects.isNull(itemDO.getCheckResult()) || Objects.isNull(itemDO.getCheckResult().getCategory())) {
            // 什么结果都没有
            return;
        }
        // 取值
        String operator = itemDO.getCheckResult().getCheckSource();
        if (CommonConstant.NONE_ACTION.equals(operator)) {
            //
            return;
        }
        ISCheckCategoryEnum checkResultEnum;
        if (!RiskQueueStatusEnum.CONFIRMED_TIMEOUT.equals(itemDO.getStatus())) {
            // 不为检测超时的状态时，才能使用check出来的数据
            checkResultEnum = itemDO.getCheckResult().getCategory();
        } else {
            // 超时使用
            checkResultEnum = CANT_FOUND_ANY;
        }
        // 标注结果
        caseMarkInfoDO.autoMark(checkResultEnum, checkResult, operator, itemDO.getRound());
        caseMarkInfoRepository.save(caseMarkInfoDO);
    }

    private RiskCaseUpdatedResultDTO handleVehicleCase(RiskCaseUpdatedParamDTO paramDTO) {
        // 查询或创建风险事件
        RiskCaseDO riskCaseDO = riskCaseRepository.queryByParam(RiskCaseDOQueryParamDTO.builder()
                        .eventId(paramDTO.getEventId())
                // 将查询结果转换为流，并获取第一个结果（如果存在）（envent_id）
                        .build()).stream().findFirst()
                // 如果不存在，创建风险事件 RiskCaseDO(vinList没有)（扩展字段是否添加）
                .orElse(RiskCaseFactory.createRiskCaseDO(CreateRiskCaseDOParamDTO.builder()
                        .caseId(paramDTO.getCaseId())
                        .vinList(paramDTO.getVinList())
                        .type(paramDTO.getType())
                        .status(paramDTO.getStatus())
                        .eventId(paramDTO.getEventId())
                        .source(paramDTO.getSource())
                        .timestamp(paramDTO.getTimestamp())
                        .recallTime(paramDTO.getRecallTime())
                        .build()));
        // 获取vin列表
        List<String> vinList = paramDTO.getVinList();
        CheckUtil.isNotEmpty(vinList, "风险事件关联的车辆列表不能为空");
        
        //根据入参，风险事件的状态，这里只需要直接更新即可
        // 更新风险事件状态
        riskCaseDO.updateStatus(paramDTO.getStatus(), paramDTO.getTimestamp());

        // 状态为大于等于终态时，才更新closeTime
        if (RiskCaseStatusEnum.isTerminal(paramDTO.getStatus()) && Objects.nonNull(paramDTO.getCloseTime())) {
            // 更新closeTime
            riskCaseDO.setCloseTime(new Date(paramDTO.getCloseTime()));
        }
        //查询风险与关联车
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder()
                        .eventId(paramDTO.getEventId())
                        .build());
        // 将查询结果转换为map，一个车一个关系
        Map<String, RiskCaseVehicleRelationDO> relationDOMap = riskCaseVehicleRelationDOList.stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getVin,
                        Function.identity(), (o1, o2) -> o1));

        //根据事件关联的车，更新车辆的关联关系——车辆可能增加 & 减少 & 车辆信息更新
        List<RiskCaseVehicleRelationDO> needRemoveRelationList = new ArrayList<>();
        List<RiskCaseVehicleRelationDO> needAddedRelationList = new ArrayList<>();
        
        //风险和车辆的关联关系 一个风险关联多个车辆  少就删除  多就新增  关系保留
        calcRelation(needRemoveRelationList, needAddedRelationList, relationDOMap,
                vinList, riskCaseDO.getCaseId(), paramDTO);
        //新增的关联关系
        handleSetVehicleSnapshot(riskCaseDO, needAddedRelationList);
        //更新地理位置信息（经纬度和系统名称）
        PositionDO anyVehiclePosition = needAddedRelationList.stream()
                .map(RiskCaseVehicleRelationDO::getVehicleSnapshotInfo)
                .filter(Objects::nonNull)
                .map(VehicleInfoDO::getPosition)
                .findFirst()
                .orElse(null);
        // 查询的结果
        VehicleEventDataMessageExtInfoDTO extInfoDTO = paramDTO.getMessageExtInfo();

        //将地理位置更新到风险事件中
        handleUpdateExtInfo(riskCaseDO, anyVehiclePosition, extInfoDTO);
        //更新需要删除的记录
        handleDelete(needRemoveRelationList);
        // 保存风险事件
        riskCaseRepository.save(riskCaseDO);
        // 保存风险事件关联关系
        riskCaseVehicleRelationRepository.batchSave(needAddedRelationList);
        // 保存风险事件关联关系
        riskCaseVehicleRelationRepository.batchSave(needRemoveRelationList);
        //新车需要更新
        return RiskCaseUpdatedResultDTO.builder().riskCaseDO(riskCaseDO).build();
    }

    /**
     * 更新车辆滞留原因
     *
     * @param riskCheckingQueueItemDO
     * @param vehicleEveInfoVTOMap
     * @param vehicleReTicketMap
     * @param vehicleWithManualParkingMap
     */
    private void updateImproperStrandingReason(RiskCheckingQueueItemDO riskCheckingQueueItemDO,
            Map<String, VehicleEveInfoVTO> vehicleEveInfoVTOMap,
            Map<String, Boolean> vehicleReTicketMap,
            Map<String, Boolean> vehicleWithManualParkingMap,
            CaseMarkInfoDO caseMarkInfoDO) {
        try {
            String vin = riskCheckingQueueItemDO.getVin();
            Boolean withReOrder = vehicleReTicketMap.get(vin);
            VehicleEveInfoVTO vehicleEveInfoVTO = vehicleEveInfoVTOMap.get(vin);
            Boolean withAccidentOrder = null == vehicleEveInfoVTO ? null : vehicleEveInfoVTO.getWithAccidentOrder();
            Boolean withRescueOrder = null == vehicleEveInfoVTO ? null : vehicleEveInfoVTO.getWithRescueOrder();
            Boolean withManualParking = vehicleWithManualParkingMap.getOrDefault(vin, false);

            ImproperStrandingReason improperStrandingReason = null == caseMarkInfoDO.getImproperStrandingReason() ?
                    ImproperStrandingReason.builder().build() : caseMarkInfoDO.getImproperStrandingReason();
            // 工单为 true 才更新
            if (null != withReOrder && withReOrder) {
                improperStrandingReason.withReOrder();
            }
            if (null != withAccidentOrder && withAccidentOrder) {
                improperStrandingReason.withAccidentOrder();
            }
            if (null != withRescueOrder && withRescueOrder) {
                improperStrandingReason.withRescueOrder();
            }
            if (null != withManualParking && withManualParking) {
                improperStrandingReason.withManualParking();
            }
            caseMarkInfoDO.updateImproperStrandingReason(improperStrandingReason);
            caseMarkInfoRepository.save(caseMarkInfoDO);
        } catch (Exception e) {
            log.error("更新车辆滞留原因失败", e);
        }
    }


    /**
     * 处理用户反馈风险case
     *
     * @param paramDTO
     * @return
     */
    private RiskCaseUpdatedResultDTO handleFeedBackCase(RiskCaseUpdatedParamDTO paramDTO) {
        RiskCaseDO riskCaseDO = riskCaseRepository.queryByParam(RiskCaseDOQueryParamDTO.builder()
                        .eventId(paramDTO.getEventId())
                        .build()).stream().findFirst()
                .orElse(RiskCaseFactory.createRiskCaseDO(CreateRiskCaseDOParamDTO.builder()
                        .vinList(paramDTO.getVinList())
                        .type(paramDTO.getType())
                        .status(paramDTO.getStatus())
                        .eventId(paramDTO.getEventId())
                        .source(paramDTO.getSource())
                        .timestamp(paramDTO.getTimestamp())
                        .recallTime(paramDTO.getRecallTime())
                        .build()));
        // 补充属性 状态-已完成，发生事件和解除时间保持一致
        riskCaseDO.setStatus(RiskCaseStatusEnum.DISPOSED);
        riskCaseDO.setCloseTime(new Date(paramDTO.getTimestamp()));

        if (Objects.isNull(riskCaseDO)) {
            return RiskCaseUpdatedResultDTO.builder().build();
        }
        handleUpdateExtInfo(riskCaseDO, null,
                paramDTO.getMessageExtInfo());
        riskCaseRepository.save(riskCaseDO);
        //新车需要更新
        return RiskCaseUpdatedResultDTO.builder().build();
    }

    /**
     * 判断是否要过滤同质化的停滞不当
     *
     * @param paramDTO
     * @return
     */
    private List<String> handleCheckStandStillNeedFilter(RiskCaseUpdatedParamDTO paramDTO) {
        if (paramDTO.getType() != RiskCaseTypeEnum.VEHICLE_STAND_STILL || !Objects.equals(
                RiskCaseStatusEnum.NO_DISPOSAL,
                paramDTO.getStatus()) || CollectionUtils.isEmpty(paramDTO.getVinList())) {
            //状态非开始 或者 非停止不前，不进行处理
            return paramDTO.getVinList();
        }
        Date beginDate = DatetimeUtil.getNSecondsBeforeDateTime(new Date(paramDTO.getTimestamp()),
                ONE_DAY_HOUR_SECONDS);
        //查询同车辆，没结束的事件
        List<String> unTerminalAndStandStillVehicleCaseIdList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder()
                        //状态
                        .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode())
                        //时间：过去24小时到目前
                        .createTimeCreateTo(beginDate)
                        //状态：未关闭
                        .statusList(RiskCaseStatusEnum.getUnTerminal())
                        .build()).stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unTerminalAndStandStillVehicleCaseIdList)) {
            //没有相关进行中的风险事件
            return paramDTO.getVinList();
        }
        // 用上面的 caseId 来查询
        List<RiskCaseVehicleRelationDO> list = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder()
                        .vinList(paramDTO.getVinList())
                        //查询时间小于事件时间
                        .caseIdList(unTerminalAndStandStillVehicleCaseIdList)
                        .build());
        if (CollectionUtils.isEmpty(list)) {
            return paramDTO.getVinList();
        }
        //如果查询出来，就需要过滤一下
        Set<String> vinSet = list.stream().map(RiskCaseVehicleRelationDO::getVin).collect(Collectors.toSet());
        //只保留不在上面的车辆
        return paramDTO.getVinList().stream().filter(s -> {
            boolean isNotFilter = !vinSet.contains(s);
            if (!isNotFilter) {
                //如果被过滤了，打印日志
                log.warn("停滞消息被过滤:{}", JacksonUtils.to(paramDTO));
            }
            return isNotFilter;
        }).collect(Collectors.toList());
    }


    /**
     * 查询 end消息 对应eventId 的start消息是否存在
     *
     * @param eventId
     * @return
     */
    public boolean checkStartMessageExist(String eventId) {
        if (StringUtils.isBlank(eventId)) {
            return false;
        }
        List<RiskCaseDO> riskCaseList = riskCaseRepository.queryByParam(RiskCaseDOQueryParamDTO.builder()
                .eventId(eventId)
                .createTimeCreateTo(DatetimeUtil.getNSecondsBeforeDateTime(new Date(), CommonConstant.SECONDS_PER_DAY))
                .build());
        return CollectionUtils.isNotEmpty(riskCaseList);
    }

    /**
     * 拓展信息
     *
     * @param riskCaseDO
     */
    private void handleUpdateExtInfo(RiskCaseDO riskCaseDO, PositionDO anyVehiclePosition,
            VehicleEventDataMessageExtInfoDTO extInfoDTO) {
        if (riskCaseDO.getExtInfo() != null) {
            //如果有值则不更新
            return;
        }
        RiskCaseExtInfoDO riskCaseExtInfoDO = buildExtInfoDTO(anyVehiclePosition, extInfoDTO);
        if (Objects.isNull(riskCaseExtInfoDO)) {
            return;
        }
        // 更新风险事件扩展信息
        riskCaseDO.updateRiskCaseExtInfoDO(riskCaseExtInfoDO);
        riskCaseDO.setPoiName(riskCaseExtInfoDO.getPoi());
    }

    /**
     * 构建拓展信息
     *
     * @param anyVehiclePosition
     * @param extInfoDTO
     * @return
     */
    private RiskCaseExtInfoDO buildExtInfoDTO(PositionDO anyVehiclePosition,
            VehicleEventDataMessageExtInfoDTO extInfoDTO) {
        if (Objects.isNull(anyVehiclePosition) && Objects.isNull(extInfoDTO)) {
            return null;
        }
        RiskCaseExtInfoDO riskCaseExtInfoDO = new RiskCaseExtInfoDO();

        // 处理位置信息
        if (Objects.nonNull(anyVehiclePosition)) {
            GisInfoDO gisInfoDO = gisInfoRepository.queryByPosition(anyVehiclePosition);
            if (Objects.nonNull(gisInfoDO)) {
                riskCaseExtInfoDO.setCity(gisInfoDO.getCity());
                riskCaseExtInfoDO.setAre(gisInfoDO.getArea());
                riskCaseExtInfoDO.setPoi(gisInfoDO.getPoi());
                riskCaseExtInfoDO.setPosition(gisInfoDO.getPosition());
            }
        }
        // 处理事件信息
        if (Objects.nonNull(extInfoDTO)) {
            riskCaseExtInfoDO.setEventDesc(extInfoDTO.getRiskEventDesc());
            // 当异常事件没有绑定车辆时无法获取位置信息，可以采用消息扩展字段里的位置信息
            if (StringUtils.isBlank(riskCaseExtInfoDO.getPoi())) {
                riskCaseExtInfoDO.setPoi(extInfoDTO.getRiskEventLocation());
            }
        }
        return riskCaseExtInfoDO;
    }

    /**
     * 处理删除
     *
     * @param needRemoveRelationList
     */
    private void handleDelete(List<RiskCaseVehicleRelationDO> needRemoveRelationList) {
        if (CollectionUtils.isEmpty(needRemoveRelationList)) {
            return;
        }
        //删除关联关系
        needRemoveRelationList.forEach(RiskCaseVehicleRelationDO::remove);
    }


    /**
     * 更新设置车辆快照
     *
     * @param riskCaseDO
     * @param needAddedRelationList
     */
    private void handleSetVehicleSnapshot(RiskCaseDO riskCaseDO,
            List<RiskCaseVehicleRelationDO> needAddedRelationList) {
        if (CollectionUtils.isEmpty(needAddedRelationList)) {
            return;
        }
        List<String> vinList = needAddedRelationList.stream().map(RiskCaseVehicleRelationDO::getVin)
                .collect(Collectors.toList());
        //要添加的车辆，必须成功保存
        List<VehicleInfoDO> vehicleInfoDOList = vehicleInfoRepository.queryByVinList(vinList);
        Map<String, VehicleInfoDO> vehicleInfoDOMap = vehicleInfoDOList.stream()
                .collect(Collectors.toMap(VehicleInfoDO::getVin, Function.identity()));
        // 取车辆上下文
        Map<String, VehicleRuntimeInfoContextDO> runtimeInfoContextDOMap = vehicleRuntimeInfoContextRepository
                .getFromCache(vinList).stream()
                .collect(Collectors.toMap(VehicleRuntimeInfoContextDO::getVin, Function.identity(), (o1, o2) -> o2));
        SystemCheckUtil.isNotEmpty(vehicleInfoDOList, "车辆信息查询异常");
        Map<String, TrafficLightContextDO> vin2TrafficLightContextDOMap = vehicleContextRepository.batchGetVehicleTrafficContext(
                vinList);
        //更新车辆信息
        needAddedRelationList.forEach(riskCaseVehicleRelationDO -> {
            VehicleInfoDO runtimeInfoSnapshot = vehicleInfoDOMap.get(riskCaseVehicleRelationDO.getVin());
            // 更新车辆的扩展信息
            SafetyAreaDO safetyAreaDO = calcSafetyArea(runtimeInfoSnapshot);
            riskCaseVehicleRelationDO.updateSafetyAreaInfo(safetyAreaDO);
            //更新车辆信息
            riskCaseVehicleRelationDO.updateVehicleSnapshotInfo(runtimeInfoSnapshot);
            RiskCaseTypeEnum riskCaseTypeEnum = riskCaseDO.getType();
            RiskCaseSourceEnum riskCaseSourceEnum = riskCaseDO.getSource();
            if ((RiskCaseTypeEnum.VEHICLE_STAND_STILL.equals(riskCaseTypeEnum)
                    || RiskCaseTypeEnum.STRANDING.equals(riskCaseTypeEnum))
                    && RiskCaseSourceEnum.recallBySelf(riskCaseSourceEnum)) {
                // 自召回的停滞，或者停滞不当，才需要更新障碍物和停滞墙
                riskCaseVehicleRelationDO.updateFromVehicleRuntimeContext(
                        runtimeInfoContextDOMap.get(riskCaseVehicleRelationDO.getVin()));
            }
            riskCaseVehicleRelationDO.updatePurpose(runtimeInfoSnapshot.getPurpose());
            riskCaseVehicleRelationDO.updateVhrMode(
                    Optional.ofNullable(runtimeInfoSnapshot.getVhr()).map(VHRModeEnum::getCode)
                            .orElse(StringUtils.EMPTY));
            Boolean isTrafficLight = Optional.ofNullable(
                            vin2TrafficLightContextDOMap.get(riskCaseVehicleRelationDO.getVin()))
                    .filter(trafficLightContextDO -> trafficLightContextDO.getUpdateTime() != null)
                    .filter(trafficLightContextDO ->
                            // 判断更新时间是否小于一分钟
                            DatetimeUtil.getSecondsDiff(
                                    DatetimeUtil.dateToLocalDateTime(new Date(trafficLightContextDO.getUpdateTime())),
                                    DatetimeUtil.dateToLocalDateTime(new Date())) < 60)
                    .map(TrafficLightContextDO::isWaitingRed).orElse(false);
            riskCaseVehicleRelationDO.updateWaitRedLight(isTrafficLight);
            // 更新车辆类型
            riskCaseVehicleRelationDO.updateVehicleType(runtimeInfoSnapshot.getVehicleType());
        });
        if (CollectionUtils.isNotEmpty(vehicleInfoDOList)) {
            riskCaseDO.updatePlaceCode(
                    vehicleInfoDOList.stream().map(VehicleInfoDO::getPlaceCode).filter(StringUtils::isNotBlank)
                            .findFirst().orElse(CharConstant.CHAR_EMPTY));
        }
    }

    /**
     * 计算停车区域
     *
     * @param runtimeInfoSnapshot
     * @return
     */
    private SafetyAreaDO calcSafetyArea(VehicleInfoDO runtimeInfoSnapshot) {
        if (Objects.isNull(runtimeInfoSnapshot) || Objects.isNull(runtimeInfoSnapshot.getPosition())) {
            return null;
        }
        try {
            // 格式转换
            PositionDO positionDO = GeoToolsUtil.transferCoordinateSystemEnum(runtimeInfoSnapshot.getPosition(),
                    CoordinateSystemEnum.GCJ02);
            if (Objects.isNull(positionDO)) {
                return null;
            }
            // 判断是否在停车区域
            String areaId = safetyAreaRepository.verifyInParkingArea(positionDO);
            if (StringUtils.isBlank(areaId)) {
                return null;
            }
            List<SafetyAreaDO> safetyAreaDOList = safetyAreaRepository.queryByParam(
                    SafetyAreaQueryParamDTO.builder().areaIdList(Arrays.asList(areaId)).build());
            if (CollectionUtils.isEmpty(safetyAreaDOList)) {
                return null;
            }
            return safetyAreaDOList.get(0);
        } catch (Exception e) {
            log.error("calcSafetyArea error", e);
            return null;
        }
    }

    /**
     * 计算关联关系的变更，包括新增、删除、更新
     *
     * @param needRemoveRelationList
     * @param needAddedRelationList
     * @param relationDOMap
     * @param vinList
     */
    private void calcRelation(List<RiskCaseVehicleRelationDO> needRemoveRelationList,
            List<RiskCaseVehicleRelationDO> needAddedRelationList,
            Map<String, RiskCaseVehicleRelationDO> relationDOMap, List<String> vinList, String caseId,
            RiskCaseUpdatedParamDTO riskCaseUpdatedParamDTO) {
        // 已经存在的关联关系中的车辆VIN集合
        Set<String> alreadyInDBRelationVinSet = relationDOMap.keySet();
        // 此次更新涉及的车辆VIN集合
        Set<String> thisRiskRelationVinNow = new HashSet<>(vinList);
        VehicleEventDataMessageExtInfoDTO extInfoDTO = riskCaseUpdatedParamDTO.getMessageExtInfo();
        // 计算需要删除的关联关系
        for (String vin : alreadyInDBRelationVinSet) {
            if (!thisRiskRelationVinNow.contains(vin)) {
                needRemoveRelationList.add(relationDOMap.get(vin));
            }
        }
        // 计算需要添加的关联关系
        for (String vin : thisRiskRelationVinNow) {
            if (!alreadyInDBRelationVinSet.contains(vin)) {
                RiskCaseVehicleRelationDO newRelation = RiskCaseFactory.createRiskRelation(
                        //创建新的
                        CreateVehicleCaseRelationDOParamDTO.builder()
                                .caseId(caseId)
                                .traceId(riskCaseUpdatedParamDTO.getTraceId())
                                .occurTime(riskCaseUpdatedParamDTO.getTimestamp())
                                .eventId(riskCaseUpdatedParamDTO.getEventId())
                                .status(RiskCaseVehicleStatusEnum.INIT)
                                .type(riskCaseUpdatedParamDTO.getType())
                                .sideBySideTimestamp(riskCaseUpdatedParamDTO.getSideBySideTimestamp())
                                .milliBeginTime(new Date(riskCaseUpdatedParamDTO.getTimestamp()))
                                .context(Optional.ofNullable(extInfoDTO)
                                        .map(VehicleEventDataMessageExtInfoDTO::getContent).orElse(new HashMap<>()))
                                .vin(vin)
                                .build());
                needAddedRelationList.add(newRelation);
            }
        }
    }

    /**
     * 关联处置的trace
     *
     * @param paramDTO
     */
    @Override
    public RiskMappingResultDTO mappingRiskCase(RiskMappingParamDTO paramDTO) {
        //根据车和事件id的信息，进行查询
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder().vin(paramDTO.getVin())
                        .eventIdList(paramDTO.getEventIdList()).build());
        if (CollectionUtils.isEmpty(riskCaseVehicleRelationDOList)) {
            //关联不上
            log.warn(paramDTO.getTraceId(), new SystemException("收到trace的关联消息，但是无相关风险事件"));
            return RiskMappingResultDTO.builder().build();
        }
        //要更新的保存一下
        List<RiskCaseVehicleRelationDO> updatedRelationList = new ArrayList<>();
        //按照事件进行分组
        Map<String, List<RiskCaseVehicleRelationDO>> caseId2RelationMap = riskCaseVehicleRelationDOList.stream()
                .peek(relation -> {
                    boolean updated = relation.updateTraceId(paramDTO.getTraceId());
                    if (updated) {
                        //发生过变更
                        updatedRelationList.add(relation);
                    }
                })
                .collect(Collectors.groupingBy(RiskCaseVehicleRelationDO::getCaseId));
        List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(RiskCaseDOQueryParamDTO.builder()
                .caseIdList(new ArrayList<>(caseId2RelationMap.keySet()))
                .build());
        Map<String, RiskCaseDO> riskCaseDOMap = riskCaseDOList.stream()
                .collect(Collectors.toMap(RiskCaseDO::getCaseId, Function.identity(), (o1, o2) -> o1));
        //计算需要更新的风险事件
        List<RiskCaseDO> updatedRiskCaseDOList = refreshRiskCaseDO(riskCaseDOMap, caseId2RelationMap);
        if (CollectionUtils.isNotEmpty(updatedRelationList)) {
            riskCaseVehicleRelationRepository.batchSave(updatedRelationList);
        }
        if (CollectionUtils.isNotEmpty(updatedRiskCaseDOList)) {
            riskCaseRepository.batchSave(updatedRiskCaseDOList);
        }
        return RiskMappingResultDTO.builder().caseIdList(
                        updatedRelationList.stream().map(RiskCaseVehicleRelationDO::getCaseId).collect(Collectors.toList()))
                .build();


    }

    /**
     * 更新数据库和发送消息
     *
     * @param callMrmStrategyConfigDTO
     * @param filterDTO
     * @param riskCaseDO
     */
    @Transactional
    public void updateRiskCaseAndSendMsg(CallMrmStrategyConfigDTO callMrmStrategyConfigDTO,
            RiskCaseCallMrmFilterDTO filterDTO,
            RiskCaseDO riskCaseDO) {

        // 更新数据库
        riskCaseDO.setMrmCalled(RiskCaseMrmCalledStatusEnum.CALLING);
        riskCaseRepository.save(riskCaseDO);

        // 更新呼叫时间
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = riskCaseVehicleRelationRepository.getByEventIdAndVin(
                riskCaseDO.getEventId(), filterDTO.getVin());
        if (Objects.nonNull(riskCaseVehicleRelationDO)) {
            riskCaseVehicleRelationDO.setRequestSeatTime(new Date());
            // 更新风险事件的关联关系中的呼叫原因字段为策略配置的呼叫原因
            riskCaseVehicleRelationDO.setCallMrmReason(String.valueOf(callMrmStrategyConfigDTO.getCallMrmReason()));
            riskCaseVehicleRelationRepository.save(riskCaseVehicleRelationDO);
        } else {
            log.error(String.format("riskCaseVehicleRelationRepository.getByEventIdAndVin is null, eventId:%s",
                    riskCaseDO.getEventId()), new IllegalArgumentException());
        }

        if (lionConfigRepository.isStagnationCallSwitchOn()) {
            //判断是否真实发送消息
            RiskCaseCallMrmMessageDTO messageDTO = RiskCaseCallMrmMessageDTO.builder()
                    .vin(filterDTO.getVin())
                    .startTimestamp(DatetimeUtil.getTimeInSeconds(riskCaseDO.getOccurTime()))
                    .key(callMrmStrategyConfigDTO.getKey())
                    .alarmTimestamp(DatetimeUtil.getTimeInSeconds(riskCaseDO.getRecallTime()))
                    .issueCode(callMrmStrategyConfigDTO.getIssueCode()).build();
            String msgId = riskCaseMessageProducer.sendCustomMessage(messageDTO);
            CheckUtil.isNotBlank(msgId, "发送消息失败");
        }

    }

    @Transactional
    @Override
    public void updateRiskCaseAndCallCloudCursor(CallSecuritySystemStrategyConfigDTO callSecuritySystemStrategyConfigDTO,
                                                 RiskCaseCallMrmFilterDTO filterDTO,
                                                 RiskCaseDO riskCaseDO) {
        // 更新数据库
        riskCaseDO.setMrmCalled(RiskCaseMrmCalledStatusEnum.CALLING);
        riskCaseRepository.save(riskCaseDO);

        // 更新呼叫时间
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = riskCaseVehicleRelationRepository.getByEventIdAndVin(
                riskCaseDO.getEventId(), filterDTO.getVin());
        if (Objects.nonNull(riskCaseVehicleRelationDO)) {
            riskCaseVehicleRelationDO.setRequestSeatTime(new Date());
            riskCaseVehicleRelationDO.setCallMrmReason(String.valueOf(callSecuritySystemStrategyConfigDTO.getReason()));
            riskCaseVehicleRelationRepository.save(riskCaseVehicleRelationDO);
        } else {
            log.error(String.format("riskCaseVehicleRelationRepository.getByEventIdAndVin is null, eventId:%s",
                    riskCaseDO.getEventId()), new IllegalArgumentException());
        }

        // 呼叫云控开关打开，提交呼叫请求
        if (lionConfigRepository.getRiskCaseSecuritySystemCallSwitch()) {
            callCloudCursorRhino.callCloudCursor(callSecuritySystemStrategyConfigDTO, filterDTO.getVin(),
                    riskCaseDO.getPoiName());
        }


    }


    /**
     * 呼叫或者取消呼叫云控
     *
     * @param callSecuritySystemStrategyConfigDTO 呼叫配置
     * @param cloudEventTypeEnum 呼叫或取消
     * @param vin 车架号
     * */
    private void callOrCancelCloudCursor(CallSecuritySystemStrategyConfigDTO callSecuritySystemStrategyConfigDTO,
                                         CallCloudCursorTypeEnum cloudEventTypeEnum, String vin) {
        // 构建呼叫请求体
        CloudCursorResourceRequest request = CloudCursorResourceRequest.builder()
                .action(cloudEventTypeEnum.name().toLowerCase())
                .reason(callSecuritySystemStrategyConfigDTO.getReason())
                .timestamp(System.currentTimeMillis())
                .vin(vin)
                .source(callSecuritySystemStrategyConfigDTO.getRequestSource())
                .needCancelCommand(true)
                .build();

        cloudCursorAdapter.callCloudCursor(request);
    }

    /**
     * 更新风险事件已调用MRM
     *
     * @param riskCaseDO
     */
    @Override
    public void updateRiskCaseMrmCalled(RiskCaseDO riskCaseDO) {
        // 更新数据库
        riskCaseDO.setMrmCalled(RiskCaseMrmCalledStatusEnum.CALLING);
        riskCaseRepository.save(riskCaseDO);
    }

    @Override
    public void manualCallMrm(String caseId) {
        RiskCaseDO thisRiskCaseDO = riskCaseRepository.getByCaseId(caseId);
        if (thisRiskCaseDO == null || RiskCaseStatusEnum.isTerminal(thisRiskCaseDO.getStatus())) {
            //已完结case不再尝试发送消息
            log.info("manualCallMrm case is terminal,caseId:{}", caseId);
            return;
        }
        List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList = riskCaseVehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder()
                        .caseIdList(Collections.singletonList(caseId))
                        .build());
        if (CollectionUtils.isEmpty(riskCaseVehicleRelationDOList)) {
            log.warn("manualCallMrm case has no vin ,caseId:{}", caseId);
            return;
        }
        riskCaseVehicleRelationDOList.forEach(caseVehicleRelationDO -> {
            RiskCaseCallMrmMessageDTO messageDTO = RiskCaseCallMrmMessageDTO.builder()
                    .vin(caseVehicleRelationDO.getVin())
                    .startTimestamp(DatetimeUtil.getTimeInSeconds(thisRiskCaseDO.getOccurTime()))
                    .key("vehicle_stop_signal")
                    .alarmTimestamp(DatetimeUtil.getTimeInSeconds(thisRiskCaseDO.getRecallTime()))
                    .issueCode(18005).build();
            String msgId = riskCaseMessageProducer.sendCustomMessage(messageDTO);
            CheckUtil.isNotBlank(msgId, "发送消息失败");
            // 设置呼叫坐席时间
            caseVehicleRelationDO.setRequestSeatTime(new Date());
        });
        //设置并保存
        thisRiskCaseDO.setMrmCalled(RiskCaseMrmCalledStatusEnum.CALLING);
        riskCaseRepository.save(thisRiskCaseDO);
        riskCaseVehicleRelationRepository.batchSave(riskCaseVehicleRelationDOList);
    }

    /**
     * 取消风险事件呼叫坐席
     *
     * @param vin
     * @param riskCaseDO
     * @param releaseMrmStrategyConfigDTOMap
     */
    @Override
    public void cancelCallMrm(String vin, RiskCaseDO riskCaseDO,
            Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> releaseMrmStrategyConfigDTOMap) {
        if (Objects.isNull(riskCaseDO.getSource()) || Objects.isNull(riskCaseDO.getType())) {
            return;
        }
        Integer source = riskCaseDO.getSource().getCode();
        Integer type = riskCaseDO.getType().getCode();

        // 4.1 判断风险事件是否配置了解除策略配置
        Pair<Integer, Integer> riskCaseCategoryPair = new Pair<>(source, type);
        if (!releaseMrmStrategyConfigDTOMap.containsKey(riskCaseCategoryPair)) {
            log.error("handleStagnationEvent 找不到解除策略配置", new IllegalArgumentException());
            return;
        }
        // 4.2 根据风险事件类型获取对应的处理策略类
        RiskCaseCategoryEnum riskCaseCategoryEnum = RiskCaseCategoryEnum.findByValue(source, type);
        if (Objects.isNull(riskCaseCategoryEnum)) {
            log.error("handleStagnationEvent 找不到风险事件类型", new IllegalArgumentException());
            return;
        }
        HandleStrategy handleStrategy = riskHandleStrategyMap.getOrDefault(riskCaseCategoryEnum.getBeanName(),
                null);
        if (Objects.isNull(handleStrategy)) {
            log.error("handleStagnationEvent 找不到处理策略类", new IllegalArgumentException());
            return;
        }
        handleStrategy.process(vin, releaseMrmStrategyConfigDTOMap.get(riskCaseCategoryPair), riskCaseDO);
    }

    @Override
    public void cancelCallSecuritySystem(String vin, RiskCaseDO riskCaseDO,
                                         Map<Pair<Integer, Integer>, LionConfigRepositoryImpl.ReleaseSecuritySystemConfigDTO> releaseSecuritySystemConfigDTOMap) {
        if (Objects.isNull(riskCaseDO.getSource()) || Objects.isNull(riskCaseDO.getType())) {
            return;
        }
        Integer source = riskCaseDO.getSource().getCode();
        Integer type = riskCaseDO.getType().getCode();

        // 4.1 判断风险事件是否配置了解除策略配置
        Pair<Integer, Integer> riskCaseCategoryPair = new Pair<>(source, type);
        if (!releaseSecuritySystemConfigDTOMap.containsKey(riskCaseCategoryPair)) {
            log.error("handleStagnationEvent 找不到解除策略配置", new IllegalArgumentException());
            return;
        }
        // 4.2 根据风险事件类型获取对应的处理策略类
        RiskCaseCategoryEnum riskCaseCategoryEnum = RiskCaseCategoryEnum.findByValue(source, type);
        if (Objects.isNull(riskCaseCategoryEnum)) {
            log.error("handleStagnationEvent 找不到风险事件类型", new IllegalArgumentException());
            return;
        }
        HandleStrategy handleStrategy = riskHandleStrategyMap.getOrDefault(riskCaseCategoryEnum.getBeanName(),
                null);
        if (Objects.isNull(handleStrategy)) {
            log.error("handleStagnationEvent 找不到处理策略类", new IllegalArgumentException());
            return;
        }
        handleStrategy.process(vin, releaseSecuritySystemConfigDTOMap.get(riskCaseCategoryPair), riskCaseDO);
    }

    /**
     * 更新风险事件的状态
     *
     * @param riskCaseDOMap
     * @param caseId2RelationMap
     * @return
     */
    private List<RiskCaseDO> refreshRiskCaseDO(Map<String, RiskCaseDO> riskCaseDOMap,
            Map<String, List<RiskCaseVehicleRelationDO>> caseId2RelationMap) {
        List<RiskCaseDO> updatedRiskCaseDOList = new ArrayList<>();
        caseId2RelationMap.forEach((caseId, relationList) -> {
            RiskCaseDO riskCaseDO = riskCaseDOMap.get(caseId);
            if (riskCaseDO == null || CollectionUtils.isEmpty(relationList)) {
                return;
            }
            //更新
            List<RiskCaseVehicleRelationDO> inProcessList = new ArrayList<>();
            List<RiskCaseVehicleRelationDO> initList = new ArrayList<>();
            List<RiskCaseVehicleRelationDO> doneList = new ArrayList<>();
            relationList.forEach(riskCaseVehicleRelationDO -> {
                // 根据风险事件车辆关联关系的状态，将关联关系列表分为三种类型：进行中、已完成和初始状态。
                if (RiskCaseVehicleStatusEnum.isInProcess(riskCaseVehicleRelationDO.getStatus())) {
                    inProcessList.add(riskCaseVehicleRelationDO);
                } else if (RiskCaseVehicleStatusEnum.isDisposed(riskCaseVehicleRelationDO.getStatus())) {
                    doneList.add(riskCaseVehicleRelationDO);
                } else {
                    initList.add(riskCaseVehicleRelationDO);
                }
            });
            //更新状态
            boolean updated = riskCaseDO.refreshStatusByRelation(relationList.size(), initList.size(),
                    inProcessList.size(),
                    doneList.size());
            if (updated) {
                //变更过，需要进行保存
                updatedRiskCaseDOList.add(riskCaseDO);
            }
        });
        return updatedRiskCaseDOList;
    }

    /**
     * 查询车辆行驶模式
     *
     * @param riskCheckingQueueItemDOList
     * @return
     */
    private Map<String, Boolean> queryDriveModeRecord(
            List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList) {

        // 检查配置
        ManualParkingMarkConfigDTO manualParkingMarkConfigDTO = lionConfigRepository.getManualParkingMarkConfig();
        if (manualParkingMarkConfigDTO == null) {
            log.error("queryDriveModeRecord 找不到人工停车标记配置");
            return new HashMap<>();
        }
        // 查询历史驾驶模式变更记录
        List<SingleVinQueryParam> vinQueryParamList = riskCheckingQueueItemDOList.stream()
                .map(x -> SingleVinQueryParam.builder()
                        .caseId(x.getTmpCaseId())
                        .vin(x.getVin())
                        .startTime(DatetimeUtil.getNSecondsBeforeDateTime(x.getOccurTime(),
                                manualParkingMarkConfigDTO.getQueryDriveModeNSecondsBeforeStranding()))
                        .endTime(new Date())
                        .build()).filter(Objects::nonNull).collect(Collectors.toList());
        DriveModeRecordQueryParamVTO driveModeQueryParam = DriveModeRecordQueryParamVTO.builder()
                .vinQueryParamList(vinQueryParamList).build();
        Map<String, DriveModeRecordVTO> driveModeRecordVTOMap = vehicleAdapter.queryDriveModeRecord(
                driveModeQueryParam);
        if (MapUtils.isEmpty(driveModeRecordVTOMap)) {
            return new HashMap<>();
        }
        // 计算是否为主动停靠
        return riskCheckingQueueItemDOList.stream()
                .collect(Collectors.toMap(
                        RiskCheckingQueueItemDO::getTmpCaseId,
                        queueItemDO -> handleCommonCompute.calcIsParkingByManual(
                                driveModeRecordVTOMap.get(queueItemDO.getTmpCaseId()),
                                queueItemDO.getOccurTime(),
                                queueItemDO.getTmpCaseId())
                ));
    }


}
