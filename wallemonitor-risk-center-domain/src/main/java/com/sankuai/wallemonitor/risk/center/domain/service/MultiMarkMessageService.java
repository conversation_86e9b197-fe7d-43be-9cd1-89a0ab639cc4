package com.sankuai.wallemonitor.risk.center.domain.service;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.wallemonitor.risk.center.infra.dto.MarkMessageDTO;

public interface MultiMarkMessageService {

    /**
     * 消费消息
     * 
     * @param message
     * @return
     */
    ConsumeStatus receive(String message);

    /**
     * 发送多版本标注消息
     * 
     * @param messageDTO
     */
    void sendMultiMarkMessage(MarkMessageDTO messageDTO);
}
