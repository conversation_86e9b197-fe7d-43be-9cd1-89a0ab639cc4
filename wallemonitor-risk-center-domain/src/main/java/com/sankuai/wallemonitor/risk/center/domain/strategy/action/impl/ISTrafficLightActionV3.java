package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.domain.result.ISTrafficLightResult;
import com.sankuai.wallemonitor.risk.center.domain.result.ISWaitingInQueueResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter.SearchNearbyRequestVTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficLightActionConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapElementTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import java.util.Comparator;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 停滞不当预检-红绿灯
 */
@Slf4j
@Component
public class ISTrafficLightActionV3 implements ISCheckAction<ISTrafficLightResult> {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private VehicleAdapter vehicleAdapter;
    /**
     * 执行预检
     *
     * @param actionContext
     */
    @Override
    public ISCheckActionResult<ISTrafficLightResult> execute(ISCheckActionContext actionContext) {
        VehicleRuntimeInfoContextDO contextDO = actionContext.getVehicleRunTimeContext();
        if (contextDO == null) {
            return ISCheckActionResult.empty();
        }
        // 如果自动驾驶状态下，为红灯
        TrafficLightTypeEnum trafficLightTypeEnum = contextDO.findTrafficLightType();
        if (trafficLightTypeEnum == null) {
            return ISCheckActionResult.empty();
        }
        TrafficLightActionConfigDTO trafficLightActionConfigDTO = actionContext
                .getCurrentActionConfig(TrafficLightActionConfigDTO.class);

        switch (trafficLightTypeEnum) {
            case YELLOW:
            case RED: {
                ISTrafficLightResult result = ISTrafficLightResult.builder().build();
                result.setNowTrafficLightType(trafficLightTypeEnum);
                if (trafficLightActionConfigDTO == null
                        || !trafficLightActionConfigDTO.isEnable()) {
                    // 如果如果没有开配置，走原来的逻辑
                    return ISCheckActionResult.<ISTrafficLightResult>builder()
                            .categoryEnum(ISCheckCategoryEnum.RED_LIGHT)
                            .actionResult(result)
                            .build();
                }
                String area = contextDO.getHdMapArea();
                // 如果开了配置：红灯但是需要判定等红的的位姿
                if (contextDO.hasInJunction()) {
                    // 计算路口的最近距离
                    HdMapElementGeoDO inJunctionElement = hdMapAdapter.searchNearby(SearchNearbyRequestVTO.builder()
                            // 区域
                            .area(area)
                            // 路口范围
                            .hdMapEnum(HdMapEnum.JUNCTION)
                            // 路口类型
                            .restrictType(Lists.newArrayList(HdMapElementTypeEnum.JUNCTION.getValue()))
                            // 车辆定位
                            .positionDO(contextDO.getLocation()).range(trafficLightActionConfigDTO.getSearchRange())
                            .build()).stream()
                            // 包含
                            .filter(hdMapElementGeoDO -> Objects.nonNull(hdMapElementGeoDO.getPolygonDO())
                                    && hdMapElementGeoDO.isInPolygon(contextDO.getLocation()))
                            .findFirst().orElse(null);
                    Double neraByDistance = null;
                    if (Objects.nonNull(inJunctionElement)) {
                        // 如果不为空,设置值
                        neraByDistance = GeoToolsUtil.pointToLineSegmentDistance(contextDO.getLocation(),
                                inJunctionElement.getPolygonDO().getPoints());
                        result.setJunctionId(inJunctionElement.getId());
                        result.setNearByJunction(neraByDistance);
                    }
                    if (neraByDistance != null
                            && neraByDistance < trafficLightActionConfigDTO.getMaxNearFromJunction()) {
                        // 比最大可远离距离小，说明靠近灯，允许
                        result.setNowTrafficLightType(trafficLightTypeEnum);
                        return ISCheckActionResult.<ISTrafficLightResult>builder()
                                .categoryEnum(ISCheckCategoryEnum.RED_LIGHT).actionResult(result).build();

                    }
                    // 如果为空 或者 远离路边边距 按照敏感原则处
                    return ISCheckActionResult.<ISTrafficLightResult>builder()
                            .categoryEnum(ISCheckCategoryEnum.IN_JUNCTION)
                            .actionResult(result)
                            .build();
                }
                // 如果不是路口，取排队的结果
                ISCheckActionResult<ISWaitingInQueueResult> queueResult = actionContext
                        .getActionResult("ISWaitingInQueue");
                // 存在障碍物数据
                boolean hasFrontObstacle = Objects.nonNull(contextDO.getFrontObstacle());
                // 是否压线
                boolean crossLine = Optional.ofNullable(queueResult).map(ISCheckActionResult::getActionResult)
                        .map(ISWaitingInQueueResult::getCrossLine).orElse(false);
                boolean awayFromJunction = contextDO
                        .awayFromJunction(trafficLightActionConfigDTO.getMinAwayFromJunction());
                Double distance2NextCrossWalk = hasFrontCrossWalk(
                        contextDO.getPreVehiclePosition(),
                        contextDO.getLocation(), area, trafficLightActionConfigDTO.getSearchRange());
                boolean awayFromJunctionOrCrossWalk = trafficLightActionConfigDTO
                        .getAwayFromJunctionOrCrossWalk(distance2NextCrossWalk, contextDO.getDistanceToJunction());
                // 设置结果
                result.setHasFrontObstacle(hasFrontObstacle);
                result.setCrossLine(crossLine);
                result.setAwayFromJunction(awayFromJunction);
                result.setDistance2CrossWalk(distance2NextCrossWalk);
                // 人行横道和路口
                result.setAwayFromJunctionOrCrossWalk(awayFromJunctionOrCrossWalk);
                if (awayFromJunctionOrCrossWalk
                        // 前方有路口或者有人行横道，且至少有10m,但 （不存在前方障碍物 或者 压了车道线）： 路中央停滞
                        && (!hasFrontObstacle || crossLine)
                ) {
                    return ISCheckActionResult.<ISTrafficLightResult>builder()
                            .categoryEnum(ISCheckCategoryEnum.IN_MIDDLE_ROAD).actionResult(result)
                            .build();
                }
                // （有障碍物 且 非压线 ）或者 无障碍物数据
                return ISCheckActionResult.<ISTrafficLightResult>builder().categoryEnum(ISCheckCategoryEnum.RED_LIGHT)
                        .actionResult(result)
                        .build();
            }
            case GREEN:
                // 如果是绿灯，按照路口检出
                return ISCheckActionResult.<ISTrafficLightResult>builder().categoryEnum(ISCheckCategoryEnum.IN_JUNCTION)
                        .actionResult(ISTrafficLightResult.builder().nowTrafficLightType(trafficLightTypeEnum).build())
                        .build();
            default:
                // 其他灯，无法确认
                return ISCheckActionResult.empty();
        }
    }

    /**
     * 看下前方是否有人行横道
     * @param preVehiclePosition
     * @param location
     * @return
     */
    private Double hasFrontCrossWalk(VehicleInQueuePositionDTO preVehiclePosition, PositionDO location, String area,
            Double distance) {
        if (preVehiclePosition == null || location == null || location.invalid()) {
            return -1D;
        }
        // 找前方的distance范围内的
        // 计算路口的最近距离
        return hdMapAdapter.searchNearby(SearchNearbyRequestVTO.builder()
                        // 区域
                        .area(area)
                        // 路口范围
                        .hdMapEnum(HdMapEnum.OBJECT)
                        // 人行横道类型
                        .restrictType(Lists.newArrayList(HdMapElementTypeEnum.CROSSWALK.getValue()))
                        // 车辆定位
                        .positionDO(location)
                        // 检索范围
                        .range(distance)
                        .build()).stream()
                // 包含
                .filter(hdMapElementGeoDO -> Objects.nonNull(hdMapElementGeoDO.getPolygonDO()))
                .map(hdMapElementGeoDO -> GeoToolsUtil.pointToLineSegmentDistance(location,
                        hdMapElementGeoDO.getPolygonDO().getPoints()))
                // 排序，取最小的一个
                .min(Comparator.naturalOrder()).orElse(-1D);
    }
}
