package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventResultDTO;

/**
 * 领域事件处理服务
 */
public interface DomainEventService {

    /**
     * 领域事件处理 处理的领域实体
     *
     * @return
     */
    DomainEventResultDTO process(DomainEventChangeDTO<?> eventDTO, Boolean async);

}
