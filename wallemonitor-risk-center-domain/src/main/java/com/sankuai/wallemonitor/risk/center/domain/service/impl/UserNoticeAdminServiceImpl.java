package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.sankuai.wallemonitor.risk.center.domain.service.UserNoticeAdminService;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.UserNoticeReadRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.UserNoticeReadRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.UserNoticeReadRecordDOQueryParamDTO;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UserNoticeAdminServiceImpl implements UserNoticeAdminService {

    @Resource
    private UserNoticeReadRecordRepository userNoticeReadRecordRepository;


    /**
     * 插入用户通知阅读记录
     *
     * @param userNoticeReadRecordDO 用户通知阅读记录对象
     * @return 插入记录的行数
     */
    @Override
    public void insertUserNoticeReadRecord(UserNoticeReadRecordDO userNoticeReadRecordDO) {
        userNoticeReadRecordRepository.save(userNoticeReadRecordDO);
    }

    /**
     * 根据用户ID查询通知版本ID
     *
     * @param userId 用户ID
     * @return 通知版本ID
     */
    @Override
    public Long queryLatestNoticeVersionByUserId(String userId) {
        UserNoticeReadRecordDOQueryParamDTO paramDTO = UserNoticeReadRecordDOQueryParamDTO.builder().userId(userId)
                .confirm(true).orderByVersionId(OrderEnum.DESC).build();
        List<UserNoticeReadRecordDO> recordList = userNoticeReadRecordRepository.queryByParam(paramDTO);
        log.info("userId = {}, recordList = {}", userId, recordList);
        if (CollectionUtils.isNotEmpty(recordList)) {
            return recordList.get(0).getVersionId();
        }
        return null;
    }

    /**
     * 根据用户ID和通知版本ID查询用户通知阅读记录，然后更新确认状态
     *
     * @param userId    用户ID
     * @param versionId 通知版本ID
     */
    @Override
    public void queryThenUpdateUserNotice(String userId, Long versionId) {
        // 查询
        UserNoticeReadRecordDOQueryParamDTO paramDTO = UserNoticeReadRecordDOQueryParamDTO.builder().userId(userId)
                .confirm(false).versionId(versionId).build();
        List<UserNoticeReadRecordDO> recordList = userNoticeReadRecordRepository.queryByParam(paramDTO);
        log.info("userId = {},versionId = {}, recordList = {}", userId, versionId, recordList);
        CheckUtil.isNotEmpty(recordList, String.format("确认失败,用户未阅读%s版本须知", versionId));

        // 批量更新方法
        for (UserNoticeReadRecordDO recordDO : recordList) {
            recordDO.setConfirm(true);
        }
        userNoticeReadRecordRepository.batchSave(recordList);
    }
}
