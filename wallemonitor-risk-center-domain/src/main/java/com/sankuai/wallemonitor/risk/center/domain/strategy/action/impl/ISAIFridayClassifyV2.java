package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.google.common.collect.Lists;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.FridayOpenAiAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.SFTFridayVideoClassifyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.FridayVerifyResultVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.SFTFridayVerifyRiskResultVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.SFTFridayVerifyRiskResultVTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.IntStream;

@Component
@Slf4j
public class ISAIFridayClassifyV2 implements ISCheckAction<SFTFridayVerifyRiskResultVTO> {

    @Resource
    private FridayOpenAiAdapter fridayOpenAiAdapter;

    @ConfigValue(LionKeyConstant.SFT_FRIDAY_VIDEO_CLASSIFY)
    private SFTFridayVideoClassifyConfigDTO sftFridayVideoClassifyConfigDTO;

    // 常量定义
    private static final String YES_VALUE = "是";
    private static final String NO_VALUE = "否";
    private static final String DEFAULT_TAKEOVER_VALUE = YES_VALUE;


    public static final String SFT_PARSE_REG = "类型\\s*:\\s*(.*?),\\s*是否需要接管\\s*:\\s*(.*?)(?=\\s|$)";

    private static final Pattern EXTRACT_MODEL_RESULT_PATTERN = Pattern.compile(SFT_PARSE_REG);



    private static final Map<String, ISCheckCategoryEnum> ALIAS_2_CATEGORY = new HashMap<String, ISCheckCategoryEnum>() {{
        put("等红灯", ISCheckCategoryEnum.RED_LIGHT);
        put("车辆顶牛", ISCheckCategoryEnum.CONFLICT_WITH_PASSAGER);
        put("静态车辆绕行", ISCheckCategoryEnum.STOP_BY_INACTIVE_VEHICLE);
        put("路中央停滞", ISCheckCategoryEnum.IN_MIDDLE_ROAD);
        put("逆行停滞", ISCheckCategoryEnum.OPPSITE_ROAD_DIRECTION);
        put("排队通行", ISCheckCategoryEnum.WAITING_FRONT_PASSAGER);
        put("施工区域停滞", ISCheckCategoryEnum.IN_VALID_DRIVING_AREA);
        put("障碍物堵路", ISCheckCategoryEnum.STOP_BY_OBSTACLE);
    }};



    @Override
    public ISCheckActionResult<SFTFridayVerifyRiskResultVTO> execute(ISCheckActionContext actionContext) {
        RiskCheckingQueueItemDO item = actionContext.getItem();
        if (item == null) {
            // 没有预检队列信息，直接返回
            return ISCheckActionResult.empty();
        }
        // 停滞开始时间
        Date startTime = Optional.ofNullable(item.getOccurTime()).orElse(new Date());

        // 车架号
        String vin = Optional.ofNullable(actionContext.getVehicleRunTimeContext())
                .map(VehicleRuntimeInfoContextDO::getVin).orElse(null);

        if (StringUtils.isEmpty(vin)) {
            return ISCheckActionResult.empty();
        }

        FridayModelParamVTO fridayModelParamVTO = FridayModelParamVTO.builder()
                .modelName(sftFridayVideoClassifyConfigDTO.getModelName())
                .appId(sftFridayVideoClassifyConfigDTO.getAppId())
                .timeout(sftFridayVideoClassifyConfigDTO.getTimeout())
                .occurTime(startTime)
                .vin(vin)
                .build();

        SFTFridayVerifyRiskResultVTO sftFridayVerifyRiskResultVTO = verifySFTResult(fridayModelParamVTO);

        ISCheckCategoryEnum isCheckCategoryEnum = sftFridayVerifyRiskResultVTO.getCheckCategory();

        if (sftFridayVerifyRiskResultVTO.getCheckCategory() == null) {
            // 没有CheckCategory ,返回空检查结果
            return ISCheckActionResult.empty();
        }

        return ISCheckActionResult.<SFTFridayVerifyRiskResultVTO>builder()
                .categoryEnum(isCheckCategoryEnum).actionResult(sftFridayVerifyRiskResultVTO).build();
    }

    private SFTFridayVerifyRiskResultVTO verifySFTResult(FridayModelParamVTO requestDTO) {
        CheckUtil.isNotNull(requestDTO, "请求参数不能为空");
        String vin = requestDTO.getVin();
        Date occurTime = requestDTO.getOccurTime();
        Date checkStartTime = new Date();

        // 构造信息体
        List<FridayOpenAiAdapter.ReqMessage> messages = new ArrayList<>();

        // 添加系统信息
        FridayOpenAiAdapter.Content systemContent = FridayOpenAiAdapter.Content.builder().type("text").text(sftFridayVideoClassifyConfigDTO.getSystemPrompt()).build();
        messages.add(FridayOpenAiAdapter.ReqMessage.builder().role("system").content(Lists.newArrayList(systemContent)).build());

        // 添加用户信息
        List<FridayOpenAiAdapter.Content> userContentList = new ArrayList<>();
        FridayOpenAiAdapter.Content userPromptText = FridayOpenAiAdapter.Content.builder().type("text").text(sftFridayVideoClassifyConfigDTO.getUserPrompt()).build();
        userContentList.add(userPromptText);

        // 添加图像信息
        List<String> imageUrls = fridayOpenAiAdapter.buildImageContext(userContentList, vin, occurTime);
        messages.add(FridayOpenAiAdapter.ReqMessage.builder().role("user").content(userContentList).build());

        // 构建OpenAI请求
        FridayOpenAiAdapter.OpenAiReqDTO openAIReqDTO = fridayOpenAiAdapter.buildOpenAiAnswerReqDTO(messages, requestDTO.getModelName());

        // 获取OpenAI结果
        List<SFTFridayVerifyRiskResultVTO> goodPredict = Lists.newArrayList();
        List<SFTFridayVerifyRiskResultVTO> badPredict = Lists.newArrayList();

        SFTFridayVerifyRiskResultVTO finalPredict = SFTFridayVerifyRiskResultVTO.builder().checkStartTime(new Date()).build();

        IntStream.range(0, sftFridayVideoClassifyConfigDTO.getVotingTimes()).parallel().forEach(x -> {
            FridayOpenAiAdapter.OpenAiRespDTO openAIAnswer = fridayOpenAiAdapter.getOpenAiAnswer(openAIReqDTO, requestDTO.getAppId(), requestDTO.getTimeout());
            // 构造返回值
            SFTFridayVerifyRiskResultVTO sftFridayVerifyRiskResultVTO = SFTFridayVerifyRiskResultVTO.builder()
                    .checkStartTime(checkStartTime)
                    .imageUrls(imageUrls).build();

            buildSFTFridayVerifyResult(sftFridayVerifyRiskResultVTO, openAIAnswer);
            log.info("stf Friday , request times = {}, imageUrls : {}, result = {} ", x, imageUrls, sftFridayVerifyRiskResultVTO);
            String firstCategory = Optional.ofNullable(sftFridayVerifyRiskResultVTO
                    .getCheckCategory()).map(ISCheckCategoryEnum::getCategory).orElse(null);

            if (ISCheckCategoryEnum.GOOD_OTHER.getCategory().equals(firstCategory)) {
                goodPredict.add(sftFridayVerifyRiskResultVTO);
            } else if (ISCheckCategoryEnum.BAD_OTHER.getCategory().equals(firstCategory)) {
                badPredict.add(sftFridayVerifyRiskResultVTO);
            }
        });

        // 多轮投票后，good和bad都为空。或者都不为空，返回空的结果
        if (CollectionUtils.isEmpty(goodPredict) == CollectionUtils.isEmpty(badPredict)) {
            return finalPredict;
        }

        // 一个为空，一个不为空,返回不为空的结果
        finalPredict = goodPredict.size() > badPredict.size() ? goodPredict.get(0) : badPredict.get(0);
        finalPredict.setCheckEndTime(new Date());

        return finalPredict;

    }

    /**
     * 构建 sft friday verify result
     * @param sftFridayVerifyRiskResultVTO Friday result
     * @param openAiRespDTO openAI response
     *
     * */
    public void buildSFTFridayVerifyResult(SFTFridayVerifyRiskResultVTO sftFridayVerifyRiskResultVTO, FridayOpenAiAdapter.OpenAiRespDTO openAiRespDTO) {
        if (openAiRespDTO == null) {
            log.debug("OpenAI响应为空，直接返回");
            return;
        }

        if (CollectionUtils.isEmpty(openAiRespDTO.getChoices())) {
            log.debug("OpenAI响应无Choice，直接返回");
            return;
        }

        String content = extractContentFromResponse(openAiRespDTO);
        if (StringUtils.isEmpty(content)) {
            log.debug("模型未给出判断结果，直接返回");
            return;
        }

        SFTParseResult parseResult = parseSFTContent(content);
        if (parseResult == null) {
            log.warn("无法解析SFT内容: {}", content);
            return;
        }

        setSFTResult(sftFridayVerifyRiskResultVTO, parseResult);
    }

    /**
     * 从OpenAI响应中提取内容
     *
     * @param openAiRespDTO OpenAI响应
     * @return 提取的内容
     */
    private String extractContentFromResponse(FridayOpenAiAdapter.OpenAiRespDTO openAiRespDTO) {
        return openAiRespDTO.getChoices().stream()
                .findFirst()
                .map(FridayOpenAiAdapter.Choice::getMessage)
                .filter(Objects::nonNull)
                .map(FridayOpenAiAdapter.RespMessage::getContent)
                .orElse(null);
    }

    /**
     * SFT解析结果
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SFTParseResult {
        private String category;
        private Boolean needTakeOver;
    }

    /**
     * 解析SFT内容
     *
     * @param content 待解析的内容
     * @return 解析结果
     */
    private SFTParseResult parseSFTContent(String content) {
        Matcher matcher = EXTRACT_MODEL_RESULT_PATTERN.matcher(content);

        if (!matcher.find()) {
            return null;
        }

        String category = matcher.group(1).trim();
        String takeOverStr = matcher.group(2).trim();

        Boolean needTakeOver = parseTakeOverValue(takeOverStr);

        return SFTParseResult.builder()
                .category(category)
                .needTakeOver(needTakeOver)
                .build();
    }

    /**
     * 设置SFT结果
     *
     * @param sftFridayVerifyRiskResultVTO 结果对象
     * @param parseResult 解析结果
     */
    private void setSFTResult(SFTFridayVerifyRiskResultVTO sftFridayVerifyRiskResultVTO, SFTParseResult parseResult) {
        sftFridayVerifyRiskResultVTO.setOriginCategory(parseResult.getCategory());
        sftFridayVerifyRiskResultVTO.setShouldBeTakeOver(parseResult.getNeedTakeOver());

        // 尝试根据分类名称获取枚举
        ISCheckCategoryEnum categoryEnum = ALIAS_2_CATEGORY.get(parseResult.getCategory());

        if (categoryEnum != null) {
            // 找到了对应枚举
            sftFridayVerifyRiskResultVTO.setCheckCategory(categoryEnum);
            return;
        }

        // 分类找不到枚举，可能是模型有幻觉，根据是否需要接管设置默认分类
        setDefaultCategoryByTakeOver(sftFridayVerifyRiskResultVTO, parseResult.getNeedTakeOver());
    }

    /**
     * 根据是否需要接管设置默认分类
     *
     * @param sftFridayVerifyRiskResultVTO 结果对象
     * @param needTakeOver 是否需要接管
     */
    private void setDefaultCategoryByTakeOver(SFTFridayVerifyRiskResultVTO sftFridayVerifyRiskResultVTO, Boolean needTakeOver) {
        FridayOpenAiAdapter.SFTParseConfig config = getSFTParseConfig();

        if (needTakeOver) {
            // 需要接管，设置成Good-其他
            sftFridayVerifyRiskResultVTO.setCheckCategory(config.getDefaultTakeOverCategory());
            log.debug("模型输出未知分类但需要接管，设置为默认分类: {}", config.getDefaultTakeOverCategory());
        } else {
            // 否则，设置成BAD-OTHER
            sftFridayVerifyRiskResultVTO.setCheckCategory(config.getDefaultNotTakeOverCategory());
            log.debug("模型输出未知分类且不需要接管，设置为默认分类: {}", config.getDefaultNotTakeOverCategory());
        }
    }


    /**
     * 解析是否需要接管的值
     *
     * @param takeOverStr 接管字符串
     * @return 是否需要接管
     */
    private Boolean parseTakeOverValue(String takeOverStr) {
        FridayOpenAiAdapter.SFTParseConfig config = getSFTParseConfig();

        if (config.getTakeOverValue().equals(takeOverStr)) {
            return true;
        } else if (config.getNotTakeOverValue().equals(takeOverStr)) {
            return false;
        }

        // 默认值处理
        log.warn("无法识别的接管值: {}, 使用默认值: {}", takeOverStr, sftFridayVideoClassifyConfigDTO.getDefaultTakeOverValue());
        return sftFridayVideoClassifyConfigDTO.getDefaultTakeOverValue().equals(config.getTakeOverValue());
    }

    /**
     * 获取SFT解析配置
     *
     * @return 解析配置
     */
    private FridayOpenAiAdapter.SFTParseConfig getSFTParseConfig() {
        return FridayOpenAiAdapter.SFTParseConfig.builder()
                .takeOverValue(sftFridayVideoClassifyConfigDTO.getTakeOverValue())
                .notTakeOverValue(sftFridayVideoClassifyConfigDTO.getNotTakeOverValue())
                .defaultTakeOverCategory(sftFridayVideoClassifyConfigDTO.getDefaultTakeOverCategory())
                .defaultNotTakeOverCategory(sftFridayVideoClassifyConfigDTO.getDefaultNotTakeOverCategory())
                .build();
    }

}
