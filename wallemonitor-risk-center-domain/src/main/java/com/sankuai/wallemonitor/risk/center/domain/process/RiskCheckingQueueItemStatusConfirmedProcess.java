package com.sankuai.wallemonitor.risk.center.domain.process;

import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.RISK_CHECKING_QUEUE_STATUS_ENTRY;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCheckQueueQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;


/**
 * 处理预检队列项「已确认为风险」
 */
@Slf4j
@Component
public class RiskCheckingQueueItemStatusConfirmedProcess implements
        DomainEventProcess {

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private LionConfigRepository lionConfigRepository;

    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(RISK_CHECKING_QUEUE_STATUS_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != RiskCheckingQueueItemDO.class) {
            return true;
        }

        // 从事件DTO中获取状态变更的风险案例列表，过滤出状态为 确认风险 的事件
        DomainEventChangeDTO<RiskCheckingQueueItemDO> typedDomainEvent = (DomainEventChangeDTO<RiskCheckingQueueItemDO>) eventDTO;
        List<RiskCheckingQueueItemDO> itemList = typedDomainEvent.getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                .stream()
                .filter(item -> RiskQueueStatusEnum.isConfirmed(item.getStatus()))
                .collect(Collectors.toList());
        log.info("itemList: {}", JacksonUtils.to(itemList));
        if (CollectionUtils.isEmpty(itemList)) {
            return true;
        }
        return handleQueueItemConfirmed(itemList);
    }

    /**
     * 处理风险事件
     *
     * @param itemList
     * @return
     */
    private boolean handleQueueItemConfirmed(List<RiskCheckingQueueItemDO> itemList) {
        // 如果没有开启预检队列 不做任何动作
        if (!lionConfigRepository.enableImproperStrandingCheckingQueue()) {
            return true;
        }
        lockUtils.batchLockCanWait(LockKeyPreUtil.buildLockKeys(itemList), () -> {
            // 这里是异步处理，所以不信任变更的实体的桩体
            List<String> caseIdList = itemList.stream().map(RiskCheckingQueueItemDO::getTmpCaseId)
                    .collect(Collectors.toList());
            // 重新获取最新的数据库数据
            List<RiskCheckingQueueItemDO> riskCheckingQueueItemList = riskCheckQueueRepository
                    .queryByParam(RiskCheckQueueQueryParam.builder().tmpCaseIdList(caseIdList).build()).stream()
                    // 如果不是已经确认的，不可以做转换
                    .filter(checkingQueueItemDO -> RiskQueueStatusEnum.isConfirmed(checkingQueueItemDO.getStatus()))
                    .collect(Collectors.toList());;
            if (CollectionUtils.isEmpty(riskCheckingQueueItemList)) {
                return;
            }
            // 查询已经存在的风险
            Set<String> existsCaseIdSet = riskCaseRepository
                    .queryByParam(RiskCaseDOQueryParamDTO.builder().caseIdList(caseIdList).build()).stream()
                    .map(RiskCaseDO::getCaseId).collect(Collectors.toSet());
            // 过滤出未转换的item，转换过，不可以再进行转换
            List<RiskCheckingQueueItemDO> nonTransferredQueueItemList = riskCheckingQueueItemList.stream()
                    .filter(item -> !existsCaseIdSet.contains(item.getTmpCaseId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(nonTransferredQueueItemList)) {
                return;
            }
            log.info("nonTransferredQueueItemList: {}", JacksonUtils.to(nonTransferredQueueItemList));
            riskCaseOperateService.transRiskQueueCaseAsTrueRiskCase(nonTransferredQueueItemList);
        });
        return true;
    }
}
