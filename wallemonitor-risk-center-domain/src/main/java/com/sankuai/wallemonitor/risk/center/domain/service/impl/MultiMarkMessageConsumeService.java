package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskMarkService;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.infra.comsumprocess.CommonMessageConsumer;
import com.sankuai.wallemonitor.risk.center.infra.dto.MarkMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskMarkRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 标注消息消费
 */
@Slf4j
@Component
public class MultiMarkMessageConsumeService implements CommonMessageConsumer {

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskMarkService riskMarkService;

    @Resource
    private RiskCheckQueueRepository riskCheckQueueRepository;

    @Resource
    private RiskMarkRepository riskMarkRepository;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private VehicleRuntimeInfoContextRepository vehicleRuntimeInfoContextRepository;


    /**
     * 处理消费标注
     *
     * @return
     */
    @Override
    public ConsumeStatus consume(String message) {
        try {
            MarkMessageDTO markMessageDTO = JacksonUtils.from(message, MarkMessageDTO.class);
            if (markMessageDTO == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            RiskCheckingQueueItemDO itemDO = riskMarkRepository.getMarkItem(markMessageDTO.getItemCaseId(),
                    markMessageDTO.getVersion());
            if (itemDO == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            RiskCaseDO strandingCase = riskCaseRepository.getByCaseId(itemDO.getEventId());
            if (strandingCase == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            lockUtils.batchLockCanWait(
                    LockKeyPreUtil.buildMultiMarkWithEventId(strandingCase.getEventId(), markMessageDTO.getVersion()),
                    10,
                    TimeUnit.SECONDS, () -> {
                        this.handleCalcMarkResult(markMessageDTO.getItemCaseId(), markMessageDTO.getDynamicRecheck(),
                                markMessageDTO.getVersion());
                    });
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (UnableGetLockException e) {
            log.warn("未获取到锁", e);
            return ConsumeStatus.CONSUME_FAILURE;

        } catch (Exception e) {
            log.error("自动标注消息消费异常", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

    }

    private void handleCalcMarkResult(String tmpCaseId, Boolean fromDynamicReCheck, String version) {
        // 取自动标注的item
        RiskCheckingQueueItemDO itemDO = riskMarkRepository.getMarkItem(tmpCaseId, version);
        if (itemDO == null) {
            // 如果已经被删除， 无需做处理
            return;
        }
        // 取db里面的风险
        RiskCaseDO riskCaseDO = riskCaseRepository.getByCaseId(itemDO.getEventId());
        if (riskCaseDO != null && RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus())) {
            // 如果收到消息时，风险或者item，已经被取消或者被解除，直接更新markInfo即可
            this.handleMarkAndDelete(itemDO, version);
            return;
        }
        // 做标注
        ISCheckActionResult markResult = riskMarkService.calcRiskMarkResult(itemDO, version);
        if (markResult == null) {
            // 检测出现未知异常
            log.error(JacksonUtils.to(itemDO), new SystemException("自动标注未获取到结果"));
            return;
        }
        RiskCheckResultDO lastCheckResult = itemDO.getCheckResult();
        // 更新结果
        riskMarkService.updateOneRiskCheckingQueueItem(itemDO, markResult);
        // 取配置
        RiskAutoCheckConfigDTO configDTO = lionConfigRepository.getRiskAutoMarkConfigByVersion(version);
        if (RiskQueueStatusEnum.isTerminatedStatus(itemDO.getStatus())) {
            // 以终态检出
            if (Boolean.TRUE.equals(markResult.isDynamicReCheck()) && Boolean.FALSE.equals(fromDynamicReCheck)) {
                // 如果如果已经终态,且 需要短延时重检的 且 不是从短延时触发的,本轮结果不可信，重置轮次
                riskMarkService.resetQueueItemForDynamicCheck(itemDO, lastCheckResult);
                // 更新
                riskMarkRepository.saveMarkItem(Collections.singletonList(itemDO), version);
                // 动态延时
                riskMarkService.sendMarkMessage(itemDO,
                        configDTO.getDynamicCheckDelayTime(markResult.getActionName(), markResult.getCategoryEnum(),
                                vehicleRuntimeInfoContextRepository.getFullByVin(itemDO.getVin())),
                        true, version);
            } else {
                // 否则，进行设置
                this.handleMarkAndDelete(itemDO, version);
            }
        } else {
            // 未以终态检出
            if (Boolean.FALSE.equals(fromDynamicReCheck) || Boolean.TRUE.equals(fromDynamicReCheck)
                            && Boolean.TRUE.equals(markResult.isWithSpeed())) {
                // 如果不是动态轮次进来的 或者【是动态轮次进入，但是有速度】
                riskMarkRepository.saveMarkItem(Collections.singletonList(itemDO), version);
                // 进入下一轮
                riskMarkService.triggerAutoMark(Collections.singletonList(itemDO), version);
            } else {
                // 否则，进行设置
                this.handleMarkAndDelete(itemDO, version);
            }
        }
    }

    /**
     * 做mark
     *
     * @param itemDO
     */
    private void handleMarkAndDelete(RiskCheckingQueueItemDO itemDO, String version) {
        // 标注
        riskMarkService.markCaseByCheckItem(itemDO, version);
        // 删除
        riskMarkRepository.deleteMarkItem(Collections.singletonList(itemDO), version);
    }

}
