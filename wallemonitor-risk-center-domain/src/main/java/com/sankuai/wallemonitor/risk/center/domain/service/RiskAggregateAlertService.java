package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import java.util.List;

/**
 * 聚合告警服务接口
 */
public interface RiskAggregateAlertService {

    /**
     * 处理风险事件聚合告警
     *
     * @param riskCaseList 风险事件ID列表
     */
    void processAggregateAlert(List<RiskCaseDO> riskCaseList);


    /**
     * 获取告警相关的UID列表
     * 用于@人功能
     */
    List<Long> getUidListForAlert(List<RiskCaseDO> riskCaseList, AggregateAlertContext context);
}