package com.sankuai.wallemonitor.risk.center.domain.result;

import com.sankuai.wallemonitor.risk.center.domain.strategy.algorithm.RiskFeatureDataDTO;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ISRandomForestClassifyResult implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 是否风险
     */
    private Boolean isRisky;
    /**
     * 风险概率
     */
    private Double riskProbability;

    /**
     * 特征信息
     */
    private RiskFeatureDataDTO featureDataDTO;
}
