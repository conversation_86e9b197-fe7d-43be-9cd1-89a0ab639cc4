package com.sankuai.wallemonitor.risk.center.domain.strategy;

import com.sankuai.walleeve.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.api.response.vo.VehicleRiskStatusVO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CloudCursorAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.CloudCursorResourceRequest;
import com.sankuai.wallemonitor.risk.center.infra.annotation.MessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseCallMrmFilterDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskCaseReleaseMrmMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallCloudCursorTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component("beaconTowerImproperStrandingHandleStrategy")
@Slf4j
public class StatusMonitorStandStillHandleStrategy extends HandleStrategy {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;

    @MessageProducer(topic = MessageTopicEnum.WALLEMONITOR_RISK_VEHICLE_STATUS_MESSAGE, appKey = "com.sankuai.carosscan.realtimeinfo")
    private CommonMessageProducer<RiskCaseReleaseMrmMessageDTO> riskCaseMessageProducer;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private CloudCursorAdapter cloudCursorAdapter;


    @Override
    public void process(CallMrmStrategyConfigDTO callMrmStrategyConfigDTO, RiskCaseCallMrmFilterDTO filterDTO,
            RiskCaseDO riskCaseDO) {
        // 判断车辆是否在灰度中，如果不是则直接返回，不进行后续处理。
        if (!lionConfigRepository.isInMrmCallVinList(filterDTO.getVin())) {
            return;
        }
        log.info("StatusMonitorStandStillHandleStrategy process, filterDTO = {}", filterDTO);

        Boolean isMrmCalled;
        // 根据用车目的进行分流
        // 业务车
        if (lionConfigRepository.isBusinessVehicleByPurpose(filterDTO.getPurpose())) {
            log.info("StatusMonitorStandStillHandleStrategy businessVehicle call mrm, riskCaseDO = {}", riskCaseDO);
            if (!lionConfigRepository.isBusinessVehicleNeedCallMrm()) {
                // 开关不打开的时候只更新,不发消息
                updateRiskCase(riskCaseDO);
                isMrmCalled = false;
            } else {
                isMrmCalled = true;
            }
        } else {
            // 路测车
            Map<String, Object> filterDTOMap = ReflectUtils.getNonNullFieldAndValue(filterDTO);
            Boolean isNeedFilter = callMrmStrategyConfigDTO.isNeedFilter(filterDTOMap, riskCaseDO.getCaseId());
            if (isNeedFilter) {
                isMrmCalled = false;
            } else {
                log.info("StatusMonitorStandStillHandleStrategy roadTestVehicle call mrm, riskCaseDO = {}", riskCaseDO);
                isMrmCalled = true;
            }

        }
        // 更新风险工单并发送消息
        if (isMrmCalled) {
            updateRiskCaseAndSendMsg(callMrmStrategyConfigDTO, filterDTO, riskCaseDO);
        }
    }

    @Override
    public void process(String vin, ReleaseMrmStrategyConfigDTO releaseMrmStrategyConfigDTO, RiskCaseDO riskCaseDO) {
        // 判断车辆是否在灰度中，如果不是则直接返回，不进行后续处理。
        if (!lionConfigRepository.isInMrmCallVinList(vin)) {
            return;
        }
        // 更新坐席呼叫状态取消状态
        riskCaseDO.setMrmCalled(RiskCaseMrmCalledStatusEnum.CANCEL);
        riskCaseRepository.save(riskCaseDO);

        // 更新取消坐席呼叫时间
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = riskCaseVehicleRelationRepository.getByEventIdAndVin(
                riskCaseDO.getEventId(), vin);
        if (Objects.nonNull(riskCaseVehicleRelationDO)) {
            riskCaseVehicleRelationDO.setCancelSeatTime(new Date());
            riskCaseVehicleRelationRepository.save(riskCaseVehicleRelationDO);
            //根据 callMrmReason字段确定取消方式
            if (StringUtils.isBlank(riskCaseVehicleRelationDO.getCallMrmReason())) {
                // 使用mq消息发送解除信号消息
                RiskCaseReleaseMrmMessageDTO messageDTO = RiskCaseReleaseMrmMessageDTO.builder()
                        .vin(vin)
                        .endTimestamp(DatetimeUtil.getTimeInSeconds(riskCaseDO.getCloseTime()))
                        .key(releaseMrmStrategyConfigDTO.getKey())
                        .alarmTimestamp(DatetimeUtil.getTimeInSeconds(riskCaseDO.getRecallTime()))
                        .issueCode(releaseMrmStrategyConfigDTO.getIssueCode()).build();
                riskCaseMessageProducer.sendCustomMessage(messageDTO);
            } else {
                // 使用http接口发送取消呼叫信号
                CloudCursorResourceRequest request = CloudCursorResourceRequest.builder()
                        .action(CallCloudCursorTypeEnum.CANCEL.name().toLowerCase())
                        .reason(Integer.parseInt(riskCaseVehicleRelationDO.getCallMrmReason()))
                        .timestamp(System.currentTimeMillis())
                        .vin(vin)
                        .source(CommonConstant.BEACON_TOWER_CALL_CLOUD_CONTROL_SOURCE)
                        .needCancelCommand(true)
                        .build();

                cloudCursorAdapter.callCloudCursor(request);
            }
        } else {
            log.error(String.format("riskCaseVehicleRelationRepository.getByEventIdAndVin is null, eventId : %s",
                    riskCaseDO.getEventId()), new IllegalArgumentException());
        }
    }

    @Override
    public void process(CallSecuritySystemStrategyConfigDTO callSecuritySystemStrategyConfigDTO, RiskCaseCallMrmFilterDTO filterDTO, RiskCaseDO riskCaseDO) {
        if (callSecuritySystemStrategyConfigDTO == null) {
            log.info("vin = {}, source = {}, code = {}, 未配置呼叫保障系统云控",
                    filterDTO.getVin(), riskCaseDO.getSource().getCode(), riskCaseDO.getType().getCode());
            return;
        }

        log.info("StatusMonitorStandStillHandleStrategy.CallSecuritySystemStrategy process, filterDTO = {}", filterDTO);
        try {
            Map<String, Object> filterDTOMap = ReflectUtils.getNonNullFieldAndValue(filterDTO);
            if (!callSecuritySystemStrategyConfigDTO.isNeedFilter(filterDTOMap, riskCaseDO.getCaseId())) {
                riskCaseOperateService.updateRiskCaseAndCallCloudCursor(callSecuritySystemStrategyConfigDTO, filterDTO,
                        riskCaseDO);
            }
        } catch (Exception e) {
            log.error("StatusMonitorStandStillHandleStrategy.CallSecuritySystemStrategy error", e);
        }
    }

    @Override
    public void buildVehicleRiskStatus(String vin, RiskCaseInfoDTO riskCaseInfoDTO,
            VehicleRiskStatusVO vehicleRiskStatusVO) {
        // 判断是否处于灰度中，如果不是则设置车辆风险状态为false。
        if (!lionConfigRepository.isInMrmCallVinList(vin)) {
            vehicleRiskStatusVO.setIsImproperStranding(false);
        }
        if (Objects.isNull(riskCaseInfoDTO) || Objects.isNull(riskCaseInfoDTO.getStatus())) {
            vehicleRiskStatusVO.setIsImproperStranding(false);
        } else {
            vehicleRiskStatusVO.setIsImproperStranding(
                    !RiskCaseStatusEnum.isTerminal(RiskCaseStatusEnum.findByValue(riskCaseInfoDTO.getStatus())));
        }
    }

    @Override
    public void process(String vin, LionConfigRepositoryImpl.ReleaseSecuritySystemConfigDTO releaseSecuritySystemConfigDTO, RiskCaseDO riskCaseDO) {
        // 更新坐席呼叫状态取消状态
        riskCaseDO.setMrmCalled(RiskCaseMrmCalledStatusEnum.CANCEL);
        riskCaseRepository.save(riskCaseDO);

        // 更新取消坐席呼叫时间
        RiskCaseVehicleRelationDO riskCaseVehicleRelationDO = riskCaseVehicleRelationRepository.getByEventIdAndVin(
                riskCaseDO.getEventId(), vin);
        if (Objects.nonNull(riskCaseVehicleRelationDO)) {
            riskCaseVehicleRelationDO.setCancelSeatTime(new Date());
            riskCaseVehicleRelationRepository.save(riskCaseVehicleRelationDO);
        } else {
            log.error(String.format("riskCaseVehicleRelationRepository.getByEventIdAndVin is null, eventId : %s",
                    riskCaseDO.getEventId()), new IllegalArgumentException());
        }


        // 如果打开了保障系统通讯开关，向云控发送消息
        if(lionConfigRepository.getRiskCaseSecuritySystemCallSwitch()) {
            CloudCursorResourceRequest request = CloudCursorResourceRequest.builder()
                    .action(CallCloudCursorTypeEnum.CANCEL.name().toLowerCase())
                    .reason(releaseSecuritySystemConfigDTO.getReason())
                    .timestamp(System.currentTimeMillis())
                    .vin(vin)
                    .source(releaseSecuritySystemConfigDTO.getRequestCode())
                    .needCancelCommand(true)
                    .build();

            cloudCursorAdapter.callCloudCursor(request);
        }
    }

    /**
     * 更新风险工单并发送消息
     *
     * @param callMrmStrategyConfigDTO
     * @param filterDTO
     * @param riskCaseDO
     */
    private void updateRiskCaseAndSendMsg(CallMrmStrategyConfigDTO callMrmStrategyConfigDTO,
            RiskCaseCallMrmFilterDTO filterDTO,
            RiskCaseDO riskCaseDO) {
        try {
            riskCaseOperateService.updateRiskCaseAndSendMsg(
                    callMrmStrategyConfigDTO, filterDTO, riskCaseDO);
        } catch (Exception e) {
            log.error("StatusMonitorStandStillHandleStrategy error", e);
        }
    }

    /**
     * 更新风险工单
     *
     * @param riskCaseDO
     */
    private void updateRiskCase(RiskCaseDO riskCaseDO) {
        try {
            riskCaseOperateService.updateRiskCaseMrmCalled(riskCaseDO);
        } catch (Exception e) {
            log.error("updateRiskCase error", e);
        }
    }

}
