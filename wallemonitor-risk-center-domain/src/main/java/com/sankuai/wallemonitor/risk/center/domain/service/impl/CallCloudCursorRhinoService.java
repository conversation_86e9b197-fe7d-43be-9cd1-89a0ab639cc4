package com.sankuai.wallemonitor.risk.center.domain.service.impl;


import com.dianping.cat.Cat;
import com.dianping.rhino.circuit.CircuitBreaker;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CloudCursorAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.CloudCursorResourceRequest;
import com.sankuai.wallemonitor.risk.center.infra.constant.CatMonitorConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.RhinoBreakerConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallCloudCursorTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.RhinoTimeExceedSystemException;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CallCloudCursorRhinoService {

    @Resource
    private CloudCursorAdapter cloudCursorAdapter;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository vehicleRelationRepository;

    /**
     * 全局熔断配置
     */
    private static final CircuitBreaker timesCircuitBreaker = com.dianping.rhino.Rhino
            .newCircuitBreaker(RhinoBreakerConstant.CALL_CLOUD_CURSOR_TIMES, false);

    /**
     * 限流器
     */
    private static final OneLimiter oneLimiter = com.dianping.rhino.Rhino.newOneLimiter();

    /**
     * 呼叫或者取消呼叫云控
     */
    public void callCloudCursor(CallSecuritySystemStrategyConfigDTO callSecuritySystemStrategyConfigDTO,
            String vin,
            String poiName) {
        try {
            Map<String, String> params = new HashMap<>();
            // 业务相关的数据
            params.put("vin", vin);
            params.put("poiName", poiName);
            // 取限流结果
            LimitResult result = oneLimiter.run(RhinoBreakerConstant.CALL_CLOUD_CURSOR_TIMES, params);
            if (result.isPass()) {
                // 未被限流 且 未被熔断，执行正常逻辑
                CloudCursorResourceRequest request = CloudCursorResourceRequest.builder()
                        .action(CallCloudCursorTypeEnum.CALL.name().toLowerCase())
                        .reason(callSecuritySystemStrategyConfigDTO.getReason()).timestamp(System.currentTimeMillis())
                        .vin(vin).source(callSecuritySystemStrategyConfigDTO.getRequestSource()).needCancelCommand(true)
                        .build();
                // 呼叫
                cloudCursorAdapter.callCloudCursor(request);
                // 未被限流的，算做成功
                timesCircuitBreaker.setSuccess();
            } else {
                // 被限流
                if (!timesCircuitBreaker.allowRequest()) {
                    // 已经处于熔断状态,fallback
                    callCloudCursorFallBack(callSecuritySystemStrategyConfigDTO, vin, poiName);
                } else {
                    // 非熔断状态，触发熔断
                    timesCircuitBreaker.setFailed(
                            new RhinoTimeExceedSystemException("触发限流" + String.format("vin=%s&poi=%s&strategy=%s", vin,
                                    poiName, JacksonUtils.to(callSecuritySystemStrategyConfigDTO))));
                }
            }
        } finally {
            // 结束
            timesCircuitBreaker.complete();
        }

    }

    /**
     * 熔断降级后的fallback方法
     */
    public void callCloudCursorFallBack(CallSecuritySystemStrategyConfigDTO callSecuritySystemStrategyConfigDTO,
            String vin,
            String poiName) {
        Cat.logEvent(CatMonitorConstant.CALL_CLOUD_CURSOR_FALLBACK, "fallback", "-1", String.format(
                "vin=%s&poi=%s&strategy=%s", vin,
                poiName, JacksonUtils.to(callSecuritySystemStrategyConfigDTO)));
    }

}
