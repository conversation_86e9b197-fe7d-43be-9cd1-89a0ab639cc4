package com.sankuai.wallemonitor.risk.center.domain.strategy;

import static com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum.CANT_FOUND_ANY;

import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ISCheckActionResult<T> {


    /**
     * 预检出来的最终类型，默认CANT_FOUND_ANY
     */
    @Default
    private ISCheckCategoryEnum categoryEnum = CANT_FOUND_ANY;
    
    @Default
    private Boolean needReCheck = false;

    /**
     * 检测器名称
     */
    private String actionName;

    /**
     * 预检的最终完整结果
     */
    private T actionResult;


    /**
     * 开始预检时间
     */
    @Default
    private Date startCheckTime = new Date();

    /**
     * 预检持续时间
     */
    @Default
    private Long duration = 0L;

    /**
     * caseId
     */
    private String caseId;

    /**
     * 是否是动态轮次
     */
    private boolean dynamicReCheck;

    /**
     * 是否带速检出
     */
    public boolean withSpeed;


    /**
     * 无法确认
     *
     * @param <T>
     * @return
     */
    public static <T> ISCheckActionResult<T> empty() {
        return ISCheckActionResult.<T>builder()
                .categoryEnum(CANT_FOUND_ANY)
                //需要保持
                .build();
    }

    /**
     * 无法确认
     *
     * @param <T>
     * @return
     */
    public static <T> ISCheckActionResult<T> empty(T actionResult) {
        return ISCheckActionResult.<T>builder().categoryEnum(CANT_FOUND_ANY).actionResult(actionResult)
                // 需要保持
                .build();
    }

    public static <T> ISCheckActionResult<T> buildResult(T result, ISCheckCategoryEnum categoryEnum) {
        return ISCheckActionResult.<T>builder().categoryEnum(categoryEnum).actionResult(result).build();
    }

    /**
     * action检出风险
     *
     * @return
     */
    public boolean withRisk() {
        return ISCheckCategoryEnum.isGood(categoryEnum);
    }

    /**
     * action检出无风险
     *
     * @return
     */
    public boolean withoutRisk() {
        return ISCheckCategoryEnum.isBad(categoryEnum);
    }

    /**
     * 无法检出
     *
     * @return
     */
    public boolean cantVerify() {
        return CANT_FOUND_ANY.equals(categoryEnum);
    }

    /**
     * 获取actionName
     *
     * @return
     */
    public String getActionName() {
        return StringUtils.defaultString(actionName, CommonConstant.NONE_ACTION);
    }

    public Long getDuration() {
        return duration;
    }

}
