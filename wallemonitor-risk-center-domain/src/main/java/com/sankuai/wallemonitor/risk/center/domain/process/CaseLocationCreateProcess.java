package com.sankuai.wallemonitor.risk.center.domain.process;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseLocationRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseLocationRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * 风险定位保存
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class CaseLocationCreateProcess implements DomainEventProcess {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private CaseLocationRelationRepository caseLocationRelationRepository;

    @Override
    @ZebraForceMaster
    @OperateEnter(OperateEnterActionEnum.CASE_LOCATION_CREATE)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        if (eventDTO.getDomainClass() != RiskCaseVehicleRelationDO.class) {
            return true;
        }

        DomainEventChangeDTO<RiskCaseVehicleRelationDO> typedDomainEvent = (DomainEventChangeDTO<RiskCaseVehicleRelationDO>)eventDTO;
        List<RiskCaseVehicleRelationDO> intervenedCaseList = typedDomainEvent
                // seatInterventionTime 发生变更
                .getBySingleField(
                        entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(),
                                "seatInterventionTime"))
                // 且不等于初始时间
                .stream().filter((a) -> !DatetimeUtil.isDbZeroTime(a.getSeatInterventionTime()))
                // 只记录停滞类型
                .filter(riskCase -> RiskCaseTypeEnum.STRANDING.equals(riskCase.getType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(intervenedCaseList)) {
            return true;
        }
        // 处理
        return handleLocationUpdate(intervenedCaseList);
    }

    private boolean handleLocationUpdate(List<RiskCaseVehicleRelationDO> intervenedCaseList) {
        try {
            if (CollectionUtils.isEmpty(intervenedCaseList)) {
                return true;
            }
            List<CaseLocationRelationDO> caseLocationRelationDOList = intervenedCaseList.stream()
                    .map(item -> {
                        VehicleInfoDO vehicleInfoDO = item.getVehicleSnapshotInfo();
                        if (Objects.isNull(vehicleInfoDO) || Objects.isNull(vehicleInfoDO.getPosition())) {
                            return null;
                        }
                        CaseLocationRelationDO caseLocationRelationDO = new CaseLocationRelationDO();
                        caseLocationRelationDO.setCaseId(item.getCaseId());
                        caseLocationRelationDO.setLocation(vehicleInfoDO.getPosition());
                        return caseLocationRelationDO;
                    }).collect(Collectors.toList());
            caseLocationRelationRepository.batchSave(caseLocationRelationDOList);
        } catch (Exception e) {
            log.error("CaseLocationCreateProcess handleLocationUpdate error", e);
        }
        return true;
    }

}
