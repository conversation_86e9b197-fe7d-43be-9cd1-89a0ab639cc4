package com.sankuai.wallemonitor.risk.center.domain.process;


import static com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum.HIGH_NEGATIVE_FAST_UPLOAD_PROCESS_MESSAGE_ENTRY;

import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DataPlatformAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.DataPlatformFastUploadRequest;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.HighNegativeUploadAutoCarDataConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class HighNegativeCaseUploadAutoCarDataProcess implements DomainEventProcess {

    @Resource
    private DataPlatformAdapter dataPlatformAdapter;

    @Resource
    private RiskCaseVehicleRelationRepository vehicleRepository;

    @Resource
    private RiskCaseRepository caseRepository;

    @Resource
    private CaseMarkInfoRepository caseMarkInfoRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private LockUtils lockUtils;

    /**
     * 处理领域事件
     *
     * @param eventDTO
     * @throws TException
     */
    @Override
    @ZebraForceMaster
    @OperateEnter(HIGH_NEGATIVE_FAST_UPLOAD_PROCESS_MESSAGE_ENTRY)
    public boolean process(DomainEventChangeDTO<?> eventDTO) throws TException {
        List<String> caseIdList = new ArrayList<>();
        if (eventDTO.getDomainClass().equals(RiskCaseDO.class)) {
            DomainEventChangeDTO<RiskCaseDO> typedDomainEvent = (DomainEventChangeDTO<RiskCaseDO>) eventDTO;
            List<RiskCaseDO> disposeCaseList = typedDomainEvent.getBySingleField(
                            entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                    .stream()
                    .filter(item ->
                            //必须是完结的
                            RiskCaseStatusEnum.isTerminal(item.getStatus())
                                    //必须是高负向的case
                                    && RiskCaseTypeEnum.isHighNegativeRiskType(item.getType())
                    )
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(disposeCaseList)) {
                return true;
            }
            caseIdList = disposeCaseList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        } else if (eventDTO.getDomainClass().equals(CaseMarkInfoDO.class)) {
            DomainEventChangeDTO<CaseMarkInfoDO> typedDomainEvent = (DomainEventChangeDTO<CaseMarkInfoDO>) eventDTO;
            List<CaseMarkInfoDO> goodMarkedCase = typedDomainEvent.getBySingleField(
                            entityFieldChangeRecordDTO -> Objects.equals(entityFieldChangeRecordDTO.getKey(), "status"))
                    .stream()
                    .filter(item ->
                            //必须是GOOD的
                            item.getCategory().equals(ISCheckCategoryEnum.GOOD_OTHER.getCategory())
                    )
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(goodMarkedCase)) {
                return true;
            }
            caseIdList = goodMarkedCase.stream().map(CaseMarkInfoDO::getCaseId)
                    .collect(Collectors.toList());
        }
        return handleFastUpload(caseIdList);

    }

    /**
     * 处理快速回传数据
     *
     * @return
     */
    private boolean handleFastUpload(List<String> caseIdList) {
        HighNegativeUploadAutoCarDataConfig config = lionConfigRepository.getHighNegativeFastUploadAutocarDataConfig();
        if (CollectionUtils.isEmpty(caseIdList) || Objects.isNull(config) || !config.getEnable()) {
            return true;
        }
        //锁外查询做校验
        //1.查询markInfo信息
        Map<String, CaseMarkInfoDO> caseMarkInfoDOMap = caseMarkInfoRepository.queryByParam(
                        CaseMarkInfoDOQueryParamDTO.builder().caseIdList(caseIdList).build()).stream()
                .collect(Collectors.toMap(CaseMarkInfoDO::getCaseId, item -> item,
                        (oldValue, newValue) -> oldValue));
        //2.过滤非上传过的good高负向
        List<RiskCaseDO> unUploadedGoodRiskCaseList = caseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(caseIdList).build()).stream().filter(riskCaseDO -> {
            CaseMarkInfoDO caseMarkInfoDO = caseMarkInfoDOMap.get(riskCaseDO.getCaseId());
            if (Objects.isNull(caseMarkInfoDO) || StringUtils.isNotBlank(riskCaseDO.getFastUploadId())) {
                //没有标注信息或者已经快传完成,剔除
                return false;
            }
            //必须是ok的case
            return ISCheckCategoryEnum.GOOD_OTHER.getCategory().equals(caseMarkInfoDO.getCategory());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unUploadedGoodRiskCaseList)) {
            //不存在非上传过的good高负向
            return true;
        }
        unUploadedGoodRiskCaseList.forEach(item -> {
            lockUtils.batchLockCanWait(
                    LockKeyPreUtil.buildKeyWithEventId(Collections.singleton(item.getEventId())), () ->
                            handleOneCaseUpload(item.getCaseId(), config)
            );
        });
        return true;
    }

    /**
     * 根据caseId做上传
     *
     * @param caseId
     * @return
     */
    private void handleOneCaseUpload(String caseId, HighNegativeUploadAutoCarDataConfig config) {
        try {
            RiskCaseDO item = caseRepository.getByCaseId(caseId);
            CaseMarkInfoDO caseMarkInfoDO = caseMarkInfoRepository.getByCaseId(caseId);
            RiskCaseVehicleRelationDO relationDO = vehicleRepository.queryByParam(
                            RiderCaseVehicleRelationDOParamDTO.builder().caseId(caseId).build()).stream()
                    .findFirst().orElse(null);
            if (Objects.isNull(relationDO) || Objects.isNull(caseMarkInfoDO) || Objects.isNull(item)) {
                log.error("caseId: {} cant find any RiskCaseVehicleRelationDO", caseId);
                return;
            }
            if (!ISCheckCategoryEnum.GOOD_OTHER.getCategory().equals(caseMarkInfoDO.getCategory())) {
                //不是good的case,返回
                return;
            }
            boolean isInGray = config.isInGrayList(item, relationDO);
            if (!isInGray) {
                //不在灰度，返回
                return;
            }
            VehicleCounterInfoDO stagnationCounter = relationDO.getStagnationCounterByDuration(
                    config.getMinDurationSeconds());
            if (Objects.isNull(stagnationCounter)) {
                return;
            }
            Date startTime = stagnationCounter.getStartTime();
            //start + 20
            Date endTime = DatetimeUtil.getNSecondsAfterDateTime(startTime, config.getAfterSeconds());
            //不可以超过当前时间
            endTime = new Date(
                    Math.min(endTime.getTime(), new Date().getTime()));
            DataPlatformFastUploadRequest request = DataPlatformFastUploadRequest.builder()
                    .vin(relationDO.getVin())
                    .begin(startTime)
                    .end(endTime)
                    .modules(config.getModules())
                    //烽火台_逆向
                    .source(item.getSource().getDesc() + "_" + item.getType().getDesc())
                    .build();
            log.info("start call data platform fast upload, case: {}, request: {}", item, request);
            String taskId = dataPlatformAdapter.fastUploadAutoCarData(request);
            log.info("task id: {}", taskId);
            //保存id
            item.setFastUploadId(taskId);
            caseRepository.save(item);
        } catch (Exception e) {
            log.error("handleOneCaseUpload error", e);
        }
    }
}
