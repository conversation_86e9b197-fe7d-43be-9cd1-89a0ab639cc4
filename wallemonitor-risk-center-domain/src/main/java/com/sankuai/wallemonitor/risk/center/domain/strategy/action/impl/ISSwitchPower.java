package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.domain.result.ISSwitchPowerResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.CommonSearchAdaptor;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.SwitchPowerSignalEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 是否切电action
 */
@Slf4j
@Component
public class ISSwitchPower implements ISCheckAction<ISSwitchPowerResult> {

    @Resource
    private CommonSearchAdaptor commonSearchAdaptor;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_IS_SWITCH_POWER_CONFIG, defaultValue = "{}")
    private ISSwitchPowerConfigDTO configDTO;

    /**
     * 执行预检
     *
     * @param actionContext
     */
    @Override
    public ISCheckActionResult<ISSwitchPowerResult> execute(ISCheckActionContext actionContext) {
        VehicleRuntimeInfoContextDO contextDO = actionContext.getVehicleRunTimeContext();
        RiskCheckingQueueItemDO itemDO = actionContext.getItem();
        // 入参校验
        if (contextDO == null || itemDO == null) {
            return ISCheckActionResult.empty();
        }
        // TODO： 保险起见，查询的开始时间取事件发生时间的 - N 分钟（是否有可能在切电中被召回）
        Map<String, String> data = commonSearchAdaptor.querySwitchPowerStatus(contextDO.getVin(),
                DatetimeUtil.getBeforeTime(itemDO.getOccurTime(), TimeUnit.MINUTES, configDTO.getBeforeMinutes()));
        // 判断是否处于切电中
        Boolean switchPower = SwitchPowerSignalEnum.isSwitching(data.get("eventText"));
        if (switchPower) {
            return ISCheckActionResult.<ISSwitchPowerResult>builder()
                    .categoryEnum(ISCheckCategoryEnum.SWITCH_POWER)
                    .build();
        }
        return ISCheckActionResult.empty();
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class ISSwitchPowerConfigDTO {

        /**
         * 查询时间范围
         */
        private Integer beforeMinutes;

    }
}
