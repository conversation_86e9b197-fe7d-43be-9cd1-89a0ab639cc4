package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.domain.result.ISWaitingInQueueResult;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleObstacleInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter.SearchNearbyRequestVTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.WaitInQueueConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.BehindObstacleCheckConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.ObstacleFineTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.ObstacleType;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapLaneDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO.VehicleObstacleContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.common.math.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 停滞不当预检 - 是否在排队通行v3
 * - 相对v2版本，优化静态车辆绕行场景提召回，检查后方是否有社会车辆
 */
@Slf4j
@Component
public class ISWaitingInQueueV3 implements ISCheckAction<ISWaitingInQueueResult> {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleAdapter vehicleAdapter;

    // 90
    private static final Double theta = 90D;

    @Override
    public ISCheckActionResult<ISWaitingInQueueResult> execute(ISCheckActionContext actionContext) {
        RiskCheckingQueueItemDO itemDO = actionContext.getItem();
        if (Objects.isNull(itemDO)) {
            return ISCheckActionResult.empty();
        }
        ISWaitingInQueueResult result = ISWaitingInQueueResult.builder().build();
        VehicleRuntimeInfoContextDO runTimeContext = actionContext.getVehicleRunTimeContext();
        String hdMapArea = vehicleAdapter.getVehicleHdMapArea(itemDO.getVin());
        if (StringUtils.isBlank(hdMapArea) || Objects.isNull(runTimeContext)
                || Objects.isNull(runTimeContext.getLocation())) {
            log.warn("无法找到车辆区域信息");
            Cat.logEvent("ISWaitingInQueueV2", "noHdMapArea");
            return ISCheckActionResult.empty(result);
        }
        // 查询障碍物的
        VehicleObstacleContextDO obstacleContext = runTimeContext.getObstacleContext();
        if (Objects.isNull(obstacleContext) || CollectionUtils.isEmpty(obstacleContext.getPerceptionObstacle())) {
            // 如果不存在障碍物的信息，也不需要做任何形式的判断了
            log.warn("无法找到车辆周围障碍物信息");
            Cat.logEvent("ISWaitingInQueueV2", "noObstacleInfo");
            return ISCheckActionResult.empty(result);
        }

        // 填充车辆的当前位置和前序位置信息，方便计算车辆方向
        WaitInQueueConfigDTO waitInQueueConfig = lionConfigRepository.getWaitInQueueConfig();
        fillVehiclePositionInfo(result, runTimeContext, itemDO.getOccurTime(), waitInQueueConfig);
        if (Objects.isNull(result.getPreVehiclePosition())) {
            return ISCheckActionResult.empty(result);
        }

        PositionDO curPosition = result.getVehiclePosition();
        PositionDO preVehiclePosition = result.getPreVehiclePosition().getPosition();
        // 查询车辆周围的车道信息
        SearchNearbyRequestVTO param = SearchNearbyRequestVTO.builder()
                .hdMapEnum(HdMapEnum.LANE_POLYGON)
                .positionDO(curPosition)
                .area(hdMapArea)
                .restrictType(waitInQueueConfig.getLaneTypeList())
                .range(waitInQueueConfig.getRange())  // 取配置距离范围内的
                .build();
        List<HdMapElementGeoDO> nearbyLaneList = hdMapAdapter.searchNearby(param);
        // 查询车辆所在的车道
        List<HdMapElementGeoDO> vehicleCurLaneList = nearbyLaneList.stream()
                .filter(hdMapElementGeoDO -> hdMapElementGeoDO.isInPolygon(runTimeContext.getLocation()))   // 确保车辆所在的车道
                .filter(hdMapElementGeoDO -> hdMapElementGeoDO.isSameDirection(preVehiclePosition,
                        curPosition, theta))    // 并且要同一个方向的
                .collect(Collectors.toList());  // 找第最后一个 fixme: 这里可能会出现重叠车道

        // 确定车辆当前所处车道
        HdMapElementGeoDO vehicleCurLane =
                CollectionUtils.isNotEmpty(vehicleCurLaneList) ? vehicleCurLaneList.get(vehicleCurLaneList.size() - 1)
                        : null;
        if (Objects.nonNull(vehicleCurLane)) {
            result.setVehicleCurLaneId(vehicleCurLane.getId());
            // 是否为单车道
            if (StringUtils.isBlank(vehicleCurLane.getPropertyByKey("left")) && StringUtils.isBlank(
                    vehicleCurLane.getPropertyByKey("right"))) {
                result.setSingleLane(true);
            }
        }
        // 确定车辆后继车道
        HdMapElementGeoDO successorLane = Optional.ofNullable(vehicleCurLane)
                .map(lane -> (List<String>) lane.getPropertyByKey(GeoElementTypeKeyConstant.SUCCESSOR))
                // 取第一个后继道路 fixme: 这里可能会出现多个后继道路
                .map(id2HdMapElementGeoDOList -> {
                    if (CollectionUtils.isNotEmpty(id2HdMapElementGeoDOList)) {
                        return id2HdMapElementGeoDOList.get(0);
                    } else {
                        return null;
                    }
                }).filter(Objects::nonNull)
                .map(hdMapAdapter::getLaneById).orElse(null);
        result.setVehicleSuccessorLaneId(
                Optional.ofNullable(successorLane).map(HdMapElementGeoDO::getId).orElse(CharConstant.CHAR_EMPTY));

        // 计算障碍物信息
        List<VehicleObstacleInfoDTO> obstacleList = buildObstacleInfo(result, obstacleContext, vehicleCurLane, successorLane);
        result.setAllObstacleList(obstacleList);

        // 找到同车道前方的障碍物，需同时满足 类型要求 + 角度要求 + 车道要求 + 距离要求
        VehicleObstacleInfoDTO vehicleFrontObstacle = obstacleList.stream()
                .filter(obstacleInfoDTO -> isAheadObstacle(obstacleInfoDTO, waitInQueueConfig))
                .findFirst().orElse(null);
        if (Objects.isNull(vehicleFrontObstacle)) {
            return ISCheckActionResult.empty(result);
        }

        // 计算障碍物是否停靠中
        calcFrontObstacleIsParking(vehicleFrontObstacle, vehicleCurLane, successorLane);
        result.setFrontObstacle(vehicleFrontObstacle);   // 保存前方障碍物信息

        // 找到同车道后方的障碍物
        VehicleObstacleInfoDTO behindObstacle = obstacleList.stream()
                .filter(obstacleInfoDTO -> isBehindObstacle(obstacleInfoDTO, waitInQueueConfig))
                .min(Comparator.comparing(VehicleObstacleInfoDTO::getDistance)).orElse(null);
        result.setBehindObstacle(behindObstacle);

        // 搜索自车当前可绕行车道
        List<String> usableLaneIds = calcUsableLane(vehicleCurLane, nearbyLaneList, obstacleList,
                waitInQueueConfig);
        result.setUsableLaneIds(usableLaneIds);

        // 检测是否有可借道通行空间(暂无路口 或者 距离路口过远)
        if (Objects.equals(runTimeContext.getDistanceToJunction(), NumberUtils.DOUBLE_MINUS_ONE)
                || runTimeContext.getDistanceToJunction() != null && runTimeContext
                        .getDistanceToJunction()
                >= waitInQueueConfig.getThresholdOpenUsableLaneChecking()) {
            // 有可使用车道，则返回障碍物堵路
            if (CollectionUtils.isNotEmpty(usableLaneIds)) {
                return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                        .categoryEnum(ISCheckCategoryEnum.STOP_BY_OBSTACLE).build();
            }

            // 后方无社会车辆且当前车辆在最右车道，则返回障碍物堵路
            if (waitInQueueConfig.getBehindObstacleCheckConfig().isEnable() &&
                    Objects.isNull(behindObstacle) && Objects.nonNull(vehicleCurLane) && StringUtils.isBlank(
                    vehicleCurLane.getPropertyByKey("right"))) {
                return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                        .categoryEnum(ISCheckCategoryEnum.STOP_BY_OBSTACLE).build();
            }
        }

        // 检测是否在辅路（mixed/biking车道）且非路口内
        if (Objects.nonNull(vehicleCurLane) && isInMixedBikingLane(runTimeContext, vehicleCurLane, waitInQueueConfig)) {
            return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                    .categoryEnum(ISCheckCategoryEnum.STOP_BY_OBSTACLE).build();
        }

        return ISCheckActionResult.<ISWaitingInQueueResult>builder().actionResult(result)
                .categoryEnum(ISCheckCategoryEnum.WAITING_FRONT_PASSAGER).build();
    }

    /**
     * 检查正后方是否有社会车辆
     */
    private boolean isBehindObstacle(VehicleObstacleInfoDTO obstacle, WaitInQueueConfigDTO config) {
        if (Objects.isNull(obstacle) || Objects.isNull(config)) {
            return false;
        }
        BehindObstacleCheckConfig behindObstacleCheckConfig = config.getBehindObstacleCheckConfig();
        if (Objects.isNull(behindObstacleCheckConfig)) {
            return false;
        }

        boolean isAngleMatched =
                Objects.nonNull(obstacle.getAngle()) && obstacle.getAngle() > behindObstacleCheckConfig.getAngle();
        boolean isDistanceMatch = Objects.nonNull(obstacle.getDistance())
                && obstacle.getDistance() < behindObstacleCheckConfig.getDistance();
        boolean isMatchedType = Objects.nonNull(obstacle.getFineType()) && behindObstacleCheckConfig.getFineTypeList()
                .contains(obstacle.getFineType());
        boolean inLane = StringUtils.isNotBlank(obstacle.getLaneId());
        log.info("isBehindObstacle isAngleMatched:{}, isDistanceMatch:{}, isMatchedType:{}, inLane:{}", isAngleMatched,
                isDistanceMatch, isMatchedType, inLane);
        return isAngleMatched && isDistanceMatch && isMatchedType && inLane;
    }

    /**
     * 计算前方障碍物是否为静态车辆
     *
     * @param obstacle
     */
    private void calcFrontObstacleIsParking(VehicleObstacleInfoDTO obstacle, HdMapElementGeoDO vehicleLane,
            HdMapElementGeoDO vehicleSuccessorLane) {
        if (Objects.isNull(obstacle)) {
            return;
        }
        if (StringUtils.isBlank(obstacle.getLaneId())) {
            return;
        }
        Map<String, HdMapElementGeoDO> laneIdMap = Lists.newArrayList(vehicleLane, vehicleSuccessorLane).stream()
                .filter(Objects::nonNull).collect(Collectors.toMap(HdMapElementGeoDO::getId, Function.identity()));
        HdMapElementGeoDO obstacleLane = laneIdMap.get(obstacle.getLaneId());
        if (Objects.isNull(obstacleLane)) {
            return;
        }
        List<PositionDO> polygonCoordinates = Optional.ofNullable(obstacleLane.getPolygonDO()).map(PolygonDO::getPoints)
                .orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(polygonCoordinates)) {
            return;
        }
        try {
            double minDistance = getMinDistance(obstacle, polygonCoordinates);
            obstacle.setDistanceToNearCurb(minDistance);
        } catch (Exception e) {
            log.warn("calcFrontObstacleIsParking error", e);
        }
    }

    /**
     * 计算到多边形每条边的最短距离
     *
     * @param obstacle
     * @param polygonCoordinates
     * @return
     */
    private double getMinDistance(VehicleObstacleInfoDTO obstacle, List<PositionDO> polygonCoordinates) {
        PositionDO obstaclePosition = obstacle.getPosition();

        // 计算到多边形每条边的最短距离
        double minDistance = Double.MAX_VALUE;

        // 遍历多边形的所有边
        for (int i = 0; i < polygonCoordinates.size() - 1; i++) {
            PositionDO p1 = polygonCoordinates.get(i);
            PositionDO p2 = polygonCoordinates.get(i + 1);

            // 计算点到线段的距离
            double distance = GeoToolsUtil.pointToLineSegmentDistance(
                    obstaclePosition.getLongitude(), obstaclePosition.getLatitude(),  // 障碍物坐标
                    p1.getLongitude(), p1.getLatitude(),                        // 线段起点
                    p2.getLongitude(), p2.getLatitude()                         // 线段终点
            );

            minDistance = Math.min(minDistance, distance);
        }
        return minDistance;
    }

    /**
     * 判断车辆是否在混合或者非机动车道内
     *
     * @param vehicleCurLane
     * @param waitInQueueConfig
     * @return
     */
    private Boolean isInMixedBikingLane(VehicleRuntimeInfoContextDO runtimeContext, HdMapElementGeoDO vehicleCurLane,
            WaitInQueueConfigDTO waitInQueueConfig) {
        if (Objects.equals(runtimeContext.getDistanceToJunction(), NumberUtils.DOUBLE_MINUS_ONE)
                || runtimeContext.getDistanceToJunction()
                >= waitInQueueConfig.getThresholdOpenBikingLaneChecking()) {
            String laneType = vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.LANE_TYPE);
            return waitInQueueConfig.getSensitiveLaneTypeList().contains(laneType);
        } else {
            return false;
        }
    }

    /**
     * 计算可借道通行空间
     *
     * @param vehicleCurLane
     * @param nearbyLaneList
     * @param obstacleList
     * @param waitInQueueConfig
     * @return
     */
    private List<String> calcUsableLane(HdMapElementGeoDO vehicleCurLane, List<HdMapElementGeoDO> nearbyLaneList,
            List<VehicleObstacleInfoDTO> obstacleList, WaitInQueueConfigDTO waitInQueueConfig) {
        if (Objects.isNull(vehicleCurLane)) {
            return Collections.emptyList();
        }

        try {
            // 计算当前可用左侧及其后继车道
            List<HdMapLaneDO> laneDOList = nearbyLaneList.stream().map(this::hdMapElementGeoToLaneDO)
                    .collect(Collectors.toList());
            ConnectedLaneCalcResultDO connectedLanes = findLeftAndSuccessorLanes(vehicleCurLane.getId(), laneDOList);
            if (Objects.isNull(connectedLanes)) {
                return Collections.emptyList();
            }
            // 左侧车道已经被障碍物堵路了 不再继续检索
            HdMapLaneDO left = connectedLanes.getLeft();
            if (Objects.isNull(left) || hasObstacleInLane(obstacleList, left, waitInQueueConfig)) {
                return Collections.emptyList();
            } else {
                return Collections.singletonList(left.getLaneId());
            }
        } catch (Exception e) {
            log.error("calcUsableLane error", e);
            return Collections.emptyList();
        }
    }

    /**
     * 判断是否有障碍物在车道内
     */
    private boolean hasObstacleInLane(List<VehicleObstacleInfoDTO> obstacleList, HdMapLaneDO lane,
            WaitInQueueConfigDTO waitInQueueConfig) {
        List<String> focusFineTypeList = waitInQueueConfig.getFineTypeList();
        PolygonDO lanePolygon = lane.getPolygonDO();
        return obstacleList.stream().anyMatch(obstacle -> lanePolygon.isInPolygon(obstacle.getPosition()) // 在车道内
                // 命中障碍物类型
                && focusFineTypeList.contains(obstacle.getFineType())
                // 角度符合阈值
                && obstacle.getAngle() < waitInQueueConfig.getUsableLaneObstacleAngleThreshold()
                // 距离符合阈值
                && obstacle.getDistance() < waitInQueueConfig.getUsableLaneObstacleDistanceThreshold());
    }

    /**
     * 计算可借道通行空间
     * @param currentLaneId
     * @param laneDOList
     * @return
     */
    private ConnectedLaneCalcResultDO findLeftAndSuccessorLanes(String currentLaneId, List<HdMapLaneDO> laneDOList) {
        if (StringUtils.isBlank(currentLaneId) || CollectionUtils.isEmpty(laneDOList)) {
            return null;
        }

        Map<String, HdMapLaneDO> laneIdMap = laneDOList.stream()
                .collect(Collectors.toMap(HdMapLaneDO::getLaneId, Function.identity(), (o1, o2) -> o1));
        HdMapLaneDO currentLane = laneIdMap.get(currentLaneId);
        if (Objects.isNull(currentLane)) {
            return null;
        }

        // 找到左侧车道对象
        String leftLaneId = currentLane.getLeft();
        HdMapLaneDO leftLane = laneIdMap.get(leftLaneId);
        if (Objects.isNull(leftLane)) {
            log.warn("left lane is null, please increase search range.");
            return null;
        }
        ConnectedLaneCalcResultDO result = ConnectedLaneCalcResultDO.builder()
                .left(leftLane)
                .build();

        List<String> successorIds = leftLane.getSuccessor();
        if (CollectionUtils.isEmpty(successorIds)) {
            return result;
        }
        List<HdMapLaneDO> successorLanes = successorIds.stream().map(laneIdMap::get)
                .filter(lane -> Objects.nonNull(lane) && Objects.equals(lane.getRoadId(),
                        currentLane.getRoadId())) // 需要找同road的？
                .collect(Collectors.toList());
        result.setLeftSuccessorList(successorLanes);
        return result;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    private static class ConnectedLaneCalcResultDO {

        /**
         * 左侧车道
         */
        private HdMapLaneDO left;
        /**
         * 左侧车道后继车道
         */
        private List<HdMapLaneDO> leftSuccessorList;

    }

    /**
     * 将车道信息转换为车道对象
     *
     * @param geoDO
     * @return
     */
    private HdMapLaneDO hdMapElementGeoToLaneDO(HdMapElementGeoDO geoDO) {
        return HdMapLaneDO.builder()
                .laneId(geoDO.getId())
                .section(geoDO.getPropertyByKey(GeoElementTypeKeyConstant.SECTION))
                .roadId(geoDO.getPropertyByKey(GeoElementTypeKeyConstant.ROAD))
                .left(geoDO.getPropertyByKey(GeoElementTypeKeyConstant.LEFT))
                .right(geoDO.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT))
                .successor(geoDO.getPropertyByKey(GeoElementTypeKeyConstant.SUCCESSOR))
                .predecessor(geoDO.getPropertyByKey(GeoElementTypeKeyConstant.PREDECESSOR))
                .centerLinePoints(geoDO.getMiddleLinePoints())
                .polygonDO(geoDO.getPolygonDO())
                .build();
    }

    /**
     * 填充车辆周围障碍物信息
     *
     * @param result
     * @param obstacleContext
     * @param vehicleCurLane
     * @param successorLane
     */
    private List<VehicleObstacleInfoDTO> buildObstacleInfo(ISWaitingInQueueResult result,
            VehicleObstacleContextDO obstacleContext, HdMapElementGeoDO vehicleCurLane,
            HdMapElementGeoDO successorLane) {

        PositionDO curPosition = result.getVehiclePosition();
        PositionDO preVehiclePosition = result.getPreVehiclePosition().getPosition();

        // 遍历过程常量
        HdMapElementGeoDO finalVehicleCurLane = vehicleCurLane;
        // 简化障碍物的信息
        List<VehicleObstacleInfoDTO> obstacleInfoList = obstacleContext.getPerceptionObstacle().stream()
                .filter(obstacle -> Objects.nonNull(obstacle) && Objects.nonNull(obstacle.getPosition()))
                .map(obstacle -> {
                    String fineType = Optional.ofNullable(obstacle.getObstacleType()).map(ObstacleType::getFineType)
                            .map(ObstacleFineTypeEnum::transferShotName)
                            .orElse(CharConstant.CHAR_EMPTY);
                    String obstacleType = Optional.ofNullable(obstacle.getObstacleType())
                            .map(ObstacleType::getCoarseType)
                            .orElse(CharConstant.CHAR_EMPTY);
                    PositionDO wgs84Position = GeoToolsUtil.utmToWgs84(obstacle.getPosition().getX(),
                            obstacle.getPosition().getY());
                    return VehicleObstacleInfoDTO.builder()
                            .obstacleId(obstacle.getId())
                            .position(wgs84Position)
                            .fineType(fineType)
                            .obstacleType(obstacleType)
                            .type(obstacle.getType())
                            .width(obstacle.getWidth())
                            .build();
                }).collect(Collectors.toList());

        obstacleInfoList.forEach((obstacle) -> {
            Double angle = GeoToolsUtil.angleCalc(curPosition, obstacle.getPosition(), preVehiclePosition,
                    curPosition); // 计算障碍物夹角
            Double distance = GeoToolsUtil.distance(curPosition, obstacle.getPosition());
            obstacle.setDistance(distance); // 设置障碍物与当前车辆之间的距离。
            obstacle.setAngle(angle);
            // 获取离中心点最近的夹角，key是最近的一个，value是下一个
            Pair<PositionDO, PositionDO> laneCenterLine = Optional.ofNullable(finalVehicleCurLane)
                    .map(lane -> lane.getMiddleLineTwoNearPoint(curPosition)).orElse(null);
            // 车道中心线的夹角不为空时 算障碍物与中心线的夹角
            if (Objects.nonNull(laneCenterLine)) {
                Double middleAngle = GeoToolsUtil.angleCalc(curPosition, obstacle.getPosition(),
                        laneCenterLine.getKey(), laneCenterLine.getValue());
                obstacle.setMiddleAngle(middleAngle);
            }
            // 找到在车道内的或者后继车道的
            boolean inSameLane = finalVehicleCurLane != null && finalVehicleCurLane.isInPolygon(obstacle.getPosition());
            boolean inSuccessorLane = successorLane != null && successorLane.isInPolygon(obstacle.getPosition());
            // 只要在同车道或者后继车道，都算
            if (inSameLane) {
                obstacle.setLaneId(finalVehicleCurLane.getId());    // 保存该障碍物所在的车道
            }
            if (inSuccessorLane) {
                obstacle.setLaneId(successorLane.getId());      // 保存该障碍物所在的车道
            }
        });

        return obstacleInfoList;
    }

    /**
     * 填充车辆位置信息
     *
     * @param result
     * @param runtimeContext
     */
    private void fillVehiclePositionInfo(ISWaitingInQueueResult result, VehicleRuntimeInfoContextDO runtimeContext,
            Date occurTime, WaitInQueueConfigDTO waitInQueueConfig) {
        PositionDO curPosition = runtimeContext.getLocation();
        // fixme: 这里取开始停止的前置时间
        Date timeBeforeOccurTime = DatetimeUtil.getNSecondsBeforeDateTime(occurTime, waitInQueueConfig.getPastSecond());
        // 查询过去的两个点
        List<VehicleInQueuePositionDTO> vehicleLastLocationList = vehicleAdapter
                .queryVehicleHistoryDataFromEveReplay(runtimeContext.getVin(),
                        DatetimeUtil.getTimeInSeconds(timeBeforeOccurTime),
                        DatetimeUtil.getTimeInSeconds(occurTime))
                .stream().map(vehicleDataInfoVO -> {
                    PositionDO positionDO = PositionDO.getPosition(vehicleDataInfoVO.getLongitude(),
                            vehicleDataInfoVO.getLatitude(), CoordinateSystemEnum.WGS84);
                    return VehicleInQueuePositionDTO.builder().position(positionDO)
                            .distance(GeoToolsUtil.distance(positionDO, curPosition))     // 计算距离
                            .time(vehicleDataInfoVO.getTime())  // 时间
                            .build();
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleLastLocationList)) {
            log.warn("无法找到车辆历史定位信息");
            Cat.logEvent("ISWaitingInQueueV2", "noVehicleLastLocation");
            return;
        }

        result.setLastPosition(new ArrayList<>(vehicleLastLocationList));
        Collections.reverse(vehicleLastLocationList);   // 直接倒序
        // 取最后一个不靠近定位点的点，防止有拐弯的情况
        VehicleInQueuePositionDTO prePositionDTO = IntStream.range(0, vehicleLastLocationList.size()).filter(index -> {
                    VehicleInQueuePositionDTO position = vehicleLastLocationList.get(index);
                    // 满足最小距离，防止过近引发异常
                    return Objects.nonNull(waitInQueueConfig) && Objects.nonNull(position)
                            && position.getDistance() > waitInQueueConfig.getPreMinDistance();
                }).mapToObj(vehicleLastLocationList::get).findFirst()
                .orElseGet(() -> vehicleLastLocationList.get(0));// 如果没找到指定的，就取最近的一个
        // 保存
        result.setVehiclePosition(curPosition);
        result.setPreVehiclePosition(prePositionDTO);
        // 前序的position
        if (Objects.isNull(prePositionDTO)) {
            log.warn("无法找到就近的俩近似点");
            Cat.logEvent("ISWaitingInQueueV2", "noPrePosition");
        }
    }

    /**
     * 是否为自车前方的障碍物
     *
     * @param obstacle
     * @param config
     * @return
     */
    public boolean isAheadObstacle(VehicleObstacleInfoDTO obstacle, WaitInQueueConfigDTO config) {
        if (Objects.isNull(obstacle) || Objects.isNull(config)) {
            return false;
        }
        boolean isAngleMatched = checkAngle(obstacle, config);

        // 判断距离是否满足
        Map<String, Double> distanceThresholdMap = config.getFineTypeDistanceThreshold();
        Double distanceThreshold = distanceThresholdMap.getOrDefault(obstacle.getFineType(),
                config.getDefaultDistanceThreshold());
        boolean isDistanceMatch = distanceThreshold != null && obstacle.getDistance() < distanceThreshold;

        List<String> fineTypeList = config.getFineTypeList();
        boolean isMatchedType = CollectionUtils.isNotEmpty(fineTypeList)
                && StringUtils.isNotBlank(obstacle.getFineType()) && fineTypeList.contains(obstacle.getFineType());

        boolean inLane = StringUtils.isNotBlank(obstacle.getLaneId());

        return isAngleMatched && isDistanceMatch && isMatchedType && inLane;
    }

    /**
     * 检查障碍物与中心线的夹角是否满足条件
     *
     * @param obstacle
     * @param config
     * @return
     */
    private boolean checkAngle(VehicleObstacleInfoDTO obstacle, WaitInQueueConfigDTO config) {
        Map<String, Double> angleThresholdByObstacleType = config.getAngleThresholdByObstacleType();
        if (MapUtils.isEmpty(angleThresholdByObstacleType)) {
            return false;
        }

        Double defaultAngleThreshold = angleThresholdByObstacleType.get("DEFAULT");
        Double angleThreshold = angleThresholdByObstacleType.getOrDefault(obstacle.getObstacleType(),
                defaultAngleThreshold);
        return obstacle.getMiddleAngle() != null && obstacle.getMiddleAngle() < angleThreshold;
    }
}
