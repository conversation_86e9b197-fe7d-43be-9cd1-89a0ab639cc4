package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.sankuai.wallemonitor.risk.center.domain.result.ISTrafficLightResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 停滞不当预检-大模型
 */
@Slf4j
@Component
public class ISTrafficLightAction implements ISCheckAction<ISTrafficLightResult> {


    /**
     * 执行预检
     *
     * @param actionContext
     */
    @Override
    public ISCheckActionResult<ISTrafficLightResult> execute(ISCheckActionContext actionContext) {
        VehicleRuntimeInfoContextDO contextDO = actionContext.getVehicleRunTimeContext();
        if (contextDO == null) {
            return ISCheckActionResult.empty();
        }
        //如果自动驾驶状态下，为红灯
        TrafficLightTypeEnum trafficLightTypeEnum = contextDO.getTrafficLightType();
        if (trafficLightTypeEnum == null) {
            return ISCheckActionResult.empty();
        }
        switch (trafficLightTypeEnum) {
            case RED:
            case YELLOW:
                //红灯或者黄灯，check一下
                return ISCheckActionResult.<ISTrafficLightResult>builder()
                        .categoryEnum(ISCheckCategoryEnum.RED_LIGHT)
                        .actionResult(ISTrafficLightResult.builder().nowTrafficLightType(trafficLightTypeEnum).build())
                        .build();
            case GREEN:
                //如果是绿灯，按照路口检出
                return ISCheckActionResult.<ISTrafficLightResult>builder()
                        .categoryEnum(ISCheckCategoryEnum.IN_JUNCTION)
                        .actionResult(ISTrafficLightResult.builder().nowTrafficLightType(trafficLightTypeEnum).build())
                        .build();
            default:
                //其他灯，无法确认
                return ISCheckActionResult.empty();
        }
    }
}
