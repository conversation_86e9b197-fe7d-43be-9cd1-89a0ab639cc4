package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.sankuai.wallemonitor.risk.center.domain.result.ISMenderParkingResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionManager;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.convert.PositionConvert;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 停滞不当预检 - 是否在区域适配的停车区范围
 */
@Slf4j
@Component
public class ISInMenderParkingArea implements ISCheckAction<ISMenderParkingResult> {

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private SafetyAreaRepository safetyAreaRepository;

    @Resource
    private ISCheckActionManager rickCheckActionManager;

    @Resource
    private PositionConvert positionDOConvert;

    /**
     * 执行预检
     *
     * @param actionContext
     */
    @Override
    public ISCheckActionResult<ISMenderParkingResult> execute(ISCheckActionContext actionContext) {
        VehicleRuntimeInfoContextDO runtimeInfoContextDO = actionContext.getVehicleRunTimeContext();
        if (runtimeInfoContextDO == null || StringUtils.isBlank(runtimeInfoContextDO.getLat()) || StringUtils.isBlank(
                runtimeInfoContextDO.getLat())) {
            return ISCheckActionResult.empty();
        }

        PositionDO positionDO = GeoToolsUtil.wgs84ToGcj02(Double.parseDouble(runtimeInfoContextDO.getLng()),
                Double.parseDouble(runtimeInfoContextDO.getLat()));
        if (Objects.isNull(positionDO)) {
            //无法判断
            return ISCheckActionResult.empty();
        }
        String matchedAreaId = safetyAreaRepository.verifyInParkingArea(positionDO);
        //如果满足要求，则结束
        return StringUtils.isBlank(matchedAreaId) ? ISCheckActionResult.empty()
                : ISCheckActionResult.<ISMenderParkingResult>builder()
                        .categoryEnum(ISCheckCategoryEnum.MENDER_IN_PARKING_AREA)
                        .actionResult(ISMenderParkingResult.builder()
                                .matchParkingId(matchedAreaId)
                                .build())
                        .build();
    }

}
