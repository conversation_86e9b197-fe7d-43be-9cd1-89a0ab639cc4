<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
    </dependency>
  </dependencies>
  <!--  可去除parent或自定义parent  -->
  <artifactId>wallemonitor-risk-center</artifactId>
  <dependencyManagement>
    <dependencies>

      <!-- geo hash -->
      <dependency>
        <groupId>ch.hsr</groupId>
        <artifactId>geohash</artifactId>
        <version>1.4.2</version>
      </dependency>

      <!-- 随机森林 -->
      <dependency>
        <groupId>nz.ac.waikato.cms.weka</groupId>
        <artifactId>weka-stable</artifactId>
        <version>3.8.6</version>
      </dependency>
      <!--csv文件读写 -->
      <dependency>
        <groupId>com.opencsv</groupId>
        <artifactId>opencsv</artifactId>
        <version>5.7.1</version>
      </dependency>

      <!-- 车辆通用搜索服务 -->
      <dependency>
        <groupId>com.sankuai.walleom</groupId>
        <artifactId>common-search-api</artifactId>
        <version>1.0.0</version>
      </dependency>
      <!--MySQL2Mafka-->
      <dependency>
        <groupId>com.meituan.databus</groupId>
        <artifactId>dbusUtils_thrift0.9.2</artifactId>
        <version>0.0.19</version>
      </dependency>
      <dependency>
        <groupId>com.sankuai.wallecmdb.data</groupId>
        <artifactId>eve-replay-inquire-api</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <artifactId>podam</artifactId>
        <groupId>uk.co.jemos.podam</groupId>
        <version>7.2.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.sankuai.walleeve</groupId>
        <artifactId>walle-eve-utils</artifactId>
        <version>1.0.17</version>
      </dependency>
      <dependency>
        <artifactId>transmittable-thread-local</artifactId>
        <groupId>com.alibaba</groupId>
        <version>2.11.5</version>
      </dependency>
      <dependency>
        <artifactId>lombok</artifactId>
        <groupId>org.projectlombok</groupId>
        <version>1.18.24</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok-mapstruct-binding</artifactId>
        <version>0.2.0</version>
      </dependency>
      <!--通用依赖-->
      <dependency>
        <groupId>com.sankuai.walleeve</groupId>
        <artifactId>walle-eve-domain</artifactId>
        <version>1.1.6</version>
      </dependency>
      <!--核心依赖-->
      <dependency>
        <artifactId>wallemonitor-risk-center-infra</artifactId>
        <groupId>com.sankuai.wallemonitor</groupId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <artifactId>wallemonitor-risk-center-api</artifactId>
        <groupId>com.sankuai.wallemonitor</groupId>
        <version>1.0.5</version>
      </dependency>
      <dependency>
        <artifactId>wallemonitor-risk-center-domain</artifactId>
        <groupId>com.sankuai.wallemonitor</groupId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <artifactId>wallemonitor-risk-center-server</artifactId>
        <groupId>com.sankuai.wallemonitor</groupId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>


      <!--push平台-->
      <dependency>
        <artifactId>dpmtpush-backend-api</artifactId>
        <groupId>com.dianping.dpmtpush</groupId>
        <version>0.0.1.23</version>
      </dependency>
      <dependency>
        <artifactId>dpmtpushtoken-mapping-api</artifactId>
        <groupId>com.dianping.dpmtpushtoken</groupId>
        <version>1.2.10</version>
      </dependency>

      <dependency>
        <groupId>com.dianping.zebra</groupId>
        <artifactId>zebra-calcite</artifactId>
        <version>3.3.1</version>
      </dependency>
      <dependency>
        <groupId>com.dianping.zebra</groupId>
        <artifactId>zebra-tool</artifactId>
        <version>3.3.1</version>
      </dependency>
      <!--geotools-->
      <dependency>
        <artifactId>gt-geojson</artifactId>
        <groupId>org.geotools</groupId>
        <version>${geotools.version}</version>
      </dependency>
      <dependency>
        <artifactId>gt-main</artifactId>
        <groupId>org.geotools</groupId>
        <version>${geotools.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>commons-text</artifactId>
            <groupId>org.apache.commons</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <artifactId>jts-core</artifactId>
        <groupId>org.locationtech.jts</groupId>
        <version>1.19.0</version>
      </dependency>
      <dependency>
        <groupId>com.sankuai.walledelivery</groupId>
        <artifactId>walle-delivery-operation-client</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <artifactId>gt-epsg-wkt</artifactId>
        <groupId>org.geotools</groupId>
        <version>${geotools.version}</version>
      </dependency>
      <!--指定坐标系查询数据库代码中指定的坐标系会从这里查询，只支持其中有的坐标系-->
      <dependency>
        <artifactId>mapstruct</artifactId>
        <groupId>org.mapstruct</groupId>
        <version>${mapstruct-jdk8.version}</version>
      </dependency>
      <dependency>
        <artifactId>mapstruct-jdk8</artifactId>
        <groupId>org.mapstruct</groupId>
        <version>${mapstruct-jdk8.version}</version>
      </dependency>
      <dependency>
        <artifactId>mapstruct-processor</artifactId>
        <groupId>org.mapstruct</groupId>
        <version>${mapstruct-jdk8.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sankuai.map.maf</groupId>
        <artifactId>openplatform-dependency</artifactId>
        <version>1.1.82</version>
      </dependency>
      <!---->
      <dependency>
        <groupId>com.meituan.mars.framework</groupId>
        <artifactId>mars-common-domain</artifactId>
        <version>3.25.2</version>
      </dependency>

      <dependency>
        <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
        <artifactId>open-sdk</artifactId>
        <version>1.0.51-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.sankuai.oa.card.toolkit</groupId>
        <artifactId>card-toolkit</artifactId>
        <version>1.0-RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.sankuai.ead</groupId>
        <artifactId>citadel-client</artifactId>
        <version>3.0.55</version>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>2.3</version> <!-- 请根据需要使用最新版本 -->
      </dependency>
      <dependency>
        <groupId>com.sankuai.walledelivery</groupId>
        <artifactId>walle-delivery-basic-client</artifactId>
        <version>1.0.11</version>
      </dependency>
      <dependency>
        <groupId>com.sankuai.carosscan</groupId>
        <artifactId>eve_common_client</artifactId>
        <version>1.1.2</version>
        <exclusions>
          <exclusion>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-simple</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <groupId>org.projectlombok</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.sankuai.walle</groupId>
        <artifactId>cms-client</artifactId>
        <version>1.0.7</version>
        <exclusions>
          <exclusion>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-simple</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <groupId>org.projectlombok</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <!--空间检索-->
      <dependency>
        <groupId>org.geotools</groupId>
        <artifactId>gt-referencing</artifactId>
        <version>${geotools.version}</version>
        <exclusions>
          <exclusion>
            <groupId>tech.units</groupId>
            <artifactId>indriya</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.geotools</groupId>
        <artifactId>gt-epsg-hsql</artifactId>
        <version>${geotools.version}</version>
      </dependency>
      <dependency>
        <groupId>org.geotools</groupId>
        <artifactId>gt-api</artifactId>
        <version>20.5</version>
      </dependency>
      <dependency>
        <groupId>tech.units</groupId>
        <artifactId>indriya</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>javax.measure</groupId>
        <artifactId>unit-api</artifactId>
        <version>2.1.3</version>
      </dependency>
      <dependency>
        <groupId>com.github.davidmoten</groupId>
        <artifactId>rtree</artifactId>
        <version>0.10</version>
      </dependency>
      <dependency>
        <groupId>org.geotools</groupId>
        <artifactId>gt-opengis</artifactId>
        <version>${geotools.version}</version>
      </dependency>
      <dependency>
        <groupId>org.geotools</groupId>
        <artifactId>gt-geometry</artifactId>
        <version>${geotools.version}</version>
      </dependency>
      <dependency>
        <groupId>org.geotools</groupId>
        <artifactId>gt-metadata</artifactId>
        <version>${geotools.version}</version>
      </dependency>
      <!--空间检索-->

    </dependencies>

  </dependencyManagement>


  <build>
    <plugins>
    </plugins>
  </build>

  <groupId>com.sankuai.wallemonitor</groupId>

  <modelVersion>4.0.0</modelVersion>
  <modules>
    <module>wallemonitor-risk-center-api</module>
    <module>wallemonitor-risk-center-infra</module>
    <module>wallemonitor-risk-center-server</module>
    <module>wallemonitor-risk-center-domain</module>
  </modules>
  <name>wallemonitor-risk-center</name>
  <packaging>pom</packaging>

  <parent>
    <artifactId>xframe-starter-parent</artifactId>
    <groupId>com.meituan.xframe</groupId>
    <!--XFrame产品版本：https://km.sankuai.com/custom/onecloud/page/133516477-->
    <version>2.6.12.1</version>
  </parent>
  <properties>
    <geotools.version>24.4.01</geotools.version>
    <mapstruct-jdk8.version>1.5.2.Final</mapstruct-jdk8.version>
  </properties>

  <version>1.0.0-SNAPSHOT</version>


</project>
