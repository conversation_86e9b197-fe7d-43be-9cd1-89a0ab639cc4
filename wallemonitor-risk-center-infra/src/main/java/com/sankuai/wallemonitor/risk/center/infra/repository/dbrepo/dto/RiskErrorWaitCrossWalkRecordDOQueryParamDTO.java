package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 错误让行检测记录查询参数
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskErrorWaitCrossWalkRecordDOQueryParamDTO {


    /**
     * 临时事件ID查询
     */
    private String tmpCaseId;

    /**
     * 批量临时事件ID查询
     */
    @InQuery(field = "tmpCaseId")
    private List<String> tmpCaseIdList;

    /**
     * 车辆VIN码查询
     */
    private String vin;

    /**
     * 批量车辆VIN码查询
     */
    @InQuery(field = "vin")
    private List<String> vinList;

    /**
     * 事件类型查询
     */
    private Integer type;

    /**
     * 批量事件类型查询
     */
    @InQuery(field = "type")
    private List<Integer> typeList;

    /**
     * 状态查询
     */
    private Integer status;

    /**
     * 批量状态查询
     */
    @InQuery(field = "status")
    private List<Integer> statusList;

    /**
     * 事件发生时间范围
     */
    @RangeQuery(field = "occurTime")
    private TimePeriod occurTimeRange;


    /**
     * 创建时间范围
     */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    /**
     * 是否删除标记
     */
    @Builder.Default
    private Integer isDeleted = 0;
}