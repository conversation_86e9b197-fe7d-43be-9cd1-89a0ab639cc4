package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskErrorWaitCrossWalkRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.entity.RiskErrorWaitCrossWalkRecordDO;
import java.util.List;

/**
 * 错误让行检测记录仓储
 */
public interface RiskErrorWaitCrossWalkRecordRepository {

    /**
     * 根据参数查询列表
     *
     * @param paramDTO 查询参数
     * @return 数据对象列表
     */
    List<RiskErrorWaitCrossWalkRecordDO> queryByParam(RiskErrorWaitCrossWalkRecordDOQueryParamDTO paramDTO);

    /**
     * 根据临时事件ID查询对象
     *
     * @param tmpCaseId 临时事件ID
     * @return 数据对象
     */
    RiskErrorWaitCrossWalkRecordDO getByTmpCaseId(String tmpCaseId);

    /**
     * 分页查询
     *
     * @param paramDTO 查询参数
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    Paging<RiskErrorWaitCrossWalkRecordDO> queryByParamByPage(RiskErrorWaitCrossWalkRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize);

    /**
     * 保存对象
     *
     * @param riskErrorWaitCrossWalkRecordDO 数据对象
     */
    void save(RiskErrorWaitCrossWalkRecordDO riskErrorWaitCrossWalkRecordDO);

    /**
     * 批量保存对象
     *
     * @param riskErrorWaitCrossWalkRecordDOList 数据对象列表
     */
    void batchSave(List<RiskErrorWaitCrossWalkRecordDO> riskErrorWaitCrossWalkRecordDOList);
}