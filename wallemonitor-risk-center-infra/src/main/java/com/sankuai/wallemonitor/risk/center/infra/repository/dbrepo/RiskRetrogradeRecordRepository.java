package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskRetrogradeRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskRetrogradeRecordDOQueryParamDTO;
import java.util.List;

public interface RiskRetrogradeRecordRepository {

    /**
     * 根据参数查询逆行预检过程
     *
     * @param paramDTO 查询参数
     * @return 逆行预检过程DO列表
     */
    List<RiskRetrogradeRecordDO> queryByParam(RiskRetrogradeRecordDOQueryParamDTO paramDTO);

    /**
     * 根据参数分页查询逆行预检过程
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 分页对象
     */
    Paging<RiskRetrogradeRecordDO> queryByParamByPage(RiskRetrogradeRecordDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize);

    /**
     * 保存逆行预检过程
     *
     * @param riskRetrogradeRecordDO 逆行预检过程DO
     */
    void save(RiskRetrogradeRecordDO riskRetrogradeRecordDO);

    /**
     * 批量保存逆行预检过程
     *
     * @param riskRetrogradeRecordDOList 逆行预检过程DO列表
     */
    void batchSave(List<RiskRetrogradeRecordDO> riskRetrogradeRecordDOList);
}
