package com.sankuai.wallemonitor.risk.center.infra.enums;


import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DetectRecordStatusEnum {
    UNKNOWN(0, "未知（数据库默认状态）"),
    PROCESSING(10, "确认中"),
    CONFIRMED(20, "已确认"),
    CANCELLED(99, "已取消"),
    TIMEOUT_CANCELLED(100, "超时取消");


    private int code;

    private String desc;

    public static DetectRecordStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DetectRecordStatusEnum recordStatusEnum : DetectRecordStatusEnum.values()) {
            if (recordStatusEnum.getCode() == code) {
                return recordStatusEnum;
            }
        }
        return null;
    }

    /**
     * 判断工单状态是否为已确认
     *
     * @param statusEnum
     * @return
     */
    public static Boolean isConfirmed(DetectRecordStatusEnum statusEnum) {
        return statusEnum == CONFIRMED;
    }

    /**
     * 判断工单状态是否为已取消
     *
     * @param statusEnum
     * @return
     */
    public static Boolean isCancel(DetectRecordStatusEnum statusEnum) {
        return statusEnum == CANCELLED || statusEnum == TIMEOUT_CANCELLED;
    }

    /**
     * 风险工单状态枚举转换
     *
     * @param detectRecordStatusEnum
     * @return
     */
    public static RiskCaseStatusEnum convertToRiskCaseStatus(DetectRecordStatusEnum detectRecordStatusEnum) {
        if (detectRecordStatusEnum == null) {
            return null;
        }

        switch (detectRecordStatusEnum) {
            case CONFIRMED:
                return RiskCaseStatusEnum.NO_DISPOSAL;
            case CANCELLED:
            case TIMEOUT_CANCELLED:
                return RiskCaseStatusEnum.DISPOSED;
            default:
                return null;
        }
    }

    public static List<Integer> getUnCancel() {
        return Arrays.asList(PROCESSING, UNKNOWN, CONFIRMED).stream().map(DetectRecordStatusEnum::getCode)
                .collect(Collectors.toList());

    }

    public int getCode() {
        return code;
    }
}
