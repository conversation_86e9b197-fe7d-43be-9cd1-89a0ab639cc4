package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiskSpecialAreaStrandingRecordDOQueryParamDTO {


    private String tmpCaseId;

    @InQuery(field = "tmpCaseId")
    private List<String> tmpCaseIdList;

    private String vin;

    @InQuery(field = "vin")
    private List<String> vinList;
    
    private Integer status;

    @InQuery(field = "status")
    private List<Integer> statusList;

}
