package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEventRelated;

public interface NegativePublicEventRelatedMapper extends CommonMapper<NegativePublicEventRelated> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<NegativePublicEventRelated> getPOClass() {
        return NegativePublicEventRelated.class;
    }
}