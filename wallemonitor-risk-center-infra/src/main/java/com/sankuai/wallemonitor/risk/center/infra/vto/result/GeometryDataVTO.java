package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.GeoTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

@Slf4j
public class GeometryDataVTO {

    /**
     * 如 Polygon MultiLineString
     * 
     * @see GeoTypeEnum
     *
     */
    private String type;

    /**
     * 坐标点
     */
    private List<List<Object>> coordinates;

    /**
     * 高精地图的数据，转换成内部使用的polygon
     *
     * @return
     */
    public List<PolygonDO> getPolygon(String id) {
        if (!GeoTypeEnum.isPolygon(type)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(coordinates)) {
            return new ArrayList<>();
        }
        if (coordinates.size() > 1) {
            log.warn("超过一个polygon的区域,id:{}", id);
        }

        return coordinates.stream().map(listPoints -> {
            if (CollectionUtils.isEmpty(listPoints)) {
                return null;
            }
            // 转换点
            List<PositionDO> positionDOS = listPoints.stream().map(list -> {
                // 做类型强转
                List<Double> listPoint = (List<Double>)list;
                if (CollectionUtils.isEmpty(listPoint) || listPoint.size() < 3) {
                    return null;
                }
                if (listPoint.get(0) == null || listPoint.get(1) == null) {
                    return null;
                }
                // fixme: 默认从高精地图拿到的都是wgs84的（需要在使用高精地图接口时，保证）
                return PositionDO.getPosition(listPoint.get(0), listPoint.get(1), CoordinateSystemEnum.WGS84);
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(positionDOS) || positionDOS.size() != listPoints.size()) {
                log.error("转换失败", new SystemException("转换点失败"));
                return null;
            }
            return PolygonDO.builder().points(positionDOS).coordinateSystemEnum(CoordinateSystemEnum.WGS84).build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取线形状的list
     * 
     * @return
     */
    public List<PositionDO> getLineList() {
        if (!GeoTypeEnum.isLineString(type)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(coordinates)) {
            return new ArrayList<>();
        }
        return coordinates.stream().map(listPoint -> {
            if (listPoint.get(0) == null || listPoint.get(1) == null) {
                return null;
            }
            // 转成中心线
            return PositionDO.getPosition((Double)listPoint.get(0), (Double)listPoint.get(1),
                    CoordinateSystemEnum.WGS84);
        }).collect(Collectors.toList());

    }
}
