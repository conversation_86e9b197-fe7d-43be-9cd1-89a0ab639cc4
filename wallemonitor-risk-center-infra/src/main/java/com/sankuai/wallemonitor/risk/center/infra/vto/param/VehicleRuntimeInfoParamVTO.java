package com.sankuai.wallemonitor.risk.center.infra.vto.param;

import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大象推送消息入参
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleRuntimeInfoParamVTO {

    /**
     * 车架号Id
     */
    private List<String> vinList;


    /**
     * 查询时段
     */
    @Builder.Default
    private Date occurTime = new Date();


}
