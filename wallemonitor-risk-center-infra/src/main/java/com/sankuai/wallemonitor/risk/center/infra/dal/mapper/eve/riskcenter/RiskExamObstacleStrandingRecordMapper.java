package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskExamObstacleStrandingRecord;

/**
 * <p>
 * 考试障碍物停滞事件检测过程表 Mapper 接口
 * </p>
 */
public interface RiskExamObstacleStrandingRecordMapper extends CommonMapper<RiskExamObstacleStrandingRecord> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskExamObstacleStrandingRecord> getPOClass() {
        return RiskExamObstacleStrandingRecord.class;
    }

} 