package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ID业务Leaf枚举
 *
 * <AUTHOR>
 * @date 2023/06/05
 */
@Getter
@AllArgsConstructor
public enum IDBizEnum {

    RISK_CASE_ID("caseId", "风险caseId"),

    EVENT_ID("eventId", "事件ID"),

    ALERT_UPGRADE_BIZ_ID("alertUpgradeBizId", "告警升级业务ID"),
    ;

    private String key;

    private String desc;
}