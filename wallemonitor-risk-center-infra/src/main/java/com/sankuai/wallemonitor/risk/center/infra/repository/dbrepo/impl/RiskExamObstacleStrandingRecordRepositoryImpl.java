package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskExamObstacleStrandingRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskExamObstacleStrandingRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskExamObstacleStrandingRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskExamObstacleStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskExamObstacleStrandingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskExamObstacleStrandingRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskExamObstacleStrandingRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskExamObstacleStrandingRecordMapper, RiskExamObstacleStrandingRecordConvert, RiskExamObstacleStrandingRecord, RiskExamObstacleStrandingRecordDO> implements
        RiskExamObstacleStrandingRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询考试障碍物停滞事件检测记录
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskExamObstacleStrandingRecordDO> queryByParam(RiskExamObstacleStrandingRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数分页查询考试障碍物停滞事件检测记录
     *
     * @param paramDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    @RepositoryQuery
    public Paging<RiskExamObstacleStrandingRecordDO> queryByParamByPage(RiskExamObstacleStrandingRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据临时事件ID查询考试障碍物停滞事件检测记录
     *
     * @param tmpCaseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskExamObstacleStrandingRecordDO getByTmpCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }

    /**
     * 保存考试障碍物停滞事件检测记录
     *
     * @param recordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskExamObstacleStrandingRecordDO recordDO) {
        super.save(recordDO);
    }

    /**
     * 批量保存考试障碍物停滞事件检测记录
     *
     * @param recordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskExamObstacleStrandingRecordDO> recordDOList) {
        super.batchSave(recordDOList);
    }
} 