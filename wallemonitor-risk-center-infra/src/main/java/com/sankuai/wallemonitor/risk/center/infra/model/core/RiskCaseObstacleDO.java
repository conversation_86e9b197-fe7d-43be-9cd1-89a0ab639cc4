package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskObstacleContextInfoDTO;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 风险事件障碍物信息DO对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskCaseObstacleDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * caseId
     */
    @DomainUnique
    private String caseId;

    /**
     * 障碍物信息
     */
    private RiskObstacleContextInfoDTO obstacle;

    /**
     * 创建时间
     */
    @DomainUnique
    private Date createTime;

    /**
     * 最近更新时间
     */
    private Date updateTime;

    /**
     * 是否删除,0|未删除,1|已删除
     */
    private Boolean isDeleted;
}