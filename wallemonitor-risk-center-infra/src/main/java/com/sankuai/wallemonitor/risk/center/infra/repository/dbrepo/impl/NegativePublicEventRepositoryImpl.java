package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.NegativePublicEventConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.NegativePublicEventMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEvent;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventDOQueryParamDTO;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NegativePublicEventRepositoryImpl extends
        AbstractMapperSingleRepository<NegativePublicEventMapper, NegativePublicEventConvert, NegativePublicEvent, NegativePublicEventDO>
        implements NegativePublicEventRepository {

    /**
     * 自增ID
     */
    private static final String UK_NEGATIVE_PUBLIC_EVENT_ID = "id";

    /**
     * 事件ID
     */
    private static final String UK_NEGATIVE_PUBLIC_EVENT_EVENT_ID = "eventId";

    /**
     * 保存负外部性事件
     *
     * @param negativePublicEventDO
     */
    @Override
    @RepositoryExecute
    public void save(NegativePublicEventDO negativePublicEventDO) {
        super.save(negativePublicEventDO);

    }

    /**
     * 根据参数查询数据
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<NegativePublicEventDO> queryByParam(NegativePublicEventDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    @Override
    @RepositoryQuery
    public Paging<NegativePublicEventDO> queryByParamByPage(NegativePublicEventDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据事件ID查询数据
     *
     * @param eventId
     * @return
     */
    @Override
    @RepositoryQuery
    public NegativePublicEventDO getByEventId(Long id, String eventId) {
        if (Objects.nonNull(id)) {
            return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                    .columnPOName(UK_NEGATIVE_PUBLIC_EVENT_ID)
                    .value(id)
                    .build()));
        }
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_NEGATIVE_PUBLIC_EVENT_EVENT_ID)
                .value(eventId)
                .build()));
    }

}
