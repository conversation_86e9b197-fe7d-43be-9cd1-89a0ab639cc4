package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.ReTicketResponse;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.ReTicketVTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Re 工单查询适配器
 * 文档链接: https://km.sankuai.com/collabpage/1424097545
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Component
@Slf4j
public class ReTicketAdapter {

    private static final String PATH = "/ticket/api/v1/vehicleState/ba";
    public static final String QUERY_PARAM_KEY_VEHICLE_NAME_LIST = "vehicleNameList";
    public static final String HEADER_AUTHORIZATION = "Authorization";

    /**
     * Re 工单状态
     */
    public static final String RE_ORDER_STATUS = "ok";

    @Value("${reTicket.queryDomain}")
    private String domain;

    @Value("$KMS{re.ticket.query.authorization}")
    private String authorization;

    @Resource
    private DelivererQueryThriftServiceAdaptor delivererQueryThriftServiceAdaptor;

    /**
     * 根据车名列表查询RE工单列表
     *
     * @param vehicleNameList 车名列表
     * @return RE工单列表
     */
    public List<ReTicketVTO> queryByVehicleNameList(List<String> vehicleNameList) {
        if (CollectionUtils.isEmpty(vehicleNameList)) {
            return Collections.emptyList();
        }

        Map<String, List<String>> requestParams = new HashMap<>();
        requestParams.put(QUERY_PARAM_KEY_VEHICLE_NAME_LIST, vehicleNameList);

        try {
            log.info("queryByVehicleNameList request, requestParams: {}", JacksonUtils.to(requestParams));
            EveHttpResponse<ReTicketResponse> response = HttpUtils.postJson(JacksonUtils.to(requestParams), domain, PATH, Collections.singletonMap(HEADER_AUTHORIZATION, authorization), ReTicketResponse.class);
            log.info("queryByVehicleNameList response: {}", JacksonUtils.to(response));
            if (!Objects.equals(response.getCode(), CommonConstant.HTTP_SUCCESS_CODE) || !Objects.equals(response.getData().getCode(), CommonConstant.HTTP_SUCCESS_CODE)) {
                log.info("queryByVehicleNameList request fail, vehicleNameList: {}, response: {}",
                        JacksonUtils.to(vehicleNameList), JacksonUtils.to(response));
            }

            return Optional.ofNullable(response.getData()).map(ReTicketResponse::getData).orElse(Collections.emptyList());
        } catch (Exception exception) {
            log.error("fail in queryByVehicleNameList with exception, vehicleNameList: {}",
                    JacksonUtils.to(vehicleNameList), exception);
        }

        return Collections.emptyList();
    }

    /**
     * 根据vin列表查询RE工单状态
     *
     * @param vinList
     * @return
     */
    public Map<String, Boolean> queryByVinList(List<String> vinList) {
        Map<String, String> vehicleName2VinMap = delivererQueryThriftServiceAdaptor.getVehicleName2VinMap(vinList);
        if (MapUtils.isEmpty(vehicleName2VinMap)) {
            return Collections.emptyMap();
        }
        Map<String, Boolean> vin2ReStatusMap = new HashMap<>();
        List<ReTicketVTO> reTicketVTOList = queryByVehicleNameList(new ArrayList<>(vehicleName2VinMap.keySet()));
        for (ReTicketVTO reTicketVTO : reTicketVTOList) {
            Boolean reStatus = !Objects.equals(reTicketVTO.getVehicleStatus(), RE_ORDER_STATUS);
            vin2ReStatusMap.put(vehicleName2VinMap.get(reTicketVTO.getVehicleName()), reStatus);
        }
        return vin2ReStatusMap;
    }


}
