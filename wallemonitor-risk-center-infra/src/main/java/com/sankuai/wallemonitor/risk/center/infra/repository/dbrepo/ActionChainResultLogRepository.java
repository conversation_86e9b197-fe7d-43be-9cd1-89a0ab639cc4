package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.ActionChainResultLogDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.LogActionChainResultQueryParamDTO;
import java.util.List;

public interface ActionChainResultLogRepository {

    /**
     * 保存
     *
     * @param resultDO
     */
    void save(ActionChainResultLogDO resultDO);

    /**
     * 根据参数查询
     *
     * @param param
     * @return
     */
    List<ActionChainResultLogDO> queryByParam(LogActionChainResultQueryParamDTO param);


}
