package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.UserNoticeReadRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.UserNoticeReadRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.UserNoticeReadRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.UserNoticeReadRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.UserNoticeReadRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.UserNoticeReadRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UserNoticeReadRecordRepositoryImpl extends
        AbstractMapperSingleRepository<UserNoticeReadRecordMapper, UserNoticeReadRecordConvert, UserNoticeReadRecord, UserNoticeReadRecordDO>
        implements UserNoticeReadRecordRepository {

    /**
     * 保存用户阅读须知记录
     *
     * @param userNoticeReadRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(UserNoticeReadRecordDO userNoticeReadRecordDO) {
        super.save(userNoticeReadRecordDO);

    }

    /**
     * 查询用户须知阅读记录
     *
     * @param userNoticeReadRecordDOQueryParamDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<UserNoticeReadRecordDO> queryByParam(
            UserNoticeReadRecordDOQueryParamDTO userNoticeReadRecordDOQueryParamDTO) {
        return super.queryByParam(userNoticeReadRecordDOQueryParamDTO);
    }

    /**
     * 批量更新/保存用户须知阅读记录
     *
     * @param readRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<UserNoticeReadRecordDO> readRecordDOList) {
        super.batchSave(readRecordDOList);
    }

}
