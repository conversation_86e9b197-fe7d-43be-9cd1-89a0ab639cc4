package com.sankuai.wallemonitor.risk.center.infra.utils.dal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * table公共工具
 */
@Slf4j
public class TableUtils {

    /**
     * 键值对的size
     */
    private static final int PAIR_SIZE = 2;

    /**
     * 获取字段名
     *
     * @param tClass
     * @param fieldName
     * @param <T>
     * @return
     */
    public static <T> String getTableFieldName(Class<T> tClass, String fieldName) {
        Field field = null;
        try {
            field = tClass.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            log.error("获取表字段失败", e);
        }
        String tableFieldName = field != null && field.isAnnotationPresent(TableField.class) ?
                field.getAnnotation(TableField.class).value() :
                (field != null ? StringUtils.camelToUnderline(field.getName()) : null);
        return "`" + tableFieldName + "`";
    }

    public static <T> List<String> getTableFieldList(Class<T> tClass) {
        List<String> allTableFieldNames = new ArrayList<>();
        try {
            Field[] fields = ReflectUtils.getDeclaredFields(tClass, false);
            for (Field field : fields) {
                if (!field.isAnnotationPresent(TableField.class) && !field.isAnnotationPresent(TableId.class)) {
                    continue;
                }
                allTableFieldNames.add(getTableFieldName(tClass, field.getName()));
            }
            return allTableFieldNames;
        } catch (Exception e) {
            log.error("获取表字段失败", e);
        }
        return allTableFieldNames;
    }

    /**
     * 获取字段的更新时间，去掉
     *
     * @param <T>
     * @param po
     * @return
     */
    public static <T> List<String> getTableExpiredKey(T po, List<String> ukList) {
        if (!po.getClass()
                .isAnnotationPresent(TableName.class)) {
            //无需过滤或者无入口时间或者不知道表名
            return null;
        }
        //获取表名
        String tableName = po.getClass().getAnnotation(TableName.class).value();
        //获取类里面的全部字段
        Field[] fields = ReflectUtils.getDeclaredFields(po.getClass(), false);
        //找到获得主键的value 的合并值
        String ukValue = Arrays.stream(fields).filter(field -> ukList.contains(field.getName()))
                .map(field -> ReflectUtils.getValueByField(field, po))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.joining(CharConstant.CHAR_XH));
        CheckUtil.isNotBlank(ukValue, "主键值不能为空");
        //获取这行记录的各个要更新字段的key
        return Arrays.stream(fields)
                //不要包含主键的key
                .map(field -> Joiner.on(CharConstant.CHAR_JH)
                        //表名#字段#主键values
                        .join(tableName, field.getName(), ukValue))
                .collect(Collectors.toList());
    }

    /**
     * 获取字段的更新时间，去掉
     *
     * @param <T>
     * @param po
     * @return
     */
    public static <T> Map<String, Object> getTableExpiredKeyAndValue(T po, Set<String> ukList) {
        if (!po.getClass()
                .isAnnotationPresent(TableName.class)) {
            //无需过滤或者无入口时间或者不知道表名
            return new HashMap<>();
        }
        //获取表名
        String tableName = po.getClass().getAnnotation(TableName.class).value();
        //获取类里面的全部字段
        Field[] fields = ReflectUtils.getDeclaredFields(po.getClass(), false);
        //找到获得主键的value 的合并值
        String mergeUkValue = Arrays.stream(fields).filter(field -> ukList.contains(field.getName()))
                .map(field -> ReflectUtils.getValueByField(field, po))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.joining(CharConstant.CHAR_XH));
        Map<String, Object> keyAndValue = new HashMap<>();
        CheckUtil.isNotBlank(mergeUkValue, "主键值不能为空");
        ReflectUtils.getNonNullFieldAndValue(po).forEach((field, value) -> {
            String expiredKey = Joiner.on(CharConstant.CHAR_JH)
                    //表名#字段#主键values,value为具体的值
                    .join(tableName, field, mergeUkValue);
            keyAndValue.put(expiredKey, value);
        });
        //获得每个字段最新的时间
        return keyAndValue;
    }

    /**
     * 获取主键
     *
     * @param poClass
     * @param <T>
     * @return
     */
    public static <T> List<String> getTableUniqueKeyList(Class<T> poClass) {
        CheckUtil.isNotNull(poClass, "类型获取失败");
        CheckUtil.isTrue(poClass.isAnnotationPresent(TableName.class), "非表po对象");
        Field[] fields = ReflectUtils.getDeclaredFields(poClass, false);
        List<String> fieldNames = new ArrayList<>();
        for (Field field : fields) {
            if (field.isAnnotationPresent(TableUnique.class)) {
                fieldNames.add(field.getName()); // 如果字段含有特定注解，则添加其名称到列表中
            }
        }
        CheckUtil.isNotEmpty(fieldNames, "数据实体{}无主键标识", poClass.getName());
        return fieldNames;
    }

    /**
     * 获取uk键和其对应的值
     *
     * @param args
     * @param <T>
     * @return
     */
    public static <T> List<UniqueKeyDTO> getTableUniqueKeyList(String... args) {
        CheckUtil.isNotNull(args, "参数不能为空");
        CheckUtil.isTrue(args.length % PAIR_SIZE == 0, "参数和值必须成对出现");
        //每两个元素成对
        List<UniqueKeyDTO> uniqueKeyDTOList = new ArrayList<>();
        for (int i = 0; i < args.length; i += PAIR_SIZE) {
            String key = args[i];
            String value = args[i + 1];
            uniqueKeyDTOList.add(UniqueKeyDTO.builder().columnPOName(key).value(value).build());
        }
        return uniqueKeyDTOList;

    }

    /**
     * 获取表名
     *
     * @param aClass
     * @return
     */
    public static String getTableName(Class<?> aClass) {
        CheckUtil.isNotNull(aClass, "参数类不能为空");
        return Arrays.stream(aClass.getAnnotations())
                .filter(annotation -> annotation.getClass().isAssignableFrom(TableName.class)).findFirst()
                .map(annotation -> (TableName)(annotation)).map(TableName::value)
                // 如果没有表名注解，使用类名转下划线
                .orElse(StringUtils.camelToUnderline(aClass.getSimpleName()));

    }
}
