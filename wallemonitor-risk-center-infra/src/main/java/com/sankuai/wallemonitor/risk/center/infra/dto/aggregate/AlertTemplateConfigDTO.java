package com.sankuai.wallemonitor.risk.center.infra.dto.aggregate;

import com.sankuai.wallemonitor.risk.center.infra.enums.AlertImageTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TemplateFieldEnum;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 告警模板配置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertTemplateConfigDTO {

    /**
     * 标题
     */
    private String title;

    /**
     * 大象卡片模板ID
     */
    private String templateId;

    /**
     * 图片类型
     * @see AlertImageTypeEnum
     */
    private String imageType;

    /**
     * 要展示的字段列表
     * @see TemplateFieldEnum
     */
    private List<String> showFieldList;

    /**
     * 告警群组ID列表
     */
    private List<Long> groupIdList;

    /**
     * 按钮文本
     */
    private String buttonText;

    /**
     * 提示文本
     */
    private String promptText;

    /**
     * 组长名字
     */
    private List<String> masterName;

    /**
     * 是否需要升级
     */
    private Integer isUpdate;

    /**
     * 升级阈值
     */
    private Integer upgradeThreshold;

    /**
     * 校验配置是否有效
     */
    public boolean isValid() {
        return StringUtils.isNotBlank(templateId)
                && CollectionUtils.isNotEmpty(groupIdList)
                && StringUtils.isNotBlank(title);
    }
} 