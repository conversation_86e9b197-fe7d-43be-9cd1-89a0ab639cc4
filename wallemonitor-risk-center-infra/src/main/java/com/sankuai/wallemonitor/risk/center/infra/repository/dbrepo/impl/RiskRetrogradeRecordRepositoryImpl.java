package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskRetrogradeRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskRetrogradeRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskRetrogradeRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskRetrogradeRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskRetrogradeRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskRetrogradeRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 逆行预检过程仓储实现
 */
@Component
@Slf4j
public class RiskRetrogradeRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskRetrogradeRecordMapper, RiskRetrogradeRecordConvert, RiskRetrogradeRecord, RiskRetrogradeRecordDO> implements
        RiskRetrogradeRecordRepository {

    /**
     * 根据参数查询逆行预检过程
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskRetrogradeRecordDO> queryByParam(RiskRetrogradeRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询逆行预检过程 (分页)
     *
     * @param paramDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public Paging<RiskRetrogradeRecordDO> queryByParamByPage(RiskRetrogradeRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 保存逆行预检过程
     *
     * @param riskRetrogradeRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskRetrogradeRecordDO riskRetrogradeRecordDO) {
        super.save(riskRetrogradeRecordDO);
    }

    /**
     * 批量保存逆行预检过程
     *
     * @param riskRetrogradeRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskRetrogradeRecordDO> riskRetrogradeRecordDOList) {
        super.batchSave(riskRetrogradeRecordDOList);
    }
}
