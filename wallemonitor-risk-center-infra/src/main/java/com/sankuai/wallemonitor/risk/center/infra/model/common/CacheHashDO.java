package com.sankuai.wallemonitor.risk.center.infra.model.common;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CacheHashDO {

    private String key;

    /**
     * 字段值
     */
    @Builder.Default
    private Map<String, Object> data = new HashMap<>();

    /**
     * 更新时间
     */
    @Builder.Default
    private Map<String, Long> updateTime = new HashMap<>();


    public Long getFieldUpdateTime(String fieldName) {
        if (MapUtils.isEmpty(updateTime)) {
            return null;
        }
        return updateTime.get(fieldName);
    }

    public void addField(String field, Object value, Long thisUpdateTime) {
        data.put(field, value);
        updateTime.put(field, thisUpdateTime);
    }

    public Map<String, Object> getAllField() {
        Map<String, Object> result = new HashMap<>();
        if (MapUtils.isEmpty(data)) {
            return result;
        }
        data.forEach((k, v) -> {
            Long thisUpdateTime = updateTime.get(k);
            if (thisUpdateTime != null) {
                result.put(k, v);
                result.put(k + "Timestamp", thisUpdateTime);
            }
        });
        //时间
        return result;

    }
}
