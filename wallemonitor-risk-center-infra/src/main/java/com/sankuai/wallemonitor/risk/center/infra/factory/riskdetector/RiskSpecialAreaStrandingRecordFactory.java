package com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector;

import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskSpecialAreaStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class RiskSpecialAreaStrandingRecordFactory extends RiskDetectorRecordFactory<RiskSpecialAreaStrandingRecordDO> {

    @Resource
    private IDGenerateRepository idGenerateRepository;


    @Override
    public RiskSpecialAreaStrandingRecordDO init(DetectContextDTO detectContextDTO) {
        VehicleRuntimeInfoContextDO runtimeContextDO = detectContextDTO.toShortVehicleInfoSnapShot();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, runtimeContextDO.getVin(),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.SPECIAL_AREA_STRANDING,
                runtimeContextDO.getLastUpdateTime());
        String areaType = "CONSTRUCTION_AREA";  // TODO 目前只有施工区域，后续再根据信息扩展
        return RiskSpecialAreaStrandingRecordDO.builder()
                .tmpCaseId(caseId)
                .type(RiskCaseTypeEnum.SPECIAL_AREA_STRANDING)
                .vin(runtimeContextDO.getVin())
                .areaType(areaType)
                .duration(0)
                .status(DetectRecordStatusEnum.PROCESSING)
                .occurTime(runtimeContextDO.getLastUpdateTime())
                .build();
    }


}
