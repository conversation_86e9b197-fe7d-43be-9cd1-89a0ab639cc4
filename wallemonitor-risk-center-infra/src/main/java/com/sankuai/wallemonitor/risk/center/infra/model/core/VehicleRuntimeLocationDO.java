package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 车辆实时定位数据
 * </p>
 *
 * <AUTHOR> @since 2025-03-17
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VehicleRuntimeLocationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车辆
     */
    @DomainUnique
    private String vin;

    /**
     * 车辆坐标(WGS84)
     */
    private PositionDO location;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private Boolean isDeleted;

}
