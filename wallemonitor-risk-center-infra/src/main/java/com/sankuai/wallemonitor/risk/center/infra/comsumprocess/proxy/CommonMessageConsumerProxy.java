package com.sankuai.wallemonitor.risk.center.infra.comsumprocess.proxy;

import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.consumer.IDeadLetterListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.comsumprocess.CommonMessageConsumer;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.MafkaMessageConsumerConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.LogCenterFieldEnum;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 消息消费代理类
 */
public class CommonMessageConsumerProxy {

    private static final Logger logger = LoggerFactory.getLogger(CommonMessageConsumerProxy.class);

    private CommonMessageConsumer commonMessageConsumer;

    private IConsumerProcessor consumerProcessor;

    private IDeadLetterListener deadLetterListener;

    @SneakyThrows
    public CommonMessageConsumerProxy(CommonMessageConsumer commonMessageConsumer,
            MafkaMessageConsumerConfigDTO consumerConfigDTO) {
        this.commonMessageConsumer = commonMessageConsumer;
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, consumerConfigDTO.getNameSpace());
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, consumerConfigDTO.getAppKey());
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, consumerConfigDTO.getGroup());
        properties.setProperty("openHook", "true");

        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        // 创建topic对应的consumer对象 死信
        consumerProcessor = MafkaClient.buildDeadLetterConsumerFactory(properties,
                consumerConfigDTO.getTopicName());
        deadLetterListener = new IDeadLetterListener() {
            @Override
            public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
                // 返回状态说明：①返回CONSUME_SUCCESS，表示消费成功准备消费下一条消息。
                // ②返回RECONSUME_LATER，表示请求再次消费该消息，默认最多三次，然后跳过此条消息的消费，开始消费下一条。(算上初始最多消费4次）
                // ③返回CONSUMER_FAILURE，表示请求继续消费，直到消费成功。
                // 注意：如果不想在消费异常时一直进行重试，造成消息积压，可以返回RECONSUME_LATER，详细设置可以看下右上角HELP文档->高阶特性->消费异常重试次数设置
                Exception exec = null;
                Map<String, Object> logMap = new HashMap<>();
                logMap.put(LogCenterFieldEnum.AL_PROVIDER.getFieldCode(), consumerConfigDTO.getGroup());
                logMap.put(LogCenterFieldEnum.AL_REQUEST.getFieldCode(), message.getBody().toString());
                Long startTime = System.currentTimeMillis();
                try {
                    ConsumeStatus consumeStatus = commonMessageConsumer.consume(message.getBody().toString());
                    if (consumeStatus == ConsumeStatus.CONSUME_FAILURE) {
                        // 失败的时候，进行投递
                        if (!handleRetry(message.getBody().toString(), consumerConfigDTO.getDeadLetterDelayMills())) {
                            // retry 里面已经有失败投递了，只要retry里面成功，走下面的返回即可,失败才需要返回CONSUME_FAILURE
                            return ConsumeStatus.CONSUME_FAILURE;
                        } else {
                            // 只要retry成功，直接返回SUCCESS即可
                            return ConsumeStatus.CONSUME_SUCCESS;
                        }
                    }
                    logMap.put(LogCenterFieldEnum.AL_RESPONSE.getFieldCode(), consumeStatus);
                    return consumeStatus;
                } catch (Exception e) {
                    // 发生异常记录
                    exec = e;
                    if (!handleRetry(message.getBody().toString(), consumerConfigDTO.getDeadLetterDelayMills())) {
                        // RETRY 里面已经有失败投递了，只要retry里面成功，走下面的返回即可,失败才需要返回CONSUME_FAILURE
                        return ConsumeStatus.CONSUME_FAILURE;
                    } else {
                        // 只要retry成功，直接返回SUCCESS即可
                        return ConsumeStatus.CONSUME_SUCCESS;
                    }
                } finally {
                    logMap.put(LogCenterFieldEnum.AL_COST.getFieldCode(), System.currentTimeMillis() - startTime);
                    logger.info(XMDLogFormat.build().putJson(JacksonUtils.to(logMap)).putTraceID().message(
                            Optional.ofNullable(exec).map(Exception::getMessage).orElse(CharConstant.CHAR_EMPTY)));
                }
            }

            /**
             * 处理死信投递
             * 
             * @param messageBody
             * @param delayMills
             * @return
             */
            private boolean handleRetry(String messageBody, long delayMills) {
                try {
                    return retry(messageBody, delayMills);
                } catch (Exception e) {
                    logger.error("死信消息重试失败", e);
                    return false;
                }
            }
        };
        // 调用recvMessageWithParallel设置listener
        // 注意1：可以修改String.class以支持自定义数据类型
        // 注意2：针对同一个consumer对象，只能调用一次该方法；多次调用的话，后面的调用都会报异常
        consumerProcessor.recvMessageWithParallel(String.class, deadLetterListener);
    }

}
