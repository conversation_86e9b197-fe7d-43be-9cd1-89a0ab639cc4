package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum HdMapEnum {
    /**
     * 内部道路
     */
    LANE("lane"),

    /**
     * 加载polygon
     */
    LANE_POLYGON("lane_polygon"),
    /**
     * 路口
     */
    JUNCTION("junction"),
    /**
     * 信号
     */
    SIGNAL("signal"),
    /**
     * 物体
     */
    OBJECT("object")

    ;

    private final String value;

    public String getValue() {
        return value;
    }

    public static HdMapEnum fromValue(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        for (HdMapEnum type : HdMapEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}