package com.sankuai.wallemonitor.risk.center.infra.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RandomForestPredictConfigDTO {

    /**
     * 模型路径
     */
    private String modelPath;

    /**
     * 数据结构路径
     */
    private String modelStructurePath;

    /**
     * 判定阈值
     */
    private Double threshold;

    /**
     * 地图元素查询范围
     */
    private Double mapElementQueryDistance;
}
