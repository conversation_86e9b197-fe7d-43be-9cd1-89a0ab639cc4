package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventDOQueryParamDTO;
import java.util.List;

public interface NegativePublicEventRepository {

    /**
     * 保存负外部性事件
     *
     * @param negativePublicEventDO
     */
    void save(NegativePublicEventDO negativePublicEventDO);

    /**
     * 查询负外部性事件
     *
     * @param paramDTO
     * @return
     */
    List<NegativePublicEventDO> queryByParam(NegativePublicEventDOQueryParamDTO paramDTO);

    /**
     * 根据参数查询负外部性事件 (分页)
     *
     * @param paramDTO
     * @return
     */
    Paging<NegativePublicEventDO> queryByParamByPage(NegativePublicEventDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize);


    /**
     * 根据事件id查询负外部性事件
     *
     * @return
     */
    NegativePublicEventDO getByEventId(Long id, String eventId);

}
