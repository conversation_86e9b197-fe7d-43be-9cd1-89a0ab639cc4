package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskLaunchTimeoutRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskLaunchTimeoutRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskLaunchTimeoutRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskLaunchTimeoutRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskLaunchTimeoutRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskLaunchTimeoutRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskLaunchTimeoutRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskLaunchTimeoutRecordMapper, RiskLaunchTimeoutRecordConvert, RiskLaunchTimeoutRecord, RiskLaunchTimeoutRecordDO> implements
        RiskLaunchTimeoutRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询风险停滞事件记录
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskLaunchTimeoutRecordDO> queryByParam(RiskLaunchTimeoutRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询风险停滞事件记录 (分页)
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public Paging<RiskLaunchTimeoutRecordDO> queryByParamByPage(RiskLaunchTimeoutRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据临时事件ID查询风险停滞事件记录
     *
     * @param tmpCaseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskLaunchTimeoutRecordDO getByTmpCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }

    /**
     * 保存风险停滞事件记录
     *
     * @param riskLaunchTimeoutRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskLaunchTimeoutRecordDO riskLaunchTimeoutRecordDO) {
        super.save(riskLaunchTimeoutRecordDO);
    }

    /**
     * 批量保存风险停滞事件记录
     *
     * @param riskLaunchTimeoutRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskLaunchTimeoutRecordDO> riskLaunchTimeoutRecordDOList) {
        super.batchSave(riskLaunchTimeoutRecordDOList);
    }
}