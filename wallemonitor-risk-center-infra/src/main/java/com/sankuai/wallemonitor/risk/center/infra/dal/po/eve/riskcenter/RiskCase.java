package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 安全风险事件表
 * </p>
 *
 * <AUTHOR> @since 2024-06-12
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("risk_case")
public class RiskCase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * caseId
     */
    @TableField("case_id")
    @TableUnique
    private String caseId;

    /**
     * 风险事件类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 业务站ID
     */
    @TableField("place_code")
    private String placeCode;

    /**
     * 状态，10-待处置|20-处置中|30-已解除（包含已取消）
     */
    @TableField("status")
    private Integer status;

    /**
     * 播报消息ID
     */
    @TableField("message_id")
    private String messageId;

    /**
     * 播报消息最新版本号
     */
    @TableField("message_version")
    private String messageVersion;

    /**
     * 外部事件ID
     */
    @TableField("event_id")
    private String eventId;

    /**
     * 风险等级
     */
    @TableField("level")
    private Integer level;

    /**
     * 外部事件来源，1-保障系统|2-车端PNC|3-运维状态监控
     */
    @TableField("source")
    private String source;

    /**
     * 事件发生时间
     */
    @TableField("occur_time")
    private Date occurTime;

    /**
     * 事件召回时间
     */
    @TableField("recall_time")
    private Date recallTime;

    /**
     * 事件完结时间
     */
    @TableField("close_time")
    private Date closeTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最近更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 拓展信息
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 是否呼叫云控[0-未呼叫|1-已呼叫|99-已取消]
     */
    @TableField("mrm_called")
    private Integer mrmCalled;

    /**
     * 呼叫[0|未呼叫，10|已呼叫，99|已取消]
     */
    @TableField("call_safety")
    private Integer callSafety;

    /**
     * 实时回传任务ID
     */
    @TableField("fast_upload_id")
    private String fastUploadId;

    /**
     * 事发地点
     */
    @TableField("poi_name")
    private String poiName;


}