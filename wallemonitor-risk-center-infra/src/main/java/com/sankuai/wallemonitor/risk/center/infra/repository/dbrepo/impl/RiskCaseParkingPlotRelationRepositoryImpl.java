package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseParkingPlotRelationConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCaseParkingPlotRelationMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseParkingPlotRelation;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseParkingPlotRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseParkingPlotRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseParkingPlotRelationDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 泊车失败事件与泊位ID关联Repository实现类
 */
@Repository
@Slf4j
public class RiskCaseParkingPlotRelationRepositoryImpl extends
        AbstractMapperSingleRepository<RiskCaseParkingPlotRelationMapper, RiskCaseParkingPlotRelationConvert, RiskCaseParkingPlotRelation, RiskCaseParkingPlotRelationDO>
        implements RiskCaseParkingPlotRelationRepository {

    private static final String UK_CASE_ID = "caseId";

    /**
     * 根据参数查询泊车失败事件与泊位ID关联关系
     *
     * @param paramDTO 查询参数
     * @return 关联关系列表
     */
    @Override
    @RepositoryQuery
    public List<RiskCaseParkingPlotRelationDO> queryByParam(RiskCaseParkingPlotRelationDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据caseId查询泊车失败事件与泊位ID关联关系
     *
     * @param caseId 事件ID
     * @return 关联关系DO
     */
    @Override
    @RepositoryQuery
    public RiskCaseParkingPlotRelationDO getByCaseId(String caseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_CASE_ID)
                .value(caseId)
                .build()));
    }

    /**
     * 保存泊车失败事件与泊位ID关联关系
     *
     * @param relationDO 关联关系DO
     */
    @Override
    @RepositoryExecute
    public void save(RiskCaseParkingPlotRelationDO relationDO) {
        super.save(relationDO);
    }

    /**
     * 批量保存泊车失败事件与泊位ID关联关系
     *
     * @param relationDOList 关联关系DO列表
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskCaseParkingPlotRelationDO> relationDOList) {
        super.batchSave(relationDOList);
    }
} 