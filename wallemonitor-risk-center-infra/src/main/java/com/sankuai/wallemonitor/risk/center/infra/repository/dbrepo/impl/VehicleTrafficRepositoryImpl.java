package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.SquirrelCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleTrafficRepository;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class VehicleTrafficRepositoryImpl implements
        VehicleTrafficRepository {

    @Resource
    private SquirrelAdapter squirrelAdapter;

    @ConfigValue(key = LionKeyConstant.TRAFFIC_LIGHT_CONFIG, defaultValue = "{}")
    private TrafficHistoryConfigDTO trafficHistoryConfig;

    /**
     * 交通灯hmi
     */
    private static final String TRAFFIC_HMI_LIGHT = "traffic_hmi_light";

    /**
     * 交通灯hmi_history
     */
    private static final String TRAFFIC_HMI_LIGHT_HISTORY = "traffic_hmi_light_history";


    /**
     * 交通灯prediction_history
     */
    private static final String TRAFFIC_PREDICTION_LIGHT_HISTORY = "traffic_prediction_light_history";


    /**
     * 交通灯hmi
     */
    private static final String TRAFFIC_PREDICTION_LIGHT = "traffic_prediction_light";


    /**
     * 保存车辆红绿灯上下文
     *
     * @param vin
     * @param context
     */
    @Override
    public void saveVehicleTrafficContext(String vin, TrafficLightContextDO context) {
        try {
            if (context.getNowPlanningId() != null) {
                savePlanningContext(vin, context);
            } else if (context.getTrafficLightList() != null) {
                savePredictionContext(vin, context);
            }
        } catch (Exception e) {
            log.error("保存车辆红绿灯上下文失败", e);
        }
    }

    private void savePlanningContext(String vin, TrafficLightContextDO context) {
        squirrelAdapter.hsetStr(SquirrelCategoryEnum.VEHICLE_CONTEXT, vin, TRAFFIC_HMI_LIGHT, context);
        if (!isHistoryEnabled()) {
            return;
        }
        List<String> plannerIdList = getPlannerIdHistory(vin);
        //添加至头部
        plannerIdList.add(0, context.getNowPlanningId());
        //移除多余的
        trimHistoryList(plannerIdList);
        //保存历史意图ID
        squirrelAdapter.hsetStr(SquirrelCategoryEnum.VEHICLE_CONTEXT, vin, TRAFFIC_HMI_LIGHT_HISTORY, plannerIdList);
    }

    private void savePredictionContext(String vin, TrafficLightContextDO context) {
        squirrelAdapter.hsetStr(SquirrelCategoryEnum.VEHICLE_CONTEXT, vin, TRAFFIC_PREDICTION_LIGHT, context);
        if (!isHistoryEnabled()) {
            return;
        }
        //获取历史的红绿灯列表
        List<List<TrafficLightDO>> historyList = getPredictionHistory(vin);
        //添加至头部
        historyList.add(0, context.getTrafficLightList());
        //移除过长的元素
        trimHistoryList(historyList);
        //保存
        squirrelAdapter.hsetStr(SquirrelCategoryEnum.VEHICLE_CONTEXT, vin, TRAFFIC_PREDICTION_LIGHT_HISTORY,
                historyList);
    }


    private boolean isHistoryEnabled() {
        return trafficHistoryConfig != null && BooleanUtils.toBoolean(trafficHistoryConfig.getEnable());
    }

    private List<String> getPlannerIdHistory(String vin) {
        List<String> history = squirrelAdapter.hgetStrAndDeserialization(
                SquirrelCategoryEnum.VEHICLE_CONTEXT, vin,
                TRAFFIC_HMI_LIGHT_HISTORY, new TypeReference<List<String>>() {
                });
        return history != null ? history : new ArrayList<>();
    }

    private List<List<TrafficLightDO>> getPredictionHistory(String vin) {
        List<List<TrafficLightDO>> history = squirrelAdapter.hgetStrAndDeserialization(
                SquirrelCategoryEnum.VEHICLE_CONTEXT, vin,
                TRAFFIC_PREDICTION_LIGHT_HISTORY, new TypeReference<List<List<TrafficLightDO>>>() {
                });
        return history != null ? history : new ArrayList<>();
    }

    private void trimHistoryList(List list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //删除至当前长度
        while (list.size() > trafficHistoryConfig.getHistorySize()) {
            list.remove(list.size() - 1);
        }
    }

    /**
     * 获取车辆红绿灯上下文
     *
     * @param vin
     */
    @Override
    public TrafficLightContextDO getVehicleTrafficContext(String vin) {
        try {
            TrafficLightContextDO planningLight = squirrelAdapter.hgetStrAndDeserialization(
                    SquirrelCategoryEnum.VEHICLE_CONTEXT, vin, TRAFFIC_HMI_LIGHT, TrafficLightContextDO.class);
            TrafficLightContextDO predictionLight = squirrelAdapter.hgetStrAndDeserialization(
                    SquirrelCategoryEnum.VEHICLE_CONTEXT, vin, TRAFFIC_PREDICTION_LIGHT, TrafficLightContextDO.class);
            if (planningLight == null || predictionLight == null) {
                return null;
            }
            Long planningLightUpdateTime = planningLight.getUpdateTime();
            Long predictionLightUpdateTime = predictionLight.getUpdateTime();
            if (planningLightUpdateTime == null || predictionLightUpdateTime == null) {
                //没有时间戳的情况
                return null;
            }
            //时间戳差值超过10s，认为数据过期
            long diff = planningLightUpdateTime - predictionLightUpdateTime;
            if (Math.abs(diff) / 1000 > 30) {
                //如果时间戳之间差异过大，无需返回
                return null;
            }
            long min = Math.min(planningLightUpdateTime, predictionLightUpdateTime);
            Long max = Math.max(planningLightUpdateTime, predictionLightUpdateTime);
            long diffFromNow = System.currentTimeMillis() - min;
            if (Math.abs(diffFromNow) / 1000 > 30) {
                //如果和当前差距过大
                return null;
            }
            List<String> hmiIdHistoryList = new ArrayList<>();
            List<List<TrafficLightDO>> predictionHistoryLight = new ArrayList<>();
            if (isHistoryEnabled()) {
                //如果打开了这里进行赋值
                hmiIdHistoryList = getPlannerIdHistory(vin);
                predictionHistoryLight = getPredictionHistory(vin);
            }
            return TrafficLightContextDO.builder()
                    .trafficLightList(predictionLight.getTrafficLightList())
                    .nowPlanningId(planningLight.getNowPlanningId())
                    .historyTrafficHmiIdList(hmiIdHistoryList)
                    .historyTrafficLightList(predictionHistoryLight)
                    .updateTime(max)
                    .build();
        } catch (Exception e) {
            log.error("获取车辆红绿灯上下文失败", e);
        }
        return null;

    }

    /**
     * 批量获取车辆红绿灯上下文
     *
     * @param vinList 车辆vin列表
     * @return
     */
    @Override
    public Map<String, TrafficLightContextDO> batchGetVehicleTrafficContext(List<String> vinList) {

        if (CollectionUtils.isEmpty(vinList)) {
            return Collections.emptyMap();
        }
        Map<String, TrafficLightContextDO> result = new HashMap<>();
        for (String vin : vinList) {
            TrafficLightContextDO trafficLightContextDO = getVehicleTrafficContext(vin);
            if (Objects.isNull(trafficLightContextDO)) {
                continue;
            }
            result.put(vin, trafficLightContextDO);
        }
        log.info("batchGetVehicleTrafficContext result:{}", result);
        return result;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class TrafficHistoryConfigDTO {

        /**
         * 历史记录大小
         */
        private Integer historySize;

        /**
         * 是否开启
         */
        private Boolean enable;


    }
}
