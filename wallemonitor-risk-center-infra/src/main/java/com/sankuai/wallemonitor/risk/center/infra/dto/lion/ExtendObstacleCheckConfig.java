package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import java.util.HashSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 传递自车配置
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ExtendObstacleCheckConfig {

    /**
     * 是否开启
     */
    private boolean enable;

    /**
     * 间隔距离
     */
    private Double distance;

    /**
     * 检索距离
     */
    @Default
    private Double searchDistance = 50D;

    /**
     * 自车类型
     */
    private Set<String> fineType;

    /**
     * 需要的check 配置
     */
    @Default
    private Set<String> checkCategory = new HashSet<>();

    public Boolean isAutoCar(String obFineType) {
        if (obFineType == null || CollectionUtils.isEmpty(fineType)) {
            return false;
        }
        return fineType.contains(obFineType);

    }
}
