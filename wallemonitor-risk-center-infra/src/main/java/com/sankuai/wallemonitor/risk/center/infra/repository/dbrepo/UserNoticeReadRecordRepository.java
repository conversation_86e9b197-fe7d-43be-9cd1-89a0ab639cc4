package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.UserNoticeReadRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.UserNoticeReadRecordDOQueryParamDTO;
import java.util.List;

public interface UserNoticeReadRecordRepository {

    void save(UserNoticeReadRecordDO userNoticeReadRecordDO);

    List<UserNoticeReadRecordDO> queryByParam(UserNoticeReadRecordDOQueryParamDTO userNoticeReadRecordDOQueryParamDTO);

    void batchSave(List<UserNoticeReadRecordDO> readRecordDOList);
}
