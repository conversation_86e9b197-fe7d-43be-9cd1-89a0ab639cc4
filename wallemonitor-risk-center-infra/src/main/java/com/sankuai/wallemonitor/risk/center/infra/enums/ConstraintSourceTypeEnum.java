package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
@JsonDeserialize(using = ConstraintSourceTypeDeserializer.class)
public enum ConstraintSourceTypeEnum {
    UNKNOWN("未知"),
    NONE("无"),
    CROSSING("交叉"),
    MEETING("会车"),
    STATIONARY("静止"),
    PEDESTRIAN("行人"),
    PARALLEL("平行"),
    START("起步"),
    BACKUP("倒车"),
    TRAFFIC_LIGHT("交通信号灯"),
    CLEAR_AREA("清除区域"),
    DEFAULT("默认"),
    SLOW_DOWN("减速"),
    SAFEGUARD("保护"),
    MANEUVER("机动"),
    CROSSWALK("人行横道"),
    MERGE("合并"),
    DIRECTSTEER("直接转向"),
    AGGREGATOR("聚合器"),
    SAFETY_STRATEGY("安全策略"),
    PARALLEL_MANEUVER("平行机动"),
    DESTINATION("目的地"),
    REFERENCE_END("参考终点"),
    PARKING_GATE("停车门"),
    CURB("路缘"),
    PATH_CURVATURE("路径曲率"),
    LANE("车道"),
    ROAD_ELEMENT("道路元素"),
    OCCLUSION("遮挡"),
    FREESPACE_LINK("自由空间链接"),
    MOCK_BRAKE("模拟刹车"),
    SPEEDUP_UPPER_SPEED_LIMIT("加速上限"),
    NONSPEEDUP_ODD("非加速奇数"),
    TRAFFIC_SIGN("交通标志"),
    SPEED_HORIZON("速度地平线"),
    TIRE_SURFACE("轮胎表面"),
    LATERAL("横向"),
    LANE_CHANGE("变道"),
    ARBITRATION("仲裁"),
    ELECTRIC_FENCE("电子围栏"),
    ONBOARD_MENDER("车载修理"),
    DOOR_OPENING("开门"),
    CHASSIS_REQUEST("底盘请求"),
    NONSPEEDUP_ODD_NONSPEEDUP_AREA("非加速奇数非加速区域"),
    PERCEPTION_NOISE("感知噪声"),
    PERCEPTION_PUDDLE("感知水坑"),
    PERCEPTION_SNOW_COVERED("感知积雪覆盖"),
    V2X("车联网"),
    HAZARD_ROAD_SURFACE_ELEMENT("危险路面元素"),
    CONFIG_SPEED_LIMIT("配置速度限制"),
    ASSISTANCE_SPEED_LIMIT("辅助速度限制"),
    FREESPACE("自由空间"),
    CONTROL_GUARD("控制保护");

    private final String description;

}
