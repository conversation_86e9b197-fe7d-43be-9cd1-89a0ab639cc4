package com.sankuai.wallemonitor.risk.center.infra.adaptar.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudCursorResourceRequest {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 原因
     * */
    private Integer reason;

    /**
     * 来源
     * */
    private Integer source;

    /**
     * 毫秒级时间戳
     */
    private Long timestamp;

    /**
     * 请求动作
     */
    private String action;

    /**
     * 是否需要一直呼叫
     */
    private Boolean needCancelCommand;
}
