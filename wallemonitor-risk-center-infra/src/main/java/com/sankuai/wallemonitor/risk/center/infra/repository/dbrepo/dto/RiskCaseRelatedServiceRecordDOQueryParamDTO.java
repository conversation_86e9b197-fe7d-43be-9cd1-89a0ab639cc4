package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class RiskCaseRelatedServiceRecordDOQueryParamDTO {

    /**
     * 风险caseId列表
     */
    @InQuery(field = "caseId")
    private List<String> caseIdList;

    /**
     * 服务名称列表
     */
    @InQuery(field = "serviceName")
    private List<String> serviceNameList;


    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean isDeleted = false;

}
