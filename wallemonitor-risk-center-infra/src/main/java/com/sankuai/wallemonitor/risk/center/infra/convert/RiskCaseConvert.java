package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListRiskCaseVO;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseBaseInfoVO;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCase;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class}, imports = {JacksonUtils.class,
        DatetimeUtil.class})
public interface RiskCaseConvert extends SingleConvert<RiskCase, RiskCaseDO> {

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toRiskCaseStatusEnum")
    @Mapping(source = "source", target = "source", qualifiedByName = "toRiskCaseSourceEnum")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseTypeEnum")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "parseCaseExtInfo")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    @Mapping(source = "callSafety", target = "callSafety", qualifiedByName = "toCallSafetyEnum")
    @Mapping(source = "mrmCalled", target = "mrmCalled", qualifiedByName = "toRiskCaseMrmCalledStatusEnum")
    RiskCaseDO toDO(RiskCase riskCase);

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toRiskCaseStatus")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(source = "source", target = "source", qualifiedByName = "toRiskCaseSource")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "serializeRiskCaseExtInfo")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    @Mapping(source = "callSafety", target = "callSafety", qualifiedByName = "toCallSafetyEnumInteger")
    @Mapping(source = "mrmCalled", target = "mrmCalled", qualifiedByName = "toMrmCalled")
    RiskCase toPO(RiskCaseDO riskCaseDO);


    /**
     * 转换成Case详情基础信息视图对象
     *
     * @return
     */
    @Mapping(target = "type", source = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(target = "status", source = "status", qualifiedByName = "toRiskCaseStatus")
    @Mapping(target = "source", source = "source", qualifiedByName = "toRiskCaseSource")
    @Mapping(target = "mrmCalledStatus", source = "mrmCalled", qualifiedByName = "toMrmCalled")
    @Mapping(target = "callSafety", source = "callSafety", qualifiedByName = "toCallSafetyEnumInteger")
    @Mapping(target = "mrmCalled", source = "mrmCalled", qualifiedByName = "toMrmCalledBoolean")
    @Mapping(target = "occurTime", expression = "java(DatetimeUtil.formatTime(riskCaseDO.getOccurTime()))")
    @Mapping(target = "recallTime", expression = "java(DatetimeUtil.formatTime(riskCaseDO.getRecallTime()))")
    @Mapping(target = "closeTime", expression = "java(DatetimeUtil.formatTime(riskCaseDO.getCloseTime()))")
    @Mapping(target = "extInfo", expression = "java(JacksonUtils.to(riskCaseDO.getExtInfo()))")
    RiskCaseBaseInfoVO toRiskCaseBaseInfoVO(RiskCaseDO riskCaseDO);

    /**
     * 转换成管理后台列表视图对象
     *
     * @return
     */
    @Mapping(target = "type", source = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(target = "status", source = "status", qualifiedByName = "toRiskCaseStatus")
    @Mapping(target = "source", source = "source", qualifiedByName = "toRiskCaseSource")
    @Mapping(target = "occurTime", expression = "java(DatetimeUtil.formatTime(riskCaseDO.getOccurTime()))")
    @Mapping(target = "recallTime", expression = "java(DatetimeUtil.formatTime(riskCaseDO.getRecallTime()))")
    @Mapping(target = "closeTime", expression = "java(DatetimeUtil.formatTime(riskCaseDO.getCloseTime()))")
    @Mapping(target = "extInfo", expression = "java(JacksonUtils.to(riskCaseDO.getExtInfo()))")
    @Mapping(target = "mrmCalledStatus", source = "mrmCalled", qualifiedByName = "toMrmCalled")
    @Mapping(target = "callSafety", source = "callSafety", qualifiedByName = "toCallSafetyEnumInteger")
    @Mapping(target = "mrmCalled", source = "mrmCalled", qualifiedByName = "toMrmCalledBoolean")
    @Mapping(target = "position", source = "extInfo", qualifiedByName = "toPosition")
    @Mapping(ignore = true, target = "vehicleList")
    @Mapping(ignore = true, target = "markInfo")
    AdminListRiskCaseVO toAdminListRiskCaseVO(RiskCaseDO riskCaseDO);
}
