package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.google.common.collect.Sets;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CloudEventTypeEnum {
    //逆行 - 40
    REVERSE(40, RiskCaseTypeEnum.RETROGRADE, "逆行"),
    //施工区域停滞 - 41
    SPECIAL_AREA_STRANDING(41, RiskCaseTypeEnum.SPECIAL_AREA_STRANDING, "施工区域停滞"),
    //非法压线 - 42
    DRIVE_ON_TRAFFIC_LINE(42, RiskCaseTypeEnum.DRIVE_ON_TRAFFIC_LINE, "非法压线");

    private static final Set<CloudEventTypeEnum> HIGH_NAGATIVE_EVENT_TYPE = Sets.newHashSet(REVERSE,
            SPECIAL_AREA_STRANDING, DRIVE_ON_TRAFFIC_LINE);

    private Integer code;
    private RiskCaseTypeEnum riskCaseTypeEnum;
    private String desc;

    public static CloudEventTypeEnum getByCode(Integer code) {
        for (CloudEventTypeEnum value : CloudEventTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static CloudEventTypeEnum getByRiskCaseType(RiskCaseTypeEnum riskCaseTypeEnum) {
        for (CloudEventTypeEnum value : CloudEventTypeEnum.values()) {
            if (value.getRiskCaseTypeEnum().equals(riskCaseTypeEnum)) {
                return value;
            }
        }
        return null;
    }


    public static Boolean isHighNegative(CloudEventTypeEnum cloudEventTypeEnum) {
        return HIGH_NAGATIVE_EVENT_TYPE.contains(cloudEventTypeEnum);
    }
}
