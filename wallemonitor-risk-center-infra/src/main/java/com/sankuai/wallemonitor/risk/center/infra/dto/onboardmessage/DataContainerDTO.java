package com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Time;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataContainerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private PlannerMasterState plannerMasterState;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PlannerMasterState implements Serializable {
        private static final long serialVersionUID = 1L;
        private PlannerWorldState plannerWorldState;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PlannerWorldState implements Serializable {
        private static final long serialVersionUID = 1L;
        private PlannerHistory plannerHistory;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PlannerHistory implements Serializable {
        private static final long serialVersionUID = 1L;
        private List<OptionalTrafficFlowState> trafficFlowStateBuffer;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OptionalTrafficFlowState implements Serializable {
        private static final long serialVersionUID = 1L;
        private Boolean hasValue;
        private Double currentTime;

        private Double[] tSamples;

        private List<TrafficFlowObstacleFeature> trafficFlowObstacleFeatures;

        public boolean validTrafficFlowFeatures() {
            if(CollectionUtils.isEmpty(trafficFlowObstacleFeatures)) {
                return false;
            }
            return trafficFlowObstacleFeatures.stream()
                    .filter(Objects::nonNull).anyMatch(trafficFlowObstacleFeature ->
                            CollectionUtils.isNotEmpty(trafficFlowObstacleFeature.getTrafficFlowObstacles()));
        }

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TrafficFlowObstacleFeature implements Serializable {
        private static final long serialVersionUID = 1L;
        private Integer trafficFlowId;

        private List<TrafficFlowObstacle> trafficFlowObstacles;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TrafficFlowObstacle implements Serializable {
        private static final long serialVersionUID = 1L;
        private Integer obstacleId ;

        private Boolean isVisible;

        private Boolean isOn;

        private List<OnTrafficFlowEvent> onTrafficFlowEvents;


        public Double getObstacleSpeed() {
            if (CollectionUtils.isEmpty(onTrafficFlowEvents)) {
                return null;
            }
        
            return onTrafficFlowEvents.stream()
                    .filter(event -> event != null &&  event.getStartSpeed() != null && event.getEndSpeed() != null)
                    .min((e1, e2) -> {
                        if (e1.getTimeRange() == null || e2.getTimeRange() == null) {
                            return 0;
                        }
                        return e2.getTimeRange().compareTo(e1.getTimeRange());
                    })
                    .map(event -> (event.getStartSpeed() + event.getEndSpeed()) / 2)
                    .orElse(null);
        } 
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OnTrafficFlowEvent implements Serializable {
        private static final long serialVersionUID = 1L;
        private Double startSpeed;

        private Double endSpeed;

        private Double endDeltaRouteSToEgo;

        private TimeRange timeRange;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TimeRange implements  Serializable, Comparable<TimeRange> {
        private static final long serialVersionUID = 1L;

        private Double end;

        private Double start;




        @Override
        public int compareTo(TimeRange o) {
            return Double.compare(start, o.getStart());
        }
    }


}
