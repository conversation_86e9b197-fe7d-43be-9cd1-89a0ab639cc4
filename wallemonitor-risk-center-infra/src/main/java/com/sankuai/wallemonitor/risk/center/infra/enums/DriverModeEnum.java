package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Sets;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DriverModeEnum {
    UNKNOWN(0, "未知"),
    AUTONOMOUS_DRIVING(1, "自动驾驶状态"),
    CLOUD_SOFT_TAKEOVER(2, "云控软接管状态"),
    CLOUD_HARD_TAKEOVER(3, "云控硬接管状态"),
    MANUAL_CONTROL(4, "手动控制状态"),
    NO_CONTROL(5, "无控制状态"),
    DRIVER_DRIVING(6, "人驾状态");
    private final int code;
    private final String description;

    /**
     * 根据code获取对应的ControlStateEnum枚举值
     *
     * @param code
     * @return
     */
    public static DriverModeEnum fromCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        for (DriverModeEnum state : DriverModeEnum.values()) {
            if (state.getCode() == code) {
                return state;
            }
        }
        return UNKNOWN;

    }

    /**
     * 是否是人为介入的模式
     *
     * @param driverModeEnum
     * @return
     */
    public static Boolean isManualDriverMode(DriverModeEnum driverModeEnum) {
        Set<DriverModeEnum> driverModeSet = Sets.newHashSet(CLOUD_SOFT_TAKEOVER, CLOUD_HARD_TAKEOVER, MANUAL_CONTROL,
                DRIVER_DRIVING);
        return driverModeSet.contains(driverModeEnum);
    }

    /**
     * 是否是无控制模式
     *
     * @param driverModeEnum
     * @return
     */
    public static Boolean isUncontrolledDriverMode(DriverModeEnum driverModeEnum) {
        Set<DriverModeEnum> driverModeSet = Sets.newHashSet(NO_CONTROL);
        return driverModeSet.contains(driverModeEnum);
    }
}