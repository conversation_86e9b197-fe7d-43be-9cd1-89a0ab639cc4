package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskSpecialAreaStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskSpecialAreaStrandingRecordDOQueryParamDTO;
import java.util.List;

/**
 * 特殊区域停滞记录仓储接口
 */
public interface RiskSpecialAreaStrandingRecordRepository {

    /**
     * 根据参数查询特殊区域停滞记录
     *
     * @param paramDTO 查询参数
     * @return 特殊区域停滞记录列表
     */
    List<RiskSpecialAreaStrandingRecordDO> queryByParam(RiskSpecialAreaStrandingRecordDOQueryParamDTO paramDTO);

    /**
     * 根据参数分页查询特殊区域停滞记录
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    Paging<RiskSpecialAreaStrandingRecordDO> queryByParamByPage(RiskSpecialAreaStrandingRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize);

    /**
     * 根据临时事件ID查询特殊区域停滞记录
     *
     * @param tmpCaseId 临时事件ID
     * @return 特殊区域停滞记录
     */
    RiskSpecialAreaStrandingRecordDO getByTmpCaseId(String tmpCaseId);

    /**
     * 保存特殊区域停滞记录
     *
     * @param recordDO 特殊区域停滞记录
     */
    void save(RiskSpecialAreaStrandingRecordDO recordDO);

    /**
     * 批量保存特殊区域停滞记录
     *
     * @param recordDOList 特殊区域停滞记录列表
     */
    void batchSave(List<RiskSpecialAreaStrandingRecordDO> recordDOList);
}