package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CallSafetyEnum {
    NOT_CALLED(0, "未呼叫"),
    CALLED(10, "已呼叫"),
    CANCELLED(99, "已取消");

    private final int code;
    private final String description;

    public static CallSafetyEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CallSafetyEnum value : CallSafetyEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid CallSafety code: " + code);
    }
}