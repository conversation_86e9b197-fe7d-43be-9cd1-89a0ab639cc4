package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GeoRangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GeoQueryDO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleRuntimeLocationDOQueryParamDTO {

    @InQuery(field = "vin")
    private List<String> vinList;

    @GeoRangeQuery(field = "location")
    private GeoQueryDO locationQuery;
}
