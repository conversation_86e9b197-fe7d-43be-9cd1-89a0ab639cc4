package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.inf.leaf.thrift.IDGen;
import com.sankuai.inf.leaf.thrift.Result;
import com.sankuai.inf.leaf.thrift.Status;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteErrorException;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

/**
 * ID生成器防腐
 *
 * <AUTHOR>
 * @Date 2022/5/21
 */
@Slf4j
@Component
public class IDGenerateAdapter {

    private static final int INVOKE_RETRY_TIME = 5;

    @ThriftClientProxy(remoteAppKey = "com.sankuai.leaf.service.common")
    private IDGen.Iface idGen;

    /**
     * 获取单个ID
     *
     * @param leafKey
     * @return
     */
    public String generateId(String leafKey) {
        return nextSnowflakeId(leafKey);
    }


    /**
     * 批量获取ID
     *
     * @param leafKey
     * @param num
     * @return
     */
    public List<String> batchGenerateIdList(String leafKey, int num) {
        return batchGenNextSnowFlake(leafKey, num);
    }


    /**
     * 使用leaf snowflake模式获取id,超时将进行重试
     */
    private String nextSnowflakeId(String leafKey) {
        int retryTime = 0;
        while (++retryTime <= INVOKE_RETRY_TIME) {
            try {
                Result result = idGen.getSnowFlake(leafKey);
                log.info("leaf id {}调用结果: {}", leafKey, result);
                if (Status.SUCCESS.equals(result.getStatus())) {
                    return String.valueOf(result.getId());
                } else {
                    log.error("leaf id生成异常, 异常码:" + result.getId());
                    throw new RemoteErrorException("leaf id生成异常, 异常码:" + result.getId());
                }
            } catch (TException e) {
                log.warn("IOException", e);
                if (e.getMessage() != null && e.getMessage().contains("timeout")) {
                    continue;
                }
                //非超时异常，或因超时重试达到最大重试次数
                if ((e.getMessage() != null && !e.getMessage().contains("timeout"))
                        || retryTime >= INVOKE_RETRY_TIME) {
                    throw new RemoteErrorException("leaf id生成错误");
                }
            }
        }
        throw new RemoteErrorException("leaf id生成错误");
    }

    /**
     * 使用leaf snowflake模式获取id,超时将进行重试
     */
    private List<String> batchGenNextSnowFlake(String leafKey, int num) {
        int retryTime = 0;
        while (++retryTime <= INVOKE_RETRY_TIME) {
            try {
                List<Result> resultList = idGen.getSnowFlakeBatch(leafKey, num);
                log.info("leaf id调用结果: {}", resultList);
                if (CollectionUtils.isEmpty(resultList) || resultList.size() < num) {
                    throw new RemoteErrorException("leaf id生成异常,数量错误");
                }
                List<String> newFlakeIdList = new ArrayList<>();
                resultList.stream().forEach(result -> {
                    if (Status.SUCCESS.equals(result.getStatus())) {
                        newFlakeIdList.add(String.valueOf(result.getId()));
                    } else {
                        log.error("leaf id生成异常, 异常码:" + result.getId());
                        throw new RemoteErrorException("leaf id生成异常, 异常码:" + result.getId());
                    }
                });
                return newFlakeIdList;
            } catch (TException e) {
                log.warn("IOException", e);
                if (e.getMessage() != null && e.getMessage().contains("timeout")) {
                    continue;
                }
                //非超时异常，或因超时重试达到最大重试次数
                if ((e.getMessage() != null && !e.getMessage().contains("timeout")) || retryTime >= INVOKE_RETRY_TIME) {
                    throw new RemoteErrorException("leaf id生成错误");
                }
            }
        }
        throw new RemoteErrorException("leaf id生成错误");
    }
}
