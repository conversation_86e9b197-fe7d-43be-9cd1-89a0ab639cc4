package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.SFTFridayVideoClassifyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ViewEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteCallFridayApiTimeoutException;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamWithMessageVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayPromptVTO.FridayPromptPartDTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.FridayVerifyResultVTO;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.sankuai.wallemonitor.risk.center.infra.vto.result.SFTFridayVerifyRiskResultVTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.runtime.parser.node.ASTIntegerRange;
import org.bouncycastle.cert.ocsp.Req;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FridayOpenAiAdapter {


    @ConfigValue(key = LionKeyConstant.SFT_FRIDAY_VIDEO_CLASSIFY)
    private SFTFridayVideoClassifyConfigDTO sftFridayVideoClassifyConfigDTO;


    public static final String CASE_URL = "https://eve.meituan.com/fe-panel-risk/index.html#/risk/caseList?openCaseId=%s";

    public static final String FRIDAY_DOMAIN = "https://aigc.sankuai.com";

    public static final String GPT_REQ_PATH = "/v1/openai/native/chat/completions";


    public static final String IMAGE_URL_TEMPLATE = "https://walle.sankuai.com/replay/video/avatarV2?vin=%s&view=%s&time=%s";
    
    /**
     * SFT结果解析配置
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SFTParseConfig {
        /**
         * 需要接管的标识值
         */
        private String takeOverValue;
        
        /**
         * 不需要接管的标识值
         */
        private String notTakeOverValue;
        
        /**
         * 需要接管时的默认分类
         */
        private ISCheckCategoryEnum defaultTakeOverCategory;
        
        /**
         * 不需要接管时的默认分类
         */
        private ISCheckCategoryEnum defaultNotTakeOverCategory;
    }



    


    public List<String> buildImageContext(List<Content> userContentList, String vin, Date occurTime) {
        // 前N s
        int endNSeconds = sftFridayVideoClassifyConfigDTO.getEndNSeconds();

        // 后 N s
        int startNSeconds = - sftFridayVideoClassifyConfigDTO.getStartNSeconds();

        // step
        int step = sftFridayVideoClassifyConfigDTO.getStep();

        List<String> imageUrlList = new ArrayList<>();

        for (int i = startNSeconds; i <= endNSeconds; i += step) {
            Date nSecondsBeforeDateTime = DatetimeUtil.getNSecondsBeforeDateTime(occurTime, i);
            String imageUrl = String.format(IMAGE_URL_TEMPLATE, vin, "front", DatetimeUtil.getDateKey(nSecondsBeforeDateTime));
            Content imageContent = Content.builder().type("image_url").imageUrl(ImageUrl.builder().url(imageUrl).build()).build();
            imageUrlList.add(imageUrl);
            userContentList.add(imageContent);
        }

        imageUrlList.parallelStream().forEach(img -> {
            try {
                HttpUtils.getImage(img);
            } catch (IOException e) {
                log.error("url = {} 读取图片异常", img);
            }
        });


        return imageUrlList;
    }


    /**
     * 顺次调用
     */
    public FridayVerifyResultVTO verify(FridayModelParamWithMessageVTO requestDTO) {
        //1 查询当前小时的停滞，使用http的方法
        CheckUtil.isNotNull(requestDTO, "请求参数不能为空");
        String vin = requestDTO.getVin();
        String caseId = requestDTO.getCaseId();
        Date occurTime = requestDTO.getOccurTime();
        List<ReqMessage> messages = new ArrayList<>();
        //合并system请求
        ReqMessage systemMessage = mergeMessageList("system", requestDTO.toReqMessageList());
        messages.add(systemMessage);
//        ReqMessage userMessage = mergeMessageList("user", requestDTO.toUserReqMessageList());
        // ReqMessage stepMessage = mergeMessageList("user", requestDTO.toExampleReqMessageList());
        //添加用户态的信息
        // messages.add(stepMessage);
        // messages.add(userMessage);
        //添加用户态的信息
        messages.addAll(requestDTO.toExampleReqMessageList());
        messages.addAll(requestDTO.toUserReqMessageList());
        //生成图片
        List<String> imageUrl = requestDTO.toUserReqMessageList().stream()
                .map(ReqMessage::getContent)
                .flatMap(List::stream)
                .filter(c -> !c.getType().equals("text"))
                .parallel().peek(img -> {
                    try {
                        HttpUtils.get(Collections.emptyMap(), img.getImageUrl().getUrl(), Collections.emptyMap(),
                                new TypeReference<Map<String, Object>>() {
                                });
                    } catch (Exception e) {
                        //log.warn("读取图片异常");
                    }
                }).map(c -> c.getImageUrl().getUrl()).collect(Collectors.toList());
        //获取参数
        OpenAiReqDTO reqDTO = buildOpenAiAnswerReqDTO(messages, requestDTO.getModelName());
        //调用
        OpenAiRespDTO respDTO = getOpenAiAnswer(reqDTO, requestDTO.getAppId(), requestDTO.getTimeout());
        if (respDTO == null) {
            return null;
        }
        //获取结果
        String content = respDTO.getChoices().stream().findFirst().map(Choice::getMessage)
                .map(RespMessage::getContent).orElse(CharConstant.CHAR_EMPTY);
        OpenApiJsonResult result = JacksonUtils.from(content, OpenApiJsonResult.class);

        return FridayVerifyResultVTO.builder().vin(vin).tiktokens(respDTO.getUsage().getTotalTokens())
                .occurTime(occurTime)
                .caseId(caseId).casePlatform(String.format(CASE_URL, caseId)).content(content)
                .imageList(imageUrl)
                //物体
                .frontObjectList(result.getObjectNameListByView(ViewEnum.FRONT.getCode()))
                .backObjectList(result.getObjectNameListByView(ViewEnum.BACK.getCode()))
                .leftObjectList(result.getObjectNameListByView(ViewEnum.LEFT.getCode()))
                .rightObjectList(result.getObjectNameListByView(ViewEnum.RIGHT.getCode()))
                .loopObjectList(result.getObjectNameListByView(ViewEnum.LOOP.getCode()))
                //场景
                .sceneList(result.getSceneNameList())
                //行为
                .activityList(result.getActivityNameList())
                .build();

    }


    /**
     * 多轮对话
     *
     * @param requestDTO
     * @param function
     * @return
     */
    public FridayVerifyResultVTO verifyByMultiRound(FridayModelParamVTO requestDTO,
            java.util.function.Function<OpenApiContext, List<FridayPromptPartDTO>> function) {

        String vin = requestDTO.getVin();
        String caseId = requestDTO.getCaseId();
        Date occurTime = requestDTO.getOccurTime();
        //最终的结果
        FridayVerifyResultVTO result = FridayVerifyResultVTO.builder()
                .vin(vin)
                .caseId(caseId)
                .occurTime(occurTime)
                .casePlatform(String.format(CASE_URL, caseId))
                .build();
        OpenApiContext apiContext = OpenApiContext.builder()
                .appId(requestDTO.getAppId())
                .model(requestDTO.getModelName())
                .resultVTO(result)
                .topP(requestDTO.getTopP())
                .times(1)
                .build();
        List<ReqMessage> systemMessageList = requestDTO.getSystemPrompt().stream()
                .map(FridayPromptPartDTO::toReqMessage)
                .collect(
                        Collectors.toList());
        List<ReqMessage> userMessageList = requestDTO.getUserPrompt().stream()
                .map(FridayPromptPartDTO::toReqMessage)
                .collect(
                        Collectors.toList());
        ReqMessage systemMessage = mergeMessageList("system", systemMessageList);
        ReqMessage userMessage = mergeMessageList("user", userMessageList);
        apiContext.addMessage(systemMessage);
        apiContext.addMessage(userMessage);
        List<String> imageList = userMessage.getContent().stream()
                .filter(c -> !c.getType().equals("text"))
                .map(content -> content.getImageUrl().getUrl())
                .collect(Collectors.toList());
        result.addImageList(imageList);
        do {
            List<FridayPromptPartDTO> list = function.apply(apiContext);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            List<ReqMessage> messages = list.stream().map(FridayPromptPartDTO::toReqMessage)
                    .collect(Collectors.toList());
            //访问并生成图片
            fillImage(messages);
            apiContext.addMessage(mergeMessageList("user", messages));
            OpenAiReqDTO reqDTO = apiContext.buildOpenAiReqDTO();
            OpenAiRespDTO respDTO = getOpenAiAnswer(reqDTO, requestDTO.getAppId(), requestDTO.getTimeout());
            if (respDTO == null) {
                return result;
            }
            //先添加第一次的结果
            apiContext.addMessage(respDTO.getRespMessage());
            String content = StringUtils.defaultIfBlank(respDTO.getRespMessage().getContent(), CharConstant.CHAR_EMPTY);
            //填充一下
            OpenApiJsonResult apiJsonResult = JacksonUtils.from(content, OpenApiJsonResult.class);
            handleFillResult(apiJsonResult, result);
            apiContext.increase();
        } while (true);
        return result;
    }

    /**
     * 因为图片是懒加载的，访问加载图片，防止模型拿到空图片
     *
     * @param messages
     */
    private void fillImage(List<ReqMessage> messages) {
        if (CollectionUtils.isEmpty(messages)) {
            return;
        }
        messages.stream()
                .map(ReqMessage::getContent)
                .flatMap(List::stream)
                .filter(c -> !c.getType().equals("text"))
                .parallel().peek(img -> {
                    try {
                        HttpUtils.get(Collections.emptyMap(), img.getImageUrl().getUrl(), Collections.emptyMap(),
                                new TypeReference<Map<String, Object>>() {
                                });
                    } catch (Exception e) {
                        //log.warn("读取图片异常");
                    }
                }).map(c -> c.getImageUrl().getUrl()).collect(Collectors.toList());
    }



    /**
     * 填充结果
     *
     * @param apiJsonResult
     */
    private void handleFillResult(OpenApiJsonResult apiJsonResult, FridayVerifyResultVTO result) {
        if (apiJsonResult == null || result == null) {
            return;
        }
        // 使用CollectionUtils判断，为空则不设置
        if (CollectionUtils.isNotEmpty(apiJsonResult.getObjectNameListByView(ViewEnum.FRONT.getCode()))) {
            result.setFrontObjectList(apiJsonResult.getObjectNameListByView(ViewEnum.FRONT.getCode()));
        }

        if (CollectionUtils.isNotEmpty(apiJsonResult.getObjectNameListByView(ViewEnum.BACK.getCode()))) {
            result.setBackObjectList(apiJsonResult.getObjectNameListByView(ViewEnum.BACK.getCode()));
        }

        if (CollectionUtils.isNotEmpty(apiJsonResult.getObjectNameListByView(ViewEnum.LEFT.getCode()))) {
            result.setLeftObjectList(apiJsonResult.getObjectNameListByView(ViewEnum.LEFT.getCode()));
        }

        if (CollectionUtils.isNotEmpty(apiJsonResult.getObjectNameListByView(ViewEnum.RIGHT.getCode()))) {
            result.setRightObjectList(apiJsonResult.getObjectNameListByView(ViewEnum.RIGHT.getCode()));
        }

        if (CollectionUtils.isNotEmpty(apiJsonResult.getObjectNameListByView(ViewEnum.LOOP.getCode()))) {
            result.setLoopObjectList(apiJsonResult.getObjectNameListByView(ViewEnum.LOOP.getCode()));
        }

        if (CollectionUtils.isNotEmpty(apiJsonResult.getSceneNameList())) {
            result.setSceneList(apiJsonResult.getSceneNameList());
        }

        if (CollectionUtils.isNotEmpty(apiJsonResult.getActivityNameList())) {
            result.setActivityList(apiJsonResult.getActivityNameList());
        }
    }

    /**
     * 合并同角色的prompt
     *
     * @param messages
     * @return
     */
    private ReqMessage mergeMessageList(String role, List<ReqMessage> messages) {
        ReqMessage systemMessage = ReqMessage.getReqMessage(role, Lists.newArrayList(), Lists.newArrayList());
        messages.forEach(m -> {
            systemMessage.getContent().addAll(m.getContent());
        });
        return systemMessage;
    }


    public OpenAiRespDTO getOpenAiAnswer(OpenAiReqDTO openAiReqDTO, String appId, Integer timeout) {
        SystemCheckUtil.isNotNull(openAiReqDTO, "openAiReqDTO不能为空");
        SystemCheckUtil.isNotBlank(openAiReqDTO.getModel(), "model不能为空");
        SystemCheckUtil.isNotEmpty(openAiReqDTO.getMessages(), "messages不能为空");
        String body = JacksonUtils.to(openAiReqDTO);
        try {
            EveHttpResponse<OpenAiRespDTO> openAiRespDTOEveHttpResp = HttpUtils.postJson(
                    body,
                    FRIDAY_DOMAIN,
                    GPT_REQ_PATH,
                    getHeaders(appId),
                    OpenAiRespDTO.class, timeout);
            log.info("openAiRespDTOEveHttpResp:{}", JacksonUtils.to(openAiRespDTOEveHttpResp));
            OpenAiRespDTO openAiRespDTO = openAiRespDTOEveHttpResp.getData();
            if (null == openAiRespDTO) {
                log.warn(JacksonUtils.to(openAiReqDTO), new SystemException("getOpenAiAnswer, data is null!"));
                //未查到任何信息
                return null;
            }
            return openAiRespDTO;
        } catch (SocketTimeoutException e) {
            throw new RemoteCallFridayApiTimeoutException("FridayOpenAiAdapter getOpenAiAnswer timeout.");
        } catch (Exception e) {
            log.error("FridayOpenAiAdapter getOpenAiAnswer error", e);
            return null;
        }
    }


    private Map<String, String> getHeaders(String appId) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", String.format("%s", appId));
        return headers;
    }

    public OpenAiReqDTO buildOpenAiAnswerReqDTO(List<ReqMessage> messages, String model) {
        String uuid = StringUtils.defaultIfBlank(Tracer.id(), UUID.randomUUID().toString());
        OpenAiReqDTO openAiReqDTO = OpenAiReqDTO.builder()
                .model(model)
                .messages(messages)
                .responseFormat(ResponseFormatDTO.builder()
                        .build())
                .topP(0.1)
                .user(uuid)
                .build();
        log.info("openAiReqDTO:{}", JacksonUtils.to(openAiReqDTO));
        return openAiReqDTO;
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class TokenResponse {

        private String model;

        private int tokenLen;
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class OpenApiContext {

        /**
         * appId
         */
        private String appId;
        /**
         * model
         */
        private String model;
        /**
         * userId
         */
        private String userId;

        /**
         * 消息上下文
         */
        @Builder.Default
        private List<ReqMessage> messages = new ArrayList<>();

        /**
         * 最近一次的助手消息
         */
        private ReqMessage latestAssistantMessage;

        /**
         * result
         */
        private FridayVerifyResultVTO resultVTO;


        /**
         * 咨询次数
         */
        private Integer times;

        /**
         * 结果获取百分比
         */
        @Builder.Default
        private Double topP = 0.1D;

        private String uuid = UUID.randomUUID().toString();


        /**
         * 消耗的token数
         */
        private Long totalTokens;

        public void addMessage(ReqMessage systemMessage) {
            messages.add(systemMessage);
        }

        public void addMessage(RespMessage assistantMessage) {
            ReqMessage reqMessage = new ReqMessage();
            reqMessage.setRole(assistantMessage.getRole());
            reqMessage.setContent(Lists.newArrayList(Content.builder()
                    .type("text").text(assistantMessage.getContent()).build()));
            //加入到上下文里面来
            messages.add(reqMessage);
        }


        /**
         * 从上下文获取消息结果
         *
         * @return
         */
        public OpenAiReqDTO buildOpenAiReqDTO() {
            OpenAiReqDTO openAiReqDTO = OpenAiReqDTO.builder()
                    .model(model)
                    .messages(messages)
                    .responseFormat(ResponseFormatDTO.builder()
                            .build())
                    .topP(topP)
                    .user(uuid)
                    .build();
            log.info("openAiReqDTO:{}", JacksonUtils.to(openAiReqDTO));
            return openAiReqDTO;
        }

        public void increase() {
            times++;
        }
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OpenAiReqDTO {

        private String model;
        private List<ReqMessage> messages;

        /**
         * 如果设置，将发送部分消息增量，就像在 ChatGPT 中一样。令牌将在可用时作为纯数据服务器发送事件发送，流由数据终止：[DONE] 消息。 默认是 false
         */
        private boolean stream;

        @JsonProperty("response_format")
        private ResponseFormatDTO responseFormat;

        /**
         * 聊天完成时生成的最大令牌数。 输入标记和生成标记的总长度受模型上下文长度的限制。 输入长度+max_tokens应当小于上下文总长度，否则会报400 Bad Request错误
         */
        @JsonProperty("max_tokens")
        private Integer maxTokens;

        /**
         * user字段可以传输一个唯一标识的id，用于问题排查。例如traceId、uuid、requestId等。
         */
        private String user;


        @JsonProperty("top_p")
        private Double topP;


        /**
         * API 将停止生成更多令牌的最多 4 个序列。可选 默认为 null
         */
        private List<String> stop;

        /**
         * 模型请求的超时时间，单位：毫秒。
         * <p>
         * 对于自研模型，默认是10秒，对于外部模型，默认是5分钟。
         */
        private Integer timeout;

    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OpenAiRespDTO {

        private List<FridayOpenAiAdapter.Choice> choices;
        private String content;
        private Long created;
        private String id;
        private String model;
        private String object;
        private FridayOpenAiAdapter.Usage usage;


        public RespMessage getRespMessage() {
            return this.getChoices().stream().findFirst().map(Choice::getMessage).orElse(null);
        }


    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class ResponseFormatDTO {

        @Builder.Default
        private String type = "json_object";

        @JsonProperty("json_schema")
        private Map<String, Object> jsonSchema;

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class OpenApiJsonResult {

        private String description;

        @Builder.Default
        private List<OpenApiObject> frontObjectList = new ArrayList<>();

        @Builder.Default
        private List<OpenApiObject> backObjectList = new ArrayList<>();

        @Builder.Default
        private List<OpenApiObject> loopObjectList = new ArrayList<>();

        @Builder.Default
        private List<OpenApiObject> leftObjectList = new ArrayList<>();

        @Builder.Default
        private List<OpenApiObject> rightObjectList = new ArrayList<>();

        @Builder.Default
        private List<Scene> sceneList = new ArrayList<>();
        @Builder.Default
        private List<Activity> activityList = new ArrayList<>();


        public List<String> getSceneNameList() {
            if (CollectionUtils.isEmpty(sceneList)) {
                return Collections.emptyList();
            }
            return sceneList.stream().map(obj -> StringUtils.join(obj.getName(), "(", obj.getReason(), ")"))
                    .collect(Collectors.toList());
        }

        public List<String> getActivityNameList() {
            if (CollectionUtils.isEmpty(activityList)) {
                return Collections.emptyList();
            }
            return activityList.stream().sorted(Comparator.comparing(Activity::getPriority))
                    .map(obj -> StringUtils.join(obj.getName(), "(", obj.priority, "|", obj.getReason(), ")"))
                    .collect(Collectors.toList());

        }

        public List<String> getObjectNameListByView(String front) {
            List<OpenApiObject> openApiObjects = new ArrayList<>();
            switch (front) {
                case "front":
                    openApiObjects = frontObjectList;
                    break;
                case "left":
                    openApiObjects = leftObjectList;
                    break;
                case "right":
                    openApiObjects = rightObjectList;
                    break;
                case "back":
                    openApiObjects = backObjectList;
                    break;
                case "loop":
                    openApiObjects = loopObjectList;
                    break;
            }
            return openApiObjects.stream().map(OpenApiObject::getFullName)
                    .collect(Collectors.toList());
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    // 内部类 Object
    public static class OpenApiObject {

        private String name;
        private String reason;
        private String image;
        private List<String> position;

        public String getFullName() {
            return StringUtils.join(name, "(", reason, "|", position, ")");
        }

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    // 内部类 Activity
    public static class Activity {

        private String name;
        private Integer priority = 0;
        private String reason;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    // 内部类 Scene
    public static class Scene {

        private String name;
        private String reason;

    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Choice {

        @JsonProperty("finish_reason")
        private String finishReason;
        private Integer index;
        private RespMessage message;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RespMessage {

        private String content;
        private String role;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Usage {

        @JsonProperty("completion_tokens")
        private Integer completionTokens;
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReqMessage {

        private String role;
        private List<Content> content;
        private String name;
        @JsonProperty("tool_calls")
        private List<ToolCall> toolCalls;
        private String thoughts;

        /**
         * 获取系统消息
         *
         * @param singletonList
         * @return
         */
        public static ReqMessage getSystemMessage(
                List<String> singletonList) {
            return ReqMessage.builder()
                    .role("system")
                    .content(singletonList.stream().map(
                                    Content::getTextContent)
                            .collect(
                                    Collectors.toList()))
                    .build();


        }

        /**
         * 获取系统消息
         *
         * @param singletonList
         * @return
         */
        public static ReqMessage getReqMessage(
                String role,
                List<String> singletonList, List<String> imageList) {

            List<Content> contentList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(singletonList)) {
                contentList.addAll(singletonList.stream().map(
                                Content::getTextContent)
                        .collect(
                                Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(imageList)) {
                contentList.addAll(imageList.stream().map(
                                Content::getImageContent)
                        .collect(
                                Collectors.toList()));
            }
            return ReqMessage.builder()
                    .role(role)
                    .content(contentList)
                    .build();
        }

        public static ReqMessage getImageMessage(String role,
                List<String> imageList) {
            return ReqMessage.builder()
                    .role(role)
                    .content(imageList.stream().map(
                                    Content::getImageContent)
                            .collect(Collectors.toList()))
                    .build();

        }

        public static ReqMessage getUserMessage(
                List<String> singletonList) {
            return ReqMessage.builder()
                    .role("user")
                    .content(singletonList.stream().map(
                                    Content::getTextContent)
                            .collect(Collectors.toList()))
                    .build();
        }
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Content {

        private String type;
        private String text;
        @JsonProperty("image_url")
        private ImageUrl imageUrl;

        /**
         * 获取文本消息
         *
         * @return
         */
        public static Content getTextContent(
                String text) {
            return Content.builder()
                    .type("text")
                    .text(text)
                    .build();


        }

        /**
         * 获取图片消息
         *
         * @return
         */
        public static Content getImageContent(
                String imageUrl) {
            return Content.builder()
                    .imageUrl(ImageUrl.builder()
                            .detail("low")
                            .url(imageUrl)
                            .build())
                    .type("image_url")
                    .build();

        }
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ImageUrl {

        private String url;
        private String detail;

        @JsonProperty("max_pixels")
        @Builder.Default
        private Long maxPixels = 200704L;

        @JsonProperty("min_pixels")
        @Builder.Default
        private Long minPixels = 200704L;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ToolCall {

        private String id;
        private String type;
        private Function function;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Function {

        private String name;
        private String arguments;
    }
}
