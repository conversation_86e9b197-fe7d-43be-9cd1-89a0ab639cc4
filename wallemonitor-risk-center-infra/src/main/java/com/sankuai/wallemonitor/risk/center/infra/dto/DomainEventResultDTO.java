package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

/**
 * 领域事件的处理结果
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class DomainEventResultDTO {

    /**
     * 处理结果
     */
    @Builder.Default
    List<DomainEventProcessResultDO> processResult = new ArrayList<>();


    /**
     * 是否都处理成功
     *
     * @return
     */
    public boolean isAllSuccess() {
        return processResult.stream().allMatch(DomainEventProcessResultDO::getProcessResult);
    }

    /**
     * 获取无法重试的处理器
     *
     * @return
     */
    public List<String> getCannotRetryProcessResultKey() {
        if (CollectionUtils.isEmpty(processResult)) {
            return new ArrayList<>();
        }
        //过滤出来没办法重试的
        return processResult.stream()
                .filter(domainEventProcessResultDO ->
                        //必须是失败的
                        Objects.equals(domainEventProcessResultDO.getCanRetry(),
                                Boolean.FALSE))
                .map(domainEventProcessResultDO -> domainEventProcessResultDO.getEventUniqueKey())
                .collect(Collectors.toList());
    }


}
