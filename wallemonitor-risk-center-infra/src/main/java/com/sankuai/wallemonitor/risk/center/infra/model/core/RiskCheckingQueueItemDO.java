package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.enums.CheckSceneEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckingExtInfoDO;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 准风险预检项
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCheckingQueueItemDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String vin;
    /**
     * 临时风险事件ID（查是后会作为正式caseId）
     */
    private String tmpCaseId;

    /**
     * 上游输入的事件ID
     */
    private String eventId;

    private RiskCaseTypeEnum type;

    private RiskCaseSourceEnum source;

    /**
     * 召回时间
     */
    private Date recallTime;

    /**
     * 实际开始时间（如停滞不当应该为开始停滞的时间
     */
    private Date occurTime;

    /**
     * 查是的时间点
     */
    private Date confirmedTime;

    /**
     * 查否及上游解除的时间点
     */
    private Date closeTime;

    /**
     * 是否正在预检中
     */
    private Boolean checking;

    /**
     * 预检的结果
     */
    private RiskCheckResultDO checkResult;

    /**
     * 主状态
     */
    private RiskQueueStatusEnum status;

    /**
     * 取消原因：查否 | 上游取消
     */
    private String cancelReason;

    /**
     * 查是原因
     */
    private String confirmReason;

    /**
     * 当前预检轮次
     */
    private Integer round;

    /**
     * 一共需要预检多少轮
     */
    private Integer maxRound;

    /**
     * 下一轮执行时间
     */
    private Date nextRoundTime;

    private RiskCheckingExtInfoDO extInfo;

    private Date createTime;

    private Date updateTime;

    private Boolean isDeleted;

    /**
     * 更新检查结果
     *
     * @param checkResult
     */
    public void updateCheckResult(RiskCheckResultDO checkResult) {
        if (Objects.isNull(checkResult)) {
            return;
        }
        // 检查上次检查结果是否为空，如果非空放在extInfo中
        if (Objects.isNull(this.extInfo)) {
            this.extInfo = RiskCheckingExtInfoDO.builder().build();
        }

        this.checkResult = checkResult;
        this.extInfo.getLastCheckResult().add(this.checkResult);
    }

    /**
     * 轮次数据增加
     */
    public void increaseRound() {
        this.round = this.round + 1;

        // 超过最大轮次
        if (this.round >= this.maxRound && !RiskQueueStatusEnum.isTerminatedStatus(this.status)) {
            this.confirm(RiskQueueStatusEnum.CONFIRMED_TIMEOUT, "OVER_MAX_CHECKING_ROUND",
                    ISCheckCategoryEnum.CANT_FOUND_ANY);
        }
    }

    /**
     * 确认预检事件为True
     */
    public void confirm(RiskQueueStatusEnum confirmStatus, String actionName, ISCheckCategoryEnum category) {
        this.status = confirmStatus;
        this.confirmReason = actionName + "#" + category;
        this.confirmedTime = new Date();
    }

    /**
     * 取消预检事件
     */
    public void cancel(Date cancelTime, String actionName, ISCheckCategoryEnum category) {
        this.status = RiskQueueStatusEnum.CANCELLED;
        this.cancelReason = actionName + "#" + category;
        this.closeTime = cancelTime;
    }

    /**
     * 设置上一轮动态配置
     * @param
     */
    public void setNextRoundDynamic(Boolean isDynamic) {
        if (Objects.isNull(this.extInfo)) {
            this.extInfo = RiskCheckingExtInfoDO.builder().build();
        }

        this.extInfo.setIsNextRoundDynamic(isDynamic);
    }

    /**
     * 是否为动态轮次
     */
    public Boolean getNextRoundDynamic() {
        if (Objects.isNull(this.extInfo)) {
            return false;
        }

        return this.extInfo.getIsNextRoundDynamic();
    }

    public String findCaseId(String checkScene) {
        if (CheckSceneEnum.PRE_CHECK.name().equals(checkScene)) {
            return this.tmpCaseId;
        }
        return this.eventId;
    }
}
