package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.SafetyArea;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class}, imports = {JacksonUtils.class})
public interface SafetyAreaConvert extends SingleConvert<SafetyArea, SafetyAreaDO> {

    @Override
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "parseSafetyAreaExtInfo")
    @Mapping(source = "source", target = "source", qualifiedByName = "toSafetyAreaInfoSourceEnum")
    @Mapping(source = "polygon", target = "polygon", qualifiedByName = "toPolygon")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    SafetyAreaDO toDO(SafetyArea safetyArea);

    @Override
    @Mapping(source = "source", target = "source", qualifiedByName = "toSafetyAreaInfoSource")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "serializeSafetyAreaExtInfo")
    @Mapping(source = "polygon", target = "polygon", qualifiedByName = "toPolygonStr")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    SafetyArea toPO(SafetyAreaDO safetyAreaDO);

}
