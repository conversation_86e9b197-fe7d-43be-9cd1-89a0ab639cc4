package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskDetectConfirmDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.BeanUtils;


/**
 * 风险检测记录基础类
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class RiskDetectorRecordBaseDO {

    private Long id;
    @DomainUnique
    private String tmpCaseId;
    private RiskCaseTypeEnum type;
    private String vin;
    private Integer duration;
    private DetectRecordStatusEnum status;
    private VehicleRuntimeInfoContextDO vehicleRuntimeInfoSnapshot;
    
    private VehicleCounterInfoDO stagnationCounter;
    private Date occurTime;
    private Date recallTime;
    private Date closeTime;
    private String extInfo;
    private Date createTime;
    private Date updateTime;
    private IsDeleteEnum isDeleted;

    /**
     * 基于最新的车辆运行时信息更新record
     */
    public void update(VehicleRuntimeInfoContextDO runtimeInfoContextDO) {
        if (this.getOccurTime() == DatetimeUtil.ZERO_DATE) {
            this.setDuration(0);
        } else {
            this.setDuration(
                    (int) DatetimeUtil.getTimeDiff(this.getOccurTime(), runtimeInfoContextDO.getLastUpdateTime(),
                            ChronoUnit.SECONDS));
        }
    }

    /**
     * 取消
     */
    public void cancel() {
        this.setStatus(DetectRecordStatusEnum.CANCELLED);
        this.setCloseTime(new Date());
    }

    /**
     * 确认
     */
    public void confirm(RiskDetectConfirmDTO context, List<String> ignoreFields) {
        VehicleRuntimeInfoContextDO runtimeInfoContextDO = context.getRuntimeContext();
        this.setStatus(DetectRecordStatusEnum.CONFIRMED);
        this.setRecallTime(new Date());
        // 保存的时候，暂时去掉障碍物、停止墙
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDOSnapshot = VehicleRuntimeInfoContextDO.builder().build();
        BeanUtils.copyProperties(runtimeInfoContextDO, vehicleRuntimeInfoContextDOSnapshot,
                ignoreFields.toArray(new String[0]));
        this.setVehicleRuntimeInfoSnapshot(vehicleRuntimeInfoContextDOSnapshot);
    }
}
