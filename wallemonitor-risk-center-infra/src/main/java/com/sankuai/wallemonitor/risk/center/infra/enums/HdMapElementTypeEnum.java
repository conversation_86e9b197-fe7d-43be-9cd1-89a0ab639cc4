package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum HdMapElementTypeEnum {

    // -----object-----
    /**
     * 路口
     */
    JUNCTION("JUNCTION", HdMapEnum.JUNCTION),

    /**
     * 禁停区
     */
    CLEAR_AREA("CLEAR_AREA", HdMapEnum.OBJECT),

    /**
     * 公交站台
     */
    BUS_STATION("BUS_STATION", HdMapEnum.OBJECT),

    /**
     * 人行横道
     */
    CROSSWALK("CROSSWALK", HdMapEnum.OBJECT),

    /**
     * 导流带
     */
    DIVERSION_ZONE("DIVERSION_ZONE", HdMapEnum.OBJECT),

    /**
     * 安全岛
     */
    REFUGE_ISLAND("REFUGE_ISLAND", HdMapEnum.OBJECT),

    /**
     * 有轨电车区域
     */
    TRAM_DRIVE_AREA("TRAM_DRIVE_AREA", HdMapEnum.OBJECT),

    /**
     * 出入口
     */
    DISTRICT_ENTRANCE("DISTRICT_ENTRANCE", HdMapEnum.OBJECT),

    /**
     * 隔离区
     */
    ISOLATION_ZONE("ISOLATION_ZONE", HdMapEnum.OBJECT),
    // -----object-----

    // ----- 道路 --------
    CITY_DRIVING("CITY_DRIVING", HdMapEnum.LANE_POLYGON),          // 城市驾驶车道
    BIKING("BIKING", HdMapEnum.LANE_POLYGON),                      // 自行车道
    SIDEWALK("SIDEWALK", HdMapEnum.LANE_POLYGON),                  // 人行道
    PARKING("PARKING", HdMapEnum.LANE_POLYGON),                    // 停车车道
    MIXED("MIXED", HdMapEnum.LANE_POLYGON),                        // 混合车道
    BUFFER("BUFFER", HdMapEnum.LANE_POLYGON),                      // 缓冲区
    EMERGENCY("EMERGENCY", HdMapEnum.LANE_POLYGON),                // 紧急车道
    EMERGENCY_PARKING_STRIP("EMERGENCY_PARKING_STRIP", HdMapEnum.LANE_POLYGON), // 紧急停车带
    // ----- 道路 --------
    ;

    private final String value;

    private final HdMapEnum mapValue;

    public static String getMapNameByArea(String s) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        HdMapElementTypeEnum hdMapElementEnum = fromValue(s);
        if (Objects.isNull(hdMapElementEnum)) {
            return null;
        }
        return hdMapElementEnum.getMapValue().getValue();
    }

    /**
     * 获取全部的lane的类型
     * 
     * @return
     */
    public static List<String> getLaneType() {
        return Arrays.stream(HdMapElementTypeEnum.values()).filter(e -> e.getMapValue().equals(HdMapEnum.LANE_POLYGON))
                .map(HdMapElementTypeEnum::getValue).collect(Collectors.toList());
    }

    public static List<String> getElementByMapType(String mapType) {
        if (StringUtils.isBlank(mapType)) {
            return Collections.emptyList();
        }
        return Arrays.stream(HdMapElementTypeEnum.values()).filter(e -> e.getMapValue().getValue().equals(mapType))
                .map(HdMapElementTypeEnum::getValue).collect(Collectors.toList());
    }

    public String getValue() {
        return value;
    }

    public static HdMapElementTypeEnum fromValue(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        for (HdMapElementTypeEnum type : HdMapElementTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}