package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskLaunchTimeoutRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskLaunchTimeoutRecordDOQueryParamDTO;
import java.util.List;

/**
 * 风险停滞事件记录仓储接口
 */
public interface RiskLaunchTimeoutRecordRepository {

    /**
     * 根据参数查询风险停滞事件记录
     *
     * @param paramDTO 查询参数
     * @return 风险停滞事件记录列表
     */
    List<RiskLaunchTimeoutRecordDO> queryByParam(RiskLaunchTimeoutRecordDOQueryParamDTO paramDTO);

    /**
     * 根据参数分页查询风险停滞事件记录
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 分页对象
     */
    Paging<RiskLaunchTimeoutRecordDO> queryByParamByPage(RiskLaunchTimeoutRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize);

    /**
     * 根据临时事件ID查询风险停滞事件记录
     *
     * @param tmpCaseId 临时事件ID
     * @return 风险停滞事件记录
     */
    RiskLaunchTimeoutRecordDO getByTmpCaseId(String tmpCaseId);

    /**
     * 保存风险停滞事件记录
     *
     * @param riskLaunchTimeoutRecordDO 风险停滞事件记录
     */
    void save(RiskLaunchTimeoutRecordDO riskLaunchTimeoutRecordDO);

    /**
     * 批量保存风险停滞事件记录
     *
     * @param riskLaunchTimeoutRecordDOList 风险停滞事件记录列表
     */
    void batchSave(List<RiskLaunchTimeoutRecordDO> riskLaunchTimeoutRecordDOList);
}
