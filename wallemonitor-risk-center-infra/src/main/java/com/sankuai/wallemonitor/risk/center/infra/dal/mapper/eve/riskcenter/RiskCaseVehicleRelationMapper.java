package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseVehicleRelation;

/**
 * <p>
 * 风险事件和车辆关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-06-12
 */
public interface RiskCaseVehicleRelationMapper extends CommonMapper<RiskCaseVehicleRelation> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskCaseVehicleRelation> getPOClass(){
        return RiskCaseVehicleRelation.class;
    }
}