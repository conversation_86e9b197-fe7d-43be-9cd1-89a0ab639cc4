package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallSafetyEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MenderOperationTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCaseExtInfoDO;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseDO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * caseId
     */
    private String caseId;

    /**
     * 风险事件类型
     */
    private RiskCaseTypeEnum type;

    /**
     * 业务站ID
     */

    private String placeCode;

    /**
     * 状态，10-待处置|20-处置中|30-已解除（包含已取消）
     */
    private RiskCaseStatusEnum status;

    /**
     * 播报消息ID
     */
    private String messageId;

    /**
     * 播报消息最新版本号
     */
    private String messageVersion;

    /**
     * 外部事件ID
     */
    private String eventId;

    /**
     * 外部事件来源，1-保障系统|2-车端PNC|3-运维状态监控
     */
    private RiskCaseSourceEnum source;

    /**
     * 事件召回时间
     */
    private Date recallTime;

    /**
     * 事件发生时间
     */
    private Date occurTime;

    /**
     * 事件完结时间
     */
    private Date closeTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最近更新时间
     */
    private Date updateTime;

    /**
     * 拓展信息
     */
    private RiskCaseExtInfoDO extInfo;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 是否删除
     */
    @Builder.Default
    private IsDeleteEnum isDeleted = IsDeleteEnum.NOT_DELETED;

    /**
     * 是否呼叫云控
     */
    private RiskCaseMrmCalledStatusEnum mrmCalled;


    /**
     * 呼叫云安全状态
     */
    private CallSafetyEnum callSafety;

    /**
     * 快传数据id
     */
    private String fastUploadId;

    /**
     * 事发地点
     */
    private String poiName;

    /**
     * 更新状态,如果状态为起始或者，完成，更新相应的时间
     *
     * @param status
     */
    public void updateStatus(RiskCaseStatusEnum status, Long timestamp) {
        if (!Objects.equals(this.status, status) && !RiskCaseStatusEnum.isTerminal(this.status)) {
            //如果状态没变化或者已解除，不需要继续更新
            this.status = status;
        }
        //根据状态，刷新时间
        updateTime(timestamp);
    }

    public PositionDO getLocation() {
        if(this.getExtInfo() == null) {
            return null;
        }
        return this.getExtInfo().getPosition();
    }

    public boolean isInSafetyArea(SafetyAreaDO safetyAreaDO) {
        String areaType = safetyAreaDO.getType();
        // 如果不是延迟等待区，只能直接判断是否在polygon内
        if(!MenderOperationTypeEnum.IMPROPER_STRANDING_DELAY_RECALL.getName().equals(areaType)) {
            return safetyAreaDO.isInPolygon(this.getLocation());
        }

        // 如果是延迟等待区，可以先判断POI是否相等，加速判断
        return StringUtils.equals(safetyAreaDO.getDescription(), this.getPoiName())
                && safetyAreaDO.isInPolygon(this.getLocation());

    }

    /**
     * ====================私有层=======================
     */

    /**
     * 根据状态，刷新时间
     *
     * @param timestamp
     */
    private void updateTime(Long timestamp) {
        switch (status) {
            case DISPOSED:
            case CANCEL:
            case MANUAL_DISPOSED: {
                if (this.closeTime == null || DatetimeUtil.isDbZeroTime(this.closeTime)) {
                    this.closeTime = new Date(timestamp);
                }
                break;
            }
        }
    }

    /**
     * 更新风险事件的拓展信息
     *
     * @param riskCaseExtInfoDO
     */
    public void updateRiskCaseExtInfoDO(RiskCaseExtInfoDO riskCaseExtInfoDO) {
        if (Objects.isNull(riskCaseExtInfoDO)) {
            return;
        }
        this.extInfo = riskCaseExtInfoDO;
    }

    /**
     * 更新消息的版本
     *
     * @param messageVersion
     */
    public void updateMessageVersion(Long messageVersion) {
        SystemCheckUtil.isNotNull(messageVersion, "消息版本不可以为空");
        if (this.messageVersion == null || NumberUtils.toLong(this.messageVersion) < messageVersion) {
            //小于版本的时候，再更新
            this.messageVersion = String.valueOf(messageVersion);
        }
    }

    /**
     * 更新消息的版本
     *
     * @param messageVersion
     */
    public void updateMessage(String messageId, Long messageVersion) {
        SystemCheckUtil.isNotBlank(messageId, "消息ID不可以为空");
        SystemCheckUtil.isNotNull(messageVersion, "消息版本不可以为空");
        this.messageId = messageId;
        //如果消息ID相同，并且版本号小于当前版本号，则更新版本号
        if (this.messageVersion == null || NumberUtils.toLong(this.messageVersion) < messageVersion) {
            //小于版本的时候，再更新
            this.messageVersion = String.valueOf(messageVersion);
        }
    }

    /**
     * 更新场地code
     *
     * @param placeCode
     */
    public void updatePlaceCode(String placeCode) {
        if (StringUtils.isBlank(placeCode)) {
            return;
        }
        this.placeCode = placeCode;
    }

    /**
     * 获取处置时间
     *
     * @return
     */
    public Integer getDisposalTime() {
        Date start =
                this.getRecallTime() == null || DatetimeUtil.isDbZeroTime(this.getRecallTime())
                        ? new Date()
                        : this.getRecallTime();
        Date end =
                this.getCloseTime() == null || DatetimeUtil.isDbZeroTime(this.getCloseTime())
                        ? new Date()
                        : this.getCloseTime();
        return DatetimeUtil.getSecondsDiff(
                DatetimeUtil.dateToLocalDateTime(start),
                DatetimeUtil.dateToLocalDateTime(end));
    }

    /**
     * 获取风险持续总时长
     *
     * @return
     */
    public Integer getRiskDurationTime() {
        Date start =
                this.getOccurTime() == null || DatetimeUtil.isDbZeroTime(this.getOccurTime())
                        ? new Date()
                        : this.getOccurTime();
        Date end =
                this.getCloseTime() == null || DatetimeUtil.isDbZeroTime(this.getCloseTime())
                        ? new Date()
                        : this.getCloseTime();
        return DatetimeUtil.getSecondsDiff(
                DatetimeUtil.dateToLocalDateTime(start),
                DatetimeUtil.dateToLocalDateTime(end));
    }

    /**
     * 根据处置的状态，进行重算
     *
     * @param allSize
     * @param init
     * @param inProcess
     * @param finished
     */
    public boolean refreshStatusByRelation(int allSize, int init, int inProcess, int finished) {
        if (allSize != init + inProcess + finished) {
            //如果关联的跟处置的不一致，不进行更新
            return false;
        }
        if (RiskCaseStatusEnum.isTerminal(status)) {
            //当前风险已经结束，不需要重置
            return false;
        } else if ((inProcess != 0 || finished != 0) && this.status == RiskCaseStatusEnum.NO_DISPOSAL) {
            //存在处置中或者完结的车辆，且状态在初始化的，更新
            this.status = RiskCaseStatusEnum.IN_DISPOSAL;
            return true;
        } else if (inProcess == 0 && finished == 0 && this.status == RiskCaseStatusEnum.IN_DISPOSAL) {
            //全部都是未完结的，且处在处置中
            this.status = RiskCaseStatusEnum.NO_DISPOSAL;
            return true;
        }
        return false;
    }

    /**
     * 如果 riskCaseDO 满足 createTime + 对应 sourceDelaySeconds < new Date() ，返回true，表示可以进行播报 否则表示仍在延迟时间内，无需播报
     *
     * @param sourceDelaySeconds
     */
    public boolean isNeedBroadcast(Integer sourceDelaySeconds) {
        if (sourceDelaySeconds == null) {
            // 有问题，不播报
            return false;
        }
        return DatetimeUtil.addSeconds(this.getOccurTime(), sourceDelaySeconds).compareTo(System.currentTimeMillis())
                <= 0;
    }

    /**
     * @return
     */
    public Map<String, String> getGroupMessageMap() {
        if (StringUtils.isEmpty(messageId)) {
            return new HashMap<>();
        }
        Map<String, String> group2PureMessageId = new HashMap<>();
        Arrays.stream(messageId.split(CharConstant.CHAR_COMMA)).forEach(messageId -> {
            String[] groupAndPureMessageId = messageId.split(CharConstant.CHAR_JH);
            if (groupAndPureMessageId.length != 2) {
                return;
            }
            //pureMessage#groupName
            group2PureMessageId.put(groupAndPureMessageId[1], groupAndPureMessageId[0]);
        });
        return group2PureMessageId;


    }
}
