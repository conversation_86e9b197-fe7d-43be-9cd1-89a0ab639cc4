package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RiskCaseStatusEnum {
    NO_DISPOSAL(10, "未处置"),
    IN_DISPOSAL(20, "处置中"),
    DISPOSED(30, "已解除"),
    MANUAL_DISPOSED(31, "人工已解除"),

    CANCEL(99, "已取消"),

    ;

    private int code;

    private String desc;

    /**
     * 根据code获取枚举
     *
     * @param riskCaseStatus
     * @return
     */
    public static RiskCaseStatusEnum findByValue(Integer riskCaseStatus) {
        if (riskCaseStatus == null) {
            return null;
        }
        for (RiskCaseStatusEnum riskCaseStatusEnum : RiskCaseStatusEnum.values()) {
            if (riskCaseStatusEnum.getCode() == riskCaseStatus) {
                return riskCaseStatusEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否结束
     *
     * @param statusEnum
     * @return
     */
    public static boolean isTerminal(RiskCaseStatusEnum statusEnum) {
        if (statusEnum == null) {
            return true;
        }
        return statusEnum.getCode() >= DISPOSED.code;
    }


    /**
     * 获取未结束的状态
     *
     * @return
     */
    public static List<Integer> getUnTerminal() {
        List<Integer> statusList = new ArrayList<>();
        for (RiskCaseStatusEnum riskCaseStatusEnum : values()) {
            if (!isTerminal(riskCaseStatusEnum)) {
                statusList.add(riskCaseStatusEnum.getCode());
            }
        }
        return statusList;

    }

    /**
     * 获取未结束的状态枚举
     *
     * @return
     */
    public static List<RiskCaseStatusEnum> getUnTerminalEnum() {
        List<RiskCaseStatusEnum> statusList = new ArrayList<>();
        for (RiskCaseStatusEnum riskCaseStatusEnum : values()) {
            if (!isTerminal(riskCaseStatusEnum)) {
                statusList.add(riskCaseStatusEnum);
            }
        }
        return statusList;

    }

    /**
     * 获取已结束的状态
     *
     * @return
     */
    public static List<Integer> getTerminal() {
        List<Integer> statusList = new ArrayList<>();
        for (RiskCaseStatusEnum statusEnum : values()) {
            if (statusEnum == null) {
                continue;
            }
            if (statusEnum.getCode() >= DISPOSED.code) {
                statusList.add(statusEnum.getCode());
            }
        }
        return statusList;
    }
}
