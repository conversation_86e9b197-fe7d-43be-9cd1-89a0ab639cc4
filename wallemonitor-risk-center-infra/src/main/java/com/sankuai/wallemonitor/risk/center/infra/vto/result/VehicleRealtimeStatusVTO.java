package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆实时状态响应实体
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VehicleRealtimeStatusVTO {
    @JsonProperty("vin")
    private String vin;

    @JsonProperty("vehicle_id")
    private String vehicleId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("vehicle_type")
    private Integer vehicleType;

    @JsonProperty("vehicle_type_desc")
    private String vehicleTypeDesc;

    @JsonProperty("cockpit_status")
    private Integer cockpitStatus;

    @JsonProperty("cockpit_status_desc")
    private String cockpitStatusDesc;

    @JsonProperty("update_timestamp")
    private Long updateTimestamp;
}
