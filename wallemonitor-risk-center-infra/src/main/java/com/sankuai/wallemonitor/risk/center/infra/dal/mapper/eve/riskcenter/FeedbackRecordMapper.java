package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.FeedbackRecord;

/**
 * <p>
 * 反馈记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-07-17
 */
public interface FeedbackRecordMapper extends CommonMapper<FeedbackRecord> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<FeedbackRecord> getPOClass() {
        return FeedbackRecord.class;
    }
}