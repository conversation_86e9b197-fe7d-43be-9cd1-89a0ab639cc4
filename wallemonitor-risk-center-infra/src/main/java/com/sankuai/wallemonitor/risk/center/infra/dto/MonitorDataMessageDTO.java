package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleMonitorInfoDO;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorDataMessageDTO {

    @JsonProperty("time")
    private Long time;

    @JsonProperty("measurement")
    private String measurement;

    @JsonProperty("tags")
    private Map<String, String> tags;

    @JsonProperty("fields")
    private VehicleMonitorInfoDO vehicleMonitorInfo;


}
