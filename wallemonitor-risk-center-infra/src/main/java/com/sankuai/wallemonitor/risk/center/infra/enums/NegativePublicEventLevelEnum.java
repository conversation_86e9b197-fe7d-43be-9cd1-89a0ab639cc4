package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 负外部性事件等级枚举
 */
@AllArgsConstructor
@Getter
public enum NegativePublicEventLevelEnum {

    DEFAULT(0, "默认"),
    HIGH_RISK(1, "高危"),
    MEDIUM_RISK(2, "中危"),
    LOW_RISK(3, "低危");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static NegativePublicEventLevelEnum fromCode(Integer code) {
        for (NegativePublicEventLevelEnum item : NegativePublicEventLevelEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

}
