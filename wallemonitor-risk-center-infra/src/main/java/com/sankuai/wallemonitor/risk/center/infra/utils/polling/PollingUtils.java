package com.sankuai.wallemonitor.risk.center.infra.utils.polling;

import com.google.common.base.Joiner;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.SquirrelCategoryEnum;
import java.util.function.BiFunction;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PollingUtils implements ApplicationListener<ContextRefreshedEvent> {

    private static final String OPERATE_POLLING_KEY = "OPERATE_POLLING_KEY_";
    private static final String OPERATE_POLLING_EXISTS_KEY = "OPERATE_POLLING_EXISTS_KEY_";
    private static SquirrelAdapter redisListCommands;
    @Resource
    private SquirrelAdapter squirrelAdapter;

    /**
     * 对operateId进行轮训,限定超时秒数
     *
     * @param operateId
     * @param timeoutInSeconds
     * @param supplier
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> R getOperationResult(String operateId, int timeoutInSeconds,
            BiFunction<Boolean, T, R> supplier) {

        try {
            //添加一个操作锁
            redisListCommands.setnx(SquirrelCategoryEnum.POLLING_CATEGORY, buildExistsKey(operateId),
                    operateId, timeoutInSeconds);
            T result = redisListCommands.brpop(SquirrelCategoryEnum.POLLING_CATEGORY,
                    buildPollingKey(operateId), timeoutInSeconds);
            if (result != null) {
                log.info("operateId:{},result:{}", operateId, JacksonUtils.to(result));
                return supplier.apply(true, result);
            } else {
                return supplier.apply(false, null);
            }
        } catch (Exception e) {
            log.error("轮训超时失败！", e);
            redisListCommands.delete(SquirrelCategoryEnum.POLLING_CATEGORY, buildExistsKey(operateId));
            return supplier.apply(false, null);
        }
    }

    /**
     * 放入操作结果
     *
     * @param operateId
     * @param value
     * @return
     */
    public static <T> void pushOperateResult(String operateId, T value) {
        try {
            redisListCommands.rpush(SquirrelCategoryEnum.POLLING_CATEGORY, buildPollingKey(operateId), value);
        } catch (Exception e) {
            log.error("push操作结果失败", e);
        } finally {
            redisListCommands.delete(SquirrelCategoryEnum.POLLING_CATEGORY, buildExistsKey(operateId));
        }
    }

    public static String buildExistsKey(String operateId) {
        return Joiner.on(CharConstant.CHAR_HX).join(OPERATE_POLLING_EXISTS_KEY, operateId);
    }

    public static String buildPollingKey(String operateId) {
        return Joiner.on(CharConstant.CHAR_HX).join(OPERATE_POLLING_KEY, operateId);
    }

    /**
     * 是否还在轮训操作记录
     *
     * @return
     */
    public static boolean isPollingOperate(String operateId) {
        return redisListCommands.exists(SquirrelCategoryEnum.POLLING_CATEGORY, buildExistsKey(operateId));
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        redisListCommands = squirrelAdapter;
    }
}
