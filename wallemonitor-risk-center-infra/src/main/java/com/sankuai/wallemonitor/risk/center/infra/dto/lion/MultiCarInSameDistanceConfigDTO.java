package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MultiCarInSameDistanceConfigDTO {

    /**
     * 是否开启
     */
    private boolean enable;

    /**
     * 需要判定的类型列表
     */
    @Builder.Default
    private Set<String> sameDistanceFineType = new HashSet<>();

    /**
     * 不同的类型的距离阈值差异
     */
    @Builder.Default
    private Map<String, Double> minDistanceDiff = new HashMap<>();

    /**
     * 默认允许距离最小,m
     */
    @Builder.Default
    private Double defaultMinDistanceDiff = 2D;

    /**
     * 允许同距离车辆数量
     */
    @Default
    private Integer inSameDistanceCount = 1;

    /**
     * 是否处于同距离
     *
     * @param fineType
     * @param distance
     * @return
     */
    public boolean inSameDistance(String fineType, Double distance) {
        if (distance == null || StringUtils.isBlank(fineType) || CollectionUtils.isEmpty(sameDistanceFineType)
                || !sameDistanceFineType.contains(fineType)) {
            return false;
        }
        Double minDistance = minDistanceDiff.getOrDefault(fineType, defaultMinDistanceDiff);
        return minDistance != null && distance <= minDistance;
    }

}
