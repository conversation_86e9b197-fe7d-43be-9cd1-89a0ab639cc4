package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.SafetyAreaInfoSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SafetyAreaExtInfoDO;

import java.util.Date;
import java.util.List;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SafetyAreaDO {

    @DomainUnique
    private String areaId;

    private SafetyAreaInfoSourceEnum source;

    private Polygon polygon;

    private String type;

    private String description;

    private SafetyAreaExtInfoDO extInfo;

    private Date createTime;

    /**
     * 是否删除
     */
    @Builder.Default
    private IsDeleteEnum isDeleted = IsDeleteEnum.NOT_DELETED;

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class Polygon {
        private List<PositionDO> pointGcjList;

        public PolygonDO getPolygonDO() {
            if (CollectionUtils.isEmpty(pointGcjList)) {
                return null;
            }
            CoordinateSystemEnum coordinateSystemEnum = pointGcjList.stream().findFirst()
                    .map(PositionDO::getCoordinateSystem).orElse(null);
            CheckUtil.isNotNull(coordinateSystemEnum, "safety area polygon 中有点的坐标不存在");

            boolean isAllMatch = pointGcjList.stream().allMatch(x -> x.getCoordinateSystem() == coordinateSystemEnum);
            CheckUtil.isTrue(isAllMatch, "safety area polygon 点的坐标系不统一");

            return PolygonDO.builder().coordinateSystemEnum(coordinateSystemEnum).points(pointGcjList).build();
        }
    }

    public boolean isInPolygon(PositionDO positionDO) {
        if(this.polygon == null || positionDO == null) {
            return false;
        }
        PolygonDO gcjPolygon = PolygonDO.builder()
                .points(polygon.getPointGcjList())
                .coordinateSystemEnum(positionDO.getCoordinateSystem())
                .build();
        return gcjPolygon.isInPolygon(positionDO);
    }

    public boolean isInTimeRange(Integer hour) {
        if(hour == null || this.getExtInfo() == null ) {
            return false;
        }
        Set<Integer> effectHours = extInfo.getEffectHours();
        if(CollectionUtils.isEmpty(effectHours)) {
            return false;
        }
        return effectHours.contains(hour);
    }

    public List<PositionDO> getPoints() {
        if(this.polygon == null) {
            return null;
        }
        return this.polygon.getPointGcjList();
    }

}
