package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskAlertRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskAlertRecordDOQueryParamDTO;
import java.util.List;

/**
 * 风险告警记录Repository接口
 */
public interface RiskAlertRecordRepository {

    /**
     * 根据参数查询风险告警记录
     *
     * @param paramDTO 查询参数
     * @return 告警记录列表
     */
    List<RiskAlertRecordDO> queryByParam(RiskAlertRecordDOQueryParamDTO paramDTO);

    /**
     * 根据参数分页查询风险告警记录
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    Paging<RiskAlertRecordDO> queryByParamByPage(RiskAlertRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize);

    /**
     * 根据业务ID获取最新的风险告警记录
     *
     * @param bizId
     * @return
     */
    RiskAlertRecordDO getLatestRecordByBizId(String bizId);

    /**
     * 根据消息ID获取最新的风险告警记录
     * 大象卡片回调消息中的requestId即这里的messageId
     *
     * @param messageId
     * @return
     */
    RiskAlertRecordDO getLatestRecordByMessageId(String messageId);

    /**
     * 保存风险告警记录
     *
     * @param recordDO 告警记录DO
     */
    void save(RiskAlertRecordDO recordDO);

    /**
     * 批量保存风险告警记录
     *
     * @param recordDOList 告警记录DO列表
     */
    void batchSave(List<RiskAlertRecordDO> recordDOList);

    /**
     * 根据ID查询风险告警记录
     *
     * @param id 记录ID
     * @return 告警记录
     */
    RiskAlertRecordDO getById(Long id);

    /**
     * 获取待升级的告警记录（按message_id分组，每组取最新记录）
     *
     * @return 待升级记录列表
     */
    List<RiskAlertRecordDO> getPendingUpgradeRecords();

} 