package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 告警记录标注枚举
 */
@Getter
@AllArgsConstructor
public enum AlertRecordLabelEnum {

    /**
     * 无用
     */
    USELESS(-1, "dislike", "无用"),

    /**
     * 未标注
     */
    UNLABELED(0, "none", "未标注"),

    /**
     * 有用
     */
    USEFUL(1, "like", "有用");

    private final Integer code;
    private final String voteType;
    private final String desc;

    /**
     * 根据code查找枚举
     */
    public static AlertRecordLabelEnum findByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AlertRecordLabelEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static AlertRecordLabelEnum findByVoteType(String voteType) {
        if (StringUtils.isBlank(voteType)) {
            return null;
        }
        for (AlertRecordLabelEnum item : values()) {
            if (item.getVoteType().equals(voteType)) {
                return item;
            }
        }
        return null;
    }
} 