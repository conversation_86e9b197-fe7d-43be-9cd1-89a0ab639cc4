package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.sankuai.walleeve.utils.GeometryUtil;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.locationtech.jts.geom.Coordinate;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PolygonDO {

    private CoordinateSystemEnum coordinateSystemEnum;
    /**
     * 多边形的点,gcj02
     */
    private List<PositionDO> points;


    /**
     * 判断是否在多边形内
     *
     * @param positionDO
     * @return
     */
    public Boolean isInPolygon(PositionDO positionDO) {
        CheckUtil.isNotNull(positionDO, "定位不可以为空");
        CheckUtil.isNotNull(positionDO.getCoordinateSystem(), "定位坐标系不可以为空");
        if (CollectionUtils.isEmpty(points)) {
            return false;
        }
        // 必须是指定坐标系的坐标
        PositionDO positionCoordinateSystem = GeoToolsUtil.transferCoordinateSystemEnum(positionDO,
                coordinateSystemEnum);
        //转成点
        Coordinate curPositionCrd = new Coordinate(positionCoordinateSystem.getLongitude(),
                positionCoordinateSystem.getLatitude());
        // 获取闭环的坐标点集合
        List<Coordinate> thisPolygonCrdList = this.getClosedCoordinates();
        return GeometryUtil.inPolygon(curPositionCrd, thisPolygonCrdList);
    }

    /**
     * 获取多边形的坐标点集合（Coordinate类型）,闭环的
     *
     * @return List<Coordinate> 坐标点集合
     */
    public List<Coordinate> getClosedCoordinates() {
        List<Coordinate> polygonPointList = this.points.stream()
                .map(point -> new Coordinate(point.getLongitude(), point.getLatitude()))
                .collect(Collectors.toList());
        // 进行闭环（如果默认是闭环的，则不进行处理，函数内不进行处理）
        return GeoToolsUtil.sortCoordinatesClockwise(polygonPointList);
    }

    /**
     * 获取外接矩形
     * 
     * @return
     */
    public List<Coordinate> getBoundaryRectangle() {
        Coordinate[] boundaryRectangleCoordinate = GeoToolsUtil.getBoundingRectangle(this);
        return Arrays.asList(boundaryRectangleCoordinate);
    }

}