package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class DomainEventDTO<T> {

    private DomainEventEntryDTO entry;

    private Long timestamp;

    private String operator;

    private String traceId;

    private DomainEventExtInfoDTO extInfo;

    private List<T> before;

    private List<T> after;


}
