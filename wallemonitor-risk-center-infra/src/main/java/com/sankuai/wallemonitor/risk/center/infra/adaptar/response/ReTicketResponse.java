package com.sankuai.wallemonitor.risk.center.infra.adaptar.response;

import com.sankuai.wallemonitor.risk.center.infra.vto.result.ReTicketVTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * RE工单响应实体
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReTicketResponse {

    /**
     * 200 表示成功, 其他则是失败
     */
    private Integer code;

    /**
     * 响应信息
     */
    private String msg;

    /**
     * 工单列表
     */
    private List<ReTicketVTO> data;
}
