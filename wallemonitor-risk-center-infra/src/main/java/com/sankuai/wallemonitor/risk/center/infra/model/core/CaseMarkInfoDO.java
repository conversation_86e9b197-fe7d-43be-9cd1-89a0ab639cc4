package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskLevelEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CaseMarkInfoExtDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CaseMarkInfoDO {

    private String caseId;

    private RiskLevelEnum riskLevel;

    private String category;

    private String subCategory;

    private String lastOperator;

    private String firstOperator;

    private Integer round;

    private String firstCategory;

    private String firstSubCategory;

    private Date createTime;

    private Date updateTime;

    private CaseMarkInfoExtDO extInfo;

    private ImproperStrandingReason improperStrandingReason;

    @Builder.Default
    private IsDeleteEnum isDeleted = IsDeleteEnum.NOT_DELETED;

    public void updateImproperStrandingReason(ImproperStrandingReason improperStrandingReason) {
        this.improperStrandingReason = improperStrandingReason;
    }

    /**
     * 人工标注
     *
     * @param category
     * @param subCategory
     * @param operator
     */
    public void manualMark(String category, String subCategory, String desc, String operator) {
        if (StringUtils.isNotBlank(category)) {
            this.category = category;
        }
        if (StringUtils.isNotBlank(subCategory)) {
            this.subCategory = subCategory;
        }
        this.lastOperator = operator;
        if (this.extInfo == null) {
            this.extInfo = CaseMarkInfoExtDO.builder().build();
        }
        if (StringUtils.isNotBlank(desc)) {
            this.extInfo.setDesc(desc);
        }
    }

    /**
     * 自动标注
     *
     * @param category
     * @param operator
     */
    public void autoMark(ISCheckCategoryEnum category, Map<String, Object> checkResult,
            String operator, Integer round) {
        if (this.extInfo == null) {
            this.extInfo = CaseMarkInfoExtDO.builder().build();
        }
        if (category != null) {
            // 更新自动标注的结果,不再更新subCategory
            this.firstCategory = category.getCategory();
            this.firstSubCategory = category.getSubcategory();
            // 如果是无法识别的情况，此处落表视为有风险
            if (category == ISCheckCategoryEnum.CANT_FOUND_ANY) {
                this.firstCategory = "GOOD";
            }
        }
        if (checkResult != null) {
            this.extInfo.addCheckResult(checkResult);
        }
        if (round != null) {
            this.extInfo.setCheckRound(round);
            this.round = round;
        }
        this.firstOperator = operator;
    }

    /**
     * 转移
     *
     * @param caseMarkInfoDO
     */
    public void copyFrom(CaseMarkInfoDO caseMarkInfoDO) {
        if (caseMarkInfoDO == null) {
            return;
        }
        if (StringUtils.isNotBlank(caseMarkInfoDO.getLastOperator())) {
            this.lastOperator = caseMarkInfoDO.getLastOperator();
        }
        if (StringUtils.isNotBlank(caseMarkInfoDO.getCategory())) {
            this.category = caseMarkInfoDO.getCategory();
        }
        if (StringUtils.isNotBlank(caseMarkInfoDO.getSubCategory())) {
            this.subCategory = caseMarkInfoDO.getSubCategory();
        }
        if (caseMarkInfoDO.getRiskLevel() != null) {
            this.riskLevel = caseMarkInfoDO.getRiskLevel();
        }
        if (StringUtils.isNotBlank(caseMarkInfoDO.getFirstOperator())) {
            this.firstOperator = caseMarkInfoDO.getFirstOperator();
        }
        if (Objects.nonNull(caseMarkInfoDO.getRound())) {
            this.round = caseMarkInfoDO.getRound();
        }

        if (caseMarkInfoDO.getExtInfo() != null) {
            CaseMarkInfoExtDO extInfo = CaseMarkInfoExtDO.builder().build();
            if (StringUtils.isNotBlank(caseMarkInfoDO.getExtInfo().getDesc())) {
                extInfo.setDesc(caseMarkInfoDO.getExtInfo().getDesc());
            }
            if (caseMarkInfoDO.getExtInfo().getCheckResult() != null) {
                extInfo.setCheckResult(caseMarkInfoDO.getExtInfo().getCheckResult());
            }
            if (caseMarkInfoDO.getExtInfo().getCheckRound() != null) {
                extInfo.setCheckRound(caseMarkInfoDO.getExtInfo().getCheckRound());
            }
            this.extInfo = extInfo;
        }
        if (caseMarkInfoDO.getImproperStrandingReason() != null) {
            this.improperStrandingReason = caseMarkInfoDO.getImproperStrandingReason();
        }
    }
}
