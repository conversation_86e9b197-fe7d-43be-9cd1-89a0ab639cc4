package com.sankuai.wallemonitor.risk.center.infra.utils.geo;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.algorithm.ConvexHull;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class PolygonUtil {


    private static final  GeometryFactory geometryFactory = new GeometryFactory();

    /**
     * 创建一个以经纬度为中心、指定半径（以纬度为单位）和边数的正多边形
     * @param centerLat (float): 中心点纬度
     * @param centerLon (float): 中心点经度
     * @param r (float): 多边形半径（单位：纬度）
     * @param n (int): 多边形边数
     * */
    public static List<PositionDO> createPolygon(Double centerLat, Double centerLon, Double r,
                                                 int n, CoordinateSystemEnum coordinateSystemEnum) {
        List<PositionDO> vertices = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            double angle = Math.PI * 2 * i / n;

            double dx = r * Math.cos(angle);
            double dy = r * Math.sin(angle);

            Double lat = centerLat + dy;
            Double lon = centerLon + dx / Math.cos(Math.toRadians(centerLat));

            vertices.add(PositionDO.builder()
                    .latitude(lat).longitude(lon).coordinateSystem(coordinateSystemEnum).build());
        }

        return vertices;
    }


    /**
     * 计算一系列点列的凸包
     * @param positionDOList : 经纬度点列
     * */
    public static List<PositionDO> computeConvexHull(List<PositionDO> positionDOList) {

        PositionDO positionDO = positionDOList.stream().findFirst().orElse(null);

        if(positionDO == null) {
            return Lists.newArrayList();
        }

        CoordinateSystemEnum coordinateSystem = positionDO.getCoordinateSystem();


        Coordinate[] coordinates = new Coordinate[positionDOList.size()];

        for (int i = 0; i < coordinates.length; i++) {
            coordinates[i] = new Coordinate(
                    positionDOList.get(i).getLongitude(),
                    positionDOList.get(i).getLatitude()
            );
        }

        ConvexHull convexHull = new ConvexHull(coordinates, geometryFactory);

        if(convexHull.getConvexHull() == null) {
            return Lists.newArrayList();
        }
        Coordinate[] hullCoordinates = convexHull.getConvexHull().getCoordinates();
        if(hullCoordinates == null || hullCoordinates.length <= 1) {
            return Lists.newArrayList();
        }
        return Arrays.stream(hullCoordinates).map(coordinate ->
                        PositionDO.builder().longitude(coordinate.getX())
                                .coordinateSystem(coordinateSystem)
                                .latitude(coordinate.getY()).build())
                .collect(Collectors.toList());
    }
}