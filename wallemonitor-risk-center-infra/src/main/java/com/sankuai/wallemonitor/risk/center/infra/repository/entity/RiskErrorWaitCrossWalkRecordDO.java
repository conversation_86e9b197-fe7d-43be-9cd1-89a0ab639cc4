package com.sankuai.wallemonitor.risk.center.infra.repository.entity;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDetectorRecordBaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 错误让行检测记录DO
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RiskErrorWaitCrossWalkRecordDO extends RiskDetectorRecordBaseDO {

    /**
     * 行人停滞积累信息
     */
    private Integer walkerStayInCrossWalkCount;

}