package com.sankuai.wallemonitor.risk.center.infra.dal.handler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.locationtech.jts.io.WKBReader;
import org.locationtech.jts.io.WKTWriter;

/**
 * Point类型的TypeHandler
 */
@MappedTypes(Point.class)
@Slf4j
public class PointTypeHandler extends BaseTypeHandler<Point> {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel(), 4326);


    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Point parameter, JdbcType jdbcType)
            throws SQLException {
        String wkt = new WKTWriter().write(parameter);
        // ps.setString(i, String.format("ST_GeomFromText('%s', 4326)", wkt));
        ps.setString(i, wkt);
    }

    @Override
    public Point getNullableResult(ResultSet rs, String columnName) throws SQLException {
        byte[] bytes = rs.getBytes(columnName);
        if (bytes == null) {
            return null;
        }
        return readWKB(bytes);
    }

    @Override
    public Point getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        byte[] bytes = rs.getBytes(columnIndex);
        if (bytes == null) {
            return null;
        }
        return readWKB(bytes);
    }

    @Override
    public Point getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        byte[] bytes = cs.getBytes(columnIndex);
        if (bytes == null) {
            return null;
        }
        return readWKB(bytes);
    }

    private Point readWKB(byte[] bytes) throws SQLException {
        if (bytes == null) {
            return null;
        }
        try {
            // 去掉SRID头4字节
            byte[] wkb = new byte[bytes.length - 4];
            System.arraycopy(bytes, 4, wkb, 0, wkb.length);
            Geometry geometry = new WKBReader(GEOMETRY_FACTORY).read(wkb);
            return geometry instanceof Point ? (Point)geometry : null;
        } catch (org.locationtech.jts.io.ParseException e) {
            log.warn("读取失败", e);
            return null;
        }
    }
}