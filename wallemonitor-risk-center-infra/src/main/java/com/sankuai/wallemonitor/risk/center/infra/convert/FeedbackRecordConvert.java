package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.FeedbackRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.FeedbackRecordDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface FeedbackRecordConvert extends SingleConvert<FeedbackRecord, FeedbackRecordDO> {

}
