package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.UserInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventDetailExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventHandleDegreeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventLevelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventNatureEnum;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class NegativePublicEventDetailDO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * eventId
     */
    private String eventId;


    /**
     * 事件性质
     */
    private NegativePublicEventNatureEnum nature;

    /**
     * 问题分类
     */
    private String category;

    /**
     * 问题分类-其他-描述
     */
    private String otherCategoryDesc;

    /**
     * 事件等级
     */
    private NegativePublicEventLevelEnum level;

    /**
     * 标题
     */
    private String title;

    /**
     * 情况说明
     */
    private String conditionDesc;

    /**
     * 确定事件性质时间
     */
    private Date determineNatureTime;

    /**
     * 确定事件性质操作人
     */
    private String determineNatureOperator;

    /**
     * 处置人
     */
    private String handler;

    /**
     * 问题归因-其他-描述
     */
    private String otherReasonDesc;

    /**
     * 排查结果
     */
    private String checkResult;

    /**
     * 确定问题归因时间
     */
    private Date determineReasonTime;

    /**
     * 确定问题归因操作人
     */
    private String determineReasonOperator;

    /**
     * 解决程度
     */
    private NegativePublicEventHandleDegreeEnum handleDegree;

    /**
     * 处置结果说明
     */
    private String handleResultDesc;

    /**
     * 处置结果附件
     */
    private String handleResultDescFileLink;

    /**
     * 处置完成时间
     */
    private Date completeTime;

    /**
     * 处置完成操作人
     */
    private String completeOperator;

    /**
     * 复盘信息
     */
    private String reviewInfo;

    /**
     * 拓展字段,json格式
     */
    private NegativePublicEventDetailExtInfoDTO extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 定因提醒
     */
    private Boolean reasonNoticed;

    /**
     * 处置提醒
     */
    private Boolean handleNoticed;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 更新事件性质
     *
     * @param natureEnum
     */
    public void updateNature(NegativePublicEventNatureEnum natureEnum) {
        if (natureEnum == null) {
            return;
        }
        String operator = Optional.ofNullable(UserInfoContext.getUserInfo())
                .map(userInfo -> String.format("%s/%s",
                        Optional.ofNullable(userInfo.getName()).orElse(""),
                        Optional.ofNullable(userInfo.getLogin()).orElse("")))
                .orElse(CommonConstant.UNKNOWN);
        this.nature = natureEnum;
        this.determineNatureTime = new Date();
        this.determineNatureOperator = operator;
    }

    /**
     * 更新问题归因
     */
    public void updateReason() {

        String operator = Optional.ofNullable(UserInfoContext.getUserInfo())
                .map(userInfo -> String.format("%s/%s",
                        Optional.ofNullable(userInfo.getName()).orElse(""),
                        Optional.ofNullable(userInfo.getLogin()).orElse("")))
                .orElse(CommonConstant.UNKNOWN);
        this.determineReasonTime = new Date();
        this.determineReasonOperator = operator;
    }

    /**
     * 更新处置结果
     *
     * @param handleDegree
     */
    public void updateHandle(NegativePublicEventHandleDegreeEnum handleDegree) {
        if (handleDegree == null) {
            return;
        }

        String operator = Optional.ofNullable(UserInfoContext.getUserInfo())
                .map(userInfo -> String.format("%s/%s",
                        Optional.ofNullable(userInfo.getName()).orElse(""),
                        Optional.ofNullable(userInfo.getLogin()).orElse("")))
                .orElse(CommonConstant.UNKNOWN);
        this.handleDegree = handleDegree;
        this.completeTime = new Date();
        this.completeOperator = operator;
    }

    /**
     * 清除定因信息
     */
    public void clearReasonInfo() {
        this.checkResult = "";
        this.determineReasonTime = DatetimeUtil.ZERO_DATE;
        this.determineReasonOperator = "";
    }

    /**
     * 清除处置信息
     */
    public void clearHandleInfo() {
        this.handleDegree = NegativePublicEventHandleDegreeEnum.DEFAULT;
        this.handleResultDesc = "";
        this.completeTime = DatetimeUtil.ZERO_DATE;
        this.completeOperator = "";
    }

    /**
     * 清除复盘信息
     */
    public void clearReviewInfo() {
        this.reviewInfo = "";
    }

    /**
     * 清除定位状态后的所有信息
     */
    public void clearAllInfoAfterDetermineNature() {
        this.checkResult = "";
        this.reasonNoticed = false;
        this.handleNoticed = false;
        this.otherReasonDesc = "";
        this.determineReasonTime = DatetimeUtil.ZERO_DATE;
        this.determineReasonOperator = "";
        this.handleDegree = NegativePublicEventHandleDegreeEnum.DEFAULT;
        this.handleResultDesc = "";
        this.completeTime = DatetimeUtil.ZERO_DATE;
        this.completeOperator = "";
        this.reviewInfo = "";
    }


}
