package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.UserIdentityAdapter;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.UserIdentityRepository;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 用户身份信息仓储层实现
 *
 * <AUTHOR>
 * @date 2025/06/14
 */
@Slf4j
@Component
public class UserIdentityRepositoryImpl implements UserIdentityRepository {

    private final int CACHE_EXPIRE_MINUTES = 5;

    @Resource
    private UserIdentityAdapter userIdentityAdapter;

    /**
     * 员工ID到MIS号的缓存，5分钟过期
     */
    private LoadingCache<Long, String> empIdToMisCache;

    @PostConstruct
    public void init() {
        this.empIdToMisCache = CacheBuilder.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
                .build(new CacheLoader<Long, String>() {
                    @Override
                    public String load(Long empId) throws Exception {
                        return loadMisByEmpId(empId);
                    }

                    @Override
                    public Map<Long, String> loadAll(Iterable<? extends Long> empIds) throws Exception {
                        List<Long> empIdList = Lists.newArrayList(empIds);
                        return loadMisByEmpIdList(empIdList);
                    }
                });
    }

    @Override
    public String getMisByEmpId(Long empId) {
        if (empId == null) {
            return null;
        }

        try {
            return empIdToMisCache.get(empId);
        } catch (ExecutionException e) {
            log.error("获取员工MIS号失败, empId: {}", empId, e);
            return null;
        }
    }

    @Override
    public Map<Long, String> getMisByEmpId(List<Long> empIdList) {
        if (CollectionUtils.isEmpty(empIdList)) {
            return Collections.emptyMap();
        }

        try {
            return empIdToMisCache.getAll(empIdList);
        } catch (ExecutionException e) {
            log.error("批量获取员工MIS号失败, empIdList: {}", empIdList, e);
            return Collections.emptyMap();
        }
    }

    private String loadMisByEmpId(Long empId) {
        if (empId == null) {
            return null;
        }

        try {
            Map<Long, String> result = userIdentityAdapter.getMisByEmpId(Lists.newArrayList(empId));
            return result.get(empId);
        } catch (Exception e) {
            log.error("加载员工MIS号失败, empId: {}", empId, e);
            return null;
        }
    }

    private Map<Long, String> loadMisByEmpIdList(List<Long> empIdList) {
        if (CollectionUtils.isEmpty(empIdList)) {
            return Collections.emptyMap();
        }

        try {
            return userIdentityAdapter.getMisByEmpId(empIdList);
        } catch (Exception e) {
            log.error("批量加载员工MIS号失败, empIdList: {}", empIdList, e);
            return Collections.emptyMap();
        }
    }
} 