package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter.SearchNearbyRequestVTO;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.HdMapElementPolygonConvert;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapElementTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapLaneAreaTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.HdMapRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskRestrictQueryDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GeometryFeatureVTO;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class HdMapRepositoryImpl implements HdMapRepository {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private HdMapElementPolygonConvert hdMapElementPolygonConvert;

    private static final Double DEFAULT_METER = 10D;

    /**
     * 获取高精地图的区域数据
     *
     * @param areaEnum
     * @param position
     * @param hdMapVersion
     * @param vin
     * @return
     */
    @Override
    public List<HdMapElementGeoDO> getHdMapAreaDataWGS84(HdMapLaneAreaTypeEnum areaEnum, PositionDO position,
            String hdMapVersion, String vin, Double meter) {
        List<GeometryFeatureVTO> geometryFeatureVTOS = hdMapAdapter
                .queryHdMapAreaWgs84(Lists.newArrayList(areaEnum.getValue()), position, hdMapVersion, vin, meter);
        return hdMapElementPolygonConvert.toHdMapElementPolygonDO(areaEnum.getMapValue().getValue(),
                geometryFeatureVTOS);
    }

    /**
     * 是否在高精地图区域中
     *
     * @param areaTypeEnum
     * @param position
     * @param hdMapVersion
     * @param vin
     * @return
     */
    @Override
    public Boolean isInHdMapAreaWGS84(HdMapLaneAreaTypeEnum areaTypeEnum, PositionDO position, String hdMapVersion,
            String vin) {
        List<HdMapElementGeoDO> hdMapPolygonList = this.getHdMapAreaDataWGS84(areaTypeEnum, position, hdMapVersion,
                vin, DEFAULT_METER);
        // 是否在区域内
        return hdMapPolygonList.stream().anyMatch(hdMapAreaVTO -> hdMapAreaVTO.isInPolygon(position));

    }

    /**
     * 是否在高精地图区域的一定范围内
     *
     * @param areaList
     * @param position
     * @param hdMapVersion
     * @param vin
     * @param meter
     * @return
     */
    @Override
    public String isRestrictParkingByRemote(List<String> areaList, PositionDO position, String hdMapVersion, String vin,
            Double meter, Double expandMeter) {
        if (CollectionUtils.isEmpty(areaList)) {
            return CharConstant.CHAR_EMPTY;
        }

        List<HdMapElementGeoDO> hdMapElementGeoDOS = hdMapElementPolygonConvert.toHdMapElementPolygonDO(
                HdMapElementTypeEnum.getMapNameByArea(areaList.get(0)),
                hdMapAdapter.queryHdMapAreaWgs84(areaList, position, hdMapVersion, vin, meter));

        if (expandMeter == null) {
            return CharConstant.CHAR_EMPTY;
        }
        HdMapElementGeoDO hdMapElementGeoDO = hdMapElementGeoDOS.stream()
                .filter(thisHdMapElementPolygonDO -> {
                    PolygonDO polygonDO = thisHdMapElementPolygonDO.getPolygonDO();
                    PolygonDO expandPolygonDO = GeoToolsUtil.expandPolygonDO(polygonDO, expandMeter);
                    if (expandPolygonDO == null) {
                        return false;
                    }
                    return expandPolygonDO.isInPolygon(position);
                }).findFirst().orElse(null);
        if (hdMapElementGeoDO == null) {
            return CharConstant.CHAR_EMPTY;
        }
        log.info(
                "isInHdMapAreaWGS84, areaEnum:{}, position:{}, hdMapVersion:{}, vin:{}, meter:{}, expandMeter:{}, type:{}",
                areaList, position, hdMapVersion, vin, meter, expandMeter, hdMapElementGeoDO.getElementType());
        return hdMapElementGeoDO.getElementType();

    }

    /**
     * 是否在禁停区周围的xm内（），返回禁停区的类型 如果满足多个禁停区，返回第一个禁停区的类型
     * 引擎调用
     * 
     * @param queryDTO
     * @return
     */
    @Override
    public String inRestrictParking(RiskRestrictQueryDTO queryDTO) {
        if (queryDTO == null || !queryDTO.valid()) {
            return CharConstant.CHAR_EMPTY;
        }
        List<String> restrictTypes = queryDTO.getRestrictTypes();
        PositionDO positionDO = queryDTO.getPosition();
        // 从高精地图，获取区域
        String area = queryDTO.getArea();
        // 从area里面，查询restrictType，满足要求的区域
        List<HdMapElementGeoDO> list = hdMapAdapter.searchNearby(
                SearchNearbyRequestVTO.builder().area(area).hdMapEnum(HdMapEnum.OBJECT).restrictType(restrictTypes)
                        .positionDO(positionDO).range(queryDTO.getMeter().doubleValue()).build());
        if (CollectionUtils.isEmpty(list)) {
            return CharConstant.CHAR_EMPTY;
        }
        return list.stream().filter(hdMapElementPolygonDO -> {
            // 膨胀后的区域
            PolygonDO expandPolygonDO = GeoToolsUtil.expandPolygonDO(hdMapElementPolygonDO.getPolygonDO(),
                    queryDTO.getExpandMeter().doubleValue());
            if (expandPolygonDO == null) {
                return false;
            }
            // 是否在区域内
            return expandPolygonDO.isInPolygon(positionDO);
        }).findFirst().map(HdMapElementGeoDO::getElementType).orElse(CharConstant.CHAR_EMPTY);
    }
}
