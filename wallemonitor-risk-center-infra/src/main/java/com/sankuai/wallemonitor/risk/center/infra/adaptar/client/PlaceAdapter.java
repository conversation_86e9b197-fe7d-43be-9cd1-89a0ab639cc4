package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.walledelivery.basic.client.response.inner.place.PlaceResponse;
import com.sankuai.walledelivery.basic.client.thrift.inner.place.RpcPlaceQueryThriftService;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PlaceInfoDO;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PlaceAdapter {

    @ThriftClientProxy(remoteAppKey = AppKeyConstant.BASIC_APP_KEY, timeout = 2000)
    private RpcPlaceQueryThriftService placeQueryThriftService;

    /**
     * 查询场地
     *
     * @param placeIdList
     * @return
     */
    public List<PlaceInfoDO> queryByPlaceCode(List<String> placeIdList) {
        if (CollectionUtils.isEmpty(placeIdList)) {
            return new ArrayList<>();
        }
        try {
            ThriftResponse<List<PlaceResponse>> placeResponseDTO = placeQueryThriftService.batchQueryByPlaceIdList(
                    placeIdList);

            if (placeResponseDTO == null || CollectionUtils.isEmpty(placeResponseDTO.getData())) {
                return new ArrayList<>();
            }
            return placeResponseDTO.getData().stream()
                    .map(placeResponse -> PlaceInfoDO.builder()
                            .placeName(placeResponse.getName())
                            .city(placeResponse.getCity())
                            .placeCode(placeResponse.getPlaceId()).build()).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("场地查询异常", e);
            return new ArrayList<>();
        }
    }

}
