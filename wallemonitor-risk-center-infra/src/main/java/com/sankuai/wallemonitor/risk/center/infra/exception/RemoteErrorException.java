package com.sankuai.wallemonitor.risk.center.infra.exception;

import com.sankuai.walleeve.commons.enums.ErrorCode;
import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;

/**
 * 调用三方服务异常
 *
 * <AUTHOR>
 * @Date 2022/10/17
 */
public class RemoteErrorException extends ErrorCodeException {


    public RemoteErrorException(String message) {
        super(ResponseCodeEnum.REMOTE_ERROR.getCode(), message);
    }

    public RemoteErrorException(int code, String message) {
        super(code, message);
    }

    public RemoteErrorException(int code) {
        super(code, ResponseCodeEnum.REMOTE_ERROR.getMessage());
    }

    public RemoteErrorException(int code, String format, Object... arguments) {
        super(code, format, arguments);
    }

    public RemoteErrorException(ErrorCode errors, Object... arguments) {
        super(errors, arguments);
    }

    public RemoteErrorException(ResponseCodeEnum responseCode) {
        super(responseCode.getCode(), responseCode.getMessage());
    }
}
