package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseRelatedServiceRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseRelatedServiceRecordDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskCaseRelatedServiceRecordConvert extends
        SingleConvert<RiskCaseRelatedServiceRecord, RiskCaseRelatedServiceRecordDO> {

    @Override
    RiskCaseRelatedServiceRecordDO toDO(RiskCaseRelatedServiceRecord riskCaseRelatedServiceRecord);

    @Override
    RiskCaseRelatedServiceRecord toPO(RiskCaseRelatedServiceRecordDO riskCaseRelatedServiceRecordDO);

}
