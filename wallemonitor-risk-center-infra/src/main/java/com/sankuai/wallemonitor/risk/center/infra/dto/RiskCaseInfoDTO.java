package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseInfoDTO {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * caseId
     */
    private String caseId;

    /**
     * 风险事件类型
     */
    private Integer type;

    /**
     * 业务站ID
     */

    private String placeCode;

    /**
     * 状态，10-待处置|20-处置中|30-已解除（包含已取消）
     */
    private Integer status;

    /**
     * 播报消息ID
     */
    private String messageId;

    /**
     * 播报消息最新版本号
     */
    private String messageVersion;

    /**
     * 外部事件ID
     */
    private String eventId;

    /**
     * 外部事件来源，1-保障系统|2-车端PNC|3-运维状态监控
     */
    private Integer source;

    /**
     * 事件发生时间
     */
    private Date occurTime;

    /**
     * 事件完结时间
     */
    private Date closeTime;

    /**
     * 拓展信息
     */
    private String extInfo;

    /**
     * 是否删除
     */
    @Builder.Default
    private IsDeleteEnum isDeleted = IsDeleteEnum.NOT_DELETED;

    /**
     * 是否呼叫云控
     */
    private Integer mrmCalled;

}