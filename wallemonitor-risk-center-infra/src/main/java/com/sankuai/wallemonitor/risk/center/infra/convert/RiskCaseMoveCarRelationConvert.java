package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseMoveCarRelation;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseMoveCarRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskCaseMoveCarRelationConvert extends
        SingleConvert<RiskCaseMoveCarRelation, RiskCaseMoveCarRelationDO> {

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    RiskCaseMoveCarRelationDO toDO(RiskCaseMoveCarRelation riskCaseMoveCarRelation);

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    RiskCaseMoveCarRelation toPO(RiskCaseMoveCarRelationDO riskCaseMoveCarRelationDO);

}
