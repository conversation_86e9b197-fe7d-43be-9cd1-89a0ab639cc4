package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo;

import com.sankuai.wallemonitor.risk.center.infra.vto.result.OperationDataVTO;
import java.util.List;
import java.util.Map;

/**
 * 运营数据仓储层
 *
 * <AUTHOR>
 * @date 2025/06/15
 */
public interface OperationDataRepository {

    /**
     * 根据ID获取运营数据
     *
     * @param id 运营数据ID
     * @return 运营数据，如果未找到返回null
     */
    OperationDataVTO getOperationDataById(String id);

    /**
     * 批量根据ID获取运营数据
     *
     * @param ids 运营数据ID列表
     * @return ID到运营数据的映射，未找到的ID不会包含在结果中
     */
    Map<String, OperationDataVTO> getOperationDataByIds(List<String> ids);

} 