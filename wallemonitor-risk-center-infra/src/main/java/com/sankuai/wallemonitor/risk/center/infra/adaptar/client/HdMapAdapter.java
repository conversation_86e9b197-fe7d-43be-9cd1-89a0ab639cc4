package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.davidmoten.rtree.Entry;
import com.github.davidmoten.rtree.RTree;
import com.github.davidmoten.rtree.geometry.Geometries;
import com.github.davidmoten.rtree.geometry.Geometry;
import com.github.davidmoten.rtree.geometry.Point;
import com.github.davidmoten.rtree.geometry.Rectangle;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.meituan.xframe.config.annotation.ConfigValueListener;
import com.meituan.xframe.config.vo.ConfigChangedEvent;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.BaAuthUtils;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.request.MapElementRequestDTO;
import com.sankuai.wallemonitor.risk.center.api.vo.HdMapElementGeoVO;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppPropertiesConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.HdMapElementPolygonConvert;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapElementTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.HdMapTimeOutException;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.CacheUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeTemplateConstant;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GeometryDataVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GeometryFeatureVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GeometryPolygonVTO;
import java.io.Serializable;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class HdMapAdapter {


    /**
     * 获取高精地图元素URI
     */
    public static final String QUERY_MAP_INFO_URI = "/api/map/element/info";

    @ConfigValue(key = LionKeyConstant.LION_KEY_HD_MAP_ELEMENT_QUERY_CONFIG, defaultValue = "{}")
    private HdMapConfig HD_MAP_CONFIG;

    /**
     * 查询区域的最新版本
     */
    private static final String GET_LATEST_AREA_VERSION_URL = "/api/release/get/newest/before";

    /**
     * 获取最新版本
     */
    private static final String GET_HD_MAP_URL = "/api/map/geojson/url/list";

    /**
     * 获取区域的摘要信息
     */
    private static final String GET_AREA_INFO_URL = "/api/optarea/get/area";

    @Value(AppPropertiesConstant.CONFIG_VALUE_HD_MAP_CLIENT_ID)
    private String clientId;

    @Value(AppPropertiesConstant.CONFIG_VALUE_HD_MAP_CLIENT_SECRET)
    private String secret;

    @Value(AppPropertiesConstant.CONFIG_VALUE_HD_MAP_DOMAIN)
    private String hdDomain;

    @Value(AppPropertiesConstant.CONFIG_VALUE_AD_MAP_DOMAIN)
    private String adDomain;

    @ConfigValue(key = LionKeyConstant.LION_KEY_HD_MAP_ELEMENT_QUERY_CONFIG, value = "", defaultValue = "{}", allowBlankValue = true)
    private HdMapConfig hdMapConfig;

    @Resource
    private HdMapElementPolygonConvert hdMapElementPolygonConvert;
    /**
     * 计算经纬度每米对应的距离，用于计算经纬度之间的距离
     */
    private static final double METER_PER_LATITUDE = 111000.0;

    /**
     * 推送刷新机制
     * 
     */
    @ConfigValueListener(key = LionKeyConstant.LION_KEY_HD_MAP_ELEMENT_QUERY_CONFIG)
    public void listenerHdMapConfigChange(ConfigChangedEvent configChangedEvent) {
        String newValue = configChangedEvent.getValue();

        if (StringUtils.isBlank(newValue)) {
            // 如果新的为空，不做刷新处理，自然失效即可
            return;
        }
        HdMapConfig newHdMapConfig = JacksonUtils.from(newValue, new TypeReference<HdMapConfig>() {});
        if (Objects.isNull(newHdMapConfig)) {
            return;
        }
        // 取旧配置
        String oldValue = configChangedEvent.getOldValue();
        HdMapConfig oldHdMapConfig = HdMapConfig.builder().build();
        if (StringUtils.isNotBlank(oldValue)) {
            oldHdMapConfig = JacksonUtils.from(oldValue, new TypeReference<HdMapConfig>() {});
        }
        Set<Pair<String, String>> newAreaAndTypeSet = newHdMapConfig
                .getLoadAreaList().stream().map(area -> newHdMapConfig.getLoadTypeList().stream()
                        .map(type -> new Pair<>(area, type)).collect(Collectors.toList()))
                .flatMap(List::stream).collect(Collectors.toSet());
        Set<Pair<String, String>> oldAreaAndTypeSet = Optional.ofNullable(oldHdMapConfig).orElse(HdMapConfig.builder()
                        .build())
                .getLoadAreaList().stream().map(area -> hdMapConfig.getLoadTypeList().stream()
                        .map(type -> new Pair<>(area, type)).collect(Collectors.toList()))
                .flatMap(List::stream).collect(Collectors.toSet());
        // 求集合的差
        Set<Pair<String, String>> diff = SetUtils.difference(newAreaAndTypeSet, oldAreaAndTypeSet);
        if (CollectionUtils.isEmpty(diff)) {
            return;
        }
        diff.forEach(diffAreaAndType -> {
            String area = diffAreaAndType.getKey();
            String type = diffAreaAndType.getValue();
            try {
                mapElementCache.refresh(HdMapAreaKeyDTO.builder().area(area).mapType(type).build());
            } catch (Exception e) {
                log.error("刷新地图缓存失败" + area + CharConstant.CHAR_XH + type, e);
            }
        });

    }

    /**
     * 每个区域地图的元素快查
     */
    public final LoadingCache<HdMapAreaKeyDTO, RTree<HdMapElementGeoDO, Geometry>> mapElementCache = CacheBuilder
            .newBuilder().refreshAfterWrite(5, TimeUnit.HOURS)  // 提前1小时异步刷新
            .build(new CacheLoader<HdMapAreaKeyDTO, RTree<HdMapElementGeoDO, Geometry>>() {
                @Override
                public RTree<HdMapElementGeoDO, Geometry> load(HdMapAreaKeyDTO key) {
                    return loadMapElementCache(key);
                }

                @Override
                public ListenableFuture<RTree<HdMapElementGeoDO, Geometry>> reload(HdMapAreaKeyDTO key,
                        RTree<HdMapElementGeoDO, Geometry> oldValue) {
                    // 异步刷新实现
                    return MoreExecutors.listeningDecorator(ParallelExecutor.getExecutor("map_loader"))
                            .submit(() -> {
                                try {
                                    return loadMapElementCache(key);
                                } catch (Exception e) {
                                    log.error("异步刷新地图失败", e);
                                    return oldValue;
                                }
                            });
                }
            });

    public final static Map<String, HdMapElementGeoDO> id2ElementMap = new ConcurrentHashMap<>();

    /**
     * 根据类型获取高精地图的区域数据
     *
     * @param areaList
     * @param vin
     * @return
     */
    public List<GeometryFeatureVTO> queryHdMapAreaWgs84(List<String> areaList, PositionDO positionDO,
            String hdMapVersion, String vin, Double distance) {
        if (CollectionUtils.isEmpty(areaList)) {
            return new ArrayList<>();
        }
        if (StringUtils.isBlank(hdMapVersion)) {
            log.warn(vin, new SystemException("hdMapVersion不存在"));
            return new ArrayList<>();
        }
        String url = hdDomain + QUERY_MAP_INFO_URI;
        MapQueryRequestParam mapQueryRequestParam = MapQueryRequestParam.builder().build();
        mapQueryRequestParam.setLat(positionDO.getLatitude());
        mapQueryRequestParam.setLon(positionDO.getLongitude());
        mapQueryRequestParam.setType(String.join(CharConstant.CHAR_COMMA, areaList));
        //设置版本和距离
        mapQueryRequestParam.setDistance(distance);
        //构建查询的map
        Map<String, String> queryMap = JacksonUtils.from(JacksonUtils.to(mapQueryRequestParam),
                new TypeReference<Map<String, String>>() {
                });
        Map<String, String> baAuthMap = getBaAuthMap(HttpMethod.GET.name(), QUERY_MAP_INFO_URI);
        //
        try {
            EveHttpResponse<HdMapResponse<List<HdAreaDataResponse>>> hdResponse = HttpUtils.get(queryMap, url,
                    baAuthMap,
                    new TypeReference<HdMapResponse<List<HdAreaDataResponse>>>() {
                    });
            if (hdResponse == null || hdResponse.getCode() != HttpStatus.OK.value() || hdResponse.getData() == null
                    || CollectionUtils.isEmpty(hdResponse.getData().getData())) {
                return new ArrayList<>();
            }
            return hdResponse.getData().getData().stream()
                    .map(hdAreaDataResponse -> {
                        GeometryFeatureVTO geometryFeatureVTO = GeometryFeatureVTO.builder()
                                .geometry(hdAreaDataResponse.getGeometry()).build();
                        // 参数的覆盖
                        geometryFeatureVTO.put(GeoElementTypeKeyConstant.LANE_AREA_TYPE, areaList.get(0));
                        geometryFeatureVTO.put(GeoElementTypeKeyConstant.LANE_TYPE,
                                HdMapElementTypeEnum.CITY_DRIVING.getValue());
                        return geometryFeatureVTO;
                    }).collect(
                    Collectors.toList());
        } catch (SocketTimeoutException e) {
            throw new HdMapTimeOutException("获取高精地图区域数据超时");
        } catch (Exception e) {
            log.error("获取高精地图区域数据未知异常", e);
            return new ArrayList<>();
        }
    }

    public HdMapElementGeoDO getLaneById(String laneId) {
        if (StringUtils.isBlank(laneId)) {
            return null;
        }
        return id2ElementMap.get(laneId);
    }

    /**
     * 获取高精地图版本
     *
     * @return
     */
    public String getLatestHdVersion(String placeCode) {
        if (StringUtils.isBlank(placeCode)) {
            return null;
        }
        HdMapVersionRequest hdMapVersionRequest = HdMapVersionRequest.builder().area(placeCode)
                .date(DatetimeUtil.formatDate(new Date(), DateTimeTemplateConstant.YEAR_MONTH_DAY_SPLIT_BY_HX))
                .suffix("r").build();
        Map<String, String> queryMap = JacksonUtils.from(JacksonUtils.to(hdMapVersionRequest),
                new TypeReference<Map<String, String>>() {});
        try {
            EveHttpResponse<HdMapResponse<HdMapVersionResponse>> mapVersionRes = HttpUtils.get(queryMap, adDomain,
                    GET_LATEST_AREA_VERSION_URL, getBaAuthMap(HttpMethod.GET.name(), GET_LATEST_AREA_VERSION_URL),
                    new TypeReference<HdMapResponse<HdMapVersionResponse>>() {});
            if (mapVersionRes == null || mapVersionRes.getCode() != HttpStatus.OK.value()
                    || mapVersionRes.getData() == null || mapVersionRes.getData().getData() == null) {
                log.warn(placeCode, new SystemException("查询高精地图的版本异常"));
                return null;
            }
            // 获取高精地图当前placeCode的最新版本
            return mapVersionRes.getData().getData().getHdmapVersion();
        } catch (Exception e) {
            log.error(placeCode + "查询高精地图的版本未知异常", e);
            return null;
        }
    }

    /**
     * 获取场地的S3链接
     *
     * @param area
     * @return
     */
    public Map<HdMapEnum, String> getHdMapS3UrlList(String area) {
        if (StringUtils.isBlank(area)) {
            return null;
        }
        String hdMapVersion = getLatestHdVersion(area);
        if (StringUtils.isBlank(hdMapVersion)) {
            return null;
        }
        HdMapS3UrlRequest hdMapS3UrlRequest = HdMapS3UrlRequest.builder().proj(CoordinateSystemEnum.WGS84.getType())
                .version(hdMapVersion).build();
        //
        Map<String, String> queryMap = JacksonUtils.from(JacksonUtils.to(hdMapS3UrlRequest),
                new TypeReference<Map<String, String>>() {});
        try {
            EveHttpResponse<HdMapResponse<List<HdMapS3UrlResponse>>> s3UrlResponse = HttpUtils.get(queryMap, adDomain,
                    GET_HD_MAP_URL, getBaAuthMap(HttpMethod.GET.name(), GET_HD_MAP_URL),
                    new TypeReference<HdMapResponse<List<HdMapS3UrlResponse>>>() {});
            if (s3UrlResponse == null || s3UrlResponse.getCode() != HttpStatus.OK.value()
                    || s3UrlResponse.getData() == null || s3UrlResponse.getData().getData() == null) {
                log.warn(area, new SystemException("查询高精地图的版本异常"));
                return new HashMap<>();
            }
            // 获取高精地图当前placeCode的最新版本
            return s3UrlResponse.getData().getData().stream().map(hdMapResult -> {
                String pureUrl = HttpUtils.removeAllParamUrl(hdMapResult.getUrl());
                // ....geojson/utm/borunceshichang_hdmap_v3.2.11.r/signal.geojson
                String mapType = StringUtils.substringBefore(HttpUtils.getLastPath(pureUrl), CharConstant.CHAR_DH);
                HdMapEnum hdMapEnum = HdMapEnum.fromValue(mapType);
                if (hdMapEnum == null) {
                    return null;
                }
                return new Pair<>(hdMapEnum, hdMapResult.getUrl());
            }).filter(Objects::nonNull).collect(Collectors.toMap(Pair::getKey, Pair::getValue, (o1, o2) -> o1));
        } catch (Exception e) {
            log.error(area + "查询高精地图的版", e);
            return new HashMap<>();
        }

    }

    @PostConstruct
    @SneakyThrows
    public void init() {
        // 初始化要加载的键列表
        // 并行加载多个键对应的值
        HD_MAP_CONFIG.getKeyList().parallelStream().forEach(key -> {
            try {
                // 执行加载逻辑
                mapElementCache.refresh(key);
            } catch (Exception e) {
                log.error("初始化地图元素缓存失败,key:{}", key, e);
            }
        });
    }

    /**
     * 获取地图元素缓存
     *
     * @return
     */
    private RTree<HdMapElementGeoDO, Geometry> getMap(String area, HdMapEnum hdMapEnum) {
        try {
            if (!hdMapConfig.shouldLoad(area, hdMapEnum)) {
                // 如果不在配置内，获取失败
                log.warn("无相关区域配置 area:{},hdMapEnum:{}，忽略加载", area, hdMapEnum);
                return null;
            }
            return mapElementCache.get(HdMapAreaKeyDTO.builder().mapType(hdMapEnum.getValue()).area(area).build());
        } catch (Exception e) {
            log.error(StringMessageFormatter.replaceMsg("获取地图元素缓存失败,area:{}", area), e);
            return null;
        }

    }

    /**
     * 加载地图元素缓存，key是 区域+地图类型
     *
     * @param areaKey
     * @return
     */
    private RTree<HdMapElementGeoDO, Geometry> loadMapElementCache(HdMapAreaKeyDTO areaKey) {
        if (areaKey == null || !areaKey.valid()) {
            return null;
        }
        // 查询区域对应的最新版本数据，
        // 批量查询s3文件
        Map<HdMapEnum, String> hdMapEnum2UrlMap = this.getHdMapS3UrlList(areaKey.getArea());
        if (MapUtils.isEmpty(hdMapEnum2UrlMap)) {
            return null;
        }
        HdMapEnum hdMapEnum = HdMapEnum.fromValue(areaKey.getMapType());
        if (!hdMapConfig.shouldLoad(areaKey.getArea(), hdMapEnum)) {
            log.warn("无需加载该地图,area:{},url:{}", areaKey, hdMapEnum2UrlMap.get(hdMapEnum));
            return null;
        }
        String url = hdMapEnum2UrlMap.get(hdMapEnum);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        try {
            // 不同的地图类型，有不同的序列化方式
            Map<String, HdMapElementGeoDO> id2PolygonDO = parseMapText(areaKey, url);
            if (HdMapEnum.LANE_POLYGON.equals(hdMapEnum)) {
                // 再加载一下lane
                HdMapAreaKeyDTO laneKeyDTO = HdMapAreaKeyDTO.builder().area(areaKey.getArea())
                        .mapType(HdMapEnum.LANE.getValue()).build();
                String laneUrl = hdMapEnum2UrlMap.get(HdMapEnum.LANE);
                if (StringUtils.isBlank(laneUrl)) {
                    log.warn("无对应的laneUrl,area:{}", areaKey);
                    return null;
                }
                Map<String, HdMapElementGeoDO> id2Lane = parseMapText(laneKeyDTO, laneUrl);
                // 填充一下
                refillLaneElementGeo(id2PolygonDO, id2Lane);
            }
            // 创建局部RTree实例
            RTree<HdMapElementGeoDO, Geometry> localTree = RTree.star().create();
            localTree = fillRtree(localTree, id2PolygonDO);
            // 将局部RTree实例添加到列表中
            return localTree;
        } catch (Exception e) {
            log.error("load地图异常", e);
            return null;
        }
    }

    /**
     * 填充lane信息
     *
     * @param id2PolygonDO
     * @param id2Lane
     */
    private void refillLaneElementGeo(Map<String, HdMapElementGeoDO> id2PolygonDO,
            Map<String, HdMapElementGeoDO> id2Lane) {
        if (MapUtils.isEmpty(id2PolygonDO)) {
            return;
        }
        id2PolygonDO.forEach((id, hdMapElementGeoDO) -> {
            HdMapElementGeoDO laneElement = id2Lane.get(id);
            if (laneElement == null) {
                log.warn("id:{} 的lane找不到中心线相关信息", id);
                return;
            }
            hdMapElementGeoDO.setMiddleLinePoints(laneElement.getMiddleLinePoints());
            // 放入全部的属性，lane的properties是
            hdMapElementGeoDO.put(laneElement.getProperties());
            id2ElementMap.put(id, hdMapElementGeoDO);
        });
    }

    private RTree<HdMapElementGeoDO, Geometry> fillRtree(RTree<HdMapElementGeoDO, Geometry> tree,
            Map<String, HdMapElementGeoDO> polygonDOList) {
        if (MapUtils.isEmpty(polygonDOList)) {
            return tree;
        }
        for (Map.Entry<String, HdMapElementGeoDO> hdMapElementGeoDOEntry : polygonDOList.entrySet()) {
            HdMapElementGeoDO hdMapElementGeoDO = hdMapElementGeoDOEntry.getValue();
            if (hdMapElementGeoDO.getPolygonDO() == null) {
                continue;
            }
            // 插入一些多边形（WGS84坐标系）
            Rectangle rectangle = GeoToolsUtil.getBoundingRectanglePolygon(hdMapElementGeoDO.getPolygonDO());
            if (rectangle == null) {
                continue;
            }
            tree = tree.add(hdMapElementGeoDO, rectangle);
        }
        return tree;
    }

    /**
     * 搜索附近的元素
     *
     * @param request
     * @return
     */
    public List<HdMapElementGeoDO> searchNearby(SearchNearbyRequestVTO request) {
        String area = request.getArea();
        HdMapEnum hdMapEnum = request.getHdMapEnum();
        PositionDO positionDO = request.getPositionDO();
        Double range = request.getRange();
        List<String> restrictType = request.getRestrictType();
        if (StringUtils.isBlank(area) || positionDO == null || positionDO.invalid() || range == null) {
            // 参数不合法或者区域为空的是
            log.warn("参数不合法, request = {}", JacksonUtils.to(request));
            return new ArrayList<>();
        }
        // 转换成wgs坐标
        PositionDO wgs84Position = GeoToolsUtil.transferCoordinateSystemEnum(positionDO, CoordinateSystemEnum.WGS84);
        if (wgs84Position == null) {
            log.warn(positionDO.toString(), new SystemException("坐标转换异常"));
            return new ArrayList<>();
        }
        // 定义检索范围
        Point searchPoint = Geometries.point(wgs84Position.getLongitude(), wgs84Position.getLatitude());
        // 搜索转换为度
        double rangeInDegrees = range / METER_PER_LATITUDE;
        // 检索，同一个点同类型的检索，默认缓存 ，默认记录600个点
        List<HdMapElementGeoDO> list = CacheUtils.doCache(this, 1L, TimeUnit.MINUTES).searchElementNearby(area,
                hdMapEnum, searchPoint, rangeInDegrees);
        return list.stream().filter((entry) -> {
            // 获取外接矩形的
            return (CollectionUtils.isNotEmpty(restrictType)
                    && restrictType.contains(entry.getElementType()));
        }).collect(Collectors.toList());
    }

    /**
     * 检索车道元素
     * 
     * @param area
     * @param hdMapEnum
     * @param searchPoint
     * @param rangeInDegrees
     * @return
     */
    public List<HdMapElementGeoDO> searchElementNearby(String area, HdMapEnum hdMapEnum, Point searchPoint,
            double rangeInDegrees) {
        RTree<HdMapElementGeoDO, Geometry> tree = getMap(area, hdMapEnum);
        if (tree == null) {
            return new ArrayList<>();
        }
        List<Entry<HdMapElementGeoDO, Geometry>> initialResults = tree.search(searchPoint, rangeInDegrees).toList()
                .toBlocking().first();
        if (CollectionUtils.isEmpty(initialResults)) {
            return new ArrayList<>();
        }
        return initialResults.stream().map(Entry::value).collect(Collectors.toList());
    }

    /**
     * 根据地图类型查询地图元素
     *
     * @param requestDTO
     * @return
     */
    public List<HdMapElementGeoVO> searchMapElementByMapElementRequestDTO(MapElementRequestDTO requestDTO) {
        if (requestDTO == null || requestDTO.getLatitude() == null || requestDTO.getLongitude() == null) {
            return new ArrayList<>();
        }
        List<HdMapElementGeoDO> list = this.searchNearby(SearchNearbyRequestVTO.builder()
                .range(requestDTO.getDistance())
                .restrictType(HdMapElementTypeEnum.getElementByMapType(requestDTO.getMapType()))
                .hdMapEnum(HdMapEnum.fromValue(requestDTO.getMapType()))
                .area(getAreaByHdMapVersion(requestDTO.getHdMapVersion()))
                .positionDO(PositionDO
                        .getPosition(requestDTO.getLongitude(), requestDTO.getLatitude(), CoordinateSystemEnum.WGS84))
                .build());
        List<HdMapElementGeoVO> hdMapElementGeoVOS = hdMapElementPolygonConvert.toHdMapElementGeoVO(list);
        return hdMapElementGeoVOS;
    }

    /**
     * 取高精地图关联的area
     *
     * @param hdMapVersion
     * @return
     */
    private String getAreaByHdMapVersion(String hdMapVersion) {
        if (StringUtils.isBlank(hdMapVersion)) {
            return null;
        }
        // hualikan_hdmap_v5.185.0.r 取第一个
        String[] split = hdMapVersion.split("_");
        String area = split[0];
        return area;
    }

    /**
     * 解析地图文本
     *
     * @param areaDTO
     * @param mapUrl
     * @return
     */
    @SneakyThrows
    private Map<String, HdMapElementGeoDO> parseMapText(HdMapAreaKeyDTO areaDTO,
            String mapUrl) {
        log.info("parseMapText,area:{},mapUrl:{}", areaDTO, mapUrl);
        try {
            EveHttpResponse<String> mapTextResponse = HttpUtils.getPlainText(new HashMap<>(), mapUrl,
                    CharConstant.CHAR_EMPTY, new HashMap<>(), hdMapConfig.getTimeout());
            // 取字符串，进行序列化
            String mapText = Optional.ofNullable(mapTextResponse).map(EveHttpResponse::getData)
                    .orElse(CharConstant.CHAR_EMPTY);
            CheckUtil.isNotBlank(mapText, "地图数据为空");
            GeometryPolygonVTO geometryPolygonVTO = JacksonUtils.from(mapText, GeometryPolygonVTO.class);
            if (geometryPolygonVTO == null || CollectionUtils.isEmpty(geometryPolygonVTO.getFeatures())) {
                return new HashMap<>();
            }
            return hdMapElementPolygonConvert.toHdMapElementPolygonDOMap(areaDTO.getMapType(),
                    geometryPolygonVTO.getFeatures());
        } catch (Exception e) {
            log.error("parseMapText异常", e);
            throw e;
        }
    }

    /**
     * 根据area获取utmZone
     *
     * @param area
     * @return
     */
    public Integer getUtmZoneByArea(String area) {
        try {
            AreaVTO areaVTO = CacheUtils.doCache(this, 1L, TimeUnit.HOURS).getAreaInfoByArea(area);
            if (areaVTO == null) {
                return null;
            }
            // 获取zone
            return areaVTO.getZone();
        } catch (Exception e) {
            log.warn("获取地区信息缓存失败", e);
        }
        return null;

    }

    /**
     * 获取区域信息
     *
     * @param area
     * @return
     */
    public AreaVTO getAreaInfoByArea(String area) {
        try {
            Map<String, String> param = new HashMap<>();
            param.put("areaName", area);
            EveHttpResponse<HdMapResponse<AreaVTO>> areInfo = HttpUtils.get(param, adDomain, GET_AREA_INFO_URL,
                    getBaAuthMap(HttpMethod.GET.name(), GET_AREA_INFO_URL),
                    new TypeReference<HdMapResponse<AreaVTO>>() {});
            if (areInfo == null || areInfo.getData() == null || areInfo.getData().getData() == null) {
                return null;
            }
            return areInfo.getData().getData();
        } catch (Exception e) {
            log.error("getAreaInfoByArea", e);
            return null;
        }
    }

    private Map<String, String> getBaAuthMap(String method, String api) {
        Map<String, String> baAuthMap = BaAuthUtils.genMWSAuthHeader(method, api, clientId, secret,
                CharConstant.CHAR_EMPTY);
        baAuthMap.put("X-Date", baAuthMap.get("Date"));
        return baAuthMap;
    }

    public List<HdMapElementGeoDO> queryByLaneIdList(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return idList.stream().map(this::getLaneById).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class HdMapVersionRequest {

        private String area;

        private String date;

        private String suffix;

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class HdMapS3UrlRequest {

        private String proj;

        private String version;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HdMapS3UrlResponse {
        private String proj;
        private String version;
        private String type;
        private String url;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class HdMapVersionResponse {
        private int id;
        private String admapVersion;
        private String hdmapVersion;
        private String tilemapVersion;
        private String ndtmapVersion;
        private boolean syncedWithCSV;
        private int majorVersion;
        private int minorVersion;
        private int patchVersion;
        private int status;
        private String addTime;
        private String updateTime;

    }
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MapQueryRequestParam {

        /**
         * 经度
         */
        private Double lon;
        /**
         * 纬度
         */
        private Double lat;
        /**
         * 高精地图版本
         */
        private String version;
        /**
         * 类型
         */
        private String type;
        /**
         * 距离，单位：米
         */
        @Builder.Default
        private double distance = 5.0;
        /**
         * 备选个数
         */
        @Builder.Default
        private int count = 1;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HdMapResponse<T> {

        private int code;
        private String message;
        private T data;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HdAreaDataResponse {

        private String type;
        private Double distance;
        private String guid;
        private GeometryDataVTO geometry;



    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class HdMapConfig {

        @Default
        private Set<String> loadAreaList = new HashSet<>();

        @Default
        private Set<String> loadTypeList = new HashSet<>();

        private Integer timeout;

        /**
         * 地图类型
         *
         * @param area
         * @param hdMapEnum
         * @return
         */
        public boolean shouldLoad(String area, HdMapEnum hdMapEnum) {
            if (StringUtils.isBlank(area) || hdMapEnum == null) {
                return false;
            }
            return loadAreaList.contains(area) && loadTypeList.contains(hdMapEnum.getValue());
        }

        public List<HdMapAreaKeyDTO> getKeyList() {
            if (CollectionUtils.isEmpty(loadAreaList) || CollectionUtils.isEmpty(loadTypeList)) {
                return new ArrayList<>();
            }
            List<HdMapAreaKeyDTO> keyList = new ArrayList<>();
            for (String area : loadAreaList) {
                for (String hdMapType : loadTypeList) {
                    keyList.add(HdMapAreaKeyDTO.builder().area(area).mapType(hdMapType).build());
                }
            }
            return keyList;
        }
    }

    /**
     * 拆分高精地图的不同元素检索
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @ToString
    public static class HdMapAreaKeyDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 区域
         */
        private String area;

        /**
         * 地图类型
         *
         * @see com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum
         */
        private String mapType;

        public boolean valid() {
            return StringUtils.isNotBlank(area) && StringUtils.isNotBlank(mapType);
        }
    }

    /**
     * 搜索附近的元素请求
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @ToString
    public static class SearchNearbyRequestVTO {
        private String area;
        private HdMapEnum hdMapEnum;
        private PositionDO positionDO;
        private List<String> restrictType;
        private Double range;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AreaVTO {

        private Long id;

        private String name;

        private String alias;

        private String guid;

        private String proj;

        private Integer zone;

        private String province;

        /**
         * 区域类型:
         * 0-未知区域类型
         * 1-运营区域
         * 2-路测区域
         * 3-研发区域
         */
        private Integer areaType;
    }

}
