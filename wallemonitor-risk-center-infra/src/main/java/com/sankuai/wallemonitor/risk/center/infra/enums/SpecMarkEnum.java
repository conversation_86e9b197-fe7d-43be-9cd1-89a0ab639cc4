package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特殊标记类型枚举
 */
@AllArgsConstructor
@Getter
public enum SpecMarkEnum {
    NONE(1, "无特殊标记"),
    BUS_ONLY(2, "公交专用道"),
    LEFT_WAITTING(3, "左转待转区"),
    ROUNDABOUT(6, "环岛"),
    S20(7, "S20标记"),
    TRANSFER(8, "换乘区域"),
    MAIN_SIDE(9, "主辅路");

    private final int code;
    private final String desc;

    /**
     * 根据 code 查询枚举
     *
     * @param code 特殊标记代码
     * @return 对应的枚举值
     */
    public static SpecMarkEnum findByCode(int code) {
        for (SpecMarkEnum specMark : SpecMarkEnum.values()) {
            if (specMark.getCode() == code) {
                return specMark;
            }
        }
        return null;
    }
}