package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseMoveCarRelationConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCaseMoveCarRelationMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseMoveCarRelation;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseMoveCarRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseMoveCarRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseMoveCarRelationDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskCaseMoveCarRelationRepositoryImpl extends
        AbstractMapperSingleRepository<RiskCaseMoveCarRelationMapper, RiskCaseMoveCarRelationConvert, RiskCaseMoveCarRelation, RiskCaseMoveCarRelationDO> implements
        RiskCaseMoveCarRelationRepository {

    /**
     * 保存扫码挪车事件关联信息
     *
     * @param riskCaseMoveCarRelationDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskCaseMoveCarRelationDO riskCaseMoveCarRelationDO) {
        super.save(riskCaseMoveCarRelationDO);
    }

    /**
     * 根据参数查询扫码挪车事件信息
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskCaseMoveCarRelationDO> queryByParam(RiskCaseMoveCarRelationDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }
}
