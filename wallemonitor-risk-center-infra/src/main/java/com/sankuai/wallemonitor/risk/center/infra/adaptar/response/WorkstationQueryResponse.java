package com.sankuai.wallemonitor.risk.center.infra.adaptar.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkstationQueryResponse {

    private int code;
    private String message;
    private WorkstationQueryResponseData data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkstationQueryResponseData {

        /**
         * 数据总数
         */
        private Long count;

        /**
         * 查询id
         */
        private String qid;

        /**
         * 每页条数
         */
        private Integer limit;

        /**
         * 当前页数
         */
        private Integer page;

        /**
         * 数据内容
         */
        private List<CaseDetail> rows;

        @Builder
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class CaseDetail {

            /**
             * 工单id
             */
            private String caseId;
        }
    }


}
