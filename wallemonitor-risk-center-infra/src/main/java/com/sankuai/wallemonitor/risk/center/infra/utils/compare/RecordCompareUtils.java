package com.sankuai.wallemonitor.risk.center.infra.utils.compare;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.instrument.util.JsonUtil;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;

/**
 * 实体记录对比类
 */
@Slf4j
public class RecordCompareUtils {

    private static final Set<Class> BASIC_CLASS = Sets.newHashSet(Integer.class, Long.class, Byte.class, Short.class,
            Float.class, Double.class, Boolean.class);

    /**
     * 对比两个实体记录的变化 如果变更前或者变更后的map为空，返回空数组 同一个实体字段的key是一样的，为空的value为空
     */
    public static Map<String, EntityFieldChangeRecordDTO> findChange(Map<String, Object> before,
            Map<String, Object> after) {
        if (MapUtils.isEmpty(before) || MapUtils.isEmpty(after)) {
            return new HashMap<>();
        }
        Set<String> entityKeySet = new HashSet<>();
        entityKeySet.addAll(MapUtils.isNotEmpty(before) ? before.keySet() : new HashSet<>());
        entityKeySet.addAll(MapUtils.isNotEmpty(after) ? after.keySet() : new HashSet<>());
        Map<String, EntityFieldChangeRecordDTO> entityChangeDetailMap = new HashMap<>();
        for (String key : entityKeySet) {
            Object beforeValue = MapUtils.getObject(before, key);
            Object afterValue = MapUtils.getObject(after, key);
            //把原始值转成字符串
            String beforeValueStr = beforeValue == null ? "" : parseValue(beforeValue);
            String afterValueStr = afterValue == null ? "" : parseValue(afterValue);
            //取出来前后的值比较
            //这里支持从数据库获取的基本类型，数字（整形,长整形，byte,bool），字符串，
            if (!Objects.equals(beforeValueStr, afterValueStr)) {
                entityChangeDetailMap.put(key,
                        EntityFieldChangeRecordDTO.builder().key(key).before(beforeValueStr).after(afterValueStr)
                                .beforeOriginal(beforeValue)
                                .afterOriginal(afterValue)
                                .build());
            }
        }
        return entityChangeDetailMap;
    }


    public static <T> List<EntityChangeRecordDTO> findRecordsChange(List<T> beforeList, List<T> afterList,
            String listKey,
            Class<T> tClass) {
        if (CollectionUtils.isEmpty(beforeList) && CollectionUtils.isEmpty(afterList)) {
            //如果都完全为空,不需要比较
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(beforeList)) {
            beforeList = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(afterList)) {
            afterList = new ArrayList<>();
        }
        //标记哪些是新增的，哪些是被删除的 onlyA的是新增的,
        Set<String> deletedEntity = new HashSet<>();
        Set<String> newAddEntity = new HashSet<>();

        //1、构建map
        Map<String, T> beforeMap = beforeList.stream().filter(Objects::nonNull).collect(Collectors
                .toMap(item -> getListKey(item, tClass, listKey), item -> item, (oldValue, newValue) -> oldValue));
        Map<String, T> afterMap = afterList.stream().filter(Objects::nonNull).collect(Collectors
                .toMap(item -> getListKey(item, tClass, listKey), item -> item, (oldValue, newValue) -> oldValue));
        //2、获取对应的keys
        Set<String> beforeMapKeys = MapUtils.isNotEmpty(beforeMap) ? beforeMap.keySet() : new HashSet<>();
        Set<String> afterMapKeys = MapUtils.isNotEmpty(afterMap) ? afterMap.keySet() : new HashSet<>();
        //3，求onlyB,onlyA,both的不需要求
        Set<String> onlyB = beforeMapKeys.stream().filter(key -> !afterMapKeys.contains(key))
                .collect(Collectors.toSet());
        Set<String> onlyA = afterMapKeys.stream().filter(key -> !beforeMapKeys.contains(key))
                .collect(Collectors.toSet());
        //4,onlyB，要给afterMap加空对象，key为onlyB的key;onlyA，要给beforeMap加空对象,key为onlyA的key
        if (CollectionUtils.isNotEmpty(onlyB)) {
            deletedEntity.addAll(onlyB);
            afterMap.putAll(buildEmptyObj(onlyB, listKey, tClass));
        }
        if (CollectionUtils.isNotEmpty(onlyA)) {
            newAddEntity.addAll(onlyA);
            beforeMap.putAll(buildEmptyObj(onlyA, listKey, tClass));
        }
        return findRecordsChange(deletedEntity, newAddEntity, beforeMap, afterMap, tClass);

    }

    private static <T> List<EntityChangeRecordDTO> findRecordsChange(Set<String> deletedEntity,
            Set<String> newAddEntity, Map<String, T> beforeMap, Map<String, T> afterMap, Class<T> tClass) {

        List<EntityChangeRecordDTO> entityChangeRecords = new ArrayList<>();
        Set<String> entityIdSet = new HashSet<>();
        entityIdSet.addAll(MapUtils.isNotEmpty(afterMap) ? afterMap.keySet() : new HashSet<>());
        entityIdSet.addAll(MapUtils.isNotEmpty(beforeMap) ? beforeMap.keySet() : new HashSet<>());
        for (String entityId : entityIdSet) {
            Object beforeEntity = MapUtils.getObject(beforeMap, entityId);
            Object afterEntity = MapUtils.getObject(afterMap, entityId);
            //取出来前后的值比较，转换成map
            EntityChangeRecordDTO entityChangeRecord = EntityChangeRecordDTO.builder().entityId(entityId).build();
            //对比两个实体前后的变化,然后设置
            Map<String, EntityFieldChangeRecordDTO> entityChangeDetailMap = RecordCompareUtils.findChange(
                    getValueMap(beforeEntity), getValueMap(afterEntity));
            if (MapUtils.isNotEmpty(entityChangeDetailMap)) {
                //获取变更的类型
                entityChangeRecord.setEntityChangeType(
                        EntityChangeType.get(newAddEntity.contains(entityId), deletedEntity.contains(entityId)));
                entityChangeRecord.setChanges(new ArrayList<>(entityChangeDetailMap.values()));
                entityChangeRecord.setBeforeEntity(beforeEntity);
                entityChangeRecord.setAfterEntity(afterEntity);
                entityChangeRecord.setChangeMaps(entityChangeDetailMap);
                entityChangeRecords.add(entityChangeRecord);
            }
        }
        return entityChangeRecords;
    }

    /**
     * 对比两个实体记录的变化 主键为多个key组合而成
     *
     * @param beforeList
     * @param afterList
     * @param listKey
     * @param tClass
     * @param <T>
     * @return
     */
    public static <T> List<EntityChangeRecordDTO> findRecordsChange(List<T> beforeList, List<T> afterList,
            List<String> listKey, Class<T> tClass) {
        if (CollectionUtils.isEmpty(beforeList) && CollectionUtils.isEmpty(afterList)) {
            //如果都完全为空,不需要比较
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(beforeList)) {
            beforeList = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(afterList)) {
            afterList = new ArrayList<>();
        }
        //1、构建map
        Map<String, T> beforeMap = beforeList.stream().filter(Objects::nonNull).collect(Collectors
                .toMap(item -> getListKey(item, tClass, listKey), item -> item, (oldValue, newValue) -> oldValue));
        Map<String, T> afterMap = afterList.stream().filter(Objects::nonNull).collect(Collectors
                .toMap(item -> getListKey(item, tClass, listKey), item -> item, (oldValue, newValue) -> oldValue));
        //2、获取对应的keys
        Set<String> beforeMapKeys = MapUtils.isNotEmpty(beforeMap) ? beforeMap.keySet() : new HashSet<>();
        Set<String> afterMapKeys = MapUtils.isNotEmpty(afterMap) ? afterMap.keySet() : new HashSet<>();
        //3、求onlyB,onlyA,both的不需要求
        Set<String> onlyB = beforeMapKeys.stream().filter(key -> !afterMapKeys.contains(key))
                .collect(Collectors.toSet());
        Set<String> onlyA = afterMapKeys.stream().filter(key -> !beforeMapKeys.contains(key))
                .collect(Collectors.toSet());
        //标记哪些是新增的，哪些是被删除的 onlyA的是新增的,
        Set<String> deletedEntity = new HashSet<>();
        Set<String> newAddEntity = new HashSet<>();
        //4、onlyB，要给afterMap加空对象，key为onlyB的key;onlyA，要给beforeMap加空对象,key为onlyA的key
        if (CollectionUtils.isNotEmpty(onlyB)) {
            deletedEntity.addAll(onlyB);
            afterMap.putAll(buildEmptyObj(onlyB, listKey, tClass));
        }
        if (CollectionUtils.isNotEmpty(onlyA)) {
            newAddEntity.addAll(onlyA);
            beforeMap.putAll(buildEmptyObj(onlyA, listKey, tClass));
        }
        return findRecordsChange(deletedEntity, newAddEntity, beforeMap, afterMap, tClass);

    }

    public static <T> List<EntityChangeRecordDTO> findRecordsChangeWithCheckKeSet(List<T> beforeList, List<T> afterList,
            List<String> listKey, Class<T> tClass, Set<String> checkKeySets) {
        if (CollectionUtils.isEmpty(checkKeySets)) {
            return new ArrayList<>();
        }
        return findRecordsChange(beforeList, afterList, listKey, tClass).stream()
                .filter(entityChangeRecord -> RecordCompareUtils.isChangeInCheckSet(entityChangeRecord, checkKeySets))
                .collect(Collectors.toList());
    }

    public static <T> List<EntityChangeRecordDTO> findRecordsChangeEntityFilterIgnoreFields(List<T> beforeList,
            List<T> afterList,
            List<String> listKey, Class<T> tClass, Set<String> ignoreChangeFiledSet) {

        List<EntityChangeRecordDTO> entityChangeRecordList = findRecordsChange(beforeList, afterList, listKey, tClass);

        List<EntityChangeRecordDTO> filterIgnoreFieldsChangeEntityList = new ArrayList<>();
        entityChangeRecordList.forEach(x -> {
            Map<String, EntityFieldChangeRecordDTO> changeMaps = x.getChangeMaps();
            for (Map.Entry<String, EntityFieldChangeRecordDTO> item : changeMaps.entrySet()) {
                String updateFieldName = item.getKey();
                if (!ignoreChangeFiledSet.contains(updateFieldName)) {
                    filterIgnoreFieldsChangeEntityList.add(x);
                }
                break;
            }
        });
        return filterIgnoreFieldsChangeEntityList;
    }


    /**
     * 过滤掉可忽略的变更字段后, 获取仍有变更实体主键值 eg: 传入新旧两组 tripList, 过滤掉可忽略的 create_time 和 update_time 字段后, 找到仍有变更的 trip 实体, 返回
     * tripId(主键)
     */
    public static <T> List<String> findRecordsChangeFilterIgnoreFields(List<T> beforeList, List<T> afterList,
            String listKey, Set<String> ignoreChangeFiledSet, Class<T> tClass) {
        List<EntityChangeRecordDTO> entityChangeRecordList = RecordCompareUtils.findRecordsChange(beforeList, afterList,
                listKey, tClass);
        List<String> filterIgnoreFieldsChangeEntityIdList = new ArrayList<>();
        entityChangeRecordList.forEach(x -> {
            Map<String, EntityFieldChangeRecordDTO> changeMaps = x.getChangeMaps();
            for (Map.Entry<String, EntityFieldChangeRecordDTO> item : changeMaps.entrySet()) {
                String updateFieldName = item.getKey();
                if (!ignoreChangeFiledSet.contains(updateFieldName)) {
                    filterIgnoreFieldsChangeEntityIdList.add(x.getEntityId());
                }
                break;
            }
        });
        return filterIgnoreFieldsChangeEntityIdList;
    }

    public static <T> List<EntityBaseChangeRecordDTO> findBaseRecordsChange(List<T> beforeList, List<T> afterList,
            String listKey, Class<T> tClass) {
        return findRecordsChange(beforeList, afterList, listKey, tClass).stream()
                .map(item -> {
                    EntityBaseChangeRecordDTO entityBaseChangeRecordDTO = new EntityBaseChangeRecordDTO();
                    BeanUtils.copyProperties(item, entityBaseChangeRecordDTO);
                    return entityBaseChangeRecordDTO;
                }).collect(Collectors.toList());
    }

    public static <T> T checkAndGetChangedFieldEntity(T before, T after, Class<T> clazz, List<String> keyList)
            throws IllegalAccessException, InstantiationException {
        if (before == null || after == null || CollectionUtils.isEmpty(keyList)) {
            return after;
        }
        Set<String> keySet = new HashSet<>(keyList);
        T result = clazz.newInstance();
        boolean anyChanged = false;
        Field[] fields = ReflectUtils.getDeclaredFields(clazz, true);
        for (Field field : fields) {
            field.setAccessible(true);
            Object beforeValue = field.get(before);
            Object afterValue = field.get(after);
            //如果是唯一键的字段名
            if (keySet.contains(field.getName())) {
                //把他用after的进行复制
                field.set(result, afterValue);
            } else if (!Objects.equals(beforeValue, afterValue)) {
                //这里是用的hash
                field.set(result, afterValue);
                anyChanged = true;
            }
        }
        log.info("仓储对比变更后的结果:{}", result);
        return anyChanged ? result : null;
    }

    @SneakyThrows
    public static <T> T checkAndGetChangedFieldEntity(T before, T after, Class<T> tClass, String key) {
        return checkAndGetChangedFieldEntity(before, after, tClass, Collections.singletonList(key));
    }

    @SneakyThrows
    public static <T> List<T> checkAndGetChangedFieldEntity(List<T> before, List<T> after, Class<T> tClass,
            List<String> keyList) {
        Map<String, T> beforeMap = Optional.ofNullable(before).orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(x -> buildEntityId(x, tClass, keyList), x -> x, (o1, o2) -> o1));
        Map<String, T> afterMap = Optional.ofNullable(after).orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(x -> buildEntityId(x, tClass, keyList), x -> x, (o1, o2) -> o1));
        List<T> changedEntityList = new ArrayList<>();
        afterMap.forEach((entityId, x) -> {
            try {
                changedEntityList.add(checkAndGetChangedFieldEntity(beforeMap.get(entityId), afterMap.get(entityId),
                        tClass, keyList));
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            } catch (InstantiationException e) {
                throw new RuntimeException(e);
            }
        });
        return changedEntityList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @SneakyThrows
    public static <T> List<T> checkAndGetChangedFieldEntity(List<T> before, List<T> after, Class<T> tClass,
            String key) {
        return checkAndGetChangedFieldEntity(before, after, tClass, Collections.singletonList(key));
    }

    /**
     * 查找多个对象间指定key的变化
     *
     * @param beforeList
     * @param afterList
     * @param listKey
     * @param tClass
     * @param checkKeySets
     * @param <T>
     * @return
     */
    public static <T> List<EntityChangeRecordDTO> findRecordsChangeWithCheckKeSet(List<T> beforeList, List<T> afterList,
            String listKey, Class<T> tClass, Set<String> checkKeySets) {
        if (CollectionUtils.isEmpty(checkKeySets)) {
            return new ArrayList<>();
        }
        return findRecordsChange(beforeList, afterList, listKey, tClass).stream()
                .filter(entityChangeRecord -> RecordCompareUtils.isChangeInCheckSet(entityChangeRecord, checkKeySets))
                .collect(Collectors.toList());
    }

    public static <T> EntityBaseChangeRecordDTO findBaseRecordsChange(T before, T after, String listKey,
            Class<T> tClass) {
        return findRecordsChange(Collections.singletonList(before), Collections.singletonList(after), listKey, tClass)
                .stream().map(item -> {
                    EntityBaseChangeRecordDTO entityBaseChangeRecordDTO = new EntityBaseChangeRecordDTO();
                    BeanUtils.copyProperties(item, entityBaseChangeRecordDTO);
                    return entityBaseChangeRecordDTO;
                }).collect(Collectors.toList()).stream().findFirst().orElse(null);
    }

    public static <T> EntityChangeRecordDTO findRecordsChange(T before, T after, String listKey, Class<T> tClass) {
        return findRecordsChange(Collections.singletonList(before), Collections.singletonList(after), listKey, tClass)
                .stream().findFirst().orElse(null);
    }

    /**
     * 查找单个对象指定变化
     *
     * @param before
     * @param after
     * @param listKey
     * @param tClass
     * @param checkKeySets
     * @param <T>
     * @return
     */
    public static <T> EntityChangeRecordDTO findRecordsChangeWithCheckKeSet(T before, T after, String listKey,
            Class<T> tClass, Set<String> checkKeySets) {
        if (CollectionUtils.isEmpty(checkKeySets)) {
            return null;
        }
        return findRecordsChange(Collections.singletonList(before), Collections.singletonList(after), listKey, tClass)
                .stream()
                .filter(entityChangeRecord -> RecordCompareUtils.isChangeInCheckSet(entityChangeRecord, checkKeySets))
                .findFirst().orElse(null);
    }

    /**
     * 判断对象的变化的key,是否在checkSet里面,如果不在则返回false
     *
     * @param entityChangeRecord
     * @param checkKeySet
     * @return
     */
    private static boolean isChangeInCheckSet(EntityChangeRecordDTO entityChangeRecord, Set<String> checkKeySet) {
        if (MapUtils.isEmpty(entityChangeRecord.getChangeMaps())) {
            //如果为空不需要返回
            return false;
        }
        Map<String, EntityFieldChangeRecordDTO> changeDetailMap = Maps.filterKeys(entityChangeRecord.getChangeMaps(),
                checkKeySet::contains);
        if (MapUtils.isEmpty(changeDetailMap)) {
            return false;
        }
        entityChangeRecord.setChangeMaps(changeDetailMap);
        entityChangeRecord.setChanges(new ArrayList<>(changeDetailMap.values()));
        return true;
    }

    private static <T> Map<String, T> buildEmptyObj(Set<String> keys, String objKeyFiledName, Class<T> tClass) {
        if (CollectionUtils.isEmpty(keys)) {
            return new HashMap<>();
        }
        Map<String, Field> fieldMap = Arrays.stream(ReflectUtils.getDeclaredFields(tClass, true)).collect(
                Collectors.toMap(Field::getName, Function.identity(), (o1, o2) -> o1));
        Map<String, T> emptyMap = new HashMap<>();
        for (String key : keys) {
            try {
                Constructor<?>[] constructors = tClass.getConstructors();
                int count = constructors[0].getParameterCount();
                T newObj;
                if (count > 0) {
                    newObj = (T) constructors[0].newInstance(Collections.nCopies(count, null).toArray());
                } else {
                    newObj = (T) constructors[0].newInstance();
                }

                //取这个字段

                Field keyField = fieldMap.get(objKeyFiledName);
                keyField.setAccessible(true);
                //设置值
                setFileValue(keyField, newObj, key);
                emptyMap.put(key, newObj);
            } catch (Exception e) {
                log.error("compare err", e);
            }
        }
        return emptyMap;
    }

    private static <T> Map<String, T> buildEmptyObj(Set<String> keys, List<String> objKeyFiledNameList,
            Class<T> tClass) {
        if (CollectionUtils.isEmpty(keys)) {
            return new HashMap<>();
        }
        Map<String, T> emptyMap = new HashMap<>();
        for (String key : keys) {
            try {
                Map<String, Field> fieldMap = Arrays.stream(ReflectUtils.getDeclaredFields(tClass, true)).collect(
                        Collectors.toMap(Field::getName, Function.identity(), (o1, o2) -> o1));
                Constructor<?>[] constructors = tClass.getConstructors();
                int count = constructors[0].getParameterCount();
                T newObj;
                if (count > 0) {
                    newObj = (T) constructors[0].newInstance(Collections.nCopies(count, null).toArray());
                } else {
                    newObj = (T) constructors[0].newInstance();
                }
                //如果是多key的方式，需要使用这个方式
                String[] keyValues = key.split(CharConstant.CHAR_XH); // 按照分隔符拆分字段值
                for (int i = 0, objKeyFiledNameListSize = objKeyFiledNameList.size(); i < objKeyFiledNameListSize;
                        i++) {
                    String objKeyFiledName = objKeyFiledNameList.get(i);
                    Field keyField = fieldMap.get(objKeyFiledName);
                    keyField.setAccessible(true);
                    //设置值
                    setFileValue(keyField, newObj, keyValues[i]);
                    emptyMap.put(key, newObj);
                }
            } catch (Exception e) {
                log.error("compare err", e);
            }
        }
        return emptyMap;
    }

    /**
     * 设置传入类型的主键值
     *
     * @param field
     * @param obj
     * @param fileValue
     * @return
     */
    private static boolean setFileValue(Field field, Object obj, String fileValue) {
        try {
            String ftype = field.getType().getName(); //field为反射出来的字段类型
            String fstype = field.getType().getSimpleName();
            if (field.getType() == String.class) {
                field.set(obj, fileValue);
                return true;
            } else if (ftype.indexOf("java.lang.") == 0) {
                // java.lang下面类型通用转换函数
                Class<?> class1 = Class.forName(ftype);
                Method method = class1.getMethod("parse" + fixParseType(fstype), String.class);
                Object ret = method.invoke(null, fileValue);
                field.set(obj, ret);
                return true;
            } else {
                log.warn("cannot find parse error");
                return false;
            }
        } catch (Exception e) {
            log.warn("setFileValue fail!", e);
            return false;
        }
    }

    /**
     * 修正类型
     *
     * @param fstype
     * @return
     */
    private static String fixParseType(String fstype) {
        switch (fstype) {
            case "Integer":
                return "Int";
            default:
                return fstype;
        }
    }

    /**
     * 获取列表的key值
     */
    private static <T> String getListKey(T obj, Class<T> tClass, String keyName) {
        Field[] fs = ReflectUtils.getDeclaredFields(tClass, true);
        for (Field field : fs) {
            field.setAccessible(true);
            if (Objects.equals(field.getName(), keyName)) {
                try {
                    //获取这个key值
                    return field.get(obj).toString();
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return "";
    }

    public static <T> String getListKey(T obj, Class<T> tClass, List<String> keyName) {
        List<String> fieldValueList = new ArrayList<>();
        Map<String, Field> fieldMap = Arrays.stream(ReflectUtils.getDeclaredFields(tClass, true))
                .collect(Collectors.toMap(Field::getName, field -> field, (oldValue, newValue) -> oldValue));
        for (String fieldName : keyName) {
            try {
                SystemCheckUtil.isTrue(fieldMap.containsKey(fieldName),
                        StringMessageFormatter.replaceMsg("getListKey# {}类中找不到字段: {}",
                                tClass.getSimpleName(), fieldName));
                Field field = fieldMap.get(fieldName);
                field.setAccessible(true);
                //获取这个key值
                fieldValueList.add(Optional.ofNullable(field.get(obj)).map(Object::toString).orElse(""));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return Joiner.on(CharConstant.CHAR_XH).join(fieldValueList);
    }

    /**
     * 获取实体的key-value map，用于做对比
     */
    public static <T> Map<String, Object> getValueMap(T entity) {
        return JsonUtil.deSerialize(new TypeReference<Map<String, Object>>() {
        }, JsonUtil.serialize(entity));
    }

    private static String parseValue(Object obj) {

        if (null == obj) {
            return null;
        }
        // 如果是String类型，直接返回,不需要进一步序列化
        if (obj instanceof String) {
            return obj.toString();
        }
        // 如果是基本类型包装类 直接返回
        if (BASIC_CLASS.contains(obj.getClass())) {
            return obj.toString();
        }
        if (obj instanceof Collection<?>) {
            //如果是集合类型，则排序后生成一个新的，方便进行比对
            obj = ((Collection<?>) obj).stream().filter(Objects::nonNull).sorted(Comparator.comparing(Object::toString))
                    .collect(Collectors.toList());
        }
        return JsonUtil.serialize(obj);
    }

    public static <T> String buildEntityId(T obj, Class<T> tClass, List<String> keyName) {
        if (obj == null || tClass == null || CollectionUtils.isEmpty(keyName)) {
            return null;
        }
        return getListKey(obj, tClass, keyName);
    }

    @AllArgsConstructor
    @Getter
    public enum EntityChangeType {
        ADD,
        CHANGE,
        DELETE;

        /**
         * 获取变更类型
         *
         * @param isAdded
         * @param isDeleted
         * @return
         */
        public static EntityChangeType get(boolean isAdded, boolean isDeleted) {
            if (isAdded) {
                return ADD;
            }
            if (isDeleted) {
                return DELETE;
            }
            return CHANGE;
        }


    }
}
