package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("negative_public_event_detail")
public class NegativePublicEventDetail {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * eventId
     */
    @TableField("event_id")
    @TableUnique
    private String eventId;

    /**
     * 事件性质
     */
    @TableField("nature")
    private Integer nature;

    /**
     * 问题分类
     */
    @TableField("category")
    private String category;

    /**
     * 问题分类-其他-描述
     */
    @TableField("other_category_desc")
    private String otherCategoryDesc;

    /**
     * 事件等级
     */
    @TableField("level")
    private Integer level;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 情况说明
     */
    @TableField("condition_desc")
    private String conditionDesc;

    /**
     * 确定事件性质时间
     */
    @TableField("determine_nature_time")
    private Date determineNatureTime;

    /**
     * 确定事件性质操作人
     */
    @TableField("determine_nature_operator")
    private String determineNatureOperator;

    /**
     * 其他-问题归因描述
     */
    @TableField("other_reason_desc")
    private String otherReasonDesc;

    /**
     * 排查结果
     */
    @TableField("check_result")
    private String checkResult;

    /**
     * 确定问题归因时间
     */
    @TableField("determine_reason_time")
    private Date determineReasonTime;

    /**
     * 确定问题归因操作人
     */
    @TableField("determine_reason_operator")
    private String determineReasonOperator;

    /**
     * 解决程度
     */
    @TableField("handle_degree")
    private Integer handleDegree;

    /**
     * 处置结果说明
     */
    @TableField("handle_result_desc")
    private String handleResultDesc;

    /**
     * 处置结果说明文件链接
     */
    @TableField("handle_result_desc_file_link")
    private String handleResultDescFileLink;

    /**
     * 处置完成时间
     */
    @TableField("complete_time")
    private Date completeTime;

    /**
     * 处置完成操作人
     */
    @TableField("complete_operator")
    private String completeOperator;

    /**
     * 复盘信息
     */
    @TableField("review_info")
    private String reviewInfo;

    /**
     * 拓展字段,json格式
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 定因提醒
     */
    @TableField("reason_noticed")
    private Boolean reasonNoticed;

    /**
     * 处置提醒
     */
    @TableField("handle_noticed")
    private Boolean handleNoticed;

}
