package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseRelatedServiceRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseRelatedServiceRecordDOQueryParamDTO;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface RiskCaseRelatedServiceRecordRepository {

    /**
     * 保存关联服务记录
     *
     * @param riskCaseRelatedServiceRecordDO
     */
    void save(RiskCaseRelatedServiceRecordDO riskCaseRelatedServiceRecordDO);

    /**
     * 批量保存关联服务记录
     *
     * @param riskCaseRelatedServiceRecordDOList
     */
    void batchSave(List<RiskCaseRelatedServiceRecordDO> riskCaseRelatedServiceRecordDOList);

    /**
     * 根据参数查询关联服务记录
     *
     * @param paramDTO
     * @return
     */
    List<RiskCaseRelatedServiceRecordDO> queryByParam(RiskCaseRelatedServiceRecordDOQueryParamDTO paramDTO);

    /**
     * 根据caseId和serviceName查询工单id
     *
     * @param caseIdList
     * @param serviceNameList
     * @return
     */
    Map<String, Set<String>> queryCaseId2WorkstationCaseId(List<String> caseIdList,
            List<String> serviceNameList);
}
