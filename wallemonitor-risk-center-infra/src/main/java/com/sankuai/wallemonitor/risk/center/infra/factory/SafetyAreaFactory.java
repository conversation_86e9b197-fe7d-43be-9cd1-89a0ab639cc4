package com.sankuai.wallemonitor.risk.center.infra.factory;

import com.sankuai.wallemonitor.risk.center.infra.enums.SafetyAreaInfoSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SafetyAreaExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.sankuai.wallemonitor.risk.center.infra.enums.MenderOperationTypeEnum.IMPROPER_STRANDING_DELAY_RECALL;

@Component
public class SafetyAreaFactory {
    public static SafetyAreaDO createSafetyArea(List<PositionDO> polygon, Set<Integer> effectHours, String poiName) {
        return SafetyAreaDO.builder()
                .areaId(UUID.randomUUID().toString().replace("-", "")) // 随机生产32位uuid
                .source(SafetyAreaInfoSourceEnum.BEACON_TOWER)
                .polygon(SafetyAreaDO.Polygon.builder().pointGcjList(polygon).build()) // 计算得到的poi
                .extInfo(SafetyAreaExtInfoDO.builder().effectHours(effectHours).build()) // 生效时间存入ext_info
                .description(poiName) // poiName作为desc
                .type(IMPROPER_STRANDING_DELAY_RECALL.getName())
                .build();
    }
}
