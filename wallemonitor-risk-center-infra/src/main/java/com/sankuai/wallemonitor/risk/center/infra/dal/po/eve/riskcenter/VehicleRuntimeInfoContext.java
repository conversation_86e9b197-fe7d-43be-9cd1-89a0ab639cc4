package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车辆运行时信息数据表
 * </p>
 *
 * <AUTHOR> @since 2024-10-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("vehicle_runtime_info_context")
public class VehicleRuntimeInfoContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车辆VIN码
     */
    @TableField("vin")
    @TableUnique
    private String vin;

    /**
     * 驾驶模式
     */
    @TableField("drive_mode")
    private Integer driveMode;

    /**
     * 当前速度(km/h)
     */
    @TableField("speed")
    private BigDecimal speed;

    /**
     * 车辆纬度（gcj02）
     */
    @TableField("lng")
    private String lng;

    /**
     * 车辆经度（gcj02）
     */
    @TableField("lat")
    private String lat;

    /**
     * 位置类型，IN_PARKING-AREA|在停车场，OPEN_ROAD|公开道路
     */
    @TableField("position_type")
    private String positionType;

    /**
     * 是否切电中
     */
    @TableField("battery_switching")
    private Boolean batterySwitching;

    /**
     * 是否和道路方向相反
     */
    @TableField("opposite_with_road")
    private Boolean oppositeWithRoad;

    /**
     * 当前压线类型,空-未压线
     */
    @TableField("driving_on_traffic_line_type")
    private String drivingOnTrafficLineType;

    /**
     * 距离前方施工区距离（米）
     */
    @TableField("distance_to_front_construction_zone")
    private Integer distanceToFrontConstructionZone;

    /**
     * 是否和前方施工区重叠
     */
    @TableField("path_overlap_with_construction_zone")
    private Boolean pathOverlapWithConstructionZone;


    /**
     * 当前交通灯信息
     */
    @TableField("cur_traffic_light")
    private String curTrafficLight;

    /**
     * 当前所见交通灯类型,RED|红,GREEN|绿,YELLOW|黄
     */
    @TableField("traffic_light_type")
    private String trafficLightType;

    /**
     * 自车到前方路口距离（米）
     */
    @TableField("distance_to_next_junction")
    private Integer distanceToNextJunction;

    /**
     * 是否在等待闸杆
     */
    @TableField("waiting_gate_pole")
    private Boolean waitingGatePole;

    /**
     * 停滞积累信息
     */
    @TableField("stagnation_counter")
    private String stagnationCounter;

    /**
     * 红灯积累信息
     */
    @TableField("red_light_counter")
    private String redLightCounter;

    /**
     * 上次数据更新时间（触发下游processor计算）
     */
    @TableField("last_update_time")
    private Date lastUpdateTime;

    /**
     * 扩展信息(JSON格式)
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 业务状态
     */
    @TableField("biz_status")
    private String bizStatus;

    /**
     * 车流速度
     * */
    @TableField("traffic_flow_speed")
    private Double trafficFlowSpeed;


}
