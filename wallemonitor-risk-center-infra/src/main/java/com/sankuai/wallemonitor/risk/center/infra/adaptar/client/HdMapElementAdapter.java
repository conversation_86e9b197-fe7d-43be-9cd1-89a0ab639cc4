package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.sankuai.wallemonitor.risk.center.infra.convert.HdMapElementPolygonConvert;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 高精地图元素适配
 */
@Component
@Slf4j
public class HdMapElementAdapter {

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private HdMapElementPolygonConvert hdMapElementPolygonConvert;


}
