package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MarkMessageDTO {


    /**
     * 是否是动态recheck
     */
    @Builder.Default
    private Boolean dynamicRecheck = false;


    /**
     * 标注Id
     */
    private String itemCaseId;

    /**
     * 版本
     */
    @Builder.Default
    private String version = CharConstant.CHAR_EMPTY;

}
