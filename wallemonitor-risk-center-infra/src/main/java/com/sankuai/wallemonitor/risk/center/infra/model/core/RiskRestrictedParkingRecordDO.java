package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.dto.RiskDetectConfirmDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class RiskRestrictedParkingRecordDO extends RiskDetectorRecordBaseDO {

    /**
     * 禁停区域
     */
    private String restrictedAreaType;

    @Override
    public void confirm(RiskDetectConfirmDTO context, List<String> ignoreList) {
        super.confirm(context, ignoreList);
        if (context.getDetectProcessContext() == null) {
            return;
        }
        this.setRestrictedAreaType(context.getRestrictedAreaType());
    }

}