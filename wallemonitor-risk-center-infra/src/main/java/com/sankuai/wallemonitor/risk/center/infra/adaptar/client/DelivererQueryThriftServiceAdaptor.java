package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.walledelivery.basic.client.request.deliverer.DelivererQueryByAccountRequest;
import com.sankuai.walledelivery.basic.client.response.deliverer.DelivererResponse;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.DelivererQueryThriftService;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
public class DelivererQueryThriftServiceAdaptor {

    @ThriftClientProxy(remoteAppKey = "com.sankuai.walledelivery.basic", timeout = 3000)
    private DelivererQueryThriftService delivererQueryThriftService;

    /**
     * 根据车辆名称获取车辆识别码（VIN）
     *
     * @param vehicleId 车牌号
     * @return 车辆识别码（VIN），如果车辆不存在则返回空字符串
     */
    public String getVinByVehicleId(String vehicleId) {
        DelivererQueryByAccountRequest request = new DelivererQueryByAccountRequest();
        request.setCarNameList(Arrays.asList(vehicleId));

        try {
            // 1 通过查询该车牌号是否对应车辆信息为基准判断车牌是否有效
            ThriftResponse<List<DelivererResponse>> thriftResponse = delivererQueryThriftService.queryDelivererWithPlace(
                    request);
            log.info("getVehicleInfoByVehicleName, responseList = {}", JacksonUtils.to(thriftResponse));
            // 2 查不到消息则说明无效
            if (Objects.isNull(thriftResponse) || CollectionUtils.isEmpty(thriftResponse.getData())) {
                log.error("queryDelivererWithPlace error",
                        new ParamInputErrorException(String.format(", 根据车牌号[%s]不存在", vehicleId)));
                return null;
            }
            DelivererResponse delivererResponse = thriftResponse.getData().get(0);
            return delivererResponse.getIdentifyNum();
        } catch (Exception e) {
            log.error(String.format("queryDelivererWithPlace error, 根据车牌号[%s]获取车辆信息失败", vehicleId), e);
        }
        return null;
    }

    /**
     * 根据车辆名称获取车辆信息
     *
     * @param vehicleId
     * @return
     */
    public DelivererResponse getDelivererInfoByVehicleId(String vehicleId) {
        DelivererQueryByAccountRequest request = new DelivererQueryByAccountRequest();
        request.setCarNameList(Arrays.asList(vehicleId));

        try {
            // 1 通过查询该车牌号是否对应车辆信息为基准判断车牌是否有效
            ThriftResponse<List<DelivererResponse>> thriftResponse = delivererQueryThriftService.queryDelivererWithPlace(
                    request);
            log.info("getDelivererInfoByVehicleId, responseList = {}", JacksonUtils.to(thriftResponse));
            // 2 查不到消息则说明无效
            if (Objects.isNull(thriftResponse) || CollectionUtils.isEmpty(thriftResponse.getData())) {
                log.error("getDelivererInfoByVehicleId error",
                        new ParamInputErrorException(String.format(", 根据车牌号[%s]不存在", vehicleId)));
                return null;
            }
            DelivererResponse delivererResponse = thriftResponse.getData().get(0);
            return delivererResponse;
        } catch (Exception e) {
            log.error(String.format("getDelivererInfoByVehicleId error, 根据车牌号[%s]获取车辆信息失败", vehicleId), e);
        }
        return null;
    }


    /**
     * 根据vin列表获取车辆信息
     *
     * @param vinList
     * @return
     */
    public List<DelivererResponse> getDelivererResponseByVinList(List<String> vinList) {
        if (CollectionUtils.isEmpty(vinList)) {
            return new ArrayList<>();
        }
        DelivererQueryByAccountRequest request = new DelivererQueryByAccountRequest();
        request.setVinList(vinList);

        try {
            ThriftResponse<List<DelivererResponse>> thriftResponse = delivererQueryThriftService.queryDelivererWithPlace(
                    request);
            log.info("getDelivererResponseByVinList, responseList = {}", JacksonUtils.to(thriftResponse));
            if (Objects.isNull(thriftResponse) || CollectionUtils.isEmpty(thriftResponse.getData())) {
                log.error("getDelivererResponseByVinList error",
                        new ParamInputErrorException(String.format(", 根据车架号号[%s]不存在", vinList)));
                return null;
            }
            List<DelivererResponse> delivererResponseList = thriftResponse.getData();
            return delivererResponseList;
        } catch (Exception e) {
            log.error(String.format("getDelivererResponseByVinList error, 根据车架号[%s]获取车辆信息失败", vinList), e);
        }
        return null;
    }

    /**
     * 根据vin列表获取车辆信息
     *
     * @param vinList
     * @return
     */
    public Map<String, String> getVehicleName2VinMap(List<String> vinList) {
        List<DelivererResponse> delivererResponseList = getDelivererResponseByVinList(vinList);
        if (CollectionUtils.isEmpty(delivererResponseList)) {
            return new HashMap<>();
        }
        Map<String, String> vehicleName2VinMap = new HashMap<>();
        delivererResponseList.forEach(delivererResponse -> {
            vehicleName2VinMap.put(delivererResponse.getAccount(), delivererResponse.getIdentifyNum());
        });
        return vehicleName2VinMap;
    }
}
