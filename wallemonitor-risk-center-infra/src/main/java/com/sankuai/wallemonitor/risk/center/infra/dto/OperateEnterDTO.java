package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SSOLogInfoDO;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 领域通用上下文信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperateEnterDTO {

    /**
     * 本次操作唯一编码
     */
    private String operateTraceId;

    /**
     * 本次操作时间
     */
    private Date operateTime;

    /**
     * 本次操作人
     */
    private SSOLogInfoDO operateUser;

    /**
     * 本次操作类型
     */
    private OperateEnterActionEnum operateAction;
}
