package com.sankuai.wallemonitor.risk.center.infra.convert;

import static com.sankuai.wallemonitor.risk.center.infra.constant.VehicleEventCodeConstant.SIDE_BY_SIDE_END;
import static com.sankuai.wallemonitor.risk.center.infra.constant.VehicleEventCodeConstant.SIDE_BY_SIDE_START;
import static com.sankuai.wallemonitor.risk.center.infra.constant.VehicleEventCodeConstant.STAND_STILL_END;
import static com.sankuai.wallemonitor.risk.center.infra.constant.VehicleEventCodeConstant.STAND_STILL_START;
import static com.sankuai.wallemonitor.risk.center.infra.constant.VehicleEventCodeConstant.TRAFFIC_JAM_END;
import static com.sankuai.wallemonitor.risk.center.infra.constant.VehicleEventCodeConstant.TRAFFIC_JAM_START;

import com.sankuai.walleeve.domain.enums.MessageType;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.domain.message.dto.RiskCaseMessageDTO;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleEventDataDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskCaseMessageDTOConvert {


    Logger logger = LoggerFactory.getLogger(RiskCaseMessageDTOConvert.class);

    String KEY_EVENT_ID = "eventId";

    String KEY_START_TIMESTAMP = "start_timestamp";

    String STUCK_VEHICLE_LIST = "stuck_vehicle_set";

    String SOURCE_EVENT_ID = "source_event_id";

    String TRACE_ID = "trace_id";

    /**
     * 转换对象
     *
     * @param vehicleEventDataDO
     * @param riskCaseRepository
     * @param vehicleRelationRepository
     * @return
     */
    default List<EveMqCommonMessage<RiskCaseMessageDTO>> vehicleEventConvert(VehicleEventDataDO vehicleEventDataDO,
            RiskCaseRepository riskCaseRepository, RiskCaseVehicleRelationRepository vehicleRelationRepository,
            VehicleInfoRepository vehicleInfoRepository) {
        if (vehicleEventDataDO == null) {
            return new ArrayList<>();
        }
        Map<String, Object> content = vehicleEventDataDO.getContent();
        switch (vehicleEventDataDO.getEventCode()) {
            case SIDE_BY_SIDE_START: {
                //如果已经有
                List<RiskCaseDO> riskCaseDOList = handleGetNominalRiskCaseDOList(vehicleEventDataDO,
                        vehicleRelationRepository, riskCaseRepository);
                if (CollectionUtils.isNotEmpty(riskCaseDOList)) {
                    logger.warn("398已有未结束的风险事件!，本次忽略,eventId:{}", vehicleEventDataDO.getEventId());
                    return new ArrayList<>();
                } else {
                    //没有，则取事件中心的id
                    VehicleEventDataMessageExtInfoDTO extInfoDTO = VehicleEventDataMessageExtInfoDTO
                            .builder().build();
                    extInfoDTO.setSideBySideStartTime(
                            vehicleEventDataDO.getContent().get(KEY_START_TIMESTAMP).toString());
                    RiskCaseMessageDTO.RiskCaseMessageDTOBuilder riskCaseMessageDTOBuilder = RiskCaseMessageDTO.builder()
                            //事件中心的id作为事件ID
                            .eventId(vehicleEventDataDO.getEventId())
                            .source(RiskCaseSourceEnum.PNC.getCode())
                            .status(RiskCaseStatusEnum.NO_DISPOSAL.getCode())
                            .type(RiskCaseTypeEnum.VEHICLE_SIDE_BY_SIDE.getCode())
                            .extInfo(JacksonUtils.to(extInfoDTO))
                            .vinList(Collections.singletonList(vehicleEventDataDO.getVin()));
                    return Collections.singletonList(
                            new EveMqCommonMessage<>(MessageType.RISK_CASE_MESSAGE.getCode(),
                                    riskCaseMessageDTOBuilder.build(), vehicleEventDataDO.getEventTimestamp()));
                }
            }
            case SIDE_BY_SIDE_END: {
                //如果是结束
                List<RiskCaseDO> riskCaseDOList = handleGetNominalRiskCaseDOList(vehicleEventDataDO,
                        vehicleRelationRepository, riskCaseRepository);
                if (CollectionUtils.isNotEmpty(riskCaseDOList)) {
                    // 设置 sideBySideStartTime
                    VehicleEventDataMessageExtInfoDTO extInfoDTO = new VehicleEventDataMessageExtInfoDTO();
                    extInfoDTO.setSideBySideStartTime(
                            vehicleEventDataDO.getContent().get(KEY_START_TIMESTAMP).toString());
                    //过去的不为空
                    return riskCaseDOList.stream()
                            .map(riskCaseDO -> RiskCaseMessageDTO.builder()
                                    //使用风险的事件id
                                    .eventId(riskCaseDO.getEventId())
                                    .source(RiskCaseSourceEnum.PNC.getCode())
                                    .status(RiskCaseStatusEnum.DISPOSED.getCode())
                                    .extInfo(JacksonUtils.to(extInfoDTO))
                                    .type(RiskCaseTypeEnum.VEHICLE_SIDE_BY_SIDE.getCode())
                                    .vinList(Collections.singletonList(vehicleEventDataDO.getVin())).build())
                            .map(riskCaseMessageDTO -> new EveMqCommonMessage<RiskCaseMessageDTO>(
                                    MessageType.RISK_CASE_MESSAGE.getCode(),
                                    riskCaseMessageDTO, vehicleEventDataDO.getEventTimestamp()))
                            .collect(Collectors.toList());
                }
                break;
            }
            case TRAFFIC_JAM_START: {
                List<String> vinList = getVinList(vehicleEventDataDO, vehicleInfoRepository);
                // 构建风险事件消息DTO
                String eventId = (String) vehicleEventDataDO.getContent().get(SOURCE_EVENT_ID);
                RiskCaseMessageDTO.RiskCaseMessageDTOBuilder riskCaseMessageDTOBuilder = RiskCaseMessageDTO.builder()
                        .eventId(eventId)
                        .source(RiskCaseSourceEnum.SAFEGUARD_SYSTEM.getCode())
                        .status(RiskCaseStatusEnum.NO_DISPOSAL.getCode())
                        .type(RiskCaseTypeEnum.VEHICLE_CONGESTION.getCode())
                        .vinList(vinList);
                return Collections.singletonList(
                        new EveMqCommonMessage<>(MessageType.RISK_CASE_MESSAGE.getCode(),
                                riskCaseMessageDTOBuilder.build(), vehicleEventDataDO.getEventTimestamp()));
            }
            case TRAFFIC_JAM_END: {
                // 先查询已有记录是否完成，有未完成才会继续执行后面的逻辑
                String eventId = (String) vehicleEventDataDO.getContent().get(SOURCE_EVENT_ID);
                List<RiskCaseDO> riskCaseDOList = riskCaseRepository.queryByParam(
                        RiskCaseDOQueryParamDTO.builder().eventId(eventId).build());
                // 如果没有风险事件 或者 风险事件全部结束
                if (CollectionUtils.isEmpty(riskCaseDOList) || riskCaseDOList.stream()
                        .allMatch(riskCaseDO -> RiskCaseStatusEnum.isTerminal(riskCaseDO.getStatus()))) {
                    // 返回null，之后外层回返回执行成功
                    return null;
                }
                List<String> vinList = getVinList(vehicleEventDataDO, vehicleInfoRepository);
                // 构建风险事件消息DTO
                RiskCaseMessageDTO.RiskCaseMessageDTOBuilder riskCaseMessageDTOBuilder = RiskCaseMessageDTO.builder()
                        .eventId(eventId)
                        .source(RiskCaseSourceEnum.SAFEGUARD_SYSTEM.getCode())
                        .status(RiskCaseStatusEnum.DISPOSED.getCode())
                        .type(RiskCaseTypeEnum.VEHICLE_CONGESTION.getCode())
                        .vinList(vinList);
                return Collections.singletonList(
                        new EveMqCommonMessage<>(MessageType.RISK_CASE_MESSAGE.getCode(),
                                riskCaseMessageDTOBuilder.build(), vehicleEventDataDO.getEventTimestamp()));
            }
            case STAND_STILL_START: {
                VehicleEventDataMessageExtInfoDTO extInfoDTO = VehicleEventDataMessageExtInfoDTO
                        .builder()
                        .build();
                // 保存content
                extInfoDTO.setContent(content);
                // 构建风险事件消息DTO
                RiskCaseMessageDTO.RiskCaseMessageDTOBuilder riskCaseMessageDTOBuilder = RiskCaseMessageDTO.builder()
                        .eventId(vehicleEventDataDO.getEventId())
                        .source(RiskCaseSourceEnum.SAFEGUARD_SYSTEM.getCode())
                        .status(RiskCaseStatusEnum.NO_DISPOSAL.getCode())
                        .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode())
                        // 保存content
                        .extInfo(JacksonUtils.to(extInfoDTO))
                        .vinList(Collections.singletonList(vehicleEventDataDO.getVin()));
                return Collections.singletonList(
                        new EveMqCommonMessage<>(MessageType.RISK_CASE_MESSAGE.getCode(),
                                riskCaseMessageDTOBuilder.build(), vehicleEventDataDO.getEventTimestamp()));
            }
            case STAND_STILL_END: {
                //找保障系统未结束的
                List<String> standStillStartEventIdList = getStandStillStartEventId(vehicleEventDataDO,
                        riskCaseRepository,
                        vehicleRelationRepository);
                if (CollectionUtils.isEmpty(standStillStartEventIdList)) {
                    //没找到返回
                    return null;
                }
                //返回
                return standStillStartEventIdList.stream()
                        .map(standStillStartEventId -> new EveMqCommonMessage<>(
                                MessageType.RISK_CASE_MESSAGE.getCode(),
                                RiskCaseMessageDTO.builder()
                                        .eventId(standStillStartEventId)
                                        .source(RiskCaseSourceEnum.SAFEGUARD_SYSTEM.getCode())
                                        .status(RiskCaseStatusEnum.DISPOSED.getCode())
                                        .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode())
                                        .vinList(Collections.singletonList(vehicleEventDataDO.getVin()))
                                        .build(), vehicleEventDataDO.getEventTimestamp())).collect(Collectors.toList());
            }
        }
        //其他情况null
        return null;
    }


    /**
     * 查询并排开始的车辆关联的未关闭风险事件
     *
     * @param vehicleEventDataDO
     * @param vehicleRelationRepository
     * @param riskCaseRepository
     * @return
     */
    default List<RiskCaseDO> handleGetNominalRiskCaseDOList(VehicleEventDataDO vehicleEventDataDO,
            RiskCaseVehicleRelationRepository vehicleRelationRepository, RiskCaseRepository riskCaseRepository) {
        Map<String, Object> map = vehicleEventDataDO.getContent();
        String sideBySideTimestamp = (String) map.get(KEY_START_TIMESTAMP);
        //查找发生时间的事件
        List<RiskCaseVehicleRelationDO> vehicleRelationDOList = vehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder()
                        //并排开始时间
                        .sideBySideTimestamp(sideBySideTimestamp)
                        .vin(vehicleEventDataDO.getVin())
                        .build());
        if (CollectionUtils.isEmpty(vehicleRelationDOList)) {
            return new ArrayList<>();
        }
        List<String> caseIdList = vehicleRelationDOList.stream()
                .map(RiskCaseVehicleRelationDO::getCaseId)
                .collect(Collectors.toList());
        //查询未结束的
        List<RiskCaseDO> unFinishedRiskCaseDOList = riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder()
                        .caseIdList(caseIdList)
                        .type(RiskCaseTypeEnum.VEHICLE_SIDE_BY_SIDE.getCode())
                        .statusList(RiskCaseStatusEnum.getUnTerminal())
                        .build());
        if (CollectionUtils.isEmpty(unFinishedRiskCaseDOList)) {
            return new ArrayList<>();
        }
        return unFinishedRiskCaseDOList;
    }

    /**
     * 查询扎堆车辆列表
     *
     * @param vehicleEventDataDO
     * @param vehicleInfoRepository
     * @return
     */
    default List<String> getVinList(VehicleEventDataDO vehicleEventDataDO,
            VehicleInfoRepository vehicleInfoRepository) {
        try {
            Object object = vehicleEventDataDO.getContent().get(STUCK_VEHICLE_LIST);
            List<String> stuckVehicleList;
            if (!(object instanceof List<?>)) {
                return new ArrayList<>();
            }
            stuckVehicleList = new ArrayList<>();
            for (Object item : (List<?>) object) {
                stuckVehicleList.add((String) item);
            }
            return vehicleInfoRepository.getVinByCarAccountList(stuckVehicleList);
        } catch (Exception e) {
            logger.error("扎堆消息获取车辆列表异常", e);
            return new ArrayList<>();
        }
    }


    /**
     * 获取停滞开始的时间戳
     *
     * @param vehicleEventDataDO
     * @param vehicleRelationRepository
     * @return
     */
    default List<String> getStandStillStartEventId(VehicleEventDataDO vehicleEventDataDO,
            RiskCaseRepository riskCaseRepository,
            RiskCaseVehicleRelationRepository vehicleRelationRepository) {
        // 从停滞结束的时间戳，反查该车的全部未关闭停滞风险，用于消息异常时的兜底官博
        Date standStillEndTime = new Date(vehicleEventDataDO.getEventTimestamp());
        //取12小时前的
        Date earlyBeginTime = DatetimeUtil.getNSecondsBeforeDateTime(standStillEndTime,
                CommonConstant.SECONDS_PER_HALF_DAY);
        //找寻过去的eventId
        // 收到5007（停滞结束）的消息时，查询「停滞开始的时间戳 < 消息里面的时间戳且来源于 保障系统」 的停滞风险，如果存在，则进行更新；如果不存在，则重试
        List<String> riskCaskVehicleCaseIdList = vehicleRelationRepository.queryByParam(
                RiderCaseVehicleRelationDOParamDTO.builder()
                        //车辆
                        .vin(vehicleEventDataDO.getVin())
                        //要早于晚于时间的
                        .milliBeginTimeBelow(standStillEndTime)
                        //但是又不能太晚，必须早于半天
                        .createTimeGrateTo(earlyBeginTime)
                        //停滞不当类型
                        .type(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode())
                        //获取查出来的eventId
                        .build()).stream().map(RiskCaseVehicleRelationDO::getCaseId).collect(Collectors.toList());
        //如果为空，则返回null，外层会重试
        if (CollectionUtils.isEmpty(riskCaskVehicleCaseIdList)) {
            //如果不存在
            return new ArrayList<>();
        }
        //取未关闭的全部eventId
        return riskCaseRepository.queryByParam(
                RiskCaseDOQueryParamDTO.builder().caseIdList(riskCaskVehicleCaseIdList)
                        //未关闭
                        .statusList(RiskCaseStatusEnum.getUnTerminal())
                        //来自保障系统
                        .source(RiskCaseSourceEnum.SAFEGUARD_SYSTEM.getCode())
                        .build()).stream().map(RiskCaseDO::getEventId).collect(Collectors.toList());
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    @EqualsAndHashCode(callSuper = false)
    class VehicleEventDataMessageExtInfoDTO {
        private String riskEventDesc;
        private String riskEventLocation;
        private String sideBySideStartTime;
        private Long beginTime;
        private Map<String, Object> content;
    }
}
