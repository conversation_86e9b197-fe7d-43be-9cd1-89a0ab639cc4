package com.sankuai.wallemonitor.risk.center.infra.adaptar.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkstationCreateRequest {
    //参考文档链接： https://km.sankuai.com/collabpage/1684314424
    /**
     * 是否是case,默认为true，设置false表示为事件
     */
    private Boolean isIssue;

    /**
     * 数据来源
     */
    private String source;

    /**
     * 问题类型
     */
    private String caseType;

    /**
     * caseTime
     */
    private String caseTime;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 烽火台caseId
     */
    private String caseId;

    /**
     * case 标题
     */
    private String title;

    /**
     * 纬度(WGS84)
     */
    private String latitude;

    /**
     * 经度(WGS84)
     */
    private String longitude;

    /**
     * 扩展字段
     */
    private CaseExtension extension;


    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CaseExtension {

        /**
         * 烽火台链接
         */
        private String beaconTowerLink;

        /**
         * 风险事件解除时间
         */
        private String caseEndTime;

    }
}
