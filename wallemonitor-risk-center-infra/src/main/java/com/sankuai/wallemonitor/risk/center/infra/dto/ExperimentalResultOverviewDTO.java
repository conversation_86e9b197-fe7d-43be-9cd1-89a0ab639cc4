package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.ead.citadel.document.node.concept.Block;
import com.sankuai.ead.citadel.document.node.impl.node.BulletList;
import com.sankuai.ead.citadel.document.node.impl.node.ListItem;
import com.sankuai.ead.citadel.document.node.impl.node.Mention;
import com.sankuai.ead.citadel.document.node.impl.node.Paragraph;
import com.sankuai.ead.citadel.document.node.impl.node.TableCell;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.sankuai.ead.citadel.document.util.Builder;
import org.apache.calcite.common.time.DateFormatUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 实验结果概览数据
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
@Data
@lombok.Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExperimentalResultOverviewDTO {

    // 实验时间
    private Date experimentalTime;

    // 实验人名
    private String personName;

    // 实验人mis
    private String personMis;

    // 实验内容
    private List<String> experimentalContent;

    // 数据集名称
    private String dataset;

    // 数据集数据个数
    private Integer datasetDataCount;

    // 实验轮次
    private Integer round;

    // 召回数
    private Integer recallCount;

    // 漏召数
    private Integer missCount;

    // 失败数
    private Integer failureCount;

    // 准确数
    private Integer accuracyCount;


    /**
     * 实验总数
     *
     * @return
     */
    private Integer totalCount() {
        CheckUtil.isNotNull(datasetDataCount, "数据个数不能为空");
        CheckUtil.isNotNull(round, "实验轮次不能为空");
        CheckUtil.isGreatThan(datasetDataCount, 0, "数据个数必须大于0");
        CheckUtil.isGreatThan(round, 0, "实验轮次必须大于0");
        return datasetDataCount * round;
    }

    /**
     * 转换为表格单元格
     *
     * @return
     */
    public TableCell[] toTableCell() {
        return new TableCell[]{
                Builder.tableCell(getExperimentalTimeParagraph()),
                Builder.tableCell(getPersonParagraph()),
                Builder.tableCell(getExperimentalContentBulletList()),
                Builder.tableCell(getDatasetParagraph()),
                Builder.tableCell(getRoundParagraph()),
                Builder.tableCell(getRecallParagraph()),
                Builder.tableCell(getMissParagraph()),
                Builder.tableCell(getFailureParagraph()),
                Builder.tableCell(getAccuracyParagraph())
        };
    }

    private Paragraph getExperimentalTimeParagraph() {
        if (experimentalTime == null) {
            return Builder.paragraph(" ");
        }
        return Builder.paragraph(DateFormatUtils.format(experimentalTime, "yyyy-MM-dd HH:mm:ss"));
    }

    private Paragraph getPersonParagraph() {
        if (StringUtils.isNotBlank(personName) && StringUtils.isNotBlank(personMis)) {
            return Builder.paragraph(Builder.mention(personName, personMis));
        } else if (StringUtils.isNotBlank(personName)) {
            return Builder.paragraph(personName);
        } else if (StringUtils.isNotBlank(personMis)) {
            return Builder.paragraph(personMis);
        } else {
            return Builder.paragraph(" ");
        }
    }

    private Block getExperimentalContentBulletList() {
        if (CollectionUtils.isEmpty(experimentalContent)) {
            return Builder.paragraph(" ");
        }
        return Builder.bulletList(experimentalContent.stream()
                .map(content -> {
                    ListItem listItem = new ListItem();
                    listItem.setContents(Collections.singletonList(Builder.paragraph(content)));
                    return listItem;
                }).toArray(ListItem[]::new));
    }

    private Paragraph getDatasetParagraph() {
        if (StringUtils.isBlank(dataset)) {
            return Builder.paragraph(" ");
        }
        return Builder.paragraph(dataset + "(" + datasetDataCount + "个)");
    }

    private Paragraph getRoundParagraph() {
        if (round == null) {
            return Builder.paragraph(" ");
        }
        return Builder.paragraph(String.valueOf(round));
    }

    private Paragraph getRecallParagraph() {
        return Builder.paragraph(toPercent(recallCount));
    }

    private Paragraph getMissParagraph() {
        return Builder.paragraph(toPercent(missCount));
    }

    private Paragraph getFailureParagraph() {
        return Builder.paragraph(toPercent(failureCount));
    }

    private Paragraph getAccuracyParagraph() {
        return Builder.paragraph(toPercent(accuracyCount));
    }


    /**
     * 转换为百分比与比例描述
     *
     * @param num
     * @return
     */
    private String toPercent(Integer num) {
        int total = totalCount();
        if (num == null || total == 0) {
            return " ";
        }
        return String.format("%.2f%% (%d/%d)", (double) num / total * 100, num, total);
    }

}
