package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.MrmCallFilterRuleHitLog;
import com.sankuai.wallemonitor.risk.center.infra.model.core.MrmCallFilterRuleHitLogDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MrmCallFilterRuleHitLogConvert extends SingleConvert<MrmCallFilterRuleHitLog, MrmCallFilterRuleHitLogDO> {
    // 目前无特殊字段转换，直接继承SingleConvert即可
} 