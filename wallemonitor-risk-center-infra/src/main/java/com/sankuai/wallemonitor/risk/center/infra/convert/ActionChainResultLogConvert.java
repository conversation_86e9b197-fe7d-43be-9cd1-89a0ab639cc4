package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.api.response.vo.ActionChainResultVO;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.ActionChainResultLog;
import com.sankuai.wallemonitor.risk.center.infra.model.core.ActionChainResultLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class}, imports = {DatetimeUtil.class})
public interface ActionChainResultLogConvert extends SingleConvert<ActionChainResultLog, ActionChainResultLogDO> {

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    ActionChainResultLogDO toDO(ActionChainResultLog resultLogPO);

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    ActionChainResultLog toPO(ActionChainResultLogDO resultLogDO);

    @Mapping(target = "createTime", expression = "java(DatetimeUtil.formatTime(resultLogDO.getCreateTime()))")
    @Mapping(target = "updateTime", expression = "java(DatetimeUtil.formatTime(resultLogDO.getUpdateTime()))")
    @Mapping(target = "checkTime", expression = "java(DatetimeUtil.formatTime(resultLogDO.getCheckTime()))")
    ActionChainResultVO toVO(ActionChainResultLogDO resultLogDO);
}
