package com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage;

import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class PerceptionObstacleDTO {

    private static final String FINE_PREFIX = "FINE_";

    private ArbitrationMonitorContext arbitrationMonitorContext;
    private String errorCode;
    private List<PerceptionObstacle> perceptionObstacle;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ArbitrationMonitorContext implements Serializable {
        private static final long serialVersionUID = 1L;
        private String noiseCategory;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PerceptionObstacle implements Serializable {
        private static final long serialVersionUID = 1L;
        private Acceleration acceleration;
        private Integer confidence;
        private Direction direction;
        private Double height;
        private String id;
        private Double length;
        private ObstacleType obstacleType;
        private Position position;
        private Double theta;
        private String type;
        private Velocity velocity;
        private Double width;

        public String findShortFineType() {

            return ObstacleFineTypeEnum.transferShotName(obstacleType.getFineType());
        }

        public PositionDO toPosition() {
            return GeoToolsUtil.utmToWgs84(position.getX(), position.getY());
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Acceleration implements Serializable {
            private static final long serialVersionUID = 1L;
            private Double x;
            private Double y;
            private Double z;
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Direction implements Serializable {
            private static final long serialVersionUID = 1L;
            private Double x;
            private Double y;
            private Double z;
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class ObstacleType implements Serializable {
            private static final long serialVersionUID = 1L;
            private String coarseType;
            private String fineType;
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Position implements Serializable {
            private static final long serialVersionUID = 1L;
            private Double x;
            private Double y;
            private Double z;
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Velocity implements Serializable {
            private static final long serialVersionUID = 1L;
            private Double x;
            private Double y;
            private Double z;
        }
    }

    // 粗粒度障碍物类型枚举
    @AllArgsConstructor
    @Getter
    public enum ObstacleCoarseTypeEnum {
        PEDESTRIAN(0),
        CYCLIST(1),
        CAR(2),
        OTHER(3),
        TOTAL(4);

        private final int value;

    }

    // 细粒度障碍物类型枚举
    @AllArgsConstructor
    @Getter
    public enum ObstacleFineTypeEnum {
        // PEDESTRIAN 相关 (0-99)
        FINE_PEDESTRIAN(0),
        FINE_CHILD(1),

        // CYCLIST 相关 (100-199)
        FINE_CYCLIST(100),
        FINE_MOTORIST(101),
        FINE_TRICYCLIST(102),
        FINE_BIKE(103),
        FINE_MOTOR(104),
        FINE_E_BIKE(105),
        FINE_TRICYCLE(106),
        FINE_MID_CAR(107),
        FINE_MEITUAN_MID_CAR_S20(108),
        FINE_NON_MOTOR_GROUPS(109),
        FINE_OTHER_MID_CAR(110),

        // CAR 相关 (200-299)
        FINE_CAR(200),
        FINE_BUS(201),
        FINE_VAN(202),
        FINE_TRUCK(203),
        FINE_MINI_CAR(204),
        FINE_CONSTRUCTION_VEHICLE(205),
        FINE_FORKLIFT_TRUCK(206),
        FINE_TRAM(207),
        FINE_SANITATION_VEHICLE(208),
        FINE_SCHOOL_BUS(209),
        FINE_FIRE_FIGHTING_TRUCK(210),
        FINE_POLICE_CAR(211),
        FINE_AMBULANCE(212),

        // OTHER 相关 (300+)
        FINE_OTHER(300),
        FINE_TRAFFIC_SIGN(301),
        FINE_TRAFFIC_CONE(302),
        FINE_TRIPOD(303),
        FINE_ANTICOLLISION_COLUMN(304),
        FINE_ANTICOLLISION_BUCKET(305),
        FINE_CONSTRUCTION_PLATE(306),
        FINE_ISOLATION_BALL(307),
        FINE_CONSTRUCTION_BARRIER(308),

        FINE_OTHER_VEHICLE(400),

        FINE_HANDCART(500),
        FINE_BABY_CAR(501),
        FINE_ELDERLY_MOBILITY_SCOOTER(502),
        FINE_ANIMAL(503),
        FINE_BARRIER(504),
        FINE_VEGETATION(505),
        FINE_OTHER_MOVABLE(506),
        FINE_LABELED_UNMOVABLE(507),
        FINE_OTHER_UNDRIVABLE(508),
        FINE_CONSTRUCTION_ZONE(509),
        FINE_PUDDLE(510),
        FINE_ABNORMAL_OBSTACLE(511),
        FINE_GATE(512),
        FINE_SNOWCOVER(513),
        FINE_UNEVEN_GROUND(514),
        FINE_CURB(515),
        FINE_POLE(516),
        FINE_OTHER_RESTRICTED_ZONE(517),
        FINE_PLASTIC_BAG(518),
        FINE_TREE_BRANCH(519),
        FINE_LEAF(520);

        private final int value;

        /**
         * 将字符串转换为 ObstacleFineTypeEnum 枚举
         *
         * @param numberOrString
         * @return
         */
        public static String transferShotName(String numberOrString) {
            try {
                if (StringUtils.isBlank(numberOrString)) {
                    return CharConstant.CHAR_EMPTY;
                }
                String finaleFullName = CharConstant.CHAR_EMPTY;
                if (StringUtils.isNumeric(numberOrString)) {
                    // 如果是数字的字符串
                    int number = Integer.parseInt(numberOrString);
                    for (ObstacleFineTypeEnum type : ObstacleFineTypeEnum.values()) {
                        if (type.getValue() == number) {
                            finaleFullName = type.name();
                            break;
                        }
                    }
                } else {
                    // 如果是正常的字符串，直接赋值
                    finaleFullName = numberOrString;
                }
                if (StringUtils.isBlank(finaleFullName)) {
                    // 如果未找到，原样返回
                    return numberOrString;
                }
                // 移除前缀
                return finaleFullName.replace(FINE_PREFIX, CharConstant.CHAR_EMPTY);
            } catch (Exception e) {
                log.warn("transferShotName error, numberOrString: {}", numberOrString, e);
                return numberOrString;
            }

        }
    }

}