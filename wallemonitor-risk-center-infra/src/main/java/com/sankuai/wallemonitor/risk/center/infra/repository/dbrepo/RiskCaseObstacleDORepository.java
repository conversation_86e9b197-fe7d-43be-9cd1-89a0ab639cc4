package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseObstacleDO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface RiskCaseObstacleDORepository {

    List<RiskCaseObstacleDO> queryByParam(RiskCaseObstacleDOQueryParamDTO paramDTO);

    Paging<RiskCaseObstacleDO> queryByParamByPage(RiskCaseObstacleDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize);

    RiskCaseObstacleDO getByVin(String vin);

    void save(RiskCaseObstacleDO RiskCaseObstacleDO);

    void batchSave(List<RiskCaseObstacleDO> RiskCaseObstacleDOList);

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class RiskCaseObstacleDOQueryParamDTO {

        @InQuery(field = "caseId")
        private List<String> caseIdList;

        /**
         * 创建时间范围
         */
        @RangeQuery(field = "createTime")
        private TimePeriod createTimeRange;

    }
}