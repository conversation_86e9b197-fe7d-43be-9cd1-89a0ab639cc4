package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 多版本标注结果表
 * </p>
 *
 * <AUTHOR> @since 2025-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("multi_version_mark_info")
public class MultiVersionMarkInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * caseId
     */
    @TableField("case_id")
    @TableUnique
    private String caseId;

    /**
     * 版本
     */
    @TableField("mark_version")
    @TableUnique
    private String markVersion;

    /**
     * 类别
     */
    @TableField("category")
    private String category;

    /**
     * 子类别
     */
    @TableField("sub_category")
    private String subCategory;

    /**
     * 拓展信息
     */
    @TableField("check_result")
    private String checkResult;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最近更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 轮次
     */
    @TableField("round")
    private Integer round;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;


}
