package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.MrmCallFilterRuleHitLog;

/**
 * 坐席呼叫过滤规则命中纪录表 Mapper 接口
 */
public interface MrmCallFilterRuleHitLogMapper extends CommonMapper<MrmCallFilterRuleHitLog> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<MrmCallFilterRuleHitLog> getPOClass() {
        return MrmCallFilterRuleHitLog.class;
    }
} 