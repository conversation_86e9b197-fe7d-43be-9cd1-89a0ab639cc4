package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/14
 */
@Getter
@AllArgsConstructor
public enum AlertPolicyEnum {
    // 单事件告警策略，接收到事件就告警
    SINGLE_CASE("single_case"),
    // 通用聚合策略, 支持AggregateFieldEnum中字段的聚合
    GENERAL_AGGREGATION("general_aggregation");

    private final String policy;

    public static AlertPolicyEnum find(String policy) {
        for (AlertPolicyEnum alertPolicyEnum : AlertPolicyEnum.values()) {
            if (alertPolicyEnum.getPolicy().equals(policy)) {
                return alertPolicyEnum;
            }
        }
        return null;
    }

}
