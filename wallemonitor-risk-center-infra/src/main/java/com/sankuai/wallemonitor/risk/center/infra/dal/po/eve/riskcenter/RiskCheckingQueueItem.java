package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 风险检查队列表
 * </p>
 *
 * <AUTHOR> @since 2024-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("risk_checking_queue_item")
public class RiskCheckingQueueItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车辆VIN码
     */
    @TableField("vin")
    private String vin;

    /**
     * 临时风险caseID
     */
    @TableField("tmp_case_id")
    @TableUnique
    private String tmpCaseId;

    /**
     * 上游输入事件ID
     */
    @TableField("event_id")
    private String eventId;

    /**
     * 风险事件类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 风险事件来源
     */
    @TableField("source")
    private Integer source;

    /**
     * 事件实际开始时间（如停滞不当事件）
     */
    @TableField("occur_time")
    private Date occurTime;

    /**
     * 事件召回时间
     */
    @TableField("recall_time")
    private Date recallTime;

    /**
     * 是否正在预检中
     */
    @TableField("checking")
    private Boolean checking;

    /**
     * 检查结果
     */
    @TableField("check_result")
    private String checkResult;

    /**
     * 队列状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 取消原因
     */
    @TableField("cancel_reason")
    private String cancelReason;

    /**
     * 查是原因
     */
    @TableField("confirmed_reason")
    private String confirmReason;

    /**
     * 风险查是的时间
     */
    @TableField("confirmed_time")
    private Date confirmedTime;

    /**
     * 结束时间
     */
    @TableField("close_time")
    private Date closeTime;

    /**
     * 当前轮次
     */
    @TableField("round")
    private Integer round;

    /**
     * 需要复检的最大轮次
     */
    @TableField("max_round")
    private Integer maxRound;

    /**
     * 下一轮执行时间
     */
    @TableField("next_round_time")
    private Date nextRoundTime;

    /**
     * 扩展信息，JSON格式，保存轮次等信息
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;


}
