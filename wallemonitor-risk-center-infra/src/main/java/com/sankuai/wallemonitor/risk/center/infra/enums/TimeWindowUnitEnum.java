package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 时间窗口单位枚举
 */
@Getter
@AllArgsConstructor
public enum TimeWindowUnitEnum {

    /**
     * 秒
     */
    SECONDS(1, "秒", 1),

    /**
     * 分钟
     */
    MINUTES(2, "分钟", 60),

    /**
     * 小时
     */
    HOURS(3, "小时", 3600);

    private final Integer code;
    private final String desc;
    private final Integer secondsMultiplier;

    public static TimeWindowUnitEnum findByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TimeWindowUnitEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 转换为秒数
     */
    public long toSeconds(long value) {
        return value * secondsMultiplier;
    }
} 