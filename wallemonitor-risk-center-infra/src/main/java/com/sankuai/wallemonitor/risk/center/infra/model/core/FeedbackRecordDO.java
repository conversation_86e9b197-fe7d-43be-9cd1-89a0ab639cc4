package com.sankuai.wallemonitor.risk.center.infra.model.core;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class FeedbackRecordDO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 反馈类型[1-建议|2-投诉|3-咨询|4-其他]
     */
    private Integer feedbackType;

    /**
     * 反馈内容
     */
    private String feedbackContent;

    /**
     * 反馈渠道[0-小程序]
     */
    private Integer feedbackChannel;

    /**
     * 附件URL
     */
    private String attachmentUrl;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除[0-未删除|1-已删除]
     */
    private Boolean isDelete;
}
