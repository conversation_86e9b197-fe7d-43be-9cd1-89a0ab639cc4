package com.sankuai.wallemonitor.risk.center.infra.utils.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.io.IOException;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DirectionDeserializer extends JsonDeserializer<Pair<PositionDO, PositionDO>> {
    @Override
    public Pair<PositionDO, PositionDO> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        if (node == null) {
            return null;
        }

        JsonNode keyNode = node.get("key");
        JsonNode valueNode = node.get("value");
        if (keyNode == null || valueNode == null) {
            return null;
        }

        try {
            PositionDO key = JacksonUtils.getObjectMapper().treeToValue(keyNode, PositionDO.class);
            PositionDO value = JacksonUtils.getObjectMapper().treeToValue(valueNode, PositionDO.class);
            if (key == null || value == null) {
                return null;
            }
            return new Pair<>(key, value);
        } catch (Exception e) {
            log.warn("Failed to deserialize direction", e);
            return null;
        }
    }
}