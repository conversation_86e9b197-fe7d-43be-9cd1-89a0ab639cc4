package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskRestrictedParkingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskRestrictedParkingRecordDOQueryParamDTO;
import java.util.List;

/**
 * 禁停区域检测记录仓储
 */
public interface RiskRestrictedParkingRecordRepository {

    /**
     * 根据参数查询禁停区域检测记录
     *
     * @param paramDTO
     * @return
     */
    List<RiskRestrictedParkingRecordDO> queryByParam(RiskRestrictedParkingRecordDOQueryParamDTO paramDTO);

    /**
     * 根据临时事件ID获取禁停区域检测记录
     *
     * @param tmpCaseId
     * @return
     */
    RiskRestrictedParkingRecordDO getByTmpCaseId(String tmpCaseId);

    /**
     * 根据参数查询禁停区域检测记录 (分页)
     *
     * @param paramDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    Paging<RiskRestrictedParkingRecordDO> queryByParamByPage(RiskRestrictedParkingRecordDOQueryParamDTO paramDTO, Integer pageNum, Integer pageSize);

    /**
     * 保存禁停区域检测记录
     *
     * @param riskRestrictedParkingRecordDO
     */
    void save(RiskRestrictedParkingRecordDO riskRestrictedParkingRecordDO);

    /**
     * 批量保存禁停区域检测记录
     *
     * @param riskRestrictedParkingRecordDOList
     */
    void batchSave(List<RiskRestrictedParkingRecordDO> riskRestrictedParkingRecordDOList);
}