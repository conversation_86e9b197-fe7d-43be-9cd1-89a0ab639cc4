package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseMarkInfoVO;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseMarkInfo;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @<PERSON> kongjian
 * @Date 2024/7/2
 */
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface CaseMarkInfoConverter extends SingleConvert<CaseMarkInfo, CaseMarkInfoDO> {

    @Override
    @Mapping(source = "riskLevel", target = "riskLevel", qualifiedByName = "toRiskLevelEnum")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "parseCaseMarkExtInfo")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    @Mapping(source = "improperStrandingReason", target = "improperStrandingReason", qualifiedByName = "parseImproperStrandingReason")
    CaseMarkInfoDO toDO(CaseMarkInfo markInfo);

    @Override
    @Mapping(source = "riskLevel", target = "riskLevel", qualifiedByName = "toRiskLevelInteger")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "serializeCaseMarkExtInfo")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    @Mapping(source = "improperStrandingReason", target = "improperStrandingReason", qualifiedByName = "serializeImproperStrandingReason")
    CaseMarkInfo toPO(CaseMarkInfoDO markInfoDO);

    @Mapping(source = "riskLevel", target = "riskLevel", qualifiedByName = "toRiskLevelInteger")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "serializeCaseMarkExtInfo")
    @Mapping(source = "improperStrandingReason", target = "improperStrandingReason", qualifiedByName = "serializeImproperStrandingReason")
    RiskCaseMarkInfoVO toVO(CaseMarkInfoDO caseMarkInfoDO);
}

