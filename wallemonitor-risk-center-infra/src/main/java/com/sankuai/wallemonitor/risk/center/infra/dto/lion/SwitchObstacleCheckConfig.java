package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SwitchObstacleCheckConfig {

    /**
     * 是否开启
     */
    private boolean enable;

    /**
     * 支持换帧判定的类型，非换帧可判定的类型，不关注
     * 比如前后的障碍物包含车或者非机动车，非机动车不关注
     */
    @Default
    private List<String> fineType = new ArrayList<>();

    /**
     * 前方范围最大夹角
     */
    @Default
    private Double frontRangeMaxAngle = 30D;

    /**
     * 后方范围最小夹角（）
     */
    @Default
    private Double behindRangeMinAngle = 150D;

    /**
     * 前序障碍物
     */
    private Integer frontSwitchMaxTime;

    /**
     * 后序障碍物
     */
    private Integer behindSwitchMaxTime;

    /**
     * 前方障碍物远离的最大距离
     */
    private Double frontObsAwayMaxDistance;

    /**
     * 前方障碍物靠近的最小距离
     */
    private Double frontObsCloseMaxDistance;

    /**
     * 前方障碍物远离的最大距离
     */
    private Double behindObsAwayMaxDistance;

    /**
     * 前方障碍物靠近的最小距离
     */
    private Double behindObsCloseMaxDistance;

    @Builder.Default
    private Map<String, String> rule = new HashMap<>();

    public ISCheckCategoryEnum aimCategory(VehicleRuntimeInfoContextDO contextDO, Map<String, Object> result) {
        if (contextDO == null || MapUtils.isEmpty(result) || MapUtils.isEmpty(rule)) {
            return null;
        }
        return rule.entrySet().stream().filter((entry) -> SpELUtil.evaluateBoolean(entry.getKey(), result)).findFirst()
                .map(Entry::getValue).map(ISCheckCategoryEnum::getBySubcategory).orElse(null);
    }
}
