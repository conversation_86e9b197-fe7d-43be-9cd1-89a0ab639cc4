package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.RiskDetectBaseConfig;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DetectContextDTO {

    /**
     * 车辆上下文
     */
    private VehicleRuntimeInfoContextDO runtimeContext;

    /**
     * 车辆信息
     */
    private VehicleEveInfoVTO eveInfo;

    /**
     * 检测器的配置
     */
    private RiskDetectBaseConfig detectConfig;

    @Default
    private List<String> ignoreFields = new ArrayList<>();

    /**
     * 检测过程上下文
     */
    @Builder.Default
    private Map<String, Object> detectProcessContext = new HashMap<>();

    public Map<String, Object> getDetectProcessContext() {
        if (Objects.isNull(detectProcessContext)) {
            detectProcessContext = new HashMap<>();
        }
        return detectProcessContext;
    }

    /**
     * 获取计算值 (表达式里面被动态使用)
     * 
     * @return
     */
    public Object get(String key) {
        if (Objects.isNull(detectConfig)) {
            return null;
        }
        String rule = detectConfig.getCalcRule(key);
        if (StringUtils.isBlank(rule)) {
            return null;
        }
        Map<String, Object> context = new HashMap<>();
        context.put("runtimeInfo", runtimeContext);
        context.put("eveInfo", eveInfo);
        // key可以取另外的key值，但是不能相互引用，风险点：如果重复引用，会引起oom
        context.put("detectorContext", this);
        // 算出来一个值
        Object obj = SpELUtil.evaluateWithVariables(rule, context, Object.class);
        detectProcessContext.put(key, obj);
        return obj;
    }

    /**
     * 获取计算值 (表达式里面被动态使用)
     *
     * @return
     */
    public <T> T get(String key, Class<T> clazz) {
        if (Objects.isNull(detectConfig)) {
            return null;
        }
        String rule = detectConfig.getCalcRule(key);
        if (StringUtils.isBlank(rule)) {
            return null;
        }
        Map<String, Object> context = new HashMap<>();
        context.put("runtimeInfo", runtimeContext);
        context.put("eveInfo", eveInfo);
        // key可以取另外的key值，但是不能相互引用，风险点：如果重复引用，会引起oom
        context.put("detectorContext", this);
        // 算出来一个值
        T obj = SpELUtil.evaluateWithVariables(rule, context, clazz);
        detectProcessContext.put(key, obj);
        return obj;
    }
    /**
     * 获取表达式上下文
     * 
     * @return
     */
    public Map<String, Object> getSpelContext() {
        Map<String, Object> spELParamContext = new HashMap<>();
        spELParamContext.put("detectorContext", this);
        spELParamContext.put("runtimeInfo", this.getRuntimeContext());
        spELParamContext.put("eveInfo", this.getEveInfo());
        return spELParamContext;

    }

    public VehicleRuntimeInfoContextDO toShortVehicleInfoSnapShot() {
        VehicleRuntimeInfoContextDO runtimeInfoContextDO = this.getRuntimeContext();
        // 保存的时候，暂时去掉障碍物、停止墙
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDOSnapshot = VehicleRuntimeInfoContextDO.builder().build();
        BeanUtils.copyProperties(runtimeInfoContextDO, vehicleRuntimeInfoContextDOSnapshot,
                ignoreFields.toArray(new String[0]));
        return vehicleRuntimeInfoContextDOSnapshot;
    }
}
