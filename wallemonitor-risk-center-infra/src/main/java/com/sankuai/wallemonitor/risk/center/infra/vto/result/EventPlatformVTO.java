package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.sankuai.wallemonitor.risk.center.infra.enums.CanBusDriverModeEnum;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class EventPlatformVTO {

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 事件类型
     */
    private Integer eventCode;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件时间戳
     */
    private Long eventTimestamp;

    /**
     * 车端/云端上报时间
     */
    private Long senderTimestamp;

    /**
     * 服务接收时间
     */
    private Long receiverTimestamp;

    /**
     * 车辆VIN
     */
    private String vin;

    /**
     * 车辆ID
     */
    private String vehicleId;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 记录名称
     */
    private String recordName;

    /**
     * UTM代号
     */
    private String utmZone;

    /**
     * UTMX坐标
     */
    private String utmX;

    /**
     * UTMY坐标
     */
    private String utmY;

    /**
     * 数据来源
     */
    private String datasource;

    /**
     * 事件内容
     */
    private DriveModeRecordContent content;

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DriveModeRecordContent {

        /**
         * 切换时间
         */
        private CanbusChassis canbusChassis;

        @Builder
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class CanbusChassis {

            /**
             * 驾驶模式
             */
            private CanBusDriverModeEnum drivingMode;
        }


    }

    /**
     * 获取驾驶模式
     *
     * @return
     */
    public CanBusDriverModeEnum getDrivingMode() {
        if (Objects.isNull(content) || Objects.isNull(content.getCanbusChassis())) {
            return null;
        }
        return content.getCanbusChassis().getDrivingMode();
    }
}
