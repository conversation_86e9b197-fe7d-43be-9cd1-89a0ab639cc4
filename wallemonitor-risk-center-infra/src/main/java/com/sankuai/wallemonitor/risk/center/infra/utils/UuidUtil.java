package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/05/12
 */
public class UuidUtil {

    /**
     * 生成uuid的工具类
     *
     * @return 生成的uuid
     */
    public static String uuid() {
        return Joiner.on("").join(Splitter.on("-").split(UUID.randomUUID().toString()));
    }
}

