package com.sankuai.wallemonitor.risk.center.infra.exception;

import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;

public class ReportLimitExceededException extends ErrorCodeException {

    public ReportLimitExceededException(String message) {
        super(ResponseCodeEnum.REPORT_TIME_REACH_LIMIT.getCode(), message);
    }
}
