package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import java.util.List;

/**
 * 风险事件仓储
 */
public interface RiskCaseVehicleRelationRepository {

    /**
     * 根据参数查询风险事件关联信息
     *
     * @param paramDTO
     * @return
     */
    List<RiskCaseVehicleRelationDO> queryByParam(RiderCaseVehicleRelationDOParamDTO paramDTO);

    /**
     * 根据事件ID和VIN查询风险事件
     *
     * @param eventId
     * @param vin
     * @return
     */
    RiskCaseVehicleRelationDO getByEventIdAndVin(String eventId, String vin);

    /**
     * 根据caseId查询风险事件关联详情
     *
     * @param caseId
     * @return
     */
    RiskCaseVehicleRelationDO getByCaseId(String caseId);


    /**
     * 保存风险事件关联信息
     *
     * @param riskCaseDO
     */
    void save(RiskCaseVehicleRelationDO riskCaseDO);

    /**
     * 批量保存风险事件关联信息
     *
     * @param riskCaseDOList
     */
    void batchSave(List<RiskCaseVehicleRelationDO> riskCaseDOList);


}
