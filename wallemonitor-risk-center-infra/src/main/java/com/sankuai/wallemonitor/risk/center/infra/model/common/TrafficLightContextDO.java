package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficLightConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Slf4j
public class TrafficLightContextDO {

    /**
     * 当前感知到的红绿灯
     */
    private List<TrafficLightDO> trafficLightList;

    /**
     * 当前感知的id
     */
    private String nowPlanningId;


    /**
     * 历史意图的红绿灯id
     */
    private List<String> historyTrafficHmiIdList;


    /**
     * 历史感知的红绿灯
     */
    private List<List<TrafficLightDO>> historyTrafficLightList;
    /**
     * 更新时间
     */
    private Long updateTime;


    /**
     * 判断是否等红绿灯
     *
     * @return
     */
    public boolean isWaitingRed() {
        if (CollectionUtils.isEmpty(trafficLightList) || StringUtils.isBlank(nowPlanningId)) {
            return false;
        }
        //感知到任意一个灯
        return trafficLightList.stream()
                .anyMatch(trafficLightDO -> nowPlanningId.equals(trafficLightDO.getId()) && trafficLightDO.isRed());

    }

    /**
     * 获取当前等待的红绿灯
     *
     * @return
     */
    public TrafficLightDO getNowTrafficLight(TrafficLightConfigDTO trafficLightConfigDTO) {
        if (CollectionUtils.isEmpty(trafficLightList) || StringUtils.isBlank(nowPlanningId)) {
            //无红绿灯信号，返回null
            return null;
        }
        //感知到灯
        TrafficLightDO aimedTrafficLight = trafficLightList.stream()
                //如果获取失败，则没有拿到红绿灯的信号
                .filter(trafficLightDO -> nowPlanningId.equals(trafficLightDO.getId()))
                .findFirst().orElse(null);
        if (aimedTrafficLight != null) {
            return aimedTrafficLight;
        }

        // 如果配置为模糊灯id的，则可使用其他非精确匹配的交通灯信号
        if (trafficLightConfigDTO.getFuzzyLightIdByPlanning()) {
            if (trafficLightList.size() != 1) {
                log.warn("has not just one traffic light, {}", trafficLightList);
            }

            aimedTrafficLight = trafficLightList.stream()
                    .filter(trafficLightDO -> TrafficLightTypeEnum.listNormalColor()
                            .contains(trafficLightDO.getColor()))
                    .findFirst().orElse(null);
            if (aimedTrafficLight != null) {
                return aimedTrafficLight;
            }
        }

        //如果未匹配上
        if (CollectionUtils.isEmpty(trafficLightList)) {
            //则看当前灯，当前如果没有灯，不做滑动窗口判断
            return null;
        }
        //当前有灯时，做如下逻辑判断
        // 1.当前意图的id，是否是过去红绿灯的一个
        if (CollectionUtils.isNotEmpty(historyTrafficLightList)) {
            //如果历史的灯不为空
            aimedTrafficLight = historyTrafficLightList.stream()
                    //扁平化，原顺序保存，先存在的时间最先
                    .flatMap(List::stream)
                    //当前意图的id，是否是过去红绿灯的一个
                    .filter(trafficLightDO -> nowPlanningId.equals(trafficLightDO.getId()))
                    .findFirst()
                    .orElse(null);
        }
        if (aimedTrafficLight == null && CollectionUtils.isNotEmpty(historyTrafficHmiIdList)) {
            // 2 如果没拿到，则看当前意图的id，是否是过去红绿灯的一个
            aimedTrafficLight = historyTrafficHmiIdList.stream()
                    //看当前的的灯，否匹配最近的一个
                    .map(historyId -> trafficLightList.stream()
                            .filter(trafficLightDO -> historyId.equals(trafficLightDO.getId()))
                            .findFirst()
                            .orElse(null))
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null);
        }
        //如果都没拿到，就返回null
        return aimedTrafficLight;

    }


}