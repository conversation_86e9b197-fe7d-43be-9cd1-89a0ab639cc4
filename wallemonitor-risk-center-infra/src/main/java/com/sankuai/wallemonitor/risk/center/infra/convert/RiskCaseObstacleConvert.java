package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseObstacle;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseObstacleDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", imports = {EnumsConvertMapper.class}, uses = {EnumsConvertMapper.class})
public interface RiskCaseObstacleConvert extends SingleConvert<RiskCaseObstacle, RiskCaseObstacleDO> {

    @Override
    @Mapping(source = "obstacle", target = "obstacle", qualifiedByName = "toRiskObstacleDTO")
    RiskCaseObstacleDO toDO(RiskCaseObstacle riskCaseObstacle);

    @Override
    @Mapping(source = "obstacle", target = "obstacle", qualifiedByName = "toRiskObstacleStr")
    RiskCaseObstacle toPO(RiskCaseObstacleDO riskCaseObstacleDO);
}