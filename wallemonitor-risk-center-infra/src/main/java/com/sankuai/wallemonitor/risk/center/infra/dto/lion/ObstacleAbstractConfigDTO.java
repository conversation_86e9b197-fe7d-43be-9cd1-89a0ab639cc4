package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ObstacleAbstractConfigDTO {

    private Boolean enable;
    /**
     * 车道搜索范围距离（米）
     */
    private Double range;

    /**
     * 使用历史位置的前移秒数
     */
    private Integer pastSecond;

    /**
     * 车辆前序定位点需要满足的距离要求（防止过近导致的异常）
     */
    private Double preMinDistance;

    /**
     * 障碍物的类型
     */
    private Set<String> fineTypeList;

    /**
     * 是否开启障碍物摘要计算
     * */
    private Boolean open;


    /**
     * 同向车道的判定夹角
     */
    @Builder.Default
    private Double laneSameDirectionTheta = 30D;


    /**
     * 车道类型
     */
    private List<String> laneTypeList;
}
