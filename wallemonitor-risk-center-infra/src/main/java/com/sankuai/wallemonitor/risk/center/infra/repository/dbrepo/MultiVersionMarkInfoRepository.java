package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.dto.MultiVersionQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.MultiVersionMarkInfoDO;
import java.util.List;

/**
 * 分拣数据仓储层
 * 
 */
public interface MultiVersionMarkInfoRepository {

    /**
     *
     *
     * @param param
     * @return
     */
    List<MultiVersionMarkInfoDO> queryByParam(MultiVersionQueryParamDTO param);

    /**
     *
     *
     * @param caseId
     * @return
     */
    MultiVersionMarkInfoDO getByCaseIdAndVersion(String caseId, String version);

    /**
     * 保存
     *
     * @param caseSortDataDO
     */
    void save(MultiVersionMarkInfoDO caseSortDataDO);

    /**
     * 批量保存
     *
     * @param caseSortDataDOList
     */
    void batchSave(List<MultiVersionMarkInfoDO> caseSortDataDOList);

}
