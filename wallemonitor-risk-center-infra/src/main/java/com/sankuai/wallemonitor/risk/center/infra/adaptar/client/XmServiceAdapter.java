package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI;
import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
@Slf4j
@Component
public class XmServiceAdapter {

    private long expireTime = 0;
    private String accessToken = "";
    private final static long FIVE_MINUTES = 1000 * 60 * 5;

    @ThriftClientProxy(remoteAppKey = AppKeyConstant.XM_OPEN_APP_KEY, timeout = 2000)
    private XmAuthServiceI.Iface xmAuthService;

    @Value("$KMS{xopen.appID}")
    private String appID;

    @Value("$KMS{xopen.appSecret}")
    private String appSecret;

    public String getToken() {
        long nowTime = System.currentTimeMillis();
        if (expireTime - FIVE_MINUTES > nowTime) {
            return accessToken;
        }
        AppAuthInfo appAuthInfo = new AppAuthInfo();
        appAuthInfo.setAppkey(appID);
        appAuthInfo.setAppSecret(appSecret);

        try {
            AccessTokenResp resp = xmAuthService.accessToken(appAuthInfo);
            if (resp.status.getCode() == 0) {
                accessToken = resp.getAccessToken().getToken();
                expireTime = resp.getAccessToken().getExpireTime();
            } else {
                log.error("生成开放平台Token失败, resp: {}", resp);
                return accessToken;
            }
            return accessToken;
        } catch (Exception e) {
            log.error("生成开放平台Token失败", e);
            return accessToken;
        }
    }
}
