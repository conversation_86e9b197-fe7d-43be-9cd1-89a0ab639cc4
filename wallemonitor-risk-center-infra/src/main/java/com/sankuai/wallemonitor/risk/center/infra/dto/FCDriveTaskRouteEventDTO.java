package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * FC行驶任务路由事件DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FCDriveTaskRouteEventDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 行驶任务ID
     */
    @JsonProperty("drive_task_id")
    private String driveTaskId;

    /**
     * 该车当前正在运行的路由信息
     */
    @JsonProperty("route_points")
    private List<RoutePoint> routePoints;

    /**
     * 该车当前正在运行的路由中的SD路由段信息
     */
    @JsonProperty("sd_route_points")
    private List<RoutePoint> sdRoutePoints;

    /**
     * 该车当前正在运行的路线的预估耗时（Follow高德路线时输出高德预估耗时，未Follow时该字段赋值为null） 单位为秒
     */
    @JsonProperty("gaode_estimated_duration")
    private Double gaodeEstimatedDuration;

    /**
     * 该车当前正在运行的路线的预估耗时（FC自行计算值） 单位为秒
     */
    @JsonProperty("fc_estimated_duration")
    private Double fcEstimatedDuration;

    /**
     * 预计抵达终点的时间戳 精确到 ms
     */
    @JsonProperty("estimated_time_of_arrival")
    private Long estimatedTimeOfArrival;

    /**
     * 事件更新时刻的时间戳 精确到 ms
     */
    @JsonProperty("update_timestamp")
    private Long updateTimestamp;

    /**
     * 路由点信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RoutePoint implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 路由点序号
         */
        @JsonProperty("route_s")
        private Double routeS;

        /**
         * x坐标
         */
        private Double x;

        /**
         * y坐标
         */
        private Double y;

        /**
         * z坐标
         */
        private Double z;
    }
} 