package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseParkingPlotRelation;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseParkingPlotRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 泊车失败事件与泊位ID关联转换器
 */
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskCaseParkingPlotRelationConvert extends
        SingleConvert<RiskCaseParkingPlotRelation, RiskCaseParkingPlotRelationDO> {

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    RiskCaseParkingPlotRelationDO toDO(RiskCaseParkingPlotRelation relation);

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    RiskCaseParkingPlotRelation toPO(RiskCaseParkingPlotRelationDO relationDO);
} 