package com.sankuai.wallemonitor.risk.center.infra.factory;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.wallemonitor.risk.center.infra.comsumprocess.CommonMessageConsumer;
import com.sankuai.wallemonitor.risk.center.infra.comsumprocess.proxy.CommonMessageConsumerProxy;
import com.sankuai.wallemonitor.risk.center.infra.dto.MafkaMessageConsumerConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.MafkaMessageProducerConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.MarkMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.producer.CommonMessageProducer;
import java.util.Properties;
import lombok.SneakyThrows;

/**
 * 生成mafka producer
 */
public class MafkaFactory {

    @SneakyThrows
    public static CommonMessageProducer<MarkMessageDTO> getMafkaCommonProducer(MafkaMessageProducerConfigDTO build) {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, build.getNameSpace());
        // 设置生产者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, build.getAppKey());
        properties.setProperty("openHook", "false");
        IProducerProcessor producer = MafkaClient.buildDelayProduceFactory(properties, build.getTopicName());
        CommonMessageProducer<MarkMessageDTO> commonMessageProducer = new CommonMessageProducer<>(
                MessageTopicEnum.WALLEMONITOR_RISK_MULTI_AUTO_MARK_MESSAGE);
        commonMessageProducer.initProcess(producer);
        return commonMessageProducer;
    }

    public static CommonMessageConsumerProxy getMafkaCommonConsumer(MafkaMessageConsumerConfigDTO consumerConfigDTO,
            CommonMessageConsumer consumerService) {
        return new CommonMessageConsumerProxy(consumerService, consumerConfigDTO);
    }
}
