package com.sankuai.wallemonitor.risk.center.infra.factory;

import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DomainUniqueKeyEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.compare.EntityChangeRecordDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.compare.RecordCompareUtils;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.collections.CollectionUtils;

/**
 * 领域事件工厂
 */
public class DomainEventFactory {


    /**
     * 构建领域事件变化DTO
     *
     * @param <T>
     * @return
     */
    public static <T> DomainEventChangeDTO<T> createDomainEventChangeDTO(DomainEventDTO<T> domainEventDTO,
            Class<?> tClass) {
        CheckUtil.isNotNull(domainEventDTO, "领域事件DTO不能为空");
        // 从注解和枚举中读取
        List<String> uniqueKey = CollectionUtils.isNotEmpty(DomainUniqueKeyEnum.getUniqueByClass(tClass))
                ? DomainUniqueKeyEnum.getUniqueByClass(tClass) : ReflectUtils.getAnnField(tClass, DomainUnique.class);
        //找到对比的结果
        List<EntityChangeRecordDTO> entityChangeRecordDTOS = RecordCompareUtils.findRecordsChange(
                domainEventDTO.getBefore(), domainEventDTO.getAfter(),
                uniqueKey, (Class<T>)tClass);
        //筛选出来新的、删除的、修改的
        Set<String> newAdd = new HashSet<>();
        Set<String> deleted = new HashSet<>();
        Set<String> changed = new HashSet<>();
        Map<String, T> beforeInstance = new HashMap<>();
        Map<String, T> afterInstance = new HashMap<>();
        Map<String, EntityChangeRecordDTO> entityChangeRecordDTOMap = new HashMap<>();
        entityChangeRecordDTOS.forEach(entityChangeRecordDTO -> {
            switch (entityChangeRecordDTO.getEntityChangeType()) {
                case ADD: {
                    newAdd.add(entityChangeRecordDTO.getEntityId());
                    break;
                }
                case DELETE: {
                    deleted.add(entityChangeRecordDTO.getEntityId());
                    break;
                }
                case CHANGE:
                default: {
                    changed.add(entityChangeRecordDTO.getEntityId());
                    break;
                }
            }
            //将entityId作为key，entityChangeRecordDTO作为value，放到map中
            entityChangeRecordDTOMap.put(entityChangeRecordDTO.getEntityId(), entityChangeRecordDTO);
            //放入前后的变化中去
            beforeInstance.put(entityChangeRecordDTO.getEntityId(), (T) entityChangeRecordDTO.getBeforeEntity());
            afterInstance.put(entityChangeRecordDTO.getEntityId(), (T) entityChangeRecordDTO.getAfterEntity());
        });
        return DomainEventChangeDTO.<T>builder()
                .entry(domainEventDTO.getEntry())
                .traceId(domainEventDTO.getTraceId())
                .operator(domainEventDTO.getOperator())
                .timestamp(domainEventDTO.getTimestamp())
                .before(domainEventDTO.getBefore())
                .after(domainEventDTO.getAfter())
                .allTypeChangedRecord(entityChangeRecordDTOMap)
                .extInfo(domainEventDTO.getExtInfo())
                //新类的字段
                .newAddedDomainKey(newAdd)
                .deletedDomainKey(deleted)
                .changedDomainKey(changed)
                .beforeMap(beforeInstance)
                .afterMap(afterInstance)
                .domainClass((Class<T>) tClass)
                .build();
    }


}
