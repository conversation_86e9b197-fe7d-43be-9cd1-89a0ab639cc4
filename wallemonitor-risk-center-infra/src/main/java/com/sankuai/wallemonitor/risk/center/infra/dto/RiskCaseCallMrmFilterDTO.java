package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseCallMrmFilterDTO {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 数据源
     */
    private Integer source;

    /**
     * 风险类型
     */
    private Integer type;

    /**
     * 持续时间
     */
    private Long duration;

    /**
     * 持续时间
     */
    private Long riskDuration;

    /**
     * vhr信息
     */
    private String vhr;

    /**
     * 坐席状态
     */
    private Integer mrmStatus;

    /**
     * 是否存在救援工单
     */
    private Boolean hasRescueOrder;

    /**
     * 是否存在事故状态
     */
    private Boolean hasAccident;

    /**
     * 是否存在RE工单
     */
    private Boolean hasReOrder;

    /**
     * 用车目的
     */
    private String purpose;

    /**
     * 停滞原因
     */
    private ImproperStrandingReason improperStrandingReason;

    /**
     * 风险类型
     */
    private String firstSubCategory;

    /**
     * 场地
     */
    private String placeCode;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 预检item
     */
    private RiskCheckingQueueItemDO item;

    /**
     * caseId
     * */
    private String caseId;

    /**
     * 车辆上下文
     */
    @JsonIgnore
    private VehicleRuntimeInfoContextDO vehicleRuntimeInfo;
}
