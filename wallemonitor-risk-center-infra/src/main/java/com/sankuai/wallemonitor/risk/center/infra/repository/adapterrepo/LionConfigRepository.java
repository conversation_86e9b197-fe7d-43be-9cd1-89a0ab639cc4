package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo;

import com.sankuai.wallemonitor.risk.center.infra.dto.BroadCastCalcConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.HighNegativeCallSafetyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskRaptorReportConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficLightConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateAlertConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.upgrade.AlertUpgradeConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.DataContainerConsumeConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.LongWaitAreaConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.ObstacleAbstractConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.PreHandleDataConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.WaitInQueueConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.BroadCastStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.CallMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.DomainEventConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.DriveOnTrafficLineDetectConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.FastUploadAutoCarDataConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.FridayConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.HighNegativeUploadAutoCarDataConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ManualParkingMarkConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.RetrogradeDetectConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.RiskDetectBaseConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.SpecialAreaStrandingDetectConfig;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.VehicleCounterRuleConfig;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.List;
import java.util.Map;
import javafx.util.Pair;

/**
 * <AUTHOR>
 * @Date 2024/6/17
 */
public interface LionConfigRepository {

    /**
     * 获取风险类型与广播策略配置之间的映射关系
     *
     * @return
     */
    Map<RiskCaseTypeEnum, BroadCastStrategyConfigDTO> getCaseType2BroadCastStrategyConfig();

    /**
     * 获取广播策略配置
     *
     * @param caseType
     * @return
     */
    BroadCastStrategyConfigDTO getByCaseType(RiskCaseTypeEnum caseType);

    /**
     * 需要监听的codeList
     */
    List<Integer> getFocusedAutocarEventCodeList();

    /**
     * 获取
     */
    BroadCastCalcConfigDTO getBroadCastCalcConfig();

    /**
     * 获取用户须知版本ID
     */
    Long getUserNoticeVersionId();

    /**
     * 获取用户须知版本内容
     */
    String getUserNoticeVersionContext();

    /**
     * 获取用户鉴权登陆态的有效时间，单位为毫秒
     */
    Long getTokenValidMillis();

    /**
     * 获取呼叫云控策略配置
     *
     * @return
     */
    Map<Pair<Integer, Integer>, CallMrmStrategyConfigDTO> getCallMrmStrategyConfigDTO();

    /**
     * 获取取消呼叫云控策略配置
     *
     * @return
     */
    Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> getReleaseMrmStrategyConfigDTO();


    /**
     * 判断是否开启呼叫开关
     *
     * @return
     */
    boolean isStagnationCallSwitchOn();


    /**
     * 判断vin是否在呼叫列表中
     *
     * @param vin
     * @return
     */
    boolean isInMrmCallVinList(String vin);


    /**
     * 判断用途是否为业务车辆
     *
     * @param purpose
     * @return
     */
    boolean isBusinessVehicleByPurpose(String purpose);

    /**
     * 判断业务是否需要调用云控
     *
     * @return
     */
    boolean isBusinessVehicleNeedCallMrm();

    /**
     * 停滞预检队列双跑是否开启
     *
     * @return
     */
    Boolean enableImproperStrandingCheckingQueue();

    /**
     * 停滞预检队列双跑是否开启
     *
     * @return
     */
    Boolean enableImproperStrandingCheckingQueueParallel();

    /**
     * 获取风险检查队列配置
     *
     * @return
     */
    RiskAutoCheckConfigDTO getRiskCheckingConfig();

    /**
     * 获取人工主动停车标记配置
     *
     * @return
     */
    ManualParkingMarkConfigDTO getManualParkingMarkConfig();

    /**
     * 获取车辆运行时信息更新并行处理的最大批量大小
     *
     * @return
     */
    Integer getListPartitionBatchSize(String type);

    /**
     * 获取特殊区域（路口施工区域）停滞预检配置
     */
    SpecialAreaStrandingDetectConfig getSpecialAreaStrandingConfig();

    /**
     * 获取逆行预检配置
     */
    RetrogradeDetectConfig getRetrogradeDetectConfig();

    /**
     * 获取非法压线预检配置
     */
    DriveOnTrafficLineDetectConfig getDriveOnTrafficLineDetectConfig();

    /**
     * 获取停滞事件的检测配置
     */
    RiskDetectBaseConfig getStrandingDetectConfig();

    Map<Integer, Map<Integer, String>> getRiskLevelConfig();

    /**
     * 获取配置的分拣所属问题列表
     */
    List<String> getSortProblemList();

    /**
     * 获取区域运营名称列表
     *
     * @return
     */
    List<String> getAreaOperateNameList();


    /**
     * 获取快速上传车辆数据配置
     *
     * @return
     */
    FastUploadAutoCarDataConfig getFastUploadAutocarDataConfig();

    /**
     * 获取大模型可以查实的类型
     *
     * @return
     */
    FridayConfig getFridayConfig();


    /**
     * 获取自动标注配置
     *
     * @return
     */
    RiskAutoCheckConfigDTO getRiskAutoMarkConfig();

    /**
     * 获取case链接模板id
     */
    Long getCaseUrlTemplateId();

    Map<String, String> getLinkDataTemplateValues();


    VehicleCounterRuleConfig getVehicleCounterRuleConfig();


    /**
     * 根据riskType获取配置
     *
     * @param riskType
     * @return
     */
    HighNegativeCallSafetyConfigDTO getRiskSafetyConfigByRiskType(RiskCaseTypeEnum riskType);

    /**
     * 获取风险类型与风险呼叫安全配置
     *
     * @return
     */
    Map<RiskCaseTypeEnum, HighNegativeCallSafetyConfigDTO> getRiskCallSafetyConfig();

    HighNegativeUploadAutoCarDataConfig getHighNegativeFastUploadAutocarDataConfig();

    /**
     * 获取领域事件配置
     *
     * @return
     */
    DomainEventConfig getDomainEventConfig();

    /**
     * occurTime时间修正开关
     *
     * @return
     */
    boolean isRiskOccurTimeAmendSwitchOn();

    /**
     * 停滞不当事件转换开关
     *
     * @return
     */
    boolean isImproperStrandingCaseTransformSwitchOn();




    Map<Integer, RiskRaptorReportConfigDTO> getRaptorReportConfig();

    Boolean isTransferMarkSwitchOn();

    RiskDetectBaseConfig getRestrictedParkingDetectConfig();

    RiskDetectBaseConfig getDetectConfig(RiskCaseTypeEnum riskCaseTypeEnum);

    Class<?> getOnboardMessageClassByTopic(String topic);

    Double getObstacleReserveDistance();

    Boolean isValidVehicle(VehicleEveInfoVTO vehicleEveInfoVTO);

    /**
     * 获取红绿灯配置
     */
    TrafficLightConfigDTO getTrafficLightConfig();

    /**
     * 过滤时间内的检查队列项
     * @return
     */
    String getTimeToCheckingQueueItemFilterVersion();

    /**
     * 获取排队通行配置
     */
    WaitInQueueConfigDTO getWaitInQueueConfig();

    /**
     * 获取数据预处理配置
     */
    PreHandleDataConfig getPreHandleDataConfig();

    /**
     * 获取所有版本的自动标注配置
     *
     * @return
     */
    Map<String, RiskAutoCheckConfigDTO> getAllAutoMarkConfig();

    /**
     * 获取指定版本的标注
     *
     * @param version
     * @return
     */
    RiskAutoCheckConfigDTO getRiskAutoMarkConfigByVersion(String version);

    /**
     * 获取障碍物摘要配置
     */
    ObstacleAbstractConfigDTO getObstacleAbstractConfig();


    /**
     * 获取data container 的消费配置
     *
     * */
    DataContainerConsumeConfigDTO getDataContainerConsumeConfigDTO();



    /**
     * 获取呼叫保障系统的配置
     * */
    Map<Pair<Integer, Integer>, LionConfigRepositoryImpl.CallSecuritySystemStrategyConfigDTO> getCallSecurityStrategyConfig();


    /**
     * 取消呼叫保障系统的配置
     * */

    Map<Pair<Integer, Integer>, LionConfigRepositoryImpl.ReleaseSecuritySystemConfigDTO> getReleaseSecuritySystemStrategyConfig();


    /**
     *
     * 获取呼叫保障系统的开关
     * */
    Boolean getRiskCaseSecuritySystemCallSwitch();

    /**
     *
     * 车辆是否在保障系统的呼叫列表里
     * */
    Boolean isInSecuritySystemCallVinList(String vin);

    /**
     *
     * 长等待区域配置
     * */
    LongWaitAreaConfigDTO getLongWaitAreaConfig();

    Map<Pair<Integer, Integer>, LionConfigRepositoryImpl.ReleaseMrmStrategyConfigDTO> getReleaseMrmDisposedConfig();

    /**
     * 获取聚合告警配置
     */
    AggregateAlertConfigDTO getAggregateAlertConfig();

    /**
     * 获取风险告警升级配置
     */
    AlertUpgradeConfigDTO getAlertUpgradeConfig();

    /**
     * 获取允许延迟的阈值
     */
    Integer getOnboardMessageAllowLaterThreshold();


}
