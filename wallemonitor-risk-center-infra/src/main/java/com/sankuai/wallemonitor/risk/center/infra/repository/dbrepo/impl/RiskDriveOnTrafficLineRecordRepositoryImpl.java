package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskDriveOnTrafficLineRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskDriveOnTrafficLineRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskDriveOnTrafficLineRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDriveOnTrafficLineRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskDriveOnTrafficLineRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskDriveOnTrafficLineRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 压线预检过程仓储实现
 */
@Component
@Slf4j
public class RiskDriveOnTrafficLineRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskDriveOnTrafficLineRecordMapper, RiskDriveOnTrafficLineRecordConvert, RiskDriveOnTrafficLineRecord, RiskDriveOnTrafficLineRecordDO> implements
        RiskDriveOnTrafficLineRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询压线预检过程
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskDriveOnTrafficLineRecordDO> queryByParam(RiskDriveOnTrafficLineRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询压线预检过程 (分页)
     *
     * @param paramDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public Paging<RiskDriveOnTrafficLineRecordDO> queryByParamByPage(
            RiskDriveOnTrafficLineRecordDOQueryParamDTO paramDTO, Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据ID查询压线预检过程
     *
     * @param id
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskDriveOnTrafficLineRecordDO getById(Long id) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(id.toString())
                .build()));
    }

    /**
     * 保存压线预检过程
     *
     * @param riskDriveOnTrafficLineRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskDriveOnTrafficLineRecordDO riskDriveOnTrafficLineRecordDO) {
        super.save(riskDriveOnTrafficLineRecordDO);
    }

    /**
     * 批量保存压线预检过程
     *
     * @param riskDriveOnTrafficLineRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskDriveOnTrafficLineRecordDO> riskDriveOnTrafficLineRecordDOList) {
        super.batchSave(riskDriveOnTrafficLineRecordDOList);
    }
}
