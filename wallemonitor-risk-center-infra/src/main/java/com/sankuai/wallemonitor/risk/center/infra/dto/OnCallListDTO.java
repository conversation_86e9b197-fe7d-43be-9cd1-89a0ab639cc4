package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OnCallListDTO {

    /**
     * 车辆vin
     */
    private String vin;

    /**
     * 操作时间
     */
    @JsonProperty("operate_time")
    private Date operateTime;

    /**
     * 工单id
     */
    @JsonProperty("case_id")
    private String caseId;

    /**
     * 数据来源
     */
    @JsonProperty("data_source")
    private String dataSource;

}
