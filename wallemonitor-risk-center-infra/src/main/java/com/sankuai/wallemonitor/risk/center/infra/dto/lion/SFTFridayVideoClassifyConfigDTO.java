package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SFTFridayVideoClassifyConfigDTO {

    private String userPrompt;

    private String systemPrompt;

    /**
     * 查停滞时发生时间前n秒的图片
     * */
    private Integer startNSeconds = 0;

    /**
     * 查停滞时发生时间后n秒的图片
     */
    private Integer endNSeconds = 0;

    /**
     * startNSceonds 到 endNSeconds 时间每几个step取一张图片
     * */
    private Integer step = 1;

    /**
     * 模型名称
     * */
    @Builder.Default
    private String modelName = "risk_check_qwen_2_5_32";

    /**
     * APPID
     * */
    @Builder.Default
    private String appId = "1840301155550003252";

    @Builder.Default
    private Integer timeout = 10;
    
    // ========== SFT解析配置 ==========
    
    /**
     * 需要接管的标识值
     */
    @Builder.Default
    private String takeOverValue = "是";
    
    /**
     * 不需要接管的标识值
     */
    @Builder.Default
    private String notTakeOverValue = "否";
    
    /**
     * 需要接管时的默认分类
     */
    @Builder.Default
    private ISCheckCategoryEnum defaultTakeOverCategory = ISCheckCategoryEnum.GOOD_OTHER;
    
    /**
     * 不需要接管时的默认分类
     */
    @Builder.Default
    private ISCheckCategoryEnum defaultNotTakeOverCategory = ISCheckCategoryEnum.BAD_OTHER;
    
    /**
     * 默认接管值（当无法识别时使用）
     */
    @Builder.Default
    private String defaultTakeOverValue = "否";

    /**
     * 请求次数，根据次数判断
     * */
    @Builder.Default
    private Integer votingTimes = 3;
}
