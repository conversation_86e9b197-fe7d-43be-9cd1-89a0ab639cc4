package com.sankuai.wallemonitor.risk.center.infra.model.core;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 考试障碍物停滞事件检测记录DO
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RiskExamObstacleStrandingRecordDO extends RiskDetectorRecordBaseDO {

    /**
     * 是否存在障碍物
     */
    private Boolean hasObstacle;
} 