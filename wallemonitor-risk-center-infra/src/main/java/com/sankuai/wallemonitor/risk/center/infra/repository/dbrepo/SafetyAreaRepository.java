package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.SafetyArea;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.SafetyAreaQueryParamDTO;

import java.util.Date;
import java.util.List;

/**
 * 风险区域仓储
 */
public interface SafetyAreaRepository {

    /**
     * 批量保存
     *
     * @param safetyAreaDOList
     */
    void batchSave(List<SafetyAreaDO> safetyAreaDOList);

    /**
     * 按条件查询安全区域
     *
     * @return
     */
    List<SafetyAreaDO> queryByParam(SafetyAreaQueryParamDTO paramDTO);

    /**
     * 判断是否在停车区域内
     *
     * @param location
     * @return
     */
    String verifyInParkingArea(PositionDO location);


    Boolean isInDelayRecallPolygon(String poiName, PositionDO positionDO, Date occurTime) ;

    /**
     * 判断是否在延长判断区域内
     * */
    Boolean isInDelayRecallPolygon(PositionDO positionDO, Date occurTime) ;

    /**
     * 根据位置查停车场
     * */
    SafetyAreaDO querySafetyAreaDOByPosition(PositionDO positionDO);
}
