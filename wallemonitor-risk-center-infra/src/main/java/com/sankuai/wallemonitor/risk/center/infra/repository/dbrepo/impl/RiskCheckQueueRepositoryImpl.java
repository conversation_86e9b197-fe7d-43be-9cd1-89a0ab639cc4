package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCheckingQueueConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCheckingQueueMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCheckingQueueItem;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCheckQueueRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCheckQueueQueryParam;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskCheckQueueRepositoryImpl extends
        AbstractMapperSingleRepository<RiskCheckingQueueMapper, RiskCheckingQueueConvert, RiskCheckingQueueItem, RiskCheckingQueueItemDO> implements
        RiskCheckQueueRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    @Resource
    private SquirrelAdapter squirrelAdapter;

    @Override
    @RepositoryQuery
    public List<RiskCheckingQueueItemDO> queryByParam(RiskCheckQueueQueryParam param) {
        return super.queryByParam(param);
    }

    @Override
    @RepositoryExecute
    public void save(RiskCheckingQueueItemDO riskCheckingQueueItemDO) {
        super.save(riskCheckingQueueItemDO);

    }

    @Override
    @RepositoryExecute
    public void batchSave(List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList) {
        super.batchSave(riskCheckingQueueItemDOList);
    }

    @Override
    @RepositoryQuery
    public RiskCheckingQueueItemDO getByCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }


}
