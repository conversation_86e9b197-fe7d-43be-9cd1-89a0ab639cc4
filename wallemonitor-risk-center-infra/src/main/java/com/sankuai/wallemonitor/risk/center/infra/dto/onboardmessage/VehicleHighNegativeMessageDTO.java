package com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.wallemonitor.risk.center.infra.enums.ConstraintSourceTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.LineType;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleHighNegativeMessageDTO {

    @JsonProperty("highNegativeEventMeta")
    private HighNegativeEventMeta highNegativeEventMeta;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionalDouble {

        @JsonProperty("hasValue")
        private Boolean hasValue;
        @JsonProperty("value")
        private Double value;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HighNegativeEventMeta {

        @JsonProperty("isValid")
        private Boolean isValid;
        private CommonMeta commonMeta;
        private TouchLineMeta touchLineMeta;
        private ConstructionZoneMeta constructionZoneMeta;
        private List<FenceAndFieldMetaDTO> closestFenceAndFieldMetas;

    }



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CommonMeta {

        private OptionalDouble distanceToNextJunction;
        @JsonProperty("isEgoOnReverseLane")
        private Boolean isEgoOnReverseLane;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TouchLineMeta {

        @JsonProperty("isTouchLine")
        private Boolean isTouchLine;
        private LineType lineType;

    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConstructionZoneMeta {

        @JsonProperty("isPathOverlapWithConstructionZone")
        private Boolean isPathOverlapWithConstructionZone;
        private Double distanceToFrontConstructionZone;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FenceAndFieldMetaDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 障碍物id
         */
        private String perceptionId;
        /**
         * 距离停止墙的距离
         */
        private Double stopDistanceToEgo;
        /**
         * 位置
         */
        private Vector2 position;
        /**
         * 停止墙的类型
         */
        private ConstraintSourceTypeEnum constraintSourceType;

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Vector2 implements Serializable {
            private static final long serialVersionUID = 1L;
            private double x;
            private double y;
        }

    }
}