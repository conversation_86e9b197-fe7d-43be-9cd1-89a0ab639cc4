package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskErrorQueueRecord;

/**
 * <p>
 * 错误排队检测记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-10-14
 */
public interface RiskErrorQueueRecordMapper extends CommonMapper<RiskErrorQueueRecord> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskErrorQueueRecord> getPOClass() {
        return RiskErrorQueueRecord.class;
    }
} 