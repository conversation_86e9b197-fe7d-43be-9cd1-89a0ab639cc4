package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface PositionConvert {


    /**
     * 转换坐标系
     *
     * @param positionDO
     * @param coordinateSystemEnum
     * @return
     */
    default PositionDO toPositionDO(PositionDO positionDO, CoordinateSystemEnum coordinateSystemEnum) {
        if (positionDO == null || coordinateSystemEnum == null) {
            return null;
        }
        return GeoToolsUtil.transferCoordinateSystemEnum(positionDO, coordinateSystemEnum);
    }
}
