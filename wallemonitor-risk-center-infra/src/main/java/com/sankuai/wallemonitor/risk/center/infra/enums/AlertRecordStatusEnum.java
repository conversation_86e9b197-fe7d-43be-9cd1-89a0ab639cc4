package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 告警记录状态枚举
 */
@Getter
@AllArgsConstructor
public enum AlertRecordStatusEnum {

    /**
     * 无需处置
     */
    NO_NEED_PROCESS(-1, "无需处置"),

    /**
     * 未处置
     */
    UNPROCESSED(0, "未处置"),

    /**
     * 处置中
     */
    PROCESSING(10, "处置中"),

    /**
     * 已处置
     */
    PROCESSED(20, "已处置");

    private final Integer code;
    private final String desc;

    /**
     * 根据code查找枚举
     */
    public static AlertRecordStatusEnum findByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AlertRecordStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public boolean isUnprocessed() {
        return this == UNPROCESSED;
    }

    public boolean isCompleted() {
        return this == PROCESSED || this == NO_NEED_PROCESS;
    }

    public boolean isProcessing() {
        return this == PROCESSING;
    }

} 