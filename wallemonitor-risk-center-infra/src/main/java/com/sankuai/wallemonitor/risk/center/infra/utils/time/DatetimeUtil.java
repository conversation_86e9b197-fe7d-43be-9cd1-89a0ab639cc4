package com.sankuai.wallemonitor.risk.center.infra.utils.time;

import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;


/**
 * 时间工具
 */
public class DatetimeUtil {


    public static final Date ZERO_DATE = convertDatetimeStr2Date("1970-01-01 08:00:01");

    public static final Integer START = 0;

    public static final Integer MIN_END = 59;

    public static final Integer SECOND_END = 59;

    /**
     * 将 Long 类型的时间转化为 Date 类型
     *
     * @param timestamp
     * @return
     */
    public static Date toDate(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return new Date(timestamp);
    }

    /**
     * 将 Date 类型的时间转化为形如 "yyyy-MM-dd HH:mm:ss" 的字符串
     *
     * @param time
     * @return
     */
    public static String formatTime(Date time) {
        SimpleDateFormat f = new SimpleDateFormat(DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND);
        return time != null ? f.format(time) : null;
    }

    /**
     * 将 Date 类型的时间转化为形如 "yyyy-MM-dd HH:mm:ss" 的字符串 对时间进行四舍五入
     *
     * @param time
     * @return
     */
    public static String formatTimeWithRounding(Date time) {
        if (time == null) {
            return null;
        }

        // 将毫秒四舍五入到最近的秒
        long timeInMillis = time.getTime();
        long roundedTimeInMillis = Math.round(timeInMillis / 1000.0) * 1000;
        Date roundedTime = new Date(roundedTimeInMillis);

        SimpleDateFormat f = new SimpleDateFormat(DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND);
        return f.format(roundedTime);
    }

    /**
     * 将 Date 类型的时间转化为Date当天的年月日日期，形如 "yyyy-MM-dd" 的日期
     *
     * @param date
     * @return
     */
    @SneakyThrows
    public static Date getDateYMD(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = sdf.format(date);
        return sdf.parse(dateString);
    }

    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 将 Date 类型的时间转化为Date当天最早的零点日期，形如 "yyyy-MM-dd 00:00:00"
     *
     * @param date
     * @return
     */
    @SneakyThrows
    public static Date getZeroDate(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDateTime localDateTime = LocalDateTime.of(localDate, LocalTime.MIN);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    @SneakyThrows
    public static Date getTodayStartDate() {
        return getZeroDate(new Date());
    }

    @SneakyThrows
    public static Date getTodayLatestDate() {
        return getLatestDate(new Date());
    }

    public static Date localDateToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }


    /**
     * 将 Date 类型的时间转化为Date当天最晚日期，形如 "yyyy-MM-dd 23:59:59"
     *
     * @param date
     * @return
     */
    @SneakyThrows
    public static Date getLatestDate(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDateTime localDateTime = LocalDateTime.of(localDate, LocalTime.MAX);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取指定时间某几个时间单位之前的时间爱你
     *
     * @param date
     * @param timeUnit
     * @param num
     * @return
     */
    public static Date getBeforeTime(Date date, TimeUnit timeUnit, Integer num) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        switch (timeUnit) {
            case DAYS:
                localDateTime = localDateTime.minusDays(num);
                break;
            case HOURS:
                localDateTime = localDateTime.minusHours(num);
                break;
            case MINUTES:
                localDateTime = localDateTime.minusMinutes(num);
                break;
            case SECONDS:
                localDateTime = localDateTime.minusSeconds(num);
                break;
            case MILLISECONDS:
                localDateTime = localDateTime.minusNanos(num * 1000000L);
                break;
            case MICROSECONDS:
                localDateTime = localDateTime.minusNanos(num * 1000L);
                break;
            default:
                throw new IllegalArgumentException("不支持的时间单位");
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取指定时间某几个时间单位之后的时间
     *
     * @param date
     * @param timeUnit
     * @param num
     * @return
     */
    public static Date getAfterTime(Date date, TimeUnit timeUnit, Integer num) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        switch (timeUnit) {
            case DAYS:
                localDateTime = localDateTime.plusDays(num);
                break;
            case HOURS:
                localDateTime = localDateTime.plusHours(num);
                break;
            case MINUTES:
                localDateTime = localDateTime.plusMinutes(num);
                break;
            case SECONDS:
                localDateTime = localDateTime.plusSeconds(num);
                break;
            case MILLISECONDS:
                localDateTime = localDateTime.plusNanos(num * 1000000L);
                break;
            case MICROSECONDS:
                localDateTime = localDateTime.plusNanos(num * 1000L);
                break;
            default:
                throw new IllegalArgumentException("不支持的时间单位");
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }


    /**
     * 将不同时间单位的时间戳转化为毫秒时间戳
     *
     * @param time 时间戳字符串
     * @return 毫秒时间戳
     */
    public static Long getMicoTimeStampFromTime(String time) {
        if (StringUtils.isBlank(time)) {
            return null;
        }
        int length = time.length();
        switch (length) {
            case 10:
                return TimeUnit.SECONDS.toMillis(Long.parseLong(time));
            case 13:
                return Long.parseLong(time); // 已经是毫秒
            case 16:
                return Long.parseLong(time) / 1000; // 微秒转毫秒
            case 19:
                return Long.parseLong(time.substring(0, 13)); // 取前13位作为毫秒
            default:
                return null;
        }
    }

    /**
     * 判断是否是数据库原点
     *
     * @param time
     * @return
     */
    public static boolean isDbZeroTime(Date time) {
        return ZERO_DATE.equals(time);
    }

    @SneakyThrows
    public static Date convertDatetimeStr2Date(String dateTimeStr) {
        return parseDate(dateTimeStr, DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND);
    }

    @SneakyThrows
    public static Date convertTimestamp2Date(Long timestamp) {
        return timestamp != null ? new Date(timestamp) : null;
    }

    @SneakyThrows
    public static String convertTimestamp2DateString(Long timestamp) {
        return timestamp != null ? formatTime(new Date(timestamp)) : null;
    }

    @SneakyThrows
    public static Date parseDate(String time, String dateTimeTemplate) {
        SimpleDateFormat f = new SimpleDateFormat(dateTimeTemplate);
        return time != null ? f.parse(time) : null;
    }

    @SneakyThrows
    public static Long getTimeStamp(Date date) {

        return date != null ? date.getTime() : ZERO_DATE.getTime();
    }

    @SneakyThrows
    public static String formatDate(Date time, String dateTimeTemplate) {
        SimpleDateFormat f = new SimpleDateFormat(dateTimeTemplate);
        return time != null ? f.format(time) : null;
    }

    public static boolean isSameDay(Date date1, Date date2) {
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate1.isEqual(localDate2);
    }

    /**
     * 给指定的日期增加指定的秒数
     *
     * @param date    日期
     * @param seconds 秒
     * @return
     */
    public static Long addSeconds(Date date, Integer seconds) {
        if (null == date) {
            return null;
        }
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime newDateTime = localDateTime.plusSeconds(seconds);
        return newDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取今天的日期字符串
     *
     * @return yyyy-MM-dd
     */
    public static String getTodayLocalDateStr() {
        return LocalDate.now().toString();
    }

    /**
     * 给指定的日期增加指定的分钟数
     *
     * @param date    日期
     * @param minutes 分钟
     * @return
     */
    public static long addMinutes(Date date, int minutes) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime newDateTime = localDateTime.plusMinutes(minutes);
        return newDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 将 Date 类型转换为 LocalDateTime 类型(使用系统时区)
     *
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        return instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 计算两个日期之间相差秒数
     *
     * @param smallDateTime
     * @param bigDateTime
     * @return 相差秒数
     */
    public static int getSecondsDiff(LocalDateTime smallDateTime, LocalDateTime bigDateTime) {
        return (int) smallDateTime.until(bigDateTime, ChronoUnit.SECONDS);
    }

    /**
     * 计算两个日期之间相差时间
     *
     * @param smallDateTime
     * @param bigDateTime
     * @param unit
     * @return
     */
    public static long getTimeDiff(Date smallDateTime, Date bigDateTime, ChronoUnit unit) {
        LocalDateTime smallDateTimeLDT = LocalDateTime.ofInstant(smallDateTime.toInstant(), ZoneId.systemDefault());
        LocalDateTime bigDateTimeLDT = LocalDateTime.ofInstant(bigDateTime.toInstant(), ZoneId.systemDefault());

        long diff = smallDateTimeLDT.until(bigDateTimeLDT, unit);
        return Math.abs(diff); // 使用绝对值，确保结果始终为正
    }

    /**
     * 获取今天的时间范围 yyyy-MM-dd 00:00:00 - yyyy-MM-dd 23:59:59
     *
     * @return
     */
    public static TimePeriod getTodayTimePeriod() {
        return TimePeriod.builder()
                .endDate(DatetimeUtil.getTodayLatestDate())
                .beginDate(DatetimeUtil.getTodayStartDate())
                .build();
    }

    /**
     * 将秒数转换为相应的时间格式。
     *
     * @param seconds 输入的秒数，可以为null。
     * @return 转换后的时间字符串，异常情况返回空字符串。
     */
    public static String formatSeconds(Integer seconds) {
        // 检查输入是否为空或小于0
        if (seconds == null || seconds < 0) {
            return "";
        }

        // 不满60秒，直接返回秒
        if (seconds < 60) {
            return seconds + "秒";
        }

        // 满60秒，转换为分和秒
        int minutes = seconds / 60;
        int remainingSeconds = seconds % 60;

        // 构建并返回结果字符串
        if (remainingSeconds > 0) {
            return minutes + "分" + remainingSeconds + "秒";
        } else {
            return minutes + "分";
        }
    }

    /**
     * 根据传入日期获取N秒之后的日期
     *
     * @param date         日期
     * @param secondsAfter 秒数
     * @return
     */
    public static Date getNSecondsAfterDateTime(Date date, Integer secondsAfter) {
        LocalDateTime localDateTime = dateToLocalDateTime(date);
        return localDateToDate(localDateTime.plusSeconds(secondsAfter));
    }

    /**
     * 根据传入日期获取N秒之后的日期
     *
     * @param secondsAfter 秒数
     * @return
     */
    public static Date getNSecondsAfterDateTime(Integer secondsAfter) {
        LocalDateTime localDateTime = dateToLocalDateTime(getNowDate());
        return localDateToDate(localDateTime.plusSeconds(secondsAfter));
    }

    /**
     * 根据传入日期获取N秒之前的日期
     *
     * @param date          日期
     * @param secondsBefore 秒数
     * @return
     */
    public static Date getNSecondsBeforeDateTime(Date date, Integer secondsBefore) {
        LocalDateTime localDateTime = dateToLocalDateTime(date);
        return localDateToDate(localDateTime.minusSeconds(secondsBefore));
    }

    /**
     * 根据传入日期获取N秒之前的日期
     *
     * @param secondsBefore 秒数
     * @return
     */
    public static Date getNSecondsBeforeDateTime(Integer secondsBefore) {
        LocalDateTime localDateTime = dateToLocalDateTime(getNowDate());
        return localDateToDate(localDateTime.minusSeconds(secondsBefore));
    }

    /**
     * 时间1在时间2之后的毫秒数
     *
     * @param time1
     * @param time2
     * @return
     */
    public static long diff(Date time1, Date time2) {
        return time1.getTime() - time2.getTime();
    }

    /**
     * 判断时间1是否在时间2之前
     *
     * @param time1
     * @param time2
     * @return
     */
    public static boolean isBefore(Date time1, Date time2) {
        return dateToLocalDateTime(time1).isBefore(dateToLocalDateTime(time2));
    }

    /**
     * 判断时间1是否在时间2之后
     *
     * @param time1
     * @param time2
     * @return
     */
    public static boolean isAfter(Date time1, Date time2) {
        return dateToLocalDateTime(time1).isAfter(dateToLocalDateTime(time2));
    }

    /**
     * 获取当前小时开始时间，包含0
     *
     * @param time
     * @return
     */
    public static Date getThisHourStart(Date time) {
        LocalDateTime localDateTime = time.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime startOfHour = localDateTime.withMinute(START).withSecond(START).withNano(START);
        return Date.from(startOfHour.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当前小时结束时间，包含59不包含下一小时的0
     *
     * @param time
     * @return
     */
    public static Date getThisHourEnd(Date time) {
        LocalDateTime localDateTime = time.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endOfHour = localDateTime.withMinute(MIN_END).withSecond(SECOND_END);
        return Date.from(endOfHour.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取时间对应的前一个小时开始，包含0
     *
     * @param time
     * @return
     */
    public static Date getPreHourStart(Date time) {
        LocalDateTime localDateTime = time.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime startOfPreHour = localDateTime.minusHours(1).withMinute(START).withSecond(START)
                .withNano(START);
        return Date.from(startOfPreHour.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取时间对应的前一个小时结束，包含59不包含下一小时的0
     *
     * @param time
     * @return
     */
    public static Date getPreHourEnd(Date time) {
        LocalDateTime localDateTime = time.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endOfPreHour = localDateTime.minusHours(1).withMinute(MIN_END).withSecond(SECOND_END);
        return Date.from(endOfPreHour.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获得短时间,比如2024-06-01 00:00:00，返回11:11
     *
     * @param createTimeEnd
     * @return
     */
    public static String getShortTime(Date createTimeEnd) {
        SimpleDateFormat format = new SimpleDateFormat(DateTimeTemplateConstant.HOUR_AND_MINUTE);
        return createTimeEnd == null ? CharConstant.CHAR_EMPTY : format.format(createTimeEnd);
    }

    /**
     * 获取持续短时间，用endTime - startTime，转换为 XX分XX秒
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static String getDurationTime(Date startTime, Date endTime) {
        // 检查入参合法性
        if (startTime == null || endTime == null || endTime.before(startTime)) {
            return CharConstant.CHAR_EMPTY;
        }
        // 计算开始时间和结束时间之间的差值，单位为毫秒
        long durationInMillis = endTime.getTime() - startTime.getTime();

        // 将毫秒转换为分钟
        long minutes = TimeUnit.MILLISECONDS.toMinutes(durationInMillis);

        // 计算剩余的秒数
        long seconds = TimeUnit.MILLISECONDS.toSeconds(durationInMillis) - TimeUnit.MINUTES.toSeconds(minutes);

        // 根据持续时间是否足够一分钟来构建返回的字符串
        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }

    public static String getDurationTime(Long seconds) {
        // 检查入参合法性
        if (seconds == null) {
            return CharConstant.CHAR_EMPTY;
        }

        // 将秒转换为分钟
        long minutes = TimeUnit.SECONDS.toMinutes(seconds);

        // 计算剩余的秒数
        seconds = TimeUnit.SECONDS.toSeconds(seconds) - TimeUnit.MINUTES.toSeconds(minutes);

        // 根据持续时间是否足够一分钟来构建返回的字符串
        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }

    /**
     * 获取秒级时间戳
     *
     * @param time
     * @return
     */
    public static Long getTimeInSeconds(Date time) {
        return time.getTime() / 1000;
    }


    /**
     * DateTime -> dateKey 2025-06-01 12:00:01 -> 20250601120001
     * */
    public static String getDateKey(Date date) {
        return Objects.requireNonNull(formatDate(date, "yyyy-MM-dd HH:mm:ss"))
                .replace(" ", "")
                .replace("-", "").replace(":", "");
    }
}
