package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 泊车失败事件与泊位ID关联查询参数DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskCaseParkingPlotRelationDOQueryParamDTO {

    /**
     * 风险事件ID
     */
    private String caseId;

    /**
     * 风险事件ID列表
     */
    @InQuery(field = "caseId")
    private List<String> caseIdList;

    /**
     * 泊位ID
     */
    private String parkingPlotId;

    /**
     * 泊位ID列表
     */
    @InQuery(field = "parkingPlotId")
    private List<String> parkingPlotIdList;

    /**
     * 创建时间范围
     */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    /**
     * 更新时间范围
     */
    @RangeQuery(field = "updateTime")
    private TimePeriod updateTimeRange;

    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean isDeleted = false;

    /**
     * 按创建时间排序
     */
    @OrderBy(field = "createTime")
    private OrderEnum orderByCreateTime;

    /**
     * 按更新时间排序
     */
    @OrderBy(field = "updateTime")
    private OrderEnum orderByUpdateTime;
} 