package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeTemplateConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大象告警展示图片类型
 *
 * <AUTHOR>
 * @date 2025/6/16
 */
@Getter
@AllArgsConstructor
public enum AlertImageTypeEnum {

    ROUTE("route", "当前路由线", "https://walle.meituan.com/replay/map/image/route?vin=%s"),
    CAMERA("camera", "摄像头",
            "https://walle.meituan.com/replay/video/occurTime?vin=%s&view=front&occurTime=%s");

    private final String type;
    private final String desc;
    private final String template;

    public static AlertImageTypeEnum getByType(String type) {
        for (AlertImageTypeEnum alertImageTypeEnum : AlertImageTypeEnum.values()) {
            if (alertImageTypeEnum.getType().equals(type)) {
                return alertImageTypeEnum;
            }
        }
        return null;
    }

    public String getUrl(String vin, Date occurTime) {
        switch (this) {
            case ROUTE:
                return String.format(template, vin);
            case CAMERA:
                return String.format(template, vin,
                        DatetimeUtil.formatDate(occurTime, DateTimeTemplateConstant.YYYYMMDDHHMMSS));
        }
        return null;
    }
}
