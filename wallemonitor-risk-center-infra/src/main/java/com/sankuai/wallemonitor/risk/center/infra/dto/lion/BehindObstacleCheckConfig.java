package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class BehindObstacleCheckConfig {

    private boolean enable;

    /**
     * 关注类型
     */
    private List<String> fineTypeList;

    /**
     * 角度阈值
     */
    private Double angle;

    /**
     * 距离阈值
     */
    private Double distance;

}
