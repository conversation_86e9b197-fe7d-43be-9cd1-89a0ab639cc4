package com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector;

import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.entity.RiskErrorWaitCrossWalkRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 错误让行检测记录工厂类
 */
@Component
public class RiskErrorWaitCrossWalkRecordFactory extends RiskDetectorRecordFactory<RiskErrorWaitCrossWalkRecordDO> {

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Override
    public RiskErrorWaitCrossWalkRecordDO init(DetectContextDTO detectContextDTO) {
        VehicleRuntimeInfoContextDO runtimeContextDO = detectContextDTO.toShortVehicleInfoSnapShot();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, runtimeContextDO.getVin(),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.ERROR_WAIT_CROSS_WALK,
                runtimeContextDO.getLastUpdateTime());

        return RiskErrorWaitCrossWalkRecordDO.builder().tmpCaseId(caseId).type(RiskCaseTypeEnum.ERROR_WAIT_CROSS_WALK)
                .vin(runtimeContextDO.getVin()).status(DetectRecordStatusEnum.PROCESSING)
                .vehicleRuntimeInfoSnapshot(runtimeContextDO).duration(0)
                .walkerStayInCrossWalkCount(detectContextDTO.get("walkerStayInCrossWalkCount", Integer.class))
                .occurTime(runtimeContextDO.getLastUpdateTime()).recallTime(DatetimeUtil.ZERO_DATE)
                .closeTime(DatetimeUtil.ZERO_DATE).extInfo(null)
                .isDeleted(IsDeleteEnum.NOT_DELETED).build();
    }
}