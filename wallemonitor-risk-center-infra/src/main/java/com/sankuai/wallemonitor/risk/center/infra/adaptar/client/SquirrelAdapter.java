package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;


import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisDefaultClient;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.SquirrelCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteSquirrelInvalidJsonDataException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CacheHashDO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 缓存抽象类
 */
@Component
@Slf4j
public class SquirrelAdapter {


    /**
     * 注入全部的store
     */
    @Resource
    private List<RedisStoreClient> redisStoreClients;

    /**
     * 缓存替换
     */
    private HashMap<String, String> clusterReplace;


    /**
     * 把不同集群的放在这个map里面
     */
    private Map<String, RedisStoreClient> redisStoreClientMap;


    @PostConstruct
    public void init() {
        this.redisStoreClientMap = redisStoreClients.stream()
                .collect(Collectors.toMap(redisStoreClient -> ((RedisDefaultClient) redisStoreClient).getClusterName(),
                        Function.identity()));
    }

    /**
     * 获取对象
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public Object get(SquirrelCategoryEnum categoryEnum, String key) {
        return getClient(categoryEnum).get(buildKey(categoryEnum, key));
    }

    /**
     * 获取对象
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public Object getSet(SquirrelCategoryEnum categoryEnum, String key, Object value, Integer defaultValue) {
        return getClient(categoryEnum).getSet(buildKey(categoryEnum, key), value,
                Optional.ofNullable(defaultValue).orElse(categoryEnum.getDefaultTimeOut()));
    }

    /**
     * 原子化设置
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public Boolean setnx(SquirrelCategoryEnum categoryEnum, String key, Object value, Integer defaultValue) {
        return getClient(categoryEnum).setnx(buildKey(categoryEnum, key), value,
                Optional.ofNullable(defaultValue).orElse(categoryEnum.getDefaultTimeOut()));
    }


    /**
     * 是否存在
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public Boolean exists(SquirrelCategoryEnum categoryEnum, String key) {
        return getClient(categoryEnum).exists(buildKey(categoryEnum, key));
    }

    /**
     * delete
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public Boolean delete(SquirrelCategoryEnum categoryEnum, String key) {
        return getClient(categoryEnum).delete(buildKey(categoryEnum, key));
    }

    /**
     * delete
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public Long multiDelete(SquirrelCategoryEnum categoryEnum, List<String> key) {
        return getClient(categoryEnum).multiDelete(buildKey(categoryEnum, key));
    }

    /**
     * 失效时间
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public Long ttl(SquirrelCategoryEnum categoryEnum, String key) {
        return getClient(categoryEnum).ttl(buildKey(categoryEnum, key));
    }

    /**
     * 获取对象
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public Map<String, Object> multiGet(SquirrelCategoryEnum categoryEnum, List<String> key) {
        Map<StoreKey, Object> storeKeyObjectMap = getClient(categoryEnum).multiGet(buildKey(categoryEnum, key));
        Map<String, Object> resultMap = new HashMap<>();
        storeKeyObjectMap.forEach((storeKey, obj) -> {
            resultMap.put(Arrays.stream(storeKey.getParams()).findFirst().map(Object::toString).get(), obj);
        });
        return resultMap;
    }

    /**
     * 获取对象
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public Map<String, Object> multiGet(SquirrelCategoryEnum categoryEnum, List<String> key, long timeout) {
        Map<StoreKey, Object> storeKeyObjectMap = getClient(categoryEnum).multiGet(buildKey(categoryEnum, key),
                timeout);
        Map<String, Object> resultMap = new HashMap<>();
        storeKeyObjectMap.forEach((storeKey, obj) -> {
            resultMap.put(Arrays.stream(storeKey.getParams()).findFirst().map(Object::toString).get(), obj);
        });
        return resultMap;
    }

    /**
     * 获取缓存key list
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    private List<StoreKey> buildKey(SquirrelCategoryEnum categoryEnum, List<String> key) {
        return key.stream().map(k -> buildKey(categoryEnum, k)).collect(Collectors.toList());
    }

    /**
     * 获取客户端
     *
     * @param categoryEnum
     * @return
     */
    private RedisStoreClient getClient(SquirrelCategoryEnum categoryEnum) {
        return redisStoreClientMap.get(SquirrelCategoryEnum.getEnvCluster(categoryEnum.getCluster()));
    }

    /**
     * 生成key
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    private StoreKey buildKey(SquirrelCategoryEnum categoryEnum, String key) {
        return new StoreKey(categoryEnum.getCategory(), key);


    }

    /**
     * 设置对象
     *
     * @param categoryEnum
     * @param key
     * @param expireTime
     * @return
     */
    public void set(SquirrelCategoryEnum categoryEnum, String key, Object value, Integer expireTime) {
        getClient(categoryEnum).set(buildKey(categoryEnum, key), value,
                Optional.ofNullable(expireTime).orElse(categoryEnum.getDefaultTimeOut()));
    }

    /**
     * 设置对象
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public void hsetStr(SquirrelCategoryEnum categoryEnum, String key, String field, Object value) {
        getClient(categoryEnum).hset(buildKey(categoryEnum, key), field, JacksonUtils.to(value));
    }

    /**
     * 设置对象
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public <T> T hgetStrAndDeserialization(SquirrelCategoryEnum categoryEnum, String key, String field,
            Class<T> tClass) {
        String objectStr = getClient(categoryEnum).hget(buildKey(categoryEnum, key), field);
        if (StringUtils.isBlank(objectStr)) {
            return null;
        }
        try {
            return JacksonUtils.from(objectStr, tClass);
        } catch (Exception e) {
            throw new RemoteSquirrelInvalidJsonDataException(
                    String.format("squirrel结果解析json失败, 结果:%s", objectStr));
        }
    }

    public <T> T hgetStrAndDeserialization(SquirrelCategoryEnum categoryEnum, String key, String field,
            TypeReference<T> tClass) {
        String objectStr = getClient(categoryEnum).hget(buildKey(categoryEnum, key), field);
        if (StringUtils.isBlank(objectStr)) {
            return null;
        }
        try {
            return JacksonUtils.from(objectStr, tClass);
        } catch (Exception e) {
            throw new RemoteSquirrelInvalidJsonDataException(
                    String.format("squirrel结果解析json失败, 结果:%s", objectStr));
        }
    }

    public <T> T hgetObject(SquirrelCategoryEnum categoryEnum, String key, String field) {
        return getClient(categoryEnum).hget(buildKey(categoryEnum, key), field);
    }

    /**
     * 设置对象
     *
     * @param categoryEnum
     * @param key
     * @return
     */
    public void hsetObject(SquirrelCategoryEnum categoryEnum, String key, String field, Object value) {
        getClient(categoryEnum).hset(buildKey(categoryEnum, key), field, value);
    }


    /**
     * 批量设置
     *
     * @param categoryEnum
     * @param valueMap
     * @param expireTime
     * @return
     */
    public void multiSetObjectAsStr(SquirrelCategoryEnum categoryEnum, Map<String, Object> valueMap,
            Integer expireTime) {
        valueMap.forEach((key, obj) -> {
            saveObjectAsStr(categoryEnum, key, obj, expireTime);
        });
    }


    /**
     * RPOP 命令的阻塞版本，当给定列表内没有任何元素可供弹出的时候，连接将被 BRPOP 命令阻塞，直到等待超时或发现可弹出元素为止
     *
     * @param categoryEnum     分类
     * @param key              key
     * @param timeoutInSeconds 等待超时时间, 单位 秒 . (最大可设置等待时长 5 分钟)
     * @return 如果队列有元素, 则返回列表的尾元素, 当 key 不存在时并且等待超过 timeoutInSeconds 返回 null
     */
    public <T> T brpop(SquirrelCategoryEnum categoryEnum, String key, int timeoutInSeconds) {
        return getClient(categoryEnum).brpop(buildKey(categoryEnum, key), timeoutInSeconds);
    }


    /**
     * 将一个或多个值 value 插入到列表 key 的表尾(最右边)。如果有多个 value 值， 那么各个 value 值按从左到右的顺序依次插入到表尾： 比如对一个空列表 mylist 执行 RPUSH mylist a b
     * c,得出的结果列表为 a b c 等同于执行命令 RPUSH mylist a 、 RPUSH mylist b 、 RPUSH mylist c 。 如果 key 不存在，一个空列表会被创建并执行 RPUSH 操作。当key
     * 存在但不是列表类型时，返回一个错误。
     *
     * @param categoryEnum 分类
     * @param key          key
     * @param value        value
     * @return 执行 RPUSH 操作后，表的长度
     */
    public <T> Long rpush(SquirrelCategoryEnum categoryEnum, String key, Object... value) {
        return getClient(categoryEnum).rpush(buildKey(categoryEnum, key), value);
    }

    /**
     * 带有过期时间的自减操作
     *
     * @param categoryEnum    分类
     * @param key             key
     * @param amount          要减少的值
     * @param expireInSeconds 初始化 key 的过期时间
     * @return 减少后的值，如果 Key 不存在，会创建这个 Key : 值= -amount，返回值 -amount,过期时间为expireInSeconds
     */
    public Long decrBy(SquirrelCategoryEnum categoryEnum, String key, long amount, int expireInSeconds) {
        return getClient(categoryEnum).decrBy(buildKey(categoryEnum, key), amount, expireInSeconds);
    }

    /**
     * 带有过期时间的incr操作
     *
     * @param key             key
     * @param amount          要增加的值
     * @param expireInSeconds 初始化 key 的过期时间
     * @return 增长后 key 的值,如果 Key 不存在，会创建这个 Key : 值=amount ,过期时间为 expireInSeconds
     */
    public Long incrBy(SquirrelCategoryEnum categoryEnum, String key, long amount, int expireInSeconds) {
        return getClient(categoryEnum).incrBy(buildKey(categoryEnum, key), amount, expireInSeconds);
    }

    /**
     * 如果当前key 存在并且 value 与 expect 相同,则删除该key. 该操作为原子操作
     *
     * @param categoryEnum 分类
     * @param key          key
     * @param expect       期望的value
     * @return true 成功， false 失败（当前value 与 expect不相同）
     */
    public Boolean compareAndDelete(SquirrelCategoryEnum categoryEnum, String key, Object expect) {
        return getClient(categoryEnum).compareAndDelete(buildKey(categoryEnum, key), expect);
    }

    /**
     * 将对象按照hash的方式更新，
     *
     * @param categoryEnum
     * @param key
     */
    public void saveHash(SquirrelCategoryEnum categoryEnum, String key,
            CacheHashDO cacheHashDO) {
        Map<String, Object> fieldMap = cacheHashDO.getData();
        if (MapUtils.isEmpty(fieldMap)) {
            return;
        }
        Map<String, Object> fieldUpdateMap = cacheHashDO.getData();
        Map<String, Long> fieldUpdateTimeMap = cacheHashDO.getUpdateTime();
        //取最新的
        CacheHashDO context = getHash(categoryEnum, key, new ArrayList<>(fieldMap.keySet()));
        CacheHashDO finalUpdateCache = CacheHashDO.builder().build();
        if (context != null) {
            Map<String, Long> inCacheTime = cacheHashDO.getUpdateTime();
            fieldUpdateMap.forEach((field, value) -> {
                //每个变更的，和缓存里面最新的比较，如果缓存的时间戳存在，
                Long thisUpdateTime = fieldUpdateTimeMap.get(field);
                if (thisUpdateTime == null) {
                    return;
                }
                Long timeUpdate = inCacheTime.get(field);
                if (timeUpdate == null || thisUpdateTime >= timeUpdate) {
                    //发生了变更
                    finalUpdateCache.addField(field, value, thisUpdateTime);
                }
            });
        }
        try {
            if (MapUtils.isNotEmpty(finalUpdateCache.getAllField())) {
                //单字段更新
                hsetAll(categoryEnum, key, finalUpdateCache.getAllField());
            }
        } catch (Exception e) {
            log.error("saveAsHash error, key:{}, context:{}", key, context, e);
        }
    }

    private void hsetAll(SquirrelCategoryEnum categoryEnum, String key, Map<String, Object> fieldMap) {
        //空值
        Set<String> nullKeySet = new HashSet<>();
        Map<String, Object> nonNullValueMap = new HashMap<>();
        fieldMap.forEach((field, value) -> {
            if (value == null) {
                nullKeySet.add(field);
            } else {
                nonNullValueMap.put(field, value);
            }
        });

        try {
            if (CollectionUtils.isNotEmpty(nullKeySet)) {
                getClient(categoryEnum).hdel(buildKey(categoryEnum, key), nullKeySet.toArray(new String[0]));
            }
            if (MapUtils.isNotEmpty(nonNullValueMap)) {
                getClient(categoryEnum).hmset(buildKey(categoryEnum, key), nonNullValueMap);
            }
        } catch (Exception e) {
            log.error("Error updating Redis hash for key: {}", key, e);
        }

    }


    public CacheHashDO getHash(SquirrelCategoryEnum categoryEnum, String key, List<String> fieldNameList) {
        //获取字段
        List<String> fieldUpdateTimeKey = fieldNameList.stream().map(thisFieldName -> thisFieldName + "Timestamp")
                .collect(Collectors.toList());
        Map<String, Object> fieldObjectMap = hgetAll(categoryEnum, key, fieldNameList);
        Map<String, Long> fieldUpdateMap = hgetAll(categoryEnum, key, fieldUpdateTimeKey);
        //设置值
        return CacheHashDO.builder().key(key).updateTime(fieldUpdateMap).data(fieldObjectMap).build();
    }

    public <T> Map<String, CacheHashDO> batchGetHashAsObject(SquirrelCategoryEnum categoryEnum, List<String> key,
            List<String> fieldList) {
        //获取字段
        return key.stream().map(thisKey -> {
            return getHash(categoryEnum, thisKey, fieldList);
        }).collect(Collectors.toMap(CacheHashDO::getKey, Function.identity(), (o1, o2) -> o1));

    }

    /**
     * 获取key里面全部的field
     *
     * @param categoryEnum
     * @param key
     * @param keyField
     * @param <T>
     * @return
     */
    private <T> Map<String, T> hgetAll(SquirrelCategoryEnum categoryEnum, String key, List<String> keyField) {
        return getClient(categoryEnum).hmget(buildKey(categoryEnum, key), keyField);
    }

    public Map<String, Long> hAllGet(SquirrelCategoryEnum categoryEnum, String key) {
        return getClient(categoryEnum).hgetAll(buildKey(categoryEnum, key));
    }

    /**
     * 获取的缓存str转换为Object
     * 
     * @param categoryEnum
     * @param key
     * @return
     * @param <T>
     */
    public <T> T getStrAsObject(SquirrelCategoryEnum categoryEnum, String key, Class<T> tClass) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        String str = getClient(categoryEnum).get(buildKey(categoryEnum, key));
        return JacksonUtils.from(str, tClass);

    }

    /**
     * 保存对象作为Str
     * 
     * @param categoryEnum
     * @param key
     * @param value
     * @param expireTime
     */
    public void saveObjectAsStr(SquirrelCategoryEnum categoryEnum, String key, Object value, Integer expireTime) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        String str = JacksonUtils.to(value);
        getClient(categoryEnum).set(buildKey(categoryEnum, key), str, expireTime);
    }
}
