package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import java.util.HashSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCheckCategoryConfigDTO {

    @Builder.Default
    private Set<String> riskCategory = new HashSet<>();
    @Builder.Default
    private Set<String> noRiskCategory = new HashSet<>();
    @Builder.Default
    private Set<String> reCheckCategory = new HashSet<>();

    public boolean isConfirmedSubcategory(ISCheckCategoryEnum isCheckCategoryEnum) {
        if (CollectionUtils.isEmpty(riskCategory)) {
            return false;
        }
        return riskCategory.contains(isCheckCategoryEnum.name()) || riskCategory.contains(CommonConstant.ALL)
                && ISCheckCategoryEnum.getGoodCategoryNameList().contains(isCheckCategoryEnum.name());
    }

    public boolean isCanceledSubCategory(ISCheckCategoryEnum isCheckCategoryEnum) {
        if (CollectionUtils.isEmpty(noRiskCategory)) {
            return false;
        }
        return noRiskCategory.contains(isCheckCategoryEnum.name()) || noRiskCategory.contains(CommonConstant.ALL)
                && ISCheckCategoryEnum.getBadCategoryNameList().contains(isCheckCategoryEnum.name());
    }

    public boolean isReCheckSubCategory(ISCheckCategoryEnum isCheckCategoryEnum) {
        if (CollectionUtils.isEmpty(reCheckCategory)) {
            return false;
        }
        return reCheckCategory.contains(isCheckCategoryEnum.name());
    }
}
