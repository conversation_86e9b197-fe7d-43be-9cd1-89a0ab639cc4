package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskErrorQueueRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskErrorQueueRecordDOQueryParamDTO;
import java.util.List;

/**
 * 错误排队检测记录仓储接口
 */
public interface RiskErrorQueueRecordRepository {

    /**
     * 根据参数查询错误排队检测记录
     *
     * @param paramDTO 查询参数
     * @return 错误排队检测记录DO列表
     */
    List<RiskErrorQueueRecordDO> queryByParam(RiskErrorQueueRecordDOQueryParamDTO paramDTO);

    /**
     * 根据参数分页查询错误排队检测记录
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 分页对象
     */
    Paging<RiskErrorQueueRecordDO> queryByParamByPage(RiskErrorQueueRecordDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize);

    /**
     * 根据临时事件ID查询错误排队检测记录
     *
     * @param tmpCaseId 临时事件ID
     * @return 错误排队检测记录DO
     */
    RiskErrorQueueRecordDO getByTmpCaseId(String tmpCaseId);

    /**
     * 保存错误排队检测记录
     *
     * @param riskErrorQueueRecordDO 错误排队检测记录DO
     */
    void save(RiskErrorQueueRecordDO riskErrorQueueRecordDO);

    /**
     * 批量保存错误排队检测记录
     *
     * @param riskErrorQueueRecordDOList 错误排队检测记录DO列表
     */
    void batchSave(List<RiskErrorQueueRecordDO> riskErrorQueueRecordDOList);
} 