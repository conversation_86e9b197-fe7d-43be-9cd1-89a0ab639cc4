package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CaseSortDataDOQueryParam {

    @InQuery(field = "caseId")
    private List<String> caseIdList;

    @InQuery(field = "problem")
    private List<String> problemList;

    @Builder.Default
    private Boolean isDeleted = false;

}
