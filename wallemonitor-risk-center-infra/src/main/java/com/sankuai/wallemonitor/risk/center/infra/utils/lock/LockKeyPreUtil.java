package com.sankuai.wallemonitor.risk.center.infra.utils.lock;


import com.google.common.collect.Sets;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 * @Date 2022/10/27
 */
public class LockKeyPreUtil {
    
    /**
     * 告警记录锁前缀
     */
    private static final String ALERT_RECORD_PRE = "alert-record-";

    /**
     * 风险事件ID
     */
    private static final String CASE_ID_PRE = "caseId-";

    /**
     * 风险计数前缀
     */
    private static final String CASE_COUNTER_PRE = "case-counter-";


    /**
     * 事件ID
     */
    private static final String EVENT_ID_PRE = "eventId-";

    /**
     * 动态标注的
     */
    private static final String MULTI_EVENT_ID_PRE = "multi-eventId-";

    /**
     * vin
     */
    private static final String VIN_PRE = "vin-";

    /**
     * vin
     */
    private static final String VIN_CONTEXT_PRE = "vin-ctx-";

    /**
     * 检测器上下文锁
     */
    private static final String RISK_DETECTOR_CONTEXT_PRE = "detector-%s-%s";

    /**
     * 类型key
     */
    private static final String VEHICLE_TYPE_KEY_TEMPLATE = "risk-type-key-%s-%s";


    /**
     * 统一前置
     */
    private static final String OPERATE_PRE = "operate-";
    /**
     * 位置更新
     */
    private static final String LOCATION_UPDATE_PRE = "location-update-";

    /**
     * 计时器更新
     */
    private static final String VIN_COUNTER_PRE = "vin-counter-";


    /**
     * 通过预检队列元素获取锁keys
     *
     * @param queueItemList
     * @return
     */
    public static Set<String> buildLockKeys(List<RiskCheckingQueueItemDO> queueItemList) {
        Set<String> vinSet = queueItemList.stream().map(RiskCheckingQueueItemDO::getVin).collect(Collectors.toSet());
        Set<String> eventIdSet = queueItemList.stream().map(RiskCheckingQueueItemDO::getEventId)
                .collect(Collectors.toSet());
        return buildEventIdAndVinAndType(eventIdSet, vinSet);
    }


    /**
     * 通过eventId构建加锁key
     *
     * @param eventIdSet
     * @return
     */
    public static Set<String> buildKeyWithEventId(Set<String> eventIdSet) {
        return batchBuildKeyWithPre(buildEventIdKey(eventIdSet));
    }

    /**
     * 通过eventId构建加锁key
     *
     * @param eventIdSet
     * @return
     */
    public static Set<String> buildMultiMarkWithEventId(Set<String> eventIdSet, String version) {
        return batchBuildKeyWithPre(buildMultiVersionEventIdKey(eventIdSet, version));
    }

    /**
     * 通过eventId构建加锁key
     *
     * @return
     */
    public static Set<String> buildMultiMarkWithEventId(String eventId, String version) {
        return batchBuildKeyWithPre(buildMultiVersionEventIdKey(Sets.newHashSet(eventId), version));
    }

    /**
     * 通过eventId构建加锁key
     *
     * @param eventIdArgs
     * @return
     */
    public static Set<String> buildKeyWithEventId(String... eventIdArgs) {
        Set<String> eventIdSet = Sets.newHashSet(eventIdArgs);
        return batchBuildKeyWithPre(buildEventIdKey(eventIdSet));
    }
    /**
     * 接受消息时的加锁依赖，待废弃，防止保障系统的链路和状态监控的消息链路不一致
     *
     * @param riskCaseTypeEnum
     * @param vinSet
     * @return
     */
    @Deprecated
    public static Set<String> buildVinAndType(Set<String> vinSet,
            RiskCaseTypeEnum riskCaseTypeEnum) {
        Set<String> typeKey = buildTypeKey(vinSet, riskCaseTypeEnum);
        Set<String> allKeyStr = Sets.newHashSet();
        allKeyStr.addAll(typeKey);
        SystemCheckUtil.isNotEmpty(allKeyStr, "批量加锁内容不能为空");
        return batchBuildKeyWithPre(allKeyStr);
    }

    public static String buildVinAndType(String vin,
            RiskCaseTypeEnum riskCaseTypeEnum) {
        return String.format(VEHICLE_TYPE_KEY_TEMPLATE, vin, riskCaseTypeEnum);
    }

    private static Set<String> buildTypeKey(Set<String> vinList, RiskCaseTypeEnum riskCaseTypeEnum) {
        if (CollectionUtils.isEmpty(vinList)) {
            return Collections.emptySet();
        }
        return vinList.stream().map(vin -> String.format(VEHICLE_TYPE_KEY_TEMPLATE, vin, riskCaseTypeEnum))
                .collect(Collectors.toSet());
    }

    public static Set<String> buildEventIdAndVin(
            Set<String> eventIdSet, Set<String> vinSet) {
        Set<String> eventIdKey = buildEventIdKey(eventIdSet);
        Set<String> vinIdSet = buildVinKey(vinSet);
        Set<String> allKeyStr = Sets.newHashSet();
        allKeyStr.addAll(eventIdKey);
        allKeyStr.addAll(vinIdSet);
        SystemCheckUtil.isNotEmpty(allKeyStr, "批量加锁内容不能为空");
        return batchBuildKeyWithPre(allKeyStr);
    }


    /**
     * 构建vin上下文锁
     *
     * @param vinSet
     * @return
     */
    public static Set<String> buildVinContextKey(
            Set<String> vinSet) {
        if (CollectionUtils.isEmpty(vinSet)) {
            return Collections.emptySet();
        }
        return batchBuildKeyWithPre(vinSet.stream().map(vin -> VIN_CONTEXT_PRE + vin).collect(Collectors.toSet()));
    }


    /**
     * 构建上下文锁
     *
     * @param vin
     * @param riskDetectorName
     * @return
     */
    public static String buildRiskDetectorManager(String vin, String riskDetectorName) {
        return String.format(RISK_DETECTOR_CONTEXT_PRE, vin, riskDetectorName);

    }

    /**
     * 构建事件ID和vin和事件类型的锁
     *
     * @param eventIdSet
     * @param vinSet
     * @return
     */
    public static Set<String> buildEventIdAndVinAndType(
            Set<String> eventIdSet, Set<String> vinSet) {
        Set<String> eventIdKey = buildEventIdKey(eventIdSet);
        Set<String> vinIdSet = buildVinKey(vinSet);
        Set<String> allKeyStr = Sets.newHashSet();
        allKeyStr.addAll(eventIdKey);
        allKeyStr.addAll(vinIdSet);
        SystemCheckUtil.isNotEmpty(allKeyStr, "批量加锁内容不能为空");
        return batchBuildKeyWithPre(allKeyStr);
    }

    private static Set<String> batchBuildKeyWithPre(Set<String> allKeyStr) {
        if (CollectionUtils.isEmpty(allKeyStr)) {
            return Collections.emptySet();
        }
        return allKeyStr.stream().map(key -> OPERATE_PRE + key).collect(Collectors.toSet());
    }

    private static Set<String> buildVinKey(Set<String> vinSet) {
        if (CollectionUtils.isEmpty(vinSet)) {
            return Collections.emptySet();
        }
        return vinSet.stream().map(vin -> VIN_PRE + vin).collect(Collectors.toSet());

    }

    private static Set<String> buildEventIdKey(Set<String> eventIdSet) {
        if (CollectionUtils.isEmpty(eventIdSet)) {
            return Collections.emptySet();
        }
        return eventIdSet.stream().map(eventId -> EVENT_ID_PRE + eventId).collect(Collectors.toSet());
    }

    private static Set<String> buildMultiVersionEventIdKey(Set<String> eventIdSet, String version) {
        if (CollectionUtils.isEmpty(eventIdSet)) {
            return Collections.emptySet();
        }
        return eventIdSet.stream().map(eventId -> MULTI_EVENT_ID_PRE + eventId + CharConstant.CHAR_HX + version)
                .collect(Collectors.toSet());
    }
    /**
     * 构建case计数器
     *
     * @param caseIdList
     * @return
     */
    public static Set<String> buildCaseCounter(Set<String> caseIdList) {
        return batchBuildKeyWithPre(
                caseIdList.stream().map(caseId -> CASE_COUNTER_PRE + caseId).collect(Collectors.toSet()));

    }

    /**
     * 构建车辆位置更新锁
     * 
     * @param vinSet
     * @return
     */
    public static Set<String> buildLocationUpdateKey(Collection<String> vinSet) {
        return batchBuildKeyWithPre(vinSet.stream().map(vin -> LOCATION_UPDATE_PRE + vin).collect(Collectors.toSet()));

    }

    public static Set<String> buildVinCounter(List<String> vinList) {
        return batchBuildKeyWithPre(vinList.stream().map(vin -> VIN_COUNTER_PRE + vin).collect(Collectors.toSet()));
    }

  

    /**
     * 构建告警记录锁键
     *
     * @param recordIds 记录ID列表
     * @return 锁键集合
     */
    public static Set<String> buildAlertRecordKeys(String... recordIds) {
        Set<String> recordIdSet = Sets.newHashSet(recordIds);
        return batchBuildKeyWithPre(recordIdSet.stream()
                .map(recordId -> ALERT_RECORD_PRE + recordId)
                .collect(Collectors.toSet()));
    }
}
