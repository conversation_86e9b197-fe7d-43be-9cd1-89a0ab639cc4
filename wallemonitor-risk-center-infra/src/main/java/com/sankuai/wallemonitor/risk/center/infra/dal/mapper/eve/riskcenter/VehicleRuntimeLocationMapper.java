package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.handler.PointTypeHandler;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.VehicleRuntimeLocation;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;

/**
 * <p>
 * 车辆实时定位数据 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2025-03-17
 */
public interface VehicleRuntimeLocationMapper extends CommonMapper<VehicleRuntimeLocation> {

    @Results({@Result(column = "location", property = "location", typeHandler = PointTypeHandler.class)})
    @SelectProvider(type = SqlProvider.class, method = "produceSelectList")

    /**
     * 根据参数构建查询语句
     *
     * @param params
     * @param clazz
     * @param extraParams
     * @return
     */
    @Override
    List<VehicleRuntimeLocation> selectList(@Param(PARAMS) Object params,
            @Param("clazz") Class<VehicleRuntimeLocation> clazz, @Param(EXT_PARAMS) Map<String, Object> extraParams);
}
