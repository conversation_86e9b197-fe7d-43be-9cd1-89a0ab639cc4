package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEventDetail;

public interface NegativePublicEventDetailMapper extends CommonMapper<NegativePublicEventDetail> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<NegativePublicEventDetail> getPOClass() {
        return NegativePublicEventDetail.class;
    }
}