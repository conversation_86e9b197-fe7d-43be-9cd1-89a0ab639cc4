package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;


import com.dianping.cat.Cat;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.BaAuthUtils;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.DataPlatformFastUploadRequest;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.DataPlatformFastUploadResponse;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.DataPlatformFastUploadResponse.FastUploadResp;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

/**
 * 数据平台接口封装
 */
@Slf4j
@Component
public class DataPlatformAdapter {

    private final static String FAST_UPLOAD_URL = "/fast_upload/add/ba";

    @Value("${eventPlatform.queryDomain}")
    private String dataPlatformDomain;

    /**
     * 事件平台BA鉴权clientId
     */
    @Value("${eventPlatform.clientId}")
    private String eventPlatformClientId;

    @Value("$KMS{eventPlatform.ba.secret}")
    private String BA_SECRET_KEY;

    /**
     * 数据平台快速回传自动驾驶数据接口
     *
     * @return
     */
    public String fastUploadAutoCarData(DataPlatformFastUploadRequest request) {
        CheckUtil.isNotBlank(request.getVin(), "vin不能为空");
        CheckUtil.isNotNull(request.getBegin(), "开始时间不能为空");
        CheckUtil.isNotNull(request.getEnd(), "结束时间不能为空");
        CheckUtil.isNotEmpty(request.getModules(), "待上传模块不能为空");

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("vin", request.getVin());
            params.put("start", DatetimeUtil.formatTime(request.getBegin()));
            params.put("end", DatetimeUtil.formatTime(request.getEnd()));
            params.put("module", String.join(CharConstant.CHAR_COMMA, request.getModules()));
            params.put("taskType", request.getTaskType());
            params.put("creator", request.getCreator());
            params.put("source", request.getSource());
            params.put("priority", request.getPriority());
            params.put("isCase", 0); // 是否为case 1为是，0为否，用户上传都默认为否
            params.put("isRecording", false); // 是否录屏，默认不录屏
            Map<String, String> headers = BaAuthUtils.genMWSAuthHeader(CommonConstant.HTTP_METHOD_POST,
                    FAST_UPLOAD_URL, eventPlatformClientId, BA_SECRET_KEY, "E, dd MMM yyyy HH:mm:ss z");
            String url = String.format("%s%s", dataPlatformDomain, FAST_UPLOAD_URL);
            String body = JacksonUtils.to(params);
            log.info("[RemoteCall] call data platform fastUploadAutoCarData, headers:{}, url:{}, body: {}", headers,
                    url, body);

            EveHttpResponse<DataPlatformFastUploadResponse> response = HttpUtils.postJson(body, url, headers,
                    DataPlatformFastUploadResponse.class);
            log.info("[RemoteCall] call data platform fastUploadAutoCarData, response：{}", JacksonUtils.to(response));
            if (response == null || response.getData() == null || response.getCode() != HttpStatus.OK.value()) {
                throw new SystemException("数据平台快速回传自动驾驶数据接口异常");
            }
            FastUploadResp innerResp = response.getData().getData();
            if (innerResp == null) {
                throw new SystemException("数据平台快速回传自动驾驶数据接口返回数据异常");
            }
            Cat.logEvent("DATA_PLATFORM_FAST_UPLOAD_AUTOCAR_DATA", request.getSource());
            return innerResp.getTaskId();
        } catch (Exception e) {
            log.error("[RemoteCall] call data platform fastUploadAutoCarData error", e);
            return null;
        }
    }
}
