package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Sets;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CanBusDriverModeEnum {
    COMPLETE_MANUAL(0, "complete_manual"),
    COMPLETE_AUTO_DRIVE(1, "complete_auto_drive"),
    AUTO_STEER_ONLY(2, "auto_steer_only"),
    AUTO_SPEED_ONLY(3, "auto_speed_only"),
    EMERGENCY_MODE(4, "emergency_mode"),
    FIELD_CONTROL_MODE(5, "field_control_mode"),
    REMOTE_CONTROL_MODE(6, "remote_control_mode"),
    MANUAL_DRIVER(7, "manual_driver");

    private final Integer code;
    private final String desc;

    /**
     * 是否是人为介入的模式
     *
     * @param driverModeEnum
     * @return
     */
    public static Boolean isManualDriverMode(CanBusDriverModeEnum driverModeEnum) {
        Set<CanBusDriverModeEnum> preDriverMode = Sets.newHashSet(REMOTE_CONTROL_MODE, FIELD_CONTROL_MODE);
        return preDriverMode.contains(driverModeEnum);
    }

    /**
     * 是否是无控制模式
     *
     * @param driverModeEnum
     * @return
     */
    public static Boolean isUncontrolledDriverMode(CanBusDriverModeEnum driverModeEnum) {
        Set<CanBusDriverModeEnum> preDriverMode = Sets.newHashSet(COMPLETE_MANUAL);
        return preDriverMode.contains(driverModeEnum);
    }
}
