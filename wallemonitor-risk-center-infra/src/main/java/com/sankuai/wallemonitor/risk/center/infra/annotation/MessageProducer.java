package com.sankuai.wallemonitor.risk.center.infra.annotation;

import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface MessageProducer {

    MessageTopicEnum topic();

    String appKey() default "";
}
