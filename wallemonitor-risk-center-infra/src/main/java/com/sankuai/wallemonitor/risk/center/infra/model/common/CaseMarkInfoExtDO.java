package com.sankuai.wallemonitor.risk.center.infra.model.common;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class CaseMarkInfoExtDO {

    /**
     * 补充说明
     */
    private String desc;

    /**
     * friday模型识别结果
     */
    private Map<String, Object> checkResult;

    private List<Map<String, Object>> checkResultList;


    /**
     * 检查轮次
     */
    private Integer checkRound;

    /**
     * 添加检测结果
     * 
     * @param checkResult
     */
    public void addCheckResult(Map<String, Object> checkResult) {
        this.checkResult = checkResult;
    }

    /**
     * 添加历史检查结果
     * 
     * @param historyCheckResult
     */
    public void addHistoryCheckResultList(List<Map<String, Object>> historyCheckResult) {
        if (Objects.isNull(checkResultList)) {
            checkResultList = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(historyCheckResult)) {
            return;
        }
        checkResultList.addAll(historyCheckResult);
    }
}
