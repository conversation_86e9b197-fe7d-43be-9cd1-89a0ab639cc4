package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventTypeEnum;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class NegativePublicEventDO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * eventId
     */
    private String eventId;

    /**
     * 事件类型
     */
    private NegativePublicEventTypeEnum type;

    /**
     * 状态
     */
    private NegativePublicEventStatusEnum status;

    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 上报人
     */
    private String reporter;

    /**
     * 群ID
     */
    private String groupId;

    /**
     * 事件发生时间
     */
    private Date occurTime;

    /**
     * 负外部性事件感知时间
     */
    private Date perceiveTime;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 扩展字段
     */
    private NegativePublicEventExtInfoDTO extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最近更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean isDeleted;
}
