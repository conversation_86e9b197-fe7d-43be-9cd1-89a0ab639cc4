package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.hsqldb.lib.StringUtil;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BroadCastCalcConfigDTO {

    /**
     * 群组id
     */
    private List<Long> groupIdList;

    /**
     * 需要播报的场地
     */
    private List<String> placeCodeList;


    /**
     * 模板id
     */
    private String templateId;

    /**
     * 校验参数是否正确
     *
     * @return
     */
    public boolean validate() {
        return !CollectionUtils.isEmpty(groupIdList) && !CollectionUtils.isEmpty(placeCodeList) && !StringUtil.isEmpty(
                templateId);
    }
}