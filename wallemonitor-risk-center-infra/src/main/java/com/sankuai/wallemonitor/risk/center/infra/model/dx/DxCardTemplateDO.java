package com.sankuai.wallemonitor.risk.center.infra.model.dx;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordLabelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ThemeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 大象卡片模板，字段如果没有值，则卡片不展示
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DxCardTemplateDO {

    // 基础字段（始终存在）
    private String title;
    private String themeColor;

    // 数据字段
    private String occurTime;
    private String vehicleName;
    private String purpose;
    private String poiName;
    private String placeCode;
    private String parkingPlotName;
    private String occurTimeImg;
    private String feedback;

    /**
     * 是否禁用按钮
     */
    private Boolean isBtnDisabled;

    /**
     * 安全员
     */
    private String securityOfficer;
    /**
     * 提示
     */
    private String prompt;
    /**
     * 按钮文本
     */
    private String buttonTex;
    /**
     * 车辆名称
     */
    private String vehicleNameV2;
    /**
     * 组长
     */
    private String master;
    /**
     * 是否显示车辆名称
     */
    private Boolean showVehicleNameV2;
    /**
     * 是否显示安全员
     */
    private Boolean showSecurityOfficer;
    /**
     * 是否显示提示
     */
    private Boolean showPrompt;
    /**
     * 是否显示组长
     */
    private Boolean showMster;
    
    

    // 显示控制字段
    private Boolean showOccurTime = false;
    private Boolean showVehicleName = false;
    private Boolean showPurpose = false;
    private Boolean showPoiName = false;
    private Boolean showPlaceCode = false;
    private Boolean showParkingPlotName = false;
    private Boolean showOccurTimeImg = false;
    private Boolean showFeedBack = false;
    private Boolean showButton = false;

    public String toArguments() {
        return JacksonUtils.to(this);
    }

    public String summarize() {
        return title;
    }

    public void updateByStatus(AlertRecordStatusEnum status) {
        if (status == null) {
            return;
        }
        switch (status) {
            case UNPROCESSED:
                updateThemeColor(ThemeEnum.RED);
                break;
            case PROCESSING:
                updateThemeColor(ThemeEnum.ORANGE);
                updateTitle("[处置中]" + title);
                break;
            case NO_NEED_PROCESS:
                updateThemeColor(ThemeEnum.GREEN);
                updateTitle("[无需处置]" + title);
                updateDisabledButton(true);
                break;
            case PROCESSED:
                updateThemeColor(ThemeEnum.GREEN);
                updateTitle("[已处置]" + title);
                updateDisabledButton(true);
                break;
            default:
                break;
        }
    }

    public void updateThemeColor(ThemeEnum theme) {
        if (theme == null) {
            return;
        }
        this.themeColor = theme.getValue();
    }

    public void updateTitle(String title) {
        if (StringUtils.isBlank(title)) {
            return;
        }
        this.title = title;
    }

    public void updateDisabledButton(Boolean disabled) {
        this.isBtnDisabled = disabled;
    }

    /**
     * 设置反馈显示状态
     *
     * @param showFeedback 是否显示反馈
     */
    public void setFeedbackVisibility(Boolean showFeedback) {
        this.showFeedBack = showFeedback;
    }

    public void updateFeedbackByLabel(AlertRecordLabelEnum labelEnum) {
        if (labelEnum == null) {
            return;
        }
        this.setFeedback(labelEnum.getVoteType());
    }
}