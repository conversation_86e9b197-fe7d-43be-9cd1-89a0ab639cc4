package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskSafetyAreaImproperParkingRecord;

/**
 * <p>
 * 非配送状态下停车区域停滞事件检测过程表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2025-02-07
 */
public interface RiskSafetyAreaImproperParkingRecordMapper extends CommonMapper<RiskSafetyAreaImproperParkingRecord> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskSafetyAreaImproperParkingRecord> getPOClass() {
        return RiskSafetyAreaImproperParkingRecord.class;
    }

}
