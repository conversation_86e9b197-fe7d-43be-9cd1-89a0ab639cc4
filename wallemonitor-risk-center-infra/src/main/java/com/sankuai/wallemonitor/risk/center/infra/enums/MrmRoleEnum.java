package com.sankuai.wallemonitor.risk.center.infra.enums;


import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 云控角色
 */
@AllArgsConstructor
@Getter
public enum MrmRoleEnum {

    MRM_DRIVER(0, "云代驾"),

    MRM_SUPPORT(1, "云辅助"),
    ;

    /**
     * 角色code
     */
    private int code;
    /**
     * 角色描述
     */
    private String desc;

    /**
     * 根据code获取角色
     *
     * @param code
     * @return
     */
    public static MrmRoleEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(MrmRoleEnum.values())
                .filter(e -> e.getCode() == code)
                .findFirst().orElse(null);

    }

}
