package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.sankuai.walleeve.utils.JacksonUtils;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.Proxy;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;

/**
 * 通用缓存类的封装
 * <p>
 * 使用方式：
 * <p>
 * CacheUtils.cache(myBean, 10, TimeUnit.MINUTES).myBeanMethod("Hello")
 * <p>
 * 其中：myBean, 10, TimeUnit.MINUTES 这三个参数，控制使用一个LoadingCache
 * <p>
 * myBeanMethod + myBeanMethod 的参数，控制使用LoadingCache的key
 */
public class CacheUtils {

    private static final ConcurrentHashMap<CacheKey, LoadingCache<MethodCall, Object>> caches = new ConcurrentHashMap<>();

    public static <T> T doCache(T target, long duration, TimeUnit unit) {
        // 使用AopProxyUtils获取原始类
        Class<?> targetClass = AopProxyUtils.ultimateTargetClass(target);
        CacheKey cacheKey = new CacheKey(targetClass, duration, unit);
        LoadingCache<MethodCall, Object> methodCache = caches.computeIfAbsent(cacheKey,
                k -> createCache(duration, unit, 600));
        // 如果是 final 类，使用 JDK 动态代理
        if (Modifier.isFinal(targetClass.getModifiers())) {
            return (T)Proxy.newProxyInstance(targetClass.getClassLoader(), getAllInterfaces(targetClass),
                    new JdkCacheInvocationHandler(target, methodCache));
        }
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(targetClass);
        enhancer.setCallback(new CacheMethodInterceptor(target, methodCache));
        return (T)enhancer.create();
    }

    // 获取类的所有接口，包括父类的接口
    private static Class<?>[] getAllInterfaces(Class<?> clazz) {
        Set<Class<?>> interfaces = new HashSet<>();
        while (clazz != null) {
            interfaces.addAll(Arrays.asList(clazz.getInterfaces()));
            clazz = clazz.getSuperclass();
        }
        return interfaces.toArray(new Class<?>[0]);
    }

    public static <T> T doCache(T target, Integer size, long duration, TimeUnit unit) {
        // 使用AopProxyUtils获取原始类
        // 使用AopProxyUtils获取原始类
        Class<?> targetClass = AopProxyUtils.ultimateTargetClass(target);
        CacheKey cacheKey = new CacheKey(targetClass, duration, unit);
        LoadingCache<MethodCall, Object> methodCache = caches.computeIfAbsent(cacheKey,
                k -> createCache(duration, unit, size));
        // 如果是 final 类，使用 JDK 动态代理
        if (Modifier.isFinal(targetClass.getModifiers())) {
            return (T)Proxy.newProxyInstance(targetClass.getClassLoader(), getAllInterfaces(targetClass),
                    new JdkCacheInvocationHandler(target, methodCache));
        }
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(targetClass);
        enhancer.setCallback(new CacheMethodInterceptor(target, methodCache));
        return (T)enhancer.create();
    }

    private static LoadingCache<MethodCall, Object> createCache(long duration, TimeUnit unit, Integer size) {
        return CacheBuilder.newBuilder().expireAfterWrite(duration, unit).maximumSize(size)
                .build(new CacheLoader<MethodCall, Object>() {
                    @Override
                    public Object load(MethodCall key) throws Exception {
                        return key.method.invoke(key.target, key.args);
                    }
                });
    }

    public static class CacheMethodInterceptor implements MethodInterceptor {

        private final Object target; // 原始对象
        private final LoadingCache<MethodCall, Object> cache; // 方法调用的缓存

        public CacheMethodInterceptor(Object target, LoadingCache<MethodCall, Object> cache) {
            this.target = target;
            this.cache = cache;
        }

        @Override
        public Object intercept(Object obj, Method method, Object[] args, MethodProxy proxy) throws Throwable {
            // 创建一个表示方法调用的key
            MethodCall call = new MethodCall(target, method, args);
            try {
                // 尝试从缓存中获取结果
                return cache.get(call);
            } catch (Exception e) {
                // 如果缓存中没有，执行原始方法，并将结果放入缓存
                Object result = proxy.invoke(target, args);
                cache.put(call, result);
                return result;
            }
        }
    }

    private static class MethodCall {

        private final Object target;
        private final Method method;
        private final Object[] args;

        MethodCall(Object target, Method method, Object[] args) {
            this.target = target;
            this.method = method;
            this.args = args;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            MethodCall that = (MethodCall)o;
            // 比较target、method、args是否相等
            String thisArgJson = JacksonUtils.to(args);
            String thatArgJson = JacksonUtils.to(that.args);
            return target.equals(that.target) && method.equals(that.method)
                    && StringUtils.equals(thisArgJson, thatArgJson);
        }

        @Override
        public int hashCode() {
            int result = target.hashCode();
            result = 31 * result + method.hashCode();
            result = 31 * result + Arrays.hashCode(args);
            return result;
        }
    }

    @Builder
    @AllArgsConstructor
    @Data
    private static class CacheKey {

        private final Class<?> targetClass;
        private final long duration;
        private final TimeUnit unit;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            CacheKey cacheKey = (CacheKey)o;
            return duration == cacheKey.duration && targetClass.equals(cacheKey.targetClass) && unit == cacheKey.unit;
        }

        @Override
        public int hashCode() {
            return Objects.hash(targetClass, duration, unit);
        }
    }

    // JDK动态代理的 InvocationHandler
    private static class JdkCacheInvocationHandler implements InvocationHandler {
        private final Object target;
        private final LoadingCache<MethodCall, Object> cache;

        public JdkCacheInvocationHandler(Object target, LoadingCache<MethodCall, Object> cache) {
            this.target = target;
            this.cache = cache;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            // Object 类的方法直接调用
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(target, args);
            }

            MethodCall call = new MethodCall(target, method, args);
            try {
                return cache.get(call);
            } catch (Exception e) {
                Object result = method.invoke(target, args);
                cache.put(call, result);
                return result;
            }
        }
    }
}