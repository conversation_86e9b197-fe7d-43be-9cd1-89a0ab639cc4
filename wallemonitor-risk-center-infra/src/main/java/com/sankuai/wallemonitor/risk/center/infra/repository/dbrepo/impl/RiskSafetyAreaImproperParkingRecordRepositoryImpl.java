package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskSafetyAreaImproperParkingRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskSafetyAreaImproperParkingRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskSafetyAreaImproperParkingRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskSafetyAreaImproperParkingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskSafetyAreaImproperParkingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskSafetyAreaImproperParkingRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskSafetyAreaImproperParkingRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskSafetyAreaImproperParkingRecordMapper, RiskSafetyAreaImproperParkingRecordConvert, RiskSafetyAreaImproperParkingRecord, RiskSafetyAreaImproperParkingRecordDO> implements
        RiskSafetyAreaImproperParkingRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询风险停滞事件记录
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskSafetyAreaImproperParkingRecordDO> queryByParam(
            RiskSafetyAreaImproperParkingRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询风险停滞事件记录 (分页)
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public Paging<RiskSafetyAreaImproperParkingRecordDO> queryByParamByPage(
            RiskSafetyAreaImproperParkingRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据临时事件ID查询风险停滞事件记录
     *
     * @param tmpCaseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskSafetyAreaImproperParkingRecordDO getByTmpCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }

    /**
     * 保存风险停滞事件记录
     *
     * @param riskSafetyAreaImproperParkingRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskSafetyAreaImproperParkingRecordDO riskSafetyAreaImproperParkingRecordDO) {
        super.save(riskSafetyAreaImproperParkingRecordDO);
    }

    /**
     * 批量保存风险停滞事件记录
     *
     * @param riskSafetyAreaImproperParkingRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskSafetyAreaImproperParkingRecordDO> riskSafetyAreaImproperParkingRecordDOList) {
        super.batchSave(riskSafetyAreaImproperParkingRecordDOList);
    }
}