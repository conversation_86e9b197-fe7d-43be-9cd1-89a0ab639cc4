package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 发车失败预检过程表
 * </p>
 *
 * <AUTHOR> @since 2025-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("risk_launch_timeout_record")
public class RiskLaunchTimeoutRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 临时事件ID，构造方式{vehicleId}{yyyyMMddHHmmss}S{source}T{type}
     */
    @TableField("tmp_case_id")
    @TableUnique
    private String tmpCaseId;

    /**
     * 事件类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 车辆VIN码
     */
    @TableField("vin")
    private String vin;

    /**
     * 持续时长(s)
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 状态，0-未停滞|10-确认中|20-已确认|99-已取消|100-超时取消（超过一定时被系统取消）
     */
    @TableField("status")
    private Integer status;

    /**
     * 事件召回时车辆运行信息状态快照
     */
    @TableField("vehicle_runtime_info_snapshot")
    private String vehicleRuntimeInfoSnapshot;

    /**
     * 开始时间
     */
    @TableField("occur_time")
    private Date occurTime;

    /**
     * 召回时间
     */
    @TableField("recall_time")
    private Date recallTime;

    /**
     * 解除时间
     */
    @TableField("close_time")
    private Date closeTime;

    /**
     * 是否在停车区域
     */
    @TableField("parking_area_id")
    private String parkingAreaId;

    /**
     * 扩展信息(JSON格式)
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 停滞积累信息
     */
    @TableField("stagnation_counter")
    private String stagnationCounter;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;


}
