package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapLaneDO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ConnectedLaneCalcResultDO {

    /**
     * 左侧车道
     */
    private HdMapLaneDO left;
    /**
     * 左侧车道后继车道
     */
    private List<HdMapLaneDO> leftSuccessorList;

}
