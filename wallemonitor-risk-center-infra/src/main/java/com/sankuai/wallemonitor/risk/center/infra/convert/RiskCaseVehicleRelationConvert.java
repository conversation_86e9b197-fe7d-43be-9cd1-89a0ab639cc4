package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseVehicleRelation;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 风险关联关系持久化数据转换为领域对象
 */
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskCaseVehicleRelationConvert extends
        SingleConvert<RiskCaseVehicleRelation, RiskCaseVehicleRelationDO> {

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toRiskCaseVehicleStatusEnum")
    @Mapping(source = "vehicleSnapshotInfo", target = "vehicleSnapshotInfo", qualifiedByName = "parseVehicleSnapshotInfo")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "parseVehicleCounterList")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "parseExtInfo")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseTypeEnum")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    RiskCaseVehicleRelationDO toDO(RiskCaseVehicleRelation riskCase);

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toRiskCaseVehicleStatus")
    @Mapping(source = "vehicleSnapshotInfo", target = "vehicleSnapshotInfo", qualifiedByName = "serializeVehicleSnapshotInfo")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "serializeVehicleCounterList")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "serializeExtInfo")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    RiskCaseVehicleRelation toPO(RiskCaseVehicleRelationDO riskCaseVehicleRelationDO);
}
