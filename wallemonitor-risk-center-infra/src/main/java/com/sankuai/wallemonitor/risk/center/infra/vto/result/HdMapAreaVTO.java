package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Coordinate;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HdMapAreaVTO {

    /**
     * 地图类型归属
     */
    private String mapType;

    /**
     * id
     */
    private String id;

    /**
     * 类型
     */
    private String type;

    /**
     * 属性
     */
    private Map<String, String> properties;

    /**
     * 范围
     */
    private PolygonDO polygon;


    /**
     * 判断是否在多边形内
     *
     * @param positionDO
     * @return
     */
    public Boolean isInPolygon(PositionDO positionDO) {
        CheckUtil.isNotNull(positionDO, "定位不可以为空");
        CheckUtil.isNotNull(positionDO.getCoordinateSystem(), "定位坐标系不可以为空");
        if (polygon == null) {
            return false;
        }
        return polygon.isInPolygon(positionDO);
    }

    /**
     * 获取多边形的坐标点集合（Coordinate类型）
     *
     * @return List<Coordinate> 坐标点集合
     */
    public List<Coordinate> getCoordinates() {
        return polygon.getClosedCoordinates();
    }

    /**
     * 获取外接矩形
     *
     * @return
     */
    public List<Coordinate> getBoundaryRectangle() {
        return polygon.getBoundaryRectangle();
    }
}
