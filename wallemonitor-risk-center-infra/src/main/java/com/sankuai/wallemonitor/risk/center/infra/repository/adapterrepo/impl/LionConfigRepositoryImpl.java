package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.meituan.xframe.config.annotation.ConfigValueListener;
import com.meituan.xframe.config.vo.ConfigChangedEvent;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.BroadCastCalcConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.HighNegativeCallSafetyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskRaptorReportConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficLightConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AggregateAlertConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.upgrade.AlertUpgradeConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.DataContainerConsumeConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.LongWaitAreaConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.ObstacleAbstractConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.PreHandleDataConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.RiskCaseMrmDisposeConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.WaitInQueueConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CaseMarkCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.MrmCallFilterRuleHitLogDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.MrmCallFilterRuleHitLogRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.VelocityUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.SpringUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.lion.LionConfigUtils;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.FridayVerifyResultVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/6/17
 */
@Slf4j
@Service
public class LionConfigRepositoryImpl implements LionConfigRepository {

    @Resource
    private MrmCallFilterRuleHitLogRepository hitLogRepository;

    private static final String detectorTemplate = "risk.detector.%s.config";

    private static final String ONBOARD_MESSAGE_MODEL_PACKAGE_PREFIX = "com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage";
    /**
     * 消息推送配置类
     */
    private Map<RiskCaseTypeEnum, BroadCastStrategyConfigDTO> caseBroadCaseStrategyConfig = new HashMap<>();

    /**
     * 风控服务呼叫云控过滤策略
     */
    private Map<Pair<Integer, Integer>, CallMrmStrategyConfigDTO> callMrmStrategyConfig = new HashMap<>();

    /**
     * 保障系统呼叫策略
     * */
    private Map<Pair<Integer, Integer>, CallSecuritySystemStrategyConfigDTO> callSecurityStrategyConfig = new HashMap<>();

    /**
     * 风控服务呼叫云控过滤策略
     */
    private Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> releaseMrmStrategyConfig = new HashMap<>();

    /**
     * 风控服务云控退控取消呼叫策略
     */
    private Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> releaseMrmDisposedConfig = new HashMap<>();

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_CASE_STAGNATION_CALL_SWITCH, value = "", defaultValue = "", allowBlankValue = true)
    private Boolean stagnationCallSwitch;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_CASE_STAGNATION_CHECK_QUEUE_SWITCH, value = "", defaultValue = "", allowBlankValue = true)
    private Boolean openImproperStrandingCheckingQueue;

    @ConfigValue(key = "risk.case.improperStranding.checkingQueue.enabled.parallel", value = "", defaultValue = "", allowBlankValue = true)
    private Boolean openImproperStrandingCheckingQueueParallel;

    @ConfigValue(key = LionKeyConstant.RISK_CASE_SECURITY_SYSTEM_CALL_SWITCH, value = "", defaultValue = "", allowBlankValue = true)
    private Boolean riskCaseSecuritySystemCallSwitch;


    private Map<Pair<Integer, Integer>, ReleaseSecuritySystemConfigDTO>
            releaseSecuritySystemConfig;


    /**
     * 风险检测器-特殊区域停滞配置
     */
    private SpecialAreaStrandingDetectConfig specialAreaStrandingDetectConfig;

    /**
     * 风险检测器-逆行配置
     */
    private RetrogradeDetectConfig retrogradeDetectConfig;

    /**
     * 风险检测器-非法压线配置
     */
    private DriveOnTrafficLineDetectConfig driveOnTrafficLineDetectConfig;


    /**
     * 风险检测器-停滞事件
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_STRANDING_DETECT_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private RiskDetectBaseConfig standingDetectConfig;

    /**
     * 负责管理各个list的partition batchSize的配置
     */
    @ConfigValue(key = "list.partition.batchSize.config", value = "", defaultValue = "", allowBlankValue = true)
    private HashMap<String, Integer> listPartitionBatchSizeMap;

    @ConfigValue(key = "risk.case.improperStranding.manualParking.config", value = "", defaultValue = "", allowBlankValue = true)
    private ManualParkingMarkConfigDTO manualParkingMarkConfigDTO;

    @ConfigValue(key = "risk.case.fastUploadAutoCarData.config", value = "", defaultValue = "", allowBlankValue = true)
    private FastUploadAutoCarDataConfig fastUploadAutocarDataConfig;

    @ConfigValue(key = "risk.trafficLight.config", value = "", defaultValue = "", allowBlankValue = true)
    private TrafficLightConfigDTO trafficLightConfigDTO;

    @ConfigValue(key = "check.queue.pickTimeToCheckingVersion", defaultValue = "old", allowBlankValue = true)
    private String pickTimeToCheckingQueueItemFilterVersion;

    @ConfigValue(key = LionKeyConstant.LION_KEY_HIGH_NEGATIVE_CASE_FAST_UPLOAD_AUTO_CAR_DATA_CONFIG, value = "", defaultValue = "", allowBlankValue = true)
    private HighNegativeUploadAutoCarDataConfig highNegativeRiskFastUpLoadConfig;
    private Map<Integer, Map<Integer, String>> riskLevelConfig;

    @ConfigValue(key = LionKeyConstant.WAITING_IN_QUEUE_CONFIG_KEY, defaultValue = "{}")
    private WaitInQueueConfigDTO waitInQueueConfigDTO;

    @ConfigValue(key = "improperStranding.preHandleData.config", defaultValue = "{}")
    private PreHandleDataConfig preHandleDataConfig;

    @ConfigValue(key = LionKeyConstant.OBSTACLE_ABSTRACT_CONFIG_KEY, defaultValue = "{}")
    private ObstacleAbstractConfigDTO obstacleAbstractDTO;

    @ConfigValue(key = "data.container.consume.config")
    private DataContainerConsumeConfigDTO dataContainerConsumeConfigDTO;

    @ConfigValue(key = LionKeyConstant.LONG_WAIT_AREA_CONFIG_KEY)
    private LongWaitAreaConfigDTO longWaitAreaConfigDTO;

    //onboard.message.later.seconds
    @ConfigValue(key = LionKeyConstant.LION_ONBOARD_MESSAGE_LATER_SECONDS, defaultValue = "10")
    private Integer onboardMessageLaterSeconds;
    /**
     * 统计推送配置
     */
    private BroadCastCalcConfigDTO broadCastCalcConfigDTO;

    private RiskAutoCheckConfigDTO riskCheckQueueConfigDTO;

    private RiskAutoCheckConfigDTO riskAutoMarkConfigDTO;

    private FridayConfig fridayConfig;

    private VehicleCounterRuleConfig vehicleCounterRuleConfig;


    @ConfigValue(key = "risk.case.area.operate.name.list", value = "", defaultValue = "", allowBlankValue = true)
    private List<String> areaOperateNameList;

    @ConfigValue(key = "risk.case.friday.confirmed.category", value = "", defaultValue = "", allowBlankValue = true)
    private List<CaseMarkCategoryEnum> fridayConfirmedCategory;

    private DomainEventConfig domainEventConfig;

    @ConfigValue(key = "risk.occurTime.amend.switch", value = "", defaultValue = "", allowBlankValue = true)
    private Boolean occurTimeAmendSwitch;

    @ConfigValue(key = "improper.stranding.case.transform.switch", defaultValue = "")
    private boolean isImproperStrandingCaseTransformSwitchOn;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_RAPTOR_REPORT_CONFIG, value = "", defaultValue = "{}", allowBlankValue = true)
    private Map<Integer, RiskRaptorReportConfigDTO> raptorReportConfig;

    @ConfigValue(
            key = LionKeyConstant.LION_KEY_RISK_CASE_TRANSFER_MARK_SWITCH, value = "", defaultValue = "false",
            allowBlankValue = true
    )
    private Boolean transferMarkSwitch;
    @ConfigValue(
            key = LionKeyConstant.LION_KEY_RESTRICTED_PARKING_DETECT_CONFIG, value = "", defaultValue = "",
            allowBlankValue = true
    )
    private RiskDetectBaseConfig restrictedParkingDetectConfig;

    @ConfigValue(
            key = LionKeyConstant.LION_KEY_LAUNCH_TIMEOUT_CONFIG, value = "", defaultValue = "{}",
            allowBlankValue = true
    )
    private RiskDetectBaseConfig launchTimeoutConfig;

    @ConfigValue(
            key = LionKeyConstant.LION_KEY_ONBOARD_MESSAGE_TOPIC_2_CLASS, value = "", defaultValue = "{}",
            allowBlankValue = true
    )
    private Map<String, String> onboardMessageTopic2Class;

    @ConfigValue(
            key = LionKeyConstant.LION_KEY_OBSTACLE_RESERVE_DISTANCE, value = "", defaultValue = "10.0",
            allowBlankValue = true
    )
    private Double obstacleReserveDistance;

    @ConfigValue(
            key = LionKeyConstant.LION_KEY_MRM_STATUS_CHANGE_TRIGGER, value = "", defaultValue = "{}",
            allowBlankValue = true
    )
    private RiskCaseMrmDisposeConfigDTO mrmDisposeConfig;

    @ConfigValueListener(key = LionKeyConstant.LION_KEY_RISK_CASE_BROADCAST_CONFIG, executeOnStart = true)
    public synchronized void configListener(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }

            Map<String, BroadCastStrategyConfigDTO> config = JacksonUtils.from(configValue,
                    new TypeReference<Map<String, BroadCastStrategyConfigDTO>>() {
                    });

            // check config is ok.
            config.values().forEach(item -> {
                if (item.isIllegal()) {
                    throw new IllegalArgumentException(
                            "case.broadcast.strategy.config is invalid. item: " + JacksonUtils.to(item));
                }
            });

            caseBroadCaseStrategyConfig = config.entrySet().stream().collect(Collectors.toMap(
                    entry -> RiskCaseTypeEnum.findByValue(Integer.valueOf(entry.getKey())),
                    Entry::getValue
            ));
        } catch (Exception e) {
            log.error("Lion:风险事件播报消息策略配置下发失败", e);
        }
    }

    @ConfigValueListener(key = LionKeyConstant.LION_KEY_CANCEL_SECURITY_SYSTEM_CONFIG, executeOnStart = true)
    public synchronized void securitySystemReleaseConfigListener(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }

            Map<String, ReleaseSecuritySystemConfigDTO> config = JacksonUtils.from(configValue,
                    new TypeReference<Map<String, ReleaseSecuritySystemConfigDTO>>() {
                    });

            log.info("Lion:保障系统取消呼叫策略配置下发成功, config = {}", config);
            releaseSecuritySystemConfig = config.entrySet().stream().collect(Collectors.toMap(
                    entry -> new Pair<>(entry.getValue().getSource(), entry.getValue().getType()),
                    Entry::getValue
            ));

        } catch (Exception e) {
            log.error("Lion:保障系统取消呼叫策略配置下发失败", e);
        }
    }

    @ConfigValueListener(key = LionKeyConstant.LION_KEY_RISK_QUEUE_CONFIG, executeOnStart = true)
    public synchronized void checkQueueConfigListener(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            riskCheckQueueConfigDTO = JacksonUtils.from(configValue, RiskAutoCheckConfigDTO.class);
        } catch (Exception e) {
            log.error("Lion:风险检查队列配置下发失败", e);
        }
    }

    @ConfigValueListener(key = LionKeyConstant.LION_KEY_RISK_AUTO_MARK_CONFIG, executeOnStart = true)
    public synchronized void autoMarkConfigListener(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            riskAutoMarkConfigDTO = JacksonUtils.from(configValue, RiskAutoCheckConfigDTO.class);
        } catch (Exception e) {
            log.error("Lion:风险自动标注配置下发失败", e);
        }
    }

    @ConfigValueListener(key = LionKeyConstant.LION_KEY_RISK_LEVEL_CONFIG, executeOnStart = true)
    public synchronized void riskLevelConfigListener(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            riskLevelConfig = JacksonUtils.from(configValue, new TypeReference<Map<Integer, Map<Integer, String>>>() {
            });
        } catch (Exception e) {
            log.error("Lion:风险检查队列配置下发失败", e);
        }
    }


    @ConfigValueListener(key = LionKeyConstant.LION_KEY_VEHICLE_RUNTIME_COUNTER_CONFIG, executeOnStart = true)
    public synchronized void initVehicleCounterRuleConfig(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            vehicleCounterRuleConfig = JacksonUtils.from(configValue,
                    new TypeReference<VehicleCounterRuleConfig>() {
                    });
        } catch (Exception e) {
            log.error("Lion:车辆上下文积累配置下发失败", e);
        }
    }

    @ConfigValueListener(key = LionKeyConstant.LION_KEY_BROADCAST_CALC_CONFIG, executeOnStart = true)
    public void initBroadCastCalcConfigDTO(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            broadCastCalcConfigDTO = JacksonUtils.from(configValue, BroadCastCalcConfigDTO.class);
        } catch (Exception e) {
            log.error("Lion:风险事件播报消息统计配置下发失败", e);
        }
    }

    @ConfigValueListener(key = LionKeyConstant.LION_KEY_AGGREGATE_ALERT_CONFIG, executeOnStart = true)
    public void initAggregateAlertConfig(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            aggregateAlertConfig = JacksonUtils.from(configValue, AggregateAlertConfigDTO.class);
            log.info("Lion:聚合告警配置下发成功, config = {}", aggregateAlertConfig);
        } catch (Exception e) {
            log.error("Lion:聚合告警配置下发失败", e);
        }
    }
 
    @ConfigValueListener(key = LionKeyConstant.LION_KEY_ALERT_UPGRADE_CONFIG, executeOnStart = true)
    public void initAlertUpgradeConfig(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            alertUpgradeConfig = JacksonUtils.from(configValue, AlertUpgradeConfigDTO.class);
            log.info("Lion:风险告警升级配置下发成功, config = {}", alertUpgradeConfig);
        } catch (Exception e) {
            log.error("Lion:风险告警升级配置下发失败", e);
        }
    }

    @ConfigValueListener(key = "risk.detector.specialAreaStranding.config", executeOnStart = true)
    public synchronized void specialAreaStrandingDetectConfigListener(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            specialAreaStrandingDetectConfig = JacksonUtils.from(configValue, SpecialAreaStrandingDetectConfig.class);
        } catch (Exception e) {
            log.error("Lion:风险检查队列配置下发失败", e);
        }
    }

    @ConfigValueListener(key = "risk.detector.retrograde.config", executeOnStart = true)
    public synchronized void retrogradeDetectConfigListener(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            retrogradeDetectConfig = JacksonUtils.from(configValue, RetrogradeDetectConfig.class);
        } catch (Exception e) {
            log.error("Lion:风险检查队列配置下发失败", e);
        }
    }

    @ConfigValueListener(key = "risk.detector.driveOnTrafficLine.config", executeOnStart = true)
    public synchronized void driveOnTrafficLineDetectConfigListener(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }
            driveOnTrafficLineDetectConfig = JacksonUtils.from(configValue, DriveOnTrafficLineDetectConfig.class);
        } catch (Exception e) {
            log.error("Lion:风险检查队列配置下发失败", e);
        }
    }

    /**
     * 风控服务呼叫云控过滤策略
     *
     * @param configEvent
     */
    @ConfigValueListener(key = LionKeyConstant.LION_KEY_CALL_MRM_STRATEGY_CONFIG, executeOnStart = true)
    public synchronized void initCallMrmStrategyConfig(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }

            Map<String, CallMrmStrategyConfigDTO> config = JacksonUtils.from(configValue,
                    new TypeReference<Map<String, CallMrmStrategyConfigDTO>>() {
                    });

            //            // check config is ok.
            //            config.values().forEach(item -> {
            //                if (item.isIllegal()) {
            //                    throw new IllegalArgumentException(
            //                            "case.broadcast.strategy.config is invalid. item: " + JacksonUtils.to(item));
            //                }
            //            });
            log.info("Lion:风控服务呼叫云控过滤策略配置下发成功, config = {}", config);
            callMrmStrategyConfig = config.entrySet().stream().collect(Collectors.toMap(
                    entry -> new Pair<>(entry.getValue().getSource(), entry.getValue().getType()),
                    Entry::getValue
            ));

        } catch (Exception e) {
            log.error("Lion:风控服务呼叫云控过滤策略配置下发失败", e);
        }
    }

    @ConfigValueListener(key = LionKeyConstant.CALL_SECURITY_SYSTEM_STRATEGY_CONFIG, executeOnStart = true)
    public synchronized void initSecuritySystemReason(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }

            Map<String, CallSecuritySystemStrategyConfigDTO> config = JacksonUtils.from(configValue,
                    new TypeReference<Map<String, CallSecuritySystemStrategyConfigDTO>>() {
                    });

            log.info("Lion:保障系统配置原因策略配置下发成功, config = {}", config);
            callSecurityStrategyConfig = config.entrySet().stream().collect(Collectors.toMap(
                    entry -> new Pair<>(entry.getValue().getSource(), entry.getValue().getType()),
                    Entry::getValue
            ));

        } catch (Exception e) {
            log.error("Lion:保障系统配置原因策略配置下发失败", e);
        }
    }

    @ConfigValueListener(key = LionKeyConstant.LION_KEY_RELEASE_MRM_STRATEGY_CONFIG, executeOnStart = true)
    public synchronized void initReleaseMrmStrategyConfig(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }

            Map<String, ReleaseMrmStrategyConfigDTO> config = JacksonUtils.from(configValue,
                    new TypeReference<Map<String, ReleaseMrmStrategyConfigDTO>>() {
                    });
            log.info("Lion:风控服务呼叫云控过滤策略配置下发成功, config = {}", config);
            releaseMrmStrategyConfig = config.entrySet().stream().collect(Collectors.toMap(
                    entry -> new Pair<>(entry.getValue().getSource(), entry.getValue().getType()),
                    Entry::getValue
            ));

        } catch (Exception e) {
            log.error("Lion:风控服务呼叫云控过滤策略配置下发失败", e);
        }
    }

    @ConfigValueListener(key = LionKeyConstant.RISK_CASE_RELEASE_MRM_DISPOSED_CONFIG, executeOnStart = true)
    public synchronized void initReleaseMrmDisposedConfig(ConfigChangedEvent configEvent) {
        try {
            String configValue = configEvent.getValue();
            if (StringUtils.isBlank(configValue)) {
                return;
            }

            Map<String, ReleaseMrmStrategyConfigDTO> config = JacksonUtils.from(configValue,
                    new TypeReference<Map<String, ReleaseMrmStrategyConfigDTO>>() {
                    });
            log.info("Lion:风控服务呼叫云控过滤策略配置下发成功, config = {}", config);
            releaseMrmDisposedConfig = config.entrySet().stream().collect(Collectors.toMap(
                    entry -> new Pair<>(entry.getValue().getSource(), entry.getValue().getType()),
                    Entry::getValue
            ));

        } catch (Exception e) {
            log.error("Lion:风控服务呼叫云控过滤策略配置下发失败", e);
        }
    }

    /**
     * 车辆事件平台的事件关注的列表
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_AUTO_CAR_EVENT_CODE, defaultValue = "[]")
    private List<Integer> focusedAutocarEventCodeList;

    /**
     * 微信小程序登陆态有效时长
     */
    @ConfigValue(key = "user.authentication.token.valid.millis", defaultValue = "")
    private Long tokenValidMillis;

    /**
     * 用户须知版本内容
     */
    @ConfigValue(key = "user.notice.version.context", defaultValue = "")
    private String userNoticeVersionContext;

    /**
     * 用户须知版本ID
     */
    @ConfigValue(key = "user.notice.version.id", defaultValue = "")
    private Long userNoticeVersionId;

    /**
     * 车辆事件平台的事件关注的列表
     */
    @ConfigValue(key = "case.sort.problem.list", defaultValue = "[]")
    private List<String> caseSortProblemList;

    /**
     * 呼叫云安全配置
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_HIGH_NEGATIVE_CALL_SAFETY_CONFIG, defaultValue = "{}")
    private Map<RiskCaseTypeEnum, HighNegativeCallSafetyConfigDTO> riskCallSafetyConfigDTOMap;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_VEHICLE_VIN_GRAY_LIST, value = "", defaultValue = "", allowBlankValue = true)
    private HashSet<String> grayVinSet;

    @ConfigValue(key = LionKeyConstant.RISK_CASE_SECURITY_SYSTEM_VIN_GRAY_LIST, value = "", defaultValue = "", allowBlankValue = true)
    private Set<String> securitySystemCallGrayVinSet;

    @ConfigValue(key = LionKeyConstant.LION_KEY_BUSINESS_VEHICLE_PURPOSE, value = "", defaultValue = "业务探索,业务运营-业务测试-坪山,业务运营-花梨坎虚拟测试场地,业务运营-颐丰,业务运营-顺平路站,业务运营-白领外卖,业务运营-坪山,业务运营", allowBlankValue = true)
    private HashSet<String> businessVehiclePurposeSet;


    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_CASE_BUSINESS_VEHICLE_CALL_SWITCH, value = "", defaultValue = "", allowBlankValue = true)
    private Boolean businessVehicleCallSwitch;

    @ConfigValue(key = "risk.link.template", defaultValue = "0")
    private long riskLinkTemplateId;

    @ConfigValue(key = "risk.link.template.values", defaultValue = "0")
    private Map<String, String> riskLinkTemplateValues;

    @ConfigValue(key = "risk.detect.vehicle.validFilter.expression", defaultValue = "", allowBlankValue = true)
    private String vehicleValidFilterExpression;

    /**
     * 聚合告警配置
     */
    private AggregateAlertConfigDTO aggregateAlertConfig;

    /**
     * 风险告警升级配置
     */
    private AlertUpgradeConfigDTO alertUpgradeConfig;


    @Override
    public Map<RiskCaseTypeEnum, BroadCastStrategyConfigDTO> getCaseType2BroadCastStrategyConfig() {
        return caseBroadCaseStrategyConfig;
    }


    @Override
    public BroadCastStrategyConfigDTO getByCaseType(RiskCaseTypeEnum caseType) {
        return caseBroadCaseStrategyConfig.get(caseType);
    }

    @Override
    public List<Integer> getFocusedAutocarEventCodeList() {
        return focusedAutocarEventCodeList;
    }

    @Override
    public BroadCastCalcConfigDTO getBroadCastCalcConfig() {
        return broadCastCalcConfigDTO;
    }

    @Override
    public Map<Pair<Integer, Integer>, CallMrmStrategyConfigDTO> getCallMrmStrategyConfigDTO() {
        return callMrmStrategyConfig;
    }

    @Override
    public Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> getReleaseMrmStrategyConfigDTO() {
        return releaseMrmStrategyConfig;
    }

    /**
     * 判断是否开启呼叫开关
     *
     * @return
     */
    @Override
    public boolean isStagnationCallSwitchOn() {
        return BooleanUtils.toBoolean(stagnationCallSwitch);
    }

    @Override
    public boolean isInMrmCallVinList(String vin) {
        if (CollectionUtils.isEmpty(grayVinSet)) {
            return false;
        }
        return grayVinSet.contains(vin) || grayVinSet.contains(CommonConstant.ALL);
    }

    @Override
    public Boolean isInSecuritySystemCallVinList(String vin) {
        if (CollectionUtils.isEmpty(securitySystemCallGrayVinSet)) {
            return false;
        }
        return securitySystemCallGrayVinSet.contains(vin) || securitySystemCallGrayVinSet.contains(CommonConstant.ALL);
    }

    @Override
    public LongWaitAreaConfigDTO getLongWaitAreaConfig() {
        return longWaitAreaConfigDTO;
    }

    @Override
    public AggregateAlertConfigDTO getAggregateAlertConfig() {
        return aggregateAlertConfig;
    }

    @Override
    public AlertUpgradeConfigDTO getAlertUpgradeConfig() {
        return alertUpgradeConfig;
    }


    @Override
    public Map<Pair<Integer, Integer>, ReleaseMrmStrategyConfigDTO> getReleaseMrmDisposedConfig() {
        return releaseMrmDisposedConfig;
    }

    @Override
    public Integer getOnboardMessageAllowLaterThreshold() {
        return onboardMessageLaterSeconds;
    }

    @Override
    public boolean isBusinessVehicleByPurpose(String purpose) {
        if (CollectionUtils.isEmpty(businessVehiclePurposeSet)) {
            return false;
        }
        return businessVehiclePurposeSet.contains(purpose);
    }


    /**
     * 配置变更时回调
     */
    @ConfigValueListener(key = LionKeyConstant.LION_KEY_FRIDAY_CONFIG, executeOnStart = true)
    public synchronized void onFridayConfig(ConfigChangedEvent event) {
        try {
            String newValue = event.getValue();
            if (StringUtils.isBlank(newValue)) {
                return;
            }
            //序列化
            fridayConfig = com.sankuai.walledelivery.utils.JacksonUtils.from(newValue,
                    FridayConfig.class);
        } catch (Exception e) {
            log.error("friday配置处理异常", e);
        }
    }

    @Override
    public boolean isBusinessVehicleNeedCallMrm() {
        return BooleanUtils.toBoolean(businessVehicleCallSwitch);
    }

    @Override
    public Map<Pair<Integer, Integer>, CallSecuritySystemStrategyConfigDTO> getCallSecurityStrategyConfig() {
        return callSecurityStrategyConfig;
    }

    @Override
    public Map<Pair<Integer, Integer>, ReleaseSecuritySystemConfigDTO> getReleaseSecuritySystemStrategyConfig() {
        return releaseSecuritySystemConfig;
    }

    @Override
    public Boolean getRiskCaseSecuritySystemCallSwitch() {
        return riskCaseSecuritySystemCallSwitch;
    }

    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RiskDetectBaseConfig {

        /**
         * 需要计算的值
         */
        private Map<String, String> calcContext;

        /**
         * 准入条件配置，形如：'#runtimeInfo?.speed < 0.1'
         */
        private String enterCondition;

        /**
         * 确认条件配置，形如：'#record?.duration >= 30'
         */
        private String confirmCondition;

        /**
         * 退出条件配置，形如：'#runtimeInfo?.speed >= 0.1'
         */
        private String cancelCondition;


        /**
         * 需要过滤的区域
         */
        private List<String> filterAreaType;

        /**
         * Polygon延迟判断开关
         * */
        private Boolean delayJudgementSwitch;

        /**
         * 获取计算规则
         *
         * @param key
         * @return
         */
        public String getCalcRule(String key) {
            if (Objects.isNull(calcContext)) {
                return null;
            }
            return calcContext.get(key);

        }
    }

    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SpecialAreaStrandingDetectConfig extends RiskDetectBaseConfig {

        private String a;
    }

    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RetrogradeDetectConfig extends RiskDetectBaseConfig {

        private String b;
    }

    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DriveOnTrafficLineDetectConfig extends RiskDetectBaseConfig {

        private String c;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class BroadCastStrategyConfigDTO {

        /**
         * 延迟播报秒数
         */
        @Builder.Default
        private Integer delaySeconds = 0;

        /**
         * 消息来源延迟播报秒数
         */
        @Builder.Default
        private Map<String, Integer> sourceDelaySeconds = new HashMap<>();

        /**
         * 创建时间查询开始偏移分钟数
         */
        private Integer createTimeQueryStartOffsetMinutes;

        /**
         * 群id
         */
        private List<Long> groupIdList;

        /**
         * 每种风险的参数模板
         */
        private Map<String, String> templateValues;

        /**
         * 每种风险的参数模板
         */
        private String templateId;

        /**
         * 不同群使用不同的模板和模板参数
         */
        private List<GroupTemplateDTO> groupTemplateList;


        /**
         * 自动驾驶版本黑名单
         */
        private Set<String> blackAutocarVersionList;

        /**
         * 场地白名单
         */
        private Set<String> placeCodeWhiteList;

        /**
         * 可重试的最大秒数
         */
        private Integer retryMaxSeconds;

        /**
         * 判断是否要重试消费
         *
         * @return
         */
        public boolean canRetry(Long timeStamp) {
            return timeStamp != null && retryMaxSeconds != null
                    && DatetimeUtil.getNSecondsAfterDateTime(new Date(timeStamp), retryMaxSeconds).compareTo(new Date())
                    >= 0;
        }

        /**
         * 根据上下文进行渲染
         *
         * @param context
         * @return
         */
        public Map<String, String> render(Map<String, Object> context) {
            if (MapUtils.isEmpty(context)) {
                return new HashMap<>();
            }
            Map<String, String> result = new HashMap<>();
            templateValues.forEach((key, valueTemplate) -> {
                result.put(key, StringUtils.defaultString(VelocityUtils.render(valueTemplate, context),
                        CharConstant.CHAR_SPACE));
            });
            return result;
        }

        /**
         * 根据上下文进行渲染
         *
         * @param context
         * @return
         */
        public Map<String, String> render(Map<String, Object> context, Map<String, String> templateValues) {
            if (MapUtils.isEmpty(context) || MapUtils.isEmpty(templateValues)) {
                return new HashMap<>();
            }
            Map<String, String> result = new HashMap<>();
            templateValues.forEach((key, valueTemplate) -> {
                result.put(key, StringUtils.defaultString(VelocityUtils.render(valueTemplate, context),
                        CharConstant.CHAR_SPACE));
            });
            return result;
        }

        /**
         * 根据分组模板，获取模板值
         *
         * @param groupTemplateName
         * @return
         */
        public Map<String, String> getGroupTemplateValuesByGroupTemplateName(String groupTemplateName) {
            if (CollectionUtils.isEmpty(groupTemplateList)) {
                return new HashMap<>();
            }
            return groupTemplateList.stream().filter(item -> StringUtils.equals(item.getGroupTemplateName(),
                    groupTemplateName)).findFirst().map(GroupTemplateDTO::getTemplateValues).orElse(new HashMap<>());
        }

        /**
         * 根据分组模板，获取模板值
         *
         * @param groupTemplateName
         * @return
         */
        public GroupTemplateDTO getGroupTemplateByGroupTemplateName(String groupTemplateName) {
            if (CollectionUtils.isEmpty(groupTemplateList)) {
                return null;
            }
            return groupTemplateList.stream().filter(item -> StringUtils.equals(item.getGroupTemplateName(),
                    groupTemplateName)).findFirst().orElse(null);
        }

        /**
         * 判断配置是否不合法
         *
         * @return
         */
        public boolean isIllegal() {
            boolean endBeforeStart =
                    delaySeconds >= createTimeQueryStartOffsetMinutes * CommonConstant.SECONDS_PER_MINUTE;

            return CollectionUtils.isEmpty(groupTemplateList) || getGroupTemplateList().stream()
                    .anyMatch(groupTemplateDTO -> CollectionUtils.isEmpty(groupTemplateDTO.getGroupIdList())
                            || MapUtils.isEmpty(
                            groupTemplateDTO.getTemplateValues()) || StringUtils.isBlank(
                            groupTemplateDTO.getTemplateId())) || endBeforeStart;
        }

        /**
         * 判断车辆版本是否在黑名单中
         *
         * @param autocarVersion
         * @return
         */
        public boolean isAutocarVersionInBlackList(String autocarVersion) {
            //黑名单为空或者不包含在内
            return CollectionUtils.isNotEmpty(blackAutocarVersionList) &&
                    blackAutocarVersionList.contains(autocarVersion);
        }

        /**
         * 判断场地是否在需要播报的白名单里面
         *
         * @param placeCode
         * @return
         */
        public boolean isPlaceCodeInWhiteList(String placeCode) {
            //白名单包含该场地或者白名单包含all
            return CollectionUtils.isNotEmpty(placeCodeWhiteList) &&
                    (placeCodeWhiteList.contains(placeCode) || placeCodeWhiteList.contains(CommonConstant.ALL));
        }

        /**
         * 获取消息来源对应类型，所需的延迟播报秒数
         *
         * @param riskCaseSourceEnum
         * @return
         */
        public Integer getSourceDelaySecondsNeeded(RiskCaseSourceEnum riskCaseSourceEnum) {
            if (riskCaseSourceEnum == null) {
                // 有问题，不播报
                return null;
            }
            // 延迟的秒数，如果从getSourceDelaySecondsNeeded里面取不到，用 delaySeconds 兜底
            return sourceDelaySeconds.getOrDefault(String.valueOf(riskCaseSourceEnum.getCode()), delaySeconds);
        }


        /**
         * 群组模板
         */
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        @Data
        public static class GroupTemplateDTO {

            /**
             * 群id
             */
            private List<Long> groupIdList;

            /**
             * 群id
             */
            private String groupTemplateName;

            /**
             * 延迟播报秒数
             */
            private Integer delaySeconds;


            /**
             * 表达式
             */
            private List<String> filters;


            /**
             * 根据来源取延迟播报秒数
             */
            private Map<Integer, Integer> sourceDelaySeconds;

            /**
             * 每种风险的参数模板
             */
            private Map<String, String> templateValues;

            /**
             * 每种风险的参数模板
             */
            private String templateId;

            /**
             * 如满足 createTime + 对应 sourceDelaySeconds < new Date() ，返回true，表示可以进行播报 否则表示仍在延迟时间内，无需播报
             *
             * @param createTime
             */
            public boolean isNeedBroadcast(Date createTime, RiskCaseSourceEnum riskCaseSourceEnum) {
                Integer finaleDelaySecond = delaySeconds;
                if (MapUtils.isNotEmpty(sourceDelaySeconds)) {
                    //如果配置了，则看
                    finaleDelaySecond = sourceDelaySeconds.getOrDefault(riskCaseSourceEnum.getCode(), delaySeconds);
                }
                if (finaleDelaySecond == null) {
                    //如果这个群组没配置，默认需要
                    return true;
                }
                return DatetimeUtil.addSeconds(createTime, finaleDelaySecond).compareTo(System.currentTimeMillis())
                        <= 0;
            }

            public boolean isNeedFilter(Map<String, Object> context) {
                if (MapUtils.isEmpty(context) || CollectionUtils.isEmpty(filters)) {
                    return false;
                }
                String filterBy = filters.stream()
                        //做逻辑判断
                        .filter(filter -> SpELUtil.evaluateBoolean(filter, context)).findFirst()
                        //过滤
                        .orElse(CharConstant.CHAR_EMPTY);
                log.info("filter by:{}", filterBy);
                //如果没有任何一个满足过滤的条件，则返回
                return StringUtils.isNotBlank(filterBy);
            }
        }
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class FastUploadAutoCarDataConfig {

        /**
         * 总开关，如果为false关闭所有功能
         */
        private Boolean enable;


        /**
         * 最低持续秒数（超过阈值才触发回传任务）
         */
        private Integer minDurationSeconds;

        /**
         * 停滞前秒数
         */
        private Integer beforeSeconds;

        /**
         * 停滞后秒数
         */
        private Integer afterSeconds;

        /**
         * 上传数据模块
         */
        private List<String> modules;

        /**
         * 需要关联的类型
         */
        private List<Integer> typeList;

        /**
         *
         * @param type
         * @return
         */
        public boolean isInTypeList(RiskCaseTypeEnum type) {
            if (CollectionUtils.isEmpty(typeList)) {
                return RiskCaseTypeEnum.VEHICLE_STAND_STILL.equals(type);
            }
            return typeList.contains(type.getCode());
        }

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class HighNegativeUploadAutoCarDataConfig {

        /**
         * 总开关，如果为false关闭所有功能
         */
        private Boolean enable;

        /**
         * 灰度规则
         */
        private List<String> grayRuleList;

        /**
         * 最低持续秒数（超过阈值才触发回传任务）
         */
        private Integer minDurationSeconds;

        /**
         * 停滞后秒数
         */
        private Integer afterSeconds;

        /**
         * 上传数据模块
         */
        private List<String> modules;


        /**
         * 是否在灰度名单
         */
        public boolean isInGrayList(RiskCaseDO caseDO, RiskCaseVehicleRelationDO caseVehicleRelationDO) {
            if (!enable) {
                return false;
            }
            if (CollectionUtils.isEmpty(grayRuleList)) {
                return false;
            }
            Map<String, Object> context = new HashMap<>();
            context.put("case", caseDO);
            context.put("relation", caseVehicleRelationDO);
            //任意时刻
            return grayRuleList.stream().anyMatch(rule -> SpELUtil.evaluateBoolean(rule, context));

        }
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ManualParkingMarkConfigDTO {

        /**
         * 查询行驶模式延迟时间
         */
        private Integer queryDriveModeNSecondsBeforeStranding;

        /**
         * 查询行驶模式超时取消时间
         */
        private Integer timeoutCancelCheckManualParkingMins;

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class CallMrmStrategyConfigDTO {

        /**
         * 数据源
         */
        private Integer source;

        /**
         * 风险类型
         */
        private Integer type;

        /**
         * mafka 上报消息唯一标识
         */
        private String key;

        /**
         * issueCode
         */
        private Integer issueCode;

        /**
         * 呼叫reason
         */
        private String callMrmReason;

        /**
         * 表达式
         */
        private List<String> filters;

        public boolean isNeedFilter(Map<String, Object> context, String caseId) {
            if (MapUtils.isEmpty(context) || CollectionUtils.isEmpty(filters)) {
                return false;
            }
            log.info("isNeedFilter :{}", filters);
            String filterBy = filters.stream()
                    //做逻辑判断
                    .filter(filter -> SpELUtil.evaluateBoolean(filter, context)).findFirst()
                    //过滤
                    .orElse(CharConstant.CHAR_EMPTY);
            boolean filter = StringUtils.isNotBlank(filterBy);
            if (filter) {
                log.info("filter by:{}", filterBy);
                saveCallFilterRuleHitLog(caseId, filterBy);
            }
            //如果没有任何一个满足过滤的条件，则返回
            return StringUtils.isNotBlank(filterBy);
        }

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class CallSecuritySystemStrategyConfigDTO {

        /**
         * 数据源
         */
        private Integer source;

        /**
         * 风险类型
         */
        private Integer type;

        /**
         * 呼叫原因
         * */
        private Integer reason;

        /**
         * 请求数据源（属于哪个系统 烽火台）
         */
        private Integer requestSource;


        /**
         * 表达式
         */
        private List<String> filters;

        /**
         * 是否需要过滤
         *
         * @param context
         * @return
         */
        public boolean isNeedFilter(Map<String, Object> context, String caseId) {
            if (MapUtils.isEmpty(context) || CollectionUtils.isEmpty(filters)) {
                return false;
            }
            String filterBy = filters.stream()
                    //做逻辑判断
                    .filter(filter -> SpELUtil.evaluateBoolean(filter, context)).findFirst()
                    //过滤
                    .orElse(CharConstant.CHAR_EMPTY);
            boolean filter = StringUtils.isNotBlank(filterBy);
            if (filter) {
                log.info("filter by:{}", filterBy);
                saveCallFilterRuleHitLog(caseId, filterBy);
            }
            //如果没有任何一个满足过滤的条件，则返回
            return filter;
        }

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ReleaseMrmStrategyConfigDTO {

        /**
         * 数据源
         */
        private Integer source;

        /**
         * 风险类型
         */
        private Integer type;

        /**
         * mafka 上报消息唯一标识
         */
        private String key;

        /**
         * issueCode
         */
        private Integer issueCode;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ReleaseSecuritySystemConfigDTO {

        /**
         * 数据源
         */
        private Integer source;

        /**
         * 风险类型
         */
        private Integer type;

        /**
         * 取消原因
         */
        private Integer reason;

        /**
         * request code
         */
        private Integer requestCode;
    }

    @Override
    public Long getUserNoticeVersionId() {
        return userNoticeVersionId;
    }

    @Override
    public String getUserNoticeVersionContext() {
        return userNoticeVersionContext;
    }

    @Override
    public Long getTokenValidMillis() {
        return tokenValidMillis;
    }

    @Override
    public Boolean enableImproperStrandingCheckingQueue() {
        return this.openImproperStrandingCheckingQueue;
    }

    @Override
    public Boolean enableImproperStrandingCheckingQueueParallel() {
        return this.openImproperStrandingCheckingQueueParallel;
    }

    @Override
    public RiskAutoCheckConfigDTO getRiskCheckingConfig() {
        return riskCheckQueueConfigDTO;
    }

    @Override
    public ManualParkingMarkConfigDTO getManualParkingMarkConfig() {
        return manualParkingMarkConfigDTO;
    }

    @Override
    public Integer getListPartitionBatchSize(String type) {
        if (StringUtils.isBlank(type)) {
            return 1; // 默认为1
        }
        return listPartitionBatchSizeMap.getOrDefault(type, 1);
    }

    @Override
    public SpecialAreaStrandingDetectConfig getSpecialAreaStrandingConfig() {
        return specialAreaStrandingDetectConfig;
    }

    @Override
    public RetrogradeDetectConfig getRetrogradeDetectConfig() {
        return retrogradeDetectConfig;
    }

    @Override
    public DriveOnTrafficLineDetectConfig getDriveOnTrafficLineDetectConfig() {
        return driveOnTrafficLineDetectConfig;
    }

    @Override
    public RiskDetectBaseConfig getStrandingDetectConfig() {
        return standingDetectConfig;
    }

    @Override
    public Map<Integer, Map<Integer, String>> getRiskLevelConfig() {
        return riskLevelConfig;
    }

    /**
     * 获取分拣问题列表
     *
     * @return
     */
    @Override
    public List<String> getSortProblemList() {
        return this.caseSortProblemList;
    }

    /**
     * 获取区域运营名称列表
     *
     * @return
     */
    @Override
    public List<String> getAreaOperateNameList() {
        return this.areaOperateNameList;
    }

    @Override
    public FridayConfig getFridayConfig() {
        return this.fridayConfig;
    }

    @Override
    public RiskAutoCheckConfigDTO getRiskAutoMarkConfig() {
        return riskAutoMarkConfigDTO;
    }

    @Override
    public Long getCaseUrlTemplateId() {
        return riskLinkTemplateId;
    }

    @Override
    public Map<String, String> getLinkDataTemplateValues() {
        return riskLinkTemplateValues;
    }

    @Override
    public FastUploadAutoCarDataConfig getFastUploadAutocarDataConfig() {
        return fastUploadAutocarDataConfig;
    }


    @Override
    public VehicleCounterRuleConfig getVehicleCounterRuleConfig() {
        return vehicleCounterRuleConfig;
    }

    @Override
    public HighNegativeCallSafetyConfigDTO getRiskSafetyConfigByRiskType(RiskCaseTypeEnum riskType) {
        if (riskType == null) {
            return null;
        }
        //获取配置
        return riskCallSafetyConfigDTOMap.get(riskType);

    }

    @Override
    public Map<RiskCaseTypeEnum, HighNegativeCallSafetyConfigDTO> getRiskCallSafetyConfig() {
        return riskCallSafetyConfigDTOMap;
    }

    @Override
    public HighNegativeUploadAutoCarDataConfig getHighNegativeFastUploadAutocarDataConfig() {
        return highNegativeRiskFastUpLoadConfig;
    }

    @Override
    public DomainEventConfig getDomainEventConfig() {
        return domainEventConfig;
    }

    @Override
    public boolean isRiskOccurTimeAmendSwitchOn() {
        return BooleanUtils.toBoolean(occurTimeAmendSwitch);
    }

    @Override
    public boolean isImproperStrandingCaseTransformSwitchOn() {
        return BooleanUtils.toBoolean(isImproperStrandingCaseTransformSwitchOn);
    }



    @Override
    public Map<Integer, RiskRaptorReportConfigDTO> getRaptorReportConfig() {

        return raptorReportConfig;
    }

    @Override
    public Boolean isTransferMarkSwitchOn() {
        return transferMarkSwitch;
    }

    @Override
    public Class<?> getOnboardMessageClassByTopic(String topic) {
        if (StringUtils.isBlank(topic) || MapUtils.isEmpty(onboardMessageTopic2Class)
                || StringUtils.isBlank(onboardMessageTopic2Class.get(topic))) {
            return null;
        }
        return ReflectUtils.findClassBySimpleName(ONBOARD_MESSAGE_MODEL_PACKAGE_PREFIX,
                onboardMessageTopic2Class.get(topic));

    }

    @Override
    public Double getObstacleReserveDistance() {
        return obstacleReserveDistance;
    }

    @Override
    public Boolean isValidVehicle(VehicleEveInfoVTO vehicleEveInfoVTO) {
        if (StringUtils.isBlank(vehicleValidFilterExpression)) {
            return true;
        }
        // 兼容不存在eve数据时的场景
        if (vehicleEveInfoVTO == null) {
            return true;
        }

        Map<String, Object> context = new HashMap<>();
        context.put("eveInfo", vehicleEveInfoVTO);
        return SpELUtil.evaluateBoolean(vehicleValidFilterExpression, context);
    }

    /**
     * 获取所有版本的自动标注配置
     *
     * @return
     */
    @Override
    public Map<String, RiskAutoCheckConfigDTO> getAllAutoMarkConfig() {
        // 获取版本 configRepo里面有缓存，这里直接取就行
        Map<String, RiskAutoCheckConfigDTO> key2Version = LionConfigUtils.getGroupAsMap(LionKeyConstant.LION_GROUP_MARK,
                new TypeReference<RiskAutoCheckConfigDTO>() {
                });
        // 添加默认版本
        key2Version.put(CharConstant.CHAR_EMPTY, riskAutoMarkConfigDTO);
        // 获取key的版本
        return key2Version;
    }

    /**
     * 获取指定版本的标注
     *
     * @param version
     * @return
     */
    @Override
    public RiskAutoCheckConfigDTO getRiskAutoMarkConfigByVersion(String version) {
        if (StringUtils.isBlank(version)) {
            // 为空使用默认版本
            return riskAutoMarkConfigDTO;
        }
        // 获取版本
        Map<String, RiskAutoCheckConfigDTO> key2Version = LionConfigUtils.getGroupAsMap(LionKeyConstant.LION_GROUP_MARK,
                new TypeReference<RiskAutoCheckConfigDTO>() {
                });
        // 获取key的版本
        return key2Version.get(version);
    }

    @Override
    public TrafficLightConfigDTO getTrafficLightConfig() {
        return trafficLightConfigDTO;
    }

    @Override
    public String getTimeToCheckingQueueItemFilterVersion() {
        return pickTimeToCheckingQueueItemFilterVersion;
    }

    @Override
    public WaitInQueueConfigDTO getWaitInQueueConfig() {
        return waitInQueueConfigDTO;
    }

    @Override
    public PreHandleDataConfig getPreHandleDataConfig() {
        return preHandleDataConfig;
    }

    @Override
    public RiskDetectBaseConfig getRestrictedParkingDetectConfig() {
        return restrictedParkingDetectConfig;
    }

    @Override
    public RiskDetectBaseConfig getDetectConfig(RiskCaseTypeEnum riskCaseTypeEnum) {

        String riskTypeCaseCameName = riskCaseTypeEnum.getLowerCaseCamelCaseName();
        return LionConfigUtils.getClass(String.format(detectorTemplate, riskTypeCaseCameName),
                RiskDetectBaseConfig.class);
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class FridayConfig {

        // 公开部署： 1840301155550003252
        // 私有部署： 1840301155554250788
        @Builder.Default
        private String appId = "1840301155550003252";

        /**
         * 模型名称
         */
        // 公开：Qwen2-VL
        // 私有: risk_check V1
        @Builder.Default
        private String modelName = "Qwen2-VL";

        /**
         * 超时时间
         */
        @Builder.Default
        private Integer timeoutSecond = 30;

        /**
         * 查询前多少秒的数据
         */
        @Builder.Default
        private Integer beforeTime = 10;

        /**
         * 获取模型概率前百分之X的数据
         */
        @Builder.Default
        private Double topP = 1D;

        /**
         * 映射为category
         */
        @Builder.Default
        private Map<String, String> mapToCategory = new HashMap<>();

        public ISCheckCategoryEnum mapToCategory(FridayVerifyResultVTO resultVTO) {
            if (resultVTO == null || MapUtils.isEmpty(mapToCategory)) {
                return null;
            }
            return mapToCategory.entrySet().stream().filter(entry -> {
                Map<String, Object> context = Maps.newHashMap();
                context.put("result", resultVTO);
                return SpELUtil.evaluateBoolean(entry.getValue(), context);
            }).findFirst().map(entry -> ISCheckCategoryEnum.getBySubcategory(entry.getKey())).orElse(null);
        }
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VehicleCounterRuleConfig {

        /**
         * 字段积累规则
         */
        private List<VehicleCounterRuleDO> counterRuleList;


        /**
         * 获取满足要求的规则
         *
         * @param contextDO
         * @return
         */
        public List<Pair<Boolean, VehicleCounterRuleDO>> doCounter(VehicleRuntimeInfoContextDO contextDO,
                boolean saveFlag) {
            if (CollectionUtils.isEmpty(counterRuleList) || contextDO == null) {
                return new ArrayList<>();
            }
            Map<String, Object> evaluateContext = new HashMap<>();
            evaluateContext.put("context", contextDO);
            List<Pair<Boolean, VehicleCounterRuleDO>> result = new ArrayList<>();
            counterRuleList.forEach(vehicleCounterRuleDO -> {
                if (saveFlag != vehicleCounterRuleDO.getSaveFieldFlag()) {
                    // 需要匹配
                    return;
                }
                boolean isMatch = SpELUtil.evaluateBoolean(vehicleCounterRuleDO.getRule(), evaluateContext);
                result.add(new Pair<>(isMatch, vehicleCounterRuleDO));
            });
            return result;
        }
    }

    @Override
    public ObstacleAbstractConfigDTO getObstacleAbstractConfig() {
        return obstacleAbstractDTO;
    }

    @Override
    public DataContainerConsumeConfigDTO getDataContainerConsumeConfigDTO() {
        return dataContainerConsumeConfigDTO;
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VehicleCounterRuleDO {

        /**
         * 规则内容
         */
        private String rule;

        /**
         * 字段名称
         */
        private String name;

        /**
         * 是否独立保存
         */
        @Builder.Default
        private Boolean saveFieldFlag = true;

    }

    /**
     * 配置变更时回调
     */
    @ConfigValueListener(key = LionKeyConstant.LION_KEY_DOMAIN_EVENT_CONFIG, executeOnStart = true)
    public void onDomainEventConfigChange(ConfigChangedEvent event) {
        try {
            String newValue = event.getValue();
            if (StringUtils.isBlank(newValue)) {
                return;
            }
            //序列化
            domainEventConfig = JacksonUtils.from(newValue, DomainEventConfig.class);
            if (domainEventConfig == null) {
                return;
            }
            domainEventConfig.resetRetryTimes();
        } catch (Exception e) {
            log.error("领域事件处理配置器解析异常", e);
        }
    }


    /**
     * 领域处理器配置类
     */
    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class DomainEventConfig {


        /**
         * 重试次数
         */
        private Integer retryTimes;

        /**
         * 原始的领域事件处理器配置,jso
         */
        private List<DomainEventProcessConfig> processConfigsList;


        /**
         * 需要同步处理的类
         */
        private Set<String> domainAsyncSet = new HashSet<>();

        /**
         * 需要异步处理的类
         */
        private Set<String> domainSyncSet = new HashSet<>();

        //以下为解析配置

        /**
         * 领域处理器的配置
         */
        private Map<String, DomainEventProcessConfig> processConfigsMap = new HashMap<>();


        /**
         * 领域到事件事件处理器的配置
         */
        private Map<String, List<String>> domain2ProcessMap = new HashMap<>();

        /**
         * 根据入口 和 领域类，获取处理类
         */
        public List<String> getProcessName(DomainEventEntryDTO entryDTO, Boolean async) {
            CheckUtil.isNotNull(entryDTO, "入口为空");
            List<String> processNameList = (List<String>) org.apache.commons.collections.MapUtils.getObject(
                    domain2ProcessMap,
                    entryDTO.getDomainClassName(),
                    new ArrayList<>());
            return processNameList.stream().filter(processName -> {
                DomainEventProcessConfig config = processConfigsMap.get(processName);
                //有配置且满足同异步要求
                return config != null && config.checkAsync(async);
            }).collect(Collectors.toList());

        }


        /**
         * 是否可以重试
         */
        public boolean canRetry(String processName, Integer retriedTime) {
            //如果是无限重试 或者 小于重试此时，就可以重试
            if (processConfigsMap.get(processName) == null) {
                return false;
            }
            return processConfigsMap.get(processName).canRetry(retriedTime);

        }

        /**
         * 是否为需要异步专属处理的模型
         *
         * @param domainSimpleClassName
         * @return
         */
        public Boolean isAsyncDomain(String domainSimpleClassName) {
            if (CollectionUtils.isEmpty(domainAsyncSet)) {
                return false;
            }
            return domainAsyncSet.contains(domainSimpleClassName);
        }

        /**
         * 是否为同步处理的模型
         *
         * @param domainSimpleClassName
         * @return
         */
        public Boolean isSyncDomain(String domainSimpleClassName) {
            if (CollectionUtils.isEmpty(domainSyncSet)) {
                return false;
            }
            return domainSyncSet.contains(domainSimpleClassName);
        }

        /**
         * setter方法，设置configList并转换值
         *
         * @param processConfigsList
         */
        public void setProcessConfigsList(
                List<DomainEventProcessConfig> processConfigsList) {
            this.processConfigsList = processConfigsList;
            if (CollectionUtils.isEmpty(processConfigsList)) {
                return;
            }
            processConfigsList.forEach(domainEventProcessConfig -> {
                //处理入口增加该处理类
                Set<String> domainClassNameList = domainEventProcessConfig.getDomainClassNames();
                domainClassNameList.forEach(domainClassName -> {
                    //把这个模型，放进去
                    domain2ProcessMap.computeIfAbsent(domainClassName, domain -> new ArrayList<>())
                            .add(domainEventProcessConfig.getProcessName());
                    if (Boolean.TRUE.equals(domainEventProcessConfig.getAsync())) {
                        //需要同步的
                        domainAsyncSet.add(domainClassName);
                    } else {
                        //需要异步的
                        domainSyncSet.add(domainClassName);
                    }
                });
                //倒置索引
                processConfigsMap.computeIfAbsent(domainEventProcessConfig.getProcessName(),
                        processName -> domainEventProcessConfig);
            });
        }

        /**
         * 设置每个process的重试次数
         */
        public void resetRetryTimes() {
            if (CollectionUtils.isEmpty(processConfigsList) || retryTimes == null) {
                return;
            }
            processConfigsList.stream().
                    //如果没有默认的次数,用配置替换
                            filter(domainEventProcessConfig -> domainEventProcessConfig.getRetryCount() == null)
                    .forEach(domainEventProcessConfig -> {
                        domainEventProcessConfig.setRetryCount(retryTimes);
                    });

        }
    }

    /**
     * 领域事件处理器配置
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    private static class DomainEventProcessConfig {

        /**
         * 处理器的名称，beanName
         */
        private String processName;

        /**
         * 可以处理的类
         */
        @Builder.Default
        private Set<String> domainClassNames = new HashSet<>();

        /**
         * 访问入口
         */
        @Builder.Default
        private Set<String> operateEntry = new HashSet<>();


        /**
         * 重试次数
         */
        @Builder.Default
        private Integer retryCount = 3;


        /**
         * 是否异步
         */
        @Builder.Default
        private Boolean async = false;


        /**
         * 是否可以重试
         */
        private boolean canRetry(Integer retriedTime) {
            //如果是无限重试 或者 小于重试此时，就可以重试
            return retryCount == null || retriedTime < retryCount;
        }

        /**
         * 获取可以处理的入口
         *
         * @return
         */
        private List<DomainEventEntryDTO> getDomainEventEntry() {
            if (CollectionUtils.isEmpty(operateEntry) || CollectionUtils.isEmpty(domainClassNames)) {
                return new ArrayList<>();
            }
            //转换
            return operateEntry.stream().map(operateEntry -> domainClassNames.stream()
                    .map(domainClassName -> DomainEventEntryDTO.builder()
                            .domainClassName(domainClassName)
                            .operateEntry(OperateEnterActionEnum.valueOf(operateEntry))
                            .build()).collect(
                            Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());

        }

        public boolean checkAsync(Boolean async) {
            Boolean needAsync = BooleanUtils.toBoolean(async);
            Boolean thisProcessIsAsync = BooleanUtils.toBoolean(this.async);
            return thisProcessIsAsync.equals(needAsync);
        }
    }

    /**
     * 静态方法：保存过滤规则命中记录
     *
     * @param caseId     case唯一ID
     * @param filterRule 命中过滤规则
     */
    public static void saveCallFilterRuleHitLog(String caseId, String filterRule) {
        // 通过Spring上下文获取仓储Bean
        try {
            MrmCallFilterRuleHitLogRepository repo = SpringUtils.getBean(
                    MrmCallFilterRuleHitLogRepository.class);
            MrmCallFilterRuleHitLogDO hitLogDO = MrmCallFilterRuleHitLogDO.builder()
                    .caseId(caseId)
                    .filterRule(filterRule).build();
            repo.save(hitLogDO);
        } catch (Exception e) {
            log.error("saveCallFilterRuleHitLog error", e);
        }

    }


}
