package com.sankuai.wallemonitor.risk.center.infra.exception;

import com.sankuai.walleeve.commons.enums.ErrorCode;
import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;

/**
 * 外部输入错误 在http,thrift,pigeon,job,consumer入口侧对入参做基本校验
 *
 * <AUTHOR>
 * @Date 2022/10/17
 */
public class ParamInputErrorException extends ErrorCodeException {


    public ParamInputErrorException(String message) {
        super(ResponseCodeEnum.PARAM_INPUT_ERROR.getCode(), message);
    }

    public ParamInputErrorException(int code, String message) {
        super(code, message);
    }

    public ParamInputErrorException(int code) {
        super(code, ResponseCodeEnum.PARAM_INPUT_ERROR.getMessage());
    }

    public ParamInputErrorException(int code, String format, Object... arguments) {
        super(code, format, arguments);
    }

    public ParamInputErrorException(ErrorCode errors, Object... arguments) {
        super(errors, arguments);
    }

    public ParamInputErrorException(ResponseCodeEnum responseCode) {
        super(responseCode.getCode(), responseCode.getMessage());
    }
}
