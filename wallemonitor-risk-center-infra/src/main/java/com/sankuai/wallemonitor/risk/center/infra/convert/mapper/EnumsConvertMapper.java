package com.sankuai.wallemonitor.risk.center.infra.convert.mapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.domain.dto.PositionDTO;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventDetailExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.NegativePublicEventExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskObstacleContextInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordLabelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.CallSafetyEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.CheckingStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HandleTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.LineType;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventHandleDegreeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventLevelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventNatureEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.PositionTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseMrmCalledStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskLevelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.SafetyAreaInfoSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.UpgradeStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CaseMarkInfoExtDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CaseSortExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.ImproperStrandingReason;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCaseExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckingExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SafetyAreaExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleRunTimeExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.PublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GisInfoVTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Point;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;

/**
 * 枚举转换器
 */
@Component
@Mapper(componentModel = "spring")
@Slf4j
public class EnumsConvertMapper {


    @Named("toPolygon")
    public static SafetyAreaDO.Polygon toPolygon(String polygon) {
        if (StringUtils.isBlank(polygon)) {
            return null;
        }
        try {
            SafetyAreaDO.Polygon result = JacksonUtils.from(polygon, new TypeReference<SafetyAreaDO.Polygon>() {
            });
            if (result == null) {
                log.warn("Failed to parse polygon: {}", polygon);
            }
            return result;
        } catch (Exception e) {
            log.error("Error parsing polygon: {}", polygon, e);
            return null;
        }
    }

    @Named("toPolygonStr")
    public static String toPolygon(SafetyAreaDO.Polygon polygon) {
        if (polygon == null) {
            return StringUtils.EMPTY;
        }
        try {
            return JacksonUtils.to(polygon);
        } catch (Exception e) {
            log.error("Failed to serialize Polygon", e);
            return StringUtils.EMPTY;
        }
    }

    @Named("toRiskCaseTypeEnum")
    public static RiskCaseTypeEnum toRiskTypeEnum(Integer riderTaskType) {
        if (riderTaskType == null) {
            return null;
        }
        return RiskCaseTypeEnum.findByValue(riderTaskType);
    }

    @Named("toRiskCaseType")
    public static Integer toRiskType(RiskCaseTypeEnum riderTaskType) {
        if (riderTaskType == null) {
            return null;
        }
        return riderTaskType.getCode();
    }

    @Named("toRiskCaseStatusEnum")
    public static RiskCaseStatusEnum toRiskCaseStatusEnum(Integer riskCaseStatus) {
        if (riskCaseStatus == null) {
            return null;
        }
        return RiskCaseStatusEnum.findByValue(riskCaseStatus);
    }

    @Named("toRiskCaseMrmCalledStatusEnum")
    public static RiskCaseMrmCalledStatusEnum toRiskCaseMrmCalledStatusEnum(Integer mrmCalled) {
        if (mrmCalled == null) {
            return null;
        }
        return RiskCaseMrmCalledStatusEnum.findByValue(mrmCalled);
    }

    @Named("formatDate")
    public static String formatDate(Date date) {
        if (date == null) {
            return CharConstant.CHAR_EMPTY;
        }
        return DatetimeUtil.formatTime(date);
    }

    @Named("splitVin")
    public static List<String> splitVin(String vinListStr) {
        if (StringUtils.isBlank(vinListStr)) {
            return new ArrayList<>();
        }
        return Arrays.stream(vinListStr.split(CharConstant.CHAR_DD)).collect(Collectors.toList());
    }


    @Named("toRiskCaseStatus")
    public static Integer toRiskCaseStatus(RiskCaseStatusEnum riskCaseStatusEnum) {
        if (riskCaseStatusEnum == null) {
            return null;
        }
        return riskCaseStatusEnum.getCode();
    }

    @Named("toMrmCalled")
    public static Integer toMrmCalled(RiskCaseMrmCalledStatusEnum mrmCalledStatusEnum) {
        if (mrmCalledStatusEnum == null) {
            return null;
        }
        return mrmCalledStatusEnum.getCode();
    }

    @Named("toMrmCalledBoolean")
    public static Boolean toMrmCalledBoolean(RiskCaseMrmCalledStatusEnum mrmCalledStatusEnum) {
        if (mrmCalledStatusEnum == null) {
            return null;
        }
        if (Objects.equals(mrmCalledStatusEnum, RiskCaseMrmCalledStatusEnum.NO_CALL)) {
            return false;
        }
        return true;
    }

    @Named("toRiskCaseVehicleStatus")
    public static Integer toRiskCaseVehicleStatus(RiskCaseVehicleStatusEnum riskCaseVehicleStatusEnum) {
        if (riskCaseVehicleStatusEnum == null) {
            return null;
        }
        return riskCaseVehicleStatusEnum.getCode();
    }

    @Named("toSafetyAreaInfoSourceEnum")
    public static SafetyAreaInfoSourceEnum toSafetyAreaInfoSourceEnum(Integer safetyAreaInfoSource) {
        return SafetyAreaInfoSourceEnum.findByValue(safetyAreaInfoSource);
    }



    @Named("toSafetyAreaInfoSource")
    public static Integer toSafetyAreaInfoSource(SafetyAreaInfoSourceEnum safetyAreaInfoSourceEnum) {
        if (safetyAreaInfoSourceEnum == null) {
            return null;
        }
        return safetyAreaInfoSourceEnum.getCode();
    }

    @Named("toRiskCaseSourceEnum")
    public static RiskCaseSourceEnum toRiskCaseSourceEnum(Integer riskCaseSource) {
        return RiskCaseSourceEnum.findByValue(riskCaseSource);
    }

    @Named("toRiskCaseSource")
    public static Integer toRiskCaseSource(RiskCaseSourceEnum riskCaseSource) {
        if (riskCaseSource == null) {
            return null;
        }
        return riskCaseSource.getCode();
    }

    @Named("toRiskLevelEnum")
    public static RiskLevelEnum toRiskLevelEnum(Integer riskLevel) {
        return RiskLevelEnum.findByValue(riskLevel);
    }

    @Named("toRiskLevelInteger")
    public static Integer toRiskLevelInteger(RiskLevelEnum riskLevelEnum) {
        if (riskLevelEnum == null) {
            return null;
        }
        return riskLevelEnum.getCode();
    }

    @Named("toRiskCaseVehicleStatusEnum")
    public static RiskCaseVehicleStatusEnum toRiskCaseVehicleStatusEnum(Integer riskCaseVehicleStatus) {
        if (riskCaseVehicleStatus == null) {
            return null;
        }
        return RiskCaseVehicleStatusEnum.findByValue(riskCaseVehicleStatus);
    }

    @Named("parseVehicleSnapshotInfo")
    public static VehicleInfoDO parseVehicleSnapshotInfo(String vehicleSnapshotInfo) {
        if (StringUtils.isBlank(vehicleSnapshotInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(vehicleSnapshotInfo, new TypeReference<VehicleInfoDO>() {
            });
        } catch (Exception e) {
            log.error("parseVehicleSnapshotInfo error", e);
            return null;
        }
    }

    @Named("parseExtInfo")
    public static RiskVehicleExtInfoDO parseExtInfo(String eventInfo) {
        if (StringUtils.isBlank(eventInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(eventInfo, new TypeReference<RiskVehicleExtInfoDO>() {
            });
        } catch (Exception e) {
            log.error("parseExtInfo error", e);
            return null;
        }
    }

    @Named("parseTrafficLight")
    public static TrafficLightDO parseTrafficLight(String trafficLight) {
        if (StringUtils.isBlank(trafficLight)) {
            return null;
        }
        try {
            return JacksonUtils.from(trafficLight, new TypeReference<TrafficLightDO>() {
            });
        } catch (Exception e) {
            log.error("parseVehicleRuntimeExtInfo error", e);
            return null;
        }
    }

    @Named("serializeTrafficLight")
    public static String serializeTrafficLight(TrafficLightDO trafficLightDO) {
        if (Objects.isNull(trafficLightDO)) {
            return CharConstant.CHAR_EMPTY;
        }
        try {
            return JacksonUtils.to(trafficLightDO);
        } catch (Exception e) {
            log.error("serializeVehicleRuntimeExtInfo error", e);
            return null;
        }
    }

    @Named("parseVehicleRuntimeExtInfo")
    public static VehicleRunTimeExtInfoDO parseVehicleRuntimeExtInfo(String eventInfo) {
        if (StringUtils.isBlank(eventInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(eventInfo, new TypeReference<VehicleRunTimeExtInfoDO>() {
            });
        } catch (Exception e) {
            log.error("parseVehicleRuntimeExtInfo error", e);
            return null;
        }
    }

    @Named("serializeVehicleRuntimeExtInfo")
    public static String serializeVehicleRuntimeExtInfo(VehicleRunTimeExtInfoDO vehicleRunTimeExtInfoDO) {
        if (Objects.isNull(vehicleRunTimeExtInfoDO)) {
            return CharConstant.CHAR_EMPTY;
        }
        try {
            return JacksonUtils.to(vehicleRunTimeExtInfoDO);
        } catch (Exception e) {
            log.error("serializeVehicleRuntimeExtInfo error", e);
            return null;
        }
    }


    @Named("serializeExtInfo")
    public static String serializeExtInfo(RiskVehicleExtInfoDO eventInfo) {
        if (eventInfo == null) {
            return null;
        }
        try {
            return JacksonUtils.to(eventInfo);
        } catch (Exception e) {
            log.error("serializeExtInfo error", e);
            return null;
        }
    }

    @Named("parseVehicleCounter")
    public static VehicleCounterInfoDO parseVehicleCounterInfo(String vehicleCounterInfo) {
        if (StringUtils.isBlank(vehicleCounterInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(vehicleCounterInfo, new TypeReference<VehicleCounterInfoDO>() {
            });
        } catch (Exception e) {
            log.error("parseVehicleCounterInfo error", e);
            return null;
        }
    }

    @Named("serializeVehicleCounter")
    public static String serializeVehicleCounterInfo(VehicleCounterInfoDO vehicleCounterInfo) {
        if (vehicleCounterInfo == null) {
            return null;
        }
        try {
            return JacksonUtils.to(vehicleCounterInfo);
        } catch (Exception e) {
            log.error("serializeVehicleCounterInfo error", e);
            return null;
        }
    }

    @Named("parseVehicleCounterList")
    public static List<VehicleCounterInfoDO> parseVehicleCounterList(String vehicleCounterInfo) {
        if (StringUtils.isBlank(vehicleCounterInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(vehicleCounterInfo, new TypeReference<List<VehicleCounterInfoDO>>() {
            });
        } catch (Exception e) {
            log.error("parseVehicleCounterList error", e);
            return null;
        }
    }

    @Named("serializeVehicleCounterList")
    public static String serializeVehicleCounterInfoList(List<VehicleCounterInfoDO> vehicleCounterInfo) {
        if (CollectionUtils.isEmpty(vehicleCounterInfo)) {
            return null;
        }
        try {
            return JacksonUtils.to(vehicleCounterInfo.stream().filter(Objects::nonNull).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("serializeVehicleCounterInfo error", e);
            return null;
        }
    }


    @Named("serializeVehicleSnapshotInfo")
    public static String serializeVehicleSnapshotInfo(VehicleInfoDO vehicleSnapshotInfoDO) {
        if (vehicleSnapshotInfoDO == null) {
            return null;
        }
        try {
            return JacksonUtils.to(vehicleSnapshotInfoDO);
        } catch (Exception e) {
            log.error("serializeVehicleSnapshotInfo error", e);
            return null;
        }
    }


    @Named("toIsDeleted")
    public static Boolean toIsDeleted(IsDeleteEnum isDelete) {
        if (isDelete == null) {
            return null;
        }
        return BooleanUtils.toBoolean(isDelete.getCode());
    }

    @Named("toIsDeletedEnum")
    public static IsDeleteEnum toIsDeletedEnum(Boolean isDelete) {
        if (isDelete == null) {
            return IsDeleteEnum.NOT_DELETED;
        }
        return isDelete ? IsDeleteEnum.DELETED : IsDeleteEnum.NOT_DELETED;
    }

    @Named("toIsDeleteInteger")
    public static Integer toIsDeleteInteger(IsDeleteEnum isDelete) {
        if (isDelete == null) {
            return null;
        }
        return isDelete.getCode();
    }

    @Named("toIsDeletedEnumFromInteger")
    public static IsDeleteEnum toIsDeletedEnumFromInteger(Integer isDelete) {
        if (isDelete == null) {
            return null;
        }
        return IsDeleteEnum.getByCode(isDelete);
    }

    @Named("parseSafetyAreaExtInfo")
    public static SafetyAreaExtInfoDO parseSafetyAreaExtInfo(String safetyAreaExtInfo) {
        if (StringUtils.isBlank(safetyAreaExtInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(safetyAreaExtInfo, new TypeReference<SafetyAreaExtInfoDO>() {
            });
        } catch (Exception e) {
            log.error("parseSafetyAreaExtInfo error", e);
            return null;
        }
    }

    @Named("serializeSafetyAreaExtInfo")
    public static String serializeSafetyAreaExtInfo(SafetyAreaExtInfoDO safetyAreaExtInfoDO) {
        if (safetyAreaExtInfoDO == null) {
            return null;
        }
        try {
            return JacksonUtils.to(safetyAreaExtInfoDO);
        } catch (Exception e) {
            log.error("serializeSafetyAreaExtInfo error", e);
            return CharConstant.CHAR_EMPTY;
        }
    }

    @Named("parseCaseExtInfo")
    public static RiskCaseExtInfoDO parseCaseExtInfo(String riskCaseExtInfo) {
        if (StringUtils.isBlank(riskCaseExtInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(riskCaseExtInfo, new TypeReference<RiskCaseExtInfoDO>() {
            });
        } catch (Exception e) {
            log.error("parseCaseExtInfo error", e);
            return null;
        }
    }


    @Named("serializeRiskCaseExtInfo")
    public static String serializeRiskCaseExtInfo(RiskCaseExtInfoDO riskCaseExtInfo) {
        if (riskCaseExtInfo == null) {
            return null;
        }
        try {
            return JacksonUtils.to(riskCaseExtInfo);
        } catch (Exception e) {
            log.error("serializeRiskCaseExtInfo error", e);
            return CharConstant.CHAR_EMPTY;
        }
    }

    @Named("serializeCheckResult")
    public static String serializeCheckResult(RiskCheckResultDO riskCheckResultDO) {
        if (riskCheckResultDO == null) {
            return null;
        }
        try {
            return JacksonUtils.to(riskCheckResultDO);
        } catch (Exception e) {
            log.error("serializeCheckResult error", e);
            return CharConstant.CHAR_EMPTY;
        }
    }

    @Named("parseCheckResult")
    public static RiskCheckResultDO parseCheckResult(String riskCheckResult) {
        if (StringUtils.isBlank(riskCheckResult)) {
            return null;
        }
        try {
            return JacksonUtils.from(riskCheckResult, new TypeReference<RiskCheckResultDO>() {
            });
        } catch (Exception e) {
            log.error("parseCheckResult error", e);
            return null;
        }
    }

    @Named("parseCaseMarkExtInfo")
    public static CaseMarkInfoExtDO parseCaseMarkExtInfo(String markInfoExt) {
        if (StringUtils.isBlank(markInfoExt)) {
            return null;
        }
        try {
            return JacksonUtils.from(markInfoExt, new TypeReference<CaseMarkInfoExtDO>() {
            });
        } catch (Exception e) {
            log.error("parseCaseMarkInfoExtInfo error", e);
            return null;
        }
    }

    @Named("parseImproperStrandingReason")
    public static ImproperStrandingReason parseImproperStrandingReason(String improperStrandingReason) {
        if (StringUtils.isBlank(improperStrandingReason)) {
            return null;
        }
        try {
            return JacksonUtils.from(improperStrandingReason, new TypeReference<ImproperStrandingReason>() {
            });
        } catch (Exception e) {
            log.error("parseImproperStrandingReason error", e);
            return null;
        }
    }

    @Named("serializeCaseMarkExtInfo")
    public static String serializeCaseMarkExtInfo(CaseMarkInfoExtDO caseMarkInfoExtDO) {
        if (caseMarkInfoExtDO == null) {
            return null;
        }
        try {
            return JacksonUtils.to(caseMarkInfoExtDO);
        } catch (Exception e) {
            log.error("serializeRiskCaseExtInfo error", e);
            return CharConstant.CHAR_EMPTY;
        }
    }

    @Named("serializeImproperStrandingReason")
    public static String serializeImproperStrandingReason(ImproperStrandingReason improperStrandingReason) {
        if (improperStrandingReason == null) {
            return null;
        }
        try {
            return JacksonUtils.to(improperStrandingReason);
        } catch (Exception e) {
            log.error("serializeImproperStrandingReason error", e);
            return CharConstant.CHAR_EMPTY;
        }
    }

    @Named("toDriverModeEnum")
    public static DriverModeEnum toDriverModeEnum(Integer code) {
        if (code == null) {
            return null;
        }
        return DriverModeEnum.fromCode(code);
    }

    @Named("toDriverModeEnumInteger")
    public static Integer toDriverModeEnumInteger(DriverModeEnum driverModeEnum) {
        if (driverModeEnum == null) {
            return null;
        }
        return driverModeEnum.getCode();
    }

    @Named("toHandleTypeEnum")
    public static HandleTypeEnum toHandleTypeEnum(Integer code) {
        if (code == null) {
            return null;
        }
        return HandleTypeEnum.fromCode(code);
    }

    @Named("toHandleTypeEnumInteger")
    public static Integer toHandleTypeEnumInteger(HandleTypeEnum handleTypeEnum) {
        if (handleTypeEnum == null) {
            return null;
        }
        return handleTypeEnum.getCode();
    }

    @Named("concatName")
    public static List<String> concatName(PublicEventDetailDO publicEventDetailDO) {
        if (publicEventDetailDO == null) {
            return new ArrayList<>();
        }
        return publicEventDetailDO.getFullName();
    }


    @Named("joinListString")
    public static String joinListString(List<String> strings) {
        if (CollectionUtils.isEmpty(strings)) {
            return CharConstant.CHAR_EMPTY;
        }
        return String.join(CharConstant.CHAR_COMMA, strings);
    }

    @Named("toPosition")
    public static PositionDO toPosition(GisInfoVTO gisInfoVTO) {
        if (gisInfoVTO == null) {
            return null;
        }
        return PositionDO.builder().latitude(gisInfoVTO.getLatitude()).longitude(gisInfoVTO.getLongitude())
                .coordinateSystem(gisInfoVTO.getCoordinateSystem())
                .build();
    }

    @Named("toPosition")
    public static PositionDTO toPosition(RiskCaseExtInfoDO extInfo) {
        if (extInfo != null && extInfo.getPosition() != null) {
            return PositionDTO.builder()
                    .lat(extInfo.getPosition().getLatitude())
                    .lng(extInfo.getPosition().getLongitude())
                    .coordinateSystem(extInfo.getPosition().getCoordinateSystem().getType())
                    .build();
        }
        return null;
    }

    @Named("toCheckingStatusEnum")
    public CheckingStatusEnum toCheckingStatusEnum(Integer code) {
        if (code == null) {
            return null;
        }
        return CheckingStatusEnum.fromCode(code);
    }

    @Named("toRiskQueueStatusEnum")
    public RiskQueueStatusEnum toRiskQueueStatusEnum(Integer code) {
        if (code == null) {
            return null;
        }
        return RiskQueueStatusEnum.fromCode(code);
    }

    @Named("toRiskCheckingExtInfoDO")
    public RiskCheckingExtInfoDO toRiskCheckingExtInfoDO(String extInfo) {
        if (StringUtils.isBlank(extInfo)) {
            return null;
        }
        return JacksonUtils.from(extInfo, RiskCheckingExtInfoDO.class);
    }

    @Named("toExtInfo")
    public String toExtInfo(RiskCheckingExtInfoDO riskCheckingExtInfoDO) {
        if (Objects.isNull(riskCheckingExtInfoDO)) {
            return null;
        }
        return JacksonUtils.to(riskCheckingExtInfoDO);
    }

    @Named("toCheckingStatus")
    public Integer toCheckingStatus(CheckingStatusEnum checkingStatus) {
        if (checkingStatus == null) {
            return null;
        }
        return checkingStatus.getCode();
    }

    @Named("toRiskQueueStatus")
    public Integer toRiskQueueStatus(RiskQueueStatusEnum status) {
        if (status == null) {
            return null;
        }
        return status.getCode();
    }

    @Named("toTrafficLightTypeEnum")
    public static TrafficLightTypeEnum toTrafficLightTypeEnum(String color) {
        if (StringUtils.isBlank(color)) {
            return null;
        }
        try {
            return TrafficLightTypeEnum.getByName(color);
        } catch (Exception e) {
            log.error("toTrafficLightTypeEnum error", e);
            return null;
        }
    }

    @Named("toTrafficLightTypeString")
    public static String toTrafficLightTypeString(TrafficLightTypeEnum trafficLightTypeEnum) {
        if (trafficLightTypeEnum == null) {
            return TrafficLightTypeEnum.NONE.name();
        }
        return trafficLightTypeEnum.name();
    }


    @Named("toDriveModeEnum")
    public static DriverModeEnum toDriveModeEnum(Integer code) {
        return DriverModeEnum.fromCode(code);
    }

    @Named("toDriveModeEnumInteger")
    public static Integer toDriveModeEnumInteger(DriverModeEnum driverModeEnum) {
        if (driverModeEnum == null) {
            return DriverModeEnum.UNKNOWN.getCode();
        }
        return driverModeEnum.getCode();
    }

    @Named("toPositionTypeEnum")
    public static PositionTypeEnum toPositionTypeEnum(String name) {
        if (name == null || name.isEmpty()) {
            return null; // 或者根据需要返回默认值
        }
        try {
            return PositionTypeEnum.valueOf(name);
        } catch (Exception e) {
            return null; // 或者根据需要处理异常，返回默认值
        }
    }

    @Named("toPositionTypeEnumName")
    public static String toPositionTypeEnumName(PositionTypeEnum positionTypeEnum) {
        if (positionTypeEnum == null) {
            return null; // 或者根据需要返回默认值
        }
        return positionTypeEnum.name();
    }

    @Named("toDetectRecordStatusEnum")
    public DetectRecordStatusEnum toRecordStatusEnum(Integer value) {
        if (value == null) {
            return null;
        }
        return DetectRecordStatusEnum.getByCode(value);
    }

    @Named("toDetectRecordStatusInteger")
    public Integer toRecordStatusInteger(DetectRecordStatusEnum status) {
        if (status == null) {
            return null;
        }
        return status.getCode();
    }

    @Named("parseVehicleRuntimeInfoContextDO")
    public VehicleRuntimeInfoContextDO parseVehicleRuntimeInfoContextDO(String json) {
        try {
            if (json == null || json.isEmpty()) {
                return null;
            }
            return JacksonUtils.from(json, VehicleRuntimeInfoContextDO.class);
        } catch (Exception e) {
            // 日志记录异常
            log.error("parseVehicleRuntimeInfoContextDO error", e);
            return null;
        }
    }

    @Named("serializeVehicleRuntimeInfoContextDO")
    public String serializeVehicleRuntimeInfoContextDO(VehicleRuntimeInfoContextDO vehicleRuntimeInfoContext) {
        try {
            if (vehicleRuntimeInfoContext == null) {
                return null;
            }
            return JacksonUtils.to(vehicleRuntimeInfoContext);
        } catch (Exception e) {
            // 日志记录异常
            log.error("serializeVehicleRuntimeInfoContextDO error", e);
            return null;
        }
    }


    @Named("parseCaseSortDataExtInfo")
    public static CaseSortExtInfoDO parseCaseSortDataExtInfo(String sortExtInfo) {
        if (StringUtils.isBlank(sortExtInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(sortExtInfo, new TypeReference<CaseSortExtInfoDO>() {
            });
        } catch (Exception e) {
            log.error("parseCaseSortDataExtInfo error", e);
            return null;
        }
    }

    @Named("stringifyCaseSortDataExtInfo")
    public static String stringifyCaseSortDataExtInfo(CaseSortExtInfoDO caseSortExtInfoDO) {
        if (caseSortExtInfoDO == null) {
            return null;
        }
        try {
            return JacksonUtils.to(caseSortExtInfoDO);
        } catch (Exception e) {
            log.error("stringifyCaseSortDataExtInfo error", e);
            return CharConstant.CHAR_EMPTY;
        }
    }

    @Named("toLineTypeEnum")
    public LineType toLineTypeEnum(String code) {
        if (code == null) {
            return null;
        }
        return LineType.getByValue(code);
    }

    @Named("toLineType")
    public String toLineType(LineType lineType) {
        if (lineType == null) {
            return null;
        }
        return lineType.name();
    }


    @Named("toCallSafetyEnum")
    public static CallSafetyEnum toCallSafetyEnum(Integer code) {
        return CallSafetyEnum.fromCode(code);
    }

    @Named("toCallSafetyEnumInteger")
    public static Integer fromCallSafetyEnum(CallSafetyEnum callSafetyEnum) {
        return callSafetyEnum != null ? callSafetyEnum.getCode() : null;
    }

    /**
     * 负外部性事件相关
     */
    @Named("toNegativePublicEventStatusEnum")
    public static NegativePublicEventStatusEnum toNegativePublicEventStatusEnum(Integer code) {
        return NegativePublicEventStatusEnum.fromCode(code);
    }

    @Named("toNegativePublicEventTypeEnum")
    public static NegativePublicEventTypeEnum toNegativePublicEventTypeEnum(Integer code) {
        return NegativePublicEventTypeEnum.fromCode(code);
    }

    @Named("parseNegativePublicEventExtInfo")
    public static NegativePublicEventExtInfoDTO parseNegativePublicEventExtInfo(String extInfo) {
        if (StringUtils.isBlank(extInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(extInfo, new TypeReference<NegativePublicEventExtInfoDTO>() {
            });
        } catch (Exception e) {
            log.error("parseNegativePublicEventExtInfo error", e);
            return null;
        }
    }

    @Named("toNegativePublicEventStatus")
    public static Integer toNegativePublicEventStatus(NegativePublicEventStatusEnum status) {
        return status != null ? status.getCode() : null;
    }

    @Named("toNegativePublicEventType")
    public static Integer toNegativePublicEventType(NegativePublicEventTypeEnum type) {
        return type != null ? type.getCode() : null;
    }

    @Named("serializeNegativePublicEventExtInfo")
    public static String serializeNegativePublicEventExtInfo(NegativePublicEventExtInfoDTO extInfo) {
        if (extInfo == null) {
            return null;
        }
        try {
            return JacksonUtils.to(extInfo);
        } catch (Exception e) {
            log.error("serializeNegativePublicEventExtInfo error", e);
            return CharConstant.CHAR_EMPTY;
        }
    }

    /**
     * 负外部性事件详情相关
     */
    @Named("toNegativePublicEventNatureEnum")
    public static NegativePublicEventNatureEnum toNegativePublicEventNatureEnum(Integer code) {
        return NegativePublicEventNatureEnum.fromCode(code);
    }

    @Named("toNegativePublicEventLevelEnum")
    public static NegativePublicEventLevelEnum toNegativePublicEventLevelEnum(Integer code) {
        return NegativePublicEventLevelEnum.fromCode(code);
    }

    @Named("toNegativePublicEventHandleDegreeEnum")
    public static NegativePublicEventHandleDegreeEnum toNegativePublicEventHandleDegreeEnum(Integer code) {
        return NegativePublicEventHandleDegreeEnum.fromCode(code);
    }

    @Named("parseNegativePublicEventDetailExtInfoDTO")
    public static NegativePublicEventDetailExtInfoDTO parseNegativePublicEventDetailExtInfoDTO(String extInfo) {
        if (StringUtils.isBlank(extInfo)) {
            return null;
        }
        try {
            return JacksonUtils.from(extInfo, new TypeReference<NegativePublicEventDetailExtInfoDTO>() {
            });
        } catch (Exception e) {
            log.error("parseNegativePublicEventDetailExtInfoDTO error", e);
            return null;
        }
    }

    @Named("toNegativePublicEventNature")
    public static Integer toNegativePublicEventNature(NegativePublicEventNatureEnum natureEnum) {
        return natureEnum != null ? natureEnum.getCode() : null;
    }

    @Named("toNegativePublicEventLevel")
    public static Integer toNegativePublicEventLevel(NegativePublicEventLevelEnum levelEnum) {
        return levelEnum != null ? levelEnum.getCode() : null;
    }

    @Named("toNegativePublicEventHandleDegree")
    public static Integer toNegativePublicEventHandleDegree(NegativePublicEventHandleDegreeEnum handleDegreeEnum) {
        return handleDegreeEnum != null ? handleDegreeEnum.getCode() : null;
    }

    @Named("serializeNegativePublicEventDetailExtInfo")
    public static String serializeNegativePublicEventDetailExtInfo(NegativePublicEventDetailExtInfoDTO extInfo) {
        if (extInfo == null) {
            return null;
        }
        try {
            return JacksonUtils.to(extInfo);
        } catch (Exception e) {
            log.error("serializeNegativePublicEventDetailExtInfo error", e);
            return CharConstant.CHAR_EMPTY;
        }
    }

    // toLocationFromPoint
    @Named("toLocationFromPoint")
    public static PositionDO toLocationFromPoint(Point point) {
        if (point == null) {
            return null;
        }
        return GeoToolsUtil.toPositionFromPoint(point);
    }

    @Named("toPointFromLocation")
    public static Point toPointFromLocation(PositionDO positionDO) {
        if (positionDO == null) {
            return null;
        }
        return GeoToolsUtil.toPointFromLocation(positionDO);
    }

    // toRiskObstacleDTO
    @Named("toRiskObstacleDTO")
    public static RiskObstacleContextInfoDTO toRiskObstacleDTO(String obstacle) {
        if (StringUtils.isBlank(obstacle)) {
            return null;
        }
        try {
            return JacksonUtils.from(obstacle, new TypeReference<RiskObstacleContextInfoDTO>() {});
        } catch (Exception e) {
            log.error("toRiskObstacleDTO error", e);
            return null;
        }
    }

    // toRiskObstacleStr
    @Named("toRiskObstacleStr")
    public static String toRiskObstacleStr(RiskObstacleContextInfoDTO riskObstacleContextInfoDTO) {
        if (riskObstacleContextInfoDTO == null) {
            return null;
        }
        try {
            return JacksonUtils.to(riskObstacleContextInfoDTO);
        } catch (Exception e) {
            log.error("toRiskObstacleStr error", e);
            return null;
        }
    }

    @Named("toAlertRecordStatusEnum")
    public static AlertRecordStatusEnum toAlertRecordStatusEnum(Integer code) {
        return AlertRecordStatusEnum.findByCode(code);
    }

    @Named("toAlertRecordStatus")
    public static Integer toAlertRecordStatus(AlertRecordStatusEnum statusEnum) {
        if (statusEnum == null) {
            return null;
        }
        return statusEnum.getCode();
    }

    @Named("toAlertRecordLabelEnum")
    public static AlertRecordLabelEnum toAlertRecordLabelEnum(Integer code) {
        return AlertRecordLabelEnum.findByCode(code);
    }

    @Named("toAlertRecordLabel")
    public static Integer toAlertRecordLabel(AlertRecordLabelEnum labelEnum) {
        if (labelEnum == null) {
            return null;
        }
        return labelEnum.getCode();
    }

    @Named("toUpgradeStatusEnum")
    public static UpgradeStatusEnum toUpgradeStatusEnum(Integer code) {
        return UpgradeStatusEnum.findByCode(code);
    }

    @Named("toUpgradeStatus")
    public static Integer toUpgradeStatus(UpgradeStatusEnum statusEnum) {
        if (statusEnum == null) {
            return null;
        }
        return statusEnum.getCode();
    }

}
