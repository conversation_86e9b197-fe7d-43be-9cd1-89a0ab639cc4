package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CrossLineWaitCheckConfig {

    private boolean enable;

    /**
     * 车宽
     */
    @Default
    private Map<String, Double> vehicleWidth = new HashMap<>();

    /**
     * 默认车辆宽度
     */
    @Default
    private Double defaultVehicleWidth = 2.4D;

    public Double getVehicleWidth(String vehicleType) {
        return vehicleWidth.getOrDefault(vehicleType, defaultVehicleWidth);
    }

    /**
     * 需要大于车身的一半
     *
     * @param distanceToNearCurb
     * @return
     */
    public boolean isCrossLine(String vehicleType, Double distanceToNearCurb) {
        return distanceToNearCurb != null
                && Math.abs(distanceToNearCurb) < getVehicleWidth(vehicleType) / 2;
    }
}
