package com.sankuai.wallemonitor.risk.center.infra.model.core;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class NegativePublicEventRelatedDO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * eventId
     */
    private String eventId;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段取值
     */
    private String fieldValue;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最近更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean isDeleted;
}
