package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 障碍物
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class VehicleObstacleInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 障碍物id
     */
    private String obstacleId;

    /**
     * 障碍物位置
     */
    private PositionDO position;

    /**
     * 障碍物类型
     */
    private String obstacleType;

    /**
     * 障碍物细分类型
     */
    private String fineType;

    /**
     * 类型
     */
    private String type;

    /**
     * 到车辆的距离
     */
    private Double distance;

    /**
     * 和车辆行径方向的夹角
     */
    private Double angle;

    /**
     * 所在的车道(自车所在车道的前、后、左、右)
     */
    private String laneId;

    /**
     * 车道和自车所在车道的关系
     */
    private String laneRelation2Vehicle;

    /**
     * 和车辆所在车道的中心线的夹角
     */
    private Double middleAngle;

    /**
     * 障碍物宽度
     */
    private Double width;

    /**
     * 距离最近车道边界的距离
     */
    private Double distanceToNearCurb;

    /**
     * 距离平行车道边界的距离
     */
    @Default
    private List<Double> distanceToNearCurbList = new ArrayList<>();

    /**
     * 是否在车里所在车道
     */
    private boolean inSameLane;

    /**
     * 更新车道和关系
     *
     * @param laneAroundType
     * @param laneGeoDO
     */
    public void updateLaneAndRelation(String laneAroundType, HdMapElementGeoDO laneGeoDO) {
        if (Objects.isNull(laneGeoDO)) {
            return;
        }
        this.laneId = laneGeoDO.getId();
        this.laneRelation2Vehicle = laneAroundType;
    }
}
