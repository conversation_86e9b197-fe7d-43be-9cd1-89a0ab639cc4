package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrafficLightActionConfigDTO {

    /**
     * 是否开启
     */
    private boolean enable;

    /**
     * 最小距离路口距离
     */
    @Builder.Default
    private Double minAwayFromJunction = 10D;


    /**
     * 在路口内，和路口边界的最大距离
     */
    @Builder.Default
    private Double maxNearFromJunction = 5D;

    /**
     * 检索路口的范围
     */
    @Builder.Default
    private Double searchRange = 10D;

    /**
     * 1、如果都存在，取最小的 和 minAwayFromJunction 判断
     * 2、如果都不存在，返回true
     * 3、如果有一个不存在，取另一个和 minAwayFromJunction 判断
     * -1 算不在
     *
     * @param distance2NextCrossWalk
     * @param distanceToJunction
     * @return
     */
    public boolean getAwayFromJunctionOrCrossWalk(Double distance2NextCrossWalk, Double distanceToJunction) {
        if (distance2NextCrossWalk == null && distanceToJunction == null) {
            // 都不存在
            return true;
        }
        if (distance2NextCrossWalk == null || distance2NextCrossWalk <= -1D) {
            // 到人行横道不存在，使用路口
            Double distance = Optional.ofNullable(distanceToJunction).orElse(-1D);
            return distance <= -1 || distance > minAwayFromJunction;
        }
        if (distanceToJunction == null || distanceToJunction <= -1D) {
            // 到路口不存在，使用人行横道
            return distance2NextCrossWalk <= -1 || distance2NextCrossWalk > minAwayFromJunction;
        }
        // 都存在，取最小的比较
        return Math.min(distance2NextCrossWalk, distanceToJunction) > minAwayFromJunction;
    }

}
