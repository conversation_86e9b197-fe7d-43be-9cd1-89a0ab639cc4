package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.dto.ListDiffDTO;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * 比对工具
 */
public final class DiffUtils {

    private DiffUtils() {

    }

    /**
     * 获取两个列表的差异信息
     *
     * @param oldList 旧列表
     * @param newList 新列表
     * @param <T>     列表元素类型
     * @return 差异信息对象
     */
    public static <T> ListDiffDTO<T> getListDiff(List<T> oldList, List<T> newList) {
        // 1 计算需要新增的数据
        List<T> addedList = new ArrayList<>(newList);
        addedList.removeAll(oldList);

        // 2 计算需要删除的数据
        List<T> deletedList = new ArrayList<>(oldList);
        deletedList.removeAll(newList);

        return ListDiffDTO.<T>builder().add(addedList).delete(deletedList).build();
    }

    /**
     * 获取变更差异内容
     *
     * @param oldObj 旧对象
     * @param newObj 新对象
     * @return 变更差异内容的 JSON 字符串
     */
    public static String getChangeDiffContent(Object oldObj, Object newObj) {
        // 创建变更差异的 Map 对象
        Map<String, Object> changeDiffMap = new HashMap<>();
        // 将旧对象和新对象放入 Map 中
        changeDiffMap.put("old", oldObj);
        changeDiffMap.put("new", newObj);
        // 将 Map 对象转换为 JSON 字符串并返回
        return JacksonUtils.to(changeDiffMap);
    }

    public static Map<String, Object> flatten(Map<String, Object> sourceMap,
            Predicate<? super Map.Entry<String, Object>> filter) {
        if (sourceMap == null) {
            return new HashMap<>();
        }
        return sourceMap.entrySet().stream()
                .flatMap(DiffUtils::flatten)
                .filter(filter)
                .collect(LinkedHashMap::new, (m, e) -> m.put("/" + e.getKey(), e.getValue()), LinkedHashMap::putAll);
    }

    private static Stream<Map.Entry<String, Object>> flatten(Map.Entry<String, Object> entry) {

        if (entry == null) {
            return Stream.empty();
        }

        if (entry.getValue() instanceof Map<?, ?>) {
            return ((Map<?, ?>) entry.getValue()).entrySet().stream()
                    .flatMap(e -> flatten(
                            new AbstractMap.SimpleEntry<>(entry.getKey() + "/" + e.getKey(), e.getValue())));
        }

        if (entry.getValue() instanceof List<?>) {
            List<?> list = (List<?>) entry.getValue();
            return IntStream.range(0, list.size())
                    .mapToObj(i -> new AbstractMap.SimpleEntry<String, Object>(entry.getKey() + "/" + i, list.get(i)))
                    .flatMap(DiffUtils::flatten);
        }

        return Stream.of(entry);
    }
}
