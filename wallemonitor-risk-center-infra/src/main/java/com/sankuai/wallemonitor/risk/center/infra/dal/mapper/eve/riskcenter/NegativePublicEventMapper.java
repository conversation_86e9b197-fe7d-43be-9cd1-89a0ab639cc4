package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEvent;

public interface NegativePublicEventMapper extends CommonMapper<NegativePublicEvent> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<NegativePublicEvent> getPOClass() {
        return NegativePublicEvent.class;
    }
}