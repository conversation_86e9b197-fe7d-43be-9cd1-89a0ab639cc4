package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.oceanus.http.internal.HttpHeader;
import com.sankuai.walle.cms.dto.RecordHistoryDTO;
import com.sankuai.walle.cms.response.RecordHistoryRep;
import com.sankuai.walle.cms.service.ReservationService;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.IThriftVehicleHistoryInfoQueryService;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.request.VehicleDataQueryRequest;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.response.VehicleDataInfoVO;
import com.sankuai.walledelivery.basic.client.enums.DelivererTypeEnum;
import com.sankuai.walledelivery.basic.client.request.deliverer.DelivererQueryByAccountRequest;
import com.sankuai.walledelivery.basic.client.request.deliverer.DelivererQueryRequest;
import com.sankuai.walledelivery.basic.client.response.deliverer.DelivererResponse;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.DelivererQueryThriftService;
import com.sankuai.walledelivery.thrift.exception.BizThriftException;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.BaAuthUtils;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.EventPlatformResponse;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppPropertiesConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteCallEveMonitorException;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteErrorException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.utils.CacheUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DriveModeRecordQueryParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DriveModeRecordQueryParamVTO.SingleVinQueryParam;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.DriveModeRecordVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.DriveModeRecordVTO.DriveModeRecord;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.EventPlatformVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleBasicVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 车辆信息服务
 */
@Component
@Slf4j
public class VehicleAdapter {


    /**
     * vhr的标签
     */
    private static final String VHR_KEY = "telecontrolVHR";

    /**
     * 约车目的KEY
     */
    private static final String PURPOSE_KEY = "businessType";

    /**
     * 获取域名
     */
    @Value(AppPropertiesConstant.CONFIG_VALUE_EVE_DOMAIN)
    private String eveBusDomain;


    @ThriftClientProxy(remoteAppKey = AppKeyConstant.BASIC_APP_KEY)
    private DelivererQueryThriftService delivererQueryThriftService;


    @ThriftClientProxy(remoteAppKey = AppKeyConstant.VRESVFE_APP_KEY)
    private ReservationService reservationService;

    @ThriftClientProxy(remoteAppKey = AppKeyConstant.EVE_HISTORY_DATA_QUERY_APP_KEY)
    private IThriftVehicleHistoryInfoQueryService vehicleHistoryInfoQueryService;

    /**
     * 数据总线获取数据的PATH
     */
    private static final String EVE_BUS_DATA_GET = "/eve/online/rest/data/bus/batch/get";

    /**
     * 数据总线状态PATH
     */
    private static final String EVE_STATUS_GET = "/eve/online/rest/monitor/status";

    /**
     * 获取全部的数据
     */
    private static final String EVE_BUS_KEY = "risk_center";

    /**
     * 标签值
     */
    private static final String KEY_VALUE = "value";

    /**
     * 事件中心统一查询接口
     */
    private static final String EVENT_QUERY_URL = "/autodriveEvent/pageQuery/ba";

    /**
     * 事件平台BA鉴权clientId
     */
    @Value("${eventPlatform.clientId}")
    private String eventPlatformClientId;

    /**
     * 事件平台BA认证密钥
     */
    @Value("$KMS{eventPlatform.ba.secret}")
    private String BA_SECRET_KEY;

    @Value("${eventPlatform.queryDomain}")
    private String eventPlatformQueryDomain;

    /**
     * 事件平台中 自动驾驶变更对应的eventCode 等于 3， https://km.sankuai.com/collabpage/2309717263
     */
    private static final String DRIVE_MODE_EVENT_CODE = "3";

    /**
     * 查询自动驾驶变更事件分页大小
     */
    private static final Integer QUERY_DRIVER_MODE_PAGE_SIZE = 200;

    /**
     * 查询车辆驾驶状态变更记录默认页码
     */
    private static final Integer QUERY_DRIVER_MODE_PAGE = 1;

    /**
     * 查询车辆驾驶状态变更记录
     *
     * @param param
     * @return
     */
    public Map<String, DriveModeRecordVTO> queryDriveModeRecord(DriveModeRecordQueryParamVTO param) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getVinQueryParamList())) {
            return new HashMap<>();
        }
        Map<String, DriveModeRecordVTO> result = new HashMap<>();

        for (SingleVinQueryParam queryParam : param.getVinQueryParamList()) {

            // 查询数据
            List<VehicleDataInfoVO> vehicleDataInfoVOList = queryVehicleHistoryDataFromEveReplay(queryParam.getVin(),
                    // 13位 转化为 10位
                    queryParam.getStartTime().getTime() / 1000,
                    queryParam.getEndTime().getTime() / 1000);
            if (CollectionUtils.isEmpty(vehicleDataInfoVOList)) {
                continue;
            }

            // 按照时间顺序进行排序
            List<VehicleDataInfoVO> sortedVehicleDataInfoVOList = vehicleDataInfoVOList.stream()
                    .sorted(Comparator.comparing(VehicleDataInfoVO::getTime))
                    .collect(Collectors.toList());
            // 数据粗加工 - 查找驾驶状态的变更时刻
            List<DriveModeRecord> driveModeRecordList = IntStream.range(1, sortedVehicleDataInfoVOList.size())
                    .mapToObj(i -> {
                        VehicleDataInfoVO pre = sortedVehicleDataInfoVOList.get(i - 1);
                        VehicleDataInfoVO current = sortedVehicleDataInfoVOList.get(i);
                        if (!Objects.equals(pre.getDriveStatusEnum(), current.getDriveStatusEnum())) {
                            return DriveModeRecord.builder()
                                    .preMode(DriverModeEnum.fromCode(pre.getDriveStatusEnum()))
                                    .curMode(DriverModeEnum.fromCode(current.getDriveStatusEnum()))
                                    .changeTime(DatetimeUtil.parseDate(current.getTime(), "yyyy-MM-dd HH:mm:ss"))
                                    .build();
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 兼容查询范围内状态一致不变的场景
            if (CollectionUtils.isEmpty(driveModeRecordList)) {
                driveModeRecordList.add(DriveModeRecord.builder()
                        .preMode(null)
                        .curMode(DriverModeEnum.fromCode(sortedVehicleDataInfoVOList.get(0).getDriveStatusEnum()))
                        .changeTime(DatetimeUtil.parseDate(sortedVehicleDataInfoVOList.get(0).getTime(),
                                "yyyy-MM-dd HH:mm:ss"))
                        .build());
            }
            if (CollectionUtils.isNotEmpty(driveModeRecordList)) {
                result.put(queryParam.getCaseId(),
                        DriveModeRecordVTO.builder().vin(queryParam.getVin()).recordList(driveModeRecordList)
                                .build());
            }
        }
        log.info("queryDriveModeRecordV2, result:{}", JacksonUtils.to(result));
        return result;
    }

    /**
     * 查询某个场地所在时间段的车辆信息
     *
     * @param timePeriod
     * @param placeCode
     * @return
     */
    public Set<String> queryReserveVehicleByTimeAndPlace(TimePeriod timePeriod, String placeCode) {
        try {
            if (!timePeriod.checkTimeBetween() || StringUtils.isBlank(placeCode)) {
                return new HashSet<>();
            }
            //这里因为约车系统没有车辆信息，选择代偿——先查询运力，再查询约车系统
            //先查询场地的全部车辆
            DelivererQueryRequest request = new DelivererQueryRequest();
            request.setPlaceId(placeCode);
            request.setType(DelivererTypeEnum.VEHICLE_POSITION_TYPE.getType());
            request.setNeedPage(false);
            ThriftResponse<List<DelivererResponse>> vehicleResponse = delivererQueryThriftService.queryDelivererByPlace(
                    request);
            if (vehicleResponse == null || CollectionUtils.isEmpty(vehicleResponse.getData())) {
                return new HashSet<>();
            }
            List<String> vinList = vehicleResponse.getData().stream().map(DelivererResponse::getIdentifyNum)
                    .collect(Collectors.toList());
            Date startDate = DatetimeUtil.getZeroDate(timePeriod.getBeginDate());
            Date endDate = DatetimeUtil.getLatestDate(timePeriod.getEndDate());
            RecordHistoryRep recordHistoryRep = reservationService.getVresvRecordHistoryByVins(vinList
                    ,
                    //查询
                    DatetimeUtil.formatTime(startDate),
                    DatetimeUtil.formatTime(endDate));
            log.info("recordHistoryRepV2:{}", JacksonUtils.to(recordHistoryRep));
            if (recordHistoryRep == null || CollectionUtils.isEmpty(recordHistoryRep.getData())) {
                return new HashSet<>();
            }
            //返回场地底下，当前时段有约车的车辆
            return recordHistoryRep.getData().stream()
                    .filter(recordHistoryDTO -> checkReserve(recordHistoryDTO, timePeriod))
                    .map(RecordHistoryDTO::getVin)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("获取约车系统的信息异常", e);
        }
        return new HashSet<>();

    }


    /**
     * 查询车辆基础信息
     *
     * @return
     */
    public List<VehicleBasicVTO> queryVehicleBasicInfoList(List<String> vinList) {
        try {
            if (CollectionUtils.isEmpty(vinList)) {
                return new ArrayList<>();
            }
            DelivererQueryByAccountRequest request = new DelivererQueryByAccountRequest();
            request.setVinList(vinList);
            ThriftResponse<List<DelivererResponse>> response = delivererQueryThriftService.queryDelivererWithPlace(
                    request);
            if (response == null || CollectionUtils.isEmpty(response.getData())) {
                return new ArrayList<>();
            }
            return response.getData().stream().map(delivererResponse -> VehicleBasicVTO.builder()
                    .vin(delivererResponse.getIdentifyNum())
                    .vehicleName(delivererResponse.getAccount())
                    .vehicleId(delivererResponse.getName())
                    .build()).collect(
                    Collectors.toList());
        } catch (Exception e) {
            log.error("查询车辆的基础信息异常", e);
            return new ArrayList<>();
        }
    }


    /**
     * 从约车信息中获取当前时间有效的近场安全员
     * 使用当前系统时间判断约车记录是否在有效时间区间内
     * 一旦找到有效记录就立即返回，提高性能
     *
     * @param reserveVehicle 约车车辆信息
     * @return 有效的近场安全员，如果没有则返回空字符串
     */
    private String getValidSubstituteFromReservation(ReserveVehicle reserveVehicle) {
        if (reserveVehicle == null || CollectionUtils.isEmpty(reserveVehicle.getVresvList())) {
            return CharConstant.CHAR_EMPTY;
        }

        long currentTime = System.currentTimeMillis(); // 使用当前系统时间戳

        // 遍历约车记录，一旦找到有效的就立即返回
        for (VresvData vresv : reserveVehicle.getVresvList()) {
            if (isReservationValid(vresv, currentTime)) {
                String substitute = vresv.getSubstitute();
                if (StringUtils.isNotBlank(substitute)) {
                    return substitute; // 找到有效安全员，立即返回
                }
            }
        }

        return CharConstant.CHAR_EMPTY; // 没有找到有效的安全员
    }

    /**
     * 检查约车记录是否在指定时间有效
     *
     * @param vresv 约车记录
     * @param currentTime 当前时间戳（毫秒）
     * @return 是否有效
     */
    private boolean isReservationValid(VresvData vresv, long currentTime) {
        if (vresv == null || vresv.getStartTime() == null || vresv.getEndTime() == null) {
            return false;
        }

        try {
            // 直接解析约车开始和结束时间为long类型
            long startTime = Long.parseLong(vresv.getStartTime());
            long endTime = Long.parseLong(vresv.getEndTime());

            // 判断是秒级还是毫秒级时间戳，统一转换为毫秒
            if (vresv.getStartTime().length() <= 10) {
                startTime *= 1000; // 秒级转毫秒
            }
            if (vresv.getEndTime().length() <= 10) {
                endTime *= 1000; // 秒级转毫秒
            }

            // 检查当前时间是否在约车时间区间内
            return currentTime >= startTime && currentTime <= endTime;
        } catch (NumberFormatException e) {
            log.warn("约车时间格式解析失败: startTime={}, endTime={}", vresv.getStartTime(), vresv.getEndTime(), e);
            return false;
        }
    }


    /**
     * 检查约车记录是否满足条件，查询约车时段的任意时刻落在timePeriod内部的车
     *
     * @param recordHistoryDTO
     * @param timePeriod
     * @return
     */
    private boolean checkReserve(RecordHistoryDTO recordHistoryDTO, TimePeriod timePeriod) {
        if (recordHistoryDTO == null || recordHistoryDTO.getStartTime() == null
                || recordHistoryDTO.getEndTime() == null) {
            return false;
        }
        Date reserveStartTime = new Date(recordHistoryDTO.getStartTime());
        Date reserveEndTime = new Date(recordHistoryDTO.getEndTime());
        if (reserveEndTime.getTime() < timePeriod.getBeginDate().getTime()) {
            //约车时段结束时段，小于开始时段
            return false;
        }
        if (reserveStartTime.getTime() > timePeriod.getEndDate().getTime()) {
            //约车时段开始时段，大于结束时段
            return false;
        }
        //必须是审核过的
        if (recordHistoryDTO.getApproved() != 1) {
            return false;
        }
        // 如果约车的开始时间在timePeriod的结束时间之前，并且约车的结束时间在timePeriod的开始时间之后，说明有交集
        return true; // 此处应返回true，表示时间段有交集，满足条件
    }

    /**
     * 查询车辆的实时信息，见：<a href="https://km.sankuai.com/collabpage/2192066535">EVE数据总线</a>
     *
     * @param paramVTO
     * @return
     */
    public List<VehicleEveInfoVTO> queryRuntimeVehicleInfo(VehicleRuntimeInfoParamVTO paramVTO) {
        if (paramVTO == null || CollectionUtils.isEmpty(paramVTO.getVinList())) {
            return new ArrayList<>();
        }
        List<VehicleEveInfoVTO> vehicleEveInfoVTOS = new ArrayList<>();
        EveBusRequest eveRequest = EveBusRequest.builder().vinList(paramVTO.getVinList()).key(EVE_BUS_KEY).build();
        try {
            EveHttpResponse<EveBusResult<List<EveBusData>>> eveHttpResponse = HttpUtils.postJson(
                    JacksonUtils.to(eveRequest),
                    eveBusDomain,
                    EVE_BUS_DATA_GET,
                    ImmutableMap.of(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, AppKeyConstant.MONITOR_ONLINE_APP_KEY),
                    new TypeReference<EveBusResult<List<EveBusData>>>() {
                    });
            log.info("eveHttpResponse：{}", JacksonUtils.to(eveHttpResponse));
            if (eveHttpResponse.getData() == null || CollectionUtils.isEmpty(eveHttpResponse.getData().getData())) {
                //未查到任何信息
                return vehicleEveInfoVTOS;
            }
            DelivererQueryByAccountRequest request = new DelivererQueryByAccountRequest();
            request.setVinList(paramVTO.getVinList());
            ThriftResponse<List<DelivererResponse>> response = delivererQueryThriftService.queryDelivererWithPlace(
                    request);
            // 相比当天末尾，延后一小时
            Date endTime = DatetimeUtil.getNSecondsAfterDateTime(DatetimeUtil.getLatestDate(paramVTO.getOccurTime()),
                    DateTimeConstant.ONE_HOUR_SECOND);
            // 开始时间取当天
            Date startTime = DatetimeUtil.getZeroDate(paramVTO.getOccurTime());
            RecordHistoryRep recordHistoryRep = reservationService.getVresvRecordHistoryByVins(paramVTO.getVinList()
                    , DatetimeUtil.formatTime(startTime), DatetimeUtil.formatTime(endTime));
            Map<String, RecordHistoryDTO> vin2RecordHistoryDTO = new HashMap<>();
            if (recordHistoryRep != null && CollectionUtils.isNotEmpty(recordHistoryRep.getData())) {
                //获取唯一个约车记录
                vin2RecordHistoryDTO.putAll(
                        recordHistoryRep.getData().stream()
                                .collect(Collectors.groupingBy(RecordHistoryDTO::getVin, Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        (List<RecordHistoryDTO> list) -> list.stream()
                                                .filter(recordHistoryDTO -> checkReserve(recordHistoryDTO,
                                                        TimePeriod.builder().endDate(paramVTO.getOccurTime())
                                                                .beginDate(paramVTO.getOccurTime()).build()))
                                                .findFirst().orElse(null) // 从每个分组的列表中获取第一个元素
                                ))));
            }
            Map<String, DelivererResponse> vin2DelivererInfo = Optional.ofNullable(response)
                    .map(ThriftResponse::getData).orElse(new ArrayList<>()).stream().collect(
                            Collectors.toMap(DelivererResponse::getIdentifyNum, Function.identity(), (o1, o2) -> o1));

            vehicleEveInfoVTOS = eveHttpResponse.getData().getData().stream()
                    .map((eveBusData -> {
                        VehicleEveInfoVTO vto = new VehicleEveInfoVTO();
                        LabelData labelData = eveBusData.getLabel();
                        String autocarVersion = Optional.ofNullable(eveBusData.getMonitor())
                                .map(Monitor::getAutocarVersion).orElse(CharConstant.CHAR_EMPTY);
                        Integer driveMode = Optional.ofNullable(eveBusData.getMonitorCompute())
                                .map(MonitorCompute::getDriveMode).orElse(NumberUtils.INTEGER_MINUS_ONE);

                        Float speed = Optional.ofNullable(eveBusData.getMonitor())
                                .map(Monitor::getKmph).orElse(NumberUtils.FLOAT_MINUS_ONE);

                        // 档位信息
                        String gear = Optional.ofNullable(eveBusData.getMonitor())
                                .map(Monitor::getGear).orElse(CharConstant.CHAR_EMPTY);

                        MonitorCompute monitorCompute = eveBusData.getMonitorCompute();
                        String placeCode = Optional.ofNullable(vin2DelivererInfo.get(eveBusData.getVin()))
                                .map(DelivererResponse::getPlaceId).orElse(CharConstant.CHAR_EMPTY);
                        String vehicleId = Optional.ofNullable(vin2DelivererInfo.get(eveBusData.getVin()))
                                .map(DelivererResponse::getName).orElse(CharConstant.CHAR_EMPTY);
                        String vehicleName = Optional.ofNullable(vin2DelivererInfo.get(eveBusData.getVin()))
                                .map(DelivererResponse::getAccount).orElse(CharConstant.CHAR_EMPTY);
                        Boolean withRescueOrder = Optional.ofNullable(eveBusData.getRescueOrder())
                                .map(RescueOrder::isHasOrder).orElse(false);
                        Boolean withAccidentOrder = Optional.ofNullable(eveBusData.getAccidentCompute())
                                .map(AccidentCompute::isAccident).orElse(false);
                        Boolean withReOrder = Optional.ofNullable(eveBusData.getReOrder())
                                .map(ReOrder::isHasOrder).orElse(false);
                        Boolean withMaintenanceOrder = Optional.ofNullable(eveBusData.getMaintenanceOrder())
                                .map(MaintenanceOrder::isHasOrder).orElse(false);
                        String vehicleType = Optional.ofNullable(eveBusData.getVehicleManage())
                                .map(VehicleManage::getFirstClassModel).orElse(CharConstant.CHAR_EMPTY);
                        // 业务状态
                        String bizStatus = Optional.ofNullable(eveBusData.getWdp())
                                .filter(wdp -> wdp.getUpdateTime() != null &&
                                        (System.currentTimeMillis() / 1000 - wdp.getUpdateTime())
                                                <= CommonConstant.SECONDS_PER_DAY)
                                .map(Wdp::getBizStatus)
                                .orElse(CharConstant.CHAR_EMPTY);

                        String telecontrol = Optional.ofNullable(vin2RecordHistoryDTO.get(eveBusData.getVin()))
                                .map(RecordHistoryDTO::getTelecontrol).orElse(CharConstant.CHAR_EMPTY);

                        // 从reserve_vehicle中获取substitute（近场安全员）
                        String substitute = getValidSubstituteFromReservation(eveBusData.getReserveVehicle());

                        vto.setVin(eveBusData.getVin());
                        vto.setVehicleId(vehicleId);
                        vto.setVehicleName(vehicleName);
                        vto.setPlaceCode(placeCode);
                        vto.setDriveMode(driveMode);
                        vto.setAutocarVersion(autocarVersion);
                        vto.setWithRescueOrder(withRescueOrder);
                        vto.setWithAccidentOrder(withAccidentOrder);
                        vto.setWithReOrder(withReOrder);
                        vto.setWithMaintenanceOrder(withMaintenanceOrder);
                        vto.setSpeed(speed);
                        vto.setGear(gear);
                        vto.setTelecontrol(telecontrol);
                        vto.setSubstitute(substitute); // 设置近场安全员
                        vto.setBizStatus(bizStatus);
                        vto.setVehicleType(vehicleType);
                        vto.setHdMapVersion(this.getVehicleHdMapVersion(eveBusData.getVin()));
                        if (labelData != null) {
                            String vhrTag = Optional.ofNullable(labelData.getTags()).map(v -> v.get(VHR_KEY))
                                    .map(v -> v.get(KEY_VALUE)).orElse(CharConstant.CHAR_EMPTY);
                            // 业务类型标签
                            String businessType = Optional.ofNullable(labelData.getTags()).map(v -> v.get(PURPOSE_KEY))
                                    .map(v -> v.get(KEY_VALUE)).orElse(CharConstant.CHAR_EMPTY);
                            // 设置业务类型
                            vto.setBusinessType(businessType);

                            if (vin2RecordHistoryDTO.containsKey(eveBusData.getVin())) {
                                vto.setVhr(VHRModeEnum.findByCode(vhrTag));
                            }                            // 约车目的,使用约车系统的subject代替
                            String purpose = Optional.ofNullable(vin2RecordHistoryDTO.get(eveBusData.getVin()))
                                    .map(RecordHistoryDTO::getUsedTypeName).orElse(businessType);
                            vto.setPurpose(purpose);
                        }
                        if (monitorCompute != null) {
                            Position evePosition = eveBusData.getMonitorCompute().getPosition();
                            //坐标已经归一化为WSG84
                            vto.setPosition(PositionDO.builder().longitude(evePosition.getLongitude())
                                    .latitude(evePosition.getLatitude()).coordinateSystem(
                                            CoordinateSystemEnum.WGS84).build());
                        }
                        return vto;
                    })).collect(Collectors.toList());
        } catch (SocketTimeoutException e) {
            throw new RemoteCallEveMonitorException("调用数据总线超时");
        } catch (Exception e) {
            log.error("queryRuntimeVehicleInfo error, paramVTO:{}", paramVTO, e);
        }

        return vehicleEveInfoVTOS;

    }

    /**
     * 查询单个车辆实时信息
     *
     * @param vin
     * @return
     */
    public VehicleEveInfoVTO queryRuntimeVehicleInfoByVin(String vin) {
        return queryRuntimeVehicleInfo(
                VehicleRuntimeInfoParamVTO.builder().vinList(Lists.newArrayList(vin)).build()).stream().findFirst()
                .orElse(null);
    }

    /**
     * 获取HdMapVersion
     *
     * @param vin
     * @return
     */
    public String getVehicleHdMapVersion(String vin) {
        return CacheUtils.doCache(this, 1, TimeUnit.HOURS).doQueryHdMapVersion(vin);
    }

    public String doQueryHdMapVersion(String vin) {
        EveBusStatusParam eveBusStatusParam = EveBusStatusParam.builder().vinList(Lists.newArrayList(vin))
                .key(EVE_BUS_KEY).build();
        String url = eveBusDomain + EVE_STATUS_GET;
        try {
            EveHttpResponse<EveBusResult<List<EveBusStatusResponse>>> eveHttpResponse = HttpUtils.postJson(
                    JacksonUtils.to(eveBusStatusParam), url,
                    ImmutableMap.of(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, AppKeyConstant.MONITOR_ONLINE_APP_KEY),
                    new TypeReference<EveBusResult<List<EveBusStatusResponse>>>() {});
            return Optional.ofNullable(eveHttpResponse).map(EveHttpResponse::getData).map(EveBusResult::getData)
                    .map(Collection::stream)
                    .map(stream -> stream.filter(eveBusStatusResponse -> vin.equals(eveBusStatusResponse.getVin()))
                            .findFirst().map(EveBusStatusResponse::getHdMapVersion).orElse(CharConstant.CHAR_EMPTY))
                    .orElse(CharConstant.CHAR_EMPTY);
        } catch (SocketTimeoutException e) {
            throw new RemoteCallEveMonitorException("调用数据总线超时");
        } catch (Exception e) {
            log.error(StringMessageFormatter.replaceMsg("getVehicleHdMapVersion error, vin:{}", vin), e);
        }
        return CharConstant.CHAR_EMPTY;

    }

    /**
     * 获取HdMaparea
     *
     * @param vin
     * @return
     */
    public String getVehicleHdMapArea(String vin) {
        String hdMapVersion = getVehicleHdMapVersion(vin);
        if (StringUtils.isEmpty(hdMapVersion)) {
            return CharConstant.CHAR_EMPTY;
        }
        // shenzhenpingshan_admap_v5.391.0 拼接规则是固定的
        return hdMapVersion.split(CharConstant.CHAR_XH)[0];

    }

    /**
     * 查询carAccountList对应的车辆vin
     *
     * @param carAccountList
     * @return
     * @throws BizThriftException
     */
    public List<String> queryVinByCarAccountList(List<String> carAccountList) {
        DelivererQueryByAccountRequest delivererQueryByAccountRequest = new DelivererQueryByAccountRequest();
        delivererQueryByAccountRequest.setCarAccountList(carAccountList);
        ThriftResponse<List<DelivererResponse>> response = null;
        try {
            response = delivererQueryThriftService.queryDelivererWithPlace(delivererQueryByAccountRequest);
        } catch (Exception e) {
            log.warn("queryVinByCarAccountList error", e);
        }

        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            return new ArrayList<>();
        }
        return response.getData().stream()
                .map(DelivererResponse::getIdentifyNum)
                .collect(Collectors.toList());
    }


    /**
     * 处理事件平台数据
     *
     * @param eventPlatformVTOList
     * @return
     */
    private List<DriveModeRecord> processEventPlatformVTOList(List<EventPlatformVTO> eventPlatformVTOList) {
        if (CollectionUtils.isEmpty(eventPlatformVTOList)) {
            return null;
        }
        // 按照时间顺序进行排序
        List<EventPlatformVTO> sortEventPlatformVTOList = eventPlatformVTOList.stream()
                .sorted(Comparator.comparingLong(EventPlatformVTO::getEventTimestamp))
                .collect(Collectors.toList());

        // 事件平台数据粗加工
        List<DriveModeRecord> driveModeRecordList = new ArrayList<>();
        for (int i = 1; i < sortEventPlatformVTOList.size(); i++) {
            EventPlatformVTO beforeEvent = sortEventPlatformVTOList.get(i - 1);
            EventPlatformVTO afterEvent = sortEventPlatformVTOList.get(i);

            DriveModeRecord driveModeRecord = DriveModeRecord.builder()
                    .changeTime(new Date(afterEvent.getEventTimestamp()))
                    //                    .preMode(beforeEvent.getDrivingMode())
                    //                    .curMode(afterEvent.getDrivingMode())
                    .build();
            driveModeRecordList.add(driveModeRecord);
        }
        log.info("processEventPlatformVTOList, driveModeRecordList:{}", JacksonUtils.to(driveModeRecordList));

        return driveModeRecordList;
    }

    /**
     * 查询事件平台
     *
     * @param vin
     * @param startTime
     * @param endTime
     * @param eventCodeList
     * @return
     */
    private List<EventPlatformVTO> queryDriveModeChangeFromEventPlatform(String vin, Long startTime, Long endTime,
            String eventCodeList) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("vinList", vin);
            params.put("startTimestamp", String.valueOf(startTime));
            params.put("endTimestamp", String.valueOf(endTime));
            params.put("eventCodeList", eventCodeList);
            params.put("size", String.valueOf(QUERY_DRIVER_MODE_PAGE_SIZE));
            params.put("page", String.valueOf(QUERY_DRIVER_MODE_PAGE));

            Map<String, String> headers = BaAuthUtils.genMWSAuthHeader(CommonConstant.HTTP_METHOD_GET,
                    EVENT_QUERY_URL,
                    eventPlatformClientId,
                    BA_SECRET_KEY,
                    "E, dd MMM yyyy HH:mm:ss z");
            String url = String.format("%s%s", eventPlatformQueryDomain, EVENT_QUERY_URL);
            log.info("queryDriveModeChangeFromEventPlatform, params:{}, url:{}", params, EVENT_QUERY_URL);
            EveHttpResponse<EventPlatformResponse> eveHttpResponse = HttpUtils.get(params, url, headers,
                    EventPlatformResponse.class);
            log.info("queryDriveModeChangeFromEventPlatform, eveHttpResponse ：{}", JacksonUtils.to(eveHttpResponse));
            if (eveHttpResponse == null || eveHttpResponse.getData() == null) {
                return new ArrayList<>();
            }
            EventPlatformResponse response = eveHttpResponse.getData();
            if (!Objects.equals(response.getCode(), 0) || CollectionUtils.isEmpty(response.getData())) {
                return new ArrayList<>();
            }
            return response.getData();

        } catch (Exception e) {
            log.error("queryDriveModeChangeFromEventPlatform error", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从EVE回放数据查询服务查询驾驶模式变更
     *
     * @param vin
     * @param startTime
     * @param endTime
     * @return
     */
    public List<VehicleDataInfoVO> queryVehicleHistoryDataFromEveReplay(String vin, Long startTime, Long endTime) {
        try {
            VehicleDataQueryRequest request = VehicleDataQueryRequest.builder().vin(vin).startTime(startTime)
                    .endTime(endTime).build();
            log.info("queryVehicleHistoryDataFromEveReplay, request:{}", JacksonUtils.to(request));
            EveThriftResponse<List<VehicleDataInfoVO>> response = vehicleHistoryInfoQueryService.getVehicleData(
                    request);
            log.info("queryVehicleHistoryDataFromEveReplay, response:{}", JacksonUtils.to(response));
            if (Objects.isNull(response) || Objects.isNull(response.getData())) {
                throw new RemoteErrorException("queryVehicleHistoryDataFromEveReplay error");
            }
            return response.getData();
        } catch (Exception e) {
            log.error("queryVehicleHistoryDataFromEveReplay, vin:{}, startTime:{}, endTime:{}", vin, startTime,
                    endTime, e);
        }
        return new ArrayList<>();
    }


    /**
     * eve数据总线
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    private static class EveBusRequest {

        /**
         * 车辆列表
         */
        @JsonProperty("vin_list")
        private List<String> vinList;

        /**
         * 事件中心的模块
         */
        private String key;


    }


    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class EveBusResult<T> {

        private int code;
        private String msg;
        private T data;

    }

    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class EveBusData {

        private String vin;
        @JsonProperty("monitor_compute")
        private MonitorCompute monitorCompute;


        @JsonProperty("monitor")
        private Monitor monitor;

        @JsonProperty("reserve_vehicle")
        private ReserveVehicle reserveVehicle;

        private LabelData label;

        @JsonProperty("vehicle_manage")
        private VehicleManage vehicleManage;

        @JsonProperty("maintenance_order")
        private MaintenanceOrder maintenanceOrder;

        @JsonProperty("rescue_order")
        private RescueOrder rescueOrder;

        @JsonProperty("accident_compute")
        private AccidentCompute accidentCompute;

        /**
         * 履约相关
         */
        @JsonProperty("wdp")
        private Wdp wdp;

        @JsonProperty("re_order")
        private ReOrder reOrder;

    }

    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class ReOrder {
        @JsonProperty("has_order")
        private boolean hasOrder;
    }



    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class MaintenanceOrder {

        @JsonProperty("has_order")
        private boolean hasOrder;


    }

    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class RescueOrder {

        @JsonProperty("has_order")
        private boolean hasOrder;

        @JsonProperty("order_status")
        private int orderStatus;
    }

    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class AccidentCompute {

        @JsonProperty("is_accident")
        private boolean isAccident;

        @JsonProperty("accident_reason_desc")
        private String accidentReasonDesc;
    }

    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class MonitorCompute {

        @JsonProperty("drive_desc")
        private List<String> driveDesc;
        @JsonProperty("drive_mode")
        private int driveMode;
        @JsonProperty("is_home")
        private boolean isHome;
        @JsonProperty("online_desc")
        private List<String> onlineDesc;
        @JsonProperty("online_status")
        private int onlineStatus;
        private Position position;
        @JsonProperty("update_time")
        private long updateTime;

    }

    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class Position {

        private double latitude;
        private double longitude;
        private String source;
    }

    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class ReserveVehicle {

        @JsonProperty("vresv_list")
        @Builder.Default
        private List<VresvData> vresvList = new ArrayList<>();
    }


    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class VresvData {

        @JsonProperty("resv_id")
        private String resvId;
        @JsonProperty("start_time")
        private String startTime;
        @JsonProperty("end_time")
        private String endTime;
        private String substitute;
        private String telecontrol;
        @JsonProperty("location_name")
        private String locationName;
        private String approved;
        private String deleted;
    }

    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class LabelData {

        /**
         * 标签数据，如VHR
         */
        @Builder.Default
        private Map<String, Map<String, String>> tags = new HashMap<>();

    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    private static class VehicleManage {

        @JsonProperty("vehicle_name")
        private String vehicleName;

        @JsonProperty("vehicle_id")
        private String vehicleId;

        @JsonProperty("license_no")
        private String licenseNo;

        @JsonProperty("first_class_model")
        private String firstClassModel;

        @JsonProperty("second_class_model")
        private String secondClassModel;

        @JsonProperty("place_name")
        private String placeName;

        @JsonProperty("city_name")
        private String cityName;

        @JsonProperty("update_time")
        private Long updateTime;

        @JsonProperty("scrap")
        private Boolean scrap;

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Monitor {

        @JsonProperty("autocar_version")
        private String autocarVersion;

        /**
         * 速度 km/h
         */
        private Float kmph;

        /**
         * 档位
         */
        private String gear;

    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class EveBusStatusParam {

        @JsonProperty("vin_list")
        private List<String> vinList;

        private String key;

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class EveBusStatusResponse {

        //hd_map_version
        @JsonProperty("hd_map_version")
        private String hdMapVersion;

        private String vin;

    }


    @Builder
    @AllArgsConstructor
    @lombok.Data
    @NoArgsConstructor
    public static class Wdp {

        /**
         * 业务状态
         */
        @JsonProperty("biz_status")
        private String bizStatus;

        /**
         * 更新时间
         */
        @JsonProperty("update_time")
        private Long updateTime;
    }
}

