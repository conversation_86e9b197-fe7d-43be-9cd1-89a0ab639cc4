package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.MrmCallFilterRuleHitLogDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.MrmCallFilterRuleHitLogDOQueryParamDTO;
import java.util.List;

/**
 * 坐席呼叫过滤规则命中纪录表仓储接口
 */
public interface MrmCallFilterRuleHitLogRepository {
    /**
     * 保存命中纪录
     * @param hitLogDO 命中纪录DO
     */
    void save(MrmCallFilterRuleHitLogDO hitLogDO);

    /**
     * 批量保存命中纪录
     * @param hitLogDOList 命中纪录DO列表
     */
    void batchSave(List<MrmCallFilterRuleHitLogDO> hitLogDOList);

    /**
     * 条件查询命中纪录
     * @param paramDTO 查询参数
     * @return 命中纪录列表
     */
    List<MrmCallFilterRuleHitLogDO> queryByParam(MrmCallFilterRuleHitLogDOQueryParamDTO paramDTO);

    /**
     * 批量删除命中纪录
     * @param idList 主键ID列表
     */
    void batchDelete(List<Long> idList);
} 