package com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector;

import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class RiskStrandingDetectorRecordFactoryImpl extends RiskDetectorRecordFactory<RiskStrandingRecordDO> {

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Override
    public RiskStrandingRecordDO init(DetectContextDTO detectContextDTO) {
        VehicleRuntimeInfoContextDO runtimeContextDO = detectContextDTO.toShortVehicleInfoSnapShot();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, runtimeContextDO.getVin(),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.STRANDING,
                runtimeContextDO.getLastUpdateTime());
        return RiskStrandingRecordDO.builder()
                .tmpCaseId(caseId)
                .vin(runtimeContextDO.getVin())
                .type(RiskCaseTypeEnum.STRANDING)
                .duration(0)
                .status(DetectRecordStatusEnum.PROCESSING)
                .occurTime(runtimeContextDO.getLastUpdateTime())
                .build();
    }
}
