package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskLaunchTimeoutRecord;

/**
 * <p>
 * 发车失败事件检测过程表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2025-02-07
 */
public interface RiskLaunchTimeoutRecordMapper extends CommonMapper<RiskLaunchTimeoutRecord> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskLaunchTimeoutRecord> getPOClass() {
        return RiskLaunchTimeoutRecord.class;
    }

}
