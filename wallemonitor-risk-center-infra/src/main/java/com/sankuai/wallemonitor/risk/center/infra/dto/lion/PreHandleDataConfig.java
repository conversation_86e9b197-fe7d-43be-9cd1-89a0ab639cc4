package com.sankuai.wallemonitor.risk.center.infra.dto.lion;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class PreHandleDataConfig {

    /**
     * 车道、障碍物、覆盖物搜索范围距离（米）
     */
    private Double range;

    /**
     * 使用历史位置的前移秒数
     */
    private Integer pastSecond;

    /**
     * 根据obstacleType判断的道路中心线夹角阈值
     */
    @Default
    private Map<String, Double> angleThresholdByObstacleType = new HashMap<>();

    /**
     * 默认障碍物检测距离阈值（米）
     */
    private Double defaultDistanceThreshold;

    /**
     * 障碍物类型距离阈值（米）
     */
    @Default
    private Map<String, Double> fineTypeDistanceThreshold = new HashMap<>();


    /**
     * 区域
     */
    @Default
    private List<String> laneTypeList = new ArrayList<>();

    /**
     * 障碍物的类型
     */
    @Default
    private List<String> fineTypeList = new ArrayList<>();


    /**
     * 车辆前序定位点需要满足的距离要求（防止过近导致的异常）
     */
    private Double preMinDistance;

    /**
     * 车辆前序定位点需要满足的距离要求（防止防止抖动点坐标）
     */
    @Default
    private Double preMaxDistance = 15.0D;

    /**
     * 可通行车道检测的障碍物夹角阈值
     */
    private Double usableLaneObstacleAngleThreshold;

    /**
     * 可通行车道检测的障碍物距离阈值
     */
    private Double usableLaneObstacleDistanceThreshold;


    /**
     * 角度阈值
     */
    @Default
    private Double behindRangeAngle = 150D;

    /**
     * 距离阈值
     */
    @Default
    private Double behindDistance = 14D;


    /**
     * 前方范围最大夹角
     */
    @Default
    private Double frontRangeMaxAngle = 30D;

    /**
     * 后方范围最小夹角（）
     */
    @Default
    private Double behindRangeMinAngle = 150D;


    /**
     * 距离最近车道线的角度阈值，用于排除不满足角度要求的线段参与最近距离计算
     */
    @Default
    private Double distance2NearByAngle = 15D;

    /**
     * 同向车道的判定夹角
     */
    @Default
    private Double laneSameDirectionTheta = 30D;

    /**
     * 增量的范围类型查询
     */
    @Default
    private List<String> rangeFineTypeList = new ArrayList<>();

}
