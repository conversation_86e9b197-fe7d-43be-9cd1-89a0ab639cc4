package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TimeDiff;
import com.sankuai.wallemonitor.risk.center.infra.enums.CompareEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.velocity.runtime.parser.node.ASTIntegerRange.IntegerRange;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskDriveOnTrafficLineRecordDOQueryParamDTO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 临时事件ID列表
     */
    @InQuery(field = "tmpCaseId")
    private List<String> tmpCaseIdList;

    /**
     * 车辆VIN码列表
     */
    @InQuery(field = "vin")
    private List<String> vinList;

    /**
     * 当前压线类型列表
     */
    @InQuery(field = "trafficLineType")
    private List<String> trafficLineTypeList;

    /**
     * 持续时长范围
     */
    @RangeQuery(field = "duration")
    private IntegerRange durationRange;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态列表
     */
    @InQuery(field = "status")
    private List<Integer> statusList;

    /**
     * 压线开始时间范围
     */
    @RangeQuery(field = "occurTime")
    private TimePeriod occurTimeRange;

    /**
     * 压线风险事件召回时间范围
     */
    @RangeQuery(field = "recallTime")
    private TimePeriod recallTimeRange;

    /**
     * 压线解除时间范围
     */
    @RangeQuery(field = "closeTime")
    private TimePeriod closeTimeRange;

    /**
     * 创建时间范围
     */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    /**
     * 更新时间范围
     */
    @RangeQuery(field = "updateTime")
    private TimePeriod updateTimeRange;

    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean isDeleted = false;

    /**
     * 排序
     */
    @OrderBy(field = "createTime")
    private OrderEnum orderByCreateTime;

    /**
     * 持续时间大于
     */
    @TimeDiff(unit = TimeUnit.SECONDS, bigField = "closeTime", smallField = "occurTime", compare = CompareEnum.GREAT_THAN)
    private Integer durationGreatThan;
}

