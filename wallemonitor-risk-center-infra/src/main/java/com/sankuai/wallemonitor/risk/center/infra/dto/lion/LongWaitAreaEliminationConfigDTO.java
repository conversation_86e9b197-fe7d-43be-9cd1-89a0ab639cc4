package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 延长等待区域淘汰策略
 * */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LongWaitAreaEliminationConfigDTO {

    /**
     * pastDays 天内没有有效case命中，去掉改区域
     *
     * */
    @Builder.Default
    private Integer pastDays = 3;


    @Builder.Default
    private Boolean open = false;

    /**
     * 往前找至少有多少case被标记
     * */
    @Builder.Default
    private Integer minimumCaseInMark = 500;


}
