package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskStrandingRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskStrandingRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskStrandingRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskStrandingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskStrandingRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskStrandingRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskStrandingRecordMapper, RiskStrandingRecordConvert, RiskStrandingRecord, RiskStrandingRecordDO> implements
        RiskStrandingRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询风险停滞事件记录
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskStrandingRecordDO> queryByParam(RiskStrandingRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询风险停滞事件记录 (分页)
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public Paging<RiskStrandingRecordDO> queryByParamByPage(RiskStrandingRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据临时事件ID查询风险停滞事件记录
     *
     * @param tmpCaseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskStrandingRecordDO getByTmpCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }

    /**
     * 保存风险停滞事件记录
     *
     * @param riskStrandingRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskStrandingRecordDO riskStrandingRecordDO) {
        super.save(riskStrandingRecordDO);
    }

    /**
     * 批量保存风险停滞事件记录
     *
     * @param riskStrandingRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskStrandingRecordDO> riskStrandingRecordDOList) {
        super.batchSave(riskStrandingRecordDOList);
    }
}