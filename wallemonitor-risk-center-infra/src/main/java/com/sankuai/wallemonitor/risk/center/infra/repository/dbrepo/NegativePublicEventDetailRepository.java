package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventDetailDOQueryParamDTO;
import java.util.List;

public interface NegativePublicEventDetailRepository {

    /**
     * 保存负向舆情事件详情
     *
     * @param negativePublicEventDetailDO
     */
    void save(NegativePublicEventDetailDO negativePublicEventDetailDO);

    /**
     * 根据事件id查询负外部性事件
     *
     * @return
     */
    NegativePublicEventDetailDO getByEventId(String eventId);

    /**
     * 根据参数查询
     *
     * @param paramDTO
     * @return
     */
    List<NegativePublicEventDetailDO> queryByParam(NegativePublicEventDetailDOQueryParamDTO paramDTO);


}
