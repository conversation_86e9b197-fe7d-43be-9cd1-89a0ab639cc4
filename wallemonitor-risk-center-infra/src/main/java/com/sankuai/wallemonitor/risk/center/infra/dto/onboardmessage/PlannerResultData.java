package com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlannerResultData implements Serializable {
    private static final long serialVersionUID = 1L;
    private ChosenReferenceLine chosenReferenceLine;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChosenReferenceLine implements Serializable {
        private ReferencePath referencePath;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReferencePath implements Serializable {
        private static final long serialVersionUID = 1L;
        private Curve curve;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Curve implements Serializable {
        private static final long serialVersionUID = 1L;
        private List<Point> point;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Point implements Serializable {
        private static final long serialVersionUID = 1L;
        private Double x;
        private Double y;
    }
}
