package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 扫码挪车事件信息表
 * </p>
 *
 * <AUTHOR> @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("risk_case_move_car_relation")
public class RiskCaseMoveCarRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * caseId
     */
    @TableField("case_id")
    @TableUnique
    private String caseId;

    /**
     * 事件ID
     */
    @TableField("event_id")
    @TableUnique
    private String eventId;

    /**
     * 上报人信息
     */
    @TableField("reporter")
    private String reporter;

    /**
     * 是否已经上报云安全工单
     */
    @TableField("reported_cloud_security")
    private Boolean reportedCloudSecurity;

    /**
     * 扩展信息
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新信息
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;


}
