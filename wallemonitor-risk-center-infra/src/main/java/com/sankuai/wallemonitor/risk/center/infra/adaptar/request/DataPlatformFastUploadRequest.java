package com.sankuai.wallemonitor.risk.center.infra.adaptar.request;


import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据平台快速上传数据请求参数类
 * @see https://km.sankuai.com/collabpage/2548935694
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataPlatformFastUploadRequest {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 上传数据开始时间
     */
    private Date begin;

    /**
     * 上传数据结束时间
     */
    private Date end;

    /**
     * 上传数据包含模块
     */
    private List<String> modules;

    /**
     * 上传优先级
     */
    @Builder.Default
    private Integer priority = 100;

    /**
     * 任务类型
     */
    @Builder.Default
    private Integer taskType = 4;

    /**
     * 创建人，默认「烽火台」
     */
    @Builder.Default
    private String creator = "beacon_tower";

    /**
     * 请求来源
     */
    private String source;
}
