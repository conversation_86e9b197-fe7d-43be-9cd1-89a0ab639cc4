package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminCaseSortDataVO;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseSortData;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseSortDataDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class}, imports = {JacksonUtils.class,
        DatetimeUtil.class})
public interface CaseSortDataConvert extends SingleConvert<CaseSortData, CaseSortDataDO> {

    @Override
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "parseCaseSortDataExtInfo")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    CaseSortDataDO toDO(CaseSortData sortData);

    @Override
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "stringifyCaseSortDataExtInfo")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    CaseSortData toPO(CaseSortDataDO sortDataDO);

    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "stringifyCaseSortDataExtInfo")
    @Mapping(target = "createTime", expression = "java(DatetimeUtil.formatTime(caseSortDataDO.getCreateTime()))")
    @Mapping(target = "updateTime", expression = "java(DatetimeUtil.formatTime(caseSortDataDO.getUpdateTime()))")
    AdminCaseSortDataVO toVO(CaseSortDataDO caseSortDataDO);
}


