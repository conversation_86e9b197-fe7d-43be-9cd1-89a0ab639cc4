package com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector;

import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskErrorQueueRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 错误排队检测记录工厂类
 */
@Slf4j
@Component
public class RiskErrorQueueRecordFactory extends RiskDetectorRecordFactory<RiskErrorQueueRecordDO> {

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Override
    public RiskErrorQueueRecordDO init(DetectContextDTO detectContextDTO) {
        if (Objects.isNull(detectContextDTO)) {
            log.warn("init error queue record with null detect context");
            return null;
        }

        if (Objects.isNull(detectContextDTO.getRuntimeContext())) {
            log.warn("init error queue record with null runtime context");
            return null;
        }

        VehicleRuntimeInfoContextDO runtimeContextDO = detectContextDTO.toShortVehicleInfoSnapShot();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, runtimeContextDO.getVin(),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.ERROR_QUEUE, runtimeContextDO.getLastUpdateTime());
        
        RiskErrorQueueRecordDO errorQueueRecordDO = RiskErrorQueueRecordDO.builder()
                .tmpCaseId(caseId)
                .type(RiskCaseTypeEnum.ERROR_QUEUE)
                .vin(runtimeContextDO.getVin())
                .duration(0)
                .status(DetectRecordStatusEnum.PROCESSING)
                .vehicleRuntimeInfoSnapshot(runtimeContextDO)
                .occurTime(runtimeContextDO.getLastUpdateTime())
                .isCrossLine((Boolean) detectContextDTO.get("isCrossLine"))
                .isInRouteLane((Boolean) detectContextDTO.get("isInFCRouteLane"))
                .hasObstacle((Boolean) detectContextDTO.get("isHasObstacle"))
                .build();

        return errorQueueRecordDO;
    }
} 