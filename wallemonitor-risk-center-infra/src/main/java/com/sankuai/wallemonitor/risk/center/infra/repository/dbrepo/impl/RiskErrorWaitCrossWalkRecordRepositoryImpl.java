package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskErrorWaitCrossWalkRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskErrorWaitCrossWalkRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.convert.RiskErrorWaitCrossWalkRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskErrorWaitCrossWalkRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskErrorWaitCrossWalkRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.repository.entity.RiskErrorWaitCrossWalkRecordDO;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 错误让行检测记录仓储实现
 */
@Component
@Slf4j
public class RiskErrorWaitCrossWalkRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskErrorWaitCrossWalkRecordMapper, RiskErrorWaitCrossWalkRecordConvert, RiskErrorWaitCrossWalkRecord, RiskErrorWaitCrossWalkRecordDO>
        implements RiskErrorWaitCrossWalkRecordRepository {

    // 常量定义
    private static final String UK_TMP_CASE_ID = "tmp_case_id";

    /**
     * 根据参数查询列表
     *
     * @param paramDTO 查询参数
     * @return 数据对象列表
     */
    @Override
    @RepositoryQuery
    public List<RiskErrorWaitCrossWalkRecordDO> queryByParam(RiskErrorWaitCrossWalkRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据临时事件ID查询对象
     *
     * @param tmpCaseId 临时事件ID
     * @return 数据对象
     */
    @Override
    @RepositoryQuery
    public RiskErrorWaitCrossWalkRecordDO getByTmpCaseId(String tmpCaseId) {
        List<UniqueKeyDTO> keys = new ArrayList<>();
        keys.add(UniqueKeyDTO.builder().columnPOName(UK_TMP_CASE_ID).value(tmpCaseId).build());
        return super.getByUniqueId(keys);
    }

    /**
     * 分页查询
     *
     * @param paramDTO 查询参数
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    @Override
    @RepositoryQuery
    public Paging<RiskErrorWaitCrossWalkRecordDO> queryByParamByPage(
            RiskErrorWaitCrossWalkRecordDOQueryParamDTO paramDTO, Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 保存对象
     *
     * @param riskErrorWaitCrossWalkRecordDO 数据对象
     */
    @Override
    @RepositoryExecute
    public void save(RiskErrorWaitCrossWalkRecordDO riskErrorWaitCrossWalkRecordDO) {
        if (riskErrorWaitCrossWalkRecordDO == null) {
            return;
        }
        super.save(riskErrorWaitCrossWalkRecordDO);
    }

    /**
     * 批量保存对象
     *
     * @param riskErrorWaitCrossWalkRecordDOList 数据对象列表
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskErrorWaitCrossWalkRecordDO> riskErrorWaitCrossWalkRecordDOList) {
        super.batchSave(riskErrorWaitCrossWalkRecordDOList);
    }
}