package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleObstacleInfoDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ObstacleInfoGroupDTO {

    /**
     * 正前方障碍物
     */
    private VehicleObstacleInfoDTO aheadObstacle;

    /**
     * 前方范围障碍物
     */
    @Builder.Default
    private List<VehicleObstacleInfoDTO> aheadObstacleList = new ArrayList<>();

    /**
     * 正后方障碍物
     */
    private VehicleObstacleInfoDTO behindObstacle;

    /**
     * 后方范围障碍物
     */
    @Builder.Default
    private List<VehicleObstacleInfoDTO> behindObstacleList = new ArrayList<>();

}
