package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PublicEventDetailCreateParamDTO {


    private List<String> vins;
    private List<String> vehicleIds;
    private String occurTime;
    private String city;
    private String riskCaseId;
    private String title;
    private Integer driverMode;
    private String discoveryType;
    private Integer handleType;
    private String requestHelpTime;
    private String startHandleTime;
    private String finishTime;
    private String groupId;

}
