package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseMoveCarRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseMoveCarRelationDOQueryParamDTO;
import java.util.List;

public interface RiskCaseMoveCarRelationRepository {

    /**
     * 保存扫码挪车事件
     *
     * @param riskCaseMoveCarRelationDO
     */
    void save(RiskCaseMoveCarRelationDO riskCaseMoveCarRelationDO);

    /**
     * 根据参数查询扫码挪车事件信息
     *
     * @param paramDTO
     * @return
     */
    List<RiskCaseMoveCarRelationDO> queryByParam(RiskCaseMoveCarRelationDOQueryParamDTO paramDTO);


}
