package com.sankuai.wallemonitor.risk.center.infra.exception;

import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;

public class HdMapTimeOutException extends ErrorCodeException {

    public HdMapTimeOutException(ResponseCodeEnum responseCode) {
        super(responseCode.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getMessage());
    }

    public HdMapTimeOutException(ResponseCodeEnum responseCode, String extMsg) {
        super(responseCode.getCode(), extMsg);
    }

    public HdMapTimeOutException(String message) {
        super(ResponseCodeEnum.SYSTEM_ERROR.getCode(), message);
    }
}
