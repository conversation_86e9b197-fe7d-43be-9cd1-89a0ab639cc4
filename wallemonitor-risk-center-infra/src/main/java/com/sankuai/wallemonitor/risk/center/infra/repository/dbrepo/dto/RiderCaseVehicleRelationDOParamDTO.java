package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.BelowTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.LimitQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class RiderCaseVehicleRelationDOParamDTO {

    /**
     * 外部事件ID
     */
    private String eventId;
    /**
     * 外部事件ID列表
     */
    @InQuery(field = "eventId")
    private List<String> eventIdList;

    /**
     * 风险caseId
     */
    private String caseId;

    /**
     * 风险caseId列表
     */
    @InQuery(field = "caseId")
    private List<String> caseIdList;

    /**
     * 车架号
     */
    private String vin;
    /**
     * 车架号vin列表
     */
    @InQuery(field = "vin")
    private List<String> vinList;

    /**
     * 发生时间
     */
    private String sideBySideTimestamp;

    /**
     * traceId
     */
    private String traceId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 是否删除,如果需要不关注删除状态时，需要修改改字段为NULL
     */
    @Builder.Default
    private Boolean isDeleted = false;


    /**
     * 时间戳小于某个值
     */
    @BelowTo(field = "milliBeginTime")
    private Date milliBeginTimeBelow;

    /**
     * 创建时间大于某个值
     */
    @GreatTo(field = "createTime")
    private Date createTimeGrateTo;


    /**
     * 排序
     */
    @OrderBy(field = "milliBeginTime")
    private OrderEnum orderByMilliBeginTime;

    /**
     * 限制
     */
    @LimitQuery
    private Integer limit;

    /**
     * vhrMode
     */
    private String vhrMode;

}
