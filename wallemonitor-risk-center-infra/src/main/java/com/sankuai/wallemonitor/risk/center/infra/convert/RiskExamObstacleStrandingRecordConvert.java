package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskExamObstacleStrandingRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskExamObstacleStrandingRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 考试障碍物停滞事件Convert
 */
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskExamObstacleStrandingRecordConvert extends
        SingleConvert<RiskExamObstacleStrandingRecord, RiskExamObstacleStrandingRecordDO> {

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusEnum")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseTypeEnum")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "parseVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "parseVehicleRuntimeInfoContextDO")
    RiskExamObstacleStrandingRecordDO toDO(RiskExamObstacleStrandingRecord record);

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusInteger")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "serializeVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "serializeVehicleRuntimeInfoContextDO")
    RiskExamObstacleStrandingRecord toPO(RiskExamObstacleStrandingRecordDO recordDO);
} 