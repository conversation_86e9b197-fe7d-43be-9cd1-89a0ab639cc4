package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TimePeriod {


    /**
     * 起始时间
     */
    private Date beginDate;


    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * build
     */
    public static TimePeriod build(Date beginDate, Date endDate) {
        return TimePeriod.builder().beginDate(beginDate).endDate(endDate).build();
    }

    /**
     * 检查时间范围是否合法 ，必须有上下界
     *
     * @return
     */
    public boolean checkTimeBetween() {
        return (endDate != null && beginDate != null) && !DatetimeUtil.isAfter(
                beginDate, endDate);
    }

    /**
     * 检查传入时间是否在时间内
     *
     * @return
     */
    public boolean checkTimeBetween(Long timestamp) {
        return timestamp != null && timestamp >= beginDate.getTime() && timestamp <= endDate.getTime();

    }

}
