package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OperateEnterActionEnum {

    VEHICLE_GRAY_STRATEGY_FILTER_ENTRY("车辆灰度策略过滤"),

    // 堵路事件处理
    RISK_EVENT_CONSUMER_ENTER_TRAFFIC_BLOCK("接收堵路事件MQ"),

    RISK_EVENT_CONSUMER_ENTER_TRAFFIC_BLOCK_HANDLE("处理堵路事件MQ"),

    RISK_EVENT_CONSUMER_ENTER("接收风险MQ"),
    VEHICLE_DATA_CONSUMER_ENTER("接受车辆事件"),
    MANUAL_RISK_CASE_OPERATE_ENTER("人工处置风险事件"),

    RISK_CASE_DETECT_CRANE("定时创建播报"),

    RISK_CASE_CALC_CRANE("定时统计播报"),

    RISK_CASE_MAPPING_ENTER("定时统计播报"),

    RISK_CASE_CREATE_OR_UPDATE_MESSAGE_ENTRY("创建或者更新消息"),

    // Thrift API : admin
    THRIFT_ADMIN_MARK_ENTRY("后台人工标注入口"),

    THRIFT_ADMIN_CHANGE_RISK_LEVEL("修改风险等级"),

    THRIFT_ADMIN_GET_CASE_DETAIL_ENTRY("后台请求case详情入口"),

    THRIFT_ADMIN_ADD_SAFETY_AREA_ENTRY("添加安全区"),

    THRIFT_ADMIN_DELETE_SAFETY_AREA_ENTRY("删除安全区"),

    THRIFT_ADMIN_LIST_CASE_ENTRY("后台获取case列表入口"),

    MAPPING_RISK_TRACE_CONSUMER_ENTER("接收风险关联MQ"),

    RISK_MRM_PROCESS_CONSUMER_ENTER("接收风险处置MQ"),

    CLOSE_UNHANDLED_RISK_CASE_CRANE("定时关闭未处理风险事件"),
    USER_AUTHENTICATION_GET_TOKEN("小程序用户鉴权获取token"),
    REPORT_FEEDBACK("上报反馈"),
    GET_USER_NOTICE("获取用户须知"),
    CONFIRM_USER_NOTICE("确认用户阅读须知"),
    GET_TOKEN("微信鉴权获取token"),

    REPORTER_FEEDBACK_RECORD("上报反馈记录"),
    RISK_CASE_CALL_MRM("呼叫云控"),

    RISK_CHECKING_QUEUE_CRANE("风险预检队列定时执行任务"),

    RISK_LEVEL_CRANE("风险等级定时任务"),

    RISK_CASE_RISK_CASE_RELEASE_MESSAGE_ENTRY("风险事件解除信号处理入口"),

    RISK_CASE_FAST_UPLOAD_PROCESS_MESSAGE_ENTRY("数据平台快速上传触发入口"),

    HIGH_NEGATIVE_FAST_UPLOAD_PROCESS_MESSAGE_ENTRY("高负向快传触发入口"),

    RISK_CASE_VEHICLE_RISK_STATUS_QUERY_ENTRY("车辆风险状态查询入口"),

    MANUAL_CALL_MRM("人工呼叫云控"),

    AUTO_MARK_RISK_CASE_PROCESS("解除风险时刻标注风险事件"),

    IMPROPER_STRANDING_CASE_CRANE("定时更新风险事件原因"),

    RISK_CHECKING_QUEUE_STATUS_ENTRY("风险事件检查队列状态处理入口"),

    IMPROPER_STRANDING_CASE_TRANSFORM_ENTRY("停滞不当事件转换入口"),

    CASE_LOCATION_CREATE("风险定位创建入口"),

    CASE_OBS_UPDATE("风险持续期间车辆周围障碍物"),


    MARK_TRANSFORM_ENTRY("标注转换入口"),

    RISK_CHECKING_QUEUE_ITEM_BE_CHECKING_ENTRY("风险预检检查入口"),

    RISK_AUTO_MARK_TRIGGER_ENTRY("风险预检标注入口"),

    RISK_CASE_CALL_MRM_STATUS_CRANE("检查风险事件呼叫坐席状态定时任务"),

    AUTO_MARK_CONSUMER_ENTRY("自动标注消费组"),

    VEHICLE_RUNTIME_INFO_CONTEXT_UPDATED_PROCESSOR_ENTRY("车辆运行时数据更新处理入口"),

    VEHICLE_RUNTIME_INFO_COUNTER_UPDATED_PROCESSOR_ENTRY("车辆累计counter更新处理入口"),

    VEHICLE_LOCATION_UPDATED_PROCESSOR_ENTRY("车辆定位更新入口"),


    TRIGGER_DYNAMIC_RECHECK_WHEN_TURNED_GREEN_PROCESSOR_ENTRY("变为绿灯时刻，触发动态预检"),
    STAGNATION_EVENT_ACCIDENT_TAG_MARK_PROCESSOR_ENTRY("停滞解除添加事故标签"),

    VEHICLE_RUNTIME_INFO_CONTEXT_UPDATE_CRANE_ENTRY("车辆运行时数据更新定时任务处理入口"),

    COMMON_ONBOARD_MESSAGE_CONSUMER_ENTRY("onboardMessage消息消费器"),

    VEHICLE_MONITOR_CONSUMER_ENTRY("数据总线消息消费器"),

    TRAFFIC_CONSUMER_ENTER("红绿灯消息消费器"),

    RISK_DETECT_RECORD_STATUS_UPDATED_ENTRY("风险事件检测记录确认处置入口"),

    STRANDING_TRIGGER_RISK_EVENT_MESSAGE_PROCESS_ENTRY("风险事件检测记录确认处置入口"),

    RISK_DETECT_RECORD_COUNTER_UPDATED_ENTRY("风险事件检测计数信息更新入口"),

    RISK_TRIGGER_SAFETY_ENTRY("风险满足停滞时触发云安全入口"),

    RISK_DISPOSED_CANCEL_SAFETY_ENTRY("风险解除时刻取消云安全"),
    GET_CASE_URL_DATA("获取风险事件详情链接数据"),

    PULL_URL_CARD_DATA("拉取风险事件详情链接卡片数据"),

    CHECK_RISK_CASE_LONG_TIME_CALL_MRM_CRANE("定时检查是否需要取消呼叫坐席"),

    REPORT_MOVE_CAR_ORDER("上报云分诊挪车工单"),
    CONSUMER_CLOUD_TRIAGE_EVENT_CHANGE_MESSAGE("分诊消息消费"),
    CONSUMER_FC_DRIVETASK_ROUTE_EVENT_MESSAGE("FC路由信息变更消息消费"),

    CLOUD_TRIAGE_NEGATIVE_EVENT_UPDATE_CONSUMER("高负向异常事件变更消息"),

    UPDATE_MRM_PROCESS_STATUS("更新云控处置状态"),

    KM_CREATE_EXPERIMENTAL_RESULT_CONTENT("创建实验结果学城文档"),

    CREATE_WORKSTATION_CASE_ENTRY("创建工作台case入口"),

    RAPTOR_REPORT_MESSAGE_ENTRY("raptor上报business指标入口"),

    WALLE_MONITOR_RISK_ONCALL_LIST_BINLOG_CONSUMER("消费oncall问题记录表的变更消息"),

    HISTORY_RISK_CASE_RELATED_WORKSTATION_ENTRY("历史风险事件关联工作台入口"),

    NEGATIVE_PUBLIC_EVENT_CREATE("负外部性事件创建"),

    NEGATIVE_PUBLIC_EVENT_CREATE_HANDLE_ENTRY("负外部性事件创建消息领域后处置"),
    EXAM_OBSTACLE_STRANDING_CREATE_HANDLE_ENTRY("考试障碍物停滞创建消息领域后处置"),

    NEGATIVE_PUBLIC_EVENT_STATUS_UPDATE_NOTICE_ENTRY("负外部性时间状态变更消息通知"),

    NEGATIVE_PUBLIC_EVENT_UPDATE_BASIC_INFO("更新负外部性事件基本信息"),

    NEGATIVE_PUBLIC_EVENT_UPDATE_QUALITATIVE_INFO("更新负外部性事件定性信息"),

    NEGATIVE_PUBLIC_EVENT_UPDATE_ATTRIBUTION_INFO("更新负外部性事件归因信息"),

    NEGATIVE_PUBLIC_EVENT_UPDATE_DISPOSAL_INFO("更新负外部性事件处置信息"),

    NEGATIVE_PUBLIC_EVENT_UPDATE_REVIEW_INFO("更新负外部性事件复盘信息"),

    NEGATIVE_PUBLIC_EVENT_FILTER_LIST("筛选负外部性事件列表"),

    NEGATIVE_PUBLIC_EVENT_QUERY_DETAIL("查询负外部性事件详情"),

    NEGATIVE_PUBLIC_EVENT_QUERY_NOTICE_CRANE("查询负外部性事件通知定时任务"),

    VEHICLE_RUNTIME_INFO_CONTEXT_UPDATE_FROM_DATA_BUS_CRANE("使用数据总线进行车辆运行上下文更新定时任务处理入口"),

    RISK_CASE_CALCULATE_SEAT_INTERVENTION_TIME_PROCESS("计算风险事件坐席介入时间处理"),

    RISK_CASE_CALCULATE_SEAT_RESPONSE_TIME_PROCESS("计算风险事件烽火台呼叫坐席连入时间处理"),

    RISK_CASE_DISPOSED_INFO_UPDATE_CRANE("风险事件处置信息更新定时任务"),

    CALL_SECURITY_SYSTEM_CRANE("保障系统呼叫定时任务"),

    CANCEL_CALL_SECURITY_SYSTEM_CRANE("保障系统取消呼叫定时任务"),

    CHECK_RISK_CASE_STAGNATION_STATUS_CRANE("停滞不当事件终态对账定时任务"),

    RISK_DELAY_POLYGON_UPDATE_CRANE("获取延迟等待区定时任务"),

    RISK_DELAY_POLYGON_ELIMINATION_CRANE("淘汰延迟等待区定时任务"),

    UPLOAD_RISK_REPORTER("停滞不当上报打点"),

    COMMON_CASE_CONSUMER_ENTRY("通用风险事件消费器"),

    RISK_CASE_AGGREGATE_ALERT_PROCESS_ENTRY("风险事件聚合告警处理入口"),

    ALERT_UPGRADE_CRANE("风险告警升级定时任务"),

    CARD_CALLBACK_ENTRY("卡片回调入口"),

    UPGRADE_CARD_PROCESSED_DISABLE_ORIGINAL_CARD_ENTRY("升级卡片处理完成后禁用原始卡片按钮入口");


    private String name;
    private boolean isConsumer;

    OperateEnterActionEnum(String name) {
        this.name = name;
        this.isConsumer = true;
    }

    OperateEnterActionEnum(boolean isConsumer, String name) {
        this.name = name;
        this.isConsumer = isConsumer;
    }

    public boolean isConsumer() {
        return isConsumer;
    }

}
