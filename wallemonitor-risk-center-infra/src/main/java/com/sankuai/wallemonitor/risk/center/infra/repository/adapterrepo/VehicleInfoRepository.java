package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo;


import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import java.util.List;

/**
 * 车辆信息仓储层
 */
public interface VehicleInfoRepository {

    /**
     * 根据VIN列表查询车辆信息
     *
     * @param vinList
     * @return
     */
    List<VehicleInfoDO> queryByVinList(List<String> vinList);

    /**
     * 根据VIN查询车辆信息
     *
     * @param vin
     * @return
     */
    VehicleInfoDO getByVin(String vin);

    /**
     * 根据CarAccountList查询车辆vin，例如 ["s20-191","s20-194","s20-250"]
     *
     * @param carAccountList
     * @return
     */
    List<String> getVinByCarAccountList(List<String> carAccountList) throws Exception;
}
