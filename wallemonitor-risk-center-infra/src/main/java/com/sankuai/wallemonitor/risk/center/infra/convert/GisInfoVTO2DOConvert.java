package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GisInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GisInfoVTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 同字段转换
 */
@Mapper(componentModel = "spring", imports = {EnumsConvertMapper.class}, uses = {EnumsConvertMapper.class})
public interface GisInfoVTO2DOConvert extends SingleConvert<GisInfoVTO, GisInfoDO> {

    List<GisInfoDO> toDOList(List<GisInfoVTO> vtoList);


    @Override
    @Mapping(source = ".", target = "position", qualifiedByName = "toPosition")
    GisInfoDO toDO(GisInfoVTO gisInfoVTO);

}
