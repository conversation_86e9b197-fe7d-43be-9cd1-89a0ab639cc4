package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEventDetail;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", imports = {EnumsConvertMapper.class}, uses = {EnumsConvertMapper.class})
public interface NegativePublicEventDetailConvert extends
        SingleConvert<NegativePublicEventDetail, NegativePublicEventDetailDO> {

    @Override
    @Mapping(source = "nature", target = "nature", qualifiedByName = "toNegativePublicEventNatureEnum")
    @Mapping(source = "level", target = "level", qualifiedByName = "toNegativePublicEventLevelEnum")
    @Mapping(source = "handleDegree", target = "handleDegree", qualifiedByName = "toNegativePublicEventHandleDegreeEnum")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "parseNegativePublicEventDetailExtInfoDTO")
    NegativePublicEventDetailDO toDO(NegativePublicEventDetail negativePublicEventDetail);

    @Override
    @Mapping(source = "nature", target = "nature", qualifiedByName = "toNegativePublicEventNature")
    @Mapping(source = "level", target = "level", qualifiedByName = "toNegativePublicEventLevel")
    @Mapping(source = "handleDegree", target = "handleDegree", qualifiedByName = "toNegativePublicEventHandleDegree")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "serializeNegativePublicEventDetailExtInfo")
    NegativePublicEventDetail toPO(NegativePublicEventDetailDO negativePublicEventDetailDO);

}
