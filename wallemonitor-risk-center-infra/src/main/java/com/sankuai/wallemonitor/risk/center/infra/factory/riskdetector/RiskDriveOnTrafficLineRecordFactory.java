package com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector;

import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.LineType;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDriveOnTrafficLineRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class RiskDriveOnTrafficLineRecordFactory extends RiskDetectorRecordFactory<RiskDriveOnTrafficLineRecordDO> {

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Override
    public RiskDriveOnTrafficLineRecordDO init(DetectContextDTO detectContextDTO) {
        VehicleRuntimeInfoContextDO runtimeContextDO = detectContextDTO.toShortVehicleInfoSnapShot();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, runtimeContextDO.getVin(),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.DRIVE_ON_TRAFFIC_LINE,
                runtimeContextDO.getLastUpdateTime());
        return RiskDriveOnTrafficLineRecordDO.builder()
                .tmpCaseId(caseId)
                .vin(runtimeContextDO.getVin())
                .type(RiskCaseTypeEnum.DRIVE_ON_TRAFFIC_LINE)
                .trafficLineType(LineType.getByValue(runtimeContextDO.getDrivingOnTrafficLineType()))
                .duration(0)
                .status(DetectRecordStatusEnum.PROCESSING)
                .occurTime(runtimeContextDO.getLastUpdateTime())
                .build();
    }


}
