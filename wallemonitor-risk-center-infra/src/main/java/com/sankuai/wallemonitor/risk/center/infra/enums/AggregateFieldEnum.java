package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聚合字段枚举
 */
@Getter
@AllArgsConstructor
public enum AggregateFieldEnum {

    POI_NAME("poiName", "POI名称"),
    TYPE("type", "风险类型"),
    PLACE_CODE("placeCode", "地点编码"),
    PARKING_PLOT_ID("parkingPlotId", "泊位ID"),
    VIN("vin", "车辆VIN码");

    private final String code;
    private final String desc;

    public static AggregateFieldEnum getByCode(String code) {
        for (AggregateFieldEnum field : values()) {
            if (field.getCode().equals(code)) {
                return field;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的聚合字段
     */
    public static boolean isValidField(String code) {
        return getByCode(code) != null;
    }
}