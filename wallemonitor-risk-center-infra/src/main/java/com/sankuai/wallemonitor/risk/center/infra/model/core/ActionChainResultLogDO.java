package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActionChainResultLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * caseId
     */
    @DomainUnique
    private String caseId;

    /**
     * 版本
     */
    @DomainUnique
    private String markVersion;

    /**
     * 运行场景 MARK | PRE_CHECK
     */
    @DomainUnique
    private String scene;

    /**
     * 轮次
     */
    @DomainUnique
    private Integer round;

    /**
     * 运行时间
     */
    @Builder.Default
    private Date checkTime = new Date();

    /**
     * 检出场景
     */
    private String category;

    /**
     * 检出的action名称
     */
    @Builder.Default
    private String actionName = "";

    /**
     * 检出结果详情
     */
    @Builder.Default
    private String resultDetail = "";

    /**
     * 其余action检出结果详情
     */
    private String otherActionCheckDetails;

    /**
     * 执行耗时
     */
    @Builder.Default
    private Long duration = 0L;

    /**
     * 受检项数据快照
     */
    private String itemDataSnapshot;

    /**
     * 车辆vin
     */
    private String vin;

    /**
     * 车辆运行信息快照
     */
    private String vehicleRuntimeInfoSnapshot;

    /**
     * 拓展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最近更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    @Builder.Default
    private IsDeleteEnum isDeleted = IsDeleteEnum.NOT_DELETED;
}