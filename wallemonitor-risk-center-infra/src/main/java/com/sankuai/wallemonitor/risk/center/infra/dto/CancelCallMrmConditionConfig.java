package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CancelCallMrmConditionConfig {

    /**
     * 检查时长
     */
    private Integer checkDuration;
    /**
     * 呼叫时长
     */
    private Long callDuration;

    /**
     * 坐席连入次数
     */
    private Long mrmConnectCount;

    /**
     * 取消原因列表
     */
    private List<String> reasonList;

}
