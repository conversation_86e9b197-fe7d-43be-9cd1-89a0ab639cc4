package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;

/**
 * 定位来源枚举
 */
@AllArgsConstructor
@Getter
public enum LocationSourceEnum {

    AUTO_CAR_UTM("autocar_utm"),  // 高精融合定位
    GNSS_BEST_POS("gnss_bestpos"), // GNSS定位
    GNSS_GPGGA("gnss_gpgga"), // GNSS定位
    AUTO_CAR_UTM_CACHED("autocar_utm_cached"); // 缓存定位

    private String source;

    /**
     * 是否是gnss定位
     *
     * @param source
     * @return
     */
    public static Boolean isGnssLocation(String source) {
        if (StringUtils.isBlank(source)) {
            return false;
        }
        Set<String> gnssLocationSource = Sets.newHashSet(GNSS_BEST_POS.getSource(), GNSS_GPGGA.getSource());
        return gnssLocationSource.contains(source);
    }
}
