package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.github.davidmoten.rtree.Entry;
import com.github.davidmoten.rtree.RTree;
import com.github.davidmoten.rtree.geometry.Geometries;
import com.github.davidmoten.rtree.geometry.Geometry;
import com.github.davidmoten.rtree.geometry.Point;
import com.github.davidmoten.rtree.geometry.Rectangle;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.utils.GeometryUtil;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.SafetyAreaConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.SafetyAreaMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.SafetyArea;
import com.sankuai.wallemonitor.risk.center.infra.enums.MenderOperationTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ParkingAreaMethodsStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.SafetyAreaDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.SafetyAreaRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.SafetyAreaQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.CacheUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javafx.util.Pair;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@Slf4j
public class SafetyAreaRepositoryImpl extends
        AbstractMapperSingleRepository<SafetyAreaMapper, SafetyAreaConvert, SafetyArea, SafetyAreaDO> implements
        SafetyAreaRepository {

    /**
     * 批量保存
     *
     * @param safetyAreaDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<SafetyAreaDO> safetyAreaDOList) {
        super.batchSave(safetyAreaDOList);
        // 只要有一个是停车场区域，刷缓存
        boolean isParkingAreaIn = safetyAreaDOList.stream().anyMatch(x ->
                MenderOperationTypeEnum.getParkingAreas().contains(x.getType()));
        if (isParkingAreaIn) {
            this.areaElementCache.refresh(PARKING_AREA_CACHE_KEY);
        }
    }

    @Override
    @RepositoryQuery
    public List<SafetyAreaDO> queryByParam(SafetyAreaQueryParamDTO param) {
        return super.queryByParam(param);
    }

    @ConfigValue(LionKeyConstant.PARKING_AREA_RUNTIME_METHODS_SWITCH)
    private String runtimeVerifyParkingAreaSwitch;


    private static final String PARKING_AREA_CACHE_KEY = "PARKING_AREA"; // 停车场区域的缓存key


    // 查找指定区域的Rtree 缓存
    public final LoadingCache<String, RTree<Pair<String, PolygonDO>, Geometry>> areaElementCache =
            CacheBuilder.newBuilder().refreshAfterWrite(5, TimeUnit.HOURS)
                .build(new CacheLoader<String, RTree<Pair<String, PolygonDO>, Geometry>>() {
                    @Override
                    public RTree<Pair<String, PolygonDO>, Geometry> load(String s) throws Exception {
                        return loadElementCache(s);

                    }
                    @Override
                    public ListenableFuture<RTree<Pair<String, PolygonDO>, Geometry>> reload(String key,
                                                                                             RTree<Pair<String, PolygonDO>, Geometry> oldValue) {
                        // 异步刷新实现
                        return MoreExecutors.listeningDecorator(ParallelExecutor.getExecutor("area_loader"))
                            .submit(() -> {
                                try {
                                    return loadElementCache(key);
                                } catch (Exception e) {
                                    log.error("异步刷新地图失败", e);
                                    return oldValue;
                                }
                            });
                    }

                });

    // 加载区域信息
    private RTree<Pair<String, PolygonDO>, Geometry> loadElementCache(String key) {
        if (PARKING_AREA_CACHE_KEY.equals(key)){
            return loadParkingArea();
        }
        return null;
    }

    // 加载停车场区域信息
    private RTree<Pair<String, PolygonDO>, Geometry> loadParkingArea() {
        List<SafetyAreaDO> safetyAreaDOList = this.queryByParam(
                SafetyAreaQueryParamDTO.builder().typeList(MenderOperationTypeEnum.getParkingAreas()).createTimeCreateTo(DatetimeUtil.ZERO_DATE).build());

        RTree<Pair<String, PolygonDO>, Geometry> rTree = RTree.star().create();

        for (SafetyAreaDO safetyAreaDO : safetyAreaDOList) {
            PolygonDO polygonDO = Optional.ofNullable(safetyAreaDO.getPolygon()).map(SafetyAreaDO.Polygon::getPolygonDO).orElse(null);
            if (polygonDO == null) {
                continue;
            }
            Rectangle boundingRectanglePolygon = GeoToolsUtil.getBoundingRectanglePolygon(polygonDO);
            rTree = rTree.add(new Pair<>(safetyAreaDO.getAreaId(), polygonDO), boundingRectanglePolygon);
        }
        return rTree;
    }

    // 在指定区域内快查点
    public List<Pair<String, PolygonDO>> searchBoundingRectangle(String key, PositionDO positionDO) {
        Point searchPoint = Geometries.point(positionDO.getLongitude(), positionDO.getLatitude());
        try {
            RTree<Pair<String, PolygonDO>, Geometry> rTree = this.areaElementCache.get(key);
            Iterator<Entry<Pair<String, PolygonDO>, Geometry>> iterator = rTree.search(searchPoint).toBlocking().toIterable().iterator();
            List<Pair<String, PolygonDO>> result = new ArrayList<>();
            while (iterator.hasNext()) {
                result.add(iterator.next().value());
            }
            return result;
        } catch (ExecutionException e) {
            log.error("区域快查点失败 : positionDO = {}", positionDO);
            return Lists.newArrayList();
        }
    }

    @PostConstruct
    @SneakyThrows
    public void init() {
        try {
            areaElementCache.refresh(PARKING_AREA_CACHE_KEY);
        } catch (Exception e) {
            log.error("初始化 safety area 失败.. ", e);
        }
    }

    @Override
    public String verifyInParkingArea(PositionDO positionDO) {
        ParkingAreaMethodsStatusEnum status = ParkingAreaMethodsStatusEnum.getStatus(runtimeVerifyParkingAreaSwitch);
        if (status == ParkingAreaMethodsStatusEnum.OFF) {
            // 不开启加速， 直接返回V1结果
            return verifyInParkingAreaV1(positionDO);
        }
        if (status == ParkingAreaMethodsStatusEnum.ON) {
            // 开启加速，直接返回V2结果
            return verifyInParkingAreaV2(positionDO);
        }

        // 观察中，两种计算方式都要使用，比对结果后，返回V1结果
        String v1Result = verifyInParkingAreaV1(positionDO);
        String v2Result = verifyInParkingAreaV2(positionDO);
        if (!StringUtils.equals(v1Result, v2Result)) {
            log.info("比对停车场位置失败 : v1 = {}, v2 = {} location = {}", v1Result, v2Result, positionDO);
        }
        return v1Result;

    }

    /**
     * 判断是否在停车区
     *
     * @param location
     * @return
     */
    public String verifyInParkingAreaV2(PositionDO location) {
        if (Objects.isNull(location)) {
            return CharConstant.CHAR_EMPTY;
        }
        if (location.getLatitude() == null || location.getLongitude() == null) { // 经纬度数据异常，先召回
            return CharConstant.CHAR_EMPTY;
        }
        long start = System.currentTimeMillis();

        List<Pair<String, PolygonDO>> boundingRectangle = searchBoundingRectangle(PARKING_AREA_CACHE_KEY, location);

        String areaId = boundingRectangle.stream().filter(x -> x.getValue().isInPolygon(location))
                .findFirst().map(Pair::getKey).orElse(CharConstant.CHAR_EMPTY);

        try {
            log.info(
                    "SafetyAreaRepository#verifyInParkingArea V2, location = {}, parkingAreaId = {}, verifyCaseInParkingAreaDuration = {},  safetyAreaDOListNum = {}",
                    location,
                    areaId,
                    System.currentTimeMillis() - start,
                    this.areaElementCache.get(PARKING_AREA_CACHE_KEY).size());
        } catch (ExecutionException e) {
            log.error("element cache get size error, {}", e.toString());
        }

        return areaId;
    }

    public String verifyInParkingAreaV1(PositionDO location) {
        if (Objects.isNull(location)) {
            return CharConstant.CHAR_EMPTY;
        }
        List<SafetyAreaDO> safetyAreaDOList = CacheUtils.doCache(this, 2, TimeUnit.HOURS).queryByParam(
                SafetyAreaQueryParamDTO.builder().typeList(MenderOperationTypeEnum.getParkingAreas()).createTimeCreateTo(DatetimeUtil.ZERO_DATE).build());
        if (CollectionUtils.isEmpty(safetyAreaDOList)) {
            return CharConstant.CHAR_EMPTY;
        }
        if (location.getLatitude() == null || location.getLongitude() == null) { // 经纬度数据异常，先召回
            return CharConstant.CHAR_EMPTY;
        }
        Coordinate coordinate = new Coordinate(location.getLongitude(), location.getLatitude());
        long start = System.currentTimeMillis();
        AtomicReference<Integer> runSafetyAreaDONum = new AtomicReference<>(0);
        return safetyAreaDOList.stream()
                .filter(safetyAreaDO -> {
                    if (Objects.isNull(safetyAreaDO.getPolygon()) || CollectionUtils.isEmpty(
                            safetyAreaDO.getPolygon().getPointGcjList())) {
                        log.info("verifyInParkingArea, polygon is null");
                        return false;
                    }
                    runSafetyAreaDONum.getAndSet(runSafetyAreaDONum.get() + 1);
                    List<Coordinate> parkingArea = safetyAreaDO.getPolygon().getPointGcjList().stream()
                            .map(x -> new Coordinate(x.getLongitude(), x.getLatitude()))
                            .collect(Collectors.toList());

                    parkingArea.add(parkingArea.get(0)); // 需要拼成闭合区域才能计算
                    return GeometryUtil.inPolygon(coordinate, parkingArea);
                })
                .findFirst()
                //打印日志
                .map(s -> {
                    log.info(
                            "SafetyAreaRepository#verifyInParkingArea V1, location = {}, parkingAreaId = {}, verifyCaseInParkingAreaDuration = {}, runSafetyAreaDONum = {}, safetyAreaDOListNum = {}",
                            location,
                            s.getAreaId(),
                            System.currentTimeMillis() - start,
                            runSafetyAreaDONum, safetyAreaDOList.size());
                    Cat.logMetricForDuration("verifyCaseInParkingAreaDuration", System.currentTimeMillis() - start);
                    return s.getAreaId();
                })
                .orElse(CharConstant.CHAR_EMPTY);
    }

    @Override
    public SafetyAreaDO querySafetyAreaDOByPosition(PositionDO location) {
        if (Objects.isNull(location)) {
            return null;
        }
        List<SafetyAreaDO> safetyAreaDOList = CacheUtils.doCache(this, 2, TimeUnit.HOURS).queryByParam(
                SafetyAreaQueryParamDTO.builder().typeList(MenderOperationTypeEnum.getParkingAreas()).createTimeCreateTo(DatetimeUtil.ZERO_DATE).build());
        if (CollectionUtils.isEmpty(safetyAreaDOList)) {
            return null;
        }
        if (location.getLatitude() == null || location.getLongitude() == null) { // 经纬度数据异常，先召回
            return null;
        }
        Coordinate coordinate = new Coordinate(location.getLongitude(), location.getLatitude());
        long start = System.currentTimeMillis();
        AtomicReference<Integer> runSafetyAreaDONum = new AtomicReference<>(0);
        return safetyAreaDOList.stream()
                .filter(safetyAreaDO -> {
                    if (Objects.isNull(safetyAreaDO.getPolygon()) || CollectionUtils.isEmpty(
                            safetyAreaDO.getPolygon().getPointGcjList())) {
                        log.info("verifyInParkingArea, polygon is null");
                        return false;
                    }
                    runSafetyAreaDONum.getAndSet(runSafetyAreaDONum.get() + 1);
                    List<Coordinate> parkingArea = safetyAreaDO.getPolygon().getPointGcjList().stream()
                            .map(x -> new Coordinate(x.getLongitude(), x.getLatitude()))
                            .collect(Collectors.toList());

                    parkingArea.add(parkingArea.get(0)); // 需要拼成闭合区域才能计算
                    return GeometryUtil.inPolygon(coordinate, parkingArea);
                })
                .findFirst()
                //打印日志
                .map(s -> {
                    log.info(
                            "SafetyAreaRepository#querySafetyAreaDOByPosition V1, location = {}, parkingArea = {}, verifyCaseInParkingAreaDuration = {}, runSafetyAreaDONum = {}, safetyAreaDOListNum = {}",
                            location,
                            s.getAreaId(),
                            System.currentTimeMillis() - start,
                            runSafetyAreaDONum, safetyAreaDOList.size());
                    Cat.logMetricForDuration("verifyCaseInParkingAreaDuration", System.currentTimeMillis() - start);
                    return s;
                })
                .orElse(null);
    }

    private Map<String, List<SafetyAreaDO>> queryDelayRecallArea() {
        List<SafetyAreaDO> safetyAreaDOList = this.queryByParam(
                SafetyAreaQueryParamDTO.builder()
                        .typeList(Lists.newArrayList(MenderOperationTypeEnum.IMPROPER_STRANDING_DELAY_RECALL.getName()))
                        .build());
        return safetyAreaDOList.stream().filter(Objects::nonNull).filter(x -> x.getDescription() != null)
                .collect(Collectors.groupingBy(SafetyAreaDO::getDescription));
    }

    @Override
    public Boolean isInDelayRecallPolygon(String poiName, PositionDO positionDO, Date occurTime) {
        Map<String, List<SafetyAreaDO>> safetyAreas = CacheUtils.doCache(this, 30, TimeUnit.MINUTES).queryDelayRecallArea();
        if(safetyAreas == null) {
            return false;
        }
        if(!safetyAreas.containsKey(poiName)) {
            return false;
        }
        return safetyAreas.get(poiName).stream().anyMatch(area ->  area.isInTimeRange( occurTime.getHours()) && area.isInPolygon(positionDO));
    }

    @Override
    public Boolean isInDelayRecallPolygon(PositionDO positionDO, Date occurTime) {
        Map<String, List<SafetyAreaDO>> safetyAreas = CacheUtils.doCache(this, 30, TimeUnit.MINUTES).queryDelayRecallArea();
        if(safetyAreas == null) {
            return false;
        }
        return safetyAreas.values().stream().filter(CollectionUtils::isNotEmpty).anyMatch(
                poi -> poi.stream().anyMatch(area -> area.isInTimeRange( occurTime.getHours()) &&  area.isInPolygon(positionDO)));
    }

}
