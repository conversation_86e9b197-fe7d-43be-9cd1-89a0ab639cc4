package com.sankuai.wallemonitor.risk.center.infra.model.core;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class RiskStrandingRecordDO extends RiskDetectorRecordBaseDO {

    /**
     * 停滞发生的开始时间
     */
    private Date occurTime;
}