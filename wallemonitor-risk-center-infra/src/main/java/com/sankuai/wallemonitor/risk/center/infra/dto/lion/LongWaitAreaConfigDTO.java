package com.sankuai.wallemonitor.risk.center.infra.dto.lion;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LongWaitAreaConfigDTO {

    /**
     *  如果poi内点较少，创建边数为n的多边形区域
     * */
    @Builder.Default
    private Integer n = 4;



    /**
     *  多边形的半径系数 ： 半径 = k * 点到中心的最大距离
     * */
    @Builder.Default
    private Double k = 1.2;


    /**
     *  最小距离，所有点到中心的距离小于minR 时，将使用创建多边形的方式，创建长等待区域
     * */
    @Builder.Default
    private Double minR = 10.0;



    /**
     *  最大距离，点到中心的距离大于maxR 时，此点将不参与区域计算
     * */
    @Builder.Default
    private Double maxR = 50.0;


    /**
     * 开关
     * */
    @Builder.Default
    private Boolean open = true;

    /**
     * 处在这个poi中的最少points，小于这个数目的poi将不计算长等待区域
     * */
    @Builder.Default
    private Integer minNumPointInPoi = 5;

    /**
     * 查询过去几天的bad case
     * */
    @Builder.Default
    private Integer pastDays = 7;

    /**
     * 最小合并距离, 小于这个值的新点将被加入到之前的区域中
     * */
    @Builder.Default
    private Integer minConcatDistance = 30;

    /**
     * 查询大于多少秒的case
     * */
    @Builder.Default
    private Integer secondsLeast = 180;
}

