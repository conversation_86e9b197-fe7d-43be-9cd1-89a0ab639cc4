package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("negative_public_event")
public class NegativePublicEvent {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * eventId
     */
    @TableField("event_id")
    @TableUnique
    private String eventId;

    /**
     * 风险事件类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 事件描述
     */
    @TableField("event_desc")
    private String eventDesc;

    /**
     * 上报人
     */
    @TableField("reporter")
    private String reporter;

    /**
     * 群ID
     */
    @TableField("group_id")
    private String groupId;


    /**
     * 事件发生时间
     */
    @TableField("occur_time")
    private Date occurTime;

    /**
     * 负外部性事件感知时间
     */
    @TableField("perceive_time")
    private Date perceiveTime;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("district")
    private String district;

    /**
     * 扩展字段
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最近更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

}
