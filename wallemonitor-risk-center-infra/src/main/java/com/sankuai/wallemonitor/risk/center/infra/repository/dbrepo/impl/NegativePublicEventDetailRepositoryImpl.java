package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.NegativePublicEventDetailConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.NegativePublicEventDetailMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEventDetail;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventDetailRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventDetailDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NegativePublicEventDetailRepositoryImpl extends
        AbstractMapperSingleRepository<NegativePublicEventDetailMapper, NegativePublicEventDetailConvert, NegativePublicEventDetail, NegativePublicEventDetailDO>
        implements NegativePublicEventDetailRepository {

    /**
     * 唯一键-事件id
     */
    private static final String UK_NEGATIVE_PUBLIC_EVENT_EVENT_ID = "eventId";

    /**
     * 保存函数
     *
     * @param negativePublicEventDetailDO
     */
    @Override
    @RepositoryExecute
    public void save(NegativePublicEventDetailDO negativePublicEventDetailDO) {
        super.save(negativePublicEventDetailDO);

    }

    /**
     * 根据事件id查询负外部性事件
     *
     * @param eventId
     * @return
     */
    @Override
    @RepositoryQuery
    public NegativePublicEventDetailDO getByEventId(String eventId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_NEGATIVE_PUBLIC_EVENT_EVENT_ID)
                .value(eventId)
                .build()));
    }

    /**
     * 根据参数查询
     *
     * @param paramDTO
     * @return
     */
    @Override
    public List<NegativePublicEventDetailDO> queryByParam(NegativePublicEventDetailDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

}
