package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.sankuai.walleeve.utils.DatetimeUtil;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleCounterInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String rule;

    private Date startTime;

    private Date endTime;

    private Integer duration;

    /**
     * 更新持续时间
     */
    public void increaseDuration(Date updateTime) {
        handleUpdateDuration(updateTime);
    }


    /**
     * 完结
     *
     * @param finishTime
     */
    public void finishCount(Date finishTime) {
        if (endTime != null) {
            //已经是完结态
            return;
        }
        this.endTime = finishTime;
        handleUpdateDuration(finishTime);

    }

    private void handleUpdateDuration(Date updateTime) {
        this.duration = DatetimeUtil.getSecondsDiff(DatetimeUtil.dateToLocalDateTime(this.startTime),
                DatetimeUtil.dateToLocalDateTime(updateTime));
    }

    /**
     * 是否已经完结计数
     *
     * @return
     */
    public boolean isCountFinished() {
        return this.endTime != null;
    }

    /**
     * 获取一个和时间有交集的子计数器
     *
     * @param startTime 子计数器的开始时间
     * @param endTime   子计数器的结束时间
     * @return 子计数器对象
     */
    public VehicleCounterInfoDO getSubCounter(Date startTime, Date endTime) {
        Date nowDate = new Date();
        // 确保输入的开始时间不晚于结束时间
        if (startTime == null || endTime == null || startTime.after(endTime)) {
            return null;
        }

        // 如果当前计数器的结束时间不为空且早于输入的开始时间，或者当前计数器的开始时间晚于输入的结束时间，则没有交集
        if ((this.endTime != null && this.endTime.before(startTime)) || this.startTime.after(endTime)) {
            return null;
        }

        // 计算交集的开始时间和结束时间,如果未结束，子集也未结束
        Date intersectStart = this.startTime.before(startTime) ? startTime : this.startTime;
        Date intersectEnd = this.endTime == null ? null : (this.endTime.after(endTime) ? endTime : this.endTime);

        // 创建并返回子计数器
        return VehicleCounterInfoDO.builder()
                .rule(this.rule)
                .startTime(intersectStart)
                .endTime(intersectEnd)
                .duration(intersectEnd != null ? DatetimeUtil.getSecondsDiff(
                        DatetimeUtil.dateToLocalDateTime(intersectStart),
                        DatetimeUtil.dateToLocalDateTime(intersectEnd)) : DatetimeUtil.getSecondsDiff(
                        DatetimeUtil.dateToLocalDateTime(intersectStart),
                        DatetimeUtil.dateToLocalDateTime(nowDate)))
                .build();
    }
}
