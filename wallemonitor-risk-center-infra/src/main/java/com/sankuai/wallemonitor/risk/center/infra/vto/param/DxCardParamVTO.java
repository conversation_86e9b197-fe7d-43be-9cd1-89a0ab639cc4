package com.sankuai.wallemonitor.risk.center.infra.vto.param;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大象卡片消息入参
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DxCardParamVTO {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 群idList
     */
    private List<Long> groupIdList;

    /**
     * 消息参数
     */
    private String arguments;

    /**
     * 摘要信息
     */
    private String abstractText;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 外部唯一ID,可填充风险事件ID
     */
    private String outBizId;

    /**
     * 消息版本
     */
    private Long version;

    /**
     * 扩展字段
     */
    private List<Long> uidList;
}
