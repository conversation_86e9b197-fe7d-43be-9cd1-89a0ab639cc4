package com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils;

import com.google.common.base.CaseFormat;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.EmbeddedValueResolverAware;
import org.springframework.expression.BeanResolver;
import org.springframework.expression.EvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringValueResolver;

/**
 * <AUTHOR> lvzhensong
 * @Description : spring容器工具类
 * @Date 2022/8/24 21:06
 **/
@Slf4j
@Component("SpringUtils")
public class SpringUtils implements ApplicationContextAware, EmbeddedValueResolverAware, BeanResolver {

    private static ApplicationContext context;
    private static String environment;

    private static StringValueResolver stringValueResolver;


    public static void setContext(ApplicationContext context) {
        SpringUtils.context = context;
    }

    public static ApplicationContext getApplicationContext() {
        return context;
    }

    @Override
    public Object resolve(EvaluationContext evaluationContext, String beanName) {
        return context.getBean(beanName);
    }
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        setContext(applicationContext);
    }

    public static String getEnvironment() {
        return environment;
    }


    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) {
        return (T) context.getBean(name);
    }


    @SuppressWarnings("unchecked")
    public static <T> T getBean(T obj) {
        return (T) context.getBean(CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_CAMEL, obj.getClass().getSimpleName()));
    }


    public static <T> T getBean(Class<? extends T> clazz) {
        return (T) context.getBean(clazz);
    }


    public static <T> List<T> getBeanList(Class<? extends T> clazz) {
        Map<String, ? extends T> beanMap = context.getBeansOfType(clazz);
        if (MapUtils.isEmpty(beanMap)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(context.getBeansOfType(clazz).values());
    }


    /**
     * 获取beanName
     *
     * @param bean
     * @return
     */
    public static <T> String getBeanName(Object bean) {
        if (bean == null) {
            return CharConstant.CHAR_EMPTY;
        }
        Class<?> beanClass = bean.getClass();
        Map<String, ?> beanMap = context.getBeansOfType(beanClass);
        if (MapUtils.isEmpty(beanMap)) {
            return null;
        }
        for (Entry<String, ?> entry : beanMap.entrySet()) {
            if (bean.getClass().isAssignableFrom(entry.getValue().getClass())) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 动态获取配置文件中的值
     *
     * @param name
     * @return
     */
    public static String getPropertiesValue(String name) {
        try {
            name = "${" + name + "}";
            return stringValueResolver.resolveStringValue(name);
        } catch (Exception e) {
            log.error(String.format("当前环境变量中没有{%s}的配置", name));
            // 获取失败则返回null
            return null;
        }
    }


    /**
     * 获取应用key
     *
     * @return
     */
    public static String getAppKey() {
        return SpringUtils.getPropertiesValue(CommonConstant.APP_NAME_KEY);
    }

    /**
     * 找到spring中指定注解的bean
     *
     * @param objectClazz
     * @param aliasClass
     * @param <T>
     * @return
     */
    public static <T> List<T> getBeanWithAliases(Class<? extends T> objectClazz,
            Class<? extends Annotation> aliasClass) {
        Map<String, Object> aliasClassBeans = context.getBeansWithAnnotation(aliasClass);
        if (MapUtils.isEmpty(aliasClassBeans)) {
            return new ArrayList<>();
        }
        List<T> beans = new ArrayList<>();
        for (Map.Entry<String, Object> entry : aliasClassBeans.entrySet()) {
            Object object = entry.getValue();
            if (!objectClazz.isAssignableFrom(object.getClass())) {
                //不是关注的类型
                continue;
            }
            beans.add((T) object);
        }
        //返回
        return beans;
    }

    @Override
    public void setEmbeddedValueResolver(StringValueResolver stringValueResolver) {
        SpringUtils.stringValueResolver = stringValueResolver;
    }
}
