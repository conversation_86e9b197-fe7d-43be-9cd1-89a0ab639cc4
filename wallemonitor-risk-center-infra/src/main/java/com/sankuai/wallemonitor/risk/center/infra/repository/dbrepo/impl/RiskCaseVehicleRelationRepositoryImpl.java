package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseVehicleRelationConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCaseVehicleRelationMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseVehicleRelation;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.dal.TableUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 风险事件关联信息仓储实现
 */
@Component
@Slf4j
public class RiskCaseVehicleRelationRepositoryImpl extends
        AbstractMapperSingleRepository<RiskCaseVehicleRelationMapper, RiskCaseVehicleRelationConvert, RiskCaseVehicleRelation, RiskCaseVehicleRelationDO> implements
        RiskCaseVehicleRelationRepository {

    private static final String EVENT_ID_FIELD = "eventId";

    private static final String VIN_FIELD = "vin";

    private static final String CASE_ID_FIELD = "caseId";

    /**
     * 根据参数查询风险事件关联信息
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskCaseVehicleRelationDO> queryByParam(RiderCaseVehicleRelationDOParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据事件ID和VIN查询风险事件
     *
     * @param eventId
     * @param vin
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskCaseVehicleRelationDO getByEventIdAndVin(String eventId, String vin) {
        return super.getByUniqueId(TableUtils.getTableUniqueKeyList(EVENT_ID_FIELD, eventId, VIN_FIELD, vin));
    }

    /**
     * 根据caseId查询风险事件关联信息
     *
     * @param caseId
     * @return
     */
    @Override
    public RiskCaseVehicleRelationDO getByCaseId(String caseId) {
        return super.getByUniqueId(TableUtils.getTableUniqueKeyList(CASE_ID_FIELD, caseId));
    }

    /**
     * 保存风险事件关联信息
     *
     * @param riskCaseVehicleRelationDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskCaseVehicleRelationDO riskCaseVehicleRelationDO) {
        super.save(riskCaseVehicleRelationDO);
    }

    /**
     * 批量保存
     *
     * @param riskCaseVehicleRelationDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskCaseVehicleRelationDO> riskCaseVehicleRelationDOList) {
        super.batchSave(riskCaseVehicleRelationDOList);
    }
}
