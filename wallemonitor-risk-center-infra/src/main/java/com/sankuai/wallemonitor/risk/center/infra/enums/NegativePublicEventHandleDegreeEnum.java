package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 负外部性事件解决程度枚举
 */
@AllArgsConstructor
@Getter
public enum NegativePublicEventHandleDegreeEnum {
    DEFAULT(0, "默认"),
    RESOLVED(1, "已解决"),
    HANDLED_PENDING_RESOLUTION(2, "已处置待解决"),
    NO_RESOLUTION_NEEDED(3, "无需解决");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static NegativePublicEventHandleDegreeEnum fromCode(Integer code) {
        for (NegativePublicEventHandleDegreeEnum item : NegativePublicEventHandleDegreeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

}
