package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
public interface CaseMarkInfoRepository {

    /**
     * 根据参数查询风险事件标注信息
     *
     * @param paramDTO
     * @return
     */
    List<CaseMarkInfoDO> queryByParam(CaseMarkInfoDOQueryParamDTO paramDTO);

    /**
     * 根据参数查询风险事件标注信息
     *
     * @param paramDTO
     * @return
     */
    Map<String, CaseMarkInfoDO> queryMapByParam(CaseMarkInfoDOQueryParamDTO paramDTO);

    /**
     * 根据风险id查询风险事件标注数据
     *
     * @param caseId
     * @return
     */
    CaseMarkInfoDO getByCaseId(String caseId);

    /**
     * 保存风险事件
     *
     * @param markInfoDO
     */
    void save(CaseMarkInfoDO markInfoDO);

    /**
     * 批量保存风险事件标注信息
     *
     * @param caseMarkInfoDOList
     */
    void batchSave(List<CaseMarkInfoDO> caseMarkInfoDOList);


}
