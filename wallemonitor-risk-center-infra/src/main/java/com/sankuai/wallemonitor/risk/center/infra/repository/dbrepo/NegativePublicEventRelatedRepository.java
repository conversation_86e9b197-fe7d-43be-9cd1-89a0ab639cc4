package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventRelatedDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventRelatedDOQueryParamDTO;
import java.util.List;

public interface NegativePublicEventRelatedRepository {

    /**
     * 保存负向舆情事件关联信息
     *
     * @param negativePublicEventRelatedDO
     */
    void save(NegativePublicEventRelatedDO negativePublicEventRelatedDO);

    /**
     * 根据参数查询关联信息
     *
     * @return
     */
    List<NegativePublicEventRelatedDO> queryByParam(NegativePublicEventRelatedDOQueryParamDTO paramDTO);

    /**
     * 批量保存
     *
     * @param relatedDOList
     */
    void batchSave(List<NegativePublicEventRelatedDO> relatedDOList);

    /**
     * 批量删除
     *
     * @param idList
     */
    void batchDeleteRelatedDO(List<Long> idList);

}
