package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 策略执行结果日志表
 *
 * <AUTHOR> @since 2024-06-12
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("action_chain_result_log")
public class ActionChainResultLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * caseId
     */
    @TableField("case_id")
    @TableUnique
    private String caseId;

    /**
     * 标注版本
     */
    @TableField("mark_version")
    @TableUnique
    private String markVersion;

    /**
     * 运行场景 MARK | PRECHEK
     */
    @TableField("scene")
    @TableUnique
    private String scene;

    /**
     * 轮次
     */
    @TableField("round")
    @TableUnique
    private Integer round;

    /**
     * 运行时间
     */
    @TableField("check_time")
    private Date checkTime;

    /**
     * 检出场景
     */
    @TableField("category")
    private String category;

    /**
     * 检出的action名称
     */
    @TableField("action_name")
    private String actionName;

    /**
     * 检出结果详情
     */
    @TableField("result_detail")
    private String resultDetail;

    /**
     * 其余action检出结果详情
     */
    @TableField("other_action_check_details")
    private String otherActionCheckDetails;

    /**
     * 执行耗时
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 受检项数据快照
     */
    @TableField("item_data_snapshot")
    private String itemDataSnapshot;

    /**
     * 车辆vin
     */
    @TableField("vin")
    private String vin;

    /**
     * 车辆运行信息快照
     */
    @TableField("vehicle_runtime_info_snapshot")
    private String vehicleRuntimeInfoSnapshot;

    /**
     * 拓展信息
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最近更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

}