package com.sankuai.wallemonitor.risk.center.infra.vto.param;

import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayPromptVTO.FridayPromptPartDTO;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayModelParamVTO {

    private String vin;
    private Date occurTime;
    private String caseId;
    private String modelName;
    private String appId;
    private Double topP;
    private Integer timeout;
    private Integer beforeTime;
    private List<FridayPromptPartDTO> systemPrompt;
    private List<FridayPromptPartDTO> userPrompt;
}