package com.sankuai.wallemonitor.risk.center.infra.model.core;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 错误绕行检测记录DO对象
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RiskErrorBypassRecordDO extends RiskDetectorRecordBaseDO {
    
    /**
     * 是否压线 0-否 1-是
     */
    private Boolean isCrossLine;

    /**
     * 是否在路由规划车道 0-否 1-是
     */
    private Boolean isInRouteLane;
} 