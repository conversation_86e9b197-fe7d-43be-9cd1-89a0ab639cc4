package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UpdateMrmProcessStatusCraneConfigDTO {

    /**
     * 风险来源列表
     */
    private List<Integer> sourceList;

    /**
     * 风险类型列表
     */
    private List<Integer> typeList;

}
