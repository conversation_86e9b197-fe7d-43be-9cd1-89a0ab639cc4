package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.MrmCallFilterRuleHitLogConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.MrmCallFilterRuleHitLogMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.MrmCallFilterRuleHitLog;
import com.sankuai.wallemonitor.risk.center.infra.model.core.MrmCallFilterRuleHitLogDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.MrmCallFilterRuleHitLogRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.MrmCallFilterRuleHitLogDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 坐席呼叫过滤规则命中纪录表仓储实现
 */
@Component
@Slf4j
public class MrmCallFilterRuleHitLogRepositoryImpl extends
        AbstractMapperSingleRepository<MrmCallFilterRuleHitLogMapper, MrmCallFilterRuleHitLogConvert, MrmCallFilterRuleHitLog, MrmCallFilterRuleHitLogDO>
        implements MrmCallFilterRuleHitLogRepository {

    @Override
    @RepositoryExecute
    public void save(MrmCallFilterRuleHitLogDO hitLogDO) {
        super.save(hitLogDO);
    }

    @Override
    @RepositoryExecute
    public void batchSave(List<MrmCallFilterRuleHitLogDO> hitLogDOList) {
        super.batchSave(hitLogDOList);
    }

    @Override
    @RepositoryQuery
    public List<MrmCallFilterRuleHitLogDO> queryByParam(MrmCallFilterRuleHitLogDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    @Override
    @RepositoryExecute
    public void batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        super.batchDelete(idList);
    }
} 