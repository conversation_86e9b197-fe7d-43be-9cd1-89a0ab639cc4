package com.sankuai.wallemonitor.risk.center.infra.constant;

public class CraneConstant {


    public static final String CALC_RISK_CASE_CRANE = "com.sankuai.wallemonitor.risk.center.riskCalcAndNotifyCrane";

    public static final String DETECT_RISK_CASE_AND_NOTIFY_CRANE = "com.sankuai.wallemonitor.risk.center.detectRiskCaseNotifyCrane";

    public static final String VEHICLE_RUNTIME_INFO_CONTEXT_UPDATE_CRANE = "com.sankuai.wallemonitor.risk.center.vehicleRuntimeInfoContextUpdateCrane";

    public static final String CLOSE_UNHANDLED_RISK_CASE_CRANE = "com.sankuai.wallemonitor.risk.center.closeUnhandledRiskCaseCrane";

    public static final String PARKING_AREA_INFO_SYNC_CRANE = "com.sankuai.wallemonitor.risk.center.parkingAreaInfoSyncCrane";

    /**
     * 风险case呼叫坐席定时任务
     */
    public static final String RISK_CASE_CALL_MRM_CRANE = "com.sankuai.wallemonitor.risk.center.riskCaseCallMrmCrane";

    /**
     * 停滞不当打点
     */
    public static final String RISK_REPORTER = "com.sankuai.wallemonitor.risk.center.risk.reporter";

    /**
     * 车辆停滞原因更新定时任务
     */
    public static final String IMPROPER_STRANDING_REASON_UPDATE_CRANE = "com.sankuai.wallemonitor.risk.center.improperStrandingReasonUpdateCrane";


    /**
     * 停滞检查队列定时任务
     */
    public static final String RISK_CHECKING_QUEUE_CRANE = "com.sankuai.wallemonitor.risk.center.riskCheckingQueueCrane";

    /**
     * 检查风险事件呼叫坐席状态定时任务
     */
    public static final String CHECK_RISK_EVENT_CALL_MRM_CRANE = "com.sankuai.wallemonitor.risk.center.checkRiskCaseCallMrmStatusCrane";


    /**
     * 风险等级crane
     */
    public static final String RISK_CASE_LEVEL_CRANE = "com.sankuai.wallemonitor.risk.center.riskCaseLevelCrane";

    /**
     * 上报云分诊挪车工单定时任务
     */
    public static final String REPORT_MOVE_CAR_ORDER_CRANE = "com.sankuai.wallemonitor.risk.center.reportMoveCarOrderCrane";

    /**
     * 检查长时间呼叫坐席定时任务
     */
    public static final String CHECK_LONG_TIME_CALL_MRM_CRANE = "com.sankuai.wallemonitor.risk.center.checkLongTimeCallMrmCrane";

    /**
     * 更新坐席处理状态定时任务
     */
    public static final String UPDATE_MRM_PROCESS_STATUS_CRANE = "com.sankuai.wallemonitor.risk.center.updateMrmProcessStatusCrane";

    /**
     * 历史风险停滞事件关联工作台case
     */
    public static final String HISTORY_RISK_CASE_RELATED_WORKSTATION_CRANE = "com.sankuai.wallemonitor.risk.center.historyRiskCaseRelatedWorkstationCrane";

    /**
     * 负外部性通知定时任务
     */
    public static final String NEGATIVE_EXTERNALITY_NOTIFICATION_CRANE = "com.sankuai.wallemonitor.risk.center.negativePublicEventNoticeCrane";

    /**
     * 车辆运行上下文从数据总线更新定时任务
     */
    public static final String VEHICLE_RUNTIME_INFO_CONTEXT_UPDATE_FROM_DATA_BUS_CRANE = "com.sankuai.wallemonitor.risk.center.vehicleRuntimeInfoContextUpdateFromDataBusCrane";

    /**
     * 风险case处置信息更新定时任务
     */
    public static final String RISK_CASE_DISPOSED_INFO_UPDATE_CRANE = "com.sankuai.wallemonitor.risk.center.riskCaseDisposedInfoUpdateCrane";


    /**
     * 停滞不当事件终态对账定时任务
     */
    public static final String IMPROPER_STRANDING_TERMINAL_STATUS_CHECK_CRANE = "com.sankuai.wallemonitor.risk.center.improperStrandingTerminalStatusCheckCrane";


    /**
     * 呼叫保障系统云控定时任务
     * */
    public static final String SECURITY_SYSTEM_CALL_CRANE = "com.sankuai.wallemonitor.risk.center.callSecuritySystemCloudControlCrane";

    /**
     * 取消呼叫保障系统云控定时任务
     * */
    public static final String SECURITY_SYSTEM_CANCEL_CRANE = "com.sankuai.wallemonitor.risk.center.cancelSecuritySystemCloudControlCrane";


    /**
     * 获取延迟等待区域的定时任务
     */
    public static final String DELAY_RECALL_AREA_CRANE = "com.sankuai.wallemonitor.risk.center.delayRecallAreaUpdateCrane";

    /**
     * 淘汰延迟等待区的定时任务
     */
    public static final String ELIMINATION_DELAY_RECALL_AREA_CRANE = "com.sankuai.wallemonitor.risk.center.delayRecallAreaEliminationCrane";

    /**
     * 风险告警升级定时任务
     */
    public static final String ALERT_UPGRADE_CRANE = "com.sankuai.wallemonitor.risk.center.alertUpgradeCrane";
}
