package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeLocationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeLocationDOQueryParamDTO;
import java.util.List;

public interface VehicleRuntimeInfoLocationRepository {

    List<VehicleRuntimeLocationDO> queryByParam(VehicleRuntimeLocationDOQueryParamDTO paramDTO);

    Paging<VehicleRuntimeLocationDO> queryByParamByPage(VehicleRuntimeLocationDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize);

    VehicleRuntimeLocationDO getByVin(String vin);

    void save(VehicleRuntimeLocationDO VehicleRuntimeLocationDO);

    void batchSave(List<VehicleRuntimeLocationDO> VehicleRuntimeLocationDOList);

}