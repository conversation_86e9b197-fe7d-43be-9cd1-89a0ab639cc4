package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HandleTypeEnum;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

/**
 * <p>
 * 舆情事件详情
 * </p>
 *
 * <AUTHOR> @since 2024-08-28
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PublicEventDetailDO {


    /**
     * 自增ID
     */
    private Long id;

    /**
     * 舆情事件ID（投诉工单ID）
     */

    private Long publicEventId;

    /**
     * 车架号列表
     */
    private List<String> vins;


    /**
     * 车牌号列表
     */
    private List<String> vehicleIds;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 实际发生时间
     */
    private Date occurTime;

    /**
     * 舆情类型
     */
    private String type;

    /**
     * 细分类型
     */
    private String subType;

    /**
     * 责任方
     */
    private String responsibleParty;

    /**
     * 是否发酵[0-否|1-是]
     */
    private Boolean isFermented;

    /**
     * 处置人
     */
    private String handlers;

    /**
     * 投诉描述
     */
    private String description;

    /**
     * 扩展字段
     */
    private String extra;
    /**
     * 组ID
     */
    private String groupId;
    /**
     * 事件发生城市
     */
    private String city;
    /**
     * 风险案例ID
     */
    private String riskCaseId;
    /**
     * 事件标题
     */
    private String title;
    /**
     * 发现类型
     */
    private String discoveryType;
    /**
     * 事件模式，使用DriverModeEnum枚举类型
     */
    private DriverModeEnum driveMode;
    /**
     * 处置方式，使用HandleTypeEnum枚举类型
     */
    private HandleTypeEnum handleType;
    /**
     * 请求帮助时间
     */
    private Date requestHelpTime;
    /**
     * 开始处理时间
     */
    private Date startHandleTime;
    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除[0-未删除|1-已删除]
     */
    private Boolean isDelete;
    /**
     * 类别
     */
    private String category;
    /**
     * 获取车辆全名
     *
     * @return
     */
    public List<String> getFullName() {
        if (CollectionUtils.isEmpty(vins) || CollectionUtils.isEmpty(vehicleIds)) {
            return new ArrayList<>();
        }
        List<String> fullNames = new ArrayList<>();
        for (int i = 0; i < vins.size(); i++) {
            fullNames.add(vehicleIds.get(i) + CharConstant.CHAR_VERTICAL + vins.get(i));
        }
        return fullNames;
    }
}
