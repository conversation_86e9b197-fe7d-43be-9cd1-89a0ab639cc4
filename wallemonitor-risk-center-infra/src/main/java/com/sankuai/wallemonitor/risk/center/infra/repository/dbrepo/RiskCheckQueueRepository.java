package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCheckQueueQueryParam;
import java.util.List;

public interface RiskCheckQueueRepository {

    /**
     * 根据组合条件查询
     *
     * @param param
     * @return
     */
    List<RiskCheckingQueueItemDO> queryByParam(RiskCheckQueueQueryParam param);

    /**
     * 保存
     *
     * @param riskCheckingQueueDO
     */
    void save(RiskCheckingQueueItemDO riskCheckingQueueDO);

    /**
     * 批量保存
     *
     * @param riskCheckingQueueItemDOList
     */
    void batchSave(List<RiskCheckingQueueItemDO> riskCheckingQueueItemDOList);

    /**
     * 根据caseId查询
     *
     * @param tmpCaseId
     * @return
     */
    RiskCheckingQueueItemDO getByCaseId(String tmpCaseId);


}
