package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.ActionChainResultLog;

public interface ActionChainResultLogMapper extends CommonMapper<ActionChainResultLog> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<ActionChainResultLog> getPOClass() {
        return ActionChainResultLog.class;
    }
}
