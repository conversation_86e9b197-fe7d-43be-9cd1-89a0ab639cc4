package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.BaAuthUtils;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppPropertiesConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FCAdapter {

    private static final String API_ROUTE_FOR_RENDER_QUERY = "/v2/drive_task/route_for_render/query";

    @Value(AppPropertiesConstant.CONFIG_VALUE_FC_DOMAIN)
    private String fcDomain;

    @Value(AppPropertiesConstant.CONFIG_VALUE_FC_BA_ACCESS_KEY)
    private String accessKey;

    @Value(AppPropertiesConstant.CONFIG_VALUE_FC_BA_SECRET_KEY)
    private String secretKey;

    /**
     * 获取车辆行驶路径的路由点
     * 
     * @param vin
     * @return
     */
    public List<PositionDO> batchGetRouteWaypoints(String vin) {
        try {
            if (StringUtils.isBlank(vin)) {
                return new ArrayList<>();
            }
            Map<String, String> params = new HashMap<>();
            params.put("vinList", vin);
            Map<String, String> headers = getBaAuthMap(HttpMethod.GET.name());
            EveHttpResponse<AutoDriveServiceResponse<List<FCDriveTaskRouteInfoDTO>>> response = HttpUtils.get(params,
                    fcDomain, API_ROUTE_FOR_RENDER_QUERY, headers,
                    new TypeReference<AutoDriveServiceResponse<List<FCDriveTaskRouteInfoDTO>>>() {});
            if (response.getCode() != HttpStatus.SC_OK) {
                return new ArrayList<>();
            }

            if (response.getData().getRet() != 0) {
                log.warn("FC返回路由线异常");
                return new ArrayList<>();
            }
            List<FCDriveTaskRouteInfoDTO> fcDriveTaskRouteInfoDTO = response.getData().getData();
            if (CollectionUtils.isEmpty(fcDriveTaskRouteInfoDTO)) {
                log.warn("FC未返回路由线");
                return new ArrayList<>();
            }
            Map<String, List<RoutePoint>> vinRoutePointsMap = fcDriveTaskRouteInfoDTO.stream()
                    .filter(route -> CollectionUtils.isNotEmpty(route.getRoutePoints())).collect(Collectors.toMap(
                            FCDriveTaskRouteInfoDTO::getVin, FCDriveTaskRouteInfoDTO::getRoutePoints, (o1, o2) -> o1));

            List<PositionDO> result = new ArrayList<>();
            vinRoutePointsMap.forEach((curVin, routePoints) -> {
                List<PositionDO> points = routePoints.stream()
                        .map(point -> GeoToolsUtil.utmToWgs84(point.getX(), point.getY())).collect(Collectors.toList());
                result.addAll(points);
            });
            return result;
        } catch (Exception e) {
            log.error("getRouteOpTaskId failed", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取BA认证头信息
     *
     * @param method
     * @return
     */
    private Map<String, String> getBaAuthMap(String method) {
        Map<String, String> baAuthMap = BaAuthUtils.genMWSAuthHeader(method, FCAdapter.API_ROUTE_FOR_RENDER_QUERY,
                accessKey, secretKey, CharConstant.CHAR_EMPTY);
        baAuthMap.put("X-Date", baAuthMap.get("Date"));
        return baAuthMap;
    }

    /**
     * 自动驾驶服务响应
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class AutoDriveServiceResponse<T> {

        /**
         * 响应码
         */
        private Integer ret;

        /**
         * 响应信息
         */
        private String msg;

        /**
         * 数据
         */
        private T data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class FCDriveTaskRouteInfoDTO {

        private String vin;

        private List<RoutePoint> routePoints; // 车辆没有正在运行的路由时该字段塞null

        private Long updateTimestamp;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class RoutePoint {

        private Double x;

        private Double y;

        private Double z;

        private Double routeS;

    }

}
