package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum LogCenterFieldEnum {

    AL_PROVIDER("al_provider"),

    TRACE_ID("traceId__"),
    AL_INVOKER("al_invoker"),
    AL_OPERATOR("al_operator"),
    AL_DOMAIN("al_domain_service"),
    AL_ADAPTER("al_adapter_service"),

    AL_PRODUCER("al_message_producer"),

    AL_REPOSITORY("al_repository"),
    AL_REQUEST("al_request"),

    AL_REQUEST_TIME("al_request_time"),
    AL_RESPONSE("al_response"),
    AL_RESPONSE_CODE("al_response_code"),
    AL_RESPONSE_MSG("al_response_msg"),
    AL_COST("al_cost"),

    OP_OPERATE_TIME("op_operate_time");

    String fieldCode;

}
