package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VehicleMonitorInfoDO {

    @JsonProperty("mrm_seat_role")
    private Integer mrmSeatRole;

    @JsonProperty("mrm_seat_status")
    private Integer mrmSeatStatus;

    @JsonProperty("online_status")
    private Integer onlineStatus;

    @JsonProperty("gear")
    private String gear;

    @JsonProperty("heading")
    private Double heading;

    @JsonProperty("latitude")
    private Double latitude;

    @JsonProperty("longitude")
    private Double longitude;

    @JsonProperty("kmph")
    @JsonAlias("speed")
    private Double speed;

    @JsonProperty("present_bat")
    private Integer presentBat;

    @JsonProperty("soc1")
    private Double soc1;

    @JsonProperty("soc2")
    private Double soc2;

    @JsonProperty("drive_status_enum")
    private Integer driveMode;

    @JsonProperty("position_source")
    private String positionSource;

    /**
     * 经纬度是否合法
     * 
     * @return
     */
    public boolean isPositionValid() {
        return Objects.nonNull(latitude) && Objects.nonNull(longitude) && latitude >= 0.0D && longitude >= 0.0D;
    }


}