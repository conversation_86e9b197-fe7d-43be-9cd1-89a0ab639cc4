package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 负外部性事件性质枚举
 */
@AllArgsConstructor
@Getter
public enum NegativePublicEventNatureEnum {
    REAL(1, "真实负外部性事件", EventGroup.REAL),
    FAKE(2, "不实外部性事件", EventGroup.FAKE),
    IRRELEVANT(3, "旧闻/混剪/重复/非自动车相关负外部性事件", EventGroup.OTHER),
    POSITIVE(4, "正面反馈或咨询", EventGroup.OTHER);

    private final Integer code;
    private final String desc;
    private final EventGroup group;

    /**
     * 对时间性质进行分组，用于后续的状态流转计算
     */
    public enum EventGroup {
        REAL,
        FAKE,
        OTHER
    }

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static NegativePublicEventNatureEnum fromCode(Integer code) {
        for (NegativePublicEventNatureEnum item : NegativePublicEventNatureEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据事件性质判断是否需要更新定因信息
     *
     * @param natureEnum
     * @return
     */
    public static Boolean needUpdateReasonInfo(NegativePublicEventNatureEnum natureEnum) {
        // 当前只有真实负外部性事件需要填写归因信息
        return Objects.equals(natureEnum, REAL);
    }

    /**
     * 根据事件性质判断是否需要更新处置信息
     *
     * @param natureEnum
     * @return
     */
    public static Boolean needUpdateHandleInfo(NegativePublicEventNatureEnum natureEnum) {
        // 当前只有真实负外部性事件和不实外部性事件需要填写处置信息
        return Objects.equals(natureEnum, REAL) || Objects.equals(natureEnum, FAKE);
    }

    /**
     * 根据事件性质判断是否需要发送处置信息
     *
     * @param natureEnum
     * @return
     */
    public static Boolean isNoNeedSendHandleInfo(NegativePublicEventNatureEnum natureEnum) {
        return Objects.equals(natureEnum, IRRELEVANT) || Objects.equals(natureEnum, POSITIVE);
    }

}
