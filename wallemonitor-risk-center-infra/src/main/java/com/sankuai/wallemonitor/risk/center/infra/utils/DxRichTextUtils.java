package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.sankuai.oa.card.toolkit.TextTransformer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 大象富文本工具类
 */
@Slf4j
public class DxRichTextUtils {

    /**
     * 支持的颜色枚举
     */
    @Getter
    @AllArgsConstructor
    public enum Color {
        GREEN("green"),
        RED("red"),
        GREY("grey"),
        YELLOW("yellow"),
        BLUE("blue"),
        DEFAULT("default"),
        /*            可以下颜色的大象版本，>= App 6.43/PC 7.7.0                */
        ORANGE("orange"),
        PURPLE("purple"),
        TEAL("teal"),
        PINK("pink"),
        COFFEE("coffee");

        private final String value;

        public static Color fromValue(String value) {
            if (value == null) {
                return DEFAULT;
            }

            for (Color color : values()) {
                if (color.value.equalsIgnoreCase(value)) {
                    return color;
                }
            }
            return DEFAULT;
        }
    }

    /**
     * 转换文本为斜体格式
     * 语法：*斜体*
     *
     * @param text 原始文本
     * @return 斜体格式文本
     */
    public static String toItalic(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        try {
            String escapedText = TextTransformer.escapeText(text);
            return "*" + escapedText + "*";
        } catch (Exception e) {
            log.error("转换斜体格式失败, text={}", text, e);
            return text;
        }
    }

    /**
     * 转换文本为粗体格式
     * 语法：**粗体**
     *
     * @param text 原始文本
     * @return 粗体格式文本
     */
    public static String toBold(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        try {
            String escapedText = TextTransformer.escapeText(text);
            return "**" + escapedText + "**";
        } catch (Exception e) {
            log.error("转换粗体格式失败, text={}", text, e);
            return text;
        }
    }

    /**
     * 创建文本链接
     * 语法：[显示文本|链接地址]
     *
     * @param displayText 显示文本
     * @param url         链接地址
     * @return 链接格式文本
     */
    public static String createLink(String displayText, String url) {
        if (displayText == null || displayText.isEmpty() || url == null || url.isEmpty()) {
            return displayText != null ? displayText : "";
        }

        try {
            String escapedDisplayText = TextTransformer.escapeText(displayText);
            String escapedUrl = TextTransformer.escapeText(url);
            return "[" + escapedDisplayText + "|" + escapedUrl + "]";
        } catch (Exception e) {
            log.error("创建文本链接失败, displayText={}, url={}", displayText, url, e);
            return displayText;
        }
    }

    /**
     * 设置文字颜色
     * 语法：<font color='颜色'>文本</font>
     *
     * @param text  文本内容
     * @param color 颜色枚举
     * @return 带颜色的文本
     */
    public static String setTextColor(String text, Color color) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        if (color == null) {
            log.warn("颜色参数为null, 将使用默认颜色");
            color = Color.DEFAULT;
        }

        try {
            String escapedText = TextTransformer.escapeText(text);
            return "<font color='" + color.getValue() + "'>" + escapedText + "</font>";
        } catch (Exception e) {
            log.error("设置文字颜色失败, text={}, color={}", text, color, e);
            return text;
        }
    }

    /**
     * 组合多种格式：粗体 + 颜色
     * 正确的嵌套顺序：**<font color='green'>文本</font>**
     *
     * @param text  文本内容
     * @param color 颜色枚举
     * @return 粗体且带颜色的文本
     */
    public static String toBoldWithColor(String text, Color color) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        if (color == null) {
            log.warn("颜色参数为null, 将使用默认颜色");
            color = Color.DEFAULT;
        }

        try {
            String escapedText = TextTransformer.escapeText(text);
            String coloredText = "<font color='" + color.getValue() + "'>" + escapedText + "</font>";
            return "**" + coloredText + "**";
        } catch (Exception e) {
            log.error("组合粗体颜色格式失败, text={}, color={}", text, color, e);
            return text;
        }
    }
} 