package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.dianping.cat.Cat;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ParallelExecutor {

    private static final Map<String, ExecutorService> executorMap = new HashMap<>();

    static {

        //cpu密集型，并行流使用
        executorMap.put("vehicle_runtime_info", TtlExecutors.getTtlExecutorService(
                Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        executorMap.put("vehicle_runtime_info_biz_status", TtlExecutors.getTtlExecutorService(
                Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        executorMap.put("is_check_action", TtlExecutors.getTtlExecutorService(
                Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        executorMap.put("is_mark_action", TtlExecutors.getTtlExecutorService(
                Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        executorMap.put("risk_detector", TtlExecutors.getTtlExecutorService(
                Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        executorMap.put("map_loader", TtlExecutors
                .getTtlExecutorService(Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        executorMap.put("multiAutoMark", TtlExecutors
                .getTtlExecutorService(Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        executorMap.put("obstacleAbstract", TtlExecutors.getTtlExecutorService(
                Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        executorMap.put("area_loader", TtlExecutors
                .getTtlExecutorService(Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        executorMap.put("mrm_filter", TtlExecutors
                .getTtlExecutorService(Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())));

        // 异步执行卡片回调
        executorMap.put("dx_callback_handler", TtlExecutors.getTtlExecutorService(
                Executors.newFixedThreadPool(Math.max(2, Runtime.getRuntime().availableProcessors() / 2))));

        // 批量查询区域适配数据线程池
        executorMap.put("mender_operation_data_loader", TtlExecutors.getTtlExecutorService(
                Executors.newFixedThreadPool(Math.max(2, Runtime.getRuntime().availableProcessors() / 2))));
    }

    public static ExecutorService getExecutor(String taskName) {
        return executorMap.get(taskName);
    }

    public static <T, R> List<R> executeParallelTasksAndGetResult(String taskName, List<T> tasks,
            Function<T, R> function) {
        ExecutorService executor = executorMap.get(taskName);
        CheckUtil.isNotNull(executor, "executor {} is null", taskName);

        // 创建一个CompletableFuture列表，每个CompletableFuture对应一个任务的执行和结果
        List<CompletableFuture<R>> futures = tasks.stream()
                .map(task -> CompletableFuture.supplyAsync(() -> function.apply(task), executor))
                .collect(Collectors.toList());

        // 使用allOf等待所有的CompletableFuture完成
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        try {
            // 等待所有任务完成
            allDoneFuture.join();
            // 收集所有任务的结果
            return futures.stream()
                    //默认结果是null
                    .map(c -> c.getNow(null)) // CompletableFuture::join 等待每个future完成并返回结果
                    //去掉null
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            Cat.logEvent("parallel_executor", "ExecutionException", "error", "");
            log.warn("Task execution failed: ", e);
            return Collections.emptyList(); // 在异常情况下返回空列表或者根据需要处理
        }
    }

    public static <T> void executeParallelTasks(String taskName, List<T> tasks,
            Consumer<T> action) {
        ExecutorService executor = executorMap.get(taskName);
        CheckUtil.isNotNull(executor, "executor {} is null", taskName);

        CompletableFuture<Void> allOf = CompletableFuture.allOf(
                tasks.stream()
                        .map(task -> CompletableFuture.runAsync(() -> executeTask(task, action), executor))
                        .toArray(CompletableFuture[]::new)
        );
        try {
            allOf.join();
        } catch (Exception e) {
            Cat.logEvent("parallel_executor", "ExecutionException", "error",
                    "");
            log.warn("Task execution failed: ", e);
        }
    }

    public static <D, T> void executeParallelTasks(String taskName, Map<D, T> tasks, BiConsumer<D, T> action) {
        ExecutorService executor = executorMap.get(taskName);
        CheckUtil.isNotNull(executor, "executor {} is null", taskName);

        CompletableFuture<Void> allOf = CompletableFuture.allOf(tasks.entrySet().stream()
                .map(taskEntry -> CompletableFuture
                        .runAsync(() -> executeTask(taskEntry.getKey(), taskEntry.getValue(), action), executor))
                .toArray(CompletableFuture[]::new));
        try {
            allOf.join();
        } catch (Exception e) {
            Cat.logEvent("parallel_executor", "ExecutionException", "error", "");
            log.warn("Task execution failed: ", e);
        }
    }

    private static <T> void executeTask(T task, Consumer<T> action) {
        try {
            action.accept(task);
        } catch (Exception e) {
            Cat.logEvent("parallel_executor", "TaskExecutionFailed", "error",
                    "");
            log.warn("任务执行失败:", e);
        }
    }

    private static <T, D> void executeTask(D key, T task, BiConsumer<D, T> action) {
        try {
            action.accept(key, task);
        } catch (Exception e) {
            Cat.logEvent("parallel_executor", "TaskExecutionFailed", "error", "");
            log.warn("任务执行失败:", e);
        }
    }

    private static <T, R> R executeTask(T task, Function<T, R> action) {
        try {
            return action.apply(task);
        } catch (Exception e) {
            Cat.logEvent("parallel_executor", "TaskExecutionFailed", "error",
                    "");
            log.warn("任务执行失败:", e);
            return null;
        }
    }

    /**
     * 异步执行单个任务，不等待结果，用于事件监听器的异步处理
     * fixme：会导致无法触发领域消息
     * @param taskName 任务名称
     * @param action   要执行的动作
     */
    public static void executeAsyncTask(String taskName, Runnable action) {
        ExecutorService executor = executorMap.get(taskName);
        CheckUtil.isNotNull(executor, "executor {} is null", taskName);

        CompletableFuture.runAsync(() -> {
            try {
                action.run();
            } catch (Exception e) {
                Cat.logEvent("parallel_executor", "AsyncTaskExecutionFailed", "error", "");
                log.warn("异步任务执行失败:", e);
            }
        }, executor);
    }
}
