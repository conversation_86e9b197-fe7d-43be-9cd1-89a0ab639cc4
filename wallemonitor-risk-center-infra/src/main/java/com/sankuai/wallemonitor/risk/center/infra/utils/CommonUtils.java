package com.sankuai.wallemonitor.risk.center.infra.utils;

import java.util.Set;
import org.apache.commons.collections.CollectionUtils;

public class CommonUtils {

    /**
     * 判断是否灰度
     *
     * @param value
     * @param valueSet
     * @param allValue
     * @param <T>
     * @return
     */
    public static <T> boolean isGray(T value, Set<T> valueSet, T allValue) {

        if (CollectionUtils.isEmpty(valueSet)) {
            return false;
        }
        return valueSet.contains(allValue) || valueSet.contains(value);
    }

}
