package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.PublicDetailConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.PublicEventDetailMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.PublicEventDetail;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.PublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.PublicDetailRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.PublicDetailDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 风险事件仓储实现
 */
@Component
@Slf4j
public class PublicDetailRepositoryImpl extends
        AbstractMapperSingleRepository<PublicEventDetailMapper, PublicDetailConvert, PublicEventDetail, PublicEventDetailDO> implements
        PublicDetailRepository {

    private static final String UK_EVENT_ID = "publicEventId";


    @Override
    @RepositoryQuery
    public List<PublicEventDetailDO> queryByParam(PublicDetailDOQueryParamDTO queryParamDTO) {
        return super.queryByParam(queryParamDTO);
    }

    @Override
    public PublicEventDetailDO getByEventId(Long eventId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_EVENT_ID)
                .value(eventId)
                .build()));
    }

    /**
     * 根据参数查询风险事件 (分页)
     *
     * @param paramDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    @RepositoryQuery
    public Paging<PublicEventDetailDO> queryByParamByPage(PublicDetailDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }
}
