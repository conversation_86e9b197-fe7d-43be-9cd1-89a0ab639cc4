package com.sankuai.wallemonitor.risk.center.infra.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum VHRModeEnum {

    VHR_EQUALS_ONE("=1", "vhr等于1"),

    VHR_GREAT_THAN_ONE(">1", "vhr大于1"),

    VHR_BELOW_TO_ONE("<1", "vhr大于1"),
    ;

    private String code;

    private String desc;

    /**
     * 根据code找VHR
     *
     * @param vhrTag
     * @return
     */
    public static VHRModeEnum findByCode(String vhrTag) {
        if (StringUtils.isBlank(vhrTag)) {
            return null;
        }
        for (VHRModeEnum vhrEnum : VHRModeEnum.values()) {
            if (vhrEnum.getCode().equals(vhrTag)) {
                return vhrEnum;
            }

        }
        return null;
    }
}
