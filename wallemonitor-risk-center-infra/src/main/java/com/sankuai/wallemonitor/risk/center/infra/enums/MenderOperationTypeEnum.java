package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum MenderOperationTypeEnum {

    PARKING_QUEUE_AREA("ParkingQueueArea"),
    PARKING_LOT_AREA("ParkingLotArea"),
    PARKING_AREA("ParkingArea"),
    AUTO_PARKING_AREA("AutoParkingArea"),
    IMPROPER_STRANDING_DELAY_RECALL("ImproperStrandingDelayRecall")
    ;

    private String name;

    public static List<String> getAllOperationTypeNames() {
        return Arrays.stream(MenderOperationTypeEnum.values())
                .map(MenderOperationTypeEnum::getName)
                .collect(Collectors.toList());
    }

    public static List<String> getParkingAreas() {
        return Arrays.stream(MenderOperationTypeEnum.values())
                .filter(x -> x != IMPROPER_STRANDING_DELAY_RECALL)
                .map(MenderOperationTypeEnum::getName)
                .collect(Collectors.toList());
    }


}
