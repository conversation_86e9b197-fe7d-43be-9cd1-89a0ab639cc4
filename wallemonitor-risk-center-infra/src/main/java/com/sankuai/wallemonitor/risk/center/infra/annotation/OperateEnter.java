package com.sankuai.wallemonitor.risk.center.infra.annotation;


import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作入口
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface OperateEnter {


    /**
     * 操作入口
     *
     * @return
     */
    OperateEnterActionEnum value();

}
