package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum HdMapLaneAreaTypeEnum {
    /**
     * 内部道路
     */
    LIVING_AREA("LIVING_AREA", HdMapEnum.LANE_POLYGON),

    /**
     * 普通道路
     */
    NORMAL("NORMAL", HdMapEnum.LANE_POLYGON),

    ;

    private final String value;

    private final HdMapEnum mapValue;

    public static String getMapNameByArea(String s) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        HdMapLaneAreaTypeEnum hdMapElementEnum = fromValue(s);
        if (Objects.isNull(hdMapElementEnum)) {
            return null;
        }
        return hdMapElementEnum.getMapValue().getValue();
    }

    public String getValue() {
        return value;
    }

    public static HdMapLaneAreaTypeEnum fromValue(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        for (HdMapLaneAreaTypeEnum type : HdMapLaneAreaTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}