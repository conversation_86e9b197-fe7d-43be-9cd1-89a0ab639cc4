package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import java.lang.reflect.Array;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;

/**
 * 反射工具类
 */
@Slf4j
public class ReflectUtils {


    /**
     * 创建一个缓存，用于存储字段信息
     */
    public static Map<String, Field[]> FIELDS_CACHE = new ConcurrentHashMap<>();


    /**
     * 创建一个缓存，用于包底下的类型信息
     */
    public static Map<String, Set<Class<?>>> CLASS_CACHE = new ConcurrentHashMap<>();

    /**
     * 获取指定类的所有字段（包括父类的字段）
     *
     * @param clazz 指定类
     * @return 字段数组
     */
    public static Field[] getDeclaredFields(Class<?> clazz, boolean getSuperClassField) {
        if (clazz == null) {
            return new Field[0];
        }
        // 构建缓存的键
        String key = getFieldKeys(clazz, getSuperClassField);
        // 尝试从缓存中获取字段数组
        Field[] fields = FIELDS_CACHE.get(key);
        // 如果缓存中已存在，则直接返回
        if (fields != null) {
            return fields;
        }
        // 计算字段信息
        fields = computeFields(clazz, getSuperClassField);
        // 尝试将计算结果放入缓存，并处理并发情况下的重复计算
        Field[] existingFields = FIELDS_CACHE.putIfAbsent(key, fields);
        // 如果其他线程已经计算并放入了结果，则使用已存在的结果
        return existingFields != null ? existingFields : fields;
    }

    /**
     * 获取指定类的所有字段（包括父类的字段）
     *
     * @param clazz 指定类
     * @return 字段数组
     */
    public static Field[] getNonFinalDeclaredFields(Class<?> clazz, boolean getSuperClassField) {
        // 尝试从缓存中获取字段数组
        Field[] fields = getDeclaredFields(clazz, getSuperClassField);
        return Arrays.stream(fields).filter(field -> !Modifier.isFinal(field.getModifiers())).toArray(Field[]::new);
    }

    private static Field[] computeFields(Class<?> clazz, boolean getSuperClassField) {
        List<Field> declaredFields = Arrays.stream(clazz.getDeclaredFields())
                .filter(field -> !field.isSynthetic())
                .collect(Collectors.toList());
        // 如果需要获取父类字段
        if (getSuperClassField) {
            Class<?> superclass = clazz.getSuperclass();
            while (superclass != null && superclass != Object.class) {
                Field[] superFields = getDeclaredFields(superclass, false); // 注意这里改为false，避免重复递归
                declaredFields.addAll(Arrays.asList(superFields));
                superclass = superclass.getSuperclass();
            }
        }
        return declaredFields.toArray(new Field[0]);
    }

    /**
     * 获取po对象的表名
     *
     * @param clazz
     * @return
     */
    public static String getTableName(Class<?> clazz) {
        if (clazz == null) {
            return null;
        }
        // 获取类上的注解名称
        return Optional.ofNullable(clazz.getAnnotation(TableName.class)).map(TableName::value).orElse(
                CharConstant.CHAR_EMPTY);
    }

    /**
     * 根据简单名称和顶级包名查找类
     *
     * @param topLevelPackageName 顶级包名
     * @param simpleName          类的简单名称
     * @return 找到的第一个匹配的类，如果没有找到返回null
     */
    public static Class<?> findClassBySimpleName(String topLevelPackageName, String simpleName) {
        //获取包底下的全部类名，这里缓存一下
        try {
            Set<Class<?>> topLevelPackageNameClass = CLASS_CACHE.computeIfAbsent(topLevelPackageName, k -> {
                Reflections reflections = new Reflections(
                        new ConfigurationBuilder().setUrls(ClasspathHelper.forPackage(topLevelPackageName))
                                .setScanners(new SubTypesScanner(false)));
                Set<Class<?>> classes = reflections.getSubTypesOf(Object.class);
                return classes;
            });
            if (CollectionUtils.isEmpty(topLevelPackageNameClass)) {
                // 没有找到匹配的类
                return null;
            }
            return topLevelPackageNameClass.stream().filter(clazz -> clazz.getSimpleName().equals(simpleName))
                    .findFirst().orElse(null);
        } catch (Exception e) {
            log.error(StringMessageFormatter.replaceMsg(
                    "findClassBySimpleName error,topLevelPackageName:{},simpleName:{}", topLevelPackageName,
                    simpleName), e);
            return null;
        }
    }

    /**
     * 根据简单名称和顶级包名查找类
     *
     * @param topLevelPackageName 顶级包名
     * @param simpleName 类的简单名称
     * @return 找到的第一个匹配的类，如果没有找到返回null
     */
    public static Class<?> findClassBySimpleName(List<String> topLevelPackageName, String simpleName) {
        if (CollectionUtils.isEmpty(topLevelPackageName)) {
            return null;
        }
        // 获取包底下的全部类名，这里缓存一下
        return topLevelPackageName.stream().map(packageName -> findClassBySimpleName(packageName, simpleName))
                .filter(Objects::nonNull).findFirst().orElse(null);
    }

    /**
     * 获取缓存key
     *
     * @param clazz
     * @param getSuperClassField
     * @return
     */
    private static String getFieldKeys(Class<?> clazz, boolean getSuperClassField) {

        return String.valueOf(Objects.hash(clazz, getSuperClassField));


    }

    /**
     * 获取单个对象的类型，或者集合对象中元素的类型
     *
     * @param obj
     * @return
     */
    public static Class<?> getDomainClass(Object obj) {
        if (obj == null) {
            return null;
        }
        Class<?> resultClass;
        if (obj instanceof Collection<?>) {
            Collection<?> collection = (Collection<?>) obj;
            if (collection.isEmpty()) {
                return null;
            }
            resultClass = collection.iterator().next().getClass();
        } else {
            resultClass = obj.getClass();
        }
        return resultClass;
    }

    /**
     * 判断对象是否有字段为空，取反代表所有字段都不为空
     *
     * @param obj 对象
     * @return 有任意字段为空返回true，否则返回false
     */
    @SneakyThrows
    public static boolean isAnyFieldEmpty(Object obj) {
        Class<?> clazz = obj.getClass();
        Field[] fields = getDeclaredFields(clazz, false);
        for (Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(obj);
            if (value == null) {
                return true;
            } else if (value instanceof String && StringUtils.isBlank((String) value)) {
                return true;
            } else if (value instanceof Collection && CollectionUtils.isEmpty((Collection<?>) value)) {
                return true;
            } else if (value instanceof Map && MapUtils.isEmpty((Map<?, ?>) value)) {
                return true;
            } else if (value.getClass().isArray() && Array.getLength(value) == 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断对象是否所有字段都为空
     *
     * @param obj 对象
     * @return 所有字段都为空返回true，否则返回false
     */
    public static boolean isAllFieldEmpty(Object obj) {
        Class<?> clazz = obj.getClass();
        Field[] fields = getDeclaredFields(clazz, false);
        boolean allFieldIsEmpty = true;
        for (Field field : fields) {
            field.setAccessible(true);
            Object value = getValueByField(field, obj);
            if (String.class.isAssignableFrom(field.getType()) && StringUtils.isNotBlank((String) value)) {
                allFieldIsEmpty = false;
                break;
            } else if (Collection.class.isAssignableFrom(field.getType()) && CollectionUtils.isNotEmpty(
                    (Collection<?>) value)) {
                allFieldIsEmpty = false;
                break;
            } else if (Map.class.isAssignableFrom(field.getType()) && MapUtils.isNotEmpty((Map<?, ?>) value)) {
                allFieldIsEmpty = false;
                break;
            } else if (field.getType().isArray() && Array.getLength(value) > 0) {
                allFieldIsEmpty = false;
                break;
            } else if (value != null) {
                //其他类型，则只需要值不为null即可
                allFieldIsEmpty = false;
                break;
            }
        }
        return allFieldIsEmpty;
    }


    /**
     * 获取值
     *
     * @param field
     * @param po
     * @return
     */
    public static Object getValueByField(Field field, Object po) {
        try {
            if (field == null || po == null) {
                return null;
            }
            field.setAccessible(true);
            return field.get(po);
        } catch (IllegalAccessException e) {
            log.error("反射获取值失败", e);
            return null;
        }
    }

    /**
     * 非null的值
     *
     * @param po
     * @return
     */
    public static <T> Map<String, Object> getNonNullFieldAndValue(T po) {
        try {
            if (po == null) {
                return new HashMap<>();
            }
            Field[] allFields = ReflectUtils.getDeclaredFields(po.getClass(), false);
            if (allFields == null || allFields.length < 1) {
                return new HashMap<>();
            }
            return Arrays.stream(allFields)
                    .filter(field -> getValueByField(field, po) != null).
                    collect(Collectors.toMap(Field::getName, field -> getValueByField(field, po)));
        } catch (Exception e) {
            log.error("获取为空字段名数组异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 过滤对象里面的指定字段的值，设置成null，如果在要忽略的字段里面，则不设置成null
     *
     * @param updatedPO
     * @param filterFieldList
     * @param ignoreFieldList
     * @param <PO>
     * @return
     */
    public static <PO> PO filterFieldValue(PO updatedPO, Set<String> filterFieldList, Set<String> ignoreFieldList) {
        if (updatedPO == null || CollectionUtils.isEmpty(filterFieldList)) {
            return updatedPO;
        }
        Class<?> clazz = updatedPO.getClass();
        Field[] declaredFields = getDeclaredFields(clazz, false);
        for (Field field : declaredFields) {
            String fieldName = field.getName();
            if (filterFieldList.contains(fieldName) && (ignoreFieldList == null || !ignoreFieldList.contains(
                    fieldName))) {
                setFieldValue(field, updatedPO, null);
            }
        }
        return updatedPO;
    }

    /**
     * 设置
     *
     * @param field
     * @param updatedPO
     * @param o
     * @param <PO>
     */
    private static <PO> void setFieldValue(Field field, PO updatedPO, Object o) {
        try {
            field.setAccessible(true);
            field.set(updatedPO, o);
        } catch (IllegalAccessException e) {
            log.error("反射设置值失败", e);
        }

    }

    /**
     * 获取类里面，满足注解的field的name
     *
     * @param tClass
     * @param domainUniqueClass
     * @return
     */
    public static List<String> getAnnField(Class<?> tClass, Class<DomainUnique> domainUniqueClass) {
        if (tClass == null || domainUniqueClass == null) {
            return new ArrayList<>();
        }
        Field[] fields = getDeclaredFields(tClass, true);
        if (fields.length < 1) {
            return new ArrayList<>();
        }
        return Arrays.stream(fields).filter(field -> field.getAnnotation(domainUniqueClass) != null).map(Field::getName)
                .collect(Collectors.toList());
    }


    public static Object createInstance(Class<?> clazz, Object... args) {
        try {
            Constructor<?>[] constructors = clazz.getConstructors();
            for (Constructor<?> constructor : constructors) {
                Integer counter = constructor.getParameterCount();
                if (args.length == 0 && counter == 0) {
                    return constructor.newInstance();
                }
                if (counter == args.length) {
                    return constructor.newInstance(args);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Error creating instance of {}: {}", clazz.getName(), e.getMessage(), e);
            return null;
        }
    }
}
