package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.BelowTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.JoinFrom;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.LeftJoin;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.Like;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEventDetail;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEventRelated;
import com.sankuai.wallemonitor.risk.center.infra.enums.LikeTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class NegativePublicEventDOQueryParamDTO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 查询指定的同名字段
     */
    private String eventId;

    /**
     * 事件ID范围查询
     */
    @InQuery(field = "eventId")
    private List<String> eventIdList;

    /**
     * 查询指定类型的数据
     */
    private Integer type;

    /**
     * 类型范围查询
     */
    @InQuery(field = "type")
    private List<Integer> eventTypeList;

    /**
     * 查询指定状态的数据
     */
    private Integer status;

    /**
     * 状态类型查询
     */
    @InQuery(field = "status")
    private List<Integer> statusList;

    /**
     * 指定查询的来源信息
     */
    private Integer source;

    /**
     * 来源范围查询
     */
    @InQuery(field = "source")
    private List<Integer> sourceList;

    /**
     * 创建时间范围
     */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    /**
     * 创建时间范围
     */
    @GreatTo(field = "createTime")
    private Date createTimeCreateTo;

    /**
     * 创建时间范围
     */
    @BelowTo(field = "createTime")
    private Date createTimeBelowTo;

    /**
     * 排序
     */
    @OrderBy(field = "createTime")
    private OrderEnum orderByCreateTime;

    /**
     * 关联具体的表名对应的Po对象
     */
    @LeftJoin(field = "eventId", table = NegativePublicEventDetail.class)
    private boolean leftJoinEventDetail;

    /**
     * 标题
     */
    @JoinFrom(NegativePublicEventDetail.class)
    @Like(field = "title", value = LikeTypeEnum.ALL)
    private String title;

    /**
     * 风险等级
     */
    @InQuery(field = "level")
    @JoinFrom(NegativePublicEventDetail.class)
    private List<Integer> levelList;

    /**
     * 问题分类
     */
    @InQuery(field = "category")
    @JoinFrom(NegativePublicEventDetail.class)
    private List<String> categoryList;

    /**
     * 定因提醒
     */
    @JoinFrom(NegativePublicEventDetail.class)
    private Boolean reasonNoticed;

    /**
     * 处置提醒
     */
    @JoinFrom(NegativePublicEventDetail.class)
    private Boolean handleNoticed;

    /**
     * 问题性质
     */
    @InQuery(field = "nature")
    @JoinFrom(NegativePublicEventDetail.class)
    private List<Integer> natureList;

    /**
     * 关联具体的表名对应的Po对象
     */
    @LeftJoin(field = "eventId", table = NegativePublicEventRelated.class)
    private boolean leftJoinEventRelated;

    /**
     * 查询指定的字段
     */
    @InQuery(field = "fieldName")
    @JoinFrom(NegativePublicEventRelated.class)
    private List<String> fieldNameList;

    /**
     * 查询指定的字段值
     */
    @InQuery(field = "fieldValue")
    @JoinFrom(NegativePublicEventRelated.class)
    private List<String> fieldValueList;

    /**
     * 事件ID模糊查询
     */
    @Like(field = "eventId", value = LikeTypeEnum.LEFT)
    private String eventIdLike;

    /**
     * 是否删除,如果需要不关注删除状态时，需要修改改字段为NULL
     */
    @Builder.Default
    private Boolean isDeleted = false;
}
