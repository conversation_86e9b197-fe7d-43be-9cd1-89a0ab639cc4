package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RiskCaseCategoryEnum {
    DEFAULT(null, null, "beaconTowerImproperStrandingHandleStrategy"),

    STATUS_MONITOR_VEHICLE_STAND_STILL(3, 1, "beaconTowerImproperStrandingHandleStrategy"),

    BEACON_TOWER_MONITOR_VEHICLE_STAND_STILL(5, 1, "beaconTowerImproperStrandingHandleStrategy");


    private Integer source;

    private Integer type;

    private String beanName;

    /**
     * 根据source和type查找对应的枚举值
     *
     * @param source
     * @param type
     * @return
     */
    public static RiskCaseCategoryEnum findByValue(Integer source, Integer type) {
        if (Objects.isNull(source) && Objects.isNull(type)) {
            return null;
        }
        for (RiskCaseCategoryEnum riskCaseCategoryEnum : RiskCaseCategoryEnum.values()) {
            if (Objects.equals(riskCaseCategoryEnum.getSource(), source) && Objects.equals(
                    riskCaseCategoryEnum.getType(), type)) {
                return riskCaseCategoryEnum;
            }
        }
        // todo: 如果找不到策略类就返回默认
        return RiskCaseCategoryEnum.DEFAULT;
    }

}
