package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TrafficData {

    private Boolean containLights;
    private Long timestamp;
    private String vin;
    private List<TrafficLight> trafficLightList;
    private Double distanceToLight;
    private TrafficHmiSignal trafficHmiSignal;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TrafficLight {

        private String color;
        private String id;
        private Boolean isBlinking;
        //红绿灯风险等级
        private String criticalLevel;
        private String source;
        private String status;
        private Double xWorld;
        private Double yWorld;
        //倒计时
        private Double countdown;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TrafficHmiSignal {

        private Integer currentReflineId;
        private HmiSignal hmiSignal;
        private Integer targetReflineId;

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class HmiSignal {

            private String finalSignalId;
        }
    }
}
