package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum IsDeleteEnum {
    NOT_DELETED(0, "未删除"),
    DELETED(1, "已删除");

    public int code;
    public String name;


    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static IsDeleteEnum getByCode(int code) {
        IsDeleteEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            IsDeleteEnum e = var1[var3];
            if (code == e.code) {
                return e;
            }
        }

        return null;
    }
}
