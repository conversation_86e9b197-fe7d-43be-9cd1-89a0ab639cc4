package com.sankuai.wallemonitor.risk.center.infra.repository.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.SingleConvert;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskErrorWaitCrossWalkRecord;
import com.sankuai.wallemonitor.risk.center.infra.repository.entity.RiskErrorWaitCrossWalkRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 错误让行检测记录转换器
 */
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskErrorWaitCrossWalkRecordConvert extends SingleConvert<RiskErrorWaitCrossWalkRecord, RiskErrorWaitCrossWalkRecordDO> {

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusEnum")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseTypeEnum")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "parseVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "parseVehicleRuntimeInfoContextDO")
    RiskErrorWaitCrossWalkRecordDO toDO(RiskErrorWaitCrossWalkRecord p);

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusInteger")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "serializeVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "serializeVehicleRuntimeInfoContextDO")
    RiskErrorWaitCrossWalkRecord toPO(RiskErrorWaitCrossWalkRecordDO d);
} 