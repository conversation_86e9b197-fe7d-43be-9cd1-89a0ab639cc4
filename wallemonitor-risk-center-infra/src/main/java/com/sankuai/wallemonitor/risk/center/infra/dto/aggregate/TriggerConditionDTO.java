package com.sankuai.wallemonitor.risk.center.infra.dto.aggregate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 触发条件DTO
 * 仅支持数量阈值判断
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TriggerConditionDTO {

    /**
     * 阈值
     */
    private Integer threshold;

    /**
     * 比较操作符
     */
    private AggregateOperatorEnum operator;

    /**
     * 校验条件是否有效
     */
    public boolean isValid() {
        return threshold != null && operator != null;
    }

    /**
     * 判断是否满足条件
     */
    public boolean isSatisfied(Integer actualValue) {
        if (!isValid() || actualValue == null) {
            return false;
        }
        return operator.compare(actualValue, threshold);
    }

    @Getter
    @AllArgsConstructor
    enum AggregateOperatorEnum {

        /**
         * 大于
         */
        GREATER_THAN(">", "大于"),

        /**
         * 大于等于
         */
        GREATER_THAN_OR_EQUAL(">=", "大于等于"),

        /**
         * 等于
         */
        EQUAL("=", "等于"),

        /**
         * 小于
         */
        LESS_THAN("<", "小于"),

        /**
         * 小于等于
         */
        LESS_THAN_OR_EQUAL("<=", "小于等于");

        private final String symbol;
        private final String desc;

        /**
         * 执行比较操作
         */
        public boolean compare(Integer actual, Integer threshold) {
            if (actual == null || threshold == null) {
                return false;
            }

            switch (this) {
                case GREATER_THAN:
                    return actual > threshold;
                case GREATER_THAN_OR_EQUAL:
                    return actual >= threshold;
                case EQUAL:
                    return actual == threshold;
                case LESS_THAN:
                    return actual < threshold;
                case LESS_THAN_OR_EQUAL:
                    return actual <= threshold;
                default:
                    return false;
            }
        }
    }
} 