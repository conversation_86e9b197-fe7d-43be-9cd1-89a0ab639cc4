package com.sankuai.wallemonitor.risk.center.infra.dto.aggregate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 聚合告警配置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregateAlertConfigDTO {

    /**
     * 聚合告警总开关
     */
    @Builder.Default
    private Boolean enabled = false;

    /**
     * 聚合策略列表
     */
    private List<AggregateStrategyConfigDTO> strategies;

    /**
     * 校验配置是否有效
     */
    public boolean isValid() {
        return enabled != null && enabled && CollectionUtils.isNotEmpty(strategies);
    }

    /**
     * 获取有效的策略列表（灰度配置有效的策略）
     */
    public List<AggregateStrategyConfigDTO> getValidStrategies() {
        if (CollectionUtils.isEmpty(strategies)) {
            return new ArrayList<>();
        }
        return strategies.stream()
                .filter(strategy -> strategy.getGrayRelease() != null && strategy.getGrayRelease().isValid())
                .collect(Collectors.toList());
    }

    /**
     * 获取启用的策略列表（为了向后兼容保留此方法）
     * @deprecated 使用 getValidStrategies() 替代
     */
    @Deprecated
    public List<AggregateStrategyConfigDTO> getEnabledStrategies() {
        return getValidStrategies();
    }
} 