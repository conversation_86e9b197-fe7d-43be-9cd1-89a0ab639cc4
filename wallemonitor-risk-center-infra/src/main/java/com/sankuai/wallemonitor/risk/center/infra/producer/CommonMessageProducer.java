package com.sankuai.wallemonitor.risk.center.infra.producer;

import com.google.common.collect.Lists;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.sankuai.walleeve.domain.enums.MessageType;
import com.sankuai.walleeve.domain.message.EveMqCommonMessage;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.MessageTopicEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.SpringUtils;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class CommonMessageProducer<T> {

    /**
     * 消息主题
     */
    private MessageTopicEnum messageTopic;

    /**
     * 注入的produceProcessor - bean
     */
    @Resource
    private List<IProducerProcessor> producerProcessorList;

    /**
     * 生成bean的producer
     */
    private volatile IProducerProcessor thisProducerProcessor;


    /**
     * 构造方法
     *
     * @param messageTopic
     */
    public CommonMessageProducer(MessageTopicEnum messageTopic) {
        this.messageTopic = messageTopic;
    }

    /**
     * 发送消息
     *
     * @param messageDTO
     * @param messageType
     */
    public String sendMqCommonMessage(T messageDTO, MessageType messageType) {
        try {
            //显示过滤一下
            if (messageDTO == null || messageType == null) {
                log.warn("消息体为空或者消息");
                return null;
            }
            IProducerProcessor iProducerProcessor = getProcess(messageTopic);
            CheckUtil.isNotNull(iProducerProcessor, "找不到对应的发送者");
            EveMqCommonMessage<T> mqCommonMessage = new EveMqCommonMessage<>();
            mqCommonMessage.setBody(messageDTO);
            mqCommonMessage.setType(messageType.getCode());
            ProducerResult producerResult = handleSend(mqCommonMessage);
            return producerResult.getMessageID();
        } catch (Exception e) {
            log.error(String.format("消息%s发送异常", messageTopic), e);
            return null;
        }

    }

    /**
     * 根据topic找到对应的producer
     *
     * @param messageTopic
     * @return
     */
    private IProducerProcessor getProcess(MessageTopicEnum messageTopic) {

        if (Objects.nonNull(thisProducerProcessor)) {
            return thisProducerProcessor;
        }
        synchronized (this) {
            if (Objects.isNull(thisProducerProcessor)) {
                //线程安全，确保双重判定 double-checked locking
                thisProducerProcessor = Optional.ofNullable(producerProcessorList).orElseGet(Lists::newArrayList)
                        .stream()
                        .filter(producerProcessor -> isMatchedProducer(producerProcessor, messageTopic))
                        .findFirst()
                        .orElse(null);
            }
        }
        return thisProducerProcessor;
    }

    /**
     * 和message进行匹配
     *
     * @param producerProcessor
     * @param messageTopic
     * @return
     */
    private boolean isMatchedProducer(IProducerProcessor producerProcessor, MessageTopicEnum messageTopic) {
        MafkaProducer mafkaProducer = (MafkaProducer) producerProcessor;
        String appKey = StringUtils.defaultIfBlank(messageTopic.getAppKey(),
                SpringUtils.getPropertiesValue(CommonConstant.APP_NAME_KEY));
        return mafkaProducer.getNamespace().equals(messageTopic.getNamespace()) &&
                mafkaProducer.getAppkey().equals(appKey) &&
                mafkaProducer.getTopic().equals(messageTopic.getTopic());
    }

    /**
     * 发送消息
     *
     * @param messageDTO
     */
    public String sendCustomMessage(T messageDTO) {
        try {
            //显示过滤一下
            if (messageDTO == null) {
                log.warn("消息体为空或者消息");
                return null;
            }
            return handleSend(messageDTO).getMessageID();
        } catch (Exception e) {
            log.error(String.format("消息%s发送异常", messageTopic), e);
            return null;
        }
    }


    /**
     * 发送消息
     *
     * @param messageDTO
     */
    public String sendDelayCustomMessage(T messageDTO, Long delaySecond) {
        try {
            //显示过滤一下
            if (messageDTO == null || delaySecond == null) {
                log.warn("消息体或者延迟时间为空");
                return null;
            }
            return handleDelaySend(messageDTO, delaySecond).getMessageID();
        } catch (Exception e) {
            log.error(String.format("消息%s发送异常", messageTopic), e);
            return null;
        }
    }

    /**
     * 处理发送
     *
     * @return
     */
    private ProducerResult handleSend(Object messageDTO) throws Exception {
        IProducerProcessor iProducerProcessor = getProcess(messageTopic);
        CheckUtil.isNotNull(iProducerProcessor, "找不到对应的发送者");
        String msgBody = JacksonUtils.to(messageDTO);
        return iProducerProcessor.sendMessage(msgBody);
    }

    /**
     * 处理发送
     *
     * @return
     */
    private ProducerResult handleDelaySend(Object messageDTO, Long delaySecond) throws Exception {
        IProducerProcessor iProducerProcessor = getProcess(messageTopic);
        CheckUtil.isNotNull(iProducerProcessor, "找不到对应的发送者");
        String msgBody = JacksonUtils.to(messageDTO);
        return iProducerProcessor.sendDelayMessage(msgBody, delaySecond * 1000);
    }


    /**
     * 发送消息
     *
     * @param messageDTO
     */
    public void sendCustomMessageList(List<T> messageDTO) {
        try {
            //显示过滤一下
            if (CollectionUtils.isEmpty(messageDTO)) {
                log.warn("消息体为空或者消息");
            }
            messageDTO.forEach(this::sendCustomMessage);
        } catch (Exception e) {
            log.error(String.format("消息%s发送异常", messageTopic), e);
        }
    }

    /**
     * 发送消息
     *
     * @param messageDTOList
     * @param messageType
     */
    public boolean sendMqCommonMessageList(List<T> messageDTOList, MessageType messageType) {
        //显示过滤一下
        if (CollectionUtils.isEmpty(messageDTOList) || messageType == null) {
            log.warn("消息体为空或者消息");
            return true;
        }
        Set<Boolean> success = new HashSet<>();
        messageDTOList.forEach(messageDTO -> success.add(
                StringUtils.isNotBlank(sendMqCommonMessage(messageDTO, messageType))));
        return success.stream().allMatch(BooleanUtils::toBoolean);
    }

    public MessageTopicEnum getMessageTopic() {
        return messageTopic;
    }

    public void initProcess(IProducerProcessor producerProcessor) {
        thisProducerProcessor = producerProcessor;
    }
}
