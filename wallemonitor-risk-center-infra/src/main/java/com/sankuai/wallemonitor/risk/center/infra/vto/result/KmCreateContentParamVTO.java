package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KmCreateContentParamVTO {

    // 人员empid
    private Integer operatorEmpId;

    // 模板id
    private String templateId;

    // 复制的文档id
    private String copyFromContentId;

    // 文档内容
    private String content;

    // 空间id
    private String spaceId;

    // 父文档id
    private String parentId;

    // 文档标题
    private String title;
}
