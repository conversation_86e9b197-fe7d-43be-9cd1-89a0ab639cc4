package com.sankuai.wallemonitor.risk.center.infra.exception;

import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;

/**
 * <AUTHOR>
 * @date 2024/11/20
 */
public class CacheLoadException extends ErrorCodeException {

    private String extMsg;

    public CacheLoadException(ResponseCodeEnum responseCode) {
        super(responseCode.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getMessage());
    }

    public CacheLoadException(ResponseCodeEnum responseCode, String extMsg) {
        super(responseCode.getCode(), extMsg);
        this.extMsg = extMsg;
    }

    public CacheLoadException(String message) {
        super(ResponseCodeEnum.SYSTEM_ERROR.getCode(), message);
        this.extMsg = message;
    }

    public String getExtMsg() {
        return extMsg;
    }

    public void setExtMsg(String extMsg) {
        this.extMsg = extMsg;
    }
}
