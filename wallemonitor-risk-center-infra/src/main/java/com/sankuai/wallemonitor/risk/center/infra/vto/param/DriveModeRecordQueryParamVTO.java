package com.sankuai.wallemonitor.risk.center.infra.vto.param;

import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆驾驶状态记录查询参数类
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DriveModeRecordQueryParamVTO {

    private List<SingleVinQueryParam> vinQueryParamList;

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SingleVinQueryParam {

        private String caseId;

        private String vin;

        private Date startTime;

        private Date endTime;
    }
}
