package com.sankuai.wallemonitor.risk.center.infra.factory;

import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskQueueStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Collections;
import java.util.Date;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


@Component
public class RiskCheckingQueueItemFactory {

    /**
     * id生成器
     */
    public static IDGenerateRepository idGenerateRepository;

    /**
     * id生成器
     */
    @Resource
    private IDGenerateRepository innerIdGenerateRepository;

    /**
     * id生成器
     */
    public static LionConfigRepository staticLionConfigRepository;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @PostConstruct
    public void init() {
        idGenerateRepository = innerIdGenerateRepository;
        staticLionConfigRepository = lionConfigRepository;
    }

    /**
     * 创建风险检查队列
     *
     * @return
     */
    public static RiskCheckingQueueItemDO createRiskCheckingQueueItem(CreateRiskCheckingQueueDOParamDTO paramDTO) {
        SystemCheckUtil.isNotNull(paramDTO, "入参不能为空");
        SystemCheckUtil.isNotBlank(paramDTO.getEventId(), "事件ID不能为空");
        SystemCheckUtil.isNotNull(paramDTO.getSource(), "风险来源不能为空");
        SystemCheckUtil.isNotNull(paramDTO.getVin(), "车架号不能为空");

        String caseId = StringUtils.isNotBlank(paramDTO.getCaseId()) ? paramDTO.getCaseId()
                : idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID,
                        Collections.singletonList(paramDTO.getVin()), paramDTO.getSource(), paramDTO.getType(),
                        paramDTO.getOccurTime());

        RiskAutoCheckConfigDTO config = staticLionConfigRepository.getRiskCheckingConfig();
        Date nextRoundTime = DatetimeUtil.getNSecondsAfterDateTime(new Date(), config.getRoundIntervalSeconds());
        return RiskCheckingQueueItemDO.builder()
                .tmpCaseId(caseId)
                .eventId(paramDTO.getEventId())
                .vin(paramDTO.getVin())
                // 放入预检队列时，立即发起预检
                .checking(true)
                .status(RiskQueueStatusEnum.VALIDATING)
                .occurTime(new Date(paramDTO.getOccurTime()))
                .recallTime(DatetimeUtil.toDate(paramDTO.getRecallTime()))
                .type(paramDTO.getType())
                .round(0)
                .maxRound(paramDTO.getMaxRound())
                .nextRoundTime(nextRoundTime)
                .source(paramDTO.getSource())
                .isDeleted(false)
                .build();
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class CreateRiskCheckingQueueDOParamDTO {

        /**
         * 事件ID
         */
        private String eventId;
        /**
         * 来源
         */
        private RiskCaseSourceEnum source;
        /**
         * 类型
         */
        private RiskCaseTypeEnum type;

        /**
         * 车辆
         */
        private String vin;

        /**
         * 发生时间
         */
        private Long occurTime;

        /**
         * 召回时间
         */
        private Long recallTime;

        /**
         * caseId
         */
        private String caseId;

        /**
         * 最大轮次
         * */
        private Integer maxRound;

    }

}
