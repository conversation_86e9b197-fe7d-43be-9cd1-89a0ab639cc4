package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class RiskCaseMrmDisposeConfigDTO {

    @Builder.Default
    private List<MrmStatusDisposeDTO> mrmStateList = new ArrayList<>();

    public List<Integer> getMrmStateAllTypeList() {
        if (CollectionUtils.isEmpty(mrmStateList)) {
            return new ArrayList<>();
        }
        return mrmStateList.stream().map(MrmStatusDisposeDTO::getMrmStatus).map(RiskCaseVehicleStatusEnum::getCode)
                .collect(Collectors.toList());
    }

    public MrmStatusDisposeDTO matchMrmDisposeConfig(RiskCaseVehicleStatusEnum status, RiskCaseDO riskCase) {
        if (riskCase == null || CollectionUtils.isEmpty(mrmStateList)) {
            return null;
        }
        return mrmStateList.stream().filter(mrmStatusDisposeDTO -> mrmStatusDisposeDTO.matched(status, riskCase))
                .findFirst().orElse(null);
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class MrmStatusDisposeDTO {

        private RiskCaseVehicleStatusEnum mrmStatus;

        private List<Integer> fromStatus;

        @Default
        private Set<Integer> riskCaseType = new HashSet<>();

        private Integer toStatus;

        /**
         *
         * @param mrmStatus
         * @param riskCaseDO
         * @return
         */
        public Integer doDispose(RiskCaseVehicleStatusEnum mrmStatus, RiskCaseDO riskCaseDO) {
            if (riskCaseDO == null || mrmStatus == null) {
                return null;
            }
            return toStatus;

        }

        /**
         * @param status
         * @param riskCase
         * @return
         */
        public boolean matched(RiskCaseVehicleStatusEnum status, RiskCaseDO riskCase) {
            if (riskCase == null || status == null) {
                return false;
            }

            return this.mrmStatus == status && fromStatus.contains(riskCase.getStatus().getCode())
                    && riskCaseType.contains(riskCase.getType().getCode());
        }
    }
}
