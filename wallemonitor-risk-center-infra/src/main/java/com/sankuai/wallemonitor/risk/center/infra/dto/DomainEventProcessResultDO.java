package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DomainEventProcessResultDO implements Serializable {

    /**
     * 事件id
     */
    private String traceId;

    /**
     * entry
     */
    private DomainEventEntryDTO entry;

    /**
     * 事件时间
     */
    private Long eventTime;

    /**
     * 处理器名称
     */
    private String processName;

    /**
     * 处理结果
     */
    private Boolean processResult;

    /**
     * 重试次数
     */
    @Builder.Default
    private Integer retriedTime = 0;

    /**
     * 是否可以重试
     */
    @Builder.Default
    private Boolean canRetry = true;

    /**
     * 获取事件的唯一key
     *
     * @return
     */
    public String getEventUniqueKey() {
        return Joiner.on(CharConstant.CHAR_JH).join(processName, entry.toString(), traceId, eventTime);
    }

    /**
     * 执行成功
     */
    public void process(boolean success) {
        processResult = success;
        if (!success) {
            retriedTime += 1;
        }

    }


}
