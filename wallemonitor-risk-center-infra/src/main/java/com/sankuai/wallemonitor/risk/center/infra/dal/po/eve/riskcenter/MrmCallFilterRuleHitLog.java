package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 坐席呼叫过滤规则命中纪录表PO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("mrm_call_filter_rule_hit_log")
public class MrmCallFilterRuleHitLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 自增ID */
    @TableId(value = "id", type = IdType.AUTO)
    @TableUnique
    @TableField("id")
    private Long id;

    /** case的唯一ID */
    @TableField("case_id")
    private String caseId;

    /** 命中过滤规则 */
    @TableField("filter_rule")
    private String filterRule;

    /** 扩展信息 */
    @TableField("ext_info")
    private String extInfo;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;

    /** 是否删除 */
    @TableField("is_deleted")
    private Boolean isDeleted;
} 