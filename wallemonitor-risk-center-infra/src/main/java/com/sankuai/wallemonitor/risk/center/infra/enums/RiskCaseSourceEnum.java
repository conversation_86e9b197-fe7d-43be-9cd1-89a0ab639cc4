package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
/**
 * 风险事件来源枚举
 */
public enum RiskCaseSourceEnum {
    /**
     * 外部事件来源，1-保障系统|2-车端PNC|3-运维状态监控
     */
    SAFEGUARD_SYSTEM(1, "保障系统"),
    PNC(2, "车辆PNC"),
    STATUS_MONITOR(3, "状态监控系统"),
    CLOUD_TRIAGE(4, "云分诊"),
    BEACON_TOWER(5, "风控服务"),
    APPLET_OF_WECHAT(6, "扫码挪车微信小程序"),
    FLEET_COMMANDER(7, "FC"),
    CLOUD_CURSOR(8, "云控系统");


    private int code;

    private String desc;


    /**
     * 根据value查询枚举
     *
     * @param code
     * @return
     */
    public static RiskCaseSourceEnum findByValue(Integer code) {
        if (code == null) {
            return null;
        }
        for (RiskCaseSourceEnum riskCaseSourceEnum : RiskCaseSourceEnum.values()) {
            if (riskCaseSourceEnum.getCode() == code) {
                return riskCaseSourceEnum;
            }
        }
        return null;
    }

    public static boolean recallBySelf(RiskCaseSourceEnum sourceEnum) {
        return sourceEnum == RiskCaseSourceEnum.BEACON_TOWER || sourceEnum == RiskCaseSourceEnum.STATUS_MONITOR;
    }

    public static List<Integer> getSelfSourceCode() {
        return Lists.newArrayList(RiskCaseSourceEnum.BEACON_TOWER, RiskCaseSourceEnum.STATUS_MONITOR).stream()
                .map(RiskCaseSourceEnum::getCode).collect(Collectors.toList());
    }

    public String getShortStr() {
        return String.format("S%02d", code);
    }
}
