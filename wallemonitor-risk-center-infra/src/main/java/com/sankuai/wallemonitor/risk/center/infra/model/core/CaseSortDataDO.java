package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CaseSortExtInfoDO;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class CaseSortDataDO {

    /**
     * case id
     */
    private String caseId;

    /**
     * 所属问题类型
     */
    private String problem;

    /**
     * 分拣人
     */
    private String sorter;

    /**
     * 次要信息
     */
    private CaseSortExtInfoDO extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否删除[0-未删除|1-已删除]
     */
    private IsDeleteEnum isDeleted;

    /**
     * 保存分拣数据
     *
     * @param problem
     * @param sorter
     */
    public void sort(String problem, String sorter, String description) {
        this.problem = problem;
        this.sorter = sorter;
        this.description = description;
    }
}
