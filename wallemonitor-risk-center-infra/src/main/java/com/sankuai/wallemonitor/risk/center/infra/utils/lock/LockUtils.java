package com.sankuai.wallemonitor.risk.center.infra.utils.lock;


import com.dianping.squirrel.client.StoreKey;
import com.google.common.collect.Sets;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.SquirrelCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LockUtils {


    /**
     * 分布式锁key超时时间，单位秒，统一设置为5秒,超过5s代表业务有问题了
     */
    private final int LOCK_EXPIRE_SECONDS = 5;
    /**
     * 默认等待时间
     */
    private final int DEFAULT_WAIT_MILLISECONDS = 3000;
    /**
     * 单个锁默认的等待超时超时毫秒时间
     */
    private final int DEFAULT_SLEEP_RETRY_MILLISECONDS = 50;
    @Resource
    private LockUtil innerLockService;
    /**
     * 批量分布式锁key超时时间，单位秒，统一设置为3秒，防止过久
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_LOCK_EXPIRE_SECOND, value = "", defaultValue = "3", allowBlankValue = true)
    private Integer batchLockExpireSeconds;
    /**
     * 批量分布式锁默认的等待超时超时毫秒时间
     */
    @ConfigValue(key = LionKeyConstant.LION_KEY_LOCK_SLEEP_MILL_SECOND, value = "", defaultValue = "100", allowBlankValue = true)
    private Integer batchLockSleepRetryMilliseconds;

    public void lockWait(String key, Runnable runnable) {
        lockWait(key, () -> {
            runnable.run();
            return null;
        });
    }

    public <T> T lockWait(String key, Supplier<T> supplier) {
        return lockCanWait(key, DEFAULT_WAIT_MILLISECONDS, TimeUnit.MILLISECONDS, supplier);
    }


    public void lockNoWait(String key, Runnable runnable) {
        lockNoWait(key, () -> {
            runnable.run();
            return true;
        });
    }

    public <T> T lockNoWait(String key, Supplier<T> func) {
        String curThreadName = Thread.currentThread().getName();
        boolean result = false;
        try {
            log.info("当前线程:{}", curThreadName);
            //超时时间统一设置为3分钟防止锁被提前释放（提前释放会导致key被误删）
            result = innerLockService.lockWithoutRetry(SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(),
                    key,
                    LOCK_EXPIRE_SECONDS);
            if (!result) {
                log.info("当前线程：{}，keys：{}，未成功占到锁，提前返回", curThreadName, key);
                throw new UnableGetLockException(ResponseCodeEnum.UNABLE_GET_LOCK);
            }
            log.info("当前线程：{}，keys：{}，上锁成功", curThreadName, key);
            //执行加锁逻辑
            return func.get();
        } finally {
            if (result) {
                innerLockService.unLock(SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(), key);
                log.info("当前线程：{}，keys：{}，锁释放成功", curThreadName, key);
            }
        }
    }

    public void lockNoRelease(String key, int expireSeconds, Runnable runnable) {
        lockNoRelease(key, expireSeconds, () -> {
            runnable.run();
            return true;
        });
    }

    public <T> T lockNoRelease(String key, int expireSeconds, Supplier<T> func) {
        String curThreadName = Thread.currentThread().getName();
        boolean result = false;
        log.info("当前线程:{}", curThreadName);
        //超时时间是输入值
        result = innerLockService.lockWithoutRetry(SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(), key,
                expireSeconds);
        if (!result) {
            log.info("当前线程：{}，keys：{}，未成功占到锁，提前返回", curThreadName, key);
            throw new UnableGetLockException(ResponseCodeEnum.UNABLE_GET_LOCK);
        }
        log.info("当前线程：{}，keys：{}，上锁成功，执行完等待{}秒超时自动释放", curThreadName, key, expireSeconds);
        //执行加锁逻辑
        return func.get();
    }


    /**
     * 加锁执行完不释放，等到超时释放
     *
     * @param key         上锁key
     * @param maxWaitTime 最大等待时间
     * @param timeUnit    时间单位
     * @param runnable    上锁成功后执行的业务逻辑
     * @return
     */
    public void lockWaitNoRelease(String key, int maxWaitTime, TimeUnit timeUnit, Runnable runnable) {
        lockWaitNoRelease(key, maxWaitTime, timeUnit, () -> {
            runnable.run();
            return true;
        });
    }

    /**
     * 加锁执行完不释放，等到超时释放
     *
     * @param key         上锁key
     * @param maxWaitTime 最大等待时间
     * @param timeUnit    时间单位
     * @param func        上锁成功后执行的业务逻辑
     * @return
     */
    public <T> T lockWaitNoRelease(String key, int maxWaitTime, TimeUnit timeUnit, Supplier<T> func) {
        return lockWaitExpireTimeNoRelease(key, maxWaitTime, timeUnit, LOCK_EXPIRE_SECONDS, func);
    }

    public void lockWaitExpireTimeNoRelease(String key, int maxWaitTime, TimeUnit timeUnit, int expireSeconds,
            Runnable runnable) {
        lockWaitExpireTimeNoRelease(key, maxWaitTime, timeUnit, expireSeconds, () -> {
            runnable.run();
            return true;
        });
    }

    /**
     * 加锁执行完不释放，按照输入超时时间等到超时释放
     *
     * @param key           上锁key
     * @param maxWaitTime   最大等待时间
     * @param timeUnit      时间单位
     * @param expireSeconds key超时秒时间
     * @param func          上锁成功后执行的业务逻辑
     * @return
     */
    public <T> T lockWaitExpireTimeNoRelease(String key, int maxWaitTime, TimeUnit timeUnit, int expireSeconds,
            Supplier<T> func) {
        String curThreadName = Thread.currentThread().getName();
        boolean result = false;
        try {
            log.info("当前线程:{}", curThreadName);
            int maxWaitMilliSeconds;
            if (timeUnit == TimeUnit.SECONDS) {
                maxWaitMilliSeconds = maxWaitTime * 1000;
            } else if (timeUnit == TimeUnit.MILLISECONDS) {
                maxWaitMilliSeconds = maxWaitTime;
            } else {
                throw new UnsupportedOperationException("仅支持秒或毫秒");
            }

            //超时时间统一设置为3分钟防止锁被提前释放（提前释放组件有bug，导致key被误删）
            result = innerLockService.lockWithoutRetry(SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(),
                    key,
                    expireSeconds);
            if (!result) {
                maxWaitMilliSeconds -= DEFAULT_SLEEP_RETRY_MILLISECONDS;
                if (maxWaitMilliSeconds > 0) {
                    //休息一秒后继续抢
                    try {
                        TimeUnit.MILLISECONDS.sleep(DEFAULT_SLEEP_RETRY_MILLISECONDS);
                    } catch (InterruptedException e) {
                        //ignore
                    }
                    return lockCanWait(key, maxWaitMilliSeconds, TimeUnit.MILLISECONDS, func);
                }
                log.info("当前线程：{}，key：{}，未成功占到锁，提前返回", curThreadName, key);
                throw new UnableGetLockException(ResponseCodeEnum.UNABLE_GET_LOCK);
            }
            log.info("当前线程：{}，keys：{}，上锁成功", curThreadName, key);
            //执行加锁逻辑
            return func.get();
        } finally {
            //不释放锁，等待超时过期
        }
    }

    public void lockCanWait(String key, int maxWaitTime, TimeUnit timeUnit, Runnable runnable) {
        lockCanWait(key, maxWaitTime, timeUnit, () -> {
            runnable.run();
            return true;
        });
    }

    public <T> T lockCanWait(String key, int maxWaitTime, TimeUnit timeUnit, Supplier<T> func) {
        String curThreadName = Thread.currentThread().getName();
        boolean result = false;
        try {
            log.info("当前线程:{}", curThreadName);
            int maxWaitMilliSeconds;
            if (timeUnit == TimeUnit.SECONDS) {
                maxWaitMilliSeconds = maxWaitTime * 1000;
            } else if (timeUnit == TimeUnit.MILLISECONDS) {
                maxWaitMilliSeconds = maxWaitTime;
            } else {
                throw new UnsupportedOperationException("仅支持秒或毫秒");
            }

            //超时时间统一设置为3分钟防止锁被提前释放（提前释放组件有bug，导致key被误删）
            result = innerLockService.lockWithoutRetry(SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(),
                    key,
                    LOCK_EXPIRE_SECONDS);
            if (!result) {
                //log.info("当前线程：{}，key：{}，未成功占到锁，提前返回，剩余maxWaitMilliSeconds{}", curThreadName, key, maxWaitMilliSeconds);
                maxWaitMilliSeconds -= DEFAULT_SLEEP_RETRY_MILLISECONDS;
                if (maxWaitMilliSeconds > 0) {
                    //休息50毫秒后继续抢
                    try {
                        TimeUnit.MILLISECONDS.sleep(DEFAULT_SLEEP_RETRY_MILLISECONDS);
                    } catch (InterruptedException e) {
                        //ignore
                    }
                    return lockCanWait(key, maxWaitMilliSeconds, TimeUnit.MILLISECONDS, func);
                }
                log.info("当前线程：{}，key：{}，未成功占到锁，提前返回", curThreadName, key);
                throw new UnableGetLockException(ResponseCodeEnum.UNABLE_GET_LOCK);
            }
            log.info("当前线程：{}，keys：{}，上锁成功", curThreadName, key);
            //执行加锁逻辑
            return func.get();
        } finally {
            if (result) {
                innerLockService.unLock(SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(), key);
                log.info("当前线程：{}，keys：{}，锁释放成功", curThreadName, key);
            }
        }
    }

    /**
     * 批量获取分布式锁，因为squirrel不具备原子性，建议set不要超过20个以上
     *
     * @param keySet
     * @param runnable
     */
    public void batchLockNoWait(Set<String> keySet, Runnable runnable) {
        batchLockNoWait(keySet, () -> {
            runnable.run();
            return true;
        });
    }

    /**
     * 批量获取分布式锁，因为squirrel不具备原子性，建议set不要超过20个以上
     *
     * @param keySet
     * @param func
     * @param <T>
     * @return
     */
    public <T> T batchLockNoWait(Set<String> keySet, Supplier<T> func) {
        String curThreadName = Thread.currentThread().getName();
        Set<String> lockedKeySet = Sets.newHashSet();
        try {
            log.info("[批量锁]当前线程:{}", curThreadName);
            for (String key : keySet) {
                //超时时间统一设置为3秒防止锁被提前释放（提前释放会导致key被误删）
                boolean result = innerLockService.lockWithoutRetry(
                        SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(), key,
                        batchLockExpireSeconds);
                if (!result) {
                    //如果是非等待，只要有一个拿不到就返回了，没必要继续往下走全拿一遍
                    log.info("[批量锁]当前线程：{}，keys：{}，未成功占到锁，非等待提前返回", curThreadName, key);
                    throw new UnableGetLockException(ResponseCodeEnum.UNABLE_GET_LOCK);
                } else {
                    lockedKeySet.add(key);
                }
            }
            log.info("[批量锁]当前线程：{}，keys：{}，上锁成功", curThreadName, keySet);
            //执行加锁逻辑
            return func.get();
        } finally {
            batchUnLock(lockedKeySet);
            log.info("[批量锁]当前线程：{}，keys：{}，锁释放成功", curThreadName, keySet);
        }
    }

    /**
     * @param keySet 加锁key
     * @param expire 过期时间
     * @return
     */
    private Boolean batchLockWithoutRetry(Set<String> keySet, int expire) {
        Set<String> lockedKeySet = Sets.newHashSet();
        try {
            for (String key : keySet) {
                boolean result = innerLockService.lockWithoutRetry(
                        SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(), key, expire);
                if (!result) {
                    log.warn("key：{}，加锁失败", key);
                    return false;
                } else {
                    lockedKeySet.add(key);
                }
            }
            log.info("[批量锁]，keys：{}，上锁成功", keySet);
            return true;
        } finally {
            // 当加锁失败时需要把已加锁成功的一些key删除掉
            if (!Objects.equals(keySet.size(), lockedKeySet.size())) {
                batchUnLock(lockedKeySet);
            }
        }
    }


    public Boolean batchLock(Set<String> keySet, int expire, int maxWaitMilliSeconds) {
        Long startTime = System.currentTimeMillis();
        boolean lockSuccess = batchLockWithoutRetry(keySet, expire);
        if (lockSuccess) {
            return true;
        }
        Long costTime = System.currentTimeMillis() - startTime;
        if (costTime < maxWaitMilliSeconds) {
            try {
                TimeUnit.MILLISECONDS.sleep(batchLockSleepRetryMilliseconds);
            } catch (InterruptedException e) {
                log.error("batchLock error", e);
            }
            return batchLock(keySet, expire, (int) (maxWaitMilliSeconds - costTime - batchLockSleepRetryMilliseconds));
        }
        return false;
    }

    public <T> T batchLockCanWait(Set<String> keySet, Supplier<T> func) {
        return batchLockCanWait(keySet, DEFAULT_WAIT_MILLISECONDS, TimeUnit.MILLISECONDS, func);
    }

    public <T> T batchLockCanWait(String key, Supplier<T> func) {
        return batchLockCanWait(Sets.newHashSet(key), DEFAULT_WAIT_MILLISECONDS, TimeUnit.MILLISECONDS, func);
    }

    public void batchLockCanWait(Set<String> keySet, Runnable func) {
        batchLockCanWait(keySet, DEFAULT_WAIT_MILLISECONDS, TimeUnit.MILLISECONDS, func);
    }

    public void batchLockCanWait(String key, Runnable func) {
        batchLockCanWait(Sets.newHashSet(key), DEFAULT_WAIT_MILLISECONDS, TimeUnit.MILLISECONDS, func);
    }


    /**
     * 按照一定等待时间去批量获取分布式锁，因为squirrel不具备原子性，建议set不要超过20个以上
     *
     * @param keySet
     * @param maxWaitTime 如果是毫秒不建议超过1分钟600000，所以这里用的int
     * @param timeUnit
     * @param func
     * @param <T>
     * @return
     */
    public <T> T batchLockCanWait(Set<String> keySet, int maxWaitTime, TimeUnit timeUnit, Supplier<T> func) {
        String curThreadName = Thread.currentThread().getName();
        Long startTime = System.currentTimeMillis();
        int maxWaitMilliSeconds;
        if (timeUnit == TimeUnit.SECONDS) {
            maxWaitMilliSeconds = maxWaitTime * 1000;
        } else if (timeUnit == TimeUnit.MILLISECONDS) {
            maxWaitMilliSeconds = maxWaitTime;
        } else {
            throw new UnsupportedOperationException("仅支持秒或毫秒");
        }
        log.info("[批量锁]当前线程:{}，剩余时间等待时间:{}毫秒", curThreadName, maxWaitMilliSeconds);
        //先尝试取一次
        try {
            return batchLockNoWait(keySet, func);
        } catch (UnableGetLockException unableGetLockException) {
            //如果拿不到锁再走递归
            Long costTime = System.currentTimeMillis() - startTime;
            if (costTime < maxWaitMilliSeconds) {
                //休息后继续抢
                try {
                    TimeUnit.MILLISECONDS.sleep(batchLockSleepRetryMilliseconds);
                } catch (InterruptedException e) {
                    //ignore
                }
                return batchLockCanWait(keySet,
                        (int) (maxWaitMilliSeconds - costTime - batchLockSleepRetryMilliseconds),
                        TimeUnit.MILLISECONDS, func);
            } else {
                log.info("[批量锁]当前线程：{}，keySet：{}，未成功占到锁，提前返回", curThreadName, keySet);
                throw new UnableGetLockException(ResponseCodeEnum.UNABLE_GET_LOCK);
            }
        }
    }

    /**
     * 批量获取分布式锁，因为squirrel不具备原子性，建议set不要超过20个以上
     *
     * @param keySet
     * @param runnable
     */
    public void batchLockCanWait(Set<String> keySet, int maxWaitTime, TimeUnit timeUnit, Runnable runnable) {
        batchLockCanWait(keySet, maxWaitTime, timeUnit, () -> {
            runnable.run();
            return true;
        });
    }

    /**
     * 批量去释放锁
     *
     * @param keySet 成功锁上的key
     * @return
     */
    public void batchUnLock(Set<String> keySet) {
        if (keySet == null || CollectionUtils.isEmpty(keySet)) {
            return;
        }
        for (String key : keySet) {
            innerLockService.unLock(SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(), key);
        }
    }

    public Boolean batchCounterLock(Set<String> keySet, int expire, Long maxWaitMilliSeconds) {
        Long startTime = System.currentTimeMillis();
        boolean lockSuccess = batchCounterLockWithoutRetry(keySet, expire);
        if (lockSuccess) {
            return true;
        }
        Long costTime = System.currentTimeMillis() - startTime;
        if (costTime < maxWaitMilliSeconds) {
            try {
                TimeUnit.MILLISECONDS.sleep(batchLockSleepRetryMilliseconds);
            } catch (InterruptedException e) {
                log.error("batchCounterLock error", e);
            }
            long remainWaitMs = maxWaitMilliSeconds - costTime - batchLockSleepRetryMilliseconds;
            return batchCounterLock(keySet, expire, remainWaitMs);
        }
        return false;
    }

    public void batchCounterUnLock(Set<String> keySet, int expire) {
        if (keySet == null || CollectionUtils.isEmpty(keySet)) {
            return;
        }
        for (String key : keySet) {
            innerLockService.counterUnLock(SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(), key, expire);
        }

    }

    private Boolean batchCounterLockWithoutRetry(Set<String> keySet, int expire) {
        Set<String> lockedKeySet = Sets.newHashSet();
        try {
            for (String key : keySet) {
                boolean result = innerLockService.counterLockWithoutRetry(
                        SquirrelCategoryEnum.RISK_CENTER_LOCK_CATEGORY.getCategory(), key,
                        expire);
                if (!result) {
                    log.warn("key：{}，加锁失败", key);
                    return false;
                } else {
                    lockedKeySet.add(key);
                }
            }
            log.info("[批量锁]，keys：{}，上锁成功", keySet);
            return true;
        } finally {
            // 当加锁失败时需要把已加锁成功的一些key删除掉
            if (!Objects.equals(keySet.size(), lockedKeySet.size())) {
                batchCounterUnLock(lockedKeySet, expire);
            }
        }
    }

    @Component
    public class LockUtil {

        private final int MAX_RETRY = 5;
        private final Logger logger = LoggerFactory.getLogger(getClass());

        @Resource
        private SquirrelAdapter squirrelAdapter;

        /**
         * 加锁
         *
         * @param expire 过期时间，单位秒
         * @return true:加锁成功，false，加锁失败
         */
        public boolean lock(String category, String key, int expire) {
            SquirrelCategoryEnum categoryEnum = SquirrelCategoryEnum.getByCategory(category);
            int i = 0;
            while (i < MAX_RETRY) {
                boolean status = lock(categoryEnum, key, expire);
                if (status) {
                    return true;
                }
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                i++;
            }

            logger.warn("failed to get lock for area {}", key);
            return false;
        }

        public boolean lockWithRetry(String category, String key, int expire, int retryTimes) {
            SquirrelCategoryEnum categoryEnum = SquirrelCategoryEnum.getByCategory(category);
            // 要确保能加锁一次，防止误配置此字段为负数此处重新赋值
            retryTimes = retryTimes < 0 ? 0 : retryTimes;
            StoreKey storeKey = new StoreKey(category, key);
            boolean status = false;
            while (retryTimes >= 0) {
                log.info("lockWithRetry,key:{},retryTimes:{}", key, retryTimes);
                status = lock(categoryEnum, key, expire);
                // 加锁成功或者不重试时直接返回
                if (status || retryTimes == 0) {
                    return status;
                }
                try {
                    // 重试时间间隔100ms
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("lockWithRetry error", e);
                }
                retryTimes--;
            }

            logger.warn("failed to get lock for area {}", key);
            return false;
        }

        /**
         * 加锁不带循环
         *
         * @param expire 过期时间，单位秒
         * @return true:加锁成功，false，加锁失败
         */
        public boolean lockWithoutRetry(String category, String key, int expire) {
            SquirrelCategoryEnum squirrelCategoryEnum = SquirrelCategoryEnum.getByCategory(category);

            return lock(squirrelCategoryEnum, key, expire);
        }

        public boolean lock(SquirrelCategoryEnum categoryEnum, String key, int expire) {
            return squirrelAdapter.setnx(categoryEnum, key, key, expire);
        }

        public void unLock(SquirrelCategoryEnum categoryEnum, String key) {
            squirrelAdapter.delete(categoryEnum, key);
        }

        public void unLock(String category, String id) {
            unLock(SquirrelCategoryEnum.getByCategory(category), id);
        }

        /**
         * 计数器解锁； 如果值不是数字，则返回； 如果值是数字，则 -1，如果 -1 后值为 0 则删除 key；
         *
         * @param category 锁类型
         * @param key      键
         */
        public void counterUnLock(String category, String key, int expire) {
            SquirrelCategoryEnum categoryEnum = SquirrelCategoryEnum.getByCategory(category);
            Object value = squirrelAdapter.get(categoryEnum, key);
            if (isNull(value) || !isNumber(value)) {
                //为空或者不是数字的时候，直接返回无需解锁
                log.info("无需操作，返回");
                return;
            }
            //读取
            Long number = (Long) squirrelAdapter.get(categoryEnum, key);
            if (number <= 0) {
                log.info("无计数，删除资源");
                squirrelAdapter.delete(categoryEnum, key);
            } else {
                //大于0的
                Long ttl = squirrelAdapter.ttl(categoryEnum, key);
                squirrelAdapter.decrBy(categoryEnum, key, 1L, ttl == null || ttl < 0 ? expire : ttl.intValue());
                Boolean isDeleted = squirrelAdapter.compareAndDelete(categoryEnum, key, 0L);
                log.info("删除{}的结果为{}", key, isDeleted);
            }
        }

        /**
         * 不带重试的计数器锁
         *
         * @param category 锁类型
         * @param key      键
         * @param expire   过期时间，单位秒
         * @return true:加锁成功，false，加锁失败
         */
        public boolean counterLockWithoutRetry(String category, String key, int expire) {
            try {
                SquirrelCategoryEnum categoryEnum = SquirrelCategoryEnum.getByCategory(category);
                Object value = squirrelAdapter.get(categoryEnum, key);
                if (!isNull(value) && !isNumber(value)) {
                    //不为空且不是数字的时候，加锁失败
                    //为空或者是数字的时候，可以加锁
                    return false;
                }
                if (!squirrelAdapter.exists(categoryEnum, key)) {
                    //如果key不存在
                    squirrelAdapter.setnx(categoryEnum, key, 1L, expire);
                } else {
                    //如果Key存在
                    squirrelAdapter.incrBy(categoryEnum, key, 1L, expire);
                }
                return true;
            } catch (Exception e) {
                log.error("counterLockWithoutRetry failed, key: " + key, e);
                return false;
            }
        }

        private boolean isNumber(Object value) {
            return value instanceof Long;
        }

        private boolean isNull(Object value) {
            return Objects.isNull(value);
        }


    }
}
