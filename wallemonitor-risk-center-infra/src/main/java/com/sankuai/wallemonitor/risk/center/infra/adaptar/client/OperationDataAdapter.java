package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.BaAuthUtils;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.OperationDataSummaryRequest;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.OperationDataVTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OperationDataAdapter {

    @Value("${mender.url}")
    private String menderUrl;

    @Value("${mender.clientId}")
    private String clientId;

    @Value("$KMS{mender.operation.data.sk}")
    private String BA_SECRET_KEY;

    private static final String GET_OPERATION_DATA_URL = "/operationdata/summary/ba";

    private static final String DEFAULT_IGNORE_CONTENT = "false";

    /**
     * 按批次获取地图区域数据 接口文档：https://km.sankuai.com/collabpage/2048000670
     */
    public List<OperationDataVTO> getOperationDataByPage(List<String> operationTypeList, Integer pageNum,
            Integer pageSize) {
        SystemCheckUtil.isNotEmpty(operationTypeList, "getOperationData, operationTypeList不能为空");
        SystemCheckUtil.isNotNull(pageNum, "getOperationData, pageNum不能为空");
        SystemCheckUtil.isNotNull(pageSize, "getOperationData, pageSize不能为空");

        String operationTypes = String.join(",", operationTypeList);

        Map<String, String> params = new HashMap<>();
        params.put("operation_types", operationTypes);
        params.put("ignore_content", DEFAULT_IGNORE_CONTENT);
        params.put("page_size", String.valueOf(pageSize));
        params.put("page_num", String.valueOf(pageNum));
        String url = menderUrl + GET_OPERATION_DATA_URL;

        Map<String, String> headers = BaAuthUtils.genMWSAuthHeader(CommonConstant.HTTP_METHOD_GET,
                GET_OPERATION_DATA_URL,
                clientId,
                BA_SECRET_KEY,
                null);
        headers.put("X-Date", headers.get("Date"));
        List<OperationDataVTO> res = new ArrayList<>();
        try {
            log.info("getOperationData, params:{}, url:{}", params, url);
            EveHttpResponse<OperationDataResult> eveHttpResponse = HttpUtils.get(params, url, headers, OperationDataResult.class);
            log.info("eveHttpResponse：{}", JacksonUtils.to(eveHttpResponse));

            if (CollectionUtils.isEmpty(eveHttpResponse.getData().getData())) {
                log.error("get operationData, data is null.");
                //未查到任何信息
                return res;
            }
            return eveHttpResponse.getData().getData();
        } catch (Exception e) {
            log.error("getOperationData error", e);
            return res;
        }
    }

    /**
     * 获取地图区域数据总数
     *
     * @param operationTypeList
     * @return
     */
    public int getOperationDataTotal(List<String> operationTypeList) {
        String operationTypes = String.join(",", operationTypeList);

        Map<String, String> params = new HashMap<>();
        params.put("operation_types", operationTypes);
        params.put("page_size", "0");
        params.put("page_num", "1");
        String url = menderUrl + GET_OPERATION_DATA_URL;

        Map<String, String> headers = BaAuthUtils.genMWSAuthHeader(CommonConstant.HTTP_METHOD_GET,
                GET_OPERATION_DATA_URL,
                clientId,
                BA_SECRET_KEY,
                null);
        headers.put("X-Date", headers.get("Date"));
        try {
            log.info("getOperationData, params:{}, url:{}", params, url);
            EveHttpResponse<OperationDataResult> eveHttpResponse = HttpUtils.get(params, url, headers,
                    OperationDataResult.class);
            log.info("eveHttpResponse：{}", JacksonUtils.to(eveHttpResponse));

            return eveHttpResponse.getData().getSize();
        } catch (Exception e) {
            log.error("getOperationData error", e);
            return 0;
        }
    }

    /**
     * 获取一个地图下的所有operation_data
     *
     * @param request 查询请求参数
     * @return operation_data列表和统计信息
     */
    public List<OperationDataVTO> getOperationDataSummary(OperationDataSummaryRequest request) {
        SystemCheckUtil.isNotNull(request, "getOperationDataSummary, request不能为空");

        // 构建查询参数
        Map<String, String> params = buildSummaryQueryParams(request);
        String url = menderUrl + GET_OPERATION_DATA_URL;

        // 构建认证头
        Map<String, String> headers = buildSummaryAuthHeaders();

        try {
            log.info("getOperationDataSummary, params:{}, url:{}", params, url);

            EveHttpResponse<OperationDataResult> response = HttpUtils.get(params, url, headers,
                    OperationDataResult.class);

            log.info("getOperationDataSummary response：{}", JacksonUtils.to(response));

            if (response == null || response.getData() == null) {
                log.error("getOperationDataSummary, response is null");
                return Collections.emptyList();
            }

            return response.getData().getData();

        } catch (Exception e) {
            log.error("getOperationDataSummary error, params:{}", params, e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建摘要查询参数
     */
    private Map<String, String> buildSummaryQueryParams(OperationDataSummaryRequest request) {
        Map<String, String> params = new HashMap<>();

        if (StringUtils.isNotBlank(request.getId())) {
            params.put("id", request.getId());
        }
        if (StringUtils.isNotBlank(request.getName())) {
            params.put("name", request.getName());
        }
        if (StringUtils.isNotBlank(request.getMapName())) {
            params.put("map_name", request.getMapName());
        }
        if (request.getPageIndex() != null) {
            params.put("page_index", String.valueOf(request.getPageIndex()));
        }
        if (request.getPageSize() != null) {
            params.put("page_size", String.valueOf(request.getPageSize()));
        }
        if (StringUtils.isNotBlank(request.getOperationType())) {
            params.put("operation_type", request.getOperationType());
        }
        if (StringUtils.isNotBlank(request.getStartTime())) {
            params.put("start_time", request.getStartTime());
        }
        if (StringUtils.isNotBlank(request.getEndTime())) {
            params.put("end_time", request.getEndTime());
        }
        if (StringUtils.isNotBlank(request.getSubmitter())) {
            params.put("submitter", request.getSubmitter());
        }
        if (request.getStatus() != null) {
            params.put("status", String.valueOf(request.getStatus()));
        }
        if (StringUtils.isNotBlank(request.getCarPurposes())) {
            params.put("car_purposes", request.getCarPurposes());
        }
        if (request.getIgnoreContent() != null) {
            params.put("ignore_content", String.valueOf(request.getIgnoreContent()));
        }

        return params;
    }

    /**
     * 构建摘要查询认证头
     */
    private Map<String, String> buildSummaryAuthHeaders() {
        Map<String, String> headers = BaAuthUtils.genMWSAuthHeader(
                CommonConstant.HTTP_METHOD_GET,
                GET_OPERATION_DATA_URL,
                clientId,
                BA_SECRET_KEY,
                null
        );
        headers.put("X-Date", headers.get("Date"));
        return headers;
    }

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class OperationDataResult {
        private int ret;
        private String msg;
        private int size;
        private List<OperationDataVTO> data;
    }
}
