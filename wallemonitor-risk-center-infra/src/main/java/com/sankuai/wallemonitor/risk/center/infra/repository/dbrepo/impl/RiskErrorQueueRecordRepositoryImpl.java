package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskErrorQueueRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskErrorQueueRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskErrorQueueRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskErrorQueueRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskErrorQueueRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskErrorQueueRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 错误排队检测记录仓储实现
 */
@Component
@Slf4j
public class RiskErrorQueueRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskErrorQueueRecordMapper, RiskErrorQueueRecordConvert, RiskErrorQueueRecord, RiskErrorQueueRecordDO> implements
        RiskErrorQueueRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询错误排队检测记录
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskErrorQueueRecordDO> queryByParam(RiskErrorQueueRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询错误排队检测记录 (分页)
     *
     * @param paramDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public Paging<RiskErrorQueueRecordDO> queryByParamByPage(RiskErrorQueueRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据临时事件ID查询错误排队检测记录
     *
     * @param tmpCaseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskErrorQueueRecordDO getByTmpCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }

    /**
     * 保存错误排队检测记录
     *
     * @param riskErrorQueueRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskErrorQueueRecordDO riskErrorQueueRecordDO) {
        super.save(riskErrorQueueRecordDO);
    }

    /**
     * 批量保存错误排队检测记录
     *
     * @param riskErrorQueueRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskErrorQueueRecordDO> riskErrorQueueRecordDOList) {
        super.batchSave(riskErrorQueueRecordDOList);
    }
} 