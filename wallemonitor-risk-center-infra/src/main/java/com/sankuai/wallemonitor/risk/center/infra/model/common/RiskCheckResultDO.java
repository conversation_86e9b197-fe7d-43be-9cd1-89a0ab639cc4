package com.sankuai.wallemonitor.risk.center.infra.model.common;


import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCheckResultDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 停滞不当检查结果列别
     */
    private ISCheckCategoryEnum category;

    /**
     * 检查时间
     */
    private Date checkTime;

    /**
     * 检查时间
     */
    private Long duration;

    /**
     * 检查来源
     */
    private String checkSource;

    /**
     * 检查的结果
     */
    private Map<String, Object> extra;

}
