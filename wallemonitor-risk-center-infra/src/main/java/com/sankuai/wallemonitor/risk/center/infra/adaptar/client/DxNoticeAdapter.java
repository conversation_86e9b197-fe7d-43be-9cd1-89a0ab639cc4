package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.carosscan.request.DxCardSendRequest;
import com.sankuai.carosscan.request.DxCardUpdateRequest;
import com.sankuai.carosscan.request.DxGroupCreateRequest;
import com.sankuai.carosscan.request.DxGroupMsgSendRequest;
import com.sankuai.carosscan.request.DxNoticeCreateMessageRequest;
import com.sankuai.carosscan.request.DxNoticeUpdateMessageRequest;
import com.sankuai.carosscan.request.DxUpdateGroupRoleNameRequest;
import com.sankuai.carosscan.request.GroupRoleName;
import com.sankuai.carosscan.response.DxCardSendResultDTO;
import com.sankuai.carosscan.response.DxCardUpdateResultDTO;
import com.sankuai.carosscan.response.DxGroupCreateResponse;
import com.sankuai.carosscan.response.DxNoticeCreateMessageResultDTO;
import com.sankuai.carosscan.response.DxUpdateGroupRoleNameResultVO;
import com.sankuai.carosscan.service.IThriftDxCardService;
import com.sankuai.carosscan.service.IThriftDxNoticeService;
import com.sankuai.carosscan.service.IThriftDxUpdateGroupService;
import com.sankuai.walleeve.commons.enums.ErrorCode.EveStatusCode;
import com.sankuai.walleeve.thrift.response.EmptyResponse;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.NoticeTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxCardParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxCardUpdateParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxNoticeParamVTO;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

/**
 * 大象通知服务
 */
@Component
@Slf4j
public class DxNoticeAdapter {

    /**
     * 外部调用服务
     */
    @ThriftClientProxy(remoteAppKey = AppKeyConstant.OUTPUT_APP_KEY, timeout = 5000)
    private IThriftDxNoticeService iThriftDxNoticeService;

    /**
     * 外部调用服务
     */
    @ThriftClientProxy(remoteAppKey = AppKeyConstant.OUTPUT_APP_KEY, timeout = 5000)
    private IThriftDxCardService iThriftDxCardService;

    /**
     * 外部调用服务
     */
    @ThriftClientProxy(remoteAppKey = AppKeyConstant.OUTPUT_APP_KEY, timeout = 5000)
    private IThriftDxUpdateGroupService iThriftDxUpdateGroupService;


    /**
     * 发消息
     *
     * @param paramVTO
     * @return
     */
    @Deprecated
    public String createOrUpdateDxMessage(DxNoticeParamVTO paramVTO) {
        String messageId = CharConstant.CHAR_EMPTY;
        try {
            SystemCheckUtil.isNotBlank(paramVTO.getTemplateId(), "模板ID不可以为空");
            SystemCheckUtil.isNotEmpty(paramVTO.getGroupIdList(), "群ID不可以为空");
            SystemCheckUtil.isNotEmpty(paramVTO.getParams(), "渲染参数不可以为空");
            SystemCheckUtil.isNotBlank(paramVTO.getOutBizId(), "外部唯一键不可以为空");
            if (StringUtils.isBlank(paramVTO.getMessageId())) {
                //更新消息
                EveThriftResponse<DxNoticeCreateMessageResultDTO> createResponse = iThriftDxNoticeService.createMessage(
                        DxNoticeCreateMessageRequest.builder()
                                .templateId(NumberUtils.toLong(paramVTO.getTemplateId()))
                                .arguments(paramVTO.getParams())
                                .bizId(paramVTO.getOutBizId())
                                .channel(NoticeTypeEnum.DYNAMIC.getCode())
                                .gidSet(new HashSet<>(paramVTO.getGroupIdList()))
                                .version(String.valueOf(paramVTO.getVersion()))
                                .build());
                messageId = Optional.ofNullable(createResponse).map(EveThriftResponse::getData)
                        .map(DxNoticeCreateMessageResultDTO::getMessageId).orElse(CharConstant.CHAR_EMPTY);

            } else {
                EveThriftResponse<EmptyResponse> updateMessage = iThriftDxNoticeService.updateMessage(
                        DxNoticeUpdateMessageRequest.builder()
                                .arguments(paramVTO.getParams())
                                .messageId(paramVTO.getMessageId())
                                .version(String.valueOf(paramVTO.getVersion()))
                                .build());
                messageId = Optional.ofNullable(updateMessage).map(EveThriftResponse::getCode)
                        .filter(code -> Objects.equals(code, EveStatusCode.OK.getCode()))
                        .map(code -> paramVTO.getMessageId()).orElse(CharConstant.CHAR_EMPTY);
            }
            return messageId;
        } catch (Exception e) {
            log.error("大象通知异常", e);
            return messageId;
        }
    }

    /**
     * 创建大象群组
     *
     * @param name
     * @param owner
     * @param admins
     * @param members
     * @param botAdmins
     * @return
     */
    public Long createDxGroup(String name, String owner, List<String> admins, List<String> members,
            List<Long> botAdmins) {
        try {
            DxGroupCreateRequest request = DxGroupCreateRequest.builder()
                    .name(name)
                    .admins(admins)
                    .users(members)
                    .owner(owner)
                    .botAdmins(botAdmins)
                    .build();
            log.info("createDxGroup:{}", request);
            EveThriftResponse<DxGroupCreateResponse> response = iThriftDxNoticeService.createDxGroup(request);
            log.info("createDxGroup:{}", response);
            if (Objects.isNull(response) || !Objects.equals(response.getCode(), EveStatusCode.OK.getCode())) {
                throw new RuntimeException("创建大象群组失败");
            }
            return response.getData().getGid();
        } catch (Exception e) {
            log.error("创建大象群聊异常", e);
            throw new RuntimeException("创建大象群组失败");
        }
    }

    /**
     * 发送大象消息
     *
     * @param gid
     * @param msgBody
     */
    public void sendDxMessage(Long gid, String msgBody) {
        try {
            Map<String, Object> msgBodyMap = new HashMap<>();
            msgBodyMap.put("text", msgBody);
            DxGroupMsgSendRequest request = DxGroupMsgSendRequest.builder()
                    .gid(gid)
                    .bodyJson(JacksonUtils.to(msgBodyMap))
                    .messageType("text")
                    .extension("{}")
                    .build();
            log.info("sendDxMessage:{}", request);
            EveThriftResponse<DxGroupCreateResponse> response = iThriftDxNoticeService.sendDxMessage(request);
            log.info("sendDxMessage:{}", response);
            if (Objects.isNull(response) || !Objects.equals(response.getCode(), EveStatusCode.OK.getCode())) {
                throw new RuntimeException("发送大象消息失败");
            }
        } catch (Exception e) {
            log.error("大象通知异常", e);
        }
    }

    /**
     * 更新大象群成员角色名称
     *
     * @param gid
     * @param groupRoleNames
     */
    public void updateDxGroupRoleName(Long gid, List<GroupRoleName> groupRoleNames) {
        try {
            DxUpdateGroupRoleNameRequest request = DxUpdateGroupRoleNameRequest.builder()
                    .gid(gid)
                    .groupRoleNames(groupRoleNames)
                    .build();
            log.info("updateDxGroupRoleName:{}", request);
            EveThriftResponse<DxUpdateGroupRoleNameResultVO> response = iThriftDxUpdateGroupService.batchUpdateDxGroupRoleName(
                    request);
            log.info("updateDxGroupRoleName:{}", response);
            if (Objects.isNull(response) || !Objects.equals(response.getCode(), EveStatusCode.OK.getCode())) {
                throw new RuntimeException("更新大象群成员角色名称失败");
            }
        } catch (Exception e) {
            log.error("更新大象群成员角色名称失败", e);
        }
    }

    /**
     * 发送大象卡片
     *
     * @param paramVTO
     * @return 消息id
     */
    public String sendDxCard(DxCardParamVTO paramVTO) {
        String messageId = CharConstant.CHAR_EMPTY;
        try {
            SystemCheckUtil.isNotNull(paramVTO.getTemplateId(), "模板ID不可以为空");
            SystemCheckUtil.isNotEmpty(paramVTO.getGroupIdList(), "群ID不可以为空");
            SystemCheckUtil.isNotBlank(paramVTO.getArguments(), "渲染参数不可以为空");
            SystemCheckUtil.isNotBlank(paramVTO.getAbstractText(), "摘要信息不可以为空");
            SystemCheckUtil.isNotBlank(paramVTO.getOutBizId(), "外部唯一键不可以为空");
            SystemCheckUtil.isNotNull(paramVTO.getVersion(), "版本信息不可以为空");

            DxCardSendRequest sendRequest = DxCardSendRequest.builder()
                    .templateId(paramVTO.getTemplateId())
                    .arguments(paramVTO.getArguments())
                    .abstractText(paramVTO.getAbstractText())
                    .bizId(paramVTO.getOutBizId())
                    .gidSet(new HashSet<>(paramVTO.getGroupIdList()))
                    .version(paramVTO.getVersion())
                    .uidList(paramVTO.getUidList())
                    .build();
            log.info("send dxCard request: {}", JacksonUtils.to(sendRequest));
            EveThriftResponse<DxCardSendResultDTO> sendResponse = iThriftDxCardService.sendCard(sendRequest);
            log.info("send dxCard response: {}", JacksonUtils.to(sendResponse));
            messageId = Optional.ofNullable(sendResponse).map(EveThriftResponse::getData)
                    .map(DxCardSendResultDTO::getMessageId).orElse(CharConstant.CHAR_EMPTY);

            return messageId;
        } catch (Exception e) {
            log.error("发送大象卡片异常", e);
            return messageId;
        }
    }

    /**
     * 更新大象卡片
     *
     * @param paramVTO
     * @return 消息id
     */
    public String updateDxCard(DxCardUpdateParamVTO paramVTO) {
        String messageId = CharConstant.CHAR_EMPTY;
        try {
            SystemCheckUtil.isNotBlank(paramVTO.getMessageId(), "消息ID不可以为空");
            SystemCheckUtil.isNotBlank(paramVTO.getArguments(), "渲染参数不可以为空");
            SystemCheckUtil.isNotNull(paramVTO.getVersion(), "版本信息不可以为空");

            DxCardUpdateRequest dxCardUpdateRequest = DxCardUpdateRequest.builder()
                    .arguments(paramVTO.getArguments())
                    .messageId(paramVTO.getMessageId())
                    .version(paramVTO.getVersion())
                    .build();
            log.info("update dxCard request: {}", JacksonUtils.to(dxCardUpdateRequest));
            EveThriftResponse<DxCardUpdateResultDTO> updatedResponse = iThriftDxCardService.updateCard(
                    dxCardUpdateRequest);
            log.info("update dxCard response: {}", JacksonUtils.to(updatedResponse));
            messageId = Optional.ofNullable(updatedResponse).map(EveThriftResponse::getCode)
                    .filter(code -> Objects.equals(code, EveStatusCode.OK.getCode()))
                    .map(code -> paramVTO.getMessageId()).orElse(CharConstant.CHAR_EMPTY);
            return messageId;
        } catch (Exception e) {
            log.error("更新大象卡片异常", e);
            return messageId;
        }
    }

}
