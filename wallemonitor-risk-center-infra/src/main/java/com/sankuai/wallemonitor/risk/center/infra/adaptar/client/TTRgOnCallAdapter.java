package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.carosscan.request.RgOnCallQueryRequest;
import com.sankuai.carosscan.response.RgOnCallInfoVO;
import com.sankuai.carosscan.service.IThriftTTRgOnCallService;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * TT 排班服务
 */
@Component
@Slf4j
public class TTRgOnCallAdapter {

    /**
     * 外部调用服务
     */
    @ThriftClientProxy(remoteAppKey = AppKeyConstant.OUTPUT_APP_KEY, timeout = 5000)
    private IThriftTTRgOnCallService iThriftTTRgOnCallService;

    /**
     * 查询rg值班人员
     *
     * @param rgId
     * @return
     */
    public List<String> queryRgOnCallUserList(Long rgId) {

        try {
            RgOnCallQueryRequest request = RgOnCallQueryRequest.builder().rgId(rgId).build();
            log.info("queryRgOnCallUserList request:{}", request);
            EveThriftResponse<RgOnCallInfoVO> response = iThriftTTRgOnCallService.queryRgOnCallUserList(request);
            log.info("queryRgOnCallUserList response:{}", response);
            if (Objects.isNull(response) || !Objects.equals(response.getCode(), 0)) {
                log.error("queryRgOnCallUserList error");
                return new ArrayList<>();
            }
            return response.getData().getOnCallUserList();
        } catch (Exception e) {
            log.error("queryRgOnCallUserList error", e);
            return new ArrayList<>();
        }

    }
}
