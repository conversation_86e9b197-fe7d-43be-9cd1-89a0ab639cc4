package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ThemeEnum {
    BLUE("blue"),
    GRE<PERSON>("green"),
    ORANGE("orange"),
    RED("red"),
    <PERSON><PERSON><PERSON><PERSON>("purple"),
    GREY("grey");

    private final String value;

    /**
     * 找到主题颜色
     *
     * @return
     */
    public static String findThemeByValue(RiskCaseStatusEnum statusEnum) {
        switch (statusEnum) {
            case NO_DISPOSAL:
                return ThemeEnum.RED.getValue();
            case IN_DISPOSAL:
                return ThemeEnum.ORANGE.getValue();
            case DISPOSED:
                return ThemeEnum.GREEN.getValue();
            default: {
                return GREY.value;
            }

        }

    }
}
