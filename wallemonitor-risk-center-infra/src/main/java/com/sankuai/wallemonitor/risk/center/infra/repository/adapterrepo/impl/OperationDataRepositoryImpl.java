package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.OperationDataAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.OperationDataSummaryRequest;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.OperationDataRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.ParallelExecutor;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.OperationDataVTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 运营数据仓储层实现
 *
 * <AUTHOR>
 * @date 2025/06/15
 */
@Slf4j
@Component
public class OperationDataRepositoryImpl implements OperationDataRepository {

    private final int CACHE_EXPIRE_MINUTES = 10;

    @Resource
    private OperationDataAdapter operationDataAdapter;

    /**
     * ID到运营数据的缓存，10分钟过期
     */
    private LoadingCache<String, OperationDataVTO> idToOperationDataCache;

    @PostConstruct
    public void init() {
        this.idToOperationDataCache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
                .build(new CacheLoader<String, OperationDataVTO>() {
                    @Override
                    public OperationDataVTO load(String id) throws Exception {
                        return loadOperationDataById(id);
                    }
                });
    }

    @Override
    public OperationDataVTO getOperationDataById(String id) {
        if (id == null) {
            return null;
        }

        try {
            return idToOperationDataCache.get(id);
        } catch (ExecutionException e) {
            log.error("获取运营数据失败, id: {}", id, e);
            return null;
        }
    }

    @Override
    public Map<String, OperationDataVTO> getOperationDataByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }

        // 过滤空ID
        List<String> validIds = ids.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validIds)) {
            return new HashMap<>();
        }

        Map<String, OperationDataVTO> result = new HashMap<>();
        List<String> uncachedIds = new ArrayList<>();

        // 先从缓存中获取已有数据
        for (String id : validIds) {
            OperationDataVTO cachedData = idToOperationDataCache.getIfPresent(id);
            if (cachedData != null) {
                result.put(id, cachedData);
            } else {
                uncachedIds.add(id);
            }
        }

        // 对未缓存的ID进行批量查询
        if (CollectionUtils.isNotEmpty(uncachedIds)) {
            Map<String, OperationDataVTO> uncachedResult = batchLoadOperationData(uncachedIds);
            result.putAll(uncachedResult);
        }

        return result;
    }

    /**
     * 批量加载运营数据
     *
     * @param ids 待加载的ID列表
     * @return ID到运营数据的映射
     */
    private Map<String, OperationDataVTO> batchLoadOperationData(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }

        // 使用ParallelExecutor并发查询多个ID，提高查询效率
        List<OperationDataVTO> dataList = ParallelExecutor.executeParallelTasksAndGetResult(
                "mender_operation_data_loader", ids, this::loadOperationDataById);

        // 将结果转换为Map，并放入缓存
        Map<String, OperationDataVTO> result = new HashMap<>();
        for (OperationDataVTO data : dataList) {
            if (data == null || StringUtils.isBlank(data.getId())) {
                continue;
            }
            idToOperationDataCache.put(data.getId(), data);
            // 因为id为模糊查询，所以这里需要过滤一下
            if (ids.contains(data.getId())) {
                result.put(data.getId(), data);
            }
        }

        log.info("批量加载运营数据完成, 请求数量: {}, 成功加载数量: {}", ids.size(), result.size());
        return result;
    }

    /**
     * 根据ID加载单个运营数据
     *
     * @param id 运营数据ID
     * @return 运营数据，如果未找到返回null
     */
    private OperationDataVTO loadOperationDataById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        try {
            OperationDataSummaryRequest request = OperationDataSummaryRequest.builder().id(id).build();

            List<OperationDataVTO> result = operationDataAdapter.getOperationDataSummary(request);
            if (CollectionUtils.isEmpty(result)) {
                log.warn("未找到运营数据, id: {}", id);
                return null;
            }
            // 因为id为模糊查询，所以这里需要过滤一下
            return result.stream().filter(operationData -> id.equals(operationData.getId())).findFirst().orElse(null);
        } catch (Exception e) {
            log.error("加载运营数据失败, id: {}", id, e);
            return null;
        }
    }
} 