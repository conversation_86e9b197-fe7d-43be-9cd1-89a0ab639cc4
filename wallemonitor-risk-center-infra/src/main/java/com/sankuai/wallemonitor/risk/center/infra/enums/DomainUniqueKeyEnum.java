package com.sankuai.wallemonitor.risk.center.infra.enums;


import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventRelatedDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDetectorRecordBaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDriveOnTrafficLineRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskRetrogradeRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskSpecialAreaStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DomainUniqueKeyEnum {

    RISK_CASE(Collections.singletonList("caseId"), RiskCaseDO.class),

    RISK_CASE_VEHICLE(Arrays.asList("eventId", "vin"), RiskCaseVehicleRelationDO.class),

    RISK_CHECKING_QUEUE_ITEM(Collections.singletonList("tmpCaseId"), RiskCheckingQueueItemDO.class),

    RISK_SPECIAL_AREA_STRANDING_RECORD(Collections.singletonList("tmpCaseId"), RiskSpecialAreaStrandingRecordDO.class),

    RISK_RETROGRADE_RECORD(Collections.singletonList("tmpCaseId"), RiskRetrogradeRecordDO.class),

    RISK_DRIVE_ON_TRAFFIC_LINE_RECORD(Collections.singletonList("tmpCaseId"), RiskDriveOnTrafficLineRecordDO.class),

    VEHICLE_RUNTIME_INFO_CONTEXT(Collections.singletonList("vin"), VehicleRuntimeInfoContextDO.class),

    RISK_DETECT_BASE_RECORD(Collections.singletonList("tmpCaseId"), RiskDetectorRecordBaseDO.class),

    CASE_MARK_INFO(Collections.singletonList("caseId"), CaseMarkInfoDO.class),

    RISK_STRANDING_DETECT_RECORD(Collections.singletonList("tmpCaseId"), RiskStrandingRecordDO.class),

    NEGATIVE_PUBLIC_EVENT_DETAIL_DO(Collections.singletonList("eventId"), NegativePublicEventDetailDO.class),
    NEGATIVE_PUBLIC_EVENT_RELATED_DO(Collections.singletonList("eventId"), NegativePublicEventRelatedDO.class),
    NEGATIVE_PUBLIC_EVENT_DO(Collections.singletonList("eventId"), NegativePublicEventDO.class),

    ;


    /**
     * 表字段唯一键
     */
    public List<String> tableUniqueKeyList;
    /**
     * 领域类的类型
     */
    private Class<?> domainClass;

    /**
     * 获取某个类的唯一键
     *
     * @param domainClass
     * @return
     */
    public static List<String> getUniqueByClass(Class<?> domainClass) {
        if (domainClass == null) {
            return new ArrayList<>();
        }
        for (DomainUniqueKeyEnum uniqueKeyEnum : DomainUniqueKeyEnum.values()) {
            if (uniqueKeyEnum.getDomainClass().equals(domainClass)) {
                return uniqueKeyEnum.getTableUniqueKeyList();
            }
        }
        return new ArrayList<>();
    }


}
