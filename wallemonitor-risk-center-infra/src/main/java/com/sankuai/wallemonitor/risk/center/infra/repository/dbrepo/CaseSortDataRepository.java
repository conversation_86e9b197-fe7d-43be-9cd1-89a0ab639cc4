package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseSortDataDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseSortDataDOQueryParam;
import java.util.List;
import java.util.Map;

/**
 * 分拣数据仓储层
 *
 * <AUTHOR>
 * @Date 2024/7/2
 */
public interface CaseSortDataRepository {

    /**
     * 根据参数查询风险事件分拣数据
     *
     * @param param
     * @return
     */
    List<CaseSortDataDO> queryByParam(CaseSortDataDOQueryParam param);

    /**
     * 根据参数查询风险事件分拣数据
     *
     * @param param
     * @return
     */
    Map<String, CaseSortDataDO> queryMapByParam(CaseSortDataDOQueryParam param);

    /**
     * 根据风险id查询事件分拣数据
     *
     * @param caseId
     * @return
     */
    CaseSortDataDO getByCaseId(String caseId);

    /**
     * 保存
     *
     * @param caseSortDataDO
     */
    void save(CaseSortDataDO caseSortDataDO);

    /**
     * 批量保存
     *
     * @param caseSortDataDOList
     */
    void batchSave(List<CaseSortDataDO> caseSortDataDOList);


}
