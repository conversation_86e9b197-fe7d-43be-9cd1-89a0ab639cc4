package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.google.common.collect.ImmutableMap;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.map.open.platform.api.iplocate.IpLocateRequest;
import com.sankuai.map.open.platform.api.iplocate.IpLocateResponse;
import com.sankuai.map.open.platform.api.iplocate.IpReverseAddress;
import com.sankuai.map.open.platform.api.regeo.RegeoResponse;
import com.sankuai.map.open.platform.api.regeo.Regeocode;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppPropertiesConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.PositionConvert;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteCallMapOpenApiTimeoutException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GisInfoVTO;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Gis适配器
 */
@Component
@Slf4j
public class GisAdapter {

    @Resource
    private PositionConvert positionDOConvert;

    @ThriftClientProxy(remoteAppKey = "com.sankuai.apigw.map.facadecenter")
    private MapOpenApiService.Iface mapOpenApiService;

    @Value(AppPropertiesConstant.CONFIG_VALUE_MAP_DOMAIN)
    private String LBS_DOMAIN;

    @Value(AppPropertiesConstant.CONFIG_VALUE_MAP_KEY)
    private String mapKey;

    /**
     * 筛选response结果中的可选字段，默认为base。
     */
    private static final String SCENARIO = "base|dp_city|businessarea|admin|poi";
    private static final String KEY_MAP_KEY = "key";
    private static final String KEY_LOCATION = "location";

    /**
     * 地理位置path
     */
    private static final String RE_GEO_PATCH = "/v1/location/regeo";

    private static final String KEY_SHOW_FIELDS = "show_fields";

    /**
     * 查询定位服务响应状态码
     */
    private static final Integer RESPONSE_STATUS = 200;

    /**
     * RPC 地图服务key
     */
    @Value(AppPropertiesConstant.CONFIG_VALUE_RPC_MAP_KEY)
    private String rpcMapKey;


    /**
     * 获取gis信息
     *
     * @param positionDO
     * @return
     */
    public GisInfoVTO getGisInfo(PositionDO positionDO) {

        try {
            if (positionDO == null) {
                return null;
            }
            //转换坐标系
            positionDO = positionDOConvert.toPositionDO(positionDO, CoordinateSystemEnum.GCJ02);
            RegeoResponse response = handleGetReGeo(positionDO);
            log.info("getGisInfo response:{}", response);
            if (response == null || response.getStatus() != HttpStatus.SC_OK) {
                return null;
            }
            if (CollectionUtils.isEmpty(response.getRegeocode())) {
                return null;
            }
            Regeocode regeocode = response.getRegeocode().stream().filter(Objects::nonNull).findFirst().orElse(null);
            String city = Optional.ofNullable(regeocode).map(Regeocode::getCity).orElse(CharConstant.CHAR_EMPTY);
            String areaCode = Optional.ofNullable(regeocode).map(Regeocode::getDistrict)
                    .orElse(CharConstant.CHAR_EMPTY);
            String poiName = Optional.ofNullable(regeocode).map(Regeocode::getFormatted_address)
                    .orElse(CharConstant.CHAR_EMPTY);
            return GisInfoVTO.builder()
                    .area(areaCode)
                    .city(city)
                    .poi(poiName)
                    .latitude(positionDO.getLatitude())
                    .longitude(positionDO.getLongitude())
                    .coordinateSystem(positionDO.getCoordinateSystem())
                    .build();
        } catch (RemoteCallMapOpenApiTimeoutException e) {
            throw e;
        } catch (Exception e) {
            log.error("地理编码出现异常", e);
        }
        return null;
    }

    /**
     * 根据ip获取地址
     *
     * @param ip IP地址
     * @return IpReverseAddress
     */
    public IpReverseAddress getAddressByIp(String ip) {
        if (StringUtils.isBlank(ip)) {
            return null;
        }
        // 1 构建定位查询请求参数
        IpLocateRequest ipLocateRequest = new IpLocateRequest();
        ipLocateRequest.setIp(ip).setKey(rpcMapKey);
        log.info("IpLocateAdapter#getAdressByIp IpLocateRequest:{}", ipLocateRequest);
        // 2 查询定位地址
        try {
            IpLocateResponse response = mapOpenApiService.ipLocate(ipLocateRequest);
            ;
            log.info("IpLocateAdapter#getAdressByIp IpLocateResponse:{}", response);
            if (Objects.nonNull(response) && Objects.equals(response.getStatus(), RESPONSE_STATUS)) {
                return response.getData().getIp_reverse_address();
            }
        } catch (Exception e) {
            log.error(String.format("IpLocateAdapter#getAdressByIp error, request:%s", ipLocateRequest), e);
        }
        return null;
    }

    private RegeoResponse handleGetReGeo(PositionDO positionDO) {
        try {
            EveHttpResponse<RegeoResponse> response = HttpUtils.get(
                    ImmutableMap.of(KEY_MAP_KEY, mapKey, KEY_LOCATION,
                            positionDO.getLocationStr(CoordinateSystemEnum.GCJ02), KEY_SHOW_FIELDS,
                            SCENARIO)
                    , LBS_DOMAIN, RE_GEO_PATCH, new HashMap<>(), RegeoResponse.class);
            log.info("re geo response:{}", JacksonUtils.to(response));
            if (response == null || response.getCode() != HttpStatus.SC_OK) {
                return null;
            }
            return response.getData();
        } catch (SocketTimeoutException e) {
            throw new RemoteCallMapOpenApiTimeoutException("地图服务调用超时");
        } catch (Exception e) {
            log.error("地理位置描述调用异常", e);
            return null;
        }
    }
}
