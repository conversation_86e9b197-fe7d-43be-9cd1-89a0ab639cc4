package com.sankuai.wallemonitor.risk.center.infra.utils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

public class UrlEncodeUtil {
    /**
     * 只对URL的查询参数部分进行编码，而不是对整个URL进行编码。这是因为URL的其它部分（如协议、主机名、路径）通常不需要编码
     *
     * @param param
     * @return
     */
    @SneakyThrows
    public static String formatUrlParam(String param) {
        if (StringUtils.isBlank(param)) {
            return null;
        }
        return URLEncoder.encode(param, StandardCharsets.UTF_8.toString());
    }
}
