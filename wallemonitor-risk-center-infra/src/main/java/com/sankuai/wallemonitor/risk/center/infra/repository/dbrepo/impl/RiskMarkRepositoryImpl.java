package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.SquirrelCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskMarkRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskMarkRepositoryImpl implements RiskMarkRepository {



    @Resource
    private SquirrelAdapter squirrelAdapter;

    /**
     * 查询最新的markItem
     *
     * <p>
     * 根据caseId查询db的item,然后做
     * <p>
     *
     * @param caseId
     * @param version
     */
    @Override
    @RepositoryQuery
    public RiskCheckingQueueItemDO getMarkItem(String caseId, String version) {
        if (StringUtils.isBlank(caseId)) {
            return null;
        }
        // 获取缓存里面的标注结果
        return squirrelAdapter.getStrAsObject(SquirrelCategoryEnum.MARK_ITEM_CATEGORY, buildKey(caseId, version),
                RiskCheckingQueueItemDO.class);
    }

    /**
     * 保存item
     *
     * @param itemList
     * @param version
     */
    @Override
    public void saveMarkItem(List<RiskCheckingQueueItemDO> itemList, String version) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        // 保存标注的数据
        squirrelAdapter.multiSetObjectAsStr(SquirrelCategoryEnum.MARK_ITEM_CATEGORY,
                itemList.stream().collect(Collectors.toMap(item ->
                // 获取构建的数据
                this.buildKey(item.getTmpCaseId(), version), Function.identity(), (k1, k2) -> k1)),
                SquirrelCategoryEnum.MARK_ITEM_CATEGORY.getDefaultTimeOut());

    }

    @Override
    public void deleteMarkItem(List<RiskCheckingQueueItemDO> itemList, String version) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        squirrelAdapter.multiDelete(SquirrelCategoryEnum.MARK_ITEM_CATEGORY,
                // 构建key
                buildKey(itemList, version));
    }



    /**
     * 构建key
     */
    private String buildKey(String caseId, String version) {
        if (Objects.isNull(caseId)) {
            return CharConstant.CHAR_EMPTY;
        }
        return StringUtils.join(caseId, StringUtils.defaultIfBlank(version, CharConstant.CHAR_EMPTY));
    }

    /**
     * 构建key
     */
    private List<String> buildKey(List<RiskCheckingQueueItemDO> itemList, String version) {
        if (CollectionUtils.isEmpty(itemList)) {
            return new ArrayList<>();
        }
        return itemList.stream().map(item -> this.buildKey(item.getTmpCaseId(), version)).collect(Collectors.toList());
    }
}
