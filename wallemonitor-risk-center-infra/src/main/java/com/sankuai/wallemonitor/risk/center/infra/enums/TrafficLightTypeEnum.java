package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum TrafficLightTypeEnum {
    NONE(0, ""),
    UNKNOWN(0, ""),
    RED(1, "红灯"),
    YELLOW(2, "黄灯"),
    GRE<PERSON>(3, "绿灯"),
    BLACK(4, "黑灯"),
    NONE_COLOR(5, "无灯");
    private int code;
    private String desc;


    public static TrafficLightTypeEnum getByName(String color) {
        if (StringUtils.isBlank(color)) {
            return NONE;
        }
        for (TrafficLightTypeEnum value : TrafficLightTypeEnum.values()) {
            if (value.name().equals(color)) {
                return value;
            }
        }
        return NONE;
    }

    public static List<TrafficLightTypeEnum> listNormalColor() {
        return Arrays.asList(RED, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>);
    }
}
