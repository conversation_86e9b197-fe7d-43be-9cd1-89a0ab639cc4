package com.sankuai.wallemonitor.risk.center.infra.exception;

import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;

public class RemoteCallMapOpenApiTimeoutException extends ErrorCodeException {
    
    public RemoteCallMapOpenApiTimeoutException(String message) {
        super(ResponseCodeEnum.REMOTE_ERROR.getCode(), message);
    }
}

