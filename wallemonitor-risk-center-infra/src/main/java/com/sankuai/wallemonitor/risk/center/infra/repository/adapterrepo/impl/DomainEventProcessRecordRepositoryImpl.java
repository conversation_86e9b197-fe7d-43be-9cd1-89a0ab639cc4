package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl;

import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventProcessResultDO;
import com.sankuai.wallemonitor.risk.center.infra.enums.SquirrelCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.DomainEventProcessRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeConstant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DomainEventProcessRecordRepositoryImpl implements
        DomainEventProcessRecordRepository {


    @Resource
    private SquirrelAdapter squirrelAdapter;

    /**
     * 获取某个入口和时间下的领域事件结果
     *
     * @param processNameList
     * @param entry
     * @param eventTime
     * @param traceId
     * @return
     */
    @Override
    public List<DomainEventProcessResultDO> getProcessResult(List<String> processNameList, DomainEventEntryDTO entry
            , Long eventTime, String traceId) {
        //从缓存中获取对象
        try {
            return squirrelAdapter.multiGet(SquirrelCategoryEnum.DOMAIN_EVENT_PROCESS_RECORD_CATEGORY,
                            handleBuildKeys(processNameList, entry, eventTime, traceId)).values().stream()
                    .map(value -> (DomainEventProcessResultDO) value).filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("读取异步执行逻辑结果异常", e);
            return new ArrayList<>();
        }
    }


    /**
     * 构建key
     *
     * @param processNameList
     * @param entry
     * @param eventTime
     * @param traceId
     * @return
     */
    private List<String> handleBuildKeys(List<String> processNameList, DomainEventEntryDTO entry, Long eventTime,
            String traceId) {

        if (CollectionUtils.isEmpty(processNameList)) {
            return new ArrayList<>();
        }
        return processNameList.stream().map(processName -> handleBuildKeys(processName, entry, eventTime, traceId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 构建key
     *
     * @param processName
     * @param entry
     * @param eventTime
     * @param traceId
     * @return
     */
    private String handleBuildKeys(String processName, DomainEventEntryDTO entry, Long eventTime,
            String traceId) {

        if (StringUtils.isEmpty(processName)) {
            return null;
        }
        //构建Key
        return Joiner.on(CharConstant.CHAR_JH).join(processName, entry.toString(), traceId, eventTime);

    }


    /**
     * 保存领域事件结果
     *
     * @param resultDO
     */
    @Override
    public void save(DomainEventProcessResultDO resultDO) {
        squirrelAdapter.set(SquirrelCategoryEnum.DOMAIN_EVENT_PROCESS_RECORD_CATEGORY,
                resultDO.getEventUniqueKey(), resultDO, DateTimeConstant.ONE_HOUR_SECOND);

    }

    /**
     * 保存领域事件结果
     *
     * @param resultDOList
     */
    @Override
    public void batchSave(List<DomainEventProcessResultDO> resultDOList) {
        if (CollectionUtils.isEmpty(resultDOList)) {
            return;
        }
        resultDOList.forEach(this::save);
    }
}
