package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleStatusChangeInfoDTO<T> {

    /**
     * 变更前的位置信息
     */
    private T beforeChangeInfo;

    /**
     * 变更后的位置信息
     */
    private T afterChangeInfo;

    /**
     * 变更时间
     */
    private Date changeTime;

}
