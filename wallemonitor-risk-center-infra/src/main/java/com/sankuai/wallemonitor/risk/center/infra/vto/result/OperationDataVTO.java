package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SafetyAreaExtInfoDO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Slf4j
public class OperationDataVTO {

    private String id;
    private Integer status;
    @JsonProperty("create_time")
    private String createTime;
    private String submitter;
    private String description;
    private String name;
    @JsonProperty("map_name")
    private String mapName;
    @JsonProperty("operation_type")
    private String operationType;
    @JsonProperty("mender_job_id")
    private Long menderJobId;
    private Content content;

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class Content {
        private String name;
        private String type;
        private AdmapInfo admapInfo;
        private Polygon polygon;
    }

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class AdmapInfo {
        private String admapName;
        private String admapVersion;
        private String admapDir;
        private String hdmapVersion;
        private String ndtmapVersion;
        private String sepdemsVersion;
        private String sepmapsVersion;
    }

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class Polygon {

        @JsonProperty("point")
        private List<PointUtm> pointUtmList;
    }

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class PointUtm {
        private Double x;
        private Double y;
    }

    public SafetyAreaExtInfoDO convert2SafetyAreaExtInfoDO() {
        SafetyAreaExtInfoDO safetyAreaExtInfoDO;
        try {
            String thisSerialString = JacksonUtils.to(this);
            safetyAreaExtInfoDO = JacksonUtils.from(thisSerialString, new TypeReference<SafetyAreaExtInfoDO>() {
            });
        } catch (Exception e) {
            log.error("OperationDataVTO.convert2SafetyAreaExtInfoDO error, OperationDataVTO:{}", JacksonUtils.to(this),
                    e);
            return SafetyAreaExtInfoDO.builder().build();
        }

        return safetyAreaExtInfoDO;
    }
}
