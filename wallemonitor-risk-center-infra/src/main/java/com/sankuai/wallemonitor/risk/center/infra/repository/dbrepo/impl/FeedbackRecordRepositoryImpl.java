package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.convert.FeedbackRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.FeedbackRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.FeedbackRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.FeedbackRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.FeedbackRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FeedbackRecordRepositoryImpl extends
        AbstractMapperSingleRepository<FeedbackRecordMapper, FeedbackRecordConvert, FeedbackRecord, FeedbackRecordDO>
        implements FeedbackRecordRepository {

    /**
     * 保存反馈
     *
     * @param feedbackRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(FeedbackRecordDO feedbackRecordDO) {
        super.save(feedbackRecordDO);

    }
}
