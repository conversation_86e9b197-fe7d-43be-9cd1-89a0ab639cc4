package com.sankuai.wallemonitor.risk.center.infra.dto.aggregate;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聚合结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregateResultDTO {

    /**
     * 聚合键
     */
    private String aggregateKey;

    /**
     * 聚合维度值
     */
    private AggregateDimensions aggregateDimensions;

    /**
     * 事件数量
     */
    private Integer caseCount;

    /**
     * 事件列表
     */
    private List<RiskCaseDO> riskCaseList;

    /**
     * 时间窗口开始时间
     */
    private Date timeWindowStart;

    /**
     * 时间窗口结束时间
     */
    private Date timeWindowEnd;

    /**
     * 统计信息
     */
    private AggregateStatistics statistics;

    /**
     * 策略ID
     */
    private String strategyId;

    /**
     * 聚合维度内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AggregateDimensions {

        /**
         * POI名称
         */
        private String poiName;

        /**
         * 风险类型代码
         */
        private Integer type;

        /**
         * 风险类型描述
         */
        private String typeDesc;

        /**
         * 地点编码
         */
        private String placeCode;

        /**
         * 泊位ID
         */
        private String parkingPlotId;
    }

    /**
     * 聚合统计信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AggregateStatistics {

        /**
         * 事件类型分布
         */
        private Map<String, Long> typeDistribution;

        /**
         * 最早时间
         */
        private Date earliestTime;

        /**
         * 最晚时间
         */
        private Date latestTime;

        /**
         * 时间跨度（秒）
         */
        private Long timeSpanSeconds;
    }
}