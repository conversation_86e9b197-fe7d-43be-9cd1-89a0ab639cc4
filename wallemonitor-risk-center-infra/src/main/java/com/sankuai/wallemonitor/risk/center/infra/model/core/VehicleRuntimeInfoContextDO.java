package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.common.collect.Lists;
import com.sankuai.walleeve.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.MonitorMetricsSceneConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.FCDriveTaskRouteEventDTO.RoutePoint;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficLightConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleObstacleInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.ControlArbitrationInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.ControlArbitrationInfoDTO.MonitorMetrics;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.ControlArbitrationInfoDTO.SceneInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.Acceleration;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.Position;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.Velocity;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO.CommonMeta;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO.ConstructionZoneMeta;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO.FenceAndFieldMetaDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO.HighNegativeEventMeta;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO.OptionalDouble;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO.TouchLineMeta;
import com.sankuai.wallemonitor.risk.center.infra.enums.ConstraintSourceTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.LineType;
import com.sankuai.wallemonitor.risk.center.infra.enums.PositionTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RoadTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleMonitorInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleRunTimeExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl.LionConfigRepositoryImpl.VehicleCounterRuleDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.serializer.DirectionDeserializer;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

/**
 * 车辆运行时信息数据传输对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class VehicleRuntimeInfoContextDO {

    private static final List<String> SAVE_TO_DB_IGNORE_FIELD_LIST = Lists.newArrayList("id", "createTime",
            "updateTime", "isDeleted", "extInfo", "obstacleContext", "fenceContext", "monitorMetricsInfo");

    private String vin;
    private DriverModeEnum driveMode;
    private Double speed;
    private Boolean batterySwitching;
    private String bizStatus;   // 业务状态
    private TrafficLightTypeEnum trafficLightType;
    private Double distanceToJunction;
    private Integer distanceToNextJunction;
    private String lng;
    private String lat;
    private PositionTypeEnum positionType;
    private Boolean oppositeWithRoad;
    private String drivingOnTrafficLineType;
    private Integer distanceToFrontConstructionZone;
    private Boolean pathOverlapWithConstructionZone;
    private Boolean waitingGatePole;
    private VehicleObstacleContextDO obstacleContext;
    private VehicleFenceContextDO fenceContext;
    private Date lastUpdateTime;
    private VehicleCounterInfoDO stagnationCounter;
    private VehicleCounterInfoDO redLightCounter;
    private TrafficLightDO curTrafficLight;
    private VehicleRunTimeExtInfoDO extInfo;

    private Date createTime;
    private Date updateTime;
    private IsDeleteEnum isDeleted;

    private MonitorMetricsDO monitorMetricsInfo;

    private Double trafficFlowSpeed;


    /**
     * 该车当前正在运行的路由信息
     */
    private List<RoutePoint> routePoints;

    /**
     * 导航线
     */
    private List<PositionDO> refinedLineList;
    private List<ObstacleAbstract> obstacleAbstracts; // 存取指定障碍物类型的摘要信息

    @Default
    private Map<String, VehicleCounterInfoDO> vehicleCounterInfo = new HashMap<>(); // 计数器


    /**
     * 预处理数据
     */
    // 基础
    private String vehicleType;
    private String hdMapArea;
    @Default
    private List<VehicleInQueuePositionDTO> prePositionList = new ArrayList<>();   // 行驶轨迹
    private PositionDO vehiclePosition;
    private VehicleInQueuePositionDTO preVehiclePosition;
    @Default
    private List<VehicleInQueuePositionDTO> preVehiclePositionList = new ArrayList<>();
    // 障碍物
    @Default
    private List<VehicleObstacleInfoDTO> allObstacleList = new ArrayList<>(); // 指定范围的障碍物
    private VehicleObstacleInfoDTO frontObstacle;   // 前方障碍物
    private VehicleObstacleInfoDTO behindObstacle;   // 后方障碍物
    @Default
    private List<VehicleObstacleInfoDTO> frontObstacleList = new ArrayList<>();
    @Default
    private List<VehicleObstacleInfoDTO> behindObstacleList = new ArrayList<>();
    // 车道
    @Default
    private List<String> usableLaneIds = new ArrayList<>(); // 可绕行车道ID列表
    private String vehicleCurLaneId; // 当前车道

    @Default
    private List<String> vehicleCurLaneIdList = new ArrayList<>(); //
    @Default
    private Map<String, String> vehicleAroundLaneId = new HashMap<>(); // 周围车道
    private Double distanceToNearCurb; // 车辆距离车道的距离
    @Default
    private List<Double> distanceToNearCurbList = new ArrayList<>(); // 车辆距离车道的距离（左、右）
    private String vehicleLaneSectionType; // 车辆在车道中的类型
    @Builder.Default
    private Boolean singleLane = false; // 是否是单独车道
    @JsonDeserialize(using = DirectionDeserializer.class)
    private Pair<PositionDO, PositionDO> direction; // 行径方向
    @Builder.Default
    private List<String> fcLaneIdList = new ArrayList<>();

    /**
     * 车道中的lane数量
     */
    private Integer laneCount;
    /**
     * 车辆所在车道的相对位置V2
     */
    private String vehicleLaneSectionTypeV2;

    /**
     * 覆盖车辆所在位置的laneId列表
     */
    @Builder.Default
    private List<String> inPolygonLaneIdList = new ArrayList<>();

    /**
     * 车道线与车辆历史轨迹线的夹角
     */
    @Builder.Default
    Map<String, List<Double>> laneId2AngleListMap = new HashMap<>();


    /**
     * 复制属性
     *
     * @param other
     */
    public void updateToSave(VehicleRuntimeInfoContextDO other) {
        //忽略字段
        List<String> ignoreFieldList = Lists.newArrayList(SAVE_TO_DB_IGNORE_FIELD_LIST);
        //去掉积累类型的
        ignoreFieldList.addAll(Arrays.stream(ReflectUtils.getDeclaredFields(this.getClass(), true))
                .filter(field -> VehicleCounterInfoDO.class.isAssignableFrom(field.getType()))
                .map(Field::getName)
                .collect(Collectors.toList()));
        //更新新值
        BeanUtils.copyProperties(other, this, ignoreFieldList.toArray(new String[]{}));
    }

    /**
     * 更新车流速度
     *
     * */
    public void updateTrafficFlowSpeed(Double trafficFlowSpeed) {
        this.trafficFlowSpeed = trafficFlowSpeed;
    }


    /**
     * 更新上下文的积累信息
     *
     * @param fieldCounterMap
     */
    public void updateCounter(List<Pair<Boolean, VehicleCounterRuleDO>> fieldCounterMap) {
        if (CollectionUtils.isEmpty(fieldCounterMap)) {
            return;
        }
        Date currentTime = new Date();
        //获取字段的map
        Map<String, Field> fieldMap = Arrays.stream(ReflectUtils.getDeclaredFields(this.getClass(), true))
                .collect(Collectors.toMap(Field::getName, Function.identity(), (o1, o2) -> o1));
        fieldCounterMap.forEach(rulePair -> {
            Boolean isMatched = rulePair.getKey();
            VehicleCounterRuleDO rule = rulePair.getValue();
            if (isMatched == null || rule == null) {
                //如果计算情况异常，不处理
                return;
            }
            String fieldName = rule.getName();
            Field field = fieldMap.get(fieldName);
            if (!isValidField(field)) {
                log.warn("无效字段: {}", fieldName);
                return;
            }
            //更新积累值
            VehicleCounterInfoDO vehicleCounter = getCounterInfo(field);
            vehicleCounter = updateCounterInfo(vehicleCounter, isMatched, rule, currentTime);
            setCounterInfo(field, vehicleCounter);
        });
    }

    /**
     * 更新上下文的积累信息
     *
     * @param fieldCounterMap
     */
    public void updateCounterMap(List<Pair<Boolean, VehicleCounterRuleDO>> fieldCounterMap) {
        if (CollectionUtils.isEmpty(fieldCounterMap)) {
            return;
        }
        Date currentTime = new Date();
        // 取全部的key
        Set<String> allCounterRuleName = new HashSet<>();
        allCounterRuleName
                .addAll(fieldCounterMap.stream().map(pair -> pair.getValue().getName()).collect(Collectors.toList()));
        allCounterRuleName.addAll(vehicleCounterInfo.keySet());
        Map<String, Pair<Boolean, VehicleCounterRuleDO>> counterMap = new HashMap<>();
        // 名字转换下
        fieldCounterMap.forEach(rulePair -> counterMap.put(rulePair.getValue().getName(), rulePair));
        // 遍历
        allCounterRuleName.forEach(counterRuleName -> {
            // 如果不在规则内，默认是不满足，从而结束
            Pair<Boolean, VehicleCounterRuleDO> rulePair = counterMap.getOrDefault(counterRuleName,
                    new Pair<>(false, VehicleCounterRuleDO.builder().name(counterRuleName).build()));
            // 取是否匹配和计数值
            Boolean isMatched = rulePair.getKey();
            VehicleCounterRuleDO counterRuleDO = rulePair.getValue();
            if (isMatched == null) {
                // 如果计算情况异常，不处理
                return;
            }
            String counterName = counterRuleDO.getName();
            // 取已存的值（可能没有）
            VehicleCounterInfoDO vehicleCounter = vehicleCounterInfo.get(counterName);
            // 结束、新建、或者更新
            vehicleCounter = updateCounterInfo(vehicleCounter, isMatched, counterRuleDO, currentTime);
            // 重新放入
            vehicleCounterInfo.put(counterName, vehicleCounter);
        });
    }

    /**
     * 判断计数器是否结束
     * 
     * @param counterName
     * @return
     */
    public boolean isCountFinished(String counterName) {
        return Optional.ofNullable(vehicleCounterInfo.get(counterName)).map(VehicleCounterInfoDO::isCountFinished)
                .orElse(true);
    }

    /**
     * 找寻计数器时间
     * spel使用
     * 
     * @param counterName
     * @return
     */
    public Integer findCounterDuration(String counterName) {
        return Optional.ofNullable(vehicleCounterInfo.get(counterName)).map(vehicleCounterInfoDO -> {
            if (vehicleCounterInfoDO.isCountFinished()) {
                return null;
            }
            return vehicleCounterInfoDO.getDuration();
        }).orElse(-1);
    }

    /**
     * 找寻计数器时间
     *
     * @param counterName
     * @return
     */
    public boolean upToCounter(String counterName, Integer duration) {
        return duration != null && findCounterDuration(counterName) >= duration;
    }

    private boolean isValidField(Field field) {
        return field != null && VehicleCounterInfoDO.class.isAssignableFrom(field.getType());
    }

    private VehicleCounterInfoDO getCounterInfo(Field field) {
        return (VehicleCounterInfoDO) ReflectUtils.getValueByField(field, this);
    }

    private VehicleCounterInfoDO updateCounterInfo(VehicleCounterInfoDO counterInfoDO, boolean isMatched,
            VehicleCounterRuleDO rule, Date currentTime) {
        if (isMatched) {
            //如果满足计数规则
            if (counterInfoDO == null || counterInfoDO.isCountFinished()) {
                counterInfoDO = VehicleCounterInfoDO.builder()
                        .rule(rule.getRule())
                        .startTime(currentTime)
                        .build();
            }
            //更新时间
            counterInfoDO.increaseDuration(currentTime);
            return counterInfoDO;
        }
        if (counterInfoDO != null && !counterInfoDO.isCountFinished()) {
            //如果不满足计数规则，且计数对象存在，而且非完结
            //完结计数过程
            counterInfoDO.finishCount(currentTime);
        }
        return counterInfoDO;
    }

    /**
     * 是否满足停滞时长
     *
     * @return
     */
    public Boolean upToStagnationCount(Integer duration) {
        if (Objects.isNull(stagnationCounter)) {
            return false;
        }
        // 在停滞且持续时间大于
        return !stagnationCounter.isCountFinished() && stagnationCounter.getDuration() >= duration;
    }

    private void setCounterInfo(Field field, VehicleCounterInfoDO counterInfo) {
        ReflectUtils.setFieldValue(field, this, counterInfo);
    }

    /**
     * 更新monitor metrics信息
     *
     * @param controlArbitrationInfoMessage
     * @param message
     */
    public void updateMonitorMetricsInfo(ControlArbitrationInfoDTO controlArbitrationInfoMessage, String message) {
        try {
            MonitorMetrics monitorMetrics = controlArbitrationInfoMessage.getMonitorMetrics();
            if (monitorMetrics == null) {
                return;
            }

            SceneInfoDTO sceneInfoDTO = monitorMetrics.getSceneInfo();
            MonitorMetricsDO monitorMetricsInfo = MonitorMetricsDO.builder()
                    .batterySwitching(monitorMetrics.isBatterySwitching())
                    .isInHomeArea(monitorMetrics.isInHomeArea())
                    .isQueuing(monitorMetrics.isQueuing())
                    .distanceToJunction(monitorMetrics.getDistanceToJunction())
                    .scene(Optional.ofNullable(sceneInfoDTO).map(SceneInfoDTO::getScene).orElse(StringUtils.EMPTY))
                    .junctionId(monitorMetrics.getCurrentJunctionId())
                    .build();
            this.setMonitorMetricsInfo(monitorMetricsInfo);
            this.distanceToJunction = transDistanceToJunction(monitorMetrics.getDistanceToJunction());
        } catch (Exception e) {
            log.error("updateMonitorMetricsInfo error", e);
        }
    }

    /**
     * 转换自车到路口距离
     *
     * @param distanceToJunction
     * @return
     */
    private double transDistanceToJunction(String distanceToJunction) {
        // FIXME 临时处理h24车无monitorMetrics内数据字段问题
        if (distanceToJunction == null || "Infinity".equals(distanceToJunction)) {
            return -1;
        }

        try {
            return new BigDecimal(distanceToJunction).setScale(1, RoundingMode.HALF_UP).doubleValue();
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * 从高负向里更新数据 : distanceToNextJunction drivingOnTrafficLineType oppositeWithRoad 暂未更新使用:
     * isPathOverlapWithConstructionZone、distanceToFrontConstructionZone;
     *
     * @param vehicleHighNegativeMessageDTO
     */
    public void updateFromHighNegative(VehicleHighNegativeMessageDTO vehicleHighNegativeMessageDTO) {
        if (vehicleHighNegativeMessageDTO == null || vehicleHighNegativeMessageDTO.getHighNegativeEventMeta() == null
                || !BooleanUtils.toBoolean(
                vehicleHighNegativeMessageDTO.getHighNegativeEventMeta().getIsValid())) {
            //如果消息不可用，则不处理，使用侧依靠过期时间设置
            return;
        }
        Integer distanceToNextJunction = Optional.ofNullable(vehicleHighNegativeMessageDTO.getHighNegativeEventMeta())
                .map(HighNegativeEventMeta::getCommonMeta)
                .map(CommonMeta::getDistanceToNextJunction)
                .map(OptionalDouble::getValue)
                .map(Double::intValue)
                .orElse(-1);
        //压线类型
        String lineType = Optional.ofNullable(vehicleHighNegativeMessageDTO.getHighNegativeEventMeta())
                .map(HighNegativeEventMeta::getTouchLineMeta)
                .map(TouchLineMeta::getLineType)
                .map(LineType::name)
                .orElse(LineType.NONE.name());
        //是否压线
        Boolean touchedLined = Optional.ofNullable(vehicleHighNegativeMessageDTO.getHighNegativeEventMeta())
                .map(HighNegativeEventMeta::getTouchLineMeta)
                .map(TouchLineMeta::getIsTouchLine)
                .orElse(false);
        //占用逆向车道
        Boolean oppositeWithRoad = Optional.ofNullable(vehicleHighNegativeMessageDTO.getHighNegativeEventMeta())
                .map(HighNegativeEventMeta::getCommonMeta)
                .map(CommonMeta::getIsEgoOnReverseLane)
                .orElse(false);
        //道施工区域的距离
        Integer distanceToFrontConstructionZone = Optional.ofNullable(
                        vehicleHighNegativeMessageDTO.getHighNegativeEventMeta())
                .map(HighNegativeEventMeta::getConstructionZoneMeta)
                .map(ConstructionZoneMeta::getDistanceToFrontConstructionZone)
                .map(Double::intValue)
                .orElse(-1);
        //道施工区域的距离
        Boolean pathOverlapWithConstructionZone = Optional.ofNullable(
                        vehicleHighNegativeMessageDTO.getHighNegativeEventMeta())
                .map(HighNegativeEventMeta::getConstructionZoneMeta)
                .map(ConstructionZoneMeta::getIsPathOverlapWithConstructionZone)
                .orElse(false);
        VehicleFenceContextDO fenceContextDO = Optional
                .ofNullable(vehicleHighNegativeMessageDTO.getHighNegativeEventMeta())
                .map(HighNegativeEventMeta::getClosestFenceAndFieldMetas)
                .filter(CollectionUtils::isNotEmpty).map(closestFenceAndFieldMetas -> VehicleFenceContextDO.builder()
                        .fenceAndFieldMetas(closestFenceAndFieldMetas).build())
                .orElse(null);
        //设置
        this.setDistanceToNextJunction(distanceToNextJunction);
        this.setDrivingOnTrafficLineType(BooleanUtils.toString(touchedLined, lineType, LineType.NONE.name()));
        this.setOppositeWithRoad(oppositeWithRoad);
        this.setPathOverlapWithConstructionZone(pathOverlapWithConstructionZone);
        this.setDistanceToFrontConstructionZone(distanceToFrontConstructionZone);
        this.setFenceContext(fenceContextDO);

    }


    /**
     * 从感知和决策更新红绿灯信息
     *
     * @param trafficData
     */
    public void updateFromTraffic(TrafficLightContextDO trafficData, TrafficLightConfigDTO trafficLightConfigDTO) {
        TrafficLightDO trafficLightDO = Optional.ofNullable(trafficData)
                .map(x -> x.getNowTrafficLight(trafficLightConfigDTO))
                .orElse(null);
        TrafficLightTypeEnum typeEnum = Optional.ofNullable(trafficLightDO).map(TrafficLightDO::getColor).orElse(null);
        if (Objects.nonNull(typeEnum) && CollectionUtils.isNotEmpty(
                trafficLightConfigDTO.getIgnoreTrafficLightTypeList())
                && trafficLightConfigDTO.getIgnoreTrafficLightTypeList().contains(typeEnum.name())) {
            return;
        }
        // 保存红绿灯信息
        this.trafficLightType = typeEnum;
        this.curTrafficLight = trafficLightDO;
    }

    public void updateObstacleAbstract(List<ObstacleAbstract> obstacleAbstracts) {
        this.obstacleAbstracts = obstacleAbstracts;
    }


    /**
     * 从状态监控更新速度、经纬度、驾驶模式
     *
     * @param vehicleMonitorInfoDO
     */
    public void updateFromMonitor(VehicleMonitorInfoDO vehicleMonitorInfoDO) {
        if (vehicleMonitorInfoDO == null) {
            return;
        }
        this.setSpeed(vehicleMonitorInfoDO.getSpeed());
        // 过滤异常0值
        if (vehicleMonitorInfoDO.isPositionValid()) {
            this.setLng(String.format("%.6f", vehicleMonitorInfoDO.getLongitude()));
            this.setLat(String.format("%.6f", vehicleMonitorInfoDO.getLatitude()));
        }
        this.setDriveMode(DriverModeEnum.fromCode(vehicleMonitorInfoDO.getDriveMode()));


    }

    public PositionDO getLocation() {
        if (StringUtils.isBlank(this.getLng()) || StringUtils.isBlank(this.getLat())) {
            return null;
        }
        return PositionDO.builder()
                .coordinateSystem(CoordinateSystemEnum.WGS84)
                .longitude(Double.valueOf(this.getLng()))
                .latitude(Double.valueOf(this.getLat()))
                .build();
    }

    /**
     * 获取GCJ02坐标系下的位置
     *
     * @return
     */
    public PositionDO getGCJ02Location() {
        PositionDO location = this.getLocation();
        if (Objects.isNull(location)) {
            return null;
        }
        return GeoToolsUtil.transferCoordinateSystemEnum(location,
                CoordinateSystemEnum.GCJ02);
    }

    public Boolean hasInJunction() {
        return this.getDistanceToJunction() != null && this.getDistanceToJunction() >= 0
                && this.getDistanceToJunction() <= CommonConstant.EPSILON;
    }

    /**
     * 更新障碍物信息到缓存
     *
     * @param obstacleDTO
     */
    public void updateObstacle(PerceptionObstacleDTO obstacleDTO, Double distance) {
        // 对id进行去重
        Map<String, PerceptionObstacle> perceptionObstacles = Optional.ofNullable(obstacleDTO)
                .map(PerceptionObstacleDTO::getPerceptionObstacle).orElse(new ArrayList<>()).stream()
                .filter(perceptionObstacle -> {
                    Position obstaclePosition = perceptionObstacle.getPosition();
                    if (obstaclePosition == null || obstaclePosition.getX() == null
                            || obstaclePosition.getY() == null) {
                        return false;
                    }
                    PositionDO obstaclePositionDO = GeoToolsUtil.utmToWgs84(obstaclePosition.getX(),
                            obstaclePosition.getY());
                    Double calcDistance = GeoToolsUtil.distance(obstaclePositionDO, this.getLocation());
                    if (calcDistance == null) {
                        return false;
                    }
                    // 只保留满足距离的
                    return calcDistance <= distance;
                }).collect(Collectors.toMap(PerceptionObstacle::getId, Function.identity(), (o1, o2) -> o1));
        // 更新障碍物信息
        this.obstacleContext = VehicleObstacleContextDO.builder()
                .perceptionObstacle(new ArrayList<>(perceptionObstacles.values())).build();
    }

    /**
     * 更新车辆的业务状态【NO_BIZ_TRIP: 暂无配送任务，BIZ_TRIP_ONWAY: 配送在途，BIZ_TRIP_WAITING：等待放/取单】
     *
     * @param bizStatus
     */
    public void updateBizStatus(String bizStatus) {
        if (StringUtils.isBlank(bizStatus)) {
            return;
        }
        this.bizStatus = bizStatus;
    }

    public boolean hasNonTrafficLight() {
        TrafficLightTypeEnum trafficLightType = this.findTrafficLightType();
        return trafficLightType == null || trafficLightType.equals(TrafficLightTypeEnum.NONE);
    }

    /**
     * 用停止墙补充红绿灯
     * 
     * @return
     */
    public TrafficLightTypeEnum findTrafficLightType() {
        if (this.trafficLightType != null && !this.trafficLightType.equals(TrafficLightTypeEnum.NONE)) {
            return this.trafficLightType;
        }
        boolean isWaitTraffic = Optional.ofNullable(fenceContext).map(VehicleFenceContextDO::getFenceAndFieldMetas)
                .orElse(new ArrayList<>()).stream()
                .anyMatch(fenceAndFieldMetaDTO -> ConstraintSourceTypeEnum.TRAFFIC_LIGHT
                        .equals(fenceAndFieldMetaDTO.getConstraintSourceType()));
        if (isWaitTraffic) {
            // 如果是停止墙也在等灯,则使用红灯，否则还是使用原来的
            return TrafficLightTypeEnum.RED;
        }
        isWaitTraffic = Optional.ofNullable(monitorMetricsInfo).map(MonitorMetricsDO::getScene)
                .orElse(CharConstant.CHAR_EMPTY).equals(MonitorMetricsSceneConstant.WAIT_TRAFFIC_LIGHT);
        if (isWaitTraffic) {
            // 如果是应急也在等灯,则使用红灯，否则还是使用原来的
            return TrafficLightTypeEnum.RED;
        }
        return this.trafficLightType;

    }
    /**
     * 远离路口Xm
     * 
     * @return
     */
    public boolean awayFromJunction(Double minAwayFromJunction) {
        if (this.getDistanceToJunction() == null || minAwayFromJunction == null || this.getDistanceToJunction() < 0) {
            // 不存在路口时，默认满足
            return true;
        }
        // 路口距离大
        return this.getDistanceToJunction() >= minAwayFromJunction;
    }


    public void updateLaneSelectionType(HdMapElementGeoDO vehicleCurLane, List<HdMapElementGeoDO> nearbyLaneList) {
        if (Objects.isNull(vehicleCurLane)) {
            return;
        }
        Map<String, HdMapElementGeoDO> laneMap = nearbyLaneList.stream()
                .collect(Collectors.toMap(HdMapElementGeoDO::getId, lane -> lane, (v1, v2) -> v1));
        if (StringUtils.isBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT))
                && StringUtils.isBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT))) {
            this.vehicleLaneSectionType = "middle";
            // 单车道
            this.laneCount = RoadTypeEnum.SINGLE_LANE.getCode();
            this.setSingleLane(true);
        }
        boolean hasLeft = StringUtils.isNotBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT));
        boolean hasRight = StringUtils.isNotBlank(vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT));
        // 如果有左、右，是中间
        // 如果有左没右，是最右
        // 如果有右没左，是最左
        if (hasLeft && hasRight) {
            this.vehicleLaneSectionType = "middle";
            // 多车道,大于两条车道
            this.laneCount = RoadTypeEnum.MULTI_LANE.getCode();
        } else if (hasLeft && !hasRight) {
            this.vehicleLaneSectionType = "right";
            // todo 这里不一定是两车道，需要检查其左侧车道是否存在更左侧车道
            String leftLaneId = vehicleCurLane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT);
            this.laneCount = determineRoadType(laneMap, leftLaneId);
        } else if (!hasLeft && hasRight) {
            this.vehicleLaneSectionType = "left";
            //  两车道
            this.laneCount = RoadTypeEnum.DOUBLE_LANE.getCode();
        } else {
            log.error("updateLaneSelectionType error, vehicleCurLane:{}", vehicleCurLane);
        }
    }

    /**
     * 计算车道类型
     *
     * @param laneMap    车道map
     * @param leftLaneId 左侧车道id
     * @return
     */
    private Integer determineRoadType(Map<String, HdMapElementGeoDO> laneMap, String leftLaneId) {
        // 参数校验
        if (Objects.isNull(laneMap) || StringUtils.isBlank(leftLaneId)) {
            return RoadTypeEnum.DOUBLE_LANE.getCode();
        }
        HdMapElementGeoDO lane = laneMap.get(leftLaneId);
        if (Objects.isNull(lane)) {
            return RoadTypeEnum.DOUBLE_LANE.getCode();
        }
        // 如果左侧车道匹配到更左的车道，则说明这是一个多车道
        if (StringUtils.isNotBlank(lane.getPropertyByKey(GeoElementTypeKeyConstant.LEFT))) {
            return RoadTypeEnum.MULTI_LANE.getCode();
        }
        return RoadTypeEnum.DOUBLE_LANE.getCode();
    }


    /**
     * 更新自车周围的LANE
     *
     * @param lane2HdMapElementGeoDOMap
     */
    public void updateAroundLane(Map<String, List<HdMapElementGeoDO>> lane2HdMapElementGeoDOMap) {
        if (MapUtils.isEmpty(lane2HdMapElementGeoDOMap)) {
            return;
        }
        lane2HdMapElementGeoDOMap.forEach((aroundType, hdMapElementGeoDOList) -> {
            String idListStr = hdMapElementGeoDOList.stream().map(HdMapElementGeoDO::getId)
                    .collect(Collectors.joining(CharConstant.CHAR_COMMA));
            if (StringUtils.isBlank(idListStr)) {
                return;
            }
            vehicleAroundLaneId.put(aroundType, idListStr);
        });

    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class MonitorMetricsDO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 场景信息
         */
        private String scene;

        /**
         * 是否切电中
         */
        private boolean batterySwitching;

        /**
         * 是否在 Home 区域
         */
        private boolean isInHomeArea;

        /**
         * 是否在排队
         */
        private boolean isQueuing;

        /**
         * 到路口的距离
         */
        @Deprecated
        private String distanceToJunction;

        /**
         * 所在路口
         */
        private String junctionId;

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VehicleObstacleContextDO implements Serializable {

        private static final long serialVersionUID = 1L;
        // 距离车附近最近的障碍物
        private List<PerceptionObstacle> perceptionObstacle;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VehicleFenceContextDO implements Serializable {

        private static final long serialVersionUID = 1L;
        // 车辆停止
        private List<FenceAndFieldMetaDTO> fenceAndFieldMetas;
    }


    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ObstacleAbstract implements Serializable {

        private static final long serialVersionUID = 1L;

        private String obstacleId;

        private String posture;

        private Double angle;

        private Double distance;

        private String position;


        private Velocity velocity;

        /**
         * 速度
         */
        private Double speed;

        private String fineType;

        /**
         * 矢量加速度
         */
        private Acceleration acceleration;
    }


    /**
     * 车辆排队中的位置和车辆的距离
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class PrePositionDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 车辆位置
         */
        private PositionDO position;
        /**
         * 时间戳
         */
        private String time;
        /**
         * 距离
         */
        private Double distance;
    }


    /**
     * 障碍物
     */
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class ObstacleInfoDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 障碍物id
         */
        private String obstacleId;

        /**
         * 障碍物位置
         */
        private PositionDO position;

        /**
         * 障碍物类型
         */
        private String obstacleType;

        /**
         * 障碍物细分类型
         */
        private String fineType;

        /**
         * 类型
         */
        private String type;

        /**
         * 到车辆的距离
         */
        private Double distance;

        /**
         * 和车辆行径方向的夹角
         */
        private Double angle;

        /**
         * 所在的车道(当前车道或者后继车道)
         */
        private String laneId;

        /**
         * 和车辆所在车道的中心线的夹角
         */
        private Double middleAngle;

        /**
         * 障碍物宽度
         */
        private Double width;

        /**
         * 距离最近车道边界的距离
         */
        private Double distanceToNearCurb;

    }
}