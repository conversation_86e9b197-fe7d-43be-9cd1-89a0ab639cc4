package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.PublicEventDetailDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.PublicDetailDOQueryParamDTO;
import java.util.List;

public interface PublicDetailRepository {

    /**
     * @param queryParamDTO
     * @return
     */
    List<PublicEventDetailDO> queryByParam(PublicDetailDOQueryParamDTO queryParamDTO);

    PublicEventDetailDO getByEventId(Long eventId);

    void save(PublicEventDetailDO publicEventDetailDO);

    void batchSave(List<PublicEventDetailDO> publicEventDetailDO);

    /**
     * 根据参数查询风险事件 (分页)
     *
     * @param paramDTO
     * @return
     */
    Paging<PublicEventDetailDO> queryByParamByPage(PublicDetailDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize);


}
