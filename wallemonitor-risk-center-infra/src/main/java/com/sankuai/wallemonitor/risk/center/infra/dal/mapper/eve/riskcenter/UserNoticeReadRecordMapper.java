package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.UserNoticeReadRecord;

/**
 * <p>
 * 用户阅读记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-07-02
 */
public interface UserNoticeReadRecordMapper extends CommonMapper<UserNoticeReadRecord> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<UserNoticeReadRecord> getPOClass() {
        return UserNoticeReadRecord.class;
    }
}
