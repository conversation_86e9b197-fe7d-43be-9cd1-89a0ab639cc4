package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GeoRangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GeoQueryDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseLocationRelationDO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface CaseLocationRelationRepository {

    List<CaseLocationRelationDO> queryByParam(CaseLocationRelationDOQueryParamDTO paramDTO);

    Paging<CaseLocationRelationDO> queryByParamByPage(CaseLocationRelationDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize);

    CaseLocationRelationDO getByVin(String vin);

    void save(CaseLocationRelationDO CaseLocationRelationDO);

    void batchSave(List<CaseLocationRelationDO> CaseLocationRelationDOList);

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class CaseLocationRelationDOQueryParamDTO {

        @InQuery(field = "caseId")
        private List<String> caseIdList;

        @GeoRangeQuery(field = "location")
        private GeoQueryDO locationQuery;

        /**
         * 创建时间范围
         */
        @RangeQuery(field = "createTime")
        private TimePeriod createTimeRange;

    }
}