package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseReleaseMrmMessageDTO {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 消息key（唯一标识）
     */
    private String key;

    /**
     * 结束时间
     */
    @JsonProperty("end_timestamp")
    private Long endTimestamp;

    /**
     * 上报时间
     */
    @JsonProperty("alarm_timestamp")
    private Long alarmTimestamp;

    /**
     * 问题code
     */
    @JsonProperty("issue_code")
    private Integer issueCode;
}
