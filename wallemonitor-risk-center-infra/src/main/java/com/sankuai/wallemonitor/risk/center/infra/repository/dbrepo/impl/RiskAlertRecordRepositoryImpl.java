package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskAlertRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskAlertRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskAlertRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.UpgradeStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskAlertRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskAlertRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskAlertRecordDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * 风险告警记录Repository实现类
 */
@Repository
@Slf4j
public class RiskAlertRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskAlertRecordMapper, RiskAlertRecordConvert, RiskAlertRecord, RiskAlertRecordDO>
        implements RiskAlertRecordRepository {

    @Resource
    private RiskAlertRecordMapper riskAlertRecordMapper;

    @Resource
    private RiskAlertRecordConvert riskAlertRecordConvert;

    @ConfigValue(key = LionKeyConstant.LION_KEY_ALERT_UPGRADE_CARNE_QUERY_TIME_THRESHOLD)
    private Integer timeThreshold;

    private static final String UK_ID = "id";

    /**
     * 根据参数查询风险告警记录
     *
     * @param paramDTO 查询参数
     * @return 告警记录列表
     */
    @Override
    @RepositoryQuery
    public List<RiskAlertRecordDO> queryByParam(RiskAlertRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数分页查询风险告警记录
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    @Override
    @RepositoryQuery
    public Paging<RiskAlertRecordDO> queryByParamByPage(RiskAlertRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    @Override
    public RiskAlertRecordDO getLatestRecordByBizId(String bizId) {
        if (StringUtils.isBlank(bizId)) {
            return null;
        }
        List<RiskAlertRecordDO> recordDOList = queryByParam(RiskAlertRecordDOQueryParamDTO.builder()
                .bizIdList(Collections.singletonList(bizId))
                .orderByCreateTime(OrderEnum.DESC)
                .build());

        return recordDOList.stream().findFirst().orElse(null);
    }

    @Override
    public RiskAlertRecordDO getLatestRecordByMessageId(String messageId) {
        if (StringUtils.isBlank(messageId)) {
            return null;
        }
        List<RiskAlertRecordDO> recordDOList = queryByParam(RiskAlertRecordDOQueryParamDTO.builder()
                .messageIdList(Collections.singletonList(messageId))
                .orderByCreateTime(OrderEnum.DESC)
                .build());

        return recordDOList.stream().findFirst().orElse(null);
    }

    /**
     * 根据ID查询风险告警记录
     *
     * @param id 记录ID
     * @return 告警记录
     */
    @Override
    @RepositoryQuery
    public RiskAlertRecordDO getById(Long id) {
        if (id == null) {
            return null;
        }
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_ID)
                .value(id.toString())
                .build()));
    }

    /**
     * 保存风险告警记录
     *
     * @param recordDO 告警记录DO
     */
    @Override
    @RepositoryExecute
    public void save(RiskAlertRecordDO recordDO) {
        super.save(recordDO);
    }

    /**
     * 批量保存风险告警记录
     *
     * @param recordDOList 告警记录DO列表
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskAlertRecordDO> recordDOList) {
        super.batchSave(recordDOList);
    }

    /**
     * 获取待升级的告警记录（按message_id分组，每组取最新记录）
     *
     * @return 待升级记录列表
     */
    @Override
    @RepositoryQuery
    public List<RiskAlertRecordDO> getPendingUpgradeRecords() {
        // 计算时间阈值：当前时间减去配置的升级阈值分钟数
        Date thresholdTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES, timeThreshold);
        return queryByParam(RiskAlertRecordDOQueryParamDTO.builder()
                .upgradeStatusList(Collections.singletonList(UpgradeStatusEnum.NEED_UPGRADE.getCode())) // 1-需要升级
                .createTimeRange(TimePeriod.builder()
                        .beginDate(thresholdTime) // 阈值时间
                        .endDate(new Date())  // 当前时间
                        .build())
                .orderByCreateTime(OrderEnum.ASC)
                .build());
    }

} 