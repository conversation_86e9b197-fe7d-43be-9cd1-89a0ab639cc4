package com.sankuai.wallemonitor.risk.center.infra.model.common;


import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HdMapLaneDO {

    private String laneId;

    private String laneType;
    /**
     * 左邻车道
     */
    private String left;
    /**
     * 右邻车道
     */
    private String right;
    /**
     * 前序车道
     */
    private List<String> predecessor;
    /**
     * 后继车道
     */
    private List<String> successor;

    /**
     * 多边形的点
     */
    private PolygonDO polygonDO;

    /**
     * 车道所属道路id
     */
    private String roadId;

    /**
     * 车道所属section
     */
    private String section;

    /**
     * 车道中心线点
     */
    private List<PositionDO> centerLinePoints;


    /**
     * 取turnType
     */
    private String turnType;

    /**
     * 是否还有右侧车道
     *
     * @return
     */
    public boolean hasRightLane() {
        return StringUtils.isNotEmpty(right);
    }

}
