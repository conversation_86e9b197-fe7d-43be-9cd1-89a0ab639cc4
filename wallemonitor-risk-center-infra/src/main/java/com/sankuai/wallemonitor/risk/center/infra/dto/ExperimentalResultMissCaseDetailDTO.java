package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.ead.citadel.document.node.concept.Block;
import com.sankuai.ead.citadel.document.node.concept.Inline;
import com.sankuai.ead.citadel.document.node.impl.node.BulletList;
import com.sankuai.ead.citadel.document.node.impl.node.ListItem;
import com.sankuai.ead.citadel.document.node.impl.node.Paragraph;
import com.sankuai.ead.citadel.document.node.impl.node.TableCell;
import com.sankuai.ead.citadel.document.util.Builder;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 漏召事件列表详情
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
@Data
@lombok.Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExperimentalResultMissCaseDetailDTO {

    // 事件ID
    private String caseId;

    // 真值
    private ISCheckCategoryEnum trueCategory;

    // 漏召次数
    private Integer missCount;

    // 识别过程信息
    private List<IdentifyProcessInfo> identifyProcessInfo;

    @Data
    @lombok.Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IdentifyProcessInfo {

        // 漏召类型
        private ISCheckCategoryEnum category;

        // 漏召次数
        private Integer count;

        // 过程详情
        private Map<String, IdentifyProcessDetail> detail;
    }

    @Data
    @lombok.Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IdentifyProcessDetail {

        // 位置信息
        String info;
        // 图片地址
        String picUrl;
    }

    private static final String CASE_LINK_TEMPLATE = "https://eve.meituan.com/fe-panel-risk/index.html#/risk/caseList?openCaseId=%s";

    /**
     * 转换为表格单元格
     *
     * @return TableCell数组
     */
    public TableCell[] toTableCell() {
        return new TableCell[]{
                Builder.tableCell(getCaseLinkParagraph()),
                Builder.tableCell(getTrueCategoryParagraph()),
                Builder.tableCell(getMissCountParagraph()),
                Builder.tableCell(getIdentifyProcessBulletList())
        };
    }

    private Paragraph getCaseLinkParagraph() {
        if (StringUtils.isBlank(caseId)) {
            return Builder.paragraph(" ");
        }
        String link = String.format(CASE_LINK_TEMPLATE, caseId);
        return Builder.paragraph(Builder.link(link, caseId, false, Builder.text(caseId)));
    }

    private Paragraph getTrueCategoryParagraph() {
        return Optional.ofNullable(trueCategory)
                .map(category -> Builder.paragraph(category.getName()))
                .orElse(Builder.paragraph(" "));
    }

    private Paragraph getMissCountParagraph() {
        return Optional.ofNullable(missCount)
                .map(count -> Builder.paragraph(String.valueOf(count)))
                .orElse(Builder.paragraph(" "));
    }

    private Block getIdentifyProcessBulletList() {
        if (CollectionUtils.isEmpty(identifyProcessInfo)) {
            return Builder.paragraph(" ");
        }

        ListItem[] listItems = identifyProcessInfo.stream()
                .map(this::createIdentifyProcessListItem)
                .toArray(ListItem[]::new);

        return Builder.bulletList(listItems);
    }

    /**
     * 创建识别过程列表项，格式：WAITING_FRONT_PASSAGER（X次）：{"前视":"机动车道#停车线_图片"}
     *
     * @param info
     * @return
     */
    private ListItem createIdentifyProcessListItem(IdentifyProcessInfo info) {
        ListItem listItem = new ListItem();
        List<Inline> inlines = new ArrayList<>();
        inlines.add(Builder.text(info.getCategory().getName() + "(" + info.getCount() + "次)"));
        if (MapUtils.isNotEmpty(info.getDetail())) {
            inlines.add(Builder.text(": {"));
            boolean isFirst = true;
            for (Map.Entry<String, IdentifyProcessDetail> entry : info.getDetail().entrySet()) {
                if (!isFirst) {
                    inlines.add(Builder.text(", "));
                }
                isFirst = false;
                inlines.add(Builder.text("\"" + entry.getKey() + "\":\"" + entry.getValue().getInfo()));
                // picUrl不为空时，才添加图片链接
                if (StringUtils.isNotBlank(entry.getValue().getPicUrl())) {
                    inlines.add(Builder.text("_"));
                    inlines.add(Builder.link(entry.getValue().getPicUrl(), "图片", false, Builder.text("图片")));
                }
                inlines.add(Builder.text("\""));
            }
            inlines.add(Builder.text("}"));
        }
        listItem.setContents(Collections.singletonList(Builder.paragraph(inlines.toArray(new Inline[0]))));
        return listItem;
    }
}


