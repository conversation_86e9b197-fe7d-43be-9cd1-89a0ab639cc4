package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskErrorBypassRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskErrorBypassRecordDOQueryParamDTO;
import java.util.List;

/**
 * 错误绕行检测记录仓储接口
 */
public interface RiskErrorBypassRecordRepository {

    /**
     * 根据参数查询错误绕行检测记录
     *
     * @param paramDTO 查询参数
     * @return 错误绕行检测记录DO列表
     */
    List<RiskErrorBypassRecordDO> queryByParam(RiskErrorBypassRecordDOQueryParamDTO paramDTO);

    /**
     * 根据参数分页查询错误绕行检测记录
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 分页对象
     */
    Paging<RiskErrorBypassRecordDO> queryByParamByPage(RiskErrorBypassRecordDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize);

    /**
     * 根据临时事件ID查询错误绕行检测记录
     *
     * @param tmpCaseId 临时事件ID
     * @return 错误绕行检测记录DO
     */
    RiskErrorBypassRecordDO getByTmpCaseId(String tmpCaseId);

    /**
     * 保存错误绕行检测记录
     *
     * @param riskErrorBypassRecordDO 错误绕行检测记录DO
     */
    void save(RiskErrorBypassRecordDO riskErrorBypassRecordDO);

    /**
     * 批量保存错误绕行检测记录
     *
     * @param riskErrorBypassRecordDOList 错误绕行检测记录DO列表
     */
    void batchSave(List<RiskErrorBypassRecordDO> riskErrorBypassRecordDOList);
} 