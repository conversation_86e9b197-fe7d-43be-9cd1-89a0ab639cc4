package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEventRelated;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventRelatedDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", imports = {EnumsConvertMapper.class}, uses = {EnumsConvertMapper.class})
public interface NegativePublicEventRelatedConvert extends
        SingleConvert<NegativePublicEventRelated, NegativePublicEventRelatedDO> {

    @Override
    NegativePublicEventRelatedDO toDO(NegativePublicEventRelated negativePublicEventRelated);

    @Override
    NegativePublicEventRelated toPO(NegativePublicEventRelatedDO negativePublicEventRelatedDO);

}
