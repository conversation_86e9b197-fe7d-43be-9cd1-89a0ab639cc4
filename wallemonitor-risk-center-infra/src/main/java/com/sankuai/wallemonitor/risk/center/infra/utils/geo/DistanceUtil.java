package com.sankuai.wallemonitor.risk.center.infra.utils.geo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/29
 */
@Slf4j
public class DistanceUtil {

    /**
     * pai
     */
    private static final double PI = 3.1415926535897932384626;

    /**
     * 地球半径(km)
     */
    private static final double EARTH_RADIUS = 6378.137;

    /**
     * 计算坐标点在地球表面的投影（弧度）
     *
     * @param d 坐标
     * @return
     */
    public static double rad(double d) {
        return d * PI / 180.0;
    }

    /**
     * 计算地球表面距离，单位：千米
     *
     * @param longitude1 位置 a 的经度
     * @param latitude1  位置 a 的纬度
     * @param longitude2 位置 b 的经度
     * @param latitude2  位置 b 的纬度
     * @return 单位：千米
     */
    public static double earthSurfaceDistance(double longitude1, double latitude1,
            double longitude2, double latitude2) {
        double a = rad(latitude1) - rad(latitude2);
        double b = rad(longitude1) - rad(longitude2);
        return 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                Math.cos(rad(latitude1)) * Math.cos(rad(latitude2)) * Math.pow(Math.sin(b / 2), 2))) * EARTH_RADIUS;
    }
}