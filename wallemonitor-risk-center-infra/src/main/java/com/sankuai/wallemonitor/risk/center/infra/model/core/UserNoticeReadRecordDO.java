package com.sankuai.wallemonitor.risk.center.infra.model.core;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class UserNoticeReadRecordDO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 确认阅读字段[0-未阅读|1-已阅读]
     */
    private Boolean confirm;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除[0-未删除|1-已删除]
     */
    private Boolean isDelete;
}
