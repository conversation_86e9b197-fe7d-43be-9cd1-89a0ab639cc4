package com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class ControlArbitrationInfoDTO {

    private MonitorMetrics monitorMetrics;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MonitorMetrics {

        private SceneInfoDTO sceneInfo;
        private boolean batterySwitching;
        private boolean isInHomeArea;
        private boolean isQueuing;
        private String distanceToJunction;
        private String currentJunctionId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SceneInfoDTO {

        /**
         * 场景类型
         */
        private String scene;
    }
}
