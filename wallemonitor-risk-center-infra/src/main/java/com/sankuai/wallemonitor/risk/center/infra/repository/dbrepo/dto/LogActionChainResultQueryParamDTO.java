package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;


import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LogActionChainResultQueryParamDTO {

    /**
     * 查询指定的同名字段
     */
    private String caseId;

    /**
     * 根据caseId查询
     */
    @InQuery(field = "caseId")
    private List<String> caseIdList;

    /**
     * 查询指定执行场景
     */
    private String scene;

    /**
     *  查询指定轮次
     */
    private Integer round;

    @OrderBy(field = "createTime")
    private OrderEnum orderByCreateTime;

    private String actionName;

    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    private String vin;

    /**
     * 标注版本
     */
    @Builder.Default
    private String markVersion = CharConstant.CHAR_EMPTY;
}
