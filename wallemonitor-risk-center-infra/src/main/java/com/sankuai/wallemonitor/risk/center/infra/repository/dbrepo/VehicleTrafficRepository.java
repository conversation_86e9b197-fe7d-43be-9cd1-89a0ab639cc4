package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightContextDO;
import java.util.List;
import java.util.Map;

/**
 * 车辆上下文仓储层
 */
public interface VehicleTrafficRepository {


    /**
     * 保存车辆红绿灯上下文
     *
     * @param trafficLightContextDO
     */
    void saveVehicleTrafficContext(String vin, TrafficLightContextDO trafficLightContextDO);


    /**
     * 获取车辆红绿灯上下文
     *
     * @param vin
     */
    TrafficLightContextDO getVehicleTrafficContext(String vin);

    /**
     * 批量获取车辆红绿灯上下文
     *
     * @param vinList 车辆vin列表
     */
    Map<String, TrafficLightContextDO> batchGetVehicleTrafficContext(List<String> vinList);

}
