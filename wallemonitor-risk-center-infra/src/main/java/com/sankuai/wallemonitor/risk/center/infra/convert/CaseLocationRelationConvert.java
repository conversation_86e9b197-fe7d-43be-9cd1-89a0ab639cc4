package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseLocationRelation;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseLocationRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.stereotype.Component;

/**
 * CaseLocationRelation 转换器
 */
@Component
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface CaseLocationRelationConvert extends SingleConvert<CaseLocationRelation, CaseLocationRelationDO> {

    /**
     * DO 转 PO
     */
    @Mapping(target = "location", source = "location", qualifiedByName = "toPointFromLocation")
    CaseLocationRelation toPO(CaseLocationRelationDO caseLocationRelationDO);

    /**
     * PO 转 DO
     */
    @Mapping(target = "location", source = "location", qualifiedByName = "toLocationFromPoint")
    CaseLocationRelationDO toDO(CaseLocationRelation caseLocationRelation);
}