package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskErrorBypassRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskErrorBypassRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 错误绕行检测记录转换器
 */
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskErrorBypassRecordConvert extends SingleConvert<RiskErrorBypassRecord, RiskErrorBypassRecordDO> {

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusEnum")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseTypeEnum")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "parseVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "parseVehicleRuntimeInfoContextDO")
    RiskErrorBypassRecordDO toDO(RiskErrorBypassRecord riskErrorBypassRecord);

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusInteger")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "serializeVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "serializeVehicleRuntimeInfoContextDO")
    RiskErrorBypassRecord toPO(RiskErrorBypassRecordDO riskErrorBypassRecordDO);
} 