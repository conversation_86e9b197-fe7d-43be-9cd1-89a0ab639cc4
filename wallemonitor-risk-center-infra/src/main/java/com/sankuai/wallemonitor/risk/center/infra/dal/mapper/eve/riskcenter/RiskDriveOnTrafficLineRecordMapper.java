package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskDriveOnTrafficLineRecord;

/**
 * <p>
 * 压线预检过程表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-10-14
 */
public interface RiskDriveOnTrafficLineRecordMapper extends CommonMapper<RiskDriveOnTrafficLineRecord> {


    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskDriveOnTrafficLineRecord> getPOClass() {
        return RiskDriveOnTrafficLineRecord.class;
    }
}
