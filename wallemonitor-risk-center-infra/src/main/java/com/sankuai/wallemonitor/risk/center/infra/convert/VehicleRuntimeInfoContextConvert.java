package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.VehicleRuntimeInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface VehicleRuntimeInfoContextConvert extends
        SingleConvert<VehicleRuntimeInfoContext, VehicleRuntimeInfoContextDO> {

    @Override
    @Mapping(source = "driveMode", target = "driveMode", qualifiedByName = "toDriveModeEnum")
    @Mapping(source = "positionType", target = "positionType", qualifiedByName = "toPositionTypeEnum")
    @Mapping(source = "trafficLightType", target = "trafficLightType", qualifiedByName = "toTrafficLightTypeEnum")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "parseVehicleRuntimeExtInfo")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "parseVehicleCounter")
    @Mapping(source = "redLightCounter", target = "redLightCounter", qualifiedByName = "parseVehicleCounter")
    @Mapping(source = "curTrafficLight", target = "curTrafficLight", qualifiedByName = "parseTrafficLight")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    VehicleRuntimeInfoContextDO toDO(VehicleRuntimeInfoContext vehicleRuntimeInfoContext);

    @Override
    @Mapping(source = "driveMode", target = "driveMode", qualifiedByName = "toDriveModeEnumInteger")
    @Mapping(source = "positionType", target = "positionType", qualifiedByName = "toPositionTypeEnumName")
    @Mapping(source = "trafficLightType", target = "trafficLightType", qualifiedByName = "toTrafficLightTypeString")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "serializeVehicleRuntimeExtInfo")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "serializeVehicleCounter")
    @Mapping(source = "redLightCounter", target = "redLightCounter", qualifiedByName = "serializeVehicleCounter")
    @Mapping(source = "curTrafficLight", target = "curTrafficLight", qualifiedByName = "serializeTrafficLight")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    VehicleRuntimeInfoContext toPO(VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO);
}
