package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆属性DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleAttributesDTO {
    /**
     * 车辆VIN码
     */
    private String vin;
    
    /**
     * VHR模式
     */
    private VHRModeEnum vhr;
    
    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 场地编码
     */
    private String placeCode;
    
    /**
     * 用途
     */
    private String purpose;
} 