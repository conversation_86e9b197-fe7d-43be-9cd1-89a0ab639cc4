package com.sankuai.wallemonitor.risk.center.infra.vto.param;

import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.FridayOpenAiAdapter.ReqMessage;
import com.sankuai.wallemonitor.risk.center.infra.utils.VelocityUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayPromptVTO {

    private List<String> systemPromptList;


    /**
     *
     */
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class FridayPromptPartDTO {

        /**
         * 描述
         */
        private String text = "交通标志，通常为红黄绿三色的交通指示灯，横向或竖向排列。位于路口，用于指示行人或机动车通行";
        /**
         * 名称
         */
        private String name = "红绿灯";

        /**
         * 类型，RULE、PLAINTEXT
         */
        private String type = "RULE";

        /**
         * 角色
         */
        private String role = "system";

        /**
         * 示例图片
         */
        private List<String> exampleImage = new ArrayList<>();

        /**
         * 规则template
         */
        private static final String RULE_TEMPLATE = "[%s]:%s\n";

        private static final String PLAINTEXT_TEMPLATE = "%s\n";


        /**
         * 转换为大模型的消息
         *
         * @return
         */
        public ReqMessage toReqMessage() {
            String template = "";
            String finalText = "";
            switch (type) {
                case "RULE":
                    template = RULE_TEMPLATE;
                    finalText = String.format(template, name, text);
                    break;
                case "PLAINTEXT":
                case "IMAGE":
                    //纯图片
                    template = PLAINTEXT_TEMPLATE;
                    finalText = String.format(template, text);
                    break;
            }
            return ReqMessage.getReqMessage(role,
                    StringUtils.isNotBlank(finalText) ? Collections.singletonList(finalText) : new ArrayList<>(),
                    exampleImage);
        }

        /**
         * 构建prompt 部分
         *
         * @return
         */
        public static FridayPromptPartDTO buildPart(String role, String text, String name, String type,
                List<String> exampleImage,
                Map<String, Object> context) {
            return FridayPromptPartDTO.builder()
                    .text(VelocityUtils.render(text, context))
                    .role(role)
                    .name(name)
                    .type(type)
                    .exampleImage(Optional.ofNullable(exampleImage).orElse(new ArrayList<>()).stream()
                            .map(img -> VelocityUtils.render(img, context))
                            .collect(Collectors.toList()))
                    .build();
        }
    }


}