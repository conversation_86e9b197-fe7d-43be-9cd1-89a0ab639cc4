package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.dianping.lion.Environment;
import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum SquirrelCategoryEnum {

    /**
     * 锁
     */
    RISK_CENTER_LOCK_CATEGORY("risk_center_lock", "redis-wproject-system", 3),

    /**
     * 操作轮训缓存
     */
    POLLING_CATEGORY("RISK_CENTER_OPERATE_CATEGORY", "redis-wproject-system", 300),

    /**
     * 领域事件记录缓存
     */
    DOMAIN_EVENT_PROCESS_RECORD_CATEGORY("RISK_CENTER_DOMAIN_EVENT", "redis-wproject-system",
            24 * 60 * 60),

    /**
     * 表字段更新时间
     */
    TABLE_FIELD_UPDATED_TIME_CATEGORY("RISK_CENTER_TABLE_FIELD_UPDATED", "redis-wproject-system",
            4 * 60 * 60),

    /**
     * 标记item缓存,最多1h
     */
    MARK_ITEM_CATEGORY("MARK_ITEM_CATEGORY", "redis-eve-system",
            60 * 60),

    /**
     * 通用缓存
     */
    COMMON_CATEGORY("RISK_CENTER_COMMON", "redis-eve-system", 24 * 60 * 60),

    /**
     * 车辆上下文缓存
     */
    VEHICLE_CONTEXT("RISK-VEHICLE_CONTEXT", "redis-eve-system", 24 * 60 * 60),

    VEHICLE_RUNTIME_CONTEXT("RISK-VEHICLE_RUNTIME_CONTEXT", "redis-eve-system", 24 * 60 * 60),

    ;

    /**
     * 环境后缀映射
     */
    private static final Map<String, String> envSuffixMap = new HashMap<>();

    static {
        envSuffixMap.put("test", "qa");
        envSuffixMap.put("prod", "product");
    }

    /**
     * 缓存category
     */
    private String category;
    /**
     * 所在集群
     */
    private String cluster;
    /**
     * 默认超时时间，单位秒
     */
    private Integer defaultTimeOut;

    /**
     * 获取环境名
     *
     * @return
     */
    public static String getEnvCluster(String cluster) {
        if (StringUtils.isBlank(cluster)) {
            return null;
        }
        return Joiner.on(CharConstant.CHAR_XH).join(cluster,
                envSuffixMap.getOrDefault(Environment.getEnvironment(), cluster));

    }


    /**
     * 根据category获取枚举
     *
     * @param category
     * @return
     */
    public static SquirrelCategoryEnum getByCategory(String category) {
        for (SquirrelCategoryEnum squirrelCategoryEnum : SquirrelCategoryEnum.values()) {
            if (squirrelCategoryEnum.getCategory().equals(category)) {
                return squirrelCategoryEnum;
            }
        }
        return null;
    }
}
