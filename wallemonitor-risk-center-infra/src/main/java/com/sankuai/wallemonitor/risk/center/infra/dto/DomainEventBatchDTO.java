package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DomainEventBatchDTO {


    /**
     * 入口
     */
    private DomainEventEntryDTO entry;

    /**
     * 由入口发起变更后，在这个入口一次性发送变更的模型
     */
    @Builder.Default
    private List<DomainEventDTO> domainEventList = new ArrayList<>();

}
