package com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils;

import java.util.function.Supplier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 显式事务调用
 */
@Component
public class TransactionUtils {


    /**
     * 注册事务后的回调
     *
     * @param runnable
     */
    public static void doAfterCommit(Runnable runnable) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {

            @Override
            public void afterCommit() {
                runnable.run();
            }
        });
    }

    /**
     * 事务调用
     *
     * @param supplier
     * @param <T>
     * @return
     */
    @Transactional(value = "transactionManager0", rollbackFor = Exception.class)
    public <T> T execute(Supplier<T> supplier) {
        return supplier.get();
    }

    /**
     * 事务调用
     *
     * @param runnable
     * @return
     */
    @Transactional(value = "transactionManager0", rollbackFor = Exception.class)
    public void execute(Runnable runnable) {
        runnable.run();
    }

}
