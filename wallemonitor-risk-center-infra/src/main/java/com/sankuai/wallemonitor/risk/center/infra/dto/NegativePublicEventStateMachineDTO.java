package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventHandleDegreeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventNatureEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.NegativePublicEventStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NegativePublicEventStateMachineDTO {

    /**
     * 状态
     */
    private NegativePublicEventStatusEnum status;

    /**
     * 性质
     */
    private NegativePublicEventNatureEnum nature;

    /**
     * reason
     */
    private Boolean reason;

    /**
     * 处置程度
     */
    private NegativePublicEventHandleDegreeEnum handleDegree;
}
