package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.VehicleHighNegativeMessageDTO.FenceAndFieldMetaDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class VehicleInfoDO {

    /**
     * 车辆信息
     */
    private String vin;


    /**
     * vehicleId，M1234
     */
    private String vehicleId;

    /**
     * vehicleName,S20-123
     */
    private String vehicleName;

    /**
     * 约车目的
     */
    private String purpose;


    /**
     * vhr类型
     */
    private VHRModeEnum vhr;

    /**
     * 【 车辆定位
     */
    private PositionDO position;

    /**
     * 场地
     */
    private String placeCode;

    /**
     * 自动车自动驾驶版本
     */
    private String autocarVersion;

    /**
     * 高精地图版本
     */
    private String hdMapVersion;

    /**
     * 驾驶模式
     */
    private Integer driveMode;

    /**
     * 救援中
     */
    private Boolean withRescueOrder;

    /**
     * 事故中
     */
    private Boolean withAccidentOrder;

    /**
     * 维修中
     */
    private Boolean withMaintenanceOrder;

    /**
     * Re工单
     * */
    private Boolean withReOrder;


    /**
     * 是否等红绿灯
     */
    private Boolean isWaitingRed;

    /**
     * 障碍物的信息
     */
    private List<PerceptionObstacle> obstacles;

    /**
     * 停止墙的信息
     */
    private List<FenceAndFieldMetaDTO> fences;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 云控安全员
     */
    private String telecontrol;

    /**
     * 近场安全员
     */
    private String substitute;

}
