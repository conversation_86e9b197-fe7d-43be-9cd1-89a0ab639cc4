package com.sankuai.wallemonitor.risk.center.infra.exception.check;

import static com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter.replaceMsg;

import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 上游入参校验，仅校验当前是否合法，是否满足入参条件 入口只针对跟前端交互的接口入口 这里不做状态等数据校验判断 抛出异常代表入参不合法：用户输入错误/上游代码数据错误
 *
 * <AUTHOR>
 */
@Slf4j
public class CheckUtil {

    private static final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 校验参数是否符合规则，搭配实体上的JSR-303注解使用，例如@NotBlank、@NotNull等 如果只需要校验所有参数都不为空，建议使用InputCheckUtil.isAllNotNull()方法
     *
     * @param obj 待校验实体
     * @param <T> 实体类型
     * @throws ParamInputErrorException 参数校验不通过时抛出异常
     */
    public static <T> void validate(T obj) {
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(obj);
        isEmpty(constraintViolations, constraintViolations.iterator().next().getMessage());
    }

    /**
     * 判断空
     *
     * @param obj
     * @param msg
     */
    public static void isNull(Object obj, String msg) {
        if (obj != null) {
            throwBizException(msg);
        }
    }

    public static void isNull(Object obj, String msg, String... replaces) {
        if (obj != null) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * 不为null，否则抛出异常
     *
     * @param obj
     * @param msg
     */
    public static void isNotNull(Object obj, String msg) {
        if (obj == null) {
            throwBizException(msg);
        }
    }

    public static void isNotNull(Object obj, String msg, String... replaces) {
        if (obj == null) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * 字符串不为空，否则抛出异常
     *
     * @param str
     * @param msg
     */
    public static void isNotBlank(String str, String msg) {
        if (StringUtils.isBlank(str)) {
            throwBizException(msg);
        }
    }

    public static void isNotBlank(String str, String msg, String... replaces) {
        if (StringUtils.isBlank(str)) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * 字符串为空，否则抛出异常
     *
     * @param str
     * @param msg
     */
    public static void isBlank(String str, String msg) {
        if (StringUtils.isNotBlank(str)) {
            throwBizException(msg);
        }
    }

    public static void isBlank(String str, String msg, String... replaces) {
        if (StringUtils.isNotBlank(str)) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * false，否则抛出异常
     *
     * @param flag
     * @param msg
     */
    public static void isFalse(boolean flag, String msg) {
        if (flag) {
            throwBizException(msg);
        }
    }

    public static void isFalse(boolean flag, String msg, String... replaces) {
        if (flag) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * true，否则抛出异常
     *
     * @param flag
     * @param msg
     */
    public static void isTrue(boolean flag, String msg) {
        if (!flag) {
            throwBizException(msg);
        }
    }

    public static void isTrue(boolean flag, String msg, String... replaces) {
        if (!flag) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }

    public static void isNotTrue(boolean flag, String msg) {
        if (flag) {
            throwBizException(msg);
        }
    }

    public static void isNotTrue(boolean flag, String msg, String... replaces) {
        if (flag) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }

    /**
     * 集合为空，否则抛出异常
     *
     * @param coll
     * @param msg
     */
    public static void isEmpty(Collection coll, String msg) {
        if (CollectionUtils.isNotEmpty(coll)) {
            throwBizException(msg);
        }
    }

    /**
     * 集合不为空，否则抛出异常
     *
     * @param coll
     * @param msg
     */
    public static void isNotEmpty(Collection coll, String msg) {
        if (CollectionUtils.isEmpty(coll)) {
            throwBizException(msg);
        }
    }

    public static void isNotEmpty(Collection coll, String msg, String... replaces) {
        if (CollectionUtils.isEmpty(coll)) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }


    /**
     * map不为空，否则抛出异常
     *
     * @param map
     * @param msg
     */
    public static void isEmpty(Map map, String msg) {
        if (MapUtils.isNotEmpty(map)) {
            throwBizException(msg);
        }
    }

    public static void isEmpty(Map map, String msg, String... replaces) {
        if (MapUtils.isNotEmpty(map)) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }

    /**
     * map不为空，否则抛出异常
     *
     * @param map
     * @param msg
     */
    public static void isNotEmpty(Map map, String msg) {
        if (MapUtils.isEmpty(map)) {
            throwBizException(msg);
        }
    }

    public static void isNotEmpty(Map map, String msg, String... replaces) {
        if (MapUtils.isEmpty(map)) {
            throwBizException(replaceMsg(msg, replaces));
        }
    }

    public static void isNotEquals(String arg1, String arg2, String msg) {
        if (StringUtils.equals(arg1, arg2)) {
            throwBizException(msg);
        }
    }

    public static void isEquals(Integer arg1, Integer arg2, String msg) {
        if (!Objects.equals(arg1, arg2)) {
            throwBizException(msg);
        }
    }

    public static void isEquals(String arg1, String arg2, String msg) {
        if (!StringUtils.equals(arg1, arg2)) {
            throwBizException(msg);
        }
    }


    public static void isGreatThan(Integer arg1, Integer arg2, String msg) {
        if (arg1 <= arg2) {
            throwBizException(msg);
        }
    }

    /**
     * 检查对象是否所有字段都不为空，有一个字段为空就会抛异常（不检查父类字段）
     *
     * @param obj 检查对象
     * @param msg 检查失败时提示文案
     * @throws ParamInputErrorException 存在字段为空
     */
    @SneakyThrows
    public static void isAllNotEmpty(Object obj, String msg) {
        if (obj == null) {
            throwBizException(msg);
        }
        if (ReflectUtils.isAnyFieldEmpty(obj)) {
            throwBizException(msg);
        }
    }

    /**
     * 所有字段都为空，抛异常（不检查父类字段）
     *
     * @param obj 检查对象
     * @param msg 检查失败时提示文案
     * @throws ParamInputErrorException 所有字段都为空
     */
    @SneakyThrows
    public static void isNotAllEmpty(Object obj, String msg) {
        if (obj == null) {
            throwBizException(msg);
        }
        if (ReflectUtils.isAllFieldEmpty(obj)) {
            throwBizException(msg);
        }
    }


    private static void throwBizException(String msg) throws ErrorCodeException {
        throw new ParamInputErrorException(msg);
    }


}
