package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 负外部性事件处置状态枚举
 */
@AllArgsConstructor
@Getter
public enum NegativePublicEventStatusEnum {
    DEFAULT(0, "默认"),
    CREATED(1, "已创建"),
    LOCATED(2, "已定位"),
    CAUSE_IDENTIFIED(3, "已定因"),
    DISPOSED(4, "已处置");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static NegativePublicEventStatusEnum fromCode(Integer code) {
        for (NegativePublicEventStatusEnum status : NegativePublicEventStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

}
