package com.sankuai.wallemonitor.risk.center.infra.vto.param;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大象推送消息入参
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DxNoticeParamVTO {

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 群idList
     */
    private List<Long> groupIdList;

    /**
     * 消息参数
     */
    private Map<String, String> params;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 外部唯一ID,可填充风险事件ID
     */
    private String outBizId;

    /**
     * 消息版本
     */
    private Long version;


}
