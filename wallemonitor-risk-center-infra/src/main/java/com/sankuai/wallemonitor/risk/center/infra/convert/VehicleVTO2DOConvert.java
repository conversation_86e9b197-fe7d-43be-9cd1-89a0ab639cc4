package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 同字段转换
 */
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface VehicleVTO2DOConvert extends SingleConvert<VehicleEveInfoVTO, VehicleInfoDO> {

    @Mapping(target = "withReOrder", source = "withReOrder")
    List<VehicleInfoDO> toDOList(List<VehicleEveInfoVTO> vtoList);

}
