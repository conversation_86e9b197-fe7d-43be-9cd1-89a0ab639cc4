package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用事件DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonEventDTO {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 事件唯一ID
     */
    private String eventId;

    /**
     * 事件类型:0-默认/未知异常，1-停滞不当，2-车辆并排，3-车辆扎堆，4-扫码挪车，5-用户反馈，6-特殊区域停滞，7-逆行，8-非法压线，9-停滞事件，10-禁停区停滞，11-起步超时，12-安全区域停放不当，13-错误绕行，14-错误排队，15-错误让行，16-异常绕圈路由，17-泊车失败
     */
    private Integer caseType;

    /**
     * 事件发生时间
     */
    private Long occurTime;

    /**
     * 事件来源:1-保障系统，2-车辆PNC，3-状态监控系统，4-云分诊，5-风控服务，6-扫码挪车微信小程序，7-FC
     */
    private Integer source;

    /**
     * 位置信息，GCJ02
     */
    private LocationInfo location;

    /**
     * 扩展字段
     */
    private Map<String, Object> extInfo;

    /**
     * 事件描述
     */
    private String description;
} 