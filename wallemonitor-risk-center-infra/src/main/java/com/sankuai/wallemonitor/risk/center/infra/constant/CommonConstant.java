package com.sankuai.wallemonitor.risk.center.infra.constant;

import com.google.common.collect.Lists;
import java.util.List;

public class CommonConstant {

    public static final String RISK_CHECKING_SOURCE_MENDER = "MENDER";

    public static final String RISK_CHECKING_SOURCE_AI_FRIDAY = "AI_FRIDAY";

    public static final String MARK_OPERATOR_UNKNOWN = "unknown";

    public static final String MARK_VEHICLE_ID_UNKNOWN = "UNKNOWN";

    public static final String MARK_OPERATOR_FRIDAY = "friday";

    public static final String LOG_SIGNATURE = "LOG_SIGNATURE";
    public static final String FULL_LOG_SIGNATURE = "FULL_LOG_SIGNATURE";
    public static final String NONE_ACTION = "byNoneAction";
    public static final String DEFAULT_ACTION = "ISDefaultAction";

    public static final String DEFAULT = "default";
    public static final double EPSILON = 1e-10;;

    public static String APP_NAME_KEY = "app.name";

    public static String LION_USERNAME = "lion.username";

    public static String LION_PWD = "lion.pwd";

    public static String ALL = "ALL";

    public static Integer SECONDS_PER_MINUTE = 60;

    public static Integer SECONDS_PER_HOUR = 3600;

    public static Integer SECONDS_PER_DAY = 86400;

    public static Integer SECONDS_PER_HALF_DAY = 43200;

    public static Integer BATCH_OPERATION_LIMIT = 100;

    public static String HTTP_METHOD_GET = "GET";

    public static String HTTP_METHOD_POST = "POST";

    public static Integer HTTP_SUCCESS_CODE = 200;

    public static Integer VEHICLE_STATUS_QUERY_SUCCESS_CODE = 0;

    /**
     * 扫码挪车事件码
     */
    public static final int PUBLIC_REMOVAL_EVENT_CODE = 32;

    /**
     * 接管中心
     */
    public static final String DATA_SOURCE = "intervention";

    /**
     * 烽火台
     */
    public static final String BEACON_TOWER = "beacon_tower";

    /**
     * 烽火台上报工作台case类型 - 停滞不当
     */
    public static final String CASE_TYPE = "improper_stranding";

    /**
     * 类构造包前缀
     */
    public static final List<String> DTO_BUILD_REPO_LIST = Lists.newArrayList(
            AppKeyConstant.RISK_CENTER_APP_KEY + CharConstant.CHAR_DH + "infra", CharConstant.CHAR_DH + "model",
            AppKeyConstant.RISK_CENTER_APP_KEY + CharConstant.CHAR_DH + "infra",
            CharConstant.CHAR_DH + "repository.dbrepo.dto",
            AppKeyConstant.RISK_CENTER_APP_KEY + CharConstant.CHAR_DH + "infra", CharConstant.CHAR_DH + "dto"

    );

    /**
     * 未知
     */
    public static final String UNKNOWN = "unknown";

    /**
     * 空
     */
    public static final String EMPTY = "空";

    /**
     * 烽火台调用保障系统呼叫云控source
     */
    public static final Integer BEACON_TOWER_CALL_CLOUD_CONTROL_SOURCE = 11;


}
