package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.google.common.collect.Sets;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ThemeEnum;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.SpringUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.expression.EvaluationException;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParseException;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;


@Slf4j
public class SpELUtil {


    private static final ExpressionParser parser = new SpelExpressionParser();
    // 使用有界缓存存储解析后的表达式
    private static final ConcurrentHashMap<String, Expression> expressionCache = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 500; // 设置缓存大小

    /**
     * 通用的类
     */
    private static final Set<Class<?>> commonUtils = Sets.newHashSet(
            // 字符串处理类
            StringUtils.class, NumberUtils.class, DatetimeUtil.class, RiskCaseTypeEnum.class, RiskCaseSourceEnum.class,
            RiskCaseStatusEnum.class, Date.class, ThemeEnum.class, GeoToolsUtil.class, CollectionUtils.class,
            HttpUtils.class);

    public static Boolean evaluateBoolean(String expressionString, Map<String, Object> variables) {
        if (log.isDebugEnabled()) {
            log.debug("SpELUtil evaluateWithVariablesToBoolean expressionString:{}, variables:{}", expressionString,
                    variables);
        }
        Boolean value = evaluateWithVariables(expressionString, variables, Boolean.class);
        log.debug("expressionString: {} value: {}", expressionString, value);
        return BooleanUtils.toBoolean(value);
    }

    public static <T> T evaluateWithVariables(String expressionString, Map<String, Object> variables, Class<T> tClass) {
        if (StringUtils.isBlank(expressionString) || MapUtils.isEmpty(variables)) {
            return null;
        }
        StandardEvaluationContext context = new StandardEvaluationContext();
        try {
            // 设置通用类
            buildCommonUtils(context);
            // 设置变量
            buildVariables(variables, context);
            // 设置bean解析器
            buildBeanResolver(context);
            // 注册方法
            buildMethodResolver(context);
            Expression expression = expressionCache.computeIfAbsent(expressionString, key -> {
                if (expressionCache.size() > MAX_CACHE_SIZE) {
                    expressionCache.clear(); // 简单的缓存清理策略，实际应用中可能需要更复杂的策略
                }
                return parser.parseExpression(key);
            });
            return expression.getValue(context, tClass);
        } catch (ParseException e) {
            log.error("表达式解析错误: {}", expressionString, e);
        } catch (EvaluationException e) {
            log.error("表达式执行错误: " + expressionString, e);
        } catch (Exception e) {
            log.error("未知错误: " + expressionString, e);
        }
        return null;
    }

    /**
     * 一些通用的方法
     *
     * @param context
     */
    private static void buildMethodResolver(StandardEvaluationContext context) {
        try {
            context.registerFunction("new",
                    SpELUtil.class.getDeclaredMethod("getBuildParamMethod", String.class, Object[].class));
        } catch (Exception e) {
            log.error("SpELUtil buildMethodResolver error", e);
        }
    }

    public static Object getBuildParamMethod(String className, Object... params) {
        if (StringUtils.isBlank(className)) {
            return null;
        }
        // 从这些方法里面找
        Class<?> clazz = ReflectUtils.findClassBySimpleName(CommonConstant.DTO_BUILD_REPO_LIST, className);
        // params 做处理，如果是Collection的，用List替换
        return ReflectUtils.createInstance(clazz, params);
    }

    private static void buildCommonUtils(StandardEvaluationContext context) {
        for (Class<?> clazz : commonUtils) {
            context.setVariable(clazz.getSimpleName(), clazz);
        }
    }

    private static void buildVariables(Map<String, Object> variables, StandardEvaluationContext context) {
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }
    }

    private static void buildBeanResolver(StandardEvaluationContext context) {
        ApplicationContext applicationContext = SpringUtils.getApplicationContext();
        if (applicationContext == null) {
            // 如果 存在，则支持bean处理
            log.warn("当前无applicationContext,无法加载bean");
            return;
        }
        context.setBeanResolver(new BeanFactoryResolver(applicationContext));
    }

}