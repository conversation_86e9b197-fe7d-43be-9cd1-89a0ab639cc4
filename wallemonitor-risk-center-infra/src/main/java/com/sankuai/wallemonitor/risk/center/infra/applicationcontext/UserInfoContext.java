package com.sankuai.wallemonitor.risk.center.infra.applicationcontext;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.sankuai.wallemonitor.risk.center.infra.model.common.SSOLogInfoDO;
import java.util.Optional;

/**
 * 用户信息
 */
public class UserInfoContext {

    private static final TransmittableThreadLocal<SSOLogInfoDO> threadLocal = new TransmittableThreadLocal<>();

    public static SSOLogInfoDO getUserInfo() {
        return threadLocal.get();
    }

    public static String getUserMis() {
        return Optional.ofNullable(threadLocal.get()).map(SSOLogInfoDO::getLogin).orElse(null);
    }

    public static Integer getEmpId() {
        return Optional.ofNullable(threadLocal.get()).map(SSOLogInfoDO::getId).orElse(null);
    }


    /**
     * 获取线程变量
     *
     * @return
     */
    public static void bind(SSOLogInfoDO userInfoVO) {
        threadLocal.set(userInfoVO);
    }

    /**
     * 清除线程变量
     */
    public static void clear() {
        threadLocal.remove();
    }
}
