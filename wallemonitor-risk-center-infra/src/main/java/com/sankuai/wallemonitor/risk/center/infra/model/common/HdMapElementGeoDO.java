package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.GeoTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapElementTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapLaneAreaTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.locationtech.jts.geom.Coordinate;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HdMapElementGeoDO {

    /**
     * 道路 或者 object 或者 信号，通用的，不同地图类型取值不同
     * 
     * @see HdMapElementTypeEnum
     *
     */
    private String elementType;

    /**
     * elementType
     *
     * @see HdMapLaneAreaTypeEnum
     *
     */
    private String areaType;

    /**
     * 地图类型
     */
    private String mapType;

    /**
     * 地理形状类型
     */
    private GeoTypeEnum geoType;

    /**
     * id
     */
    private String id;

    /**
     * 属性
     */
    private Map<String, Object> properties;

    /**
     * 多边形的点
     */
    private PolygonDO polygonDO;

    /**
     * 中间线点
     */
    private List<PositionDO> middleLinePoints;


    /**
     * 判断是否在多边形内
     *
     * @param positionDO
     * @return
     */
    public Boolean isInPolygon(PositionDO positionDO) {
        CheckUtil.isNotNull(positionDO, "定位不可以为空");
        CheckUtil.isNotNull(positionDO.getCoordinateSystem(), "定位坐标系不可以为空");
        if (polygonDO == null) {
            return false;
        }
        switch (geoType) {
            case POLYGON:
                // 目前只有多边形可以做这个判断
                return polygonDO.isInPolygon(positionDO);
            default:
                return false;
        }
    }

    /**
     * 获取坐标点集合（Coordinate类型）
     *
     * @return List<Coordinate> 坐标点集合
     */
    public List<Coordinate> getCoordinates() {
        if (polygonDO == null) {
            return new ArrayList<>();
        }
        return polygonDO.getClosedCoordinates();
    }

    /**
     * 获取坐标点集合（Coordinate类型）
     *
     * @return List<Coordinate> 坐标点集合
     */
    public List<List<Double>> getCoordinatesList() {
        List<Coordinate> coordinates = getCoordinates();
        if (CollectionUtils.isEmpty(coordinates)) {
            return new ArrayList<>();
        }
        List<List<Double>> result = new ArrayList<>();
        for (Coordinate coordinate : coordinates) {
            result.add(Arrays.asList(coordinate.x, coordinate.y));
        }
        return result;
    }

    /**
     * 获取坐标点集合（Coordinate类型）
     *
     * @return List<Coordinate> 坐标点集合
     */
    public List<List<Double>> getMiddleLinePointList() {
        List<PositionDO> positionDOS = getMiddleLinePoints();
        if (CollectionUtils.isEmpty(positionDOS)) {
            return new ArrayList<>();
        }
        List<List<Double>> result = new ArrayList<>();
        for (PositionDO coordinate : positionDOS) {
            result.add(coordinate.getPointList());
        }
        return result;
    }

    /**
     * 获取外接矩形
     * 
     * @return
     */
    public List<Coordinate> getBoundaryRectangle() {
        if (polygonDO == null) {
            return new ArrayList<>();
        }
        return polygonDO.getBoundaryRectangle();
    }

    /**
     * 获取属性
     * 
     * @param key
     * @param <T>
     */
    public <T> T getPropertyByKey(String key) {
        if (MapUtils.isEmpty(properties) || !properties.containsKey(key)) {
            return null;
        }
        return (T)properties.get(key);
    }

    /**
     * 判断是否是同一个方向的
     *
     * 
     * @return
     */
    public boolean isSameDirection(PositionDO before, PositionDO after, Double theta) {
        if (before == null || after == null) {
            return false;
        }
        // 取中心线最靠近车辆坐标的点
        Pair<PositionDO, PositionDO> nearMiddleLinePair = this.getMiddleLineTwoNearPoint(after);
        if (nearMiddleLinePair == null) {
            return false;
        }
        return GeoToolsUtil.isAngleLessThan(before, after, nearMiddleLinePair.getKey(), nearMiddleLinePair.getValue(),
                theta);
    }

    /**
     * 判断是否是同一个方向的，并输出角度信息
     *
     * @param before 车辆前一个点
     * @param after 车辆后一个点
     * @param theta 阈值角度
     * @return Pair<Boolean, Double> key: 是否同向，value: 实际夹角（度）
     */
    public Pair<Boolean, Double> isSameDirectionWithAngle(PositionDO before, PositionDO after, Double theta) {
        if (before == null || after == null) {
            return new Pair<>(false, null);
        }
        // 取中心线最靠近车辆坐标的点
        Pair<PositionDO, PositionDO> nearMiddleLinePair = this.getMiddleLineTwoNearPoint(after);
        if (nearMiddleLinePair == null) {
            return new Pair<>(false, null);
        }
        Double angle = GeoToolsUtil.angleCalc(before, after, nearMiddleLinePair.getKey(), nearMiddleLinePair.getValue());
        if (angle == null) {
            return new Pair<>(false, null);
        }
        boolean isSame = angle < theta;
        return new Pair<>(isSame, angle);
    }

    public void put(Map<String, Object> properties) {
        if (properties == null) {
            return;
        }
        if (this.properties == null) {
            this.properties = new HashMap<>();
        }
        this.properties.putAll(properties);
    }

    /**
     * 获取中间线两个最近点
     * 
     * @param curPosition
     * @return
     */
    public Pair<PositionDO, PositionDO> getMiddleLineTwoNearPoint(PositionDO curPosition) {
        // 获取position在
        List<PositionDO> positionDOS = getMiddleLinePoints();
        if (CollectionUtils.isEmpty(positionDOS) || curPosition == null || positionDOS.size() < 2) {
            // 中心线为空 或者 车辆当前坐标为空 或者 中心线小于2
            return null;
        }
        // 算最近的一个点
        Integer minDistanceIndex = IntStream.range(0, positionDOS.size())
                // 映射为距离对
                .mapToObj(i -> new Pair<>(i, GeoToolsUtil.distance(curPosition, positionDOS.get(i))))
                // 取最小的
                .min(Comparator.comparingDouble(Pair::getValue)).map(Pair::getKey).orElse(0);
        if (minDistanceIndex == 0) {
            // 如果匹配到了开头
            return new Pair<>(positionDOS.get(0), positionDOS.get(1));
        }
        if (minDistanceIndex == positionDOS.size() - 1) {
            // 如果匹配到了结尾
            return new Pair<>(positionDOS.get(minDistanceIndex - 1), positionDOS.get(minDistanceIndex));
        }
        // 如果是中心的点
        return new Pair<>(positionDOS.get(minDistanceIndex), positionDOS.get(minDistanceIndex + 1));

    }

    public HdMapLaneDO toLandDO() {
        if (this.elementType == null || !HdMapElementTypeEnum.getLaneType().contains(this.elementType)) {
            return null;
        }
        return HdMapLaneDO.builder().laneId(this.getId())
                // 道路类型
                .laneType(this.elementType).section(this.getPropertyByKey(GeoElementTypeKeyConstant.SECTION))
                .roadId(this.getPropertyByKey(GeoElementTypeKeyConstant.ROAD))
                .left(this.getPropertyByKey(GeoElementTypeKeyConstant.LEFT))
                .right(this.getPropertyByKey(GeoElementTypeKeyConstant.RIGHT))
                .successor(this.getPropertyByKey(GeoElementTypeKeyConstant.SUCCESSOR))
                .predecessor(this.getPropertyByKey(GeoElementTypeKeyConstant.PREDECESSOR))
                .centerLinePoints(this.getMiddleLinePoints()).polygonDO(this.getPolygonDO())
                .turnType(this.getPropertyByKey(GeoElementTypeKeyConstant.TURN_TYPE))
                .build();
    }
}