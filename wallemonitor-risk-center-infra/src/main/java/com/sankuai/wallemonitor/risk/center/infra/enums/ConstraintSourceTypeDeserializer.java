package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;

/**
 * ConstraintSourceTypeEnum 的自定义反序列化器 用于处理枚举值为数字的情况
 */
public class ConstraintSourceTypeDeserializer extends JsonDeserializer<ConstraintSourceTypeEnum> {

    @Override
    public ConstraintSourceTypeEnum deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        // 统一转为字符串处理
        String value = p.getValueAsString();
        for (ConstraintSourceTypeEnum type : ConstraintSourceTypeEnum.values()) {
            if (type.name().equals(value)) {
                return type;
            }
        }
        return ConstraintSourceTypeEnum.UNKNOWN;
    }


} 