package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 *
 * 识别有无风险结果
 * */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SFTFridayVerifyRiskResultVTO {

    @Builder.Default
    private List<String> imageUrls = new ArrayList<>();


    private ISCheckCategoryEnum checkCategory;

    private Date checkStartTime;

    private Date checkEndTime;

    private String originCategory; // 原始预测种类 如 ： 等红灯、静态车辆绕行

    private Boolean shouldBeTakeOver; // 原始预测 : 是否需要被接管

}
