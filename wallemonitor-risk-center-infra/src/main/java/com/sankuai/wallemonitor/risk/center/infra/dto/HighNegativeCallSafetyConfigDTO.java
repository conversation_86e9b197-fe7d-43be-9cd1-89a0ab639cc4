package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HighNegativeCallSafetyConfigDTO {


    /**
     * 准入规则
     */
    private List<String> grayRuleList;


    /**
     * 准入规则
     *
     * @return
     */
    public Boolean canCall(RiskCaseVehicleRelationDO caseVehicleRelationDO) {
        if (CollectionUtils.isEmpty(grayRuleList)) {
            return false;
        }
        Map<String, Object> context = new HashMap<>();
        context.put("relation", caseVehicleRelationDO);
        return grayRuleList.stream()
                .allMatch(rule -> SpELUtil.evaluateBoolean(rule, context));
    }


}
