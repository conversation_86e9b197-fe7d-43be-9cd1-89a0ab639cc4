package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.api.response.vo.AdminListPublicEventDetailVO;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.PublicEventDetail;
import com.sankuai.wallemonitor.risk.center.infra.model.core.PublicEventDetailDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 同字段转换
 */
@Mapper(componentModel = "spring", imports = {EnumsConvertMapper.class}, uses = {EnumsConvertMapper.class})
public interface PublicDetailConvert extends SingleConvert<PublicEventDetail, PublicEventDetailDO> {

    List<PublicEventDetailDO> toDOList(List<PublicEventDetail> poList);


    @Mapping(target = "vins", source = "vins", qualifiedByName = "splitVin")
    @Mapping(target = "vehicleIds", source = "vehicleIds", qualifiedByName = "splitVin")
    @Mapping(target = "driveMode", source = "driveMode", qualifiedByName = "toDriverModeEnum")
    @Mapping(target = "handleType", source = "handleType", qualifiedByName = "toHandleTypeEnum")
    PublicEventDetailDO toDO(PublicEventDetail po);

    @Mapping(target = "vins", source = "vins", qualifiedByName = "joinListString")
    @Mapping(target = "vehicleIds", source = "vehicleIds", qualifiedByName = "joinListString")
    @Mapping(target = "driveMode", source = "driveMode", qualifiedByName = "toDriverModeEnumInteger")
    @Mapping(target = "handleType", source = "handleType", qualifiedByName = "toHandleTypeEnumInteger")
    PublicEventDetail toPO(PublicEventDetailDO po);

    List<PublicEventDetail> toPOList(List<PublicEventDetailDO> doList);

    List<PublicEventDetail> toVOList(List<PublicEventDetailDO> vtoList);

    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "formatDate")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "formatDate")
    @Mapping(target = "occurTime", source = "occurTime", qualifiedByName = "formatDate")
    @Mapping(target = "requestHelpTime", source = "requestHelpTime", qualifiedByName = "formatDate")
    @Mapping(target = "startHandleTime", source = "startHandleTime", qualifiedByName = "formatDate")
    @Mapping(target = "finishTime", source = "finishTime", qualifiedByName = "formatDate")
    @Mapping(target = "vins", source = "vins", qualifiedByName = "joinListString")
    @Mapping(target = "vehicleIds", source = "vehicleIds", qualifiedByName = "joinListString")
    @Mapping(target = "vehicleList", source = "detailDO", qualifiedByName = "concatName")
    @Mapping(target = "handleType", source = "handleType", qualifiedByName = "toHandleTypeEnumInteger")
    @Mapping(target = "driveMode", source = "driveMode", qualifiedByName = "toDriverModeEnumInteger")
    AdminListPublicEventDetailVO toVO(PublicEventDetailDO detailDO);

}
