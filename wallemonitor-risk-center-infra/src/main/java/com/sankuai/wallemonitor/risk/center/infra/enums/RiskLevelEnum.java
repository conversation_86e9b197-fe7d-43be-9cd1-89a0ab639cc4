package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Getter
@AllArgsConstructor
public enum RiskLevelEnum {

    HIGH(1, "高风险"),
    MEDIUM(2, "中风险"),
    LOW(3, "低风险"),
    NONE(4, "无风险"),
    ;

    private int code;

    private String desc;


    /**
     * 根据value查询枚举
     *
     * @param code
     * @return
     */
    public static RiskLevelEnum findByValue(Integer code) {
        if (code == null) {
            return null;
        }
        for (RiskLevelEnum riskLevelEnum : RiskLevelEnum.values()) {
            if (riskLevelEnum.getCode() == code) {
                return riskLevelEnum;
            }
        }
        return null;
    }

}
