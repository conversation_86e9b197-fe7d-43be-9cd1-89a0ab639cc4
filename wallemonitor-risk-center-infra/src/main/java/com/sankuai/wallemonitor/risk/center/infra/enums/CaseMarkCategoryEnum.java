package com.sankuai.wallemonitor.risk.center.infra.enums;

import org.apache.commons.lang3.StringUtils;

public enum CaseMarkCategoryEnum {
    // 有风险
    IN_VALID_DRIVING_AREA("GOOD", "IN_VALID_DRIVING_AREA", "非行驶区域停滞", "有风险"),
    IN_MIDDLE_ROAD("GOOD", "IN_MIDDLE_ROAD", "路中间停滞", "有风险"),
    IN_JUNCTION("GOOD", "IN_JUNCTION", "路口绿灯停滞", "有风险"),
    OPPSITE_ROAD_DIRECTION("GOOD", "OPPSITE_ROAD_DIRECTION", "逆行停滞", "有风险"),
    CONFLICT_WITH_PASSAGER("GOOD", "CONFLICT_WITH_PASSAGER", "车辆顶牛", "有风险"),
    STOP_BY_OBSTACLE("GOOD", "STOP_BY_OBSTACLE", "障碍物堵路", "有风险"),
    STOP_ON_ROAD_SIDE("GOOD", "STOP_ON_ROAD_SIDE", "靠边停滞", "有风险"),
    AT_CONSTRUCTION_SITE("GOOD", "AT_CONSTRUCTION_SITE", "在施工现场", "有风险"),
    AT_ACCIDENT_SCENE("GOOD", "AT_ACCIDENT_SCENE", "在事故现场", "有风险"),
    GOOD_OTHER("GOOD", "GOOD_OTHER", "其他", "有风险"),

    // 无风险
    RED_LIGHT("BAD", "RED_LIGHT", "等待红绿灯", "无风险"),
    WAITING_FRONT_PASSAGER("BAD", "WAITING_FRONT_PASSAGER", "排队通行", "无风险"),
    IN_PARKING_AREA("BAD", "IN_PARKING_AREA", "停车场停放", "无风险"),
    IN_PARKING_QUEUE_AREA("BAD", "IN_PARKING_QUEUE_AREA", "缓冲区等待", "无风险"),
    WAITING_RIDER_TAKING_ORDER("BAD", "WAITING_RIDER_TAKING_ORDER", "在等待取餐", "无风险"),
    ON_TRAILER("BAD", "ON_TRAILER", "拖车中", "无风险"),
    IN_SHOW("BAD", "IN_SHOW", "展览中", "无风险"),
    BAD_OTHER("BAD", "BAD_OTHER", "其他", "无风险"),
    MENDER_IN_PARKING_AREA("BAD", "MENDER_IN_PARKING_AREA",
            "在区域适配的停滞区域内", "无风险"),

    // 无法评估
    NO_DETAIL("OTHER", "NO_DETAIL", "信息不足", "无法评估"),

    CANT_FOUND_ANY("", "", "不能分辨", "不能分辨");

    private final String category;
    private final String subcategory;
    private final String name;
    private final String desc;

    CaseMarkCategoryEnum(String category, String subcategory, String name, String desc) {
        this.category = category;
        this.subcategory = subcategory;
        this.name = name;
        this.desc = desc;
    }

    /**
     * 根据名称获取枚举值
     *
     * @param name
     * @return
     */
    public static CaseMarkCategoryEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (CaseMarkCategoryEnum value : CaseMarkCategoryEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;

    }

    /**
     * 根据名称获取枚举值
     *
     * @return
     */
    public static CaseMarkCategoryEnum getBySubcategory(String subcategory) {
        if (StringUtils.isBlank(subcategory)) {
            return null;
        }
        for (CaseMarkCategoryEnum value : CaseMarkCategoryEnum.values()) {
            if (value.getSubcategory().equals(subcategory)) {
                return value;
            }
        }
        return null;

    }

    public static boolean isGood(CaseMarkCategoryEnum checking) {
        if (checking == null) {
            return false;
        }
        return checking.getCategory().equals("GOOD");
    }

    public String getCategory() {
        return category;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }
}