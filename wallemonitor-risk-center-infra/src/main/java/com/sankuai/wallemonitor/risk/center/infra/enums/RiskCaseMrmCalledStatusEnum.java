package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.curator.shaded.com.google.common.collect.Sets;

@AllArgsConstructor
@Getter
public enum RiskCaseMrmCalledStatusEnum {
    NO_CALL(0, "未呼叫"),
    CALLING(1, "呼叫中"),

    CANCEL(99, "已取消"),
    ;

    private int code;

    private String desc;

    /**
     * 根据value查询枚举
     *
     * @param mrmCalled
     * @return
     */
    public static RiskCaseMrmCalledStatusEnum findByValue(Integer mrmCalled) {
        if (mrmCalled == null) {
            return null;
        }
        for (RiskCaseMrmCalledStatusEnum mrmCalledStatusEnum : RiskCaseMrmCalledStatusEnum.values()) {
            if (mrmCalledStatusEnum.getCode() == mrmCalled) {
                return mrmCalledStatusEnum;
            }
        }
        return null;
    }

    /**
     * 已呼叫枚举列表
     *
     * @return
     */
    public static List<Integer> getMrmCalledList() {
        return Arrays.asList(CALLING.getCode(), CANCEL.getCode());
    }

    /**
     * 未呼叫枚举列表
     *
     * @return
     */
    public static List<Integer> getNotMrmCalledList() {
        return Arrays.asList(NO_CALL.getCode());
    }

    /**
     * 是否已呼叫
     */
    public boolean isMrmCalled() {
        Set<RiskCaseMrmCalledStatusEnum> mrmCalledStatusEnums = Sets.newHashSet(RiskCaseMrmCalledStatusEnum.CALLING,
                RiskCaseMrmCalledStatusEnum.CANCEL);
        return mrmCalledStatusEnums.contains(this);
    }
}
