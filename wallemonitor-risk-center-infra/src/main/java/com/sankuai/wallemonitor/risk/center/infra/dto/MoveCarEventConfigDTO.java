package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MoveCarEventConfigDTO {

    /**
     * 最大上报次数
     */
    private Integer maxReportNum;

    /**
     * 上报时查询一定时间内是否有重复工单
     */
    private Integer eventReportValidDurationHour;

    /**
     * 定时任务查询一定时间内是否有未处理的工单
     */
    private Integer craneQueryValidDurationMin;

    /**
     * 定时取消超时未处置的挪车事件
     */
    private Integer cancelTimeoutUnDisposedDurationSec;

    /**
     * 过滤规则
     */
    private List<String> filters;

}
