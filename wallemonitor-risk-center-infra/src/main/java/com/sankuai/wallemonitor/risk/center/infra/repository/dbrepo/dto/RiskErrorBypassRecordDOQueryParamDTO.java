package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 错误绕行检测记录查询参数DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiskErrorBypassRecordDOQueryParamDTO {

    private String tmpCaseId;

    @InQuery(field = "tmpCaseId")
    private List<String> tmpCaseIdList;

    private String vin;

    @InQuery(field = "vin")
    private List<String> vinList;

    private Integer duration;

    private Integer status;

    @InQuery(field = "status")
    private List<Integer> statusList;

    @RangeQuery(field = "occurTime")
    private TimePeriod occurTimeRange;

    @RangeQuery(field = "recallTime")
    private TimePeriod recallTimeRange;

    @RangeQuery(field = "closeTime")
    private TimePeriod closeTimeRange;

    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    @RangeQuery(field = "updateTime")
    private TimePeriod updateTimeRange;

    @OrderBy(field = "createTime")
    private OrderEnum orderByCreateTime;

    private Boolean isCrossLine;

    private Boolean isInRouteLane;

    @Builder.Default
    private Boolean isDeleted = false;
} 