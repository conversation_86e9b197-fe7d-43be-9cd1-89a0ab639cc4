package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO.ObstacleAbstract;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskObstacleContextInfoDTO {

    private List<ObstacleAbstract> obstacleAbstractList;

}
