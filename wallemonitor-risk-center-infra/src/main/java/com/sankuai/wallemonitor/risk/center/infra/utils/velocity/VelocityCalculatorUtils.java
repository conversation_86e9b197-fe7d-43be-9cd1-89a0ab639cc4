package com.sankuai.wallemonitor.risk.center.infra.utils.velocity;

import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import lombok.extern.slf4j.Slf4j;

/**
 * 速度计算工具类
 */
@Slf4j
public class VelocityCalculatorUtils {



    /**
     * 车流的绝对速度大小
     * @param obstacle 障碍物信息
     * @return 速度大小（米/秒）
     */
    public static double calculateObstacleAbsoluteSpeed(VehicleRuntimeInfoContextDO.ObstacleAbstract obstacle) {
        if (obstacle == null || obstacle.getVelocity() == null) {
            log.warn("Obstacle or velocity is null, return 0.0");
            return 0.0;
        }

        PerceptionObstacleDTO.PerceptionObstacle.Velocity velocity = obstacle.getVelocity();
        double obstacleVx = velocity.getX();
        double obstacleVy = velocity.getY();
        
        return Math.sqrt(obstacleVx * obstacleVx + obstacleVy * obstacleVy);
    }
} 