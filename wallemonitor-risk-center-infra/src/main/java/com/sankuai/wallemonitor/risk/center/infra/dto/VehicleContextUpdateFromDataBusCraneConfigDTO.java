package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleContextUpdateFromDataBusCraneConfigDTO {

    /**
     * 并行批次大小
     */
    private Integer batchSize;
    /**
     * 业务类型列表
     */
    private List<String> bussinessTypeList;
}
