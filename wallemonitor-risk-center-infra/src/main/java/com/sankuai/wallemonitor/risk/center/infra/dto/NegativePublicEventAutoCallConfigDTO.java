package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NegativePublicEventAutoCallConfigDTO {

    /**
     * 自动外呼文本
     */
    private String autoCallMediaBody;

    /**
     * 自动外呼备用呼叫列表
     */
    private List<String> autoCallBackupCallerList;

    /**
     * 自动外呼RGID
     */
    private Long autoCallRgId;

    /**
     * 自动外呼值班时间
     */
    private List<String> autoCallOnCallTimeRange;
}
