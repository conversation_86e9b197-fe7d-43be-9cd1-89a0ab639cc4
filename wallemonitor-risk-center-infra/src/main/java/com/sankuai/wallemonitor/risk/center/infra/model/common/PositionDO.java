package com.sankuai.wallemonitor.risk.center.infra.model.common;


import static com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum.GCJ02;
import static com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum.WGS84;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Point;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PositionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 坐标系系统
     */
    private CoordinateSystemEnum coordinateSystem;

    public static PositionDO getPosition(Double longitude, Double latitude, CoordinateSystemEnum coordinateSystemEnum) {
        if (Objects.isNull(longitude) || Objects.isNull(latitude) || Objects.isNull(coordinateSystemEnum)) {
            return null;
        }
        return PositionDO.builder().longitude(longitude).latitude(latitude).coordinateSystem(coordinateSystemEnum)
                .build();

    }

    public static PositionDO getPosition(String position, CoordinateSystemEnum wgs84) {
        if (StringUtils.isBlank(position)) {
            return null;
        }
        String[] split = position.split(CharConstant.CHAR_DD);
        if (split.length != 2) {
            return null;
        }
        return PositionDO.getPosition(Double.parseDouble(split[0]), Double.parseDouble(split[1]), wgs84);

    }

    /**
     * 输出指定类型的坐标
     *
     * @return
     */
    public String getLocationStr(CoordinateSystemEnum coordinateSystemEnum) {
        SystemCheckUtil.isTrue(latitude != null && longitude != null, "位置信息为空");
        SystemCheckUtil.isNotNull(coordinateSystem, "位置信息为空");
        switch (coordinateSystemEnum) {
            case WGS84: {
                return WGS84.equals(this.getCoordinateSystem()) ? joinLocation()
                        : GeoToolsUtil.gcj02Wgs84(this.getLongitude(), this.getLatitude()).getLocationStr(WGS84);
            }
            case GCJ02: {
                return GCJ02.equals(this.getCoordinateSystem()) ? joinLocation()
                        : GeoToolsUtil.wgs84ToGcj02(this.getLongitude(), this.getLatitude()).getLocationStr(GCJ02);
            }
            default:
                return null;
        }
    }

    public boolean invalid() {

        return latitude == null || longitude == null || coordinateSystem == null
                || Math.abs(latitude - 0.0) < CommonConstant.EPSILON
                        && Math.abs(longitude - 0.0) < CommonConstant.EPSILON;
    }

    private String joinLocation() {
        return Joiner.on(CharConstant.CHAR_DD).join(longitude, latitude);
    }

    public List<Double> getPointList() {
        return Lists.newArrayList(longitude, latitude);
    }

    public Point toPoint() {
        return GeoToolsUtil.toPointFromLocation(this);
    }
}
