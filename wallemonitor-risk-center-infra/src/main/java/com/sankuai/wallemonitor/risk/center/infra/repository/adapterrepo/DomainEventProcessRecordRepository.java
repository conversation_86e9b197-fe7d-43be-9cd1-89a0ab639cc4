package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo;

import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventProcessResultDO;
import java.util.List;

public interface DomainEventProcessRecordRepository {

    /**
     * 获取某个入口和时间下的领域事件结果
     *
     * @return
     */
    List<DomainEventProcessResultDO> getProcessResult(List<String> processName, DomainEventEntryDTO entry,
            Long eventTime, String traceId);


    /**
     * 保存领域事件结果
     *
     * @param resultDO
     */
    void save(DomainEventProcessResultDO resultDO);

    /**
     * 保存领域事件结果
     *
     * @param resultDO
     */
    void batchSave(List<DomainEventProcessResultDO> resultDO);

}
