package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficStatusEnum;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TrafficLightDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前感知到的红绿灯
     */
    private TrafficLightTypeEnum color;

    /**
     * 状态
     */
    private TrafficStatusEnum status;

    /**
     * 风险等级
     */
    private String criticalLevel;

    /**
     * 倒计时
     */
    private Double countdown;


    /**
     * id
     */
    private String id;


    public boolean isRed() {
        return TrafficLightTypeEnum.RED.equals(color);
    }
}