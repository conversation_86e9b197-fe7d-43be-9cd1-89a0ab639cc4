package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteErrorException;
import com.sankuai.walleom.common.search.api.request.EagleCommonSearchRequest;
import com.sankuai.walleom.common.search.api.request.EagleCommonSearchRequest.BoolQuery;
import com.sankuai.walleom.common.search.api.request.EagleCommonSearchRequest.QueryCondition;
import com.sankuai.walleom.common.search.api.request.EagleCommonSearchRequest.Sort;
import com.sankuai.walleom.common.search.api.response.EagleCommonSearchResponse;
import com.sankuai.walleom.common.search.api.thrift.IThriftEagleCommnonSearchService;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 通用搜索服务
 */
@Component
@Slf4j
public class CommonSearchAdaptor {

    @ThriftClientProxy(remoteAppKey = AppKeyConstant.COMMON_SEARCH_APP_KEY, timeout = 5000)
    private IThriftEagleCommnonSearchService iThriftEagleCommnonSearchService;

    /**
     * 获取所有业务车的vin列表
     *
     * @return
     */
    public List<String> getBusinessCarVinList(List<String> businessTypeList) {
        try {
            EagleCommonSearchRequest eagleCommonSearchRequest = new EagleCommonSearchRequest();
            eagleCommonSearchRequest.setDataset("vehicle_real_time_info");
            // 构建查询参数
            QueryCondition businessTypeCondition1 = QueryCondition.builder()
                    .field("business_type")
                    .valueList(businessTypeList)
                    .queryType("terms")
                    .build();
            BoolQuery boolQuery = BoolQuery.builder()
                    .must(Arrays.asList(businessTypeCondition1))
                    .build();
            eagleCommonSearchRequest.setBoolQuery(boolQuery);
            EveThriftResponse<EagleCommonSearchResponse> response = iThriftEagleCommnonSearchService.commonSearch(
                    eagleCommonSearchRequest);
            // log.info("getBusinessCarVinList，commonSearch:{}", JacksonUtils.to(response));
            if (Objects.isNull(response) || response.getCode() != 0) {
                throw new RemoteErrorException("查询通用搜索服务失败");
            }
            EagleCommonSearchResponse data = response.getData();
            if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getDetailList())) {
                return Collections.emptyList();
            }
            return data.getDetailList().stream()
                    .map(detail -> detail.get("vin"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 查询车辆当前切电状态
     *
     * @param vin
     * @return
     */
    public Map<String, String> querySwitchPowerStatus(String vin, Date startTime) {
        try {
            EagleCommonSearchRequest eagleCommonSearchRequest = new EagleCommonSearchRequest();
            eagleCommonSearchRequest.setDataset("event");
            // 构建查询参数
            // 确定事件码
            QueryCondition eventCodeCondition = QueryCondition.builder()
                    .field("eventCode")
                    .value("1080001")
                    .queryType("term")
                    .build();
            // 确定查询对象
            QueryCondition vinCondition = QueryCondition.builder()
                    .field("vin")
                    .value(vin)
                    .queryType("term")
                    .build();
            // 确定查询范围
            QueryCondition updateTimeCondition = QueryCondition.builder()
                    .field("updateTime")
                    .gte(String.valueOf(startTime.getTime() / 1000))
                    .lte(String.valueOf(new Date().getTime() / 1000))
                    .queryType("range")
                    .build();
            BoolQuery boolQuery = BoolQuery.builder()
                    .must(Arrays.asList(eventCodeCondition, vinCondition, updateTimeCondition))
                    .build();
            eagleCommonSearchRequest.setBoolQuery(boolQuery);
            // 设置倒叙排序，即取当下最新的数据
            eagleCommonSearchRequest.setSort(
                    Arrays.asList(Sort.builder().field("updateTime").ascending(false).build()));
            // 只取最近的一个数据
            eagleCommonSearchRequest.setPageIndex(1);
            eagleCommonSearchRequest.setPageSize(1);
            EveThriftResponse<EagleCommonSearchResponse> response = iThriftEagleCommnonSearchService.commonSearch(
                    eagleCommonSearchRequest);
            // log.info("querySwitchPowerStatus，commonSearch:{}", JacksonUtils.to(response));
            if (Objects.isNull(response) || response.getCode() != 0) {
                throw new RemoteErrorException("查询通用搜索服务失败");
            }
            EagleCommonSearchResponse data = response.getData();
            if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getDetailList())) {
                return new HashMap<>();
            }
            return data.getDetailList().get(0);
        } catch (Exception e) {
            log.error("querySwitchPowerStatus error", e);
        }
        return new HashMap<>();
    }
}
