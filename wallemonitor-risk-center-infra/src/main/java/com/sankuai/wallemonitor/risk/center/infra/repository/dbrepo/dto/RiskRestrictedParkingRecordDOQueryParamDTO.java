package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.BelowTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskRestrictedParkingRecordDOQueryParamDTO {

    /**
     * 临时事件ID
     */
    private String tmpCaseId;

    /**
     * 临时事件ID列表
     */
    @InQuery(field = "tmpCaseId")
    private List<String> tmpCaseIdList;

    /**
     * 风险事件类型
     */
    private Integer type;

    /**
     * 风险事件类型列表
     */
    @InQuery(field = "type")
    private List<Integer> typeList;

    /**
     * 车辆VIN码
     */
    private String vin;

    /**
     * 车辆VIN码列表
     */
    @InQuery(field = "vin")
    private List<String> vinList;

    /**
     * 持续时长范围
     */
    @RangeQuery(field = "duration")
    private TimePeriod durationRange;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态列表
     */
    @InQuery(field = "status")
    private List<Integer> statusList;

    /**
     * 禁停区域
     */
    private String restrictedAreaType;

    /**
     * 禁停区域列表
     */
    @InQuery(field = "restrictedAreaType")
    private List<String> restrictedAreaTypeList;

    /**
     * 开始时间范围
     */
    @RangeQuery(field = "occurTime")
    private TimePeriod occurTimeRange;

    /**
     * 召回时间范围
     */
    @RangeQuery(field = "recallTime")
    private TimePeriod recallTimeRange;

    /**
     * 解除时间范围
     */
    @RangeQuery(field = "closeTime")
    private TimePeriod closeTimeRange;

    /**
     * 创建时间范围
     */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    /**
     * 更新时间范围
     */
    @RangeQuery(field = "updateTime")
    private TimePeriod updateTimeRange;

    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean isDeleted = false;

    /**
     * 排序
     */
    @OrderBy(field = "createTime")
    private OrderEnum orderByCreateTime;

    /**
     * 持续时间大于
     */
    @GreatTo(field = "duration")
    private Integer durationGreatThan;

    /**
     * 持续时间小于
     */
    @BelowTo(field = "duration")
    private Integer durationBelowTo;

    /**
     * 创建时间大于
     */
    @GreatTo(field = "createTime")
    private Date createTimeGreatTo;

    /**
     * 创建时间小于
     */
    @BelowTo(field = "createTime")
    private Date createTimeBelowTo;
}