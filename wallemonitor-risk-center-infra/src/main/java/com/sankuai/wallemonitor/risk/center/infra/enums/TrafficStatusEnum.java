package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum TrafficStatusEnum {
    NORMAL(0, "正常"),
    WARNING(1, "警告"),
    BROKEN(2, "故障"),
    NONE(3, "无");
    private int code;
    private String desc;


    public static TrafficStatusEnum getByName(String status) {
        if (StringUtils.isBlank(status)) {
            return NONE;
        }
        for (TrafficStatusEnum value : TrafficStatusEnum.values()) {
            if (value.name().equals(status)) {
                return value;
            }
        }
        return NONE;
    }
}
