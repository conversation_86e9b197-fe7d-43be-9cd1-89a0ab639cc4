package com.sankuai.wallemonitor.risk.center.infra.adaptar.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EveEsQueryRequest {

    /**
     * es 索引
     */
    private String dataset;

    /**
     * 查询条件
     */
    @JsonProperty("query")
    private QueryParamsDTO queryParamsDTO;

    /**
     * 排序条件
     */
    @JsonProperty("sort")
    private List<SortParamsDTO> sortParamsDTO;

    /**
     * 页码
     */
    @JsonProperty("page_index")
    private Long pageIndex;

    /**
     * 页面大小
     */
    @JsonProperty("page_size")
    private Long pageSize;


    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QueryParamsDTO {

        @JsonProperty("term")
        private List<TermDTO> termDTOList;

        @JsonProperty("range")
        private List<RangeDTO> rangeDTOList;

        @JsonProperty("terms")
        private List<TermDTOS> termsDTOList;

        @Builder
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TermDTO {

            private String field;
            private String value;
        }

        @Builder
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TermDTOS {

            private String field;
            private List<String> value;
        }

        @Builder
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class RangeDTO {

            private String field;
            /**
             * 大于等于（greater than or equal to）条件的值
             */
            private Long gte;

            /**
             * 小于等于（less than or equal to）条件的值
             */
            private Long lte;

            /**
             * 大于（greater than）条件的值
             */
            private Long gt;

            /**
             * 小于（less than）条件的值
             */
            private Long lt;
        }

    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SortParamsDTO {

        private String field;
        /**
         * true: 升序; false: 降序
         */
        private Boolean ascending;

    }

}
