package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.enums.RelatedServiceNameEnum;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Slf4j
public class RiskCaseRelatedServiceRecordDO {

    /**
     * caseId
     */
    private String caseId;

    /**
     * 服务名称
     */
    private RelatedServiceNameEnum serviceName;

    /**
     * 关联ID
     */
    private String relatedId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最近更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean isDeleted;
}
