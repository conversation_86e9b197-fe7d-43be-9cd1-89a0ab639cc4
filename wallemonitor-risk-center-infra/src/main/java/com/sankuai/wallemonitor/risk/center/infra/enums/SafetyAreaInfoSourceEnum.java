package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SafetyAreaInfoSourceEnum {

    MENDER_PORTAL(0, "区域适配"),
    BEACON_TOWER(1, "烽火台自维护"),
    ;

    private int code;
    private String desc;

    /**
     * 根据value查询枚举
     *
     * @param code
     * @return
     */
    public static SafetyAreaInfoSourceEnum findByValue(Integer code) {
        if (code == null) {
            return null;
        }
        for (SafetyAreaInfoSourceEnum sourceEnum : SafetyAreaInfoSourceEnum.values()) {
            if (sourceEnum.getCode() == code) {
                return sourceEnum;
            }
        }
        return null;
    }
}
