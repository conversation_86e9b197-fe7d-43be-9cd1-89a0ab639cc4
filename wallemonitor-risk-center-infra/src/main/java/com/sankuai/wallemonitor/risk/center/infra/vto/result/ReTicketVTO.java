package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * RE工单实体
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReTicketVTO {

    /**
     * 车名
     */
    private String vehicleName;

    /**
     * 工单状态,
     */
    private String vehicleStatus;

    /**
     * 工单内容
     */
    private ReTicketData data;

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReTicketData {
        private Long createdAt;

        private String itemName;

        private Long id;

        private Long updatedAt;

        private String name;

        private List<String> cc;

        private String assigned;

        private StateDTO state;

        @Builder
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class StateDTO {
            private String name;
        }
    }
}
