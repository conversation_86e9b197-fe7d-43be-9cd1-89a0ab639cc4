package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;


import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.BaAuthUtils;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.CloudCursorResourceRequest;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.CloudCursorResourceResponse;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CloudCursorAdapter {

    @Value("$KMS{cloud_cursor_secret}")
    private String cloudCursorCallSecretKey; // 呼叫云控的BA 鉴权secret


    @Value("${cloudCursor.clientId}")
    private String cloudCursorCallClientId; // 呼叫云控 BA鉴权的clientId

    @Value("${cloudCursor.url}")
    private String cloudCursorHost; // 呼叫云控域名

    /**
     * 呼叫云控操作url
     */
    private static final String CALL_CLOUD_OPERATION_URL = "/v1/cloud-operation/tasks";

    public CloudCursorResourceResponse callCloudCursor(CloudCursorResourceRequest cloudCursorResourceRequest) {
        try {
            String url = String.format("%s%s", cloudCursorHost, CALL_CLOUD_OPERATION_URL);
            log.info("呼叫云控请求数据 ： {}", cloudCursorResourceRequest);
            Map<String, String> headers =
                    BaAuthUtils.genMWSAuthHeader(CommonConstant.HTTP_METHOD_POST, CALL_CLOUD_OPERATION_URL,
                            cloudCursorCallClientId, cloudCursorCallSecretKey, "E, dd MMM yyyy HH:mm:ss z");

            EveHttpResponse<CloudCursorResourceResponse> cloudCursorResourceResponseEveHttpResponse =
                    HttpUtils.postJson(JacksonUtils.to(cloudCursorResourceRequest), url, headers,
                            CloudCursorResourceResponse.class);
            log.info("呼叫云控响应 : {}", cloudCursorResourceResponseEveHttpResponse);
            return cloudCursorResourceResponseEveHttpResponse.getData();
        } catch (Exception e) {
            log.error("呼叫云控异常", e);
            return null;
        }
    }


}
