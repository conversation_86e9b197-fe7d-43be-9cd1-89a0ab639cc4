package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 转向类型枚举
 */
@AllArgsConstructor
@Getter
public enum TurnTypeEnum {
    NO_TURN(1, "无转向"),
    LEFT_TURN(2, "左转"),
    RIGHT_TURN(3, "右转"),
    U_TURN(4, "掉头"),
    U_TURN_R(5, "右侧掉头"),
    TWICE_LEFT_TURN(6, "两次左转"),
    TWICE_U_TURN(7, "两次掉头");

    private final int code;
    private final String desc;

    /**
     * 根据 code 查询枚举
     *
     * @param code 转向类型代码
     * @return 对应的枚举值
     */
    public static TurnTypeEnum findByCode(int code) {
        for (TurnTypeEnum turnType : TurnTypeEnum.values()) {
            if (turnType.getCode() == code) {
                return turnType;
            }
        }
        return null;
    }
}