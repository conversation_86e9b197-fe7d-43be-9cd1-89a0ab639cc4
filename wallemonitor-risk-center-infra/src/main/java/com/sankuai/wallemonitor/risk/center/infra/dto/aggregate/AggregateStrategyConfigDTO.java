package com.sankuai.wallemonitor.risk.center.infra.dto.aggregate;

import com.sankuai.wallemonitor.risk.center.infra.enums.AggregateFieldEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertPolicyEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 聚合策略配置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregateStrategyConfigDTO {

    /**
     * 聚合告警策略
     * @see AlertPolicyEnum
     */
    private String alertPolicy;

    /**
     * 配置名称，用于记录最终告警由哪个配置产生
     */
    private String configName;

    /**
     * 灰度发布配置
     * 如果为null或所有维度都为空，则策略不生效
     */
    private AggregateAlertGrayConfigDTO grayRelease;

    /**
     * 事件类型 - 指定该告警配置针对特定类型的事件
     * 如果为null或空，则表示适用于所有类型的事件
     * @see RiskCaseTypeEnum
     */
    private Integer caseType;

    /**
     * 聚合维度：poi_name, type, vin, place_code
     * @see AggregateFieldEnum
     */
    private List<String> aggregateBy;

    /**
     * 时间窗口配置
     */
    private TimeWindowConfigDTO timeWindow;

    /**
     * 触发条件列表
     */
    private List<TriggerConditionDTO> triggerConditions;

    /**
     * 告警模板配置
     */
    private AlertTemplateConfigDTO alertTemplate;

    /**
     * 校验配置是否有效
     */
    public boolean isValid() {
        return StringUtils.isNotBlank(alertPolicy)
                && grayRelease != null && grayRelease.isValid()
                && CollectionUtils.isNotEmpty(aggregateBy)
                && timeWindow != null && timeWindow.isValid()
                && CollectionUtils.isNotEmpty(triggerConditions)
                && alertTemplate != null && alertTemplate.isValid();
    }
} 