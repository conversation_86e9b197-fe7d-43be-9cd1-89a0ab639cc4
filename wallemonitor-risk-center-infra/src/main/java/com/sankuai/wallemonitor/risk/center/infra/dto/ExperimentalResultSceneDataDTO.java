package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.ead.citadel.document.node.concept.Block;
import com.sankuai.ead.citadel.document.node.impl.node.ListItem;
import com.sankuai.ead.citadel.document.node.impl.node.Paragraph;
import com.sankuai.ead.citadel.document.node.impl.node.TableCell;
import com.sankuai.ead.citadel.document.util.Builder;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 实验结果分场景数据
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
@Data
@lombok.Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExperimentalResultSceneDataDTO {

    // 风险场景
    private ISCheckCategoryEnum categoryEnum;

    // 实际事件数
    private Integer actualCaseCount;

    // 召回事件数
    private Integer recallCaseCount;

    // 漏召事件列表
    private List<MissCaseAndTimes> missedEvents;


    @Data
    @lombok.Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MissCaseAndTimes {

        // 事件ID
        private String caseId;

        // 事件漏召次数
        private Integer times;
    }

    /**
     * 转换为表格单元格
     *
     * @return TableCell数组
     */
    public TableCell[] toTableCell() {
        CheckUtil.isGreatThan(recallCaseCount, -1, "召回事件数不能小于0");
        CheckUtil.isGreatThan(actualCaseCount, -1, "实际事件数不能小于0");
        return new TableCell[]{
                Builder.tableCell(getCategoryParagraph()),
                Builder.tableCell(getRecallRateParagraph()),
                Builder.tableCell(getActualCaseCountParagraph()),
                Builder.tableCell(getRecallCaseCountParagraph()),
                Builder.tableCell(getMissedEventsBulletList())
        };
    }

    private Paragraph getCategoryParagraph() {
        return Optional.ofNullable(categoryEnum)
                .map(category -> Builder.paragraph(category.getName()))
                .orElse(Builder.paragraph(" "));
    }

    private Paragraph getRecallRateParagraph() {
        if (recallCaseCount != null && actualCaseCount != null && actualCaseCount > 0) {
            return Builder.paragraph(String.format("%.2f%%", (double) recallCaseCount / actualCaseCount * 100));
        }
        return Builder.paragraph(" ");
    }

    private Paragraph getActualCaseCountParagraph() {
        return Optional.ofNullable(actualCaseCount)
                .map(count -> Builder.paragraph(String.valueOf(count)))
                .orElse(Builder.paragraph(" "));
    }

    private Paragraph getRecallCaseCountParagraph() {
        return Optional.ofNullable(recallCaseCount)
                .map(count -> Builder.paragraph(String.valueOf(count)))
                .orElse(Builder.paragraph(" "));
    }

    private Block getMissedEventsBulletList() {
        if (CollectionUtils.isEmpty(missedEvents)) {
            return Builder.paragraph(" ");
        }

        ListItem[] listItems = missedEvents.stream()
                .filter(element -> Objects.nonNull(element) && Objects.nonNull(element.getCaseId()))
                .map(missCase -> {
                    ListItem listItem = new ListItem();
                    String content = missCase.getCaseId() + " (" + missCase.getTimes() + "次)";
                    listItem.setContents(Collections.singletonList(Builder.paragraph(content)));
                    return listItem;
                })
                .toArray(ListItem[]::new);

        return Builder.bulletList(listItems);
    }

}


