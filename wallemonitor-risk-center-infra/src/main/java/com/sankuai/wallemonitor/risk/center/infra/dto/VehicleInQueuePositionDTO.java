package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆排队中的位置和车辆的距离
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleInQueuePositionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车辆位置
     */
    private PositionDO position;
    /**
     * 时间戳
     */
    private String time;
    /**
     * 距离
     */
    private Double distance;

    public Boolean invalid() {
        return position == null || position.invalid();
    }
}
