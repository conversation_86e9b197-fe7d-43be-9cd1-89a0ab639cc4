package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;


import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.OperateEnterContext;
import com.sankuai.wallemonitor.risk.center.infra.convert.SingleConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.compare.RecordCompareUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 通用的mapper仓储,用于领域对象转换成单个持久化对象存储，或单个持久化对象，转换成领域对象
 */
@Slf4j
public abstract class AbstractMapperSingleRepository<Mapper extends CommonMapper<PO>, Covert extends SingleConvert<PO, DO>, PO, DO> {

    @Autowired
    private Mapper mapper;

    @Autowired
    private Covert covert;


    /**
     * 批量保存
     *
     * @param doList
     */
    @RepositoryExecute
    public void batchSave(List<DO> doList) {
        //转换，根据参数获取，对比，然后保存
        if (CollectionUtils.isEmpty(doList)) {
            return;
        }
        doList.stream().filter(Objects::nonNull).forEach(this::save);
    }

    /**
     * 单个保存
     *
     * @param dataObject
     */
    @RepositoryExecute
    public void save(DO dataObject) {
        if (dataObject == null) {
            //无需处理
            return;
        }
        //获取之前的对象
        DO before = OperateEnterContext.getDomainAfterQuery(dataObject);
        //转换成poList
        PO po = covert.toPO(dataObject);
        PO beforePO = covert.toPO(before);
        //对比
        PO changedPO = compareAndGetNeedToSaveChangedList(beforePO, po, mapper.getPOClass());
        if (changedPO == null) {
            log.info("保存时，无变更对象");
            return;
        }
        //插入或者更新
        mapper.save(changedPO);
    }


    /**
     * 根据主键进行查询,
     */
    @RepositoryQuery
    public DO getByUniqueId(List<UniqueKeyDTO> uniqueKeyList) {
        //通过唯一键获取参数
        if (CollectionUtils.isEmpty(uniqueKeyList)) {
            return null;
        }
        //转换
        PO thisPO = mapper.selectByUk(uniqueKeyList);
        return covert.toDO(thisPO);

    }

    /**
     * 根据主键进行查询,
     */
    @RepositoryQuery
    public DO getByUniqueId(String... uniqueKeyAndValue) {
        if (uniqueKeyAndValue == null || uniqueKeyAndValue.length == 0) {
            return null;
        }
        SystemCheckUtil.isTrue(uniqueKeyAndValue.length % 2 == 0, "唯一键参数的数量不正确");
        List<UniqueKeyDTO> uniqueKeyList = new ArrayList<>();
        for (int i = 0; i < uniqueKeyAndValue.length; i += 2) {
            uniqueKeyList.add(new UniqueKeyDTO(uniqueKeyAndValue[i], uniqueKeyAndValue[i + 1]));
        }
        // 转换
        PO thisPO = mapper.selectByUk(uniqueKeyList);
        return covert.toDO(thisPO);

    }

    /**
     * 根据主键进行查询,
     */
    @RepositoryQuery
    public List<DO> queryByParam(Object queryParam) {
        //通过唯一键获取参数
        CheckUtil.isNotAllEmpty(queryParam, "批量查询时入参为空");
        //转换
        List<PO> thisPOList = mapper.queryByParam(queryParam);
        return thisPOList.stream().map(covert::toDO).collect(Collectors.toList());

    }

    /**
     * 根据主键进行查询,
     */
    @RepositoryQuery
    public Paging<DO> queryPageByParam(Object queryParam, Integer pageNum, Integer pageSize) {
        //通过唯一键获取参数
        CheckUtil.isNotAllEmpty(queryParam, "批量查询时入参为空");
        CheckUtil.isNotNull(pageSize, "分页参数为空");
        CheckUtil.isNotNull(pageNum, "分页参数为空");
        //转换
        Paging<PO> thisPOList = mapper.queryByParamByPage(queryParam, pageNum, pageSize);
        if (thisPOList == null || CollectionUtils.isEmpty(thisPOList.getElements())) {
            Paging.PagingBuilder<DO> pageBuilder = Paging.builder();
            return pageBuilder.elements(new ArrayList<>()).total(0).pageSize(pageSize).pageNum(pageNum).build();
        }
        Paging<DO> page = new Paging<>();
        page.setPageNum(thisPOList.getPageNum());
        page.setPageSize(thisPOList.getPageSize());
        page.setTotal(thisPOList.getTotal());
        page.setElements(thisPOList.getElements().stream().map(covert::toDO).collect(Collectors.toList()));
        return page;
    }

    /**
     * 对比获取变更的类型
     *
     * @return
     */
    @SneakyThrows
    protected PO compareAndGetNeedToSaveChangedList(PO before, PO after, Class<PO> poClass) {
        //进行对比
        return RecordCompareUtils.checkAndGetChangedFieldEntity(before, after, poClass,
                mapper.getTableUniqueList());

    }

    /**
     * 根据主键进行查询,
     */
    @RepositoryQuery
    public void batchDelete(List<Long> idList) {
        //通过唯一键获取参数
        CheckUtil.isNotAllEmpty(idList, "批量删除时入参为空");
        //转换
        mapper.deleteBatchIds(idList);
    }


}
