package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.convert.CaseSortDataConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.CaseSortDataMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseSortData;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseSortDataDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseSortDataRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseSortDataDOQueryParam;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CaseSortDataRepositoryImpl extends
        AbstractMapperSingleRepository<CaseSortDataMapper, CaseSortDataConvert, CaseSortData, CaseSortDataDO>
        implements CaseSortDataRepository {

    private static final String UK_RISK_CASE_ID = "caseId";

    @Override
    public List<CaseSortDataDO> queryByParam(CaseSortDataDOQueryParam param) {
        return super.queryByParam(param);
    }

    @Override
    public Map<String, CaseSortDataDO> queryMapByParam(CaseSortDataDOQueryParam param) {
        List<CaseSortDataDO> caseSortDataDOList = queryByParam(param);
        if (CollectionUtils.isEmpty(caseSortDataDOList)) {
            return new HashMap<>();
        }
        return caseSortDataDOList.stream().collect(Collectors.toMap(CaseSortDataDO::getCaseId, Function.identity()));
    }

    @Override
    public CaseSortDataDO getByCaseId(String caseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_RISK_CASE_ID)
                .value(caseId)
                .build()));
    }
}
