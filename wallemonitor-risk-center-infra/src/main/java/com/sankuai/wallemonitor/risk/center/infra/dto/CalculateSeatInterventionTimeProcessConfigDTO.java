package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CalculateSeatInterventionTimeProcessConfigDTO {

    /**
     * 风险case类型
     */
    private List<Integer> caseTypeList;

    /**
     * 风险case来源
     */
    private List<Integer> caseSourceList;

    /**
     * 查询范围延迟事件秒级
     */
    private Integer queryRangeDelaySeconds;
}
