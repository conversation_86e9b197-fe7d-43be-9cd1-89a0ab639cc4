package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.VehicleRuntimeInfoLocationConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.VehicleRuntimeLocationMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.VehicleRuntimeLocation;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeLocationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoLocationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeLocationDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class VehicleRuntimeInfoLocationRepositoryImpl extends
        AbstractMapperSingleRepository<VehicleRuntimeLocationMapper, VehicleRuntimeInfoLocationConvert, VehicleRuntimeLocation, VehicleRuntimeLocationDO>
        implements VehicleRuntimeInfoLocationRepository {

    private static final String KEY_VIN = "vin";

    @Override
    @RepositoryQuery
    public List<VehicleRuntimeLocationDO> queryByParam(VehicleRuntimeLocationDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    @Override
    public Paging<VehicleRuntimeLocationDO> queryByParamByPage(VehicleRuntimeLocationDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    @Override
    @RepositoryQuery
    public VehicleRuntimeLocationDO getByVin(String vin) {
        return super.getByUniqueId(KEY_VIN, vin);
    }

    @Override
    @RepositoryExecute
    public void save(VehicleRuntimeLocationDO VehicleRuntimeLocationDO) {
        super.save(VehicleRuntimeLocationDO);
    }

    @Override
    @RepositoryExecute
    public void batchSave(List<VehicleRuntimeLocationDO> VehicleRuntimeLocationDOList) {
        super.batchSave(VehicleRuntimeLocationDOList);
    }
}
