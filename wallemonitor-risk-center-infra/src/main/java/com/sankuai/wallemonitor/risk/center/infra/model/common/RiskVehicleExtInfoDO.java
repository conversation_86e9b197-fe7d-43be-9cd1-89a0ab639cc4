package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.sankuai.wallemonitor.risk.center.infra.enums.MrmRoleEnum;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class RiskVehicleExtInfoDO {

    /**
     * 云控mis
     */
    private String mrmMisId;

    /**
     * 云控mis
     */
    private String mrmSeatNo;

    /**
     * 云控角色
     */
    private MrmRoleEnum role;


    /**
     * 呼叫云安全时间
     */
    private Date callSafetyTime;


    /**
     * 取消云安全时间
     */
    private Date cancelSafetyTime;

    /**
     * 云安全确认时间
     */
    private Date safetyConfirmTime;

    /**
     * 云安全确认时间
     */
    private Date safetyCancelTime;

    /**
     * 关联信息
     */
    private Map<String, Object> vehicleContent;

    /**
     * 安全区域类型
     */
    private String safetyAreaType;
}
