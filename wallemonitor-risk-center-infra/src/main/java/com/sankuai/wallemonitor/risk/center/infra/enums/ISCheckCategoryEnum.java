package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

public enum ISCheckCategoryEnum {
    // 默认匹配不到任何检查结果
    CANT_FOUND_ANY("", "CANT_FOUND_ANY", "不能分辨"),

    // Ai Friday 识别为GOOD - 有风险
    IN_VALID_DRIVING_AREA("GOOD", "IN_VALID_DRIVING_AREA",
            "非行驶区域停滞"),
    IN_MIDDLE_ROAD("GOOD", "IN_MIDDLE_ROAD", "路中间停滞"),
    IN_JUNCTION("GOOD", "IN_JUNCTION", "路口绿灯停滞"),
    OPPSITE_ROAD_DIRECTION("GOOD", "OPPSITE_ROAD_DIRECTION", "逆行停滞"),
    CONFLICT_WITH_PASSAGER("GOOD", "CONFLICT_WITH_PASSAGER", "车辆顶牛"),
    STOP_BY_OBSTACLE("GOOD", "STOP_BY_OBSTACLE", "障碍物堵路"),
    STOP_ON_ROAD_SIDE("GOOD", "STOP_ON_ROAD_SIDE", "靠边停滞"),
    STOP_BY_INACTIVE_VEHICLE("GOOD", "STOP_BY_INACTIVE_VEHICLE", "静态车辆绕行"),
    GOOD_OTHER("GOOD", "GOOD_OTHER", "其他"),

    // Ai Friday 识别为BAD - 无风险
    RED_LIGHT("BAD", "RED_LIGHT", "待红灯变绿后通行"),
    WAITING_FRONT_PASSAGER("BAD", "WAITING_FRONT_PASSAGER", "排队通行"),
    IN_PARKING_AREA("BAD", "IN_PARKING_AREA", "停车场停放"),
    IN_PARKING_QUEUE_AREA("BAD", "IN_PARKING_QUEUE_AREA", "缓冲区等待"),
    WAITING_RIDER_TAKING_ORDER("BAD", "WAITING_RIDER_TAKING_ORDER",
            "在等待取餐"),
    ON_TRAILER("BAD", "ON_TRAILER", "拖车中"),
    IN_SHOW("BAD", "IN_SHOW", "展览中"),
    BAD_OTHER("BAD", "BAD_OTHER", "其他"),

    SWITCH_POWER("BAD", "SWITCH_POWER", "切电中"),

    // 根据车辆位置识别为在停车区域范围内
    MENDER_IN_PARKING_AREA("BAD", "MENDER_IN_PARKING_AREA",
            "在区域适配的停滞区域内"),
    //车辆数据总线实时信息和上下文信息过滤
    EVE_AND_RUNTIME_INFO_FILTER("BAD", "EVE_AND_RUNTIME_INFO_FILTER",
            "车辆数据总线实时信息和上下文信息过滤"),
    ACCIDENT_HANDLE("BAD", "ACCIDENT_HANDLE", "事故处置");

    private final String category;
    private final String subcategory;
    private final String name;

    ISCheckCategoryEnum(String category, String subcategory, String name) {
        this.category = category;
        this.subcategory = subcategory;
        this.name = name;
    }

    /**
     * 根据名称获取枚举值
     *
     * @param name
     * @return
     */
    public static ISCheckCategoryEnum getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (ISCheckCategoryEnum value : ISCheckCategoryEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }

    public static List<ISCheckCategoryEnum> getGoodCategoryList() {
        return Arrays.stream(ISCheckCategoryEnum.values()).filter(ISCheckCategoryEnum::isGood)
                .collect(Collectors.toList());
    }

    public static List<String> getGoodCategoryNameList() {
        return getGoodCategoryList().stream().map(ISCheckCategoryEnum::name).collect(Collectors.toList());
    }

    public static ISCheckCategoryEnum getBySubcategory(String subcategory) {
        if (StringUtils.isBlank(subcategory)) {
            return null;
        }
        for (ISCheckCategoryEnum value : ISCheckCategoryEnum.values()) {
            if (value.getSubcategory().equals(subcategory)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据名称获取枚举值
     *
     * @param name
     * @return
     */
    public static ISCheckCategoryEnum getByName(CaseMarkCategoryEnum categoryEnum) {
        if (categoryEnum == null) {
            return null;
        }
        return getByName(categoryEnum.name());
    }

    /**
     * 判断是否为风险项
     *
     * @param category
     * @return
     */
    public static boolean isBad(ISCheckCategoryEnum category) {
        if (category == null) {
            return false;
        }
        return category.getCategory().equals("BAD");
    }

    /**
     * 判断是否为风险项
     * @param category
     * @return
     */
    public static boolean isGood(ISCheckCategoryEnum category) {
        if (category == null) {
            return false;
        }
        return category.getCategory().equals("GOOD");
    }

    public static Set<String> getBadCategoryNameList() {
        return Arrays.stream(ISCheckCategoryEnum.values()).filter(ISCheckCategoryEnum::isBad)
                .map(ISCheckCategoryEnum::name).collect(Collectors.toSet());
    }

    public String getCategory() {
        return category;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public String getName() {
        return name;
    }
}