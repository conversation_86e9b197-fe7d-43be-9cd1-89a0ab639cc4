package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 泊车失败事件与泊位ID关联DO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskCaseParkingPlotRelationDO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 风险事件ID
     */
    @DomainUnique
    private String caseId;

    /**
     * 泊位ID
     */
    private String parkingPlotId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    @Builder.Default
    private IsDeleteEnum isDeleted = IsDeleteEnum.NOT_DELETED;
} 