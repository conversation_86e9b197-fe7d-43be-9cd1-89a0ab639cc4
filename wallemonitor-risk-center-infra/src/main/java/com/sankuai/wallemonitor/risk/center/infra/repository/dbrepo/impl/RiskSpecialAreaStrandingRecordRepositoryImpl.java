package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskSpecialAreaStrandingRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskSpecialAreaStrandingRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskSpecialAreaStrandingRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskSpecialAreaStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskSpecialAreaStrandingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskSpecialAreaStrandingRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 特殊区域停滞记录仓储实现
 */
@Component
@Slf4j
public class RiskSpecialAreaStrandingRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskSpecialAreaStrandingRecordMapper, RiskSpecialAreaStrandingRecordConvert, RiskSpecialAreaStrandingRecord, RiskSpecialAreaStrandingRecordDO> implements
        RiskSpecialAreaStrandingRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询特殊区域停滞记录
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskSpecialAreaStrandingRecordDO> queryByParam(RiskSpecialAreaStrandingRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询特殊区域停滞记录 (分页)
     *
     * @param paramDTO
     * @return
     */
    @Override
    public Paging<RiskSpecialAreaStrandingRecordDO> queryByParamByPage(
            RiskSpecialAreaStrandingRecordDOQueryParamDTO paramDTO, Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据临时事件ID查询特殊区域停滞记录
     *
     * @param tmpCaseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskSpecialAreaStrandingRecordDO getByTmpCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }

    /**
     * 保存特殊区域停滞记录
     *
     * @param recordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskSpecialAreaStrandingRecordDO recordDO) {
        super.save(recordDO);
    }

    /**
     * 批量保存特殊区域停滞记录
     *
     * @param recordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskSpecialAreaStrandingRecordDO> recordDOList) {
        super.batchSave(recordDOList);
    }
}