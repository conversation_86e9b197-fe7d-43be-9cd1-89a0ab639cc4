package com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector;

import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskErrorBypassRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 错误绕行检测记录工厂类
 */
@Component
public class RiskErrorBypassRecordFactory extends RiskDetectorRecordFactory<RiskErrorBypassRecordDO> {

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Override
    public RiskErrorBypassRecordDO init(DetectContextDTO detectContextDTO) {
        VehicleRuntimeInfoContextDO runtimeContextDO = detectContextDTO.toShortVehicleInfoSnapShot();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, runtimeContextDO.getVin(),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.ERROR_BYPASS, runtimeContextDO.getLastUpdateTime());
        return RiskErrorBypassRecordDO.builder()
                .tmpCaseId(caseId)
                .type(RiskCaseTypeEnum.ERROR_BYPASS)
                .vin(runtimeContextDO.getVin())
                .duration(0)
                .status(DetectRecordStatusEnum.PROCESSING)
                .occurTime(runtimeContextDO.getLastUpdateTime())
                //fixme: 需要计算这两个值并进行填充
                .isCrossLine((Boolean) detectContextDTO.get("isCrossLine"))
                .isInRouteLane((Boolean)detectContextDTO.get("isInRefineLine"))
                .build();
    }
} 