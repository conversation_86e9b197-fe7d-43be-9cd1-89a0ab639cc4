package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.convert.ActionChainResultLogConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.ActionChainResultLogMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.ActionChainResultLog;
import com.sankuai.wallemonitor.risk.center.infra.model.core.ActionChainResultLogDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.ActionChainResultLogRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.LogActionChainResultQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ActionChainResultLogRepositoryImpl extends
        AbstractMapperSingleRepository<ActionChainResultLogMapper, ActionChainResultLogConvert, ActionChainResultLog, ActionChainResultLogDO>
        implements ActionChainResultLogRepository {

    /**
     * 保存
     *
     * @param resultDO
     */
    @Override
    @RepositoryExecute
    public void save(ActionChainResultLogDO resultDO) {
        super.save(resultDO);

    }

    /**
     * 按条件查询
     *
     * @param param
     * @return
     */
    @Override
    public List<ActionChainResultLogDO> queryByParam(LogActionChainResultQueryParamDTO param) {
        return super.queryByParam(param);
    }
}
