package com.sankuai.wallemonitor.risk.center.infra.adaptar.response;

import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleRealtimeStatusVTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆状态远程调用响应
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VehicleRealtimeStatusResponse {
    /**
     * 0 表示成功, 其他则是失败
     */
    private Integer ret;

    /**
     * 提示信息
     */
    private String msg;

    /**
     * 查询车辆状态结果列表
     */
    private List<VehicleRealtimeStatusVTO> data;
}
