package com.sankuai.wallemonitor.risk.center.infra.adaptar.response;

import com.sankuai.wallemonitor.risk.center.infra.vto.result.EventPlatformVTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventPlatformResponse {

    /**
     * 0: ok; 1001: 系统错误
     */
    private Integer code;

    /**
     * 响应信息
     */
    private String msg;

    /**
     * 数据
     */
    private List<EventPlatformVTO> data;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 页面大小
     */
    private Integer size;

    /**
     * 总页数
     */
    private Integer totalPage;

    /**
     * 总数量
     */
    private Integer totalSize;


}
