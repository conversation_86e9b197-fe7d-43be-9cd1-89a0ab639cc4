package com.sankuai.wallemonitor.risk.center.infra.adaptar.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据平台快速回传接口响应结果
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataPlatformFastUploadResponse {

    /**
     * 0: ok; 1001: 系统错误
     */
    private String ret;

    /**
     * 响应信息
     */
    private String msg;

    /**
     * 响应数据
     */
    private FastUploadResp data;


    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FastUploadResp {

        @JsonProperty("id")
        private String taskId;
    }
}
