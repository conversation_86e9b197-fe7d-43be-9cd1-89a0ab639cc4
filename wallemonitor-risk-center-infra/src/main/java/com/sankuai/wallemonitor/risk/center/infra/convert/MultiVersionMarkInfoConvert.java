package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.MultiVersionMarkInfo;
import com.sankuai.wallemonitor.risk.center.infra.model.core.MultiVersionMarkInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", imports = {EnumsConvertMapper.class}, uses = {EnumsConvertMapper.class})
public interface MultiVersionMarkInfoConvert extends SingleConvert<MultiVersionMarkInfo, MultiVersionMarkInfoDO> {

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    @Mapping(source = "checkResult", target = "checkResult", qualifiedByName = "parseCheckResult")
    MultiVersionMarkInfoDO toDO(MultiVersionMarkInfo multiVersionMarkInfo);

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    @Mapping(source = "checkResult", target = "checkResult", qualifiedByName = "serializeCheckResult")
    MultiVersionMarkInfo toPO(MultiVersionMarkInfoDO multiVersionMarkInfoDO);
}
