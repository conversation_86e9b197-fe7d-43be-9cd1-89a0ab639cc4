package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.dianping.lion.client.util.CollectionUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.BaAuthUtils;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.WorkstationCreateRequest;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.WorkstationQueryRequest;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.WorkstationQueryRequest.RangeQueryCondition;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.WorkstationQueryRequest.TermQueryCondition;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.WorkstationQueryRequest.TermsQueryCondition;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.WorkstationCreateResponse;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.WorkstationCreateResponse.WorkstationCreateResponseData;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.WorkstationQueryResponse;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.WorkstationQueryResponse.WorkstationQueryResponseData;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.WorkstationMsgConsumerConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteErrorException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WorkstationAdapter {

    /**
     * 查询工单列表接口
     */
    private static final String QUERY_WORKSTATION_CASE_ID_URL = "/workstation/api/case/search/ba";

    /**
     * 创建工单接口: https://km.sankuai.com/collabpage/1684314424
     */
    private static final String CREATE_WORKSTATION_CASE_DETAIL_URL = "/workstation/api/case/create/ba";

    /**
     * clientId
     */
    private static final String CLIENT_ID = "risk_center";

    @Value("${workstation.queryDomain}")
    private String workstationHost;

    @Value("$KMS{workstation.ba.secret}")
    private String BA_SECRET_KEY;

    @ConfigValue(key = LionKeyConstant.LION_KEY_RISK_WORKSTATION_MSG_CONSUMER_CONFIG, value = "", defaultValue = "1", allowBlankValue = true)
    private WorkstationMsgConsumerConfigDTO workstationMsgConsumerConfigDTO;


    /**
     * 获取case平台caseID集合
     *
     * @return
     */
    public List<String> getWorkstationCaseId(String vin, String startTime, String endTime) {
        // BA鉴权
        Map<String, String> headers = BaAuthUtils.genMWSAuthHeader(CommonConstant.HTTP_METHOD_POST,
                QUERY_WORKSTATION_CASE_ID_URL, CLIENT_ID, BA_SECRET_KEY, "E, dd MMM yyyy HH:mm:ss z");
        String url = String.format("%s%s", workstationHost, QUERY_WORKSTATION_CASE_ID_URL);

        // 构建查询请求
        WorkstationQueryRequest request = new WorkstationQueryRequest();

        List<Object> queryList = new ArrayList<>();
        //        queryList.add(
        //                TermQueryCondition.builder().field("caseType").value(CommonConstant.DATA_SOURCE).group(1).build());
        queryList.add(TermsQueryCondition.<String>builder().field("caseType")
                .value(workstationMsgConsumerConfigDTO.getCaseTypeList()).group(1).build());
        queryList.add(TermQueryCondition.builder().field("vin").value(vin).group(1).build());
        queryList.add(
                RangeQueryCondition.builder().field("operateTime").fieldType("datetime").gte(startTime).lte(endTime)
                        .group(1).build());
        request.setQueryCondition(queryList);
        try {
            EveHttpResponse<WorkstationQueryResponse> response = HttpUtils.postJson(JacksonUtils.to(request),
                    url,
                    headers,
                    new TypeReference<WorkstationQueryResponse>() {
                    });
            log.info("getWorkstationCaseId request:{}, response:{}", JacksonUtils.to(request),
                    JacksonUtils.to(response));
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || Objects.isNull(
                    response.getData().getData())) {
                throw new RemoteErrorException("工单平台查询工单id失败");
            }
            // 解析响应
            WorkstationQueryResponseData responseData = response.getData().getData();
            if (CollectionUtils.isEmpty(responseData.getRows())) {
                return new ArrayList<>();
            }
            return responseData.getRows().stream().map(WorkstationQueryResponseData.CaseDetail::getCaseId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getWorkstationCaseId error", e);
        }
        return new ArrayList<>();
    }

    /**
     * 创建case
     *
     * @param request
     * @return
     */
    public WorkstationCreateResponseData createWorkstationCase(WorkstationCreateRequest request) {
        // 1 构建BA鉴权请求头
        Map<String, String> headers = BaAuthUtils.genMWSAuthHeader(CommonConstant.HTTP_METHOD_POST,
                CREATE_WORKSTATION_CASE_DETAIL_URL, CLIENT_ID, BA_SECRET_KEY, "E, dd MMM yyyy HH:mm:ss z");
        String url = String.format("%s%s", workstationHost, CREATE_WORKSTATION_CASE_DETAIL_URL);
        try {
            EveHttpResponse<WorkstationCreateResponse> response = HttpUtils.postJson(JacksonUtils.to(request), url,
                    headers,
                    WorkstationCreateResponse.class);
            log.info("createWorkstationCase, response:{}", JacksonUtils.to(response));
            if (Objects.isNull(response) || Objects.isNull(response.getData())) {
                throw new RemoteErrorException("创建工单失败");
            }
            WorkstationCreateResponse workstationCreateResponse = response.getData();
            // 创建工单成功
            if (Objects.equals(workstationCreateResponse.getCode(), 0)) {
                return workstationCreateResponse.getData();
                // caseId重复且case内容前后完全一致
            } else if (Objects.equals(workstationCreateResponse.getCode(), 406)) {
                log.warn(String.format("createWorkstationCase, 工单已存在, caseId:%s", request.getCaseId()));
                return null;
                // 其他错误码
            } else {
                String errMsg = String.format("创建工单失败：工单平台返回错误响应, code:%s, msg:%s",
                        workstationCreateResponse.getCode(),
                        workstationCreateResponse.getMessage());
                throw new RemoteErrorException(errMsg);
            }
        } catch (Exception e) {
            log.error("createWorkstationCase error", e);
        }
        return null;
    }

}
