package com.sankuai.wallemonitor.risk.center.infra.applicationcontext;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.meituan.mtrace.Tracer;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.OperateEnterDTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class OperateEnterContext {


    private static final TransmittableThreadLocal<Long> entryTimeStamp = new TransmittableThreadLocal<>();
    private static final TransmittableThreadLocal<Map<String, Object>> queryDomainFromRepository = new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<Map<String, Object>> executeDomainAfterExecute = new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<OperateEnterDTO> operateEnterInfo = new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<DomainEventExtInfoDTO> operateExtInfo = new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<Map<String, ConcurrentLinkedDeque<Integer>>> domainChangeSortList = new TransmittableThreadLocal<>();

    private static final Object sortLock = new Object();

    /**
     * 入口客户端ip
     */
    private static final TransmittableThreadLocal<String> entryClientIp = new TransmittableThreadLocal<>();

    public static void bindQueryDomainContainer(Map<String, Object> map) {
        queryDomainFromRepository.set(map);
    }

    public static void bindExecuteDomainContainer(Map<String, Object> map) {
        executeDomainAfterExecute.set(map);
    }

    public static void putDomainFromRepository(Map<String, Object> map) {
        Map<String, Object> domainMap = queryDomainFromRepository.get();
        if (MapUtils.isEmpty(map)) {
            return;
        }
        if (domainMap == null) {
            log.info("没有初始化领域，查询放入失败,入口:{}", operateExtInfo.get());
            return;
        }
        //放进去
        domainMap.putAll(map);
    }

    public static void putDomainAfterExecute(Map<String, Object> map) {
        Map<String, Object> domainMap = executeDomainAfterExecute.get();
        if (MapUtils.isEmpty(map)) {
            return;
        }
        if (domainMap == null) {
            log.info("没有初始化领域，更新放入失败，入口:{}", operateEnterInfo.get());
            return;
        }
        //放进去
        domainMap.putAll(map);
    }


    public static Map<String, Object> getAllDomainAfterExecute() {
        if (MapUtils.isEmpty(executeDomainAfterExecute.get())) {
            return new HashMap<>();
        }
        return new HashMap<>(executeDomainAfterExecute.get());
    }

    public static Map<String, Object> getAllDomainAfterQuery() {
        if (MapUtils.isEmpty(queryDomainFromRepository.get())) {
            return new HashMap<>();
        }
        return new HashMap<>(queryDomainFromRepository.get());
    }

    public static <T> List<T> getDomainAfterQuery(List<T> domainList) {
        if (MapUtils.isEmpty(queryDomainFromRepository.get()) || CollectionUtils.isEmpty(domainList)) {
            return new ArrayList<>();
        }
        return domainList.stream()
                .map(domain -> (T) queryDomainFromRepository.get().get(String.valueOf(System.identityHashCode(domain))))
                .filter(
                        Objects::nonNull).collect(
                        Collectors.toList());

    }

    public static <T> T getDomainAfterQuery(T domain) {
        if (MapUtils.isEmpty(queryDomainFromRepository.get()) || domain == null) {
            return null;
        }
        return (T) queryDomainFromRepository.get().get(String.valueOf(System.identityHashCode(domain)));

    }


    public static OperateEnterDTO getOperateEnterInfo() {
        return OperateEnterContext.operateEnterInfo.get();
    }

    public static String getOperateTraceId() {
        return Optional.ofNullable(OperateEnterContext.operateEnterInfo.get()).map(OperateEnterDTO::getOperateTraceId)
                .orElse(Tracer.id());
    }

    public static void bindOperateEnterInfo(OperateEnterDTO operateEnterDTO) {
        OperateEnterContext.operateEnterInfo.set(operateEnterDTO);
    }

    public static void clear() {
        queryDomainFromRepository.remove();
        operateEnterInfo.remove();
        executeDomainAfterExecute.remove();
        operateExtInfo.remove();
        domainChangeSortList.remove();
        entryTimeStamp.remove();
        entryClientIp.remove();
    }

    public static void bindOperateExtInfo() {
        operateExtInfo.set(DomainEventExtInfoDTO.builder().build());
    }

    public static void bindDomainChangeList() {
        domainChangeSortList.set(new ConcurrentHashMap<>());
    }


    public static <T> List<Integer> getDomainSortedChangeList(String domainClassName) {
        if (domainChangeSortList.get() == null || StringUtils.isBlank(domainClassName)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(
                domainChangeSortList.get().getOrDefault(domainClassName, new ConcurrentLinkedDeque<>()));
    }

    public static void addDomainSort(String domainClassName, List<Integer> domainClassNameSort) {
        if (domainChangeSortList.get() == null || CollectionUtils.isEmpty(domainClassNameSort) || StringUtils.isBlank(
                domainClassName)) {
            return;
        }
        synchronized (sortLock) {
            domainChangeSortList.get().computeIfAbsent(domainClassName, x -> new ConcurrentLinkedDeque<>())
                    .addAll(domainClassNameSort);
        }
    }


    public static DomainEventExtInfoDTO getExtInfo() {
        return operateExtInfo.get();
    }


    public static void bindEntryTimeStamp(long currentTimeMillis) {
        entryTimeStamp.set(currentTimeMillis);
    }

    public static Long getEntryTimeStamp() {
        return entryTimeStamp.get();
    }

    /**
     * 绑定入口客户端ip
     * @param clientIp 客户端ip
     */
    public static void bindEntryClientIp(String clientIp) {
        entryClientIp.set(clientIp);
    }

    /**
     * 获取入口客户端ip
     * @return 客户端ip
     */
    public static String getClientIp() {
       return entryClientIp.get();
    }
}
