package com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector;

import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskRetrogradeRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class RiskRetrogradeRecordFactory extends RiskDetectorRecordFactory<RiskRetrogradeRecordDO> {

    @Resource
    private IDGenerateRepository idGenerateRepository;


    @Override
    public RiskRetrogradeRecordDO init(DetectContextDTO detectContextDTO) {
        VehicleRuntimeInfoContextDO runtimeContextDO = detectContextDTO.toShortVehicleInfoSnapShot();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, runtimeContextDO.getVin(),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.RETROGRADE, runtimeContextDO.getLastUpdateTime());
        return RiskRetrogradeRecordDO.builder()
                .tmpCaseId(caseId)
                .type(RiskCaseTypeEnum.RETROGRADE)
                .vin(runtimeContextDO.getVin())
                .duration(0)
                .status(DetectRecordStatusEnum.PROCESSING)
                .occurTime(runtimeContextDO.getLastUpdateTime())
                .build();
    }


}
