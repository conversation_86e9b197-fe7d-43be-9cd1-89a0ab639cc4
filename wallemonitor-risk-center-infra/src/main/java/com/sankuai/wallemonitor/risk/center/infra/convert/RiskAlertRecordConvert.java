package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskAlertRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskAlertRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 风险告警记录转换器
 */
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskAlertRecordConvert extends
        SingleConvert<RiskAlertRecord, RiskAlertRecordDO> {

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnumFromInteger")
    @Mapping(source = "status", target = "status", qualifiedByName = "toAlertRecordStatusEnum")
    @Mapping(source = "label", target = "label", qualifiedByName = "toAlertRecordLabelEnum")
    @Mapping(source = "upgradeStatus", target = "upgradeStatus", qualifiedByName = "toUpgradeStatusEnum")
    RiskAlertRecordDO toDO(RiskAlertRecord record);

    @Override
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleteInteger")
    @Mapping(source = "status", target = "status", qualifiedByName = "toAlertRecordStatus")
    @Mapping(source = "label", target = "label", qualifiedByName = "toAlertRecordLabel")
    @Mapping(source = "upgradeStatus", target = "upgradeStatus", qualifiedByName = "toUpgradeStatus")
    RiskAlertRecord toPO(RiskAlertRecordDO recordDO);
} 