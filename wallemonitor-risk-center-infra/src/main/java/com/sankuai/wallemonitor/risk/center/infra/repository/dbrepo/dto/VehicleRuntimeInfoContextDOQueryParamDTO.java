package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleRuntimeInfoContextDOQueryParamDTO {

    /**
     * 车辆VIN码
     */
    private String vin;

    /**
     * 车辆VIN码列表
     */
    @InQuery(field = "vin")
    private List<String> vinList;

    /**
     * 驾驶模式列表
     */
    @InQuery(field = "driveMode")
    private List<Integer> driveModeList;

    /**
     * 位置类型列表
     */
    @InQuery(field = "positionType")
    private List<String> positionTypeList;

    /**
     * 是否切电中
     */
    private Integer batterySwitching;

    /**
     * 是否和道路方向相反
     */
    private Integer oppositeWithRoad;
    /**
     * 交通灯类型列表
     */
    @InQuery(field = "trafficLightType")
    private List<String> trafficLightTypeList;

    /**
     * 创建时间范围
     */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    /**
     * 上次更新时间范围
     */
    @RangeQuery(field = "lastUpdateTime")
    private TimePeriod lastUpdateTimeRange;

    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean isDeleted = false;

}