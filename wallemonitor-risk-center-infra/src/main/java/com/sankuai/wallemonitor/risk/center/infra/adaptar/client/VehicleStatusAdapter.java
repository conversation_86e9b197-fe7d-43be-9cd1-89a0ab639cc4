package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.VehicleRealtimeStatusResponse;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleRealtimeStatusVTO;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 车辆状态查询 适配器
 * 文档链接: https://km.sankuai.com/collabpage/1521843415
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Component
@Slf4j
public class VehicleStatusAdapter {
    private static final String PATH = "/iiirc/openapi/queryVehicleStatusBatch";

    @Value("${vehicleStatus.queryDomain}")
    private String domain;

    /**
     * 根据车架号查询车辆状态列表
     *
     * @param vehicleType 车辆类型
     * @param vinList 车架号vin
     * @return
     */
    public List<VehicleRealtimeStatusVTO> queryByVinList(Integer vehicleType, List<String> vinList) {
        if (Objects.isNull(vehicleType) && CollectionUtils.isEmpty(vinList)) {
            log.info("queryByVinList with illegal param, vehicleType: {}, vinList: {}", vehicleType, vinList);
            return Collections.emptyList();
        }

        Map<String, String> requestParam = new HashMap<>();
        if (Objects.nonNull(vehicleType)) {
            requestParam.put("vehicleType", vehicleType.toString());
        }

        if (CollectionUtils.isNotEmpty(vinList)) {
            requestParam.put("vinList", String.join(CharConstant.CHAR_DD, vinList));
        }

        try {
            log.info("queryByVinList request, requestParam: {}", JacksonUtils.to(requestParam));
            EveHttpResponse<VehicleRealtimeStatusResponse> response = HttpUtils.get(requestParam, domain,
                    PATH, new HashMap<>(), VehicleRealtimeStatusResponse.class);
            log.info("queryByVinList response, response: {}", JacksonUtils.to(response));
            if (!Objects.equals(response.getCode(), CommonConstant.HTTP_SUCCESS_CODE) || !Objects.equals(response.getData().getRet(), CommonConstant.VEHICLE_STATUS_QUERY_SUCCESS_CODE)) {
                log.info("queryByVinList request fail, vehicleType: {}, vinList: {}, response: {}", vehicleType,
                        JacksonUtils.to(vinList), JacksonUtils.to(response));
            }

            return Optional.ofNullable(response.getData()).map(VehicleRealtimeStatusResponse::getData).orElse(Collections.emptyList());
        } catch (Exception exception) {
            log.error("fail in queryByVinList with exception, vehicleType: {}, vinList: {}", vehicleType,
                    JacksonUtils.to(vinList), exception);
        }

        return Collections.emptyList();
    }

    /**
     * 根据车架号查询车辆状态列表
     *
     * @param vinList
     * @return
     */
    public Map<String, Integer> getVin2MrmStatusMap(List<String> vinList) {
        List<VehicleRealtimeStatusVTO> queryByVinList = queryByVinList(-1, vinList);
        if (CollectionUtils.isEmpty(queryByVinList)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> vin2MrmStatusMap = new HashMap<>();
        for (VehicleRealtimeStatusVTO vehicleRealtimeStatusVTO : queryByVinList) {
            if (Objects.isNull(vehicleRealtimeStatusVTO)) {
                continue;
            }
            vin2MrmStatusMap.put(vehicleRealtimeStatusVTO.getVin(), vehicleRealtimeStatusVTO.getCockpitStatus());
        }

        return vin2MrmStatusMap;
    }
}
