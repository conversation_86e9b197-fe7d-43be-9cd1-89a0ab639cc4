package com.sankuai.wallemonitor.risk.center.infra.exception;

import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;

/**
 * 熔断异常
 */
public class RhinoTimeExceedSystemException extends ErrorCodeException {

    private String extMsg;

    public RhinoTimeExceedSystemException(ResponseCodeEnum responseCode) {
        super(responseCode.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getMessage());
    }

    public RhinoTimeExceedSystemException(ResponseCodeEnum responseCode, String extMsg) {
        super(responseCode.getCode(), extMsg);
        this.extMsg = extMsg;
    }

    public RhinoTimeExceedSystemException(String message) {
        super(ResponseCodeEnum.SYSTEM_ERROR.getCode(), message);
        this.extMsg = message;
    }

    public String getExtMsg() {
        return extMsg;
    }

    public void setExtMsg(String extMsg) {
        this.extMsg = extMsg;
    }
}
