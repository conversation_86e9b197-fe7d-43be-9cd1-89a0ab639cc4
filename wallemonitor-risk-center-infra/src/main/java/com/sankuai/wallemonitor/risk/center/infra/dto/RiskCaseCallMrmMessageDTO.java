package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseCallMrmMessageDTO {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 消息key（唯一标识）
     */
    private String key;

    /**
     * 开始时间
     */
    @JsonProperty("start_timestamp")
    private Long startTimestamp;

    /**
     * 上报时间
     */
    @JsonProperty("alarm_timestamp")
    private Long alarmTimestamp;

    /**
     * 问题code
     */
    @JsonProperty("issue_code")
    private Integer issueCode;
}
