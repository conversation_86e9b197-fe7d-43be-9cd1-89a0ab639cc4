package com.sankuai.wallemonitor.risk.center.infra.utils.compare;

import com.sankuai.wallemonitor.risk.center.infra.utils.compare.RecordCompareUtils.EntityChangeType;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.Builder;
import lombok.Data;

@Data
public class EntityChangeRecordDTO extends EntityBaseChangeRecordDTO {


    /**
     * 具体变更的map,key为具体的列名
     */
    private Map<String, EntityFieldChangeRecordDTO> changeMaps;

    /**
     * 新增、修改、删除
     */
    private EntityChangeType entityChangeType;

    /**
     * 额外字段
     */
    private Map<String, String> extInfo;

    /**
     * 变更前的实体对象
     */
    private Object beforeEntity;

    /**
     * 变更后的实体对象
     */
    private Object afterEntity;

    @Builder(toBuilder = true)
    public EntityChangeRecordDTO(String entityId, Date updateTime,
            List<EntityFieldChangeRecordDTO> changes, Map<String, EntityFieldChangeRecordDTO> changeMaps,
            Map<String, String> extInfo) {
        super(entityId, updateTime, changes);
        this.changeMaps = changeMaps;
        this.extInfo = extInfo;
    }

    /**
     * 获取字段变更
     *
     * @return
     */
    public EntityFieldChangeRecordDTO getFieldChange(String fieldName) {
        return Optional.ofNullable(changeMaps).map(map -> map.get(fieldName)).orElse(null);
    }
}
