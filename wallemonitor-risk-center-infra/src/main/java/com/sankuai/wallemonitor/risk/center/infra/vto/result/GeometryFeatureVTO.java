package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.GeoElementTypeKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapElementTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

@Slf4j
public class GeometryFeatureVTO {

    /**
     * lane或者object的id
     */
    private String id;
    /***
     * feature 或者 collection
     */
    private String type;
    /**
     * 具体的geometry信息
     */
    private GeometryDataVTO geometry;
    /**
     * 一些属性值
     */
    private Map<String, Object> properties;


    /**
     * 获取类型
     * 
     * @return
     */
    public String getElementType(String map) {
        HdMapEnum hdMapEnum = HdMapEnum.fromValue(map);
        if (hdMapEnum == null) {
            return null;
        }
        switch (hdMapEnum) {
            case OBJECT:
                // 驼峰转下划线，并大写
                return org.apache.commons.lang3.StringUtils
                        .upperCase(StringUtils

                                .camelToUnderline(String.valueOf(properties.get(GeoElementTypeKeyConstant.O_TYPE))));
            case LANE_POLYGON:
            case LANE:
                // 如果是lane或者lanePolygon,直接获取即可
                return String.valueOf(properties.get(GeoElementTypeKeyConstant.LANE_TYPE));
            case JUNCTION: {
                // 取id j_8fed2dac98a2
                return HdMapElementTypeEnum.JUNCTION.getValue();
            }
            default:
                return CharConstant.CHAR_EMPTY;
        }
    }

    /**
     * 获取道路的区域类型
     *
     * @return
     */
    public String getLaneAreaType(String map) {
        if (!HdMapEnum.LANE.getValue().equals(map)) {
            return CharConstant.CHAR_EMPTY;
        }
        return String.valueOf(properties.get(GeoElementTypeKeyConstant.LANE_AREA_TYPE));
    }

    /**
     * 获取多边形
     * 
     * @return
     */
    public List<PolygonDO> toPolygon() {
        if (geometry == null) {
            return new ArrayList<>();
        }
        return geometry.getPolygon(id);
    }

    /**
     * 获取线类型
     * 
     * @return
     */
    public List<PositionDO> toLineList() {
        return getGeometry().getLineList();
    }

    public void put(String key, String value) {
        if (properties == null) {
            properties = new HashMap<>();
        }
        properties.put(key, value);
    }
}
