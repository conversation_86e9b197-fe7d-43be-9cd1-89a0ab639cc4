package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeConstant;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskAutoCheckConfigDTO {


    /**
     * action的检查分类配置
     */
    @Default
    private Map<String, RiskCheckCategoryConfigDTO> checkCategoryConfig = new HashMap<>();

    /**
     * action的检查分类配置
     */
    @Default
    private Map<String, RiskCheckFilterConfigDTO> checkFilterConfig = new HashMap<>();

    /**
     * 常规配置
     */
    @Default
    private Map<String, Map<String, Object>> actionCommonConfig = new HashMap<>();

    /**
     * 调用链
     */
    @Default
    private Map<String, String> actionChain = new HashMap<>();

    /**
     * 有风险case集合重检category
     */
    @Default
    private Map<String, Set<String>> dynamicReCheckCategory = new HashMap<>();

    /**
     * 最大检查轮次
     */
    @Default
    private Integer maxCheckRound = 5;

    // 5L
    private static final Long DEFAULT_DELAY_TIME = 5L;

    /**
     * 轮次间隔秒数
     */
    @Default
    private Integer roundIntervalSeconds = 30;

    /**
     * 跳过本轮检测的速度阈值（起步中，当前不检查）
     */
    @Default
    private Double skipSpeedThreshold = 999999.0;

    /**
     * 轮次间隔秒数
     */
    @Default
    private Map<ISCheckCategoryEnum, Long> categoryDynamicDelayTime = new HashMap<>();

    /**
     * 轮次间隔秒数
     */
    @Default
    private Map<String, Map<String, Integer>> categoryDynamicDelayRule = new HashMap<>();

    @Default
    private List<String> markTimePeriodList = new ArrayList<>();

    @Default
    private Map<String, String> riskReDistribution = new HashMap<>();

    @Default
    private List<String> noNeedToMarkRuleList = new ArrayList<>();

    @Default
    private Boolean useDelayRecallArea = false;

    @Default
    private Integer maxDelayRound = 16;

    /**
     * 是否在标注时间内
     *
     * @return
     */
    public boolean isInMarkTime() {
        LocalTime now = LocalTime.now();
        return markTimePeriodList.stream().anyMatch(timePeriodPair -> {
            try {
                List<String> timePair = Arrays.asList(StringUtils.split(timePeriodPair, '-'));
                if (CollectionUtils.isEmpty(timePair) || timePair.size() != 2) {
                    return false;
                }
                String startStr = timePair.get(0);
                String endStr = timePair.get(1);
                // 如果配成 00:00:00 - 24:00:00 ，认为全时段均可匹配
                if (DateTimeConstant.ONE_DAY_START.equals(startStr) &&
                        DateTimeConstant.ONE_DAY_END.equals(endStr)) {
                    return true;
                }
                LocalTime start = LocalTime.parse(startStr, DateTimeFormatter.ofPattern("HH:mm:ss"));
                LocalTime end = LocalTime.parse(endStr, DateTimeFormatter.ofPattern("HH:mm:ss"));
                return now.isAfter(start) && now.isBefore(end);
            } catch (Exception e) {
                return false;
            }
        });
    }

    public boolean isDynamicReCheckCategory(String action, ISCheckCategoryEnum category) {
        if (Objects.isNull(category)) {
            return false;
        }
        if (MapUtils.isEmpty(dynamicReCheckCategory)) {
            return false;
        }
        return dynamicReCheckCategory.getOrDefault(action, new HashSet<>()).contains(category.name());
    }


    /**
     * 根据车辆的上下文信息，获取一个delay数据
     *
     * @param category
     * @return
     */
    public Long getDynamicCheckDelayTime(String checkAction, ISCheckCategoryEnum category,
            VehicleRuntimeInfoContextDO contextDO) {
        if (Objects.isNull(category)) {
            return DEFAULT_DELAY_TIME;
        }
        String key = checkAction + CharConstant.CHAR_JH + category.name();
        Map<String, Integer> rule2DelayTime = categoryDynamicDelayRule.get(key);
        if (MapUtils.isEmpty(rule2DelayTime)) {
            return DEFAULT_DELAY_TIME;
        }

        return rule2DelayTime.keySet().stream().filter(rule -> {
            // 取满足要求的
            Map<String, Object> ruleContextMap = new HashMap<>();
            ruleContextMap.put("context", contextDO);
            return SpELUtil.evaluateBoolean(rule, ruleContextMap);
        }).findFirst().map(rule2DelayTime::get).map(Long::valueOf).orElse(DEFAULT_DELAY_TIME);
    }

    /**
     * 是否需要标注
     * 必须不满足任何一个标注
     * 
     * @param eveInfoVTO
     * @param checkingQueueItemDO
     * @return
     */
    public boolean needToMark(VehicleEveInfoVTO eveInfoVTO, RiskCheckingQueueItemDO checkingQueueItemDO) {
        if (CollectionUtils.isEmpty(noNeedToMarkRuleList)) {
            return true;
        }
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put("eveInfo", eveInfoVTO);
        contextMap.put("item", checkingQueueItemDO);
        return noNeedToMarkRuleList.stream().noneMatch(rule -> SpELUtil.evaluateBoolean(rule, contextMap));
    }
}
