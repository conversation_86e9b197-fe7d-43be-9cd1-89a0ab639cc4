package com.sankuai.wallemonitor.risk.center.infra.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SeatInterventionConfigDTO {

    /**
     * 是否启用
     */
    private boolean enable;

    /**
     * 检索距离
     */
    @Default
    private Double searchRange = 5D;

    /**
     * 向前检索的时间范围（秒）
     */
    @Default
    private Integer beforeSeconds = -1;

    /**
     * 有接管时风险召回
     */
    private boolean recallWhenIntervention;

}
