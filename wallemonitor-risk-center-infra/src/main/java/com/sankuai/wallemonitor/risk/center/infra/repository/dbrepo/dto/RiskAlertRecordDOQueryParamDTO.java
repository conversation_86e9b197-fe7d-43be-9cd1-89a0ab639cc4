package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.Like;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.enums.LikeTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 风险告警记录查询参数DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskAlertRecordDOQueryParamDTO {

    /**
     * 告警策略
     */
    @Like(value = LikeTypeEnum.ALL, field = "alertPolicy")
    private String alertPolicy;

    /**
     * 配置名列表
     */
    @InQuery(field = "configName")
    private List<String> configNameList;

    /**
     * 配置名模糊查询
     */
    @Like(value = LikeTypeEnum.ALL, field = "configName")
    private String configNameLike;

    /**
     * 大象消息请求Id列表
     */
    @InQuery(field = "bizId")
    private List<String> bizIdList;

    /**
     * 大象消息Id列表
     */
    @InQuery(field = "messageId")
    private List<String> messageIdList;

    /**
     * 大象群组列表
     */
    @InQuery(field = "groupId")
    private List<String> groupIdList;

    /**
     * 操作类型列表：-1-无需处理，0-未处理，10-处理中，20-已处理
     */
    @InQuery(field = "status")
    private List<Integer> statusList;

    /**
     * 标注类型列表：-1-无用，0-标注，1-有用
     */
    @InQuery(field = "label")
    private List<Integer> labelList;

    /**
     * 升级状态列表：0-无需升级，1-需要升级，2-完成升级
     */
    @InQuery(field = "upgradeStatus")
    private List<Integer> upgradeStatusList;

    /**
     * 升级消息ID列表
     */
    @InQuery(field = "upgradeMessageId")
    private List<String> upgradeMessageIdList;

    /**
     * 操作人列表
     */
    @InQuery(field = "operator")
    private List<String> operatorList;

    /**
     * 操作人模糊查询
     */
    @Like(value = LikeTypeEnum.ALL, field = "operator")
    private String operatorLike;

    /**
     * 创建时间范围
     */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    /**
     * 更新时间范围
     */
    @RangeQuery(field = "updateTime")
    private TimePeriod updateTimeRange;

    /**
     * 排序方式
     */
    @OrderBy(field = "createTime")
    @Builder.Default
    private OrderEnum orderByCreateTime = OrderEnum.DESC;
} 