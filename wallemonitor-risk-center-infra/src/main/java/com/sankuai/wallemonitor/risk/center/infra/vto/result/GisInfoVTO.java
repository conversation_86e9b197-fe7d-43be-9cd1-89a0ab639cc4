package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class GisInfoVTO {

    /**
     * 最靠近的poi
     */
    private String poi;

    /**
     * 城市
     */
    private String city;

    /**
     * 行政区
     */
    private String area;

    /**
     * 维度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 坐标系系统
     */
    private CoordinateSystemEnum coordinateSystem;
}
