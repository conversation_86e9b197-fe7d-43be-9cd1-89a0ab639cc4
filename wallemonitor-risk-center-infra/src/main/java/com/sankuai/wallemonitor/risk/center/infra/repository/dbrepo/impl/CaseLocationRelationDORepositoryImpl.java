package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.CaseLocationRelationConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.CaseLocationRelationMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseLocationRelation;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseLocationRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseLocationRelationRepository;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CaseLocationRelationDORepositoryImpl extends
        AbstractMapperSingleRepository<CaseLocationRelationMapper, CaseLocationRelationConvert, CaseLocationRelation, CaseLocationRelationDO>
        implements CaseLocationRelationRepository {

    private static final String KEY_CASE_ID = "caseId";

    @Override
    @RepositoryQuery
    public List<CaseLocationRelationDO> queryByParam(CaseLocationRelationDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    @Override
    public Paging<CaseLocationRelationDO> queryByParamByPage(CaseLocationRelationDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    @Override
    @RepositoryQuery
    public CaseLocationRelationDO getByVin(String vin) {
        return super.getByUniqueId(KEY_CASE_ID, vin);
    }

    @Override
    @RepositoryExecute
    public void save(CaseLocationRelationDO CaseLocationRelationDO) {
        super.save(CaseLocationRelationDO);
    }

    @Override
    @RepositoryExecute
    public void batchSave(List<CaseLocationRelationDO> CaseLocationRelationDOList) {
        super.batchSave(CaseLocationRelationDOList);
    }
}
