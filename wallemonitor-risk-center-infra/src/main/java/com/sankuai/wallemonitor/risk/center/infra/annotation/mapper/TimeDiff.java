package com.sankuai.wallemonitor.risk.center.infra.annotation.mapper;

import com.sankuai.wallemonitor.risk.center.infra.enums.CompareEnum;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TimeDiff {

    TimeUnit unit();

    String bigField();

    String smallField();

    CompareEnum compare();
}

