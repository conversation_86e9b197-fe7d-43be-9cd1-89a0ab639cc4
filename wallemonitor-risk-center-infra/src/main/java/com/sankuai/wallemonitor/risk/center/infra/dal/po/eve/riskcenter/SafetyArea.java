package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 停车区域范围数据表
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safety_area")
public class SafetyArea implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Long id;

    /**
     * 区域ID
     */
    @TableField("area_id")
    @TableUnique
    private String areaId;

    /**
     * 数据来源, 0-区域适配
     */
    @TableField("source")
    private Integer source;

    /**
     * 区域范围，JSON
     */
    @TableField("polygon")
    private String polygon;

    /**
     * 描述，比如: 在菜站门口增加窄路通行策略区域
     */
    @TableField("description")
    private String description;

    /**
     * 其它信息
     */
    @TableField("ext_info")
    private String extInfo;


    /**
     * 类型
     */
    @TableField("type")
    private String type;


    /**
     * 开始时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;


}
