package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.BelowTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCheckQueueQueryParam {


    /**
     * 车辆VIN码
     */
    @InQuery(field = "vin")
    private List<String> vinList;

    /**
     * 临时风险caseID
     */
    @InQuery(field = "tmpCaseId")
    private List<String> tmpCaseIdList;

    /**
     * 上游输入事件ID
     */
    private String eventId;

    /**
     * 上游输入事件ID
     */
    @InQuery(field = "eventId")
    private List<String> eventIdList;

    /**
     * 风险事件类型
     */
    @InQuery(field = "type")
    private List<Integer> typeList;

    /**
     * 风险事件来源
     */
    @InQuery(field = "source")
    private List<Integer> sourceList;

    /**
     * 开始时间范围查询
     */
    @RangeQuery(field = "occurTime")
    private TimePeriod occurTimeRange;

    /**
     * 结束时间范围查询
     */
    @RangeQuery(field = "closeTime")
    private TimePeriod closeTimeRange;

    /**
     * 是否正在预检中
     */
    private Boolean checking;

    /**
     * 队列状态
     */
    @InQuery(field = "status")
    private List<Integer> statusList;

    /**
     * 创建时间范围查询
     */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    /**
     * 创建时间范围查询
     */
    @GreatTo(field = "createTime")
    private Date createTimeCreateThan;

    /**
     * 早于下一轮执行时间查询
     */
    @BelowTo(field = "nextRoundTime")
    private Date nextRoundTimeBelowTo;

    /**
     * 更新时间范围查询
     */
    @RangeQuery(field = "updateTime")
    private TimePeriod updateTimeRange;

    /**
     * 更新时间排序
     */
    @OrderBy(field = "updateTime")
    private OrderEnum orderByUpdateTime;

    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean isDeleted = false;


}
