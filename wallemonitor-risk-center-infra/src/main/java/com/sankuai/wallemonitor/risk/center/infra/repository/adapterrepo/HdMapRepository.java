package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo;

import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapLaneAreaTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskRestrictQueryDTO;
import java.util.List;

public interface HdMapRepository {



    List<HdMapElementGeoDO> getHdMapAreaDataWGS84(HdMapLaneAreaTypeEnum areaEnum, PositionDO position,
            String hdMapVersion,
            String vin, Double meter);

    /**
     * 是否在高精地图区域中
     *
     * @param areaTypeEnum
     * @param position
     * @param hdMapVersion
     * @param vin
     * @return
     */
    Boolean isInHdMapAreaWGS84(HdMapLaneAreaTypeEnum areaTypeEnum, PositionDO position, String hdMapVersion,
            String vin);

    /**
     * 是否在高精地图区域的一定范围内
     *
     * @param areaEnum
     * @param position
     * @param hdMapVersion
     * @param vin
     * @return
     */

    String isRestrictParkingByRemote(List<String> areaList, PositionDO position, String hdMapVersion, String vin,
            Double meter, Double expandMeter);

    /**
     * 是否在特定类型的区域中
     */
    String inRestrictParking(RiskRestrictQueryDTO queryDTO);
}
