package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public enum RiskQueueStatusEnum {
    VALIDATING(0, "验证中"),

    CONFIRMED_RISK(10, "已确认为风险"),

    CONFIRMED_TIMEOUT(20, "超时确认为风险"),

    CANCELLED(99, "已取消"),

    CANCELLED_TIMEOUT(100, "超时取消风险"),
    ;

    private final int code;
    private final String description;

    RiskQueueStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取确认为奉献的状态
     *
     * @return
     */
    public static Set<RiskQueueStatusEnum> listConfirmedStatus() {
        return new HashSet<>(Arrays.asList(CONFIRMED_RISK, CONFIRMED_TIMEOUT));
    }

    /**
     * 是否已确认为风险
     *
     * @return
     */
    public static Boolean isConfirmed(RiskQueueStatusEnum status) {
        return new HashSet<>(Arrays.asList(CONFIRMED_RISK, CONFIRMED_TIMEOUT)).contains(status);
    }

    /**
     * 是否为需要校验的状态
     *
     * @return
     */
    public static Set<RiskQueueStatusEnum> listNeedCheckingStatus() {
        return new HashSet<>(Arrays.asList(VALIDATING));
    }

    /**
     * 是否为已终结状态
     *
     * @param status
     * @return
     */
    public static Boolean isTerminatedStatus(RiskQueueStatusEnum status) {
        return new HashSet<>(Arrays.asList(CANCELLED, CANCELLED_TIMEOUT, CONFIRMED_RISK, CONFIRMED_TIMEOUT))
                .contains(status);
    }

    public static RiskQueueStatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RiskQueueStatusEnum status : RiskQueueStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
