package com.sankuai.wallemonitor.risk.center.infra.enums;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
public enum ConfirmType {
    NO_TIPS(1, "不需要提示"),
    //推荐使用这个
    TOAST(2, "toast提示"),
    DIALOG(3, "dialog提示"),
    HTML_TEXT(4, "html文本"),
    HTML_LINK(5, "html超链接"),
    ;

    private int value;
    private String meaning;

    ConfirmType(int value, String meaning) {
        this.value = value;
        this.meaning = meaning;
    }

    public int getValue() {
        return value;
    }
}