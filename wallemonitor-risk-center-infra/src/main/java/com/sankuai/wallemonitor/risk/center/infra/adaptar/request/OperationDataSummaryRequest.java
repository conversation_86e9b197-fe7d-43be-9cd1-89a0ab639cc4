package com.sankuai.wallemonitor.risk.center.infra.adaptar.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OperationDataSummaryRequest {

    /**
     * 模糊查询uuid
     */
    private String id;

    /**
     * 模糊查询name
     */
    private String name;

    /**
     * 指定地图
     */
    private String mapName;

    /**
     * 在第几页
     */
    private Integer pageIndex;

    /**
     * 每一页的个数
     */
    private Integer pageSize;

    /**
     * 特定operation_type下的operation_data筛选
     */
    private String operationType;

    /**
     * 在start_time之后进行更新的operation_data
     */
    private String startTime;

    /**
     * 在end_time之前进行更新的operation_data
     */
    private String endTime;

    /**
     * 特定同学提交的operation_data
     */
    private String submitter;

    /**
     * 上下线状态
     * UNKNOWN(0), CREATED(1), SUBMITTED(2), ONLINE(3), RETRACTED(4),
     * OFFLINE(5), TESTING(6), DELETED(8), UPDATED(9), APPROVE_SUBMITTED(10)
     */
    private Integer status;

    /**
     * 约车平台中的用车目的key值，多选按逗号分隔
     */
    private String carPurposes;

    /**
     * 是否附加content，返回为json格式的措施数据内容
     */
    @Builder.Default
    private Boolean ignoreContent = true;
}