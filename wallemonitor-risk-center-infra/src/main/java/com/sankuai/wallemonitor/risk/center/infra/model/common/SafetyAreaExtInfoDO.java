package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@Data
@NoArgsConstructor
public class SafetyAreaExtInfoDO {

    private String id;
    private Integer status;
    @JsonProperty("create_time")
    private String createTime;
    private String submitter;
    private String description;
    private String name;
    @JsonProperty("map_name")
    private String mapName;
    @JsonProperty("operation_type")
    private String operationType;
    @JsonProperty("mender_job_id")
    private Long menderJobId;
    private Content content;
    @JsonProperty("effect_hours")
    private Set<Integer> effectHours; // 区域生效时间

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class Content {
        private String name;
        private String type;
        private AdmapInfo admapInfo;
        private Polygon polygon;
    }

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class AdmapInfo {
        private String admapName;
        private String admapVersion;
        private String admapDir;
        private String hdmapVersion;
        private String ndtmapVersion;
        private String sepdemsVersion;
        private String sepmapsVersion;
    }

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class Polygon {

        @JsonProperty("point")
        private List<PointUtm> pointUtmList;
    }

    @Builder
    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static class PointUtm {
        private Double x;
        private Double y;
    }

}
