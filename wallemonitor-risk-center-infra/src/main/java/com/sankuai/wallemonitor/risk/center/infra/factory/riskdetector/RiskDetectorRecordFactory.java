package com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector;

import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDetectorRecordBaseDO;

/**
 * 风险检测器工厂类
 *
 * @param <T>
 */
public abstract class RiskDetectorRecordFactory<T extends RiskDetectorRecordBaseDO> {

    public abstract T init(DetectContextDTO detectContextDTO);

}