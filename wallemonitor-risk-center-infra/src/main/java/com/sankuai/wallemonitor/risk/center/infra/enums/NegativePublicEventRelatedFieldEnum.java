package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum NegativePublicEventRelatedFieldEnum {
    RELATED_FILE_LINKS("relatedFileLinks", "关联的文件链接"),
    CONDITION_DESC_RELATED_FILE_LINKS("conditionDescRelatedFileLink", "情况说明关联附件"),
    RELATED_VINS("relatedVins", "关联的车架号列表"),
    HANDLERS("handlers", "关联处置人"),
    SOURCE("source", "事件来源"),
    REASON("reason", "问题归因"),
    HANDLE_RESULT_DESC_FILE_LINK("handleResultDescFileLink", "处理结果说明附件");

    private final String name;
    private final String desc;
}
