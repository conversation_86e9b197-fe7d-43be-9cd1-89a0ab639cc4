package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiskDetectConfirmDTO {

    /**
     * 车辆上下文
     */
    private VehicleRuntimeInfoContextDO runtimeContext;

    /**
     * 车辆信息
     */
    private VehicleEveInfoVTO eveInfo;

    /**
     * 检测过程输出值
     */
    private Map<String, Object> detectProcessContext;

    /**
     * 禁停区域类型
     * 
     * @return
     */
    public String getRestrictedAreaType() {
        if (detectProcessContext == null) {
            return CharConstant.CHAR_EMPTY;
        }
        return (String)detectProcessContext.get("restrictedAreaType");

    }

}
