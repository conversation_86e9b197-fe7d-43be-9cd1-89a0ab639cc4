package com.sankuai.wallemonitor.risk.center.infra.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MafkaMessageConsumerConfigDTO {

    private String topicName;

    private String appKey;

    private String nameSpace;

    private String group;

    private boolean deadLetter;

    private long deadLetterDelayMills;
}
