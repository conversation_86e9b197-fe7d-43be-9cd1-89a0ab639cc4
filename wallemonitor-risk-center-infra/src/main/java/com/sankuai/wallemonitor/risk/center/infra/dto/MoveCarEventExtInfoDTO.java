package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分诊异常事件表扩展字段数据结构
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MoveCarEventExtInfoDTO {

    /**
     * 微信小程序用户唯一ID - 公众挪车事件使用
     */
    private String openId;

    /**
     * 车辆位置
     */
    private String carPosition;

    /**
     * 车辆位置经纬度
     */
    private String carPositionGps;

    /**
     * 挪车原因
     */
    private String moveCarReason;

    /**
     * 上报人手机号
     */
    private String phoneNumber;

    /**
     * 现场照片url
     */
    private List<String> urlList;

    /**
     * 车辆vhr信息
     */
    private String vhrDesc;

    /**
     * 云控安全员
     */
    private String cloudSecurity;
}
