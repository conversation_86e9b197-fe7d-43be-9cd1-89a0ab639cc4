package com.sankuai.wallemonitor.risk.center.infra.vto.param;

import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultMissCaseDetailDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultOverviewDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultSceneDataDTO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateExperimentalResultParamVTO {

    // 创建人empid, 查询：https://qabetter.it.test.sankuai.com/org/emp/sync
    private Integer operatorEmpId;

    // 标题
    private String title;

    // 数据概览
    private ExperimentalResultOverviewDTO overview;

    // 分场景数据
    private List<ExperimentalResultSceneDataDTO> sceneDataList;

    // 漏召事件列表详情
    private List<ExperimentalResultMissCaseDetailDTO> missCaseDetailList;
}
