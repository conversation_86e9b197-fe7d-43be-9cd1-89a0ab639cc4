package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseObstacleConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCaseObstacleMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseObstacle;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseObstacleDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseObstacleDORepository;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskCaseObstacleRepositoryImpl extends
        AbstractMapperSingleRepository<RiskCaseObstacleMapper, RiskCaseObstacleConvert, RiskCaseObstacle, RiskCaseObstacleDO>
        implements RiskCaseObstacleDORepository {

    private static final String KEY_CASE_ID = "caseId";

    @Override
    @RepositoryQuery
    public List<RiskCaseObstacleDO> queryByParam(RiskCaseObstacleDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    @Override
    public Paging<RiskCaseObstacleDO> queryByParamByPage(RiskCaseObstacleDOQueryParamDTO paramDTO, Integer pageNum,
            Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    @Override
    @RepositoryQuery
    public RiskCaseObstacleDO getByVin(String vin) {
        return super.getByUniqueId(KEY_CASE_ID, vin);
    }

    @Override
    @RepositoryExecute
    public void save(RiskCaseObstacleDO RiskCaseObstacleDO) {
        super.save(RiskCaseObstacleDO);
    }

    @Override
    @RepositoryExecute
    public void batchSave(List<RiskCaseObstacleDO> RiskCaseObstacleDOList) {
        super.batchSave(RiskCaseObstacleDOList);
    }
}
