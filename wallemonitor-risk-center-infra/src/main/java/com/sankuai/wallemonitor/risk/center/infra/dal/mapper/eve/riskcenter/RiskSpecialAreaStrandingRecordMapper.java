package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskSpecialAreaStrandingRecord;

/**
 * <p>
 * 特殊区域（路口施工区域）停滞预检过程表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-10-14
 */
public interface RiskSpecialAreaStrandingRecordMapper extends CommonMapper<RiskSpecialAreaStrandingRecord> {

    @Override
    default Class<RiskSpecialAreaStrandingRecord> getPOClass() {
        return RiskSpecialAreaStrandingRecord.class;
    }


}
