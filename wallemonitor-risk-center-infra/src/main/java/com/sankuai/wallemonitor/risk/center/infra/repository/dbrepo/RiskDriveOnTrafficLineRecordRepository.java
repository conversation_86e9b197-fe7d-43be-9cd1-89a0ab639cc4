package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskDriveOnTrafficLineRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskDriveOnTrafficLineRecordDOQueryParamDTO;
import java.util.List;

public interface RiskDriveOnTrafficLineRecordRepository {

    /**
     * 根据参数查询压线预检过程
     *
     * @param paramDTO 查询参数
     * @return 压线预检过程列表
     */
    List<RiskDriveOnTrafficLineRecordDO> queryByParam(RiskDriveOnTrafficLineRecordDOQueryParamDTO paramDTO);

    /**
     * 根据参数查询压线预检过程（分页）
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    Paging<RiskDriveOnTrafficLineRecordDO> queryByParamByPage(RiskDriveOnTrafficLineRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize);

    /**
     * 根据ID查询压线预检过程
     *
     * @param id 记录ID
     * @return 压线预检过程
     */
    RiskDriveOnTrafficLineRecordDO getById(Long id);

    /**
     * 保存压线预检过程
     *
     * @param riskDriveOnTrafficLineRecordDO 压线预检过程对象
     */
    void save(RiskDriveOnTrafficLineRecordDO riskDriveOnTrafficLineRecordDO);

    /**
     * 批量保存压线预检过程
     *
     * @param riskDriveOnTrafficLineRecordDOList 压线预检过程对象列表
     */
    void batchSave(List<RiskDriveOnTrafficLineRecordDO> riskDriveOnTrafficLineRecordDOList);
}