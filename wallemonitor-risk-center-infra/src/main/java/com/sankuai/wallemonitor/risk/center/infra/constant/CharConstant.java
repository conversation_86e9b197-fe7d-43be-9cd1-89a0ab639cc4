package com.sankuai.wallemonitor.risk.center.infra.constant;

/**
 * <AUTHOR>
 * @date 2023/6/9
 */
public class CharConstant {

    /**
     * 点号
     */
    public static final String CHAR_DH = ".";
    /**
     * 等于号
     */
    public static final String CHAR_DY = "=";
    /**
     * 下划线
     */
    public static final String CHAR_XH = "_";
    /**
     * 井号
     */
    public static final String CHAR_JH = "#";
    /**
     * 美元符号
     */
    public static final String CHAR_MY = "$";
    /**
     * 逗号
     */
    public static final String CHAR_DD = ",";
    /**
     * 中文顿号
     */
    public static final String CHAR_DT = "、";
    /**
     * 横线
     */
    public static final String CHAR_HX = "-";
    /**
     * 冒号
     */
    public static final String CHAR_MH = ":";
    /**
     * 斜线
     */
    public static final String CHAR_XX = "/";
    /**
     * 问号
     */
    public static final String CHAR_WH = "?";
    /**
     * 空格
     */
    public static final String CHAR_SPACE = " ";
    /**
     * 空字符
     */
    public static final String CHAR_EMPTY = "";

    /**
     * 波浪符
     */
    public static final String CHAR_BL = "~";
    public static final String CHAR_TRUE = "true";
    public static final String CHAR_FALSE = "false";

    /**
     * 竖线
     */
    public static final String CHAR_VERTICAL = "|";

    /**
     * 左圆括号
     */
    public static final String CHAR_LEFT_PARENTHESIS = "(";

    /**
     * 右圆括号
     */
    public static final String CHAR_RIGHT_PARENTHESIS = ")";

    /**
     * 逗号
     */
    public static final String CHAR_COMMA = ",";
    //"]"
    public static final String CHAR_RIGHT_BRACKET = "]";
    //"["
    public static final String CHAR_LEFT_BRACKET = "[";
    // %
    public static final String CHAR_PERCENT = "%";
}