package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class RiskRaptorReportConfigDTO {


    @Builder.Default
    private List<BusinessReporterConfigDTO> reporterKeyList = new ArrayList<>();


    /**
     * 是否被过滤
     *
     * @param caseDO
     * @return
     */
    public BusinessReporterConfigDTO matched(RiskCaseDO caseDO, RiskCaseVehicleRelationDO relationDO) {
        if (CollectionUtils.isEmpty(reporterKeyList) || caseDO == null || relationDO == null) {
            // 参数为空，不命中
            return null;
        }
        // 不能满足任意一个过滤条件，必须全部不满足
        return reporterKeyList.stream().filter(reporterConfig -> reporterConfig.aimed(caseDO, relationDO)).findFirst()
                .orElse(null);
    }

    /**
     * 不同指标的打点方式不同
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BusinessReporterConfigDTO {

        private String raptorBusinessType;

        private String raptorRule;

        @Builder.Default
        private Map<String, String> raptorTagRule = new HashMap<>();

        /**
         * 是否命中
         * 
         * @param riskCaseDO
         * @param relationDO
         * @return
         */
        public boolean aimed(RiskCaseDO riskCaseDO, RiskCaseVehicleRelationDO relationDO) {
            if (riskCaseDO == null || relationDO == null || StringUtils.isBlank(raptorRule)) {
                return false;
            }
            Map<String, Object> context = new HashMap<>();
            context.put("case", riskCaseDO);
            context.put("relation", relationDO);
            return SpELUtil.evaluateBoolean(raptorRule, context);
        }

        public Map<String, String> getRaptorTagValue(RiskCaseDO riskCaseDO,
                RiskCaseVehicleRelationDO riskCaseVehicleRelationDO) {
            Map<String, String> result = new HashMap<>();
            if (riskCaseDO == null || riskCaseVehicleRelationDO == null || MapUtils.isEmpty(raptorTagRule)) {
                return result;
            }
            raptorTagRule.forEach((raptorTag, raptorTagValueRule) -> {
                Map<String, Object> context = new HashMap<>();
                context.put("case", riskCaseDO);
                context.put("relation", riskCaseVehicleRelationDO);
                result.put(raptorTag, SpELUtil.evaluateWithVariables(raptorTagValueRule, context, String.class));
            });
            return result;
        }

    }
}
