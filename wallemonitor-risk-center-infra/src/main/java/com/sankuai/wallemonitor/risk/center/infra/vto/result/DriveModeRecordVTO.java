package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆驾驶状态记录查询结果
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DriveModeRecordVTO {

    private String vin;

    private List<DriveModeRecord> recordList;

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DriveModeRecord {

        /**
         * 切换时间
         */
        private Date changeTime;

        /**
         * 当前驾驶状态
         */
        private DriverModeEnum curMode;

        /**
         * 前一个驾驶状态
         */
        private DriverModeEnum preMode;
    }
}
