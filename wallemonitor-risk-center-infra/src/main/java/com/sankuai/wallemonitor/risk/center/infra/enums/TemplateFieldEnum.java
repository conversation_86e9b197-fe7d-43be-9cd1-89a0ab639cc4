package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模板字段枚举
 */
@Getter
@AllArgsConstructor
public enum TemplateFieldEnum {

    OCCUR_TIME("occurTime", "发生时间"),
    VEHICLE_NAME("vehicleName", "车辆名称"),
    PURPOSE("purpose", "用车目的"),
    POI_NAME("poiName", "POI"),
    PLACE_CODE("placeCode", "车辆场地"),
    PARKING_PLOT_NAME("parkingPlotName", "停车场"),
    OCCUR_TIME_IMG("occurTimeImg", "发生时间图片"),
    FEEDBACK("feedback", "反馈信息"),
    BUTTON("button", "按钮"),

    //自启动卡片字段
    SECURITY_OFFICER("securityOfficer", "安全员"),
    PROMPT("prompt", "提示词"),
    BUTTON_TEXT("buttonText", "按钮文字"),
    VEHICLE_NAME_V2("vehicleNameV2", "车辆名称"),
    MASTER("master", "组长");

    private final String code;

    private final String desc;

    public static TemplateFieldEnum getByCode(String code) {
        for (TemplateFieldEnum field : values()) {
            if (field.getCode().equals(code)) {
                return field;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的字段
     */
    public static boolean isValidField(String code) {
        return getByCode(code) != null;
    }
} 