package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户须知阅读记录查询
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserNoticeReadRecordDOQueryParamDTO {

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 是否删除,如果需要不关注删除状态时，需要修改改字段为NULL
     */
    @Builder.Default
    private Boolean isDelete = false;

    /**
     * 确认阅读
     */
    private Boolean confirm;

    /**
     * 版本ID排序
     */
    @OrderBy(field = "versionId")
    private OrderEnum orderByVersionId;

    /**
     * 版本ID
     */
    private Long versionId;

}
