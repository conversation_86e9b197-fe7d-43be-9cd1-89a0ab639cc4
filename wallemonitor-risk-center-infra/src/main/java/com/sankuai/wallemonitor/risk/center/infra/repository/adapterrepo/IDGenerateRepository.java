package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo;


import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import java.util.Date;
import java.util.List;

/**
 * id生成仓储
 *
 * <AUTHOR>
 * @date 2023/4/04
 */
public interface IDGenerateRepository {

    /**
     * 根据业务获取ID
     *
     * @param idBizEnum
     * @param vinList
     * @param source
     * @param type
     * @param timestamp
     * @return
     */
    String generateByKey(IDBizEnum idBizEnum, List<String> vinList, RiskCaseSourceEnum source, RiskCaseTypeEnum type,
            Long timestamp);


    /**
     * 根据业务获取ID
     *
     * @param idBizEnum
     * @return
     */
    String generateByKey(IDBizEnum idBizEnum);

    /**
     * 根据业务获取ID
     *
     * @param idBizEnum
     * @param vin
     * @param riskCaseSourceEnum
     * @param riskCaseTypeEnum
     * @param date
     * @return
     */
    String generateByKey(IDBizEnum idBizEnum, String vin, RiskCaseSourceEnum riskCaseSourceEnum,
            RiskCaseTypeEnum riskCaseTypeEnum, Date date);

    /**
     * 生成eventId
     *
     * @param eventTime
     * @param riskType
     * @param vehicleName
     * @return
     */
    String generateEventId(Date eventTime, String riskType, String vehicleName);

    /**
     * 转换eventId
     *
     * @param eventId
     * @param riskType
     * @return
     */
    String convertEventId(String eventId, RiskCaseTypeEnum riskType);

    /**
     * 生成通用业务ID
     *
     * @param idBizEnum 业务ID类型
     * @param prefix 业务前缀
     * @param timestamp 时间戳（可选，为null时使用当前时间）
     * @return 生成的业务ID
     */
    String generateBusinessId(IDBizEnum idBizEnum, String prefix, Long timestamp);
}
