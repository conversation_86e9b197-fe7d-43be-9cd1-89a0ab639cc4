package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 分拣数据表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("case_sort_data")
public class CaseSortData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * caseId
     */
    @TableField("case_id")
    @TableUnique
    private String caseId;

    /**
     * 所属问题类型
     */
    @TableField("problem")
    private String problem;

    /**
     * 分拣人
     */
    @TableField("sorter")
    private String sorter;

    /**
     * 拓展信息
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最近更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 描述信息
     */
    @TableField("description")
    private String description;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;
}
