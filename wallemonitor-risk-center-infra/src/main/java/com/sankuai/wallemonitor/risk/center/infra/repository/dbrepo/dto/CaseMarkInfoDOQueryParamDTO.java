package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.BelowTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;

import java.util.Date;
import java.util.List;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CaseMarkInfoDOQueryParamDTO {

    /**
     * 范围查询
     */
    @InQuery(field = "caseId")
    private List<String> caseIdList;

    /**
     * 创建时间范围
     * */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;



    /**
     * 标记结果查询
     * */
    @InQuery(field = "category")
    private List<String> categoryIn;

}
