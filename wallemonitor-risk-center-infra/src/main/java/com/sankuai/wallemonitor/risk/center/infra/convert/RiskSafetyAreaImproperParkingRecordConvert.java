package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskSafetyAreaImproperParkingRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskSafetyAreaImproperParkingRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskSafetyAreaImproperParkingRecordConvert extends
        SingleConvert<RiskSafetyAreaImproperParkingRecord, RiskSafetyAreaImproperParkingRecordDO> {

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusEnum")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnum")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseTypeEnum")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "parseVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "parseVehicleRuntimeInfoContextDO")
    RiskSafetyAreaImproperParkingRecordDO toDO(RiskSafetyAreaImproperParkingRecord riskSafetyAreaImproperParkingRecord);

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusInteger")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleted")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "serializeVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "serializeVehicleRuntimeInfoContextDO")
    RiskSafetyAreaImproperParkingRecord toPO(
            RiskSafetyAreaImproperParkingRecordDO riskSafetyAreaImproperParkingRecordDO);
}