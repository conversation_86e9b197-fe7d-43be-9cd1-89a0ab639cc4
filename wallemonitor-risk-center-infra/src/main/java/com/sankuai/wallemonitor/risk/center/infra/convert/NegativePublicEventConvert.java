package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEvent;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", imports = {EnumsConvertMapper.class}, uses = {EnumsConvertMapper.class})
public interface NegativePublicEventConvert extends SingleConvert<NegativePublicEvent, NegativePublicEventDO> {

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toNegativePublicEventStatusEnum")
    @Mapping(source = "type", target = "type", qualifiedByName = "toNegativePublicEventTypeEnum")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "parseNegativePublicEventExtInfo")
    NegativePublicEventDO toDO(NegativePublicEvent negativePublicEvent);

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toNegativePublicEventStatus")
    @Mapping(source = "type", target = "type", qualifiedByName = "toNegativePublicEventType")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "serializeNegativePublicEventExtInfo")
    NegativePublicEvent toPO(NegativePublicEventDO negativePublicEventDO);

}
