package com.sankuai.wallemonitor.risk.center.infra.model.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * sso的登录信息
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SSOLogInfoDO {

    private int id; //用户uid
    private String login; //mis号
    private String name; //姓名
    private String code; //员工号
    private String email; //邮箱
}
