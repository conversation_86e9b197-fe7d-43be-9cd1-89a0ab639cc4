package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.carosscan.request.AutoCallRequest;
import com.sankuai.carosscan.response.AutoCallVO;
import com.sankuai.carosscan.service.IThriftAutoCallService;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 自动外呼
 */
@Component
@Slf4j
public class AutoCallAdapter {

    /**
     * 外部调用服务
     */
    @ThriftClientProxy(remoteAppKey = AppKeyConstant.OUTPUT_APP_KEY)
    private IThriftAutoCallService iThriftAutoCallService;

    /**
     * 自动外呼
     *
     * @param businessCallId
     * @param mediaBody
     * @param misList
     */
    public void autoCall(String businessCallId, String mediaBody, List<String> misList) {

        try {
            AutoCallRequest request = AutoCallRequest.builder()
                    .businessCallId(businessCallId)
                    .mediaBody(mediaBody)
                    .category("PUBLIC_EVENT")
                    .callMisList(misList)
                    .build();
            // log.info("autoCall request:{}", request);
            EveThriftResponse<AutoCallVO> response = iThriftAutoCallService.autoCall(request);
            // log.info("autoCall response:{}", response);
            if (Objects.isNull(response) || !Objects.equals(response.getCode(), 0)) {
                throw new RuntimeException("autoCall failed");
            }
        } catch (Exception e) {
            log.error("autoCall error", e);
        }

    }
}
