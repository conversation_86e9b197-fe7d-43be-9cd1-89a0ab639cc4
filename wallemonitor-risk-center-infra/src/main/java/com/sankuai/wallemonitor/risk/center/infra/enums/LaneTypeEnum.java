package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum LaneTypeEnum {
    /**
     * 单车道（包含单机动车道 + 辅路）
     */
    SINGLE_LANE(1, "单车道"),

    /**
     * 多车道
     */
    MULTI_LANE(2, "多车道"),

    /**
     * 未知
     */
    UNKNOWN(-1, "未知");

    private Integer code;
    private String desc;
}
