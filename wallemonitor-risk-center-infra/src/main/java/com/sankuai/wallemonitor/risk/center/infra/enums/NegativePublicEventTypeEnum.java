package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 负外部性事件类型枚举
 */
@AllArgsConstructor
@Getter
public enum NegativePublicEventTypeEnum {
    DEFAULT(0, "默认"),
    COMPLAINT(1, "投诉"),
    PUBLIC_OPINION(2, "舆情"),
    ALL(3, "叠加");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取对应的NegativePublicEventTypeEnum枚举值
     *
     * @param code
     * @return
     */
    public static NegativePublicEventTypeEnum fromCode(Integer code) {
        for (NegativePublicEventTypeEnum type : NegativePublicEventTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

}
