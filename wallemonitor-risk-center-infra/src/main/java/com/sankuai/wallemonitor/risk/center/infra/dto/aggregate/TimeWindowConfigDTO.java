package com.sankuai.wallemonitor.risk.center.infra.dto.aggregate;

import com.sankuai.wallemonitor.risk.center.infra.enums.TimeWindowUnitEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 时间窗口配置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimeWindowConfigDTO {

    /**
     * 时间窗口大小
     */
    private Integer windowSize;

    /**
     * 时间单位
     */
    private TimeWindowUnitEnum windowUnit;

    /**
     * 校验配置是否有效
     */
    public boolean isValid() {
        return windowSize != null && windowSize > 0 && windowUnit != null;
    }

    /**
     * 获取时间窗口的秒数
     */
    public long getWindowSeconds() {
        if (!isValid()) {
            return 0;
        }
        return windowUnit.toSeconds(windowSize);
    }
} 