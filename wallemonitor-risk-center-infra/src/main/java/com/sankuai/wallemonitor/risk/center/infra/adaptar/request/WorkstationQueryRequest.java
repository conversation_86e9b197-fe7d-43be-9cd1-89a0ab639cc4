package com.sankuai.wallemonitor.risk.center.infra.adaptar.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkstationQueryRequest {

    /**
     * 查询条件
     */
    @JsonProperty("query")
    private List<Object> queryCondition;

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RangeQueryCondition {

        /**
         * 类型
         */
        @Builder.Default
        private String type = "RANGE";

        /**
         * 字段名称
         */
        private String field;

        /**
         * 字段类型
         */
        @Builder.Default
        private String fieldType = "datetime";

        /**
         * 大于等于
         */
        private String gte;

        /**
         * 小于等于
         */
        private String lte;

        /**
         * TODO: 确定该字段的含义
         */
        private Integer group;

    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TermQueryCondition<T> {

        /**
         * 类型
         */
        @Builder.Default
        private String type = "TERM";

        /**
         * 字段名称
         */
        private String field;
        
        /**
         * 取值
         */
        private T value;


        /**
         * TODO: 确定该字段的含义
         */
        private Integer group;

    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TermsQueryCondition<T> {

        /**
         * 类型
         */
        @Builder.Default
        private String type = "TERMS";

        /**
         * 字段名称
         */
        private String field;

        /**
         * 字段类型
         */
        @Builder.Default
        private String fieldType = "array";

        /**
         * 取值
         */
        private List<T> value;


        /**
         * TODO: 确定该字段的含义
         */
        private Integer group;

    }

}
