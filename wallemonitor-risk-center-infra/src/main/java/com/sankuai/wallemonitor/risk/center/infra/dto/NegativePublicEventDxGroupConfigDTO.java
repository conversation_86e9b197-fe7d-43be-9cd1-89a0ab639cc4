package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NegativePublicEventDxGroupConfigDTO {

    /**
     * 群成员列表
     */
    private List<String> groupMemberList;

    /**
     * 群机器人管理列表
     */
    private List<Long> botAdmins;

    /**
     * 群主
     */
    private String owner;

    /**
     * 负外部性工单值班配置
     */
    private List<TTRGidConfigDTO> ttRGidConfigDTOList;

    /**
     * 投诉工单地址
     */
    private String negativePublicEventUrl;
}
