package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCaseMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCase;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 风险事件仓储实现
 */
@Component
@Slf4j
public class RiskCaseRepositoryImpl extends
        AbstractMapperSingleRepository<RiskCaseMapper, RiskCaseConvert, RiskCase, RiskCaseDO> implements
        RiskCaseRepository {

    private static final String UK_RISK_CASE_ID = "caseId";


    private static final String UK_EVENT_ID = "eventId";

    /**
     * 根据参数查询风险事件
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskCaseDO> queryByParam(RiskCaseDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据事件id获取风险
     *
     * @param eventId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskCaseDO getByEventId(String eventId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_EVENT_ID)
                .value(eventId)
                .build()));
    }

    /**
     * 根据参数查询风险事件 (分页)
     *
     * @param paramDTO
     * @return
     */
    @Override
    public Paging<RiskCaseDO> queryByParamByPage(RiskCaseDOQueryParamDTO paramDTO, Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据风险id查询风险事件
     *
     * @param caseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskCaseDO getByCaseId(String caseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_RISK_CASE_ID)
                .value(caseId)
                .build()));
    }

    /**
     * 保存风险事件
     *
     * @param riskCaseDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskCaseDO riskCaseDO) {
        super.save(riskCaseDO);

    }

    /**
     * 批量保存风险事件
     *
     * @param riskCaseDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskCaseDO> riskCaseDOList) {
        super.batchSave(riskCaseDOList);
    }
}
