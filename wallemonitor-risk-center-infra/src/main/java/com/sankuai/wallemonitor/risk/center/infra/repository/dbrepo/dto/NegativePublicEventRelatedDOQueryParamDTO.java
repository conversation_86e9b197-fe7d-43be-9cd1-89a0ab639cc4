package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class NegativePublicEventRelatedDOQueryParamDTO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 查询指定的同名字段
     */
    private String eventId;

    /**
     * 事件ID范围查询
     */
    @InQuery(field = "eventId")
    private List<String> eventIdList;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段名称范围查询
     */
    @InQuery(field = "fieldName")
    private List<String> fieldNameList;

    /**
     * 字段取值
     */
    private String fieldValue;

    /**
     * 字段取值范围查询
     */
    @InQuery(field = "fieldValue")
    private List<String> fieldValueList;

    /**
     * 是否删除,如果需要不关注删除状态时，需要修改改字段为NULL
     */
    @Builder.Default
    private Boolean isDeleted = false;
}
