package com.sankuai.wallemonitor.risk.center.infra.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 替换的方法
 */
public class StringMessageFormatter {

    public static String replaceMsg(String msg, Object... replace) {
        String str = msg;
        if (StringUtils.isNotBlank(str) && replace != null && replace.length > 0) {
            for (Object temp : replace) {
                str = str.replaceFirst("\\{}", "" + temp);
            }
        }
        return str;
    }
}
