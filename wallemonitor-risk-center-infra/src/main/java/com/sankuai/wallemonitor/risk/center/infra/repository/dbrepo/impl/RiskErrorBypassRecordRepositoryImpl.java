package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskErrorBypassRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskErrorBypassRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskErrorBypassRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskErrorBypassRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskErrorBypassRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskErrorBypassRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 错误绕行检测记录仓储实现
 */
@Component
@Slf4j
public class RiskErrorBypassRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskErrorBypassRecordMapper, RiskErrorBypassRecordConvert, RiskErrorBypassRecord, RiskErrorBypassRecordDO> implements
        RiskErrorBypassRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询错误绕行检测记录
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskErrorBypassRecordDO> queryByParam(RiskErrorBypassRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询错误绕行检测记录 (分页)
     *
     * @param paramDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public Paging<RiskErrorBypassRecordDO> queryByParamByPage(RiskErrorBypassRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据临时事件ID查询错误绕行检测记录
     *
     * @param tmpCaseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskErrorBypassRecordDO getByTmpCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }

    /**
     * 保存错误绕行检测记录
     *
     * @param riskErrorBypassRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskErrorBypassRecordDO riskErrorBypassRecordDO) {
        super.save(riskErrorBypassRecordDO);
    }

    /**
     * 批量保存错误绕行检测记录
     *
     * @param riskErrorBypassRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskErrorBypassRecordDO> riskErrorBypassRecordDOList) {
        super.batchSave(riskErrorBypassRecordDOList);
    }
} 