package com.sankuai.wallemonitor.risk.center.infra.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum HandleTypeEnum {
    UNKNOWN(0, "UNKNOWN"),
    MRM(1, "MRM"),
    RESCUE(2, "RESCUE");

    private final int code;
    private final String description;


    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static HandleTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (HandleTypeEnum type : HandleTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;

    }
}