package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseRelatedServiceRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskCaseRelatedServiceRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseRelatedServiceRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseRelatedServiceRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRelatedServiceRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseRelatedServiceRecordDOQueryParamDTO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RiskCaseRelatedServiceRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskCaseRelatedServiceRecordMapper, RiskCaseRelatedServiceRecordConvert, RiskCaseRelatedServiceRecord, RiskCaseRelatedServiceRecordDO> implements
        RiskCaseRelatedServiceRecordRepository {

    @Override
    @RepositoryExecute
    public void save(RiskCaseRelatedServiceRecordDO riskCaseRelatedServiceRecordDO) {
        super.save(riskCaseRelatedServiceRecordDO);
    }

    @Override
    @RepositoryExecute
    public void batchSave(List<RiskCaseRelatedServiceRecordDO> riskCaseRelatedServiceRecordDOList) {
        super.batchSave(riskCaseRelatedServiceRecordDOList);
    }

    @Override
    @RepositoryQuery
    public List<RiskCaseRelatedServiceRecordDO> queryByParam(RiskCaseRelatedServiceRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据caseId列表和serviceName列表查询关联的工作台caseId列表
     *
     * @param caseIdList
     * @param serviceNameList
     * @return
     */
    public Map<String, Set<String>> queryCaseId2WorkstationCaseId(List<String> caseIdList,
            List<String> serviceNameList) {
        RiskCaseRelatedServiceRecordDOQueryParamDTO paramDTO = RiskCaseRelatedServiceRecordDOQueryParamDTO.builder()
                .caseIdList(caseIdList)
                .serviceNameList(serviceNameList)
                .build();
        List<RiskCaseRelatedServiceRecordDO> riskCaseRelatedServiceRecordDOList = queryByParam(paramDTO);
        if (CollectionUtils.isEmpty(riskCaseRelatedServiceRecordDOList)) {
            return new HashMap<>();
        }
        return riskCaseRelatedServiceRecordDOList.stream()
                .collect(Collectors.groupingBy(
                        RiskCaseRelatedServiceRecordDO::getCaseId,
                        Collectors.mapping(RiskCaseRelatedServiceRecordDO::getRelatedId, Collectors.toSet())
                ));
    }
}
