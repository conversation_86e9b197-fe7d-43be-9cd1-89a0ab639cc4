package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.sankuai.inf.kms.pangolin.api.model.EncryptionRequest;
import com.sankuai.inf.kms.pangolin.api.service.EncryptServiceFactory;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 手机号加解密工具类
 */
@Component
@Slf4j
public class PhoneNumberEncryptUtil {

    private static IEncryptService phoneEncryptService;

    /**
     * kms平台加解密key
     */
    private static final String ENCRYPT_KEY = "phone";

    @Value("${app.name}")
    private String appName;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        phoneEncryptService = EncryptServiceFactory.create(EncryptionRequest.Builder.anEncryptionRequest().withNamespace(appName).withKeyName(
                ENCRYPT_KEY).build());
    }

    /**
     * 加密电话号码
     *
     * @param plainPhoneNum 电话号码原文
     * @return 密文
     */
    public static String encryptPhone(String plainPhoneNum) {
        if (StringUtils.isBlank(plainPhoneNum)) {
            return plainPhoneNum;
        }
        try {
            return phoneEncryptService.encryptUTF8String(plainPhoneNum);
        } catch (Exception exception) {
            log.error("fail in encryptPhone with exception!", exception);
            return plainPhoneNum;
        }
    }

    /**
     * 解密电话号码
     *
     * @param cipherPhoneNum 电话号码密文
     * @return 原文
     */
    public static String decryptPhone(String cipherPhoneNum) {
        if (StringUtils.isBlank(cipherPhoneNum)) {
            return cipherPhoneNum;
        }

        try {
            return phoneEncryptService.decryptUTF8String(cipherPhoneNum);
        } catch (Exception exception) {
            log.error("fail in encryptPhone with exception!", exception);
            return cipherPhoneNum;
        }
    }
}
