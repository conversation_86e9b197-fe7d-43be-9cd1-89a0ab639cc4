package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.CaseMarkInfoConverter;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.CaseMarkInfoMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseMarkInfo;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.CaseMarkInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.CaseMarkInfoDOQueryParamDTO;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Component
@Slf4j
public class CaseMarkInfoRepositoryImpl extends
        AbstractMapperSingleRepository<CaseMarkInfoMapper, CaseMarkInfoConverter, CaseMarkInfo, CaseMarkInfoDO> implements
        CaseMarkInfoRepository {

    private static final String UK_RISK_CASE_ID = "caseId";

    /**
     * 根据参数查询风险事件
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<CaseMarkInfoDO> queryByParam(CaseMarkInfoDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    @Override
    public Map<String, CaseMarkInfoDO> queryMapByParam(CaseMarkInfoDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO).stream().collect(
                Collectors.toMap(CaseMarkInfoDO::getCaseId, Function.identity(), (c1, c2) -> c1));
    }


    @Override
    @RepositoryQuery
    public CaseMarkInfoDO getByCaseId(String caseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_RISK_CASE_ID)
                .value(caseId)
                .build()));
    }
}
