package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.constant.VehicleEventCodeConstant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RiskCaseTypeEnum {

    UNKNOWN(0, "默认/未知异常",
            ImmutableSet.of()),
    VEHICLE_STAND_STILL(1, "停滞不当",
            ImmutableSet.of(VehicleEventCodeConstant.STAND_STILL_START, VehicleEventCodeConstant.STAND_STILL_END)),
    VEHICLE_SIDE_BY_SIDE(2, "车辆并排",
            ImmutableSet.of(VehicleEventCodeConstant.SIDE_BY_SIDE_START, VehicleEventCodeConstant.SIDE_BY_SIDE_END)),
    VEHICLE_CONGESTION(3, "车辆扎堆",
            ImmutableSet.of(VehicleEventCodeConstant.TRAFFIC_JAM_START, VehicleEventCodeConstant.TRAFFIC_JAM_END)),
    MOVE_CAR_EVENT(4, "扫码挪车", new HashSet<>()),
    USER_FEEDBACK(5, "用户反馈", new HashSet<>()),

    SPECIAL_AREA_STRANDING(6, "特殊区域停滞", new HashSet<>()),

    RETROGRADE(7, "逆行", new HashSet<>()),

    DRIVE_ON_TRAFFIC_LINE(8, "非法压线", new HashSet<>()),

    STRANDING(9, "停滞事件", new HashSet<>()),
    RESTRICTED_PARKING(10, "禁停区停滞", new HashSet<>()),
    LAUNCH_TIMEOUT(11, "起步超时", new HashSet<>()),
    SAFETY_AREA_IMPROPER_PARKING(12, "安全区域停放不当", new HashSet<>()),
    ERROR_BYPASS(13, "错误绕行", new HashSet<>()),
    ERROR_QUEUE(14, "错误排队", new HashSet<>()),
    ERROR_WAIT_CROSS_WALK(15, "错误让行", new HashSet<>()),
    EXAM_OBSTACLE_STRANDING(16, "考试障碍物停滞事件", new HashSet<>()),
    PARKING_FAILURE(17, "泊车失败", new HashSet<>()),
    ABNORMAL_CIRCLE_ROUTE(18, "异常绕圈路由", new HashSet<>()),
    TRAFFIC_BLOCK_EVENT(19, "堵路事件", new HashSet<>()),
    AUTOMATIC_START_EVENT(101, "H24主驾无人起自动失败", new HashSet<>());

    private int code;

    private String desc;

    private Set<Integer> eventCodes;

    private static final List<RiskCaseTypeEnum> MUST_ASSOCIATED_VEHICLE_EVENT_CODES = Arrays.asList(USER_FEEDBACK);

    private static final List<RiskCaseTypeEnum> HIGH_NEGATIVE_RISK_TYPE = Lists.newArrayList(SPECIAL_AREA_STRANDING,
            RETROGRADE, DRIVE_ON_TRAFFIC_LINE);

    /**
     * 根据value查询RiskCaseTypeEnum
     *
     * @param riderTaskType
     * @return
     */
    public static RiskCaseTypeEnum findByValue(Integer riderTaskType) {
        if (riderTaskType == null) {
            return null;
        }
        for (RiskCaseTypeEnum riskCaseTypeEnum : RiskCaseTypeEnum.values()) {
            if (riskCaseTypeEnum.getCode() == riderTaskType) {
                return riskCaseTypeEnum;
            }
        }
        return null;
    }

    /**
     * 获取小写后的驼峰命名
     */
    public String getLowerCaseCamelCaseName() {
        // 使用StringUtils
        return StringUtils.underlineToCamel(name());
    }

    /**
     * 根据eventCode查询RiskCaseTypeEnum
     *
     * @param eventCode
     * @return
     */
    public static RiskCaseTypeEnum findByEventCode(Integer eventCode) {
        for (RiskCaseTypeEnum riskCaseTypeEnum : RiskCaseTypeEnum.values()) {
            if (riskCaseTypeEnum.getEventCodes().contains(eventCode)) {
                return riskCaseTypeEnum;
            }
        }
        return null;
    }

    public String getShortStr() {
        return String.format("T%02d", code);
    }

    /**
     * 判断是否必须关联车辆
     *
     * @param typeEnum
     * @return
     */
    public static boolean isNotAssociatedVehicle(RiskCaseTypeEnum typeEnum) {
        return MUST_ASSOCIATED_VEHICLE_EVENT_CODES.contains(typeEnum);
    }

    public static boolean isHighNegativeRiskType(RiskCaseTypeEnum typeEnum) {
        return HIGH_NEGATIVE_RISK_TYPE.contains(typeEnum);
    }
}
