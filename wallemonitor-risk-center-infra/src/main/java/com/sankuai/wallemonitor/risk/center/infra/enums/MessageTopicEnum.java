package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum MessageTopicEnum {
    /**
     * 风险处置消息
     */
    WALLEMONITOR_RISK_MRM_PROCESS_MESSAGE("waimai", "wallemonitor.risk.mrm.process.message", CharConstant.CHAR_EMPTY),

    /**
     * 风险MQ
     */
    WALLEMONITOR_RISK_EVENT_MESSAGE("waimai", "wallemonitor.risk.event.message", CharConstant.CHAR_EMPTY),

    /**
     * 风险映射MQ
     */
    WALLEMONITOR_RISK_EVENT_MAPPING_MESSAGE("waimai", "wallemonitor.risk.eventId.traceId.mapping",
            CharConstant.CHAR_EMPTY),

    /**
     * 领域变更消息
     */
    WALLEMONITOR_RISK_DOMAIN_EVENT_MESSAGE("waimai", "wallemonitor.risk.center.domain.event", CharConstant.CHAR_EMPTY),

    /**
     * 领域异步消息
     */
    WALLEMONITOR_RISK_DOMAIN_EVENT_ASYNC_MESSAGE("waimai", "wallemonitor.risk.center.domain.event.async",
            CharConstant.CHAR_EMPTY),

    /**
     * 预检队列专有领域消息
     */
    RISK_CHECKING_QUEUE_ITEM_DOMAIN_EVENT_MESSAGE("waimai", "risk.center.risk_checking_queue_item.domain.event",
            CharConstant.CHAR_EMPTY),

    /**
     * 车辆状态变更消息
     */
    WALLEMONITOR_RISK_VEHICLE_STATUS_MESSAGE("waimai", "mad-vehicle.real.status.out",
            "com.sankuai.carosscan.realtimeinfo"),

    /**
     * 自动标注
     */
    WALLEMONITOR_RISK_AUTO_MARK_MESSAGE("waimai", "wallemonitor.risk.auto.mark.message", CharConstant.CHAR_EMPTY),

    /**
     * 自动标注多版本
     */
    WALLEMONITOR_RISK_MULTI_AUTO_MARK_MESSAGE("waimai", "wallemonitor.risk.multi.auto.mark.message",
            CharConstant.CHAR_EMPTY),

    /**
     * 向云分诊系统发送工单
     */
    CLOUD_TRIAGE_EVENT_MESSAGE("waimai", "walleops.cloud.triage.event.information",
            "com.sankuai.walleops.cloud.triage"),

    /**
     * 堵路事件发送消息
     */
    WALLEMONITOR_RISK_CENTER_COMMON_OUTPUT_RISK_EVENT_MESSAGE("waimai", "wallemonitor.risk.center.common.output.risk.event.message",
            CharConstant.CHAR_EMPTY),

    /**
     * 堵路事件接收消息
     */
    WALLEMONITOR_RISK_CENTER_COMMON_INPUT_RISK_EVENT_MESSAGE("waimai", "wallemonitor.risk.center.common.input.risk.event.message",
            CharConstant.CHAR_EMPTY);


    private final String namespace;
    private final String topic;

    private final String appKey;

    /**
     * 拼接消息主题
     *
     * @return
     */
    @Override
    public String toString() {
        return Joiner.on(CharConstant.CHAR_MH).join(namespace, topic);
    }
}
