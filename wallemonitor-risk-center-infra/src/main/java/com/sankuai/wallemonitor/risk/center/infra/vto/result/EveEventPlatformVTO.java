package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class EveEventPlatformVTO<T> {

    /**
     * 总数
     */
    private Long total;

    /**
     * 详情
     */
    private List<T> detail;

    /**
     * 页码
     */
    @JsonProperty("page_index")
    private Integer pageIndex;

    /**
     * 每页数量
     */
    @JsonProperty("page_size")
    private Integer pageSize;

    /**
     * 总页数
     */
    @JsonProperty("total_page")
    private Integer totalPage;


    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChangeDetail {

        /**
         * 车辆vin
         */
        private String vin;

        /**
         * 事件类型
         */
        @JsonProperty("event_type")
        private String eventType;

        /**
         * 事件id
         */
        @JsonProperty("event_id")
        private String eventId;

        /**
         * 旧值
         */
        @JsonProperty("old_value")
        private String oldValue;

        /**
         * 新值
         */
        @JsonProperty("new_value")
        private String newValue;

        /**
         * 扩展信息
         */
        @JsonProperty("ext_info")
        private String extInfo;

        /**
         * 更新时间
         */
        @JsonProperty("update_time")
        private Long updateTime;

        /**
         * 创建时间
         */
        @JsonProperty("create_time")
        private Long createTime;

    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MrmCockpitEventRecord {

        /**
         * 车辆vin
         */
        private String vin;

        /**
         * 车辆名称
         */
        private String name;

        /**
         * 坐席连入时间
         */
        @JsonProperty("add_time")
        private Date addTime;

        /**
         * 坐席用户
         */
        private String user;

    }
}
