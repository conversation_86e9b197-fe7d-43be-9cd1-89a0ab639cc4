package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCheckingQueueItem;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", uses = EnumsConvertMapper.class)
public interface RiskCheckingQueueConvert extends SingleConvert<RiskCheckingQueueItem, RiskCheckingQueueItemDO> {

    RiskCheckingQueueConvert INSTANCE = Mappers.getMapper(RiskCheckingQueueConvert.class);

    @Override
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseTypeEnum")
    @Mapping(source = "source", target = "source", qualifiedByName = "toRiskCaseSourceEnum")
    @Mapping(source = "status", target = "status", qualifiedByName = "toRiskQueueStatusEnum")
    @Mapping(source = "checkResult", target = "checkResult", qualifiedByName = "parseCheckResult")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "toRiskCheckingExtInfoDO")
    RiskCheckingQueueItemDO toDO(RiskCheckingQueueItem po);

    @Override
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(source = "source", target = "source", qualifiedByName = "toRiskCaseSource")
    @Mapping(source = "status", target = "status", qualifiedByName = "toRiskQueueStatus")
    @Mapping(source = "checkResult", target = "checkResult", qualifiedByName = "serializeCheckResult")
    @Mapping(source = "extInfo", target = "extInfo", qualifiedByName = "toExtInfo")
    RiskCheckingQueueItem toPO(RiskCheckingQueueItemDO doObj);
}