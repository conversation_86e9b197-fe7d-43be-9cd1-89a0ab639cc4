package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiskRestrictQueryDTO {

    /**
     * 限制类型列表
     */
    private List<String> restrictTypes;

    /**
     * 位置信息
     */
    private PositionDO position;

    /**
     * 高精地图版本
     */
    private String hdMapVersion;

    /**
     * 距离（米）
     */
    private Integer meter;

    /**
     * 膨胀区域
     */
    private Integer expandMeter;

    public boolean valid() {

        return CollectionUtils.isNotEmpty(restrictTypes) && position != null && StringUtils.isNotEmpty(hdMapVersion)
                && meter != null;
    }

    /**
     * 从高精地图里面解析出来区域
     * 
     * @return
     */
    public String getArea() {
        if (StringUtils.isEmpty(hdMapVersion)) {
            return CharConstant.CHAR_EMPTY;
        }
        // shenzhenpingshan_admap_v5.391.0 拼接规则是固定的
        return hdMapVersion.split(CharConstant.CHAR_XH)[0];
    }
}