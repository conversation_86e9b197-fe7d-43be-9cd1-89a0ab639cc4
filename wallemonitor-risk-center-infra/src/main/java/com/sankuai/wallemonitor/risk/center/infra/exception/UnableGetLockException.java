package com.sankuai.wallemonitor.risk.center.infra.exception;


import com.sankuai.walleeve.commons.enums.ErrorCode;
import com.sankuai.walleeve.commons.exception.ErrorCodeException;
import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;

public class UnableGetLockException extends ErrorCodeException {

    public UnableGetLockException(int code, String message) {
        super(code, message);
    }

    public UnableGetLockException(String message) {
        super(ResponseCodeEnum.UNABLE_GET_LOCK.getCode(), message);
    }

    public UnableGetLockException(int code) {
        super(code, ResponseCodeEnum.UNABLE_GET_LOCK.getMessage());
    }

    public UnableGetLockException(int code, String format, Object... arguments) {
        super(code, format, arguments);
    }

    public UnableGetLockException(ErrorCode errors, Object... arguments) {
        super(errors, arguments);
    }

    public UnableGetLockException(ResponseCodeEnum responseCode) {
        super(responseCode.getCode(), responseCode.getMessage());
    }
}
