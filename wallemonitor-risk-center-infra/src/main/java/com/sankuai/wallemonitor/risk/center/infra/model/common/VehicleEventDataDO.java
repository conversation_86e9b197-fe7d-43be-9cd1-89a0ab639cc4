package com.sankuai.wallemonitor.risk.center.infra.model.common;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆事件
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleEventDataDO {

    @JsonProperty("eventId")
    @JsonAlias("event_id")
    private String eventId;

    @JsonProperty("eventCode")
    @JsonAlias("event_code")
    private int eventCode;

    @JsonProperty("eventName")
    @JsonAlias("event_name")
    private String eventName;

    @JsonProperty("eventTimestamp")
    @JsonAlias("event_timestamp")
    private long eventTimestamp;

    @JsonProperty("senderTimestamp")
    @JsonAlias("sender_timestamp")
    private long senderTimestamp;

    @JsonProperty("receiverTimestamp")
    @JsonAlias("receiver_timestamp")
    private long receiverTimestamp;

    @JsonProperty("vin")
    private String vin;

    @JsonProperty("vehicleId")
    @JsonAlias("vehicle_id")
    private String vehicleId;

    @JsonProperty("vehicleName")
    @JsonAlias("vehicle_name")
    private String vehicleName;

    @JsonProperty("recordName")
    @JsonAlias("recor_name")
    private String recordName;

    @JsonProperty("utmZone")
    @JsonAlias("utm_zone")
    private int utmZone;

    @JsonProperty("utmX")
    @JsonAlias("utm_x")
    private String utmX;

    @JsonProperty("utmY")
    @JsonAlias("utm_y")
    private String utmY;

    @JsonProperty("datasource")
    private String datasource;

    @JsonProperty("content")
    private Map<String, Object> content;

}