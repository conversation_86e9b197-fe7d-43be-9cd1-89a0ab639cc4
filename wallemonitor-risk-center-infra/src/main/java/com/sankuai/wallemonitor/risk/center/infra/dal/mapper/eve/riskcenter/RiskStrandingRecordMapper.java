package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskStrandingRecord;

/**
 * <p>
 * 风险事件检测过程表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-11-27
 */
public interface RiskStrandingRecordMapper extends CommonMapper<RiskStrandingRecord> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskStrandingRecord> getPOClass() {
        return RiskStrandingRecord.class;
    }

}
