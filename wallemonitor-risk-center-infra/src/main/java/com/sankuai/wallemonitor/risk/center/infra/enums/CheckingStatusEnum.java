package com.sankuai.wallemonitor.risk.center.infra.enums;

public enum CheckingStatusEnum {
    NOT_CHECKING(0, "不在检查中"),
    CHECKING(1, "检查中");

    private final int code;
    private final String description;

    CheckingStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static CheckingStatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CheckingStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
