package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 坐席呼叫过滤规则命中纪录表查询参数DTO
 */
@Data
public class MrmCallFilterRuleHitLogDOQueryParamDTO implements Serializable {
    /** case的唯一ID */
    private String caseId;
    /** 开始时间 */
    private Date startTime;
    /** 结束时间 */
    private Date endTime;
    /** 是否删除 */
    private Boolean isDeleted;
} 