package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.vo.HdMapElementGeoVO;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.enums.GeoTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapElementTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.HdMapEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.GeometryFeatureVTO;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", imports = {EnumsConvertMapper.class}, uses = {EnumsConvertMapper.class})
public interface HdMapElementPolygonConvert {

    default List<HdMapElementGeoDO> toHdMapElementPolygonDO(String map, GeometryFeatureVTO geometryFeatureVTO) {
        if (Objects.isNull(geometryFeatureVTO) || geometryFeatureVTO.getGeometry() == null) {
            return Collections.emptyList();
        }
        HdMapElementTypeEnum elementTypeEnum = HdMapElementTypeEnum.fromValue(geometryFeatureVTO.getElementType(map));
        if (elementTypeEnum == null) {
            return Collections.emptyList();
        }
        HdMapEnum mapEnum = HdMapEnum.fromValue(map);
        if (mapEnum == null) {
            return Collections.emptyList();
        }
        switch (mapEnum) {
            case LANE:
                // 如果是lane类型，构建
                List<PositionDO> middleList = geometryFeatureVTO.toLineList();
                return Collections.singletonList(HdMapElementGeoDO.builder().id(geometryFeatureVTO.getId())
                        // 取类型
                        .elementType(elementTypeEnum.getValue())
                        // 取区域类型
                        .areaType(geometryFeatureVTO.getLaneAreaType(map))
                        // 取地图类型
                        .mapType(mapEnum.getValue())
                        // 地理位置类型
                        .geoType(GeoTypeEnum.fromValue(geometryFeatureVTO.getGeometry().getType()))
                        // 属性相关
                        .properties(geometryFeatureVTO.getProperties())
                        // 中心线
                        .middleLinePoints(middleList).build());
            default:
                // 有范围的区域
                return geometryFeatureVTO.toPolygon().stream()
                        .map(polygonDO -> HdMapElementGeoDO.builder().id(geometryFeatureVTO.getId())
                                // 取元素类型
                                .elementType(elementTypeEnum.getValue())
                                // 取地图类型
                                .mapType(mapEnum.getValue())
                                // 地理位置类型
                                .geoType(GeoTypeEnum.fromValue(geometryFeatureVTO.getGeometry().getType()))
                                // 属性相关
                                .properties(geometryFeatureVTO.getProperties())
                                // 取范围
                                .polygonDO(polygonDO).build())
                        .collect(Collectors.toList());
        }

    }

    default List<HdMapElementGeoDO> toHdMapElementPolygonDO(String map,
            List<GeometryFeatureVTO> geometryFeatureVTOList) {
        if (CollectionUtils.isEmpty(geometryFeatureVTOList)) {
            return Collections.emptyList();
        }
        // 摊平
        return geometryFeatureVTOList.stream()
                .map(geometryFeatureVTO -> this.toHdMapElementPolygonDO(map, geometryFeatureVTO))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 转成id->map
     * 
     * @param map
     * @param geometryFeatureVTOList
     * @return
     */
    default Map<String, HdMapElementGeoDO> toHdMapElementPolygonDOMap(String map,
            List<GeometryFeatureVTO> geometryFeatureVTOList) {
        if (CollectionUtils.isEmpty(geometryFeatureVTOList)) {
            return new HashMap<>();
        }
        Map<String, HdMapElementGeoDO> laneMap = new HashMap<>();
        geometryFeatureVTOList.stream().map(geometryFeatureVTO -> this.toHdMapElementPolygonDO(map, geometryFeatureVTO))
                // 摊平
                .flatMap(Collection::stream)
                // 拿到id
                .forEach(hdMapElementGeoDO -> laneMap.put(hdMapElementGeoDO.getId(), hdMapElementGeoDO));
        return laneMap;
    }

    default List<HdMapElementGeoVO> toHdMapElementGeoVO(List<HdMapElementGeoDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream()
                .map(hdMapElementGeoDO -> HdMapElementGeoVO.builder().laneId(hdMapElementGeoDO.getId())
                        .points(hdMapElementGeoDO.getCoordinatesList())
                        .laneMiddleLinePoints(hdMapElementGeoDO.getMiddleLinePointList())
                        .laneType(hdMapElementGeoDO.getElementType())
                        // 元素类型
                        .elementType(hdMapElementGeoDO.getElementType())
                        // 拓展信息
                        .properties(JacksonUtils.to(hdMapElementGeoDO.getProperties())).build())
                .collect(Collectors.toList());
    }
}