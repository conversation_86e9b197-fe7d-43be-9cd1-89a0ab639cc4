package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseParkingPlotRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseParkingPlotRelationDOQueryParamDTO;
import java.util.List;

/**
 * 泊车失败事件与泊位ID关联Repository接口
 */
public interface RiskCaseParkingPlotRelationRepository {

    /**
     * 根据参数查询泊车失败事件与泊位ID关联关系
     *
     * @param paramDTO 查询参数
     * @return 关联关系列表
     */
    List<RiskCaseParkingPlotRelationDO> queryByParam(RiskCaseParkingPlotRelationDOQueryParamDTO paramDTO);

    /**
     * 根据caseId查询泊车失败事件与泊位ID关联关系
     *
     * @param caseId 事件ID
     * @return 关联关系DO
     */
    RiskCaseParkingPlotRelationDO getByCaseId(String caseId);

    /**
     * 保存泊车失败事件与泊位ID关联关系
     *
     * @param relationDO 关联关系DO
     */
    void save(RiskCaseParkingPlotRelationDO relationDO);

    /**
     * 批量保存泊车失败事件与泊位ID关联关系
     *
     * @param relationDOList 关联关系DO列表
     */
    void batchSave(List<RiskCaseParkingPlotRelationDO> relationDOList);
} 