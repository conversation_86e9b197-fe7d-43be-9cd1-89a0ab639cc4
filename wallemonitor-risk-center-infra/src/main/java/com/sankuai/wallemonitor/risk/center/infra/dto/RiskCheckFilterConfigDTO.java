package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.utils.SpELUtil;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCheckFilterConfigDTO {

    private List<String> filterList;

    /**
     * 判断是否可以过滤
     *
     * @param evalContext
     * @return
     */
    public Boolean isFilter(Map<String, Object> evalContext) {
        if (CollectionUtils.isEmpty(filterList)) {
            return false;
        }
        return filterList.stream()
                .anyMatch(filter -> SpELUtil.evaluateBoolean(filter, evalContext));


    }
}
