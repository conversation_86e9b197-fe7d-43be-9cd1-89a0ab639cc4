package com.sankuai.wallemonitor.risk.center.infra.model.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 停滞不当原因
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImproperStrandingReason {

    /**
     * 是否有救援工单
     */
    private Boolean withRescueOrder;

    /**
     * 是否有事故工单
     */
    private Boolean withAccidentOrder;

    /**
     * 是否有RE工单
     */
    private Boolean withReOrder;

    /**
     * 是否人工主动停靠（坐席、近场）
     */
    private Boolean withManualParking;

    public void withRescueOrder() {
        this.setWithRescueOrder(true);
    }

    public void withAccidentOrder() {
        this.setWithAccidentOrder(true);
    }

    public void withReOrder() {
        this.setWithReOrder(true);
    }

    public void withManualParking() {
        this.withManualParking = true;
    }
}
