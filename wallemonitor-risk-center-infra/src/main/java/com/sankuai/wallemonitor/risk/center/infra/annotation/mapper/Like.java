package com.sankuai.wallemonitor.risk.center.infra.annotation.mapper;

import com.sankuai.wallemonitor.risk.center.infra.enums.LikeTypeEnum;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Like {

    LikeTypeEnum value() default LikeTypeEnum.LEFT;

    String field() default "";
}
