package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.convert.VehicleVTO2DOConvert;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.VehicleRuntimeInfoParamVTO;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class VehicleInfoRepositoryImpl implements VehicleInfoRepository {

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private VehicleVTO2DOConvert vehicleInfoConvert;


    /**
     * 根据VIN列表查询车辆信息
     *
     * @param vinList
     * @return
     */
    @Override
    public List<VehicleInfoDO> queryByVinList(List<String> vinList) {
        return vehicleInfoConvert.toDOList(vehicleAdapter.queryRuntimeVehicleInfo(
                VehicleRuntimeInfoParamVTO.builder().vinList(vinList).build()));
    }

    /**
     * 根据VIN查询车辆信息
     *
     * @param vin
     * @return
     */
    @Override
    public VehicleInfoDO getByVin(String vin) {
        List<VehicleInfoDO> vehicleInfoDOList = queryByVinList(Lists.newArrayList(vin));
        return vehicleInfoDOList.stream().findFirst().orElse(null);
    }

    /**
     * 根据CarAccountList查询车辆信息，例如 ["s20-191","s20-194","s20-250"]
     *
     * @param getByCarAccountList
     * @return
     */
    @Override
    public List<String> getVinByCarAccountList(List<String> getByCarAccountList) throws Exception{
        return vehicleAdapter.queryVinByCarAccountList(getByCarAccountList);
    }
}
