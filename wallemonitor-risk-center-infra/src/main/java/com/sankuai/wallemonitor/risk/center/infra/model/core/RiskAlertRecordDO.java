package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordLabelEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.UpgradeStatusEnum;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 风险告警记录实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskAlertRecordDO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 告警策略
     */
    private String alertPolicy;

    /**
     * 配置名
     */
    private String configName;

    /**
     * 业务id
     */
    @DomainUnique
    private String bizId;

    /**
     * 大象消息Id
     */
    private String messageId;

    /**
     * 大象群组
     */
    private String groupId;

    /**
     * 事件ID列表
     */
    private String eventIds;

    /**
     * 告警参数列表
     */
    private String arguments;

    /**
     * 操作类型：-1-无需处理，0-未处理，10-处理中，20-已处理
     */
    private AlertRecordStatusEnum status;

    /**
     * 标注类型：-1-无用，0-未标注，1-有用
     */
    private AlertRecordLabelEnum label;

    /**
     * 升级状态：0-无需升级，1-需要升级，2-完成升级
     */
    private UpgradeStatusEnum upgradeStatus;

    /**
     * 升级后的大象消息Id
     */
    private String upgradeMessageId;

    /**
     * 操作人
     */
    @DomainUnique
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    @Builder.Default
    private IsDeleteEnum isDeleted = IsDeleteEnum.NOT_DELETED;
} 