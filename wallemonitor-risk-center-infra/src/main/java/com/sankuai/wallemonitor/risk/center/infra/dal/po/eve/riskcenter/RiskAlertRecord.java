package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 聚合告警记录PO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("risk_alert_record")
public class RiskAlertRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @TableUnique
    private Long id;

    /**
     * 告警策略
     */
    @TableField("alert_policy")
    private String alertPolicy;

    /**
     * 配置名
     */
    @TableField("config_name")
    private String configName;

    /**
     * 业务id
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 大象消息Id
     */
    @TableField("message_id")
    private String messageId;

    /**
     * 大象群组
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 事件ID列表
     */
    @TableField("event_ids")
    private String eventIds;

    /**
     * 告警参数列表
     */
    @TableField("arguments")
    private String arguments;

    /**
     * 操作类型：-1-无需处理，0-未处理，10-处理中，20-已处理
     */
    @TableField("status")
    private Integer status;

    /**
     * 标注类型：-1-无用，0-未标注，1-有用
     */
    @TableField("label")
    private Integer label;

    /**
     * 升级状态：0-无需升级，1-需要升级，2-完成升级
     */
    @TableField("upgrade_status")
    private Integer upgradeStatus;

    /**
     * 升级后的大象消息Id
     */
    @TableField("upgrade_message_id")
    private String upgradeMessageId;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
} 