package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskRestrictedParkingRecordConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.RiskRestrictedParkingRecordMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskRestrictedParkingRecord;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskRestrictedParkingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskRestrictedParkingRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskRestrictedParkingRecordDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 禁停区域检测记录仓储实现
 */
@Component
@Slf4j
public class RiskRestrictedParkingRecordRepositoryImpl extends
        AbstractMapperSingleRepository<RiskRestrictedParkingRecordMapper, RiskRestrictedParkingRecordConvert, RiskRestrictedParkingRecord, RiskRestrictedParkingRecordDO> implements
        RiskRestrictedParkingRecordRepository {

    private static final String UK_TMP_CASE_ID = "tmpCaseId";

    /**
     * 根据参数查询禁停区域检测记录
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<RiskRestrictedParkingRecordDO> queryByParam(RiskRestrictedParkingRecordDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据临时事件ID获取禁停区域检测记录
     *
     * @param tmpCaseId
     * @return
     */
    @Override
    @RepositoryQuery
    public RiskRestrictedParkingRecordDO getByTmpCaseId(String tmpCaseId) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_TMP_CASE_ID)
                .value(tmpCaseId)
                .build()));
    }

    /**
     * 根据参数查询禁停区域检测记录 (分页)
     *
     * @param paramDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public Paging<RiskRestrictedParkingRecordDO> queryByParamByPage(RiskRestrictedParkingRecordDOQueryParamDTO paramDTO, Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 保存禁停区域检测记录
     *
     * @param riskRestrictedParkingRecordDO
     */
    @Override
    @RepositoryExecute
    public void save(RiskRestrictedParkingRecordDO riskRestrictedParkingRecordDO) {
        super.save(riskRestrictedParkingRecordDO);
    }

    /**
     * 批量保存禁停区域检测记录
     *
     * @param riskRestrictedParkingRecordDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<RiskRestrictedParkingRecordDO> riskRestrictedParkingRecordDOList) {
        super.batchSave(riskRestrictedParkingRecordDOList);
    }
}