package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class DataContainerConsumeConfigDTO {

    private Boolean enable;

    /**
     * 计算前 K 个车流的障碍物
     * */
    private Long topKTrafficFlowBuffer;

    /**
     * 是否开启topic /walle/planning/data_container 的计算
     * */
    private Boolean open;
}
