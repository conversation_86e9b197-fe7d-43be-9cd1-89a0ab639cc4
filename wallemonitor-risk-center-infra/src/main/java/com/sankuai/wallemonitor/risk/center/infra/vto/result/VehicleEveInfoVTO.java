package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.VHRModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleEveInfoVTO {

    /**
     * 车辆信息
     */
    private String vin;

    /**
     * vehicleId，M1234
     */
    private String vehicleId;

    /**
     * vehicleName,S20-123
     */
    private String vehicleName;

    /**
     * 约车目的
     */
    private String purpose;


    /**
     * vhr类型
     */
    private VHRModeEnum vhr;

    /**
     * 车辆定位
     */
    private PositionDO position;

    /**
     * 场地
     */
    private String placeCode;

    /**
     * 自动驾驶版本
     */
    private String autocarVersion;

    /**
     * 驾驶模式
     */
    private Integer driveMode;

    /**
     * 救援中
     */
    private Boolean withRescueOrder;

    /**
     * 事故中
     */
    private Boolean withAccidentOrder;

    /**
     * 维修中
     */
    private Boolean withMaintenanceOrder;

    /**
     * Re工单
     * */
    private Boolean withReOrder;

    /**
     * 驾驶速度 km/h
     */
    private Float speed;

    /**
     * 云控安全员
     */
    private String telecontrol;

    /**
     * 近场安全员
     */
    private String substitute;

    /**
     * 获取hdMapVersion
     */
    private String hdMapVersion;

    /**
     * 业务状态
     */
    private String bizStatus;

    /**
     * 车辆档位
     */
    private String gear;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 获取hdMap区域
     *
     * @return
     */
    public String findHdMapArea() {
        if (StringUtils.isEmpty(hdMapVersion)) {
            return CharConstant.CHAR_EMPTY;
        }
        // shenzhenpingshan_admap_v5.391.0 拼接规则是固定的
        return hdMapVersion.split(CharConstant.CHAR_XH)[0];
    }

}
