package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 风险实时定位DO对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaseLocationRelationDO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * caseId
     */
    @DomainUnique
    private String caseId;

    /**
     * 风险坐标(WGS84)
     */
    private PositionDO location;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除,0|未,1|已
     */
    private Boolean isDeleted;
}