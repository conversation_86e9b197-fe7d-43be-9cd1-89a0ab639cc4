package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.VehicleRuntimeInfoContext;

/**
 * <p>
 * 车辆运行时信息数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-10-14
 */
public interface VehicleRuntimeInfoContextMapper extends CommonMapper<VehicleRuntimeInfoContext> {

    @Override
    default Class<VehicleRuntimeInfoContext> getPOClass() {
        return VehicleRuntimeInfoContext.class;
    }

}
