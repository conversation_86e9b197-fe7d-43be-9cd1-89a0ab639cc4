package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeInfoContextDOQueryParamDTO;
import java.util.List;
import java.util.Set;

/**
 * 车辆运行时信息上下文仓储接口
 */
public interface VehicleRuntimeInfoContextRepository {

    /**
     * 根据参数查询车辆运行时信息
     *
     * @param paramDTO 查询参数
     * @return 车辆运行时信息列表
     */
    List<VehicleRuntimeInfoContextDO> queryByParam(VehicleRuntimeInfoContextDOQueryParamDTO paramDTO);

    /**
     * 分页查询车辆运行时信息
     *
     * @param paramDTO 查询参数
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    Paging<VehicleRuntimeInfoContextDO> queryByParamByPage(VehicleRuntimeInfoContextDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize);

    /**
     * 根据车辆VIN码获取运行时信息
     *
     * @param vin 车辆VIN码
     * @return 车辆运行时信息
     */
    VehicleRuntimeInfoContextDO getByVin(String vin);

    /**
     * 根据车辆VIN码获取完整的运行时信息
     *
     * @param vin 车辆VIN码
     * @return 完整的车辆运行时信息
     */
    @RepositoryQuery
    VehicleRuntimeInfoContextDO getFullByVin(String vin);

    /**
     * 批量根据车辆VIN码获取完整的运行时信息
     *
     * @param vin 车辆VIN码列表
     * @return 完整的车辆运行时信息列表
     */
    @RepositoryQuery
    List<VehicleRuntimeInfoContextDO> getFullByVin(List<String> vin);

    /**
     * 保存车辆运行时信息
     *
     * @param vehicleRuntimeInfoContextDO 车辆运行时信息
     */
    void save(VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO);

    /**
     * 批量保存车辆运行时信息
     *
     * @param vehicleRuntimeInfoContextDOList 车辆运行时信息列表
     */
    void batchSave(List<VehicleRuntimeInfoContextDO> vehicleRuntimeInfoContextDOList);


    /**
     * 单个查询缓存中车辆运行时状态信息
     *
     * @param vin 车辆VIN码
     * @return 车辆运行时信息
     */
    VehicleRuntimeInfoContextDO getFromCache(String vin);

    /**
     * 批量查询缓存中车辆运行时状态信息
     *
     * @param vinList 车辆VIN码列表
     * @return 车辆运行时信息列表
     */
    @RepositoryQuery
    List<VehicleRuntimeInfoContextDO> getFromCache(List<String> vinList);

    /**
     * 批量查询缓存中车辆运行时状态信息（按需获取）
     *
     * @param vinList 车辆VIN码列表
     * @param needFields 需要获取的字段列表
     * @return 车辆运行时信息列表
     */
    @RepositoryQuery
    List<VehicleRuntimeInfoContextDO> getFromCache(List<String> vinList, List<String> needFields);

    /**
     * 获取指定时间段内的车辆VIN码集合
     *
     * @param timePeriod 时间段
     * @return 车辆VIN码集合
     */
    Set<String> queryFromCache(TimePeriod timePeriod);


    /**
     * 批量存储车辆运行时信息数据到缓存中
     *
     * @param vehicleRuntimeInfoContextDO 车辆运行时信息
     * @param timestamp 时间戳
     */
    void updateCache(VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO, Long timestamp);

}