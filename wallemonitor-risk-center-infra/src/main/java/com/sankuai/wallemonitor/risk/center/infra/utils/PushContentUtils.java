package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.sankuai.wallemonitor.risk.center.infra.enums.ConfirmType;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.CharEncoding;

@Slf4j
public class PushContentUtils {

    //交互消息大象协议
    private static final String PROTOCOL = "mtdaxiang://www.meituan.com/doVolleyRequest?";

    /**
     * 构建大象协议的content
     *
     * @param buttonName
     * @param targetUrl
     * @param bizParams
     * @param confirmStyleDTO
     * @param <T>
     * @return
     */
    public static <T> String buildContent(String buttonName, String targetUrl, T bizParams,
            ConfirmStyleDTO confirmStyleDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        //按钮名称
        sb.append(buttonName);
        sb.append("|");
        //大象协议
        sb.append(PROTOCOL);
        //二级协议
        sb.append("path=%2fupgrade%2ffunction%2fsetting%2fdoVolleyForThird%2fv2");
        //业务方参数组装
        sb.append("&requestparams=").append(buildBizParams(bizParams, targetUrl));
        //确认按钮样式组装
        sb.append(buildUrlParams(confirmStyleDTO));
        sb.append("]");

        return sb.toString();
    }

    private static <T> String buildBizParams(T bizParams, String targetUrl) {
        Map<String, Object> map = new HashMap<>();
        map.put("targetUrl", targetUrl);
        map.put("bizParams", bizParams);

        try {
            //这里不要修改成自己类json序列化容易出问题
            return URLEncoder.encode(InnerJacksonUtils.toJson(map), CharEncoding.UTF_8);
        } catch (UnsupportedEncodingException e) {
            log.error("构建参数链接异常", e);
        }
        return "";
    }

    private static String buildUrlParams(ConfirmStyleDTO confirmStyleDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append("&promptType=").append(confirmStyleDTO.getPromptType().getValue());
        sb.append("&isNeedConfirm=").append(Boolean.TRUE.equals(confirmStyleDTO.getNeedConfirm()) ? "Y" : "N");
        try {
            sb.append("&confirmTitle=")
                    .append(URLEncoder.encode(confirmStyleDTO.getConfirmTitle(), CharEncoding.UTF_8));
            sb.append("&confirmContent=")
                    .append(URLEncoder.encode(confirmStyleDTO.getConfirmContent(),
                            CharEncoding.UTF_8));
        } catch (UnsupportedEncodingException e) {
            log.error("", e);
        }
        return sb.toString();
    }

    @Slf4j
    @Getter
    @Setter
    public static class ConfirmStyleDTO {

        private ConfirmType promptType;
        private Boolean needConfirm;
        private String confirmTitle;
        private String confirmContent;
    }

    private static class InnerJacksonUtils {

        private static final Object MAPPER_LOCK = new Object();
        private static volatile ObjectMapper mapper;

        private InnerJacksonUtils() {
        }

        private static ObjectMapper getMapperInstance() {
            if (mapper == null) {
                synchronized (MAPPER_LOCK) {
                    if (mapper == null) {
                        mapper = new ObjectMapper();
                        mapper.configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, true);
                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                        mapper.configure(SerializationFeature.WRAP_ROOT_VALUE, false);
                        mapper.configure(DeserializationFeature.UNWRAP_ROOT_VALUE, false);
                        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                        mapper.setLocale(Locale.CHINA);
                        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
                    }
                }
            }
            return mapper;
        }

        public static String toJson(Object param) {
            try {
                ObjectMapper objectMapper = getMapperInstance();
                return objectMapper.writeValueAsString(param);
            } catch (Exception e) {
                throw new RuntimeException("JacksonUtil toJson error", e);
            }
        }

        public static <T> T jsonToBean(String json, Class<T> cls) {
            try {
                ObjectMapper objectMapper = getMapperInstance();
                return objectMapper.readValue(json, cls);
            } catch (Exception e) {
                throw new RuntimeException("JacksonUtil jsonToBean error", e);
            }
        }

        public static <T> T jsonToBeanByTypeReference(String json, TypeReference<T> typeReference) {
            try {
                return getMapperInstance().readValue(json, typeReference);
            } catch (Exception e) {
                throw new RuntimeException("JacksonUtil jsonToBeanByTypeReference error", e);
            }
        }
    }

}