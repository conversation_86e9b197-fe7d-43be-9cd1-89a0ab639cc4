package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl;


import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.IDGenerateAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeTemplateConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleBasicVTO;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * id生成器仓储服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class IDGenerateRepositoryImpl implements IDGenerateRepository {

    /**
     * 通用Leaf前缀
     */
    private static final String COMMON_PREFIX = "wallemonitor.risk.center";

    @Resource
    private IDGenerateAdapter iDGenerateAdapter;

    @Resource
    private VehicleAdapter vehicleAdapter;

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Override
    public String generateByKey(IDBizEnum idBizEnum, String vin, RiskCaseSourceEnum source, RiskCaseTypeEnum type,
            Date date) {
        return generateByKey(idBizEnum, Lists.newArrayList(vin), source, type, DatetimeUtil.getTimeStamp(date));
    }

    /**
     * 获取对应实体的ID
     *
     * @param idBizEnum
     * @param vinList
     * @param source
     * @param type
     * @param timestamp
     * @return
     */
    @Override
    public String generateByKey(IDBizEnum idBizEnum, List<String> vinList, RiskCaseSourceEnum source,
            RiskCaseTypeEnum type, Long timestamp) {
        SystemCheckUtil.isNotNull(idBizEnum, "生成id的类型不能为空");
        switch (idBizEnum) {
            case RISK_CASE_ID: {
                List<VehicleBasicVTO> vehicleBasicVTOS = vehicleAdapter.queryVehicleBasicInfoList(vinList);
                VehicleBasicVTO vehicleBasicVTO = vehicleBasicVTOS.stream().findFirst().orElse(null);
                String vehicleId = Optional.ofNullable(vehicleBasicVTO).map(VehicleBasicVTO::getVehicleId)
                        .orElse(CharConstant.CHAR_EMPTY);
                // 替换中文
                String finalVehicleId = containsChinese(vehicleId) ? getCleanVehicleName(vehicleBasicVTO) : vehicleId;
                if (!RiskCaseTypeEnum.isNotAssociatedVehicle(type)) {
                    if (StringUtils.isBlank(vehicleId)) {
                        String vin = vehicleBasicVTOS.stream().map(VehicleBasicVTO::getVin).findFirst()
                                .orElse(CharConstant.CHAR_EMPTY);
                        log.error("VIN: {} 的车辆名称为空，使用默认值{}", vin, CommonConstant.MARK_VEHICLE_ID_UNKNOWN);
                        finalVehicleId = CommonConstant.MARK_VEHICLE_ID_UNKNOWN;
                    }
                }

                String dateTime = DatetimeUtil.formatDate(new Date(timestamp), DateTimeTemplateConstant.YYYYMMDDHHMMSS);
                return Joiner.on(CharConstant.CHAR_EMPTY)
                        .join(finalVehicleId, dateTime, source.getShortStr(), type.getShortStr());
            }
            default: {
                return null;
            }
        }
    }

    /**
     * 去除特殊字符
     * @param vehicleBasicVTO
     * @return
     */
    public static String getCleanVehicleName(VehicleBasicVTO vehicleBasicVTO) {
        if (Objects.isNull(vehicleBasicVTO)) {
            return CharConstant.CHAR_EMPTY;
        }
        String vehicleName = vehicleBasicVTO.getVehicleName();
        if (StringUtils.isBlank(vehicleName)) {
            return CharConstant.CHAR_EMPTY;
        }
        return vehicleName.replaceAll("[^a-zA-Z0-9]", "").toUpperCase();
    }

    /**
     * 检查是否包含中文字符
     *
     * @param str
     * @return
     */
    public static boolean containsChinese(String str) {
        if (str == null) {
            return false;
        }
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        return m.find();
    }

    /**
     * 根据业务获取ID
     *
     * @param idBizEnum
     * @return
     */
    @Override
    public String generateByKey(IDBizEnum idBizEnum) {
        return iDGenerateAdapter.generateId(idBizEnum.getKey());
    }

    /**
     * 生成eventId
     *
     * @param eventTime
     * @param riskType
     * @param vehicleName
     * @return
     */
    @Override
    public String generateEventId(Date eventTime, String riskType, String vehicleName) {
        return new StringBuilder(DatetimeUtil.formatDate(eventTime, "yyyyMMddHHmmssSSS"))
                .append(CharConstant.CHAR_XH).append(riskType)
                .append(CharConstant.CHAR_XH).append(vehicleName)
                .toString();

    }

    @Override
    public String convertEventId(String eventId, RiskCaseTypeEnum riskType) {
        if (StringUtils.isBlank(eventId) || riskType == null) {
            return eventId;
        }

        int firstSeparatorIndex = eventId.indexOf(CharConstant.CHAR_XH);
        int lastSeparatorIndex = eventId.lastIndexOf(CharConstant.CHAR_XH);

        if (firstSeparatorIndex == -1 || lastSeparatorIndex == -1 || firstSeparatorIndex == lastSeparatorIndex) {
            return eventId;
        }

        String prefix = eventId.substring(0, firstSeparatorIndex);
        String suffix = eventId.substring(lastSeparatorIndex + 1);

        return prefix + CharConstant.CHAR_XH + riskType.name() + CharConstant.CHAR_XH + suffix;
    }

    /**
     * 生成通用业务ID
     *
     * @param idBizEnum 业务ID类型
     * @param prefix 业务前缀
     * @param timestamp 时间戳（可选，为null时使用当前时间）
     * @return 生成的业务ID
     */
    @Override
    public String generateBusinessId(IDBizEnum idBizEnum, String prefix, Long timestamp) {
        SystemCheckUtil.isNotNull(idBizEnum, "生成id的类型不能为空");
        SystemCheckUtil.isNotBlank(prefix, "业务前缀不能为空");

        switch (idBizEnum) {
            case ALERT_UPGRADE_BIZ_ID: {
                // 使用传入的时间戳，如果为null则使用当前时间
                long version = timestamp != null ? timestamp : System.currentTimeMillis();
                return prefix + "_" + version;
            }
            default: {
                log.warn("未支持的业务ID类型: {}", idBizEnum);
                return null;
            }
        }
    }

    
}
