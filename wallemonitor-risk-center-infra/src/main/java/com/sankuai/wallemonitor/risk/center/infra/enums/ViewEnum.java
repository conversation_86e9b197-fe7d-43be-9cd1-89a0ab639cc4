package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum ViewEnum {
    FRONT("front", 0, "前视"),
    LEFT("left", 0, "左视"),
    RIGHT("right", 0, "右视"),
    BACK("back", 0, "后视"),
    LOOP("loop", 0, "环视"),
    ;

    private final String code;
    private final int videoTypeCode;
    private final String description;

    /**
     * 根据code获取ViewEnum
     *
     * @param view
     * @return
     */
    public static ViewEnum getByCode(String view) {
        if (StringUtils.isBlank(view)) {
            return null;
        }
        for (ViewEnum viewEnum : ViewEnum.values()) {
            if (StringUtils.lowerCase(viewEnum.getCode()).equals(view)) {
                return viewEnum;
            }
        }
        return null;
    }

    public static List<ViewEnum> getViewEnums() {
        return Arrays.asList(ViewEnum.FRONT, ViewEnum.BACK, ViewEnum.LEFT, ViewEnum.RIGHT, ViewEnum.LOOP);

    }
}