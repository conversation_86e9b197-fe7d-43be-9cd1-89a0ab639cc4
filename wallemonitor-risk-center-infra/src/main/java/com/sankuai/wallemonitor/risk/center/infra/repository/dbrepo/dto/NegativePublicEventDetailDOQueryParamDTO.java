package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class NegativePublicEventDetailDOQueryParamDTO {

    /**
     * 事件ID范围查询
     */
    @InQuery(field = "eventId")
    private List<String> eventIdList;
    
    /**
     * 是否删除,如果需要不关注删除状态时，需要修改改字段为NULL
     */
    @Builder.Default
    private Boolean isDeleted = false;
}
