package com.sankuai.wallemonitor.risk.center.infra.utils.compare;


import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.constant.EntityKeyConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityFieldChangeRecordDTO {

    /**
     * 更新字段
     */
    private String key;

    /**
     * 更新前的值
     */
    private String before;

    /**
     * 更新后的值
     */
    private String after;

    /**
     * 变更前的值
     */
    private Object beforeOriginal;

    /**
     * 变更后的值
     */
    private Object afterOriginal;

    /***
     * 获取值的变更类型
     * @return
     */

    public String getChangeType() {
        if (StringUtils.isBlank(before) && StringUtils.isNotBlank(after)) {
            return EntityKeyConstant.ENTITY_EMPTY_TO_VALUE;
        } else if (StringUtils.isNotBlank(before) && StringUtils.isNotBlank(after)) {
            return EntityKeyConstant.ENTITY_VALUE_CHANGE;
        } else if (StringUtils.isNotBlank(before) && StringUtils.isBlank(after)) {
            return EntityKeyConstant.ENTITY_VALUE_TO_EMPTY;
        }
        return CharConstant.CHAR_EMPTY;
    }


    public boolean isChanged() {
        return StringUtils.isNotBlank(getChangeType());
    }
}
