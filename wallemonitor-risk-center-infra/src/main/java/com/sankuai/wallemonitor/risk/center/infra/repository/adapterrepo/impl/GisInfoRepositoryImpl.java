package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl;

import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.GisAdapter;
import com.sankuai.wallemonitor.risk.center.infra.convert.GisInfoVTO2DOConvert;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GisInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.GisInfoRepository;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GisInfoRepositoryImpl implements GisInfoRepository {

    @Resource
    private GisInfoVTO2DOConvert convert;
    @Resource
    private GisAdapter gisAdapter;

    /**
     * 根据经纬度查询地理信息
     *
     * @param position
     * @return
     */
    @Override
    public GisInfoDO queryByPosition(PositionDO position) {
        return convert.toDO(gisAdapter.getGisInfo(position));
    }
}
