package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseParkingPlotRelation;

/**
 * 泊车失败事件与泊位ID关联表 Mapper 接口
 */
public interface RiskCaseParkingPlotRelationMapper extends CommonMapper<RiskCaseParkingPlotRelation> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskCaseParkingPlotRelation> getPOClass() {
        return RiskCaseParkingPlotRelation.class;
    }
} 