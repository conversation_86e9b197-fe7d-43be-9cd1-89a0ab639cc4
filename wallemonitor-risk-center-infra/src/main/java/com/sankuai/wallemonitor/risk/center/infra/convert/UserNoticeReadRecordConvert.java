package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.UserNoticeReadRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.UserNoticeReadRecordDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface UserNoticeReadRecordConvert extends SingleConvert<UserNoticeReadRecord, UserNoticeReadRecordDO> {

}
