package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SafetyAreaQueryParamDTO {

    /**
     * 创建时间范围
     */
    @GreatTo(field = "createTime")
    private Date createTimeCreateTo;

    /**
     * 区域id
     */
    @InQuery(field = "areaId")
    private List<String> areaIdList;


    /**
     * 创建时间范围
     */
    @InQuery(field = "type")
    private List<String> typeList;
    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean isDeleted = false;
}