package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter;

import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common.CommonMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCheckingQueueItem;

/**
 * <p>
 * 风险检查队列表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-10-10
 */
public interface RiskCheckingQueueMapper extends CommonMapper<RiskCheckingQueueItem> {

    /**
     * 获取mapper泛型参数
     */
    @Override
    default Class<RiskCheckingQueueItem> getPOClass() {
        return RiskCheckingQueueItem.class;
    }
}
