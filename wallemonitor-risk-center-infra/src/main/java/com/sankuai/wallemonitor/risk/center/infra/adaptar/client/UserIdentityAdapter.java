package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.req.QueryEmpIdentityByEmpIdListReq;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.resp.QueryEmpIdentityByEmpIdListResp;
import com.sankuai.walleeve.utils.JacksonUtils;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/14
 */
@Slf4j
@Component
public class UserIdentityAdapter {

    private static final int SUCCESS_CODE = 0;

    @Resource
    private DxService dxService;

    @Resource
    private XmServiceAdapter xmServiceAdapter;

    public Map<Long, String> getMisByEmpId(List<Long> empIdList) {
        if (CollectionUtils.isEmpty(empIdList)) {
            return Collections.emptyMap();
        }
        try {
            String token = xmServiceAdapter.getToken();
            if (StringUtils.isBlank(token)) {
                return Collections.emptyMap();
            }
            QueryEmpIdentityByEmpIdListReq req = new QueryEmpIdentityByEmpIdListReq();
            req.setEmpIdList(empIdList);
            QueryEmpIdentityByEmpIdListResp resp = dxService.queryEmpIdentityByEmpIdList(token, req);
            log.info("queryUidByEmpIdList resp: {}", JacksonUtils.to(resp));
            if (resp == null || resp.getStatus() == null || resp.getStatus().getCode() != SUCCESS_CODE
                    || resp.getData() == null || MapUtils.isEmpty(resp.getData().getData())) {
                return Collections.emptyMap();
            }
            return resp.getData().getData().entrySet().stream()
                    .filter(entry -> entry.getValue() != null && StringUtils.isNotBlank(entry.getValue().getMis()))
                    .collect(Collectors.toMap(
                            entry -> entry.getKey(),
                            entry -> entry.getValue().getMis(),
                            (v1, v2) -> v1));
        } catch (Exception e) {
            log.error("getMisByEmpId error, empIdList:{}", empIdList, e);
            return Collections.emptyMap();
        }
    }
}
