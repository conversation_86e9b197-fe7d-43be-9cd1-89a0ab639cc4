package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import java.util.List;

/**
 * 风险事件仓储
 */
public interface RiskCaseRepository {


    /**
     * 根据参数查询风险事件
     *
     * @param paramDTO
     * @return
     */
    List<RiskCaseDO> queryByParam(RiskCaseDOQueryParamDTO paramDTO);


    /**
     * 根据事件id获取风险
     *
     * @return
     */
    RiskCaseDO getByEventId(String eventId);

    /**
     * 根据参数查询风险事件 (分页)
     *
     * @param paramDTO
     * @return
     */
    Paging<RiskCaseDO> queryByParamByPage(RiskCaseDOQueryParamDTO paramDTO, Integer pageNum, Integer pageSize);

    /**
     * 根据风险id查询风险事件
     *
     * @param caseId
     * @return
     */
    RiskCaseDO getByCaseId(String caseId);

    /**
     * 保存风险事件
     *
     * @param riskCaseDO
     */
    void save(RiskCaseDO riskCaseDO);

    /**
     * 批量保存风险事件
     *
     * @param riskCaseDOList
     */
    void batchSave(List<RiskCaseDO> riskCaseDOList);


}
