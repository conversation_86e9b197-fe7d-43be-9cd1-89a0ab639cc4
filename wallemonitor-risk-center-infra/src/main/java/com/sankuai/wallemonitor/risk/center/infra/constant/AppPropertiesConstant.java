package com.sankuai.wallemonitor.risk.center.infra.constant;


/**
 * lion key 维护类
 */
public class AppPropertiesConstant {

    public static final String CONFIG_VALUE_EVE_DOMAIN = "${domain.eve}";

    public static final String CONFIG_VALUE_MAP_DOMAIN = "${domain.map}";

    public static final String CONFIG_VALUE_EVE_RISK_DOMAIN = "${domain.eve_risk}";

    public static final String CONFIG_VALUE_MONITOR_BACKUP_VIDEO_URL = "${domain.monitor}";

    public static final String CONFIG_VALUE_MONITOR_BACKUP_VIDEO_DETAIL_URL = "${domain.monitor_detail}";

    public static final String CONFIG_VALUE_MAP_KEY = "$KMS{lbsmap_key}";

    /**
     * rpc服务对应的地图平台key
     */
    public static final String CONFIG_VALUE_RPC_MAP_KEY = "$KMS{rpc_map_key}";

    /**
     * 用户鉴权加密密钥
     */
    public static final String WECHAT_AUTH_SECRET = "${wechat.secret}";


    /**
     * 高精地图域名
     */
    public static final String CONFIG_VALUE_HD_MAP_DOMAIN = "${domain.hd_map}";

    /**
     * 高精地图域名2
     */
    public static final String CONFIG_VALUE_AD_MAP_DOMAIN = "${domain.ad_map}";

    /**
     * 高精地图的secret
     */
    public static final String CONFIG_VALUE_HD_MAP_CLIENT_SECRET = "$KMS{hd_client_secret}";

    /**
     * 高精地图的clientId
     */
    public static final String CONFIG_VALUE_HD_MAP_CLIENT_ID = "${hdMap.clientId}";

    /**
     * fc平台的secret 的 key
     */
    public static final String CONFIG_VALUE_FC_BA_SECRET_KEY = "$KMS{fc.ba.secret.key}";

    /**
     * fc平台的access key
     */
    public static final String CONFIG_VALUE_FC_BA_ACCESS_KEY = "$KMS{fc.ba.access.key}";

    public static final String CONFIG_VALUE_FC_DOMAIN = "${domain.fc}";
}
