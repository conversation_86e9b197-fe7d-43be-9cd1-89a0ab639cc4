package com.sankuai.wallemonitor.risk.center.infra.constant;


/**
 * lion key 维护类
 */
public class LionKeyConstant {


    // 时间阈值
    public static final String LION_KEY_ALERT_UPGRADE_CARNE_QUERY_TIME_THRESHOLD = "risk.alert.upgrade.carne.query.time.threshold";


    public static final String LION_KEY_DOMAIN_EVENT_CONFIG = "risk.domain.event.config";


    public static final String LION_KEY_AUTO_CAR_EVENT_CODE = "risk.center.autocar.eventcode.whitelist";

    public static final String LION_KEY_RISK_CASE_BROADCAST_CONFIG = "case.broadcast.strategy.config";

    public static final String LION_KEY_BROADCAST_CALC_CONFIG = "case.broadcast.calc.config";

    public static final String LION_KEY_LOCK_EXPIRE_SECOND = "batchLock.expireSeconds";
    public static final String LION_KEY_LOCK_SLEEP_MILL_SECOND = "batchLock.sleepRetryMilliseconds";
    public static final String LION_KEY_WARN_EXCEPTION = "warn.exception.list";

    public static final String LION_FILTER_LIST = "log.filter.list";

    public static final String LION_KEY_RISK_QUEUE_CONFIG = "risk.case.improperStranding.checkingQueue.config";

    public static final String LION_KEY_VEHICLE_GRAY_STRATEGY_CONFIG = "vehicle.gray.strategy.config";

    /**
     * 风控服务呼叫云控过滤策略
     */
    public static final String LION_KEY_CALL_MRM_STRATEGY_CONFIG = "risk.case.call.mrm.strategy.config";

    /**
     * 风控服务解除呼叫云控策略
     */
    public static final String LION_KEY_RELEASE_MRM_STRATEGY_CONFIG = "risk.case.release.mrm.strategy.config";

    /**
     * 风险事件定时上报云控查询时间范围
     */
    public static final String LION_KEY_RISK_CALL_MRM_QUERY_TIME_RANGE = "risk.case.call.mrm.query.time.range";

    /**
     * 风险事件定时上报保障系统时间范围
     */
    public static final String LION_KEY_RISK_CALL_SECURITY_SYSTEM_TIME_RANGE = "risk.case.call.security.system.query.time.range";

    /**
     * 车辆风险状态分类配置
     */
    public static final String LION_KEY_RISK_VEHICLE_STATUS_CATEGORY = "risk.case.vehicle.risk.status.category.config";

    /**
     * 停滞不前呼叫云控车辆vin灰度列表
     */
    public static final String LION_KEY_RISK_VEHICLE_VIN_GRAY_LIST = "risk.case.vehicle.vin.gray.list";

    /**
     * 车辆风险状态查询时间范围
     */
    public static final String LION_KEY_VEHICLE_RISK_STATUS_QUERY_TIME_RANGE = "risk.case.vehicle.status.query.time.range";

    /**
     * 业务车用车目的集合
     */
    public static final String LION_KEY_BUSINESS_VEHICLE_PURPOSE = "risk.case.business.vehicle.purpose";

    /**
     * 业务车停滞不前呼叫坐席开关
     */
    public static final String LION_KEY_RISK_CASE_BUSINESS_VEHICLE_CALL_SWITCH = "risk.case.business.vehicle.call.switch";

    /**
     * 停滞不前事件呼叫坐席开关
     */
    public static final String LION_KEY_RISK_CASE_STAGNATION_CALL_SWITCH = "risk.case.stagnation.call.switch";

    /**
     * 状态监控停滞不前入大模型检查队列灰度开关
     */
    public static final String LION_KEY_RISK_CASE_STAGNATION_CHECK_QUEUE_SWITCH = "risk.case.improperStranding.checkingQueue.enabled";

    /**
     * 风险等级配置
     */
    public static final String LION_KEY_RISK_LEVEL_CONFIG = "risk.level.config.config";

    /**
     * 车辆上下文积累配置
     */
    public static final String LION_KEY_VEHICLE_RUNTIME_COUNTER_CONFIG = "vehicle.runtime.counter.config";

    /**
     * 风险等级配置
     */
    public static final String LION_RISK_DETECT_AREA_FILTER = "risk.area.filter.config";

    /**
     * 风险自动打标配置
     */
    public static final String LION_KEY_RISK_AUTO_MARK_CONFIG = "risk.case.improperStranding.autoMark.config";


    /**
     * fridayConfig
     */
    public static final String LION_KEY_FRIDAY_CONFIG = "risk.friday.meta.config";

    /**
     * 停车场检测action中判断车辆处于无控制的时长
     */
    public static final String LION_KEY_IS_IN_PARKING_AREA_IN_NO_CONTROL_DURATION = "risk.action.isInParkingArea.inNoControl.duration";

    /**
     * 取消呼叫坐席的判断条件配置
     */
    public static final String LION_KEY_CANCEL_CALL_MRM_CONDITION_CONFIG = "risk.case.cancelCallMrmCondition.config";

    /**
     * 取消呼叫保障系统判断条件配置
     */
    public static final String LION_KEY_CANCEL_SECURITY_SYSTEM_CONFIG = "risk.case.release.security.system.config";

    /**
     * 扫码挪车相关配置LL
     */
    public static final String LION_KEY_MOVE_CAR_EVENT_CONFIG = "risk.moveCar.event.config";

    /**
     * 风控过滤action配置
     */
    public static final String LION_KEY_RISK_FILTER_ACTION_CONFIG = "risk.common.filter.action.config";

    /**
     * 风控服务呼叫云安全配置
     */
    public static final String LION_KEY_HIGH_NEGATIVE_CALL_SAFETY_CONFIG = "high.negative.case.call.safety.config";

    /**
     * 高负向风险快传配置
     */
    public static final String LION_KEY_HIGH_NEGATIVE_CASE_FAST_UPLOAD_AUTO_CAR_DATA_CONFIG = "high.negative.case.fastUploadAutoCarData.config";


    /**
     * 地图元素的查询配置
     */
    public static final String LION_KEY_HD_MAP_ELEMENT_QUERY_CONFIG = "hd.map.query.element.config";

    /**
     * 关联工单定时任务配置
     */
    public static final String LION_RELATED_WORKSTATION_CRANE_CONFIG = "risk.related.workstation.crane.config";

    /**
     * 停滞事件检测配置
     */
    public static final String LION_KEY_STRANDING_DETECT_CONFIG = "risk.detector.stranding.config";

    /**
     * 禁停区检测配置
     */
    public static final String LION_KEY_RESTRICTED_PARKING_DETECT_CONFIG = "risk.detector.restrictedParking.config";

    /**
     * 起步超时
     */
    public static final String LION_KEY_LAUNCH_TIMEOUT_CONFIG = "risk.detector.launchTimeout.config";

    /**
     * case 工作台消息消费配置
     */
    public static final String LION_KEY_RISK_WORKSTATION_MSG_CONSUMER_CONFIG = "risk.workstation.msg.consumer.config";

    /**
     * 红绿灯相关配置
     */
    public static final String TRAFFIC_LIGHT_CONFIG = "traffic.light.config";
    public static final String LION_KEY_RISK_RAPTOR_REPORT_CONFIG = "risk.raptor.report.config";

    /**
     * 负外部性时间状态机流转规则配置
     */
    public static final String LION_KEY_STATE_MACHINE_TRANSITION_RULE_CONFIG = "negative.public.event.stateMachine.transition.rule";

    /**
     * 负外部性事件大象群创建相关配置
     */
    public static final String LION_KEY_NEGATIVE_PUBLIC_EVENT_DX_GROUP_CONFIG = "negative.public.event.dx.group.config";

    /**
     * 负外部性事件自动外呼相关配置
     */
    public static final String LION_KEY_NEGATIVE_PUBLIC_EVENT_AUTO_CALL_CONFIG = "negative.public.event.auto.call.config";

    /**
     * 负外部性事件定时任务配置
     */
    public static final String LION_KEY_NEGATIVE_PUBLIC_EVENT_CRANE_CONFIG = "negative.public.event.crane.config";

    public static final String LION_KEY_RISK_CASE_TRANSFER_MARK_SWITCH = "risk.case.transfer.mark.switch";

    /**
     * topic 2 class
     */
    public static final String LION_KEY_ONBOARD_MESSAGE_TOPIC_2_CLASS = "onboard.message.topic.to.class";
    /**
     * 障碍物保留距离
     */
    public static final String LION_KEY_OBSTACLE_RESERVE_DISTANCE = "obstacle.reserve.distance";

    /**
     * 云控状态变更触发逻辑
     */
    public static final String LION_KEY_MRM_STATUS_CHANGE_TRIGGER = "mrm.status.change.trigger";

    /**
     * 等待排队配置
     */
    public static final String WAITING_IN_QUEUE_CONFIG_KEY = "waiting.in.queue.config";

    /**
     * 更新MRM处理状态定时任务配置
     */
    public static final String LION_KEY_UPDATE_MRM_PROCESS_STATUS_CRANE_CONFIG = "risk.updateMrmProcessStatusCrane.config";

    /**
     * 计算云控坐席介入时间领域处理器配置
     */
    public static final String LION_KEY_CALCULATE_SEAT_INTERVENTION_TIME_PROCESS_CONFIG = "risk.calculateSeatInterventionTimeProcess.config";

    /**
     * 已处理案件信息更新定时任务配置
     */
    public static final String LION_KEY_DISPOSED_CASE_INFO_UPDATE_CRANE_CONFIG = "risk.disposedCaseInfoUpdateCrane.config";

    /**
     * 切电action配置
     */
    public static final String LION_KEY_RISK_IS_SWITCH_POWER_CONFIG = "risk.iSSwitchPower.config";

    /**
     * 标记分组
     */
    public static final String LION_GROUP_MARK = "multi_mark";

    /**
     * 多版本
     */
    public static final String LION_KEY_MULTI_VERSION = "multi.auto.mark.version";
    /**
     * 随机森林预测配置
     */
    public static final String LION_KEY_RANDOM_FOREST_PREDICT_CONFIG = "risk.randomForest.predict.config";

    /**
     *
     *  呼叫保障系统的配置
     * */
    public static final String CALL_SECURITY_SYSTEM_STRATEGY_CONFIG = "call.security.system.strategy.config";


    /**
     * 呼叫保障系统的开关
     * */
    public static final String RISK_CASE_SECURITY_SYSTEM_CALL_SWITCH =  "risk.case.security.system.call.switch";

    /**
     * 呼叫保障系统的车辆开关
     * */
    public static final String RISK_CASE_SECURITY_SYSTEM_VIN_GRAY_LIST = "risk.case.security.system.vin.gray.list";

    /**
     * 错误排队检测器中前方障碍物判定配置
     */
    public static final String LION_KEY_ERROR_QUEUE_DETECTOR_OBSTACLE_JUDGE_CONFIG = "error.queue.detector.obstacle.judge.config";

    /**
     * 障碍物详情配置
     */
    public static final String OBSTACLE_ABSTRACT_CONFIG_KEY = "obstacle.abstract.config";

    /**
     * 检测器保存快照时忽略字段
     */
    public static final String LION_KEY_DETECTOR_IGNORE_FIELDS = "risk.detector.save.ignore.fields";

    /**
     * 长等待区域配置字段
     * */
    public static final String LONG_WAIT_AREA_CONFIG_KEY = "long.wait.area.config";

    /**
     * 长等待区域淘汰策略配置字段
     * */
    public static final String LONG_WAIT_AREA_ELIMINATION_CONFIG_KEY = "long.wait.area.elimination.config";


    /**
     * 堵路事件处理时间阈值
     * */
    public static final String TRAFFIC_BLOCK_EVENT_PROCESS_TIME_THRESHOLD = "traffic.block.event.process.time.threshold";

    /**
     * 退控取消呼叫云控开关
     * */
    public static final String RISK_MRM_CONSUMER_DISPOSED_SWITCH = "risk.mrm.consumer.disposed.switch";

    /**
     * mrm系统云控退控时的取消呼叫配置
     * */
    public static final String RISK_CASE_RELEASE_MRM_DISPOSED_CONFIG = "risk.case.release.mrm.disposed.config";

    /**
     * 优化停车场区域判断的三元开关Key
     * */
    public static final String PARKING_AREA_RUNTIME_METHODS_SWITCH = "parking.area.runtime.methods.switch";
    /**
     * 消息延迟时间
     */
    public static final String LION_ONBOARD_MESSAGE_LATER_SECONDS = "onboard.message.later.seconds";

    /**
     * Friday SFT 分类配置
     * */
    public static final String SFT_FRIDAY_VIDEO_CLASSIFY = "sft.friday.video.classify.config";

     /**
      * 聚合告警配置
      * */
    public static final String LION_KEY_AGGREGATE_ALERT_CONFIG = "risk.case.aggregate.alert.config";

    /**
     * 风险告警升级配置
     */
    public static final String LION_KEY_ALERT_UPGRADE_CONFIG = "risk.alert.upgrade.config";

    /**
     * 云分诊用车目的拦截挪车灰度配置
     */
    public static final String SEND_CLOUD_TRIAGE_GRAY_CONFIG = "send.cloud.triage.gray.config";
}
