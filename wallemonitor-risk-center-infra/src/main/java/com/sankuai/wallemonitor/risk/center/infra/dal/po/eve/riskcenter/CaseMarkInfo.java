package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2024/7/2
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("case_mark_info")
public class CaseMarkInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * caseId
     */
    @TableField("case_id")
    @TableUnique
    private String caseId;

    /**
     * 风险等级
     */
    @TableField("risk_level")
    private Integer riskLevel;

    /**
     * 类别，1-GOOD | 2-BAD
     */
    @TableField("category")
    private String category;

    /**
     * 子类别
     */
    @TableField("sub_category")
    private String subCategory;

    /**
     * 最近操作人misId
     */
    @TableField("last_operator")
    private String lastOperator;

    /**
     * 首次操作人misId
     */
    @TableField("first_operator")
    private String firstOperator;

    /**
     * 标注轮次
     */
    @TableField("round")
    private Integer round;

    /**
     * 首次标注分类（一般是策略输出）
     */
    @TableField("first_category")
    private String firstCategory;

    /**
     * 首次标注子类别（一般是策略输出）
     */
    @TableField("first_sub_category")
    private String firstSubCategory;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最近更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 拓展信息
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 停滞不当原因
     */
    @TableField("improper_stranding_reason")
    private String improperStrandingReason;


    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;


}

