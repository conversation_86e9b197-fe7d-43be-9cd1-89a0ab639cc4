package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum LineType {
    YELLOW_SOLID_LINE,
    WHITE_SOLID_LINE,
    NONE;

    public static LineType getByValue(String drivingOnTrafficLineType) {
        return Arrays.stream(LineType.values()).filter(e -> e.name().equals(drivingOnTrafficLineType)).findFirst()
                .orElse(null);
    }
}