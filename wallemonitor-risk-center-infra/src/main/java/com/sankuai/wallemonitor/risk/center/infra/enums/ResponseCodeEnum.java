package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.sankuai.walleeve.commons.enums.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 0-500 系统级别 600 - 700 放货员
 */

@AllArgsConstructor
@Getter
public enum ResponseCodeEnum implements ErrorCode {

    // 注意非必要前端会特殊判断特别code数字的话此处不做新增，提示语自己传入代替默认提示语即可

    // 只要出现一次就要研发介入的异常
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    SYSTEM_ERROR(501, "系统错误"),

    // 业务异常，正常会频繁出现，需要按照比例监控
    UNABLE_GET_LOCK(20001, "其他人正在操作，请稍等重试"),
    NOW_DATA_NOT_MATCH(20002, "当前数据状态不满足，请刷新后重试"),
    PARAM_INPUT_ERROR(20003, "输入参数有误，请重试"),
    REMOTE_ERROR(20004, "调用三方服务失败，请稍后重试"),

    // 骑手(driver)异常错误
    RIDER_NOT_FOUND(9001, "骑手不存在"),
    RIDER_LOCATION_NOT_FOUND(9002, "未查到骑手位置信息"),
    RIDER_NOT_ONLINE(9003, "骑手未上线"),

    RIDER_MAX_LIMIT(9004, "骑手已达背单上线"),

    DISPATCH_UNKNOWN_ERROR(1600000, "调度分单过程未识别错误"),
    POLLING_TIME_OUT(4000000, "轮询超时"),

    // 扫码挪车
    CHECK_VEHICLE_ID_ERROR(10, "check vehicleId error"),
    MOVE_CAR_REPEAT(11, "move car repeat"),
    REPORT_TIME_REACH_LIMIT(12, "the number of reports reached the upper limit"),
    BAD_REQUEST(3, "bad request"),

    // 创建文档
    CREATE_CONTENT_ERROR(60000, "创建文档失败")
    ;

    /**
     * 返回码
     */
    private int code;

    /**
     * 返回信息
     */
    private String message;

    public static ResponseCodeEnum getByErrorCode(int errorCode) {
        for (ResponseCodeEnum value : ResponseCodeEnum.values()) {
            if (value.getCode() == errorCode) {
                return value;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
