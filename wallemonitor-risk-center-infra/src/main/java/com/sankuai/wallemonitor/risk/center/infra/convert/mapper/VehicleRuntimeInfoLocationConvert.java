package com.sankuai.wallemonitor.risk.center.infra.convert.mapper;

import com.sankuai.wallemonitor.risk.center.infra.convert.SingleConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.VehicleRuntimeLocation;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeLocationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface VehicleRuntimeInfoLocationConvert
        extends SingleConvert<VehicleRuntimeLocation, VehicleRuntimeLocationDO> {

    @Override
    @Mapping(source = "location", target = "location", qualifiedByName = "toLocationFromPoint")
    VehicleRuntimeLocationDO toDO(VehicleRuntimeLocation vehicleRuntimeInfoContext);

    @Override
    @Mapping(source = "location", target = "location", qualifiedByName = "toPointFromLocation")
    VehicleRuntimeLocation toPO(VehicleRuntimeLocationDO vehicleRuntimeInfoContextDO);
}
