package com.sankuai.wallemonitor.risk.center.infra.factory.riskdetector;

import com.sankuai.wallemonitor.risk.center.infra.dto.DetectContextDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.DetectRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskLaunchTimeoutRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class RiskLaunchTimeoutRecordFactory extends RiskDetectorRecordFactory<RiskLaunchTimeoutRecordDO> {

    @Resource
    private IDGenerateRepository idGenerateRepository;

    @Override
    public RiskLaunchTimeoutRecordDO init(DetectContextDTO detectContextDTO) {
        VehicleRuntimeInfoContextDO runtimeContextDO = detectContextDTO.toShortVehicleInfoSnapShot();
        String caseId = idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, runtimeContextDO.getVin(),
                RiskCaseSourceEnum.BEACON_TOWER, RiskCaseTypeEnum.LAUNCH_TIMEOUT,
                runtimeContextDO.getLastUpdateTime());
        return RiskLaunchTimeoutRecordDO.builder()
                .tmpCaseId(caseId)
                .type(RiskCaseTypeEnum.LAUNCH_TIMEOUT)
                .vin(runtimeContextDO.getVin())
                .duration(0)
                .status(DetectRecordStatusEnum.PROCESSING)
                .occurTime(runtimeContextDO.getLastUpdateTime())
                .parkingAreaId(Objects.toString(detectContextDTO.get("parkingAreaId"), "")).build();
    }
}