package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 舆情事件详情
 * </p>
 *
 * <AUTHOR> @since 2024-08-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("public_event_detail")
public class PublicEventDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 舆情事件ID（投诉工单ID）
     */
    @TableField("public_event_id")
    @TableUnique
    private Long publicEventId;

    /**
     * 车架号列表
     */
    @TableField("vins")
    private String vins;

    /**
     * 车牌号列表
     */
    @TableField("vehicle_ids")
    private String vehicleIds;

    /**
     * 来源类型
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 实际发生时间
     */
    @TableField("occur_time")
    private Date occurTime;

    /**
     * 舆情类型
     */
    @TableField("type")
    private String type;

    /**
     * 细分类型
     */
    @TableField("sub_type")
    private String subType;

    /**
     * 责任方
     */
    @TableField("responsible_party")
    private String responsibleParty;

    /**
     * 是否发酵[0-否|1-是]
     */
    @TableField("is_fermented")
    private Boolean isFermented;

    /**
     * 处置人
     */
    @TableField("handlers")
    private String handlers;

    /**
     * 投诉描述
     */
    @TableField("description")
    private String description;

    /**
     * 扩展字段
     */
    @TableField("extra")
    private String extra;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


    @TableField("city")
    private String city;

    @TableField("risk_case_id")
    private String riskCaseId;

    @TableField("title")
    private String title;

    @TableField("drive_mode")
    private Integer driveMode;

    @TableField("discovery_type")
    private String discoveryType;

    @TableField("handle_type")
    private int handleType;

    @TableField("request_help_time")
    private Date requestHelpTime;

    @TableField("start_handle_time")
    private Date startHandleTime;

    @TableField("finish_time")
    private Date finishTime;

    /**
     * 群id
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除[0-未删除|1-已删除]
     */
    @TableField("is_delete")
    private Boolean isDelete;

    /**
     * 类别
     */
    @TableField("category")
    private String category;

}
