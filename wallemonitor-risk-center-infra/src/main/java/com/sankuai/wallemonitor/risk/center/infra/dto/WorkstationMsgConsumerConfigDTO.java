package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WorkstationMsgConsumerConfigDTO {


    /**
     * 风险事件类型列表
     */
    private List<Integer> riskTypeList;


    /**
     * 风险事件来源列表
     */
    private List<Integer> riskSourceList;

    /**
     * 数据源列表
     */
    private List<String> dataSourceList;

    /**
     * case类型列表
     */
    private List<String> caseTypeList;

}
