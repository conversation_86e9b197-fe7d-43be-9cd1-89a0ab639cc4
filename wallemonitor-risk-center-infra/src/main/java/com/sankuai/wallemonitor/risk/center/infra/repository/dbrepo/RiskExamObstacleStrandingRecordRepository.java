package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskExamObstacleStrandingRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskExamObstacleStrandingRecordDOQueryParamDTO;
import java.util.List;

/**
 * 考试障碍物停滞事件检测记录仓储接口
 */
public interface RiskExamObstacleStrandingRecordRepository {

    /**
     * 根据参数查询考试障碍物停滞事件检测记录
     *
     * @param paramDTO 查询参数
     * @return 记录列表
     */
    List<RiskExamObstacleStrandingRecordDO> queryByParam(RiskExamObstacleStrandingRecordDOQueryParamDTO paramDTO);

    /**
     * 根据参数分页查询考试障碍物停滞事件检测记录
     *
     * @param paramDTO 查询参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 分页对象
     */
    Paging<RiskExamObstacleStrandingRecordDO> queryByParamByPage(RiskExamObstacleStrandingRecordDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize);

    /**
     * 根据临时事件ID查询考试障碍物停滞事件检测记录
     *
     * @param tmpCaseId 临时事件ID
     * @return 记录
     */
    RiskExamObstacleStrandingRecordDO getByTmpCaseId(String tmpCaseId);

    /**
     * 保存考试障碍物停滞事件检测记录
     *
     * @param recordDO 记录
     */
    void save(RiskExamObstacleStrandingRecordDO recordDO);

    /**
     * 批量保存考试障碍物停滞事件检测记录
     *
     * @param recordDOList 记录列表
     */
    void batchSave(List<RiskExamObstacleStrandingRecordDO> recordDOList);
} 