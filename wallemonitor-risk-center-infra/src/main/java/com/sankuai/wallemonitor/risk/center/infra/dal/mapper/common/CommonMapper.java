package com.sankuai.wallemonitor.risk.center.infra.dal.mapper.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.BelowTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GeoRangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.JoinFrom;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.LeftJoin;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.Like;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.LimitQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TimeDiff;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.dal.handler.PointTypeHandler;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.CompareEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.LikeTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GeoQueryDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.NumericRange;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.dal.TableUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;


/**
 * 实现了通用查询的mapper
 *
 * @param <T>
 */
@Mapper
public interface CommonMapper<T> extends EnhancedMapper<T> {

    /**
     * 要被忽略的字段
     */
    Set<String> IGNORE_FIELD_NAMES = new HashSet<>(Arrays.asList("serialVersionUID"));

    /**
     * 参数
     */
    String PARAMS = "params";

    /**
     * 额外参数
     */
    String EXT_PARAMS = "extraParams";

    /**
     * 插入sql
     */
    String INSERT_SQL = "INSERT INTO %s (";
    /**
     * 赋值sql
     */
    String VALUES_SQL = ") VALUES (";
    /**
     * 更新sql
     */
    String UPDATE_SQL = ") ON DUPLICATE KEY UPDATE ";

    /**
     * 数量限制
     */
    String LIMIT_TEMPLATE = "LIMIT %d";


    String VALUE_TEMPLATE = "#{params.%s}";

    String TIME_START_TEMPLATE = "#{params.%s.beginDate}";

    String NUM_UPPER_BOUND_TEMPLATE = "#{params.%s.upperBound}";

    String NUM_LOWER_BOUND_TEMPLATE = "#{params.%s.lowerBound}";

    String TIME_END_TEMPLATE = "#{params.%s.endDate}";

    String LIST_VALUE_TEMPLATE = "#{params.%s[%s]}";

    String TABLE_COLUMN_TEMPLATE = "%s.%s";

    String TIME_DIFF_TEMPLATE = "TIMESTAMPDIFF(%s, %s, %s) %s";

    /**
     * 获取table的唯一键列表
     *
     * @return
     */
    default List<String> getTableUniqueList() {
        //获取基础类型
        return TableUtils.getTableUniqueKeyList(getPOClass());

    }

    /**
     * 获取mapper泛型参数
     */
    /**
     * 获取mapper泛型参数的实际类型
     */
    default Class<T> getPOClass() {
        Type[] genericInterfaces = getClass().getGenericInterfaces();
        for (Type genericInterface : genericInterfaces) {
            Type[] thisMapperGenericInterfaces = ((Class)genericInterface).getGenericInterfaces();
            for (Type thisMapperGenericInterface : thisMapperGenericInterfaces) {
                if (thisMapperGenericInterface instanceof ParameterizedType) {
                    ParameterizedType parameterizedType = (ParameterizedType)thisMapperGenericInterface;
                    if (parameterizedType.getRawType() == CommonMapper.class) {
                        return (Class<T>)parameterizedType.getActualTypeArguments()[0];
                    }
                }
            }
        }
        throw new IllegalStateException("无法获取泛型的实际类型");
    }

    /**
     * 支持任意类型的查询
     *
     * @param queryObject
     * @return
     */
    default List<T> queryByParam(Object queryObject) {
        return selectList(queryObject, getPOClass(), new HashMap<>());
    }

    /**
     * 支持分页查询
     *
     * @param queryObject
     * @return
     */
    default Paging<T> queryByParamByPage(Object queryObject, Integer pageNum, Integer pageSize) {
        SystemCheckUtil.isNotNull(pageNum, "分页页数不能为空");
        SystemCheckUtil.isNotNull(pageSize, "分页大小不能为空");
        //设置limit和分页
        PageInfo<T> pageInfo = PageHelper.startPage(pageNum, pageSize)
                .doSelectPageInfo(() -> {
                    selectList(queryObject, getPOClass(), new HashMap<>());
                });
        Paging<T> page = new Paging<>();
        page.setElements(pageInfo.getList());
        page.setTotal(pageInfo.getTotal());
        page.setPageNum(pageInfo.getPageNum());
        page.setPageSize(pageInfo.getPageSize());
        return page;
    }

    /**
     * 根据入参生成 Wrapper，进行selectOne
     *
     * @return
     */
    default T selectByUk(List<UniqueKeyDTO> uniqueKeyDTOList) {
        if (CollectionUtils.isEmpty(uniqueKeyDTOList)) {
            return null;
        }
        // 获取CommonMapper实现类的Type
        Class<T> clazz = getPOClass();
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        //字段
        List<String> fieldList = TableUtils.getTableFieldList(clazz);
        //查询字段
        queryWrapper.select(fieldList.toArray(new String[]{}));
        for (UniqueKeyDTO uniqueKeyDTO : uniqueKeyDTOList) {
            queryWrapper.eq(TableUtils.getTableFieldName(clazz, uniqueKeyDTO.getColumnPOName()),
                    uniqueKeyDTO.getValue());
        }
        return selectOne(queryWrapper);

    }


    /**
     * 更新或者插入
     *
     * @param params
     * @return
     */
    @UpdateProvider(type = SqlProvider.class, method = "insertOrUpdateOnDuplicateKey")
    int save(@Param(PARAMS) T params);


    /**
     * 根据参数构建查询语句
     *
     * @param params
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "produceSelectList")
    List<T> selectList(@Param(PARAMS) Object params, @Param("clazz") Class<T> clazz,
            @Param(EXT_PARAMS) Map<String, Object> extraParams);

    /**
     * 硬删除方法
     *
     * @param params
     * @return
     */
    @DeleteProvider(type = SqlProvider.class, method = "produceHardDelete")
    int hardDelete(@Param(PARAMS) Object params);


    /**
     * sql提供类
     */
    class SqlProvider {

        /**
         * 生成动态插入sql
         *
         * @param paramMap
         */
        @SneakyThrows
        public String insertOrUpdateOnDuplicateKey(Map<String, Object> paramMap) {
            Object po = paramMap.get(PARAMS);
            SystemCheckUtil.isNotNull(po, "sql执行时，插入对象为空");
            //获取表名
            String tableName = TableUtils.getTableName(po.getClass());
            SystemCheckUtil.isNotBlank(tableName, "sql执行时，表名为空");
            StringBuilder insertSql = new StringBuilder(String.format(INSERT_SQL, tableName));
            StringBuilder valuesSql = new StringBuilder(VALUES_SQL);
            StringBuilder updateSql = new StringBuilder(UPDATE_SQL);
            //获取传入类型的
            Field[] fields = ReflectUtils.getNonFinalDeclaredFields(po.getClass(), false);
            boolean allNull = true;
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(po);
                if (value != null && !field.isSynthetic() && !IGNORE_FIELD_NAMES.contains(field.getName())) {
                    allNull = false;
                    String columnName = TableUtils.getTableFieldName(po.getClass(), field.getName());
                    String poFieldName = field.getName();
                    insertSql.append(columnName).append(", ");
                    if (value instanceof Point) {
                        // 特定类型
                        valuesSql.append("ST_GeomFromText(#{" + PARAMS + "." + poFieldName + ",typeHandler="
                                + PointTypeHandler.class.getName() + "}, 4326), ");
                        updateSql.append(columnName).append(" = ST_GeomFromText(#{" + PARAMS + "." + poFieldName
                                + ",typeHandler=" + PointTypeHandler.class.getName() + "}, 4326), ");
                    } else {
                        // 其他类型
                        valuesSql.append("#{" + PARAMS + ".").append(poFieldName).append("}, ");
                        updateSql.append(columnName).append(" = #{" + PARAMS + ".").append(poFieldName).append("}, ");
                    }
                }
            }
            SystemCheckUtil.isFalse(allNull, "全部字段为空，无法处理");
            //构造sql
            insertSql.delete(insertSql.length() - 2, insertSql.length());
            valuesSql.delete(valuesSql.length() - 2, valuesSql.length());
            updateSql.delete(updateSql.length() - 2, updateSql.length());
            return insertSql.append(valuesSql).append(updateSql).toString();
        }

        /**
         * 根据参数查询
         *
         * @return
         */
        public <T> String produceSelectList(Object params, Class<T> clazz, Map<String, Object> extraParams) {
            CheckUtil.isNotNull(clazz, "返回类型为空");
            //获取查询参数的字段(包含父类字段)
            Field[] queryObjectFields = ReflectUtils.getDeclaredFields(params.getClass(), false);
            Field[] fields = ReflectUtils.getDeclaredFields(clazz, false);
            String tableName = TableUtils.getTableName(clazz);
            CheckUtil.isNotBlank(tableName, "表名为空");
            QuerySqlContext querySqlContext = buildQuerySqlContext(clazz, tableName, params,
                    queryObjectFields,
                    fields, extraParams);
            return querySqlContext.getSqlString();

        }

        /**
         * 生成硬删除sql
         *
         * @param paramMap
         * @return
         */
        public String produceHardDelete(Map<String, Object> paramMap) {
            Object params = paramMap.get(PARAMS);
            Class<?> clazz = params.getClass();
            String tableName = TableUtils.getTableName(clazz);
            CheckUtil.isNotBlank(tableName, "表名为空");

            StringBuilder sql = new StringBuilder("DELETE FROM ").append(tableName).append(" WHERE ");
            Field[] fields = ReflectUtils.getDeclaredFields(clazz, false);

            boolean hasCondition = false;
            for (Field field : fields) {
                if (field.isSynthetic()) {
                    continue;
                }
                field.setAccessible(true);
                try {
                    Object value = field.get(params);
                    if (value != null) {
                        String columnName = TableUtils.getTableFieldName(clazz, field.getName());
                        if (hasCondition) {
                            sql.append(" AND ");
                        }
                        sql.append(columnName).append(" = #{").append(PARAMS).append(".").append(field.getName())
                                .append("}");
                        hasCondition = true;
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("生成硬删除SQL时发生异常", e);
                }
            }

            CheckUtil.isTrue(hasCondition, "硬删除条件为空");
            return sql.toString();
        }

        /**
         * 构建查询sql
         *
         * @param queryObject
         * @param queryObjectFields
         * @param tableFields
         * @return
         */
        private <T> QuerySqlContext buildQuerySqlContext(Class<T> resultClass, String tableName,
                Object queryObject,
                Field[] queryObjectFields, Field[] tableFields, Map<String, Object> extraParams) {
            QuerySqlContext context = QuerySqlContext.builder().build();
            StringBuilder fieldSql = context.getFieldSql();
            StringBuilder fromAndJoinSql = context.getJoinSql();
            List<String> orderByAsc = context.getOrderByAsc();
            List<String> orderByDesc = context.getOrderByDesc();
            List<String> whereConditions = context.getWhereConditions();
            //遍历表字段，构建 (字段不可以重复)
            fieldSql.append("SELECT DISTINCT").append("\n");
            for (Field field : tableFields) {
                if (!field.isAnnotationPresent(TableField.class) && !field.isAnnotationPresent(TableId.class)) {
                    continue;
                }
                String fieldName = TableUtils.getTableFieldName(resultClass, field.getName());
                SystemCheckUtil.isNotBlank(fieldName, field.getName() + "对应的列名不可以为空");
                // 原有处理逻辑
                fieldSql.append(tableName).append(".").append(fieldName).append(" AS ").append("`")
                        .append(field.getName()).append("`").append(",").append("\n");
            }
            if (org.apache.commons.lang3.StringUtils.endsWith(fieldSql, ",\n")) {
                //去掉后面的
                fieldSql.delete(fieldSql.length() - 2, fieldSql.length());
            }
            //再设置from
            fromAndJoinSql.append("FROM ").append(tableName).append(CharConstant.CHAR_SPACE).append(" AS ")
                    .append(tableName).append("\n");
            //遍历查询结构
            for (Field field : queryObjectFields) {
                if (field.isSynthetic()) {
                    continue;
                }
                field.setAccessible(true);
                try {
                    Object value = field.get(queryObject);
                    String queryName = field.getName();
                    String thisFieldTableName = tableName;
                    Class targetClass = resultClass;
                    if (value == null) {
                        continue;
                    }
                    if (field.isAnnotationPresent(JoinFrom.class)) {
                        //如果是来自另外的表
                        thisFieldTableName = TableUtils.getTableName(field.getAnnotation(JoinFrom.class).value());
                        //字段所在的类
                        targetClass = field.getAnnotation(JoinFrom.class).value();
                    }
                    //数值
                    StringBuilder thisWhere = new StringBuilder();
                    //取对应的po列名，然后根据
                    if (field.isAnnotationPresent(InQuery.class)) {
                        CheckUtil.isTrue(Collection.class.isAssignableFrom(value.getClass()),
                                "InQuery 标注在非集合类型的字段上");
                        Collection<?> collections = (Collection<?>) value;
                        if (CollectionUtils.isEmpty(collections)) {
                            //集合类型为空
                            continue;
                        }
                        //in 查询
                        String poFieldName = org.apache.commons.lang3.StringUtils.defaultString(
                                field.getAnnotation(InQuery.class).field(), field.getName());
                        String columnName = TableUtils.getTableFieldName(targetClass, poFieldName);
                        thisWhere.append(String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, columnName))
                                .append(" in ")
                                .append("(");
                        for (int i = 0; i < collections.size(); i++) {
                            thisWhere.append(String.format(LIST_VALUE_TEMPLATE, queryName, i)).append(",");
                        }
                        //删除末尾的
                        thisWhere.delete(thisWhere.length() - 1, thisWhere.length());
                        thisWhere.append(")").append("\n");
                        //添加
                    } else if (field.isAnnotationPresent(RangeQuery.class)) {
                        // 范围查询,支持 时间字段 和 数字字段
                        if (field.get(queryObject) instanceof TimePeriod) {
                            TimePeriod timePeriod = (TimePeriod) (field.get(queryObject));
                            SystemCheckUtil.isTrue(timePeriod.checkTimeBetween(), "时间范围查询时，不合法，必须有上下界");
                            //注解field或者入参列表，当做PO字段，驼峰转do
                            String poFieldName = org.apache.commons.lang3.StringUtils.defaultString(
                                    field.getAnnotation(RangeQuery.class).field(), field.getName());
                            String columnName = TableUtils.getTableFieldName(targetClass, poFieldName);
                            SystemCheckUtil.isNotBlank(columnName, "列名不可以为空");
                            thisWhere.append(String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, columnName))
                                    .append(" BETWEEN ")
                                    .append(String.format(TIME_START_TEMPLATE, queryName))
                                    .append(" AND ")
                                    .append(String.format(TIME_END_TEMPLATE, queryName)).append("\n");
                        }
                    } else if (field.isAnnotationPresent(GeoRangeQuery.class)) {
                        if (!(field.get(queryObject) instanceof GeoQueryDO)) {
                            continue;
                        }
                        // 取距离
                        GeoQueryDO geoQueryDO = (GeoQueryDO)field.get(queryObject);
                        Double distance = geoQueryDO.getDistance();
                        // 空间索引查询,支持特定的类型
                        GeoRangeQuery geoQuery = field.getAnnotation(GeoRangeQuery.class);
                        // po字段的名称
                        String poFieldName = org.apache.commons.lang3.StringUtils.defaultIfBlank(geoQuery.field(),
                                field.getName());
                        Polygon extendPolygon = GeoToolsUtil.getBoundingPolygon(geoQueryDO.getPoint(), distance);

                        // 表column字段名称
                        String columnName = TableUtils.getTableFieldName(targetClass, poFieldName);
                        extraParams.put("extendPolygon", GeoToolsUtil.geometry2WKT(extendPolygon));
                        // 处理点到点的距离查询,只支持wgs84
                        thisWhere.append(" ST_Contains(").append(" ST_GeomFromText(")
                                .append("#{extraParams.extendPolygon}").append(", 4326), ")
                                .append(String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, columnName))
                                .append(")");
                    } else if (field.isAnnotationPresent(BelowTo.class)) {
                        //如果是小于
                        //注解field或者入参列表，当做PO字段，驼峰转do
                        String poFieldName = org.apache.commons.lang3.StringUtils.defaultString(
                                field.getAnnotation(BelowTo.class).field(), field.getName());
                        String columnName = TableUtils.getTableFieldName(targetClass, poFieldName);
                        SystemCheckUtil.isNotBlank(columnName, "列名不可以为空");
                        //列的值小于入参
                        thisWhere.append(String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, columnName))
                                .append(" <= ")
                                .append(
                                        String.format(VALUE_TEMPLATE, queryName));
                    } else if (field.isAnnotationPresent(TimeDiff.class)) {
                        if (value == null) {
                            continue;
                        }
                        //如果是时间差
                        TimeDiff timeDiff = field.getAnnotation(TimeDiff.class);
                        TimeUnit unit = timeDiff.unit();
                        String bigField = timeDiff.bigField();
                        String smallField = timeDiff.smallField();
                        CompareEnum compare = timeDiff.compare();
                        CheckUtil.isNotNull(unit, "时间差单位不可为空");
                        CheckUtil.isNotNull(compare, "时间差单位不可为空");
                        CheckUtil.isNotBlank(bigField, "时间差第一个PO字段不可为空");
                        CheckUtil.isNotBlank(smallField, "时间差第二个PO字段不可为空");
                        //得到字段
                        String bigColumnName = TableUtils.getTableFieldName(targetClass, bigField);
                        String smallColumnName = TableUtils.getTableFieldName(targetClass, smallField);
                        CheckUtil.isNotBlank(bigColumnName, "找不到字段名");
                        CheckUtil.isNotBlank(smallColumnName, "找不到字段名");
                        String comparePart = "";
                        switch (compare) {
                            case BETWEEN: {
                                if (!NumericRange.class.isAssignableFrom(value.getClass())) {
                                    //必须是值的界类型
                                    continue;
                                }
                                comparePart = String.format(" BETWEEN %s AND %s",
                                        String.format(NUM_LOWER_BOUND_TEMPLATE, queryName),
                                        String.format(NUM_UPPER_BOUND_TEMPLATE, queryName));
                                break;
                            }
                            case BELOW_TO: {
                                comparePart = String.format(" < %s",
                                        String.format(VALUE_TEMPLATE, queryName));
                                break;
                            }
                            case GREAT_THAN: {
                                comparePart = String.format(" > %s",
                                        String.format(VALUE_TEMPLATE, queryName));
                                break;
                            }
                        }
                        String condition = String.format(TIME_DIFF_TEMPLATE,
                                //时间单位
                                org.apache.commons.lang3.StringUtils.substringBeforeLast(unit.name(), "S")
                                //字段1
                                , String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, smallColumnName)
                                //字段2
                                , String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, bigColumnName),
                                //比较部分
                                comparePart
                        );
                        if (StringUtils.isBlank(condition)) {
                            continue;
                        }
                        whereConditions.add(condition);

                    } else if (field.isAnnotationPresent(GreatTo.class)) {
                        //如果是小于
                        //注解field或者入参列表，当做PO字段，驼峰转do
                        String poFieldName = org.apache.commons.lang3.StringUtils.defaultString(
                                field.getAnnotation(GreatTo.class).field(), field.getName());
                        String columnName = TableUtils.getTableFieldName(targetClass, poFieldName);
                        thisWhere.append(String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, columnName))
                                .append(" >= ")
                                .append(CharConstant.CHAR_SPACE).append(
                                        String.format(VALUE_TEMPLATE, queryName));

                    } else if (field.isAnnotationPresent(LimitQuery.class)) {
                        //如果是限制
                        context.setLimit((Integer) value);
                    } else if (field.isAnnotationPresent(OrderBy.class)) {
                        //如果是排序
                        //注解field或者入参列表，当做PO字段，驼峰转do
                        String poFieldName = org.apache.commons.lang3.StringUtils.defaultString(
                                field.getAnnotation(OrderBy.class).field(), field.getName());
                        String columnName = TableUtils.getTableFieldName(targetClass, poFieldName);
                        SystemCheckUtil.isNotBlank(columnName, "列名不可以为空");
                        //值是排序
                        OrderEnum orderEnum = (OrderEnum) value;
                        if (orderEnum.equals(OrderEnum.ASC)) {
                            orderByAsc.add(String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, columnName));
                        } else {
                            orderByDesc.add(String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, columnName));
                        }
                    } else if (field.isAnnotationPresent(LeftJoin.class)) {
                        LeftJoin leftJoin = field.getAnnotation(LeftJoin.class);
                        Class<?> leftJoinClass = leftJoin.table();
                        if (!(Boolean) value) {
                            //无需连表
                            continue;
                        }
                        String joinTableColumnName = TableUtils.getTableFieldName(leftJoinClass, leftJoin.field());
                        String tableColumnName = TableUtils.getTableFieldName(resultClass, leftJoin.field());
                        String joinTableName = TableUtils.getTableName(leftJoinClass);
                        //如果是联表，设
                        //LEFT JOIN XXXX as XXXX
                        fromAndJoinSql.append("LEFT JOIN ").append(joinTableName)
                                .append(" AS ").append(joinTableName).append("\n");
                        //ON ( XXX.XXX = XXXX.XX )
                        fromAndJoinSql.append("ON ( ").append(tableName).append(CharConstant.CHAR_DH)
                                .append(tableColumnName).append(CharConstant.CHAR_SPACE).append(" = ")
                                .append(joinTableName).append(CharConstant.CHAR_DH).append(joinTableColumnName)
                                .append(" )")
                                .append("\n");
                    } else if (field.isAnnotationPresent(Like.class)) {
                        Like likeCondition = field.getAnnotation(Like.class);
                        if (likeCondition.value() == null) {
                            continue;
                        }
                        String leftChar = BooleanUtils.toString(
                                likeCondition.value().equals(LikeTypeEnum.LEFT)
                                        || likeCondition.value().equals(LikeTypeEnum.ALL),
                                CharConstant.CHAR_PERCENT, CharConstant.CHAR_EMPTY);
                        String rightChar = BooleanUtils.toString(
                                likeCondition.value().equals(LikeTypeEnum.RIGHT)
                                        || likeCondition.value().equals(LikeTypeEnum.ALL),
                                CharConstant.CHAR_PERCENT, CharConstant.CHAR_EMPTY);
                        // 如果是匹配方式
                        String poFieldName = org.apache.commons.lang3.StringUtils
                                .defaultString(field.getAnnotation(Like.class).field(), field.getName());
                        String columnName = TableUtils.getTableFieldName(targetClass, poFieldName);
                        String valueName = String.format(VALUE_TEMPLATE, queryName);
                        thisWhere.append(String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, columnName))
                                .append(" LIKE ").append(" CONCAT( '").append(leftChar).append("',").append(valueName)
                                .append(",'").append(rightChar).append("' )");
                    } else {
                        //不加注解，使用入参列名对应的值，作为PO字段，作为条件字段
                        String columnName = TableUtils.getTableFieldName(targetClass, field.getName());
                        SystemCheckUtil.isNotBlank(columnName, "列名不可以为空");
                        thisWhere.append(String.format(TABLE_COLUMN_TEMPLATE, thisFieldTableName, columnName))
                                .append(" = ")
                                .append(
                                        String.format(VALUE_TEMPLATE, queryName)).append("\n");
                    }
                    if (StringUtils.isNotBlank(thisWhere.toString())) {
                        //如果有条件，则加进去
                        whereConditions.add(thisWhere.toString());
                    }
                } catch (IllegalAccessException e) {
                    SystemCheckUtil.isBlank(e.getMessage(), "通用查询异常");
                }
            }
            CheckUtil.isNotEmpty(whereConditions, "where 条件为空");
            //转换成where 语句
            return context;
        }
    }


    /**
     * 构建查询结果
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class QueryWrapperResult<T> {

        private QueryWrapper<T> queryWrapper;
        private Integer limit;
        private List<String> orderByAsc;
        private List<String> orderByDesc;

        public QueryWrapper<T> queryWithLimitAndOrder() {
            if (queryWrapper == null) {
                return null;
            }
            if (limit != null) {
                queryWrapper.last(String.format(LIMIT_TEMPLATE, limit));
            }
            if (CollectionUtils.isNotEmpty(orderByAsc)) {
                queryWrapper.orderByAsc(orderByAsc.toArray(new String[0]));
            }
            if (CollectionUtils.isNotEmpty(orderByDesc)) {
                queryWrapper.orderByDesc(orderByDesc.toArray(new String[0]));
            }
            return queryWrapper;
        }

        public void check() {
            SystemCheckUtil.isNotNull(queryWrapper, "动态查询时条件为空");
            SystemCheckUtil.isTrue(!queryWrapper.isEmptyOfWhere(), "动态查询时条件为空");
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class QuerySqlContext {

        @Builder.Default
        private StringBuilder fieldSql = new StringBuilder();
        @Builder.Default
        private StringBuilder joinSql = new StringBuilder();
        @Builder.Default
        private List<String> whereConditions = new ArrayList<>();
        @Builder.Default
        private List<String> orderByAsc = new ArrayList<>();
        @Builder.Default
        private List<String> orderByDesc = new ArrayList<>();
        @Builder.Default
        private Integer limit = null;

        /**
         * 生成完整的sql
         *
         * @return
         */
        public String getSqlString() {
            StringBuilder wholeSql = new StringBuilder();
            wholeSql.append(fieldSql).append("\n").append(joinSql).append("\n");
            if (CollectionUtils.isNotEmpty(whereConditions)) {
                //拼接查询条件
                wholeSql.append(" WHERE ").append(org.apache.commons.lang3.StringUtils.join(whereConditions, " AND "));
            }
            if (CollectionUtils.isNotEmpty(orderByAsc) || CollectionUtils.isNotEmpty(orderByDesc)) {
                wholeSql.append(" ORDER BY \n");
                String asc = "";
                String desc = "";
                if (CollectionUtils.isNotEmpty(orderByAsc)) {
                    asc = orderByAsc.stream().map(order -> order + " ASC ")
                            .collect(Collectors.joining(CharConstant.CHAR_DD));
                }
                if (CollectionUtils.isNotEmpty(orderByDesc)) {
                    desc = orderByDesc.stream().map(order -> order + " DESC ")
                            .collect(Collectors.joining(CharConstant.CHAR_DD));
                }
                wholeSql.append(org.apache.commons.lang3.StringUtils.join(
                        Stream.of(asc, desc).filter(StringUtils::isNotBlank).collect(
                                Collectors.toList()), ","));
                wholeSql.append("\n");
            }
            if (limit != null) {
                wholeSql.append(String.format(LIMIT_TEMPLATE, limit)).append('\n');
            }
            return wholeSql.toString();

        }
    }


}
