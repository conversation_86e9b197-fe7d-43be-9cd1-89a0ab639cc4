package com.sankuai.wallemonitor.risk.center.infra.exception.check;


import static com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter.replaceMsg;

import com.sankuai.wallemonitor.risk.center.infra.enums.ResponseCodeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import java.util.Collection;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by xiafei03 on 2022/6/6.
 */
@Slf4j
public class SystemCheckUtil {

    /**
     * 为null，否则抛出异常
     *
     * @param obj
     * @param msg
     */
    public static void isNull(Object obj, ResponseCodeEnum errorEnum, String msg) {
        if (obj != null) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isNull(Object obj, ResponseCodeEnum errorEnum) {
        if (obj != null) {
            throwSysException(errorEnum);
        }
    }

    public static void isNull(Object obj, String msg) {
        if (obj != null) {
            throwSysException(msg);
        }
    }

    /**
     * 不为null，否则抛出异常
     *
     * @param obj
     * @param msg
     */
    public static void isNotNull(Object obj, ResponseCodeEnum errorEnum, String msg) {
        if (obj == null) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isNotNull(Object obj, ResponseCodeEnum errorEnum) {
        if (obj == null) {
            throwSysException(errorEnum);
        }
    }

    public static void isNotNull(Object obj, String msg) {
        if (obj == null) {
            throwSysException(msg);
        }
    }

    public static void isNotNull(Object obj, String msg, String... replace) {
        if (obj == null) {
            throwSysException(replaceMsg(msg, replace));
        }
    }


    /**
     * 字符串不为空，否则抛出异常
     *
     * @param str
     * @param msg
     */
    public static void isNotBlank(String str, ResponseCodeEnum errorEnum, String msg) {
        if (StringUtils.isBlank(str)) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isNotBlank(String str, ResponseCodeEnum errorEnum) {
        if (StringUtils.isBlank(str)) {
            throwSysException(errorEnum);
        }
    }

    public static void isNotBlank(String str, String msg) {
        if (StringUtils.isBlank(str)) {
            throwSysException(msg);
        }
    }

    public static void isNotBlank(String str, String msg, String... replaces) {
        if (StringUtils.isBlank(str)) {
            throwSysException(replaceMsg(msg, replaces));
        }
    }

    /**
     * 字符串为空，否则抛出异常
     *
     * @param str
     * @param msg
     */
    public static void isBlank(String str, ResponseCodeEnum errorEnum, String msg) {
        if (StringUtils.isNotBlank(str)) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isBlank(String str, ResponseCodeEnum errorEnum) {
        if (StringUtils.isNotBlank(str)) {
            throwSysException(errorEnum);
        }
    }

    public static void isBlank(String str, String msg) {
        if (StringUtils.isNotBlank(str)) {
            throwSysException(msg);
        }
    }

    /**
     * false，否则抛出异常
     *
     * @param flag
     * @param msg
     */
    public static void isFalse(boolean flag, ResponseCodeEnum errorEnum, String msg) {
        if (flag) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isFalse(boolean flag, ResponseCodeEnum errorEnum) {
        if (flag) {
            throwSysException(errorEnum);
        }
    }

    public static void isFalse(boolean flag, String msg) {
        if (flag) {
            throwSysException(msg);
        }
    }

    /**
     * true，否则抛出异常
     *
     * @param flag
     * @param msg
     */
    public static void isTrue(boolean flag, ResponseCodeEnum errorEnum, String msg) {
        if (!flag) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isTrue(boolean flag, ResponseCodeEnum errorEnum) {
        if (!flag) {
            throwSysException(errorEnum);
        }
    }

    public static void isTrue(boolean flag, String msg) {
        if (!flag) {
            throwSysException(msg);
        }
    }


    /**
     * 集合为空，否则抛出异常
     *
     * @param coll
     * @param msg
     */
    public static void isEmpty(Collection coll, ResponseCodeEnum errorEnum, String msg) {
        if (CollectionUtils.isNotEmpty(coll)) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isEmpty(Collection coll, ResponseCodeEnum errorEnum) {
        if (CollectionUtils.isNotEmpty(coll)) {
            throwSysException(errorEnum);
        }
    }

    public static void isEmpty(Collection coll, String msg) {
        if (CollectionUtils.isNotEmpty(coll)) {
            throwSysException(msg);
        }
    }

    /**
     * 集合不为空，否则抛出异常
     *
     * @param coll
     * @param msg
     */
    public static void isNotEmpty(Collection coll, ResponseCodeEnum errorEnum, String msg) {
        if (CollectionUtils.isEmpty(coll)) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isNotEmpty(Collection coll, ResponseCodeEnum errorEnum) {
        if (CollectionUtils.isEmpty(coll)) {
            throwSysException(errorEnum);
        }
    }

    public static void isNotEmpty(Collection coll, String msg) {
        if (CollectionUtils.isEmpty(coll)) {
            throwSysException(msg);
        }
    }

    /**
     * map不为空，否则抛出异常
     *
     * @param map
     * @param msg
     */
    public static void isEmpty(Map map, ResponseCodeEnum errorEnum, String msg) {
        if (MapUtils.isNotEmpty(map)) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isEmpty(Map map, ResponseCodeEnum errorEnum) {
        if (MapUtils.isNotEmpty(map)) {
            throwSysException(errorEnum);
        }
    }

    public static void isEmpty(Map map, String msg) {
        if (MapUtils.isNotEmpty(map)) {
            throwSysException(msg);
        }
    }

    /**
     * map不为空，否则抛出异常
     *
     * @param map
     * @param msg
     */
    public static void isNotEmpty(Map map, ResponseCodeEnum errorEnum, String msg) {
        if (MapUtils.isEmpty(map)) {
            throwSysException(errorEnum, msg);
        }
    }

    public static void isNotEmpty(Map map, ResponseCodeEnum errorEnum) {
        if (MapUtils.isEmpty(map)) {
            throwSysException(errorEnum);
        }
    }

    public static void isNotEmpty(Map map, String msg) {
        if (MapUtils.isEmpty(map)) {
            throwSysException(msg);
        }
    }

    private static void throwSysException(String msg) {
        throwSysException(null, msg);
    }

    private static void throwSysException(ResponseCodeEnum errorEnum) {
        throw new SystemException(errorEnum);
    }

    private static void throwSysException(ResponseCodeEnum errorEnum, String msg) {
        if (errorEnum == null) {
            errorEnum = ResponseCodeEnum.SYSTEM_ERROR;
        }
        throw new SystemException(errorEnum, msg);
    }

}
