package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.sankuai.meituan.config.util.http.HttpUtils;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WeChatServiceAdaptor {

    /**
     * 小程序AppID
     */
    private static final String APP_ID = "wx935df54eb9a5e08e";

    /**
     * 小程序AppSecret
     */
    private static final String AS = "a273549ffaad9e9a183ec0debfb4b1ab";

    /**
     * 根据登陆凭证获取唯一ID微信接口
     */
    private static final String JS_CODE_2_SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session?appid=";

    /**
     * 根据微信小程序的登录凭证获取用户的会话密钥和OpenID
     *
     * @param code 微信小程序的登录凭证
     * @return 用户的OpenID
     * @throws RemoteErrorException 远程调用错误异常
     */
    public String getSessionKeyAndOpenid(String code) throws RemoteErrorException {
        String url = JS_CODE_2_SESSION_URL + APP_ID + "&secret=" + AS + "&js_code=" + code
                + "&grant_type=authorization_code";
        try {
            // 1 根据code请求用户相关信息
            log.info("getSessionKeyAndOpenid, url = [{}]", url);
            String response = HttpUtils.get(url);
            log.info("getSessionKeyAndOpenid, response = {}", response);

            // 2 解析响应
            String errCode = JacksonUtils.getAsString(response, "errcode");
            if (StringUtils.isNotBlank(errCode)) {
                // 处理错误响应
                throw new RemoteErrorException(JacksonUtils.getAsString(response, "errmsg"));
            }
            return JacksonUtils.getAsString(response, "openid");
        }catch (RemoteErrorException e){
            log.error("getSessionKeyAndOpenid error", e);
            throw new RemoteErrorException(e.getMessage());
        }
        catch (Exception e){
            log.error("getSessionKeyAndOpenid error", e);
            throw new RuntimeException(e);
        }
    }

}
