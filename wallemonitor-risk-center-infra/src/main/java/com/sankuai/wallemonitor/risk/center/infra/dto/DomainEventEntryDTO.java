package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.google.common.base.Joiner;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import java.io.Serializable;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DomainEventEntryDTO implements Serializable {

    /**
     * 领域类,不包含包名
     */
    private String domainClassName;

    /**
     * 处理入口
     */
    private OperateEnterActionEnum operateEntry;

    @Override
    public String toString() {
        return Joiner.on(CharConstant.CHAR_JH).join(domainClassName, operateEntry);
    }

    @Override
    public int hashCode() {
        return Objects.hash(domainClassName, operateEntry);
    }

    @Override
    public boolean equals(Object another) {
        return another != null && Objects.equals(this.toString(), another.toString());
    }

}
