package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 大模型停滞验证结果
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class FridayVerifyResultVTO {

    @Builder.Default
    private Integer tiktokens = 0;

    /**
     * 车辆vin
     */
    private String vin;

    /**
     * 发生时间
     */
    private Date occurTime;

    /**
     * caseId
     */
    private String caseId;

    /**
     * casePlatform
     */
    private String casePlatform;


    /**
     * 识别的物体
     */
    @Builder.Default
    private List<String> frontObjectList = new ArrayList<>();
    @Builder.Default
    private List<String> leftObjectList = new ArrayList<>();
    @Builder.Default
    private List<String> loopObjectList = new ArrayList<>();
    @Builder.Default
    private List<String> rightObjectList = new ArrayList<>();
    @Builder.Default
    private List<String> backObjectList = new ArrayList<>();
    /**
     * 识别的场景
     */
    @Builder.Default
    private List<String> sceneList = new ArrayList<>();
    /**
     * 识别的行为
     */
    @Builder.Default
    private List<String> activityList = new ArrayList<>();

    /**
     * 大模型的输出
     */
    private String content;

    /**
     * 图片链接
     */
    @Builder.Default
    private List<String> imageList = new ArrayList<>();


    public String getActivity() {
        if (CollectionUtils.isEmpty(activityList)) {
            return CharConstant.CHAR_EMPTY;
        }
        return activityList.stream().findFirst()
                .map(s -> StringUtils.substringBefore(s, CharConstant.CHAR_LEFT_PARENTHESIS))
                .orElse(CharConstant.CHAR_EMPTY);
    }

    public String getAllInfo() {
        List<String> resultList = new ArrayList<>();
        resultList.add("图片:'" + imageList.get(0) + "'" + "\n");
        if (CollectionUtils.isNotEmpty(frontObjectList)) {
            resultList.add("前视:" + String.join("#", frontObjectList) + "\n");
        }
        if (CollectionUtils.isNotEmpty(leftObjectList)) {
            resultList.add("左视:" + String.join("#", leftObjectList) + "\n");
        }
        if (CollectionUtils.isNotEmpty(loopObjectList)) {
            resultList.add("环视:" + String.join("#", loopObjectList) + "\n");
        }
        if (CollectionUtils.isNotEmpty(rightObjectList)) {
            resultList.add("右视:" + String.join("#", rightObjectList) + "\n");
        }
        if (CollectionUtils.isNotEmpty(backObjectList)) {
            resultList.add("后视:" + String.join("#", backObjectList) + "\n");
        }
        if (CollectionUtils.isNotEmpty(sceneList)) {
            resultList.add("场景:" + String.join("#", sceneList) + "\n");
        }
        if (CollectionUtils.isNotEmpty(activityList)) {
            resultList.add("行为:" + String.join("#", activityList) + "\n");
        }
        return String.join("^", resultList);
    }

    public void addImageList(List<String> imageList) {
        if (CollectionUtils.isEmpty(imageList)) {
            return;
        }
        this.imageList.addAll(imageList);
    }

    public List<String> getObjectNameByView(String view) {
        List<String> viewObject = new ArrayList<>();
        if (StringUtils.equals(view, "front")) {
            viewObject = frontObjectList;
        } else if (StringUtils.equals(view, "left")) {
            viewObject = leftObjectList;
        } else if (StringUtils.equals(view, "right")) {
            viewObject = rightObjectList;
        } else if (StringUtils.equals(view, "loop")) {
            viewObject = loopObjectList;
        }
        return Optional.ofNullable(viewObject).orElse(new ArrayList<>()).stream()
                .map(objectStr -> StringUtils.substringBefore(objectStr, "("))
                .collect(Collectors.toList());
    }

    public List<String> getSceneName() {
        return this.sceneList.stream().map(objectStr -> StringUtils.substringBefore(objectStr, "("))
                .collect(Collectors.toList());
    }
}
