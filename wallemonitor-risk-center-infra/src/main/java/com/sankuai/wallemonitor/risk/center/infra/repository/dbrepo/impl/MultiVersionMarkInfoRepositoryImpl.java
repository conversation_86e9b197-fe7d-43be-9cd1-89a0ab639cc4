package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.MultiVersionMarkInfoConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.MultiVersionMarkInfoMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.MultiVersionMarkInfo;
import com.sankuai.wallemonitor.risk.center.infra.dto.MultiVersionQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.MultiVersionMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.MultiVersionMarkInfoRepository;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MultiVersionMarkInfoRepositoryImpl extends
        AbstractMapperSingleRepository<MultiVersionMarkInfoMapper, MultiVersionMarkInfoConvert, MultiVersionMarkInfo, MultiVersionMarkInfoDO>
        implements MultiVersionMarkInfoRepository {

    private static final String UK_CASE_ID = "caseId";
    private static final String UK_VERSION = "markVersion";

    /**
     * @param param
     * @return
     */
    @Override
    @RepositoryQuery
    public List<MultiVersionMarkInfoDO> queryByParam(MultiVersionQueryParamDTO param) {
        return super.queryByParam(param);
    }

    /**
     * @param caseId
     * @param version
     * @return
     */
    @Override
    @RepositoryQuery
    public MultiVersionMarkInfoDO getByCaseIdAndVersion(String caseId, String version) {
        return super.getByUniqueId(UK_CASE_ID, caseId, UK_VERSION, version);
    }

    /**
     * 保存
     *
     * @param multiVersionMarkInfoDO
     */
    @Override
    @RepositoryExecute
    public void save(MultiVersionMarkInfoDO multiVersionMarkInfoDO) {
        super.save(multiVersionMarkInfoDO);

    }

    /**
     * 批量保存
     *
     * @param multiVersionMarkInfDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<MultiVersionMarkInfoDO> multiVersionMarkInfDOList) {
        super.batchSave(multiVersionMarkInfDOList);
    }
}
