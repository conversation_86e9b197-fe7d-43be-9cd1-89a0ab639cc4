package com.sankuai.wallemonitor.risk.center.infra.model.core;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 坐席呼叫过滤规则命中纪录表DO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MrmCallFilterRuleHitLogDO implements Serializable {

    /**
     * 自增ID
     */
    private Long id;
    /**
     * case的唯一ID
     */
    private String caseId;
    /**
     * 命中过滤规则
     */
    private String filterRule;
    /**
     * 扩展信息
     */
    private String extInfo;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除
     */
    private Boolean isDeleted;
} 