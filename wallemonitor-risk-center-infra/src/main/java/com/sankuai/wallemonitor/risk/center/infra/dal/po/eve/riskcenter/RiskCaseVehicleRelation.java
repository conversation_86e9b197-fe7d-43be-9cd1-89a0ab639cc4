package com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TableUnique;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 风险事件和车辆关联表
 * </p>
 *
 * <AUTHOR> @since 2024-06-12
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("risk_case_vehicle_relation")
public class RiskCaseVehicleRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * caseId
     */
    @TableField("case_id")
    @TableUnique
    private String caseId;

    /**
     * 外部事件ID
     */
    @TableField("event_id")
    @TableUnique
    private String eventId;

    /**
     * vin车架号
     */
    @TableField("vin")
    @TableUnique
    private String vin;

    /**
     * 保障系统生成的traceId,关联坐席处置状态
     */
    @TableField("trace_id")
    private String traceId;

    /**
     * 车辆故障时快照信息
     */
    @TableField("vehicle_snapshot_info")
    private String vehicleSnapshotInfo;

    /**
     * 云控的控车信息，json字段，包含：控车人misId，控车时间，云控角色
     */
    @TableField("ext_info")
    private String extInfo;

    /**
     * 车辆处置状态,0|初始化,10|待分配,20|预分配,30|已分配,40|已处置,50|已拒绝,99|已取消
     */
    @TableField("status")
    private Integer status;

    /**
     * 车辆时间
     */
    @TableField("occur_time")
    private Date occurTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最近更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 并排开始时间
     */
    @TableField("side_by_side_timestamp")
    private String sideBySideTimestamp;

    /**
     * 请求坐席时间
     */
    @TableField("request_seat_time")
    private Date requestSeatTime;

    /**
     * 取消坐席时间
     */
    @TableField("cancel_seat_time")
    private Date cancelSeatTime;

    /**
     * 坐席连入时间
     */
    @TableField("seat_connect_time")
    private Date seatConnectTime;

    /**
     * 坐席退控时间
     */
    @TableField("seat_exit_time")
    private Date seatExitTime;

    /**
     * 坐席介入时间
     */
    @TableField("seat_intervention_time")
    private Date seatInterventionTime;

    /**
     * 烽火台呼叫坐席连入时间
     */
    @TableField("seat_response_time")
    private Date seatResponseTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;

    /**
     * 车辆事件开始时间戳
     */
    @TableField("milli_begin_time")
    private Date milliBeginTime;

    /**
     * 停滞积累信息
     */
    @TableField("stagnation_counter")
    private String stagnationCounter;

    /**
     * 车辆事件所属类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 用车目的
     */
    @TableField("purpose")
    private String purpose;

    /**
     * vhr模式
     */
    @TableField("vhr_mode")
    private String vhrMode;

    /**
     * 云控呼叫原因
     */
    @TableField("call_mrm_reason")
    private String callMrmReason;

    /**
     * 车型
     */
    @TableField("vehicle_type")
    private String vehicleType;

}
