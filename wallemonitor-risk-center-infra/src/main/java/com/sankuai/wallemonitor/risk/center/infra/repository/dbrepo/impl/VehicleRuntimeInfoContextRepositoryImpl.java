package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.google.common.collect.Lists;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walleeve.domain.page.Paging;
import com.sankuai.walleeve.utils.ReflectUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.SquirrelAdapter;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.applicationcontext.OperateEnterContext;
import com.sankuai.wallemonitor.risk.center.infra.convert.VehicleRuntimeInfoContextConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.VehicleRuntimeInfoContextMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.VehicleRuntimeInfoContext;
import com.sankuai.wallemonitor.risk.center.infra.dto.UniqueKeyDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.SquirrelCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CacheHashDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeInfoContextDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.compare.EntityChangeRecordDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.compare.RecordCompareUtils;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javafx.util.Pair;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

/**
 * 车辆运行时信息仓储实现
 */
@Component
@Slf4j
public class VehicleRuntimeInfoContextRepositoryImpl extends
        AbstractMapperSingleRepository<VehicleRuntimeInfoContextMapper, VehicleRuntimeInfoContextConvert, VehicleRuntimeInfoContext, VehicleRuntimeInfoContextDO> implements
        VehicleRuntimeInfoContextRepository {

    private static final String UK_VIN = "vin";

    @Resource
    private SquirrelAdapter squirrelAdapter;

    @ConfigValue(
            key = "vehicle.runtime.info.fill.by.cache",
            defaultValue = "[\"speed\",\"driveMode\",\"trafficLightType\",\"fenceContext\",\"obstacleContext\",\"obstacleAbstracts\",\"monitorMetricsInfo\",\"distanceToJunction\",\"vehicleCounterInfo\",\"routePoints\",\"fcLaneIdList\"]"
    )
    private Set<String> fillByCache;

    /**
     * 根据参数查询车辆运行时信息
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<VehicleRuntimeInfoContextDO> queryByParam(VehicleRuntimeInfoContextDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 根据参数查询车辆运行时信息 (分页)
     *
     * @param paramDTO
     * @return
     */
    @Override
    public Paging<VehicleRuntimeInfoContextDO> queryByParamByPage(VehicleRuntimeInfoContextDOQueryParamDTO paramDTO,
            Integer pageNum, Integer pageSize) {
        return super.queryPageByParam(paramDTO, pageNum, pageSize);
    }

    /**
     * 根据VIN码查询车辆运行时信息
     *
     * @param vin
     * @return
     */
    @Override
    @RepositoryQuery
    public VehicleRuntimeInfoContextDO getByVin(String vin) {
        return super.getByUniqueId(Lists.newArrayList(UniqueKeyDTO.builder()
                .columnPOName(UK_VIN)
                .value(vin)
                .build()));
    }

    /**
     * 根据VIN码查询车辆运行时信息
     *
     * @param vin
     * @return
     */
    @Override
    @RepositoryQuery
    public VehicleRuntimeInfoContextDO getFullByVin(String vin) {
        return getFullByVin(Collections.singletonList(vin)).stream().findFirst().orElse(null);
    }

    @Override
    @RepositoryQuery
    public List<VehicleRuntimeInfoContextDO> getFullByVin(List<String> vinList) {
        if (CollectionUtils.isEmpty(vinList)) {
            return Collections.emptyList();
        }
        // 从DB
        Map<String, VehicleRuntimeInfoContextDO> vin2FromDbMap = super.queryByParam(
                VehicleRuntimeInfoContextDOQueryParamDTO.builder().vinList(vinList).build()).stream()
                .collect(Collectors.toMap(VehicleRuntimeInfoContextDO::getVin, Function.identity(), (v1, v2) -> v1));
        if (MapUtils.isEmpty(vin2FromDbMap)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(fillByCache)) {
            return new ArrayList<>(vin2FromDbMap.values());
        }
        // 需要填充的字段
        Map<String, Field> needFillField = Arrays
                .stream(ReflectUtils.getDeclaredFields(VehicleRuntimeInfoContextDO.class, false))
                .filter(field -> fillByCache.contains(field.getName()))
                .collect(Collectors.toMap(Field::getName, Function.identity(), (o1, o2) -> o1));
        // 从缓存中取数
        Map<String, Map<String, Object>> vin2FieldValue = this.getFromCache(vinList).stream().map(fromCache -> {
            // 取map的值
            Map<String, Object> fieldValueMap = ReflectUtils.getNonNullFieldAndValue(fromCache);
            // 保留需要的值
            fieldValueMap.keySet().retainAll(needFillField.keySet());
            return new Pair<>(fromCache.getVin(), fieldValueMap);
        }).collect(Collectors.toMap(Pair::getKey, Pair::getValue, (o1, o2) -> o1));
        if (MapUtils.isEmpty(vin2FieldValue)) {
            return new ArrayList<>(vin2FromDbMap.values());
        }
        // 填充
        vin2FromDbMap.forEach((vin, fromDb) -> {
            Map<String, Object> fieldValueMap = vin2FieldValue.get(vin);
            if (MapUtils.isEmpty(fieldValueMap)) {
                // 为空的时候，做处理
                return;
            }
            ReflectUtils.setFieldValueByField(fieldValueMap, VehicleRuntimeInfoContextDO.class, fromDb);
        });
        return new ArrayList<>(vin2FromDbMap.values());
    }

    /**
     * 保存车辆运行时信息
     *
     * @param vehicleRuntimeInfoContextDO
     */
    @Override
    @RepositoryExecute
    public void save(VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO) {
        super.save(vehicleRuntimeInfoContextDO);
    }

    /**
     * 批量保存车辆运行时信息
     *
     * @param vehicleRuntimeInfoContextDOList
     */
    @Override
    @RepositoryExecute
    public void batchSave(List<VehicleRuntimeInfoContextDO> vehicleRuntimeInfoContextDOList) {
        super.batchSave(vehicleRuntimeInfoContextDOList);
    }

    @Override
    @RepositoryQuery
    public VehicleRuntimeInfoContextDO getFromCache(String vin) {
        List<String> fields = Arrays.stream(ReflectUtils.getDeclaredFields(VehicleRuntimeInfoContextDO.class, false))
                .map(Field::getName).collect(Collectors.toList());
        CacheHashDO vehicleRuntimeInfoContextDOCacheHashDO = squirrelAdapter.getHash(
                SquirrelCategoryEnum.VEHICLE_RUNTIME_CONTEXT,
                vin,
                fields);
        //设置
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = VehicleRuntimeInfoContextDO.builder().vin(vin)
                .build();
        ReflectUtils.setFieldValueByField(vehicleRuntimeInfoContextDOCacheHashDO.getData(),
                VehicleRuntimeInfoContextDO.class, vehicleRuntimeInfoContextDO);
        return vehicleRuntimeInfoContextDO;
    }

    @Override
    @RepositoryQuery
    public List<VehicleRuntimeInfoContextDO> getFromCache(List<String> vinList) {
        List<String> fields = Arrays.stream(ReflectUtils.getDeclaredFields(VehicleRuntimeInfoContextDO.class, false))
                .map(Field::getName).collect(Collectors.toList());
        return getDataFromCache(vinList, fields);
    }

    /**
     * 批量查询缓存中车辆运行时状态信息（按需获取）
     *
     * @param vinList
     * @param needFields
     * @return
     */
    @Override
    public List<VehicleRuntimeInfoContextDO> getFromCache(List<String> vinList, List<String> needFields) {
        if (CollectionUtils.isEmpty(needFields)) {
            return Collections.emptyList();
        }
        List<String> fields = Arrays.stream(ReflectUtils.getDeclaredFields(VehicleRuntimeInfoContextDO.class, false))
                .map(Field::getName).filter(needFields::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fields)) {
            return Collections.emptyList();
        }
        return getDataFromCache(vinList, fields);
    }

    private List<VehicleRuntimeInfoContextDO> getDataFromCache(List<String> vinList, List<String> fields) {
        Map<String, CacheHashDO> cacheHashDOMap = squirrelAdapter
                .batchGetHashAsObject(SquirrelCategoryEnum.VEHICLE_RUNTIME_CONTEXT, vinList, fields);
        // 设置
        List<VehicleRuntimeInfoContextDO> runtimeInfoContextDOS = new ArrayList<>();
        vinList.forEach(vin -> {
            VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = VehicleRuntimeInfoContextDO.builder().vin(vin)
                    .build();
            CacheHashDO vehicleRuntimeInfoContextDOCacheHashDO = cacheHashDOMap.get(vin);
            if (vehicleRuntimeInfoContextDOCacheHashDO == null) {
                return;
            }
            ReflectUtils.setFieldValueByField(vehicleRuntimeInfoContextDOCacheHashDO.getData(),
                    VehicleRuntimeInfoContextDO.class, vehicleRuntimeInfoContextDO);
            // 添加
            runtimeInfoContextDOS.add(vehicleRuntimeInfoContextDO);
        });
        return runtimeInfoContextDOS;
    }

    /**
     * 获取过去24小时的
     *
     * @param timePeriod
     * @return
     */
    @Override
    public Set<String> queryFromCache(TimePeriod timePeriod) {
        if (timePeriod == null) {
            return Collections.emptySet();
        }
        Map<String, Long> vin2Timestamp = squirrelAdapter.hAllGet(SquirrelCategoryEnum.VEHICLE_RUNTIME_CONTEXT,
                "ALL_VIN");
        if (MapUtils.isEmpty(vin2Timestamp)) {
            return Collections.emptySet();
        }
        return vin2Timestamp.entrySet().stream()
                //过滤出来满足要求的时间的key
                .filter(entry -> timePeriod.checkTimeBetween(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

    }

    /**
     * 批量存储车辆运行时信息数据到缓存中
     *
     * @param vehicleRuntimeInfoContextDO
     * @return
     */
    @Override
    public void updateCache(VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO, Long timestamp) {
        try {
            //时间被更新
            vehicleRuntimeInfoContextDO.setLastUpdateTime(new Date(timestamp));
            //取当前的数据
            VehicleRuntimeInfoContextDO beforeCacheValue = OperateEnterContext.getDomainAfterQuery(
                    vehicleRuntimeInfoContextDO);
            EntityChangeRecordDTO changeRecordDTO = RecordCompareUtils.findRecordsChange(beforeCacheValue,
                    vehicleRuntimeInfoContextDO, UK_VIN,
                    VehicleRuntimeInfoContextDO.class);
            if (changeRecordDTO == null || MapUtils.isEmpty(changeRecordDTO.getChangeMaps())) {
                return;
            }
            //获取变化的keys
            Set<String> changedField = changeRecordDTO.getChangeMaps().keySet();
            CacheHashDO changedCacheDO = new CacheHashDO();
            Arrays.stream(ReflectUtils.getDeclaredFields(VehicleRuntimeInfoContextDO.class, false))
                    //需要在变更的列里面
                    .filter(field -> changedField.contains(field.getName()))
                    //设置字段的更新时间
                    .forEach(
                            field -> changedCacheDO.addField(field.getName(),
                                    ReflectUtils.getValueByField(field, vehicleRuntimeInfoContextDO), timestamp));
            //保存
            squirrelAdapter.saveHash(SquirrelCategoryEnum.VEHICLE_RUNTIME_CONTEXT,
                    vehicleRuntimeInfoContextDO.getVin(), changedCacheDO
            );
            if (MapUtils.isNotEmpty(changedCacheDO.getAllField())) {
                //更新为当前时间
                squirrelAdapter.hsetObject(SquirrelCategoryEnum.VEHICLE_RUNTIME_CONTEXT,
                        "ALL_VIN", vehicleRuntimeInfoContextDO.getVin(), timestamp
                );
            }
        } catch (Exception e) {
            log.error("保存缓存数据异常", e);
        }
    }
}
