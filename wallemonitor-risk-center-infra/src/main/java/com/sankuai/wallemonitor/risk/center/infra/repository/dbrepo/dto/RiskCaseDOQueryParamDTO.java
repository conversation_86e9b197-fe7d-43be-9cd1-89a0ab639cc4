package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.BelowTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GeoRangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.JoinFrom;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.LeftJoin;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.RangeQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.TimeDiff;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseLocationRelation;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseMarkInfo;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.CaseSortData;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.MultiVersionMarkInfo;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseMoveCarRelation;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskCaseVehicleRelation;
import com.sankuai.wallemonitor.risk.center.infra.enums.CompareEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GeoQueryDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TimePeriod;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 风险案例查询参数
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCaseDOQueryParamDTO {

    /**
     * 查询指定的同名字段
     */
    private String eventId;

    /**
     * 查询指定的同名字段
     */
    @InQuery(field = "eventId")
    private List<String> eventIdList;

    /**
     * 范围查询
     */
    @InQuery(field = "caseId")
    private List<String> caseIdList;

    /**
     * 事发地点查询
     */
    @InQuery(field = "poiName")
    private List<String> poiNameList;

    /**
     * 场地
     */
    private String placeCode;

    /**
     * 场地列表
     */
    @InQuery(field = "placeCode")
    private List<String> placeCodeList;

    /**
     * 查询指定类型的数据
     */
    @InQuery(field = "type")
    private List<Integer> caseTypeList;

    /**
     * 查询指定类型的数据
     */
    private Integer type;

    /**
     * 查询指定状态的数据
     */
    @InQuery(field = "status")
    private List<Integer> statusList;

    /**
     * 指定messageId查询
     */
    private String messageId;

    /**
     * 范围查询
     */
    @InQuery(field = "messageId")
    private List<String> messageIdList;

    /**
     * 来源范围查询
     */
    @InQuery(field = "source")
    private List<Integer> sourceList;

    /**
     * 创建时间范围
     */
    @RangeQuery(field = "createTime")
    private TimePeriod createTimeRange;

    /**
     * 创建时间范围（开始）
     */
    @GreatTo(field = "createTime")
    private Date createTimeCreateTo;

    /**
     * 创建时间范围（结束）
     */
    @BelowTo(field = "createTime")
    private Date createTimeBelowTo;

    /**
     * 持续时间
     */
    @TimeDiff(unit = TimeUnit.SECONDS, bigField = "closeTime", smallField = "occurTime", compare = CompareEnum.GREAT_THAN)
    private Integer durationGreatThan;

    /**
     * 持续时间
     */
    @TimeDiff(unit = TimeUnit.SECONDS, bigField = "closeTime", smallField = "recallTime", compare = CompareEnum.GREAT_THAN)
    private Integer recallDurationGreatThan;

    /**
     * 排序
     */
    @OrderBy(field = "createTime")
    private OrderEnum orderByCreateTime;

    /**
     * 车辆列表
     */
    @InQuery(field = "vin")
    @JoinFrom(RiskCaseVehicleRelation.class)
    private List<String> vinList;

    /**
     * 多版本信息表
     */
    @JoinFrom(MultiVersionMarkInfo.class)
    private String markVersion;

    /**
     * 用车目的列表
     */
    @InQuery(field = "purpose")
    @JoinFrom(RiskCaseVehicleRelation.class)
    private List<String> purposeList;

    /**
     * vhr模式列表
     */
    @InQuery(field = "vhrMode")
    @JoinFrom(RiskCaseVehicleRelation.class)
    private List<String> vhrModeList;

    /**
     * 类别列表
     */
    @InQuery(field = "category")
    @JoinFrom(CaseMarkInfo.class)
    private List<String> categoryList;

    /**
     * 标注场景列表
     */
    @InQuery(field = "subCategory")
    @JoinFrom(CaseMarkInfo.class)
    private List<String> subCategoryList;

    /**
     * 最近标注人列表
     */
    @InQuery(field = "lastOperator")
    @JoinFrom(CaseMarkInfo.class)
    private List<String> lastOperatorList;

    /**
     * 标注轮次列表
     */
    @InQuery(field = "round")
    @JoinFrom(CaseMarkInfo.class)
    private List<Integer> markRoundList;

    /**
     * 首次标注场景列表
     */
    @InQuery(field = "firstSubCategory")
    @JoinFrom(CaseMarkInfo.class)
    private List<String> firstSubCategoryList;

    /**
     * 等级列表
     */
    @InQuery(field = "riskLevel")
    @JoinFrom(CaseMarkInfo.class)
    private List<Integer> levelList;

    /**
     * 是否上报云安全
     */
    @JoinFrom(RiskCaseMoveCarRelation.class)
    private Boolean reportedCloudSecurity;

    /**
     * 云控接管字段
     */
    @JoinFrom(RiskCaseVehicleRelation.class)
    private Date seatInterventionTime;

    /**
     * 云控呼叫原因
     */
    @JoinFrom(RiskCaseVehicleRelation.class)
    private String callMrmReason;

    /**
     * 云控呼叫原因
     */
    @InQuery(field = "callMrmReason")
    @JoinFrom(RiskCaseVehicleRelation.class)
    private List<String> callMrmReasonList;

    /**
     * 车型
     */
    @InQuery(field = "vehicleType")
    @JoinFrom(RiskCaseVehicleRelation.class)
    private List<String> vehicleTypeList;

    /**
     * 大于云控接管字段
     */
    @GreatTo(field = "seatInterventionTime")
    @JoinFrom(RiskCaseVehicleRelation.class)
    private Date seatInterventionTimeCreateTo;

    /**
     * 关联具体的表名对应的Po对象
     */
    @LeftJoin(field = "caseId", table = RiskCaseVehicleRelation.class)
    private boolean leftJoinRelation;

    /**
     * 关联具体的表名对应的Po对象
     */
    @LeftJoin(field = "caseId", table = CaseLocationRelation.class)
    private boolean leftJoinLocationRelation;

    /**
     * 呼叫安全状态
     */
    private Integer callSafety;

    /**
     * 呼叫安全状态
     */
    @InQuery(field = "callSafety")
    private List<Integer> callSafetyList;


    /**
     * 关联具体的表名对应的Po对象
     */
    @LeftJoin(field = "caseId", table = CaseMarkInfo.class)
    private boolean leftJoinMarkInfo;

    /**
     * 关联具体的表名对应的Po对象
     */
    @LeftJoin(field = "caseId", table = RiskCaseMoveCarRelation.class)
    private boolean leftJoinRiskCaseMoveCarRelation;

    /**
     * 是否删除,如果需要不关注删除状态时，需要修改改字段为NULL
     */
    @Default
    private Boolean isDeleted = false;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 是否呼叫云控
     */
    @InQuery(field = "mrmCalled")
    private List<Integer> mrmCalledList;

    /**
     * 归属问题列表
     */
    @InQuery(field = "problem")
    @JoinFrom(CaseSortData.class)
    private List<String> problemList;

    /**
     * 关联具体的表名对应的Po对象
     */
    @LeftJoin(field = "caseId", table = CaseSortData.class)
    private boolean leftJoinSort;

    /**
     * 关联多版本表
     */
    @LeftJoin(field = "caseId", table = MultiVersionMarkInfo.class)
    private boolean leftJoinMultiVersion;

    @GeoRangeQuery(field = "location")
    @LeftJoin(field = "caseId", table = MultiVersionMarkInfo.class)
    private GeoQueryDO locationQuery;

    /**
     * 发生时间范围
     */
    @RangeQuery(field = "occurTime")
    private TimePeriod occurTimeRange;

    /**
     * 大于事件发生时间
     */
    @BelowTo(field = "occurTime")
    private Date occurTimeBelowTo;

    /**
     * 小于事件关闭时间
     */
    @GreatTo(field = "closeTime")
    private Date closeTimeCreateTo;

    /**
     * 多版本的标记
     */
    @InQuery(field = "subCategory")
    @JoinFrom(MultiVersionMarkInfo.class)
    private List<String> versionSubCategoryList;

}
