package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.dianping.cat.util.StringUtils;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.ead.citadel.document.node.concept.Block;
import com.sankuai.ead.citadel.document.node.impl.node.Doc;
import com.sankuai.ead.citadel.document.node.impl.node.Heading;
import com.sankuai.ead.citadel.document.node.impl.node.Table;
import com.sankuai.ead.citadel.document.node.impl.node.TableCell;
import com.sankuai.ead.citadel.document.node.impl.node.TableHeader;
import com.sankuai.ead.citadel.document.node.impl.node.TableRow;
import com.sankuai.ead.citadel.document.node.impl.node.Title;
import com.sankuai.ead.citadel.document.parser.DocumentParsingException;
import com.sankuai.ead.citadel.document.parser.Serializer;
import com.sankuai.ead.citadel.document.processor.impl.DocValidateResult;
import com.sankuai.ead.citadel.document.processor.impl.DocValidator;
import com.sankuai.ead.citadel.document.util.Builder;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.AppKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultMissCaseDetailDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultOverviewDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.ExperimentalResultSceneDataDTO;
import com.sankuai.wallemonitor.risk.center.infra.exception.DocumentCreateException;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.CreateExperimentalResultParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.KmCreateContentParamVTO;
import com.sankuai.xm.openplatform.api.service.open.CollaborationContentReq;
import com.sankuai.xm.openplatform.api.service.open.CollaborationContentResp;
import com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
@Slf4j
@Component
public class KmServiceAdapter {

    @ThriftClientProxy(remoteAppKey = AppKeyConstant.XM_OPEN_APP_KEY, timeout = 10000)
    private XmOpenKmServiceI.Iface xmOpenKmService;

    @Resource
    private XmServiceAdapter xmServiceAdapter;

    @ConfigValue("km.experimentalResult.parentId")
    private String experimentalResultParentId;

    private static final Map<String, Double> OVERVIEW_HEADER_WITH_WIDTH = new LinkedHashMap<String, Double>() {{
        put("实验时间", 185.0);
        put("实验人", 95.0);
        put("实验内容", 320.0);
        put("数据集", 180.0);
        put("实验轮次", 85.0);
        put("召回率", 155.0);
        put("漏召率", 155.0);
        put("失败率", 155.0);
        put("准确率", 155.0);
    }};

    private static final Map<String, Double> RECALL_DATA_HEADER_WITH_WIDTH = new LinkedHashMap<String, Double>() {{
        put("风险场景", 150.0);
        put("召回率", 95.0);
        put("实际事件数", 110.0);
        put("召回事件数", 110.0);
        put("漏召事件列表", 700.0);
    }};

    private static final Map<String, Double> MISS_EVENT_HEADER_WITH_WIDTH = new LinkedHashMap<String, Double>() {{
        put("漏召caseID", 310.0);
        put("真值", 140.0);
        put("漏召次数", 85.0);
        put("识别过程信息", 750.0);
    }};

    /**
     * 创建文档
     *
     * @param param 创建文档参数
     * @return 文档id
     * @link <a href="https://km.sankuai.com/collabpage/1617579977">学城开发常用操作</a>
     */
    public String createContent(KmCreateContentParamVTO param) {
        CheckUtil.isNotNull(param, "km创建文档参数不能为空");
        CheckUtil.isNotNull(param.getOperatorEmpId(), "km创建文档人员empid不能为空");

        try {
            String token = xmServiceAdapter.getToken();
            if (StringUtils.isBlank(token)) {
                throw new DocumentCreateException("获取token失败");
            }
            CollaborationContentReq contentReq = new CollaborationContentReq();
            contentReq.setOperatorEmpId(param.getOperatorEmpId());
            contentReq.setTemplateId(param.getTemplateId());
            contentReq.setCopyFromContentId(param.getCopyFromContentId());
            contentReq.setContent(param.getContent());
            contentReq.setSpaceId(param.getSpaceId());
            contentReq.setParentId(param.getParentId());
            Optional.ofNullable(param.getTitle()).ifPresent(contentReq::setTitle);

            CollaborationContentResp createContentResp = xmOpenKmService.addCollaborationContent(token, contentReq);
            if (createContentResp == null || createContentResp.getStatus() == null
                    || createContentResp.getStatus().getCode() != 0) {
                throw new DocumentCreateException("创建文档失败，resp: " + JacksonUtils.to(createContentResp));
            }
            return createContentResp.getInfo();
        } catch (Exception e) {
            throw new DocumentCreateException("创建文档失败: " + e.getMessage());
        }
    }

    /**
     * 创建实验结果文档
     *
     * @return
     */
    public String createExperimentalResultContent(CreateExperimentalResultParamVTO param)
            throws DocumentParsingException {
        CheckUtil.isNotNull(param, "创建实验结果参数不能为空");
        CheckUtil.isNotNull(param.getOperatorEmpId(), "km创建文档人员empid不能为空");
        // 标题
        String currentMMddDate = LocalDate.now().format(DateTimeFormatter.ofPattern("MMdd"));
        String titleString = currentMMddDate + "-" + param.getTitle() + Optional.ofNullable(param.getOverview())
                .map(overview -> "-" + overview.getPersonMis()).orElse("");
        Title title = Builder.title(titleString);

        // 一、数据概览
        Heading overviewHeading = Builder.heading(3, "一、数据概览");
        // 创建数据概览表格
        Table overviewTable = buildTable(OVERVIEW_HEADER_WITH_WIDTH,
                Optional.ofNullable(param.getOverview()).map(ExperimentalResultOverviewDTO::toTableCell)
                        .orElse(new TableCell[0]));

        // 二、分场景数据
        Heading sceneDataHeading = Builder.heading(3, "二、分场景数据");
        // 创建分场景数据表格
        Table sceneDataTable = buildTable(RECALL_DATA_HEADER_WITH_WIDTH,
                Optional.ofNullable(param.getSceneDataList()).orElse(Collections.emptyList()).stream().map(
                        ExperimentalResultSceneDataDTO::toTableCell).toArray(TableCell[][]::new));

        // 三、漏召事件列表详情
        Heading missCaseHeading = Builder.heading(3, "三、漏召事件列表详情");
        Table missCaseTable = buildTable(MISS_EVENT_HEADER_WITH_WIDTH,
                Optional.ofNullable(param.getMissCaseDetailList()).orElse(Collections.emptyList()).stream().map(
                        ExperimentalResultMissCaseDetailDTO::toTableCell).toArray(TableCell[][]::new));

        List<Block> blockList = new ArrayList<>();
        blockList.add(overviewHeading);
        blockList.add(overviewTable);
        blockList.add(sceneDataHeading);
        blockList.add(sceneDataTable);
        blockList.add(missCaseHeading);
        blockList.add(missCaseTable);

        // 文档格式校验
        Doc doc = Builder.doc(title, blockList.toArray(new Block[0]));
        DocValidateResult valid = DocValidator.valid(doc);
        if (!valid.getSuccess()) {
            throw new DocumentCreateException("文档格式校验失败：" + valid.getMessage());
        }

        String contentString = Serializer.serialize(doc);
        KmCreateContentParamVTO contentParamVTO = KmCreateContentParamVTO.builder()
                .operatorEmpId(param.getOperatorEmpId())
                .parentId(experimentalResultParentId)
                .title(titleString)
                .content(contentString)
                .build();
        return createContent(contentParamVTO);
    }

    /**
     * 创建表格的公共方法
     * @param headerWithWidth 表头和列宽的Map
     * @param contentCells 表格内容的单元格数组
     * @return 创建好的Table对象
     */
    private Table buildTable(Map<String, Double> headerWithWidth, TableCell[]... contentCells) {
        TableRow header = Builder.tableRow(
                headerWithWidth.entrySet().stream()
                        .map(entry -> {
                            TableHeader tableHeader = Builder.tableHeader(entry.getKey());
                            tableHeader.setColwidth(Collections.singletonList(entry.getValue()));
                            return tableHeader;
                        })
                        .toArray(TableHeader[]::new)
        );

        List<TableRow> tableRows = new ArrayList<>();
        tableRows.add(header);
        Arrays.stream(contentCells)
                .map(Builder::tableRow)
                .forEach(tableRows::add);

        return Builder.table(tableRows.toArray(new TableRow[0]));
    }
}
