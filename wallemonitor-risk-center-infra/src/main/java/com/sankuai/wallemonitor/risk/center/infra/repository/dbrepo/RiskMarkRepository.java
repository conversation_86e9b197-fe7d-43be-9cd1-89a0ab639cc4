package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo;

import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import java.util.List;

public interface RiskMarkRepository {

    /**
     * 查询最新的markItem
     *
     * <p>
     * 根据caseId查询db的item,这里查询的是
     * <p>
     * </p>
     */
    RiskCheckingQueueItemDO getMarkItem(String caseId, String version);

    /**
     * 保存标注结果
     *
     * @param itemList
     * @param version
     */
    void saveMarkItem(List<RiskCheckingQueueItemDO> itemList, String version);

    /**
     * 删除markItem
     *
     * @param itemList
     * @param version
     */
    void deleteMarkItem(List<RiskCheckingQueueItemDO> itemList, String version);
}
