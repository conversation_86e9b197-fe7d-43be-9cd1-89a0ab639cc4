package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.BelowTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.OrderBy;
import com.sankuai.wallemonitor.risk.center.infra.enums.OrderEnum;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 风险案例查询参数
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PublicDetailDOQueryParamDTO {


    /**
     * 范围查询
     */
    @InQuery(field = "publicEventId")
    private List<Long> publicEventIdList;


    /**
     * 单个查询
     */
    private Long publicEventId;


    /**
     * 责任人查询
     */
    @InQuery(field = "responsibleParty")
    private List<String> responsiblePartyList;

    /**
     * 类别
     */
    @InQuery(field = "category")
    private List<String> categoryList;

    /**
     * 创建时间范围
     */
    @GreatTo(field = "occurTime")
    private Date occurTimeCreateTo;

    
    @OrderBy(field = "occurTime")
    private OrderEnum orderByOccurTime;

    /**
     * 创建时间范围
     */
    @BelowTo(field = "occurTime")
    private Date occurTimeBelowTo;

}
