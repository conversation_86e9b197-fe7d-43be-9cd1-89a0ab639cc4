package com.sankuai.wallemonitor.risk.center.infra.utils;

import java.util.Collections;
import java.util.List;

public class NumberCalcUtils {

    /**
     * 计算整数列的top90
     *
     * @param values
     * @return
     */
    public static Integer calculateTop90(List<Integer> values) {
        if (values == null || values.isEmpty()) {
            return 0;
        }
        // 对列表进行排序
        Collections.sort(values);
        // 计算90%的索引位置，注意这里使用的是向下取整
        int index90 = (int) Math.floor(values.size() * 0.9) - 1;
        // 确保索引不越界
        index90 = Math.max(index90, 0);

        // 返回90%位置的值
        return values.get(index90);
    }


}
