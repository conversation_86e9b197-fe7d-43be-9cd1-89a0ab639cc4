package com.sankuai.wallemonitor.risk.center.infra.factory;

import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CaseMarkInfoExtDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.CaseSortExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.CaseSortDataDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.MultiVersionMarkInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 风险事件DO工厂类
 */
@Component
public class RiskCaseFactory {

    /**
     * id生成器
     */
    public static IDGenerateRepository idGenerateRepository;

    /**
     * id生成器
     */
    @Resource
    private IDGenerateRepository innerIdGenerateRepository;


    @PostConstruct
    public void init() {
        idGenerateRepository = innerIdGenerateRepository;
    }

    /**
     * 初始化多版本标注信息
     * 
     * @param caseId
     * @param version
     * @return
     */
    public static MultiVersionMarkInfoDO initMultiVersionMarkInfo(String caseId, String version) {
        return MultiVersionMarkInfoDO.builder()
                //
                .caseId(caseId)
                //
                .markVersion(version)
                //
                .checkResult(RiskCheckResultDO.builder().build()).build();
    }


    /**
     * 创建风险事件
     *
     * @return
     */
    public static RiskCaseDO createRiskCaseDO(CreateRiskCaseDOParamDTO paramDTO) {
        SystemCheckUtil.isNotNull(paramDTO, "入参不能为空");
        SystemCheckUtil.isNotBlank(paramDTO.getEventId(), "事件ID不能为空");
        SystemCheckUtil.isNotNull(paramDTO.getStatus(), "风险状态不能为空");
        SystemCheckUtil.isNotNull(paramDTO.getSource(), "风险来源不能为空");

        String caseId = StringUtils.isNotBlank(paramDTO.getCaseId()) ? paramDTO.getCaseId()
                : idGenerateRepository.generateByKey(IDBizEnum.RISK_CASE_ID, paramDTO.getVinList(),
                        //开始时间没有时，使用原时间
                        paramDTO.getSource(), paramDTO.getType(), paramDTO.getTimestamp());
        return RiskCaseDO.builder()
                .caseId(caseId)
                .eventId(paramDTO.getEventId())
                .status(RiskCaseStatusEnum.NO_DISPOSAL)
                .recallTime(DatetimeUtil.toDate(paramDTO.getRecallTime()))
                .type(paramDTO.getType())
                .source(paramDTO.getSource())
                .occurTime(DatetimeUtil.toDate(paramDTO.getTimestamp()))
                .isDeleted(IsDeleteEnum.NOT_DELETED)
                .build();

    }


    /**
     * 创建风险事件
     *
     * @return
     */
    public static CaseSortDataDO createCaseSortData(String caseId) {
        SystemCheckUtil.isNotBlank(caseId, "caseId不能为空");
        return CaseSortDataDO.builder()
                .caseId(caseId)
                .extInfo(CaseSortExtInfoDO.builder().build())
                .isDeleted(IsDeleteEnum.NOT_DELETED)
                .build();
    }

    /**
     * 创建风险事件关联车辆
     *
     * @param relationDOParamDTO
     * @return
     */
    public static RiskCaseVehicleRelationDO createRiskRelation(CreateVehicleCaseRelationDOParamDTO relationDOParamDTO) {
        SystemCheckUtil.isNotNull(relationDOParamDTO, "入参不能为空");
        SystemCheckUtil.isNotBlank(relationDOParamDTO.getEventId(), "事件ID不能为空");
        SystemCheckUtil.isNotBlank(relationDOParamDTO.getCaseId(), "caseId不能为空");
        return RiskCaseVehicleRelationDO.builder()
                .vin(relationDOParamDTO.getVin())
                .caseId(relationDOParamDTO.getCaseId())
                .status(RiskCaseVehicleStatusEnum.INIT)
                .eventId(relationDOParamDTO.getEventId())
                .type(relationDOParamDTO.getType())
                //traceId可能为空
                .traceId(relationDOParamDTO.getTraceId())
                .occurTime(new Date(relationDOParamDTO.getOccurTime()))
                .sideBySideTimestamp(relationDOParamDTO.getSideBySideTimestamp())
                .milliBeginTime(relationDOParamDTO.getMilliBeginTime())
                .isDeleted(IsDeleteEnum.NOT_DELETED)
                .extInfo(RiskVehicleExtInfoDO.builder().vehicleContent(relationDOParamDTO.getContext()).build())
                .build();
    }


    /**
     * 初始化风险事件标注信息
     *
     * @param caseId
     * @return
     */
    public static CaseMarkInfoDO initMarkInfo(String caseId) {
        return CaseMarkInfoDO.builder().caseId(caseId)
                .extInfo(CaseMarkInfoExtDO.builder().build())
                .build();
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class CreateRiskCaseDOParamDTO {

        /**
         * 事件ID
         */
        private String eventId;
        /**
         * 来源
         */
        private RiskCaseSourceEnum source;
        /**
         * 类型
         */
        private RiskCaseTypeEnum type;
        /**
         * 状态
         */
        private RiskCaseStatusEnum status;
        /**
         * 车辆列表
         */
        private List<String> vinList;

        /**
         * 业务站点id
         */
        private String businessStationId;

        /**
         * 时间
         */
        private Long timestamp;

        /**
         * 召回时间
         */
        private Long recallTime;

        /**
         * csaseId
         */
        private String caseId;

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class CreateVehicleCaseRelationDOParamDTO {

        /**
         * 事件ID
         */
        private String eventId;

        /**
         * caseID
         */
        private String caseId;

        /**
         * 状态
         */
        private RiskCaseVehicleStatusEnum status;

        /**
         * 风险类型
         */
        private RiskCaseTypeEnum type;

        /**
         * 车辆
         */
        private String vin;

        /**
         * traceId
         */
        private String traceId;

        /**
         * 发生时间
         */
        private Long occurTime;

        /**
         * 并排开始时间戳
         */
        private String sideBySideTimestamp;

        /**
         * 停滞不当开始时间戳
         */
        private Date milliBeginTime;

        /**
         * 车辆关联的信息
         */
        private Map<String, Object> context;
    }
}
