package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RiskCaseVehicleStatusEnum {

    INIT(0, "初始化"),
    PENDING_ASSIGNMENT(10, "请求坐席中"),
    PRE_ASSIGNED(20, "坐席已分配"),
    ASSIGNED(30, "坐席已连入"),
    DISPOSED(40, "坐席已退控"),
    REJECTED(50, "已拒绝"),
    CANCELLED(99, "已取消");

    private final int code;
    private final String desc;

    /**
     * 根据value获取枚举
     *
     * @param value
     * @return
     */
    public static RiskCaseVehicleStatusEnum findByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (RiskCaseVehicleStatusEnum statusEnum : RiskCaseVehicleStatusEnum.values()) {
            if (statusEnum.getCode() == value) {
                return statusEnum;

            }
        }
        return null;
    }

    public static boolean isInProcess(RiskCaseVehicleStatusEnum status) {
        if (status == null) {
            return false;
        }
        return Objects.equals(status, ASSIGNED) || Objects.equals(status,
                REJECTED);

    }

    /**
     * 是否处置完成
     *
     * @param status
     * @return
     */
    public static boolean isDisposed(RiskCaseVehicleStatusEnum status) {
        return Objects.equals(status, DISPOSED);

    }
}
