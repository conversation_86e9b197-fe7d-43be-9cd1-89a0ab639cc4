package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficData;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficData.TrafficHmiSignal;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficData.TrafficHmiSignal.HmiSignal;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightContextDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.TrafficLightDO;
import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class}, imports = {JacksonUtils.class})
public interface TrafficDataConvert {


    default TrafficLightContextDO toDO(TrafficData trafficDataPO) {
        TrafficLightContextDO contextDO = TrafficLightContextDO.builder().build();

        if (trafficDataPO.getTrafficHmiSignal() != null) {
            String id = Optional.of(trafficDataPO)
                    .map(TrafficData::getTrafficHmiSignal)
                    .map(TrafficHmiSignal::getHmiSignal)
                    .map(HmiSignal::getFinalSignalId).orElse(CharConstant.CHAR_EMPTY);
            contextDO.setNowPlanningId(id);
        }
        if (trafficDataPO.getContainLights() != null) {
            contextDO.setTrafficLightList(
                    Optional.ofNullable(trafficDataPO.getTrafficLightList()).orElse(new ArrayList<>()).stream()
                            .map(trafficLight -> {
                                TrafficLightDO trafficLightDO = TrafficLightDO.builder().build();
                                trafficLightDO.setId(trafficLight.getId());
                                trafficLightDO.setColor(TrafficLightTypeEnum.getByName(trafficLight.getColor()));
                                trafficLightDO.setStatus(TrafficStatusEnum.getByName(trafficLight.getStatus()));
                                trafficLightDO.setCriticalLevel(trafficLight.getCriticalLevel());
                                trafficLightDO.setCountdown(trafficLight.getCountdown());
                                return trafficLightDO;
                            }).collect(Collectors.toList()));
        }
        contextDO.setUpdateTime(trafficDataPO.getTimestamp());
        return contextDO;
    }
}
