package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
@ToString
@NoArgsConstructor
public enum SwitchPowerSignalEnum {
    REQUEST("VCU切电信号状态: request switch", "请求切电"),
    PROCESSING("VCU切电信号状态: switching", "切电中"),
    SUCCESS("VCU切电信号状态: switch succeed", "切电成功");


    private String name;
    private String desc;

    /**
     * 根据名称获取枚举值
     *
     * @param name
     * @return
     */
    public static SwitchPowerSignalEnum getByName(String name) {
        for (SwitchPowerSignalEnum signal : values()) {
            if (signal.getName().equals(name)) {
                return signal;
            }
        }
        return null; // 如果没有匹配的枚举值，返回null
    }

    /**
     * 判断是否正在切电
     *
     * @param switchPowerSignal
     * @return
     */
    public static Boolean isSwitching(String switchPowerSignal) {
        if (StringUtils.isBlank(switchPowerSignal)) {
            return false;
        }
        SwitchPowerSignalEnum signalEnum = getByName(switchPowerSignal);
        if (Objects.isNull(signalEnum)) {
            return false;
        }
        switch (signalEnum) {
            case PROCESSING:
            case SUCCESS:
                return true;
            case REQUEST:
            default:
                return false;
        }

    }

}
