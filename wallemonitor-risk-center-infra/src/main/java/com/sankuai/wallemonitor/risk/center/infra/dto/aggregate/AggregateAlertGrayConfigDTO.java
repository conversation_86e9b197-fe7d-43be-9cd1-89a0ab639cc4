package com.sankuai.wallemonitor.risk.center.infra.dto.aggregate;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 聚合告警灰度配置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregateAlertGrayConfigDTO {

    /**
     * 车辆归属灰度配置（内部为"或"关系）
     */
    private AttributesGrayConfigDTO attributesGrayConfig;

    /**
     * 用车目的灰度名单
     * 支持"ALL"表示全量生效
     * 与位置维度之间是"且"关系
     */
    private List<String> purposeGrayList;

    /**
     * 车辆归属灰度配置
     * POI、场地、车辆三个维度之间是"或"关系
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttributesGrayConfigDTO {

        /**
         * POI灰度名单
         * 支持"ALL"表示全量生效
         */
        private List<String> poiGrayList;

        /**
         * 场地灰度名单
         * 支持"ALL"表示全量生效
         */
        private List<String> placeCodeGrayList;

        /**
         * 车辆VIN灰度名单
         * 支持"ALL"表示全量生效
         */
        private List<String> vinGrayList;

        /**
         * 判断位置维度是否有效配置
         */
        public boolean isValid() {
            return CollectionUtils.isNotEmpty(poiGrayList)
                    || CollectionUtils.isNotEmpty(placeCodeGrayList)
                    || CollectionUtils.isNotEmpty(vinGrayList);
        }

        /**
         * 判断位置维度是否命中（内部为"或"关系）
         */
        public boolean isLocationHit(String poiName, String placeCode, String vin) {
            if (!isValid()) {
                return false;
            }

            // POI维度检查
            if (CollectionUtils.isNotEmpty(poiGrayList)) {
                if (poiGrayList.contains("ALL") || poiGrayList.contains(poiName)) {
                    return true;
                }
            }

            // 场地维度检查
            if (CollectionUtils.isNotEmpty(placeCodeGrayList)) {
                if (placeCodeGrayList.contains("ALL") || placeCodeGrayList.contains(placeCode)) {
                    return true;
                }
            }

            // 车辆维度检查
            if (CollectionUtils.isNotEmpty(vinGrayList)) {
                if (vinGrayList.contains("ALL") || vinGrayList.contains(vin)) {
                    return true;
                }
            }

            return false;
        }

        /**
         * 判断位置维度是否全量生效
         */
        public boolean isFullRelease() {
            return (CollectionUtils.isNotEmpty(poiGrayList) && poiGrayList.contains("ALL"))
                    || (CollectionUtils.isNotEmpty(placeCodeGrayList) && placeCodeGrayList.contains("ALL"))
                    || (CollectionUtils.isNotEmpty(vinGrayList) && vinGrayList.contains("ALL"));
        }
    }

    /**
     * 校验灰度配置是否有效
     * 如果所有维度都为空，则策略不生效
     */
    public boolean isValid() {
        return (attributesGrayConfig != null && attributesGrayConfig.isValid())
                || CollectionUtils.isNotEmpty(purposeGrayList);
    }

    /**
     * 判断是否命中灰度条件
     * 位置维度（内部为"或"关系）与用车目的维度之间是"且"关系
     */
    public boolean isGrayHit(String poiName, String placeCode, String vin, String purpose) {
        // 如果配置无效，直接返回false
        if (!isValid()) {
            return false;
        }

        // 用车目的维度检查 - 如果配置了用车目的维度，必须命中
        if (CollectionUtils.isNotEmpty(purposeGrayList)) {
            if (!purposeGrayList.contains("ALL") && !purposeGrayList.contains(purpose)) {
                return false;
            }
        }

        // 位置维度检查 - 如果配置了位置维度，必须命中（内部为"或"关系）
        if (attributesGrayConfig != null && attributesGrayConfig.isValid()) {
            if (!attributesGrayConfig.isLocationHit(poiName, placeCode, vin)) {
                return false;
            }
        }

        // 通过所有检查，返回true
        return true;
    }

    /**
     * 判断是否全量生效
     */
    public boolean isFullRelease() {
        return (attributesGrayConfig != null && attributesGrayConfig.isFullRelease())
                || (CollectionUtils.isNotEmpty(purposeGrayList) && purposeGrayList.contains("ALL"));
    }
} 