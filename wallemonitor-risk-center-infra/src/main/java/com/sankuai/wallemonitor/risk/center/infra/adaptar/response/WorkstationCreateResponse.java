package com.sankuai.wallemonitor.risk.center.infra.adaptar.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkstationCreateResponse {

    private int code;
    private String message;
    private WorkstationCreateResponseData data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkstationCreateResponseData {

        /**
         * 工作台caseId
         */
        private String caseId;

        /**
         * 工作台case链接
         */
        private String link;
    }

}
