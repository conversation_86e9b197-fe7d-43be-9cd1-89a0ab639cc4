package com.sankuai.wallemonitor.risk.center.infra.adaptar.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.walleeve.thrift.response.EveHttpResponse;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.HttpUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.EveEsQueryRequest;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.EveEsQueryRequest.QueryParamsDTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.EveEsQueryRequest.QueryParamsDTO.RangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.EveEsQueryRequest.QueryParamsDTO.TermDTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.EveEsQueryRequest.QueryParamsDTO.TermDTOS;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.request.EveEsQueryRequest.SortParamsDTO;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.response.EveEventPlatformResponse;
import com.sankuai.wallemonitor.risk.center.infra.dto.MrmConnectionInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleStatusChangeInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.LocationSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.RemoteErrorException;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.EveEventPlatformVTO.ChangeDetail;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.EveEventPlatformVTO.MrmCockpitEventRecord;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 事件平台搜索 文档地址：https://km.sankuai.com/collabpage/2268280379
 *
 * @auther zdc
 */
@Component
@Slf4j
public class EventPlatSearchAdapter {

    /**
     * es 查询接口
     */
    private static final String PATH = "/eve/online/rest/eagle/search/common";

    @Value("${eveEventPlatform.queryDomain}")
    private String domain;

    /**
     * 驾驶模式变更事件对应的 eventType
     */
    private static final String DRIVE_MODE_CHANGE_EVENT_TYPE = "drive_mode_change";

    /**
     * 位置数据源变更事件对应的 eventType
     */
    private static final String POSITION_CHANGE_EVENT_TYPE = "position_change";

    /**
     * 坐席事件类型
     */
    private static final String MRM_EVENT_TYPE = "3";

    /**
     * es 索引名称 - 车辆状态变更事件
     */
    private static final String DATA_SET = "vehicle_status_event";

    /**
     * es 索引名称 - 坐席连车事件
     */
    private static final String MRM_COCKPIT_EVENT_RECORD_DATA_SET = "mrm_cockpit_event_record";

    /**
     * 查询最新更新到MRM的驾驶模式变更
     *
     * @param vin
     * @param startTime
     * @param endTime
     * @return
     */
    public VehicleStatusChangeInfoDTO queryLatestUpdateToMrmDriveModeChange(String vin, Date startTime, Date endTime) {
        try {
            EveEsQueryRequest request = buildUpdateToMrmQueryRequest(vin, startTime.getTime(), endTime.getTime());
            log.info("queryLatestUpdateToMrmDriveModeChange request, requestParams: {}", JacksonUtils.to(request));
            EveHttpResponse<EveEventPlatformResponse> response = HttpUtils.postJson(JacksonUtils.to(request),
                    domain, PATH, null, EveEventPlatformResponse.class);
            log.info("queryLatestUpdateToMrmDriveModeChange response: {}", JacksonUtils.to(response));
            ChangeDetail changeDetail = handleEveHttpResponse(response, new TypeReference<ChangeDetail>() {
            });
            if (Objects.isNull(changeDetail)) {
                log.warn("queryLatestUpdateToMrmDriveModeChange changeDetail is null");
                return null;
            }

            VehicleStatusChangeInfoDTO vehicleDriveModeChangeInfoDTO = JacksonUtils.from(changeDetail.getExtInfo(),
                    VehicleStatusChangeInfoDTO.class);
            if (Objects.isNull(vehicleDriveModeChangeInfoDTO)) {
                log.error("queryLatestUpdateToMrmDriveModeChange vehicleDriveModeChangeInfoDTO is null");
                return null;
            }
            return vehicleDriveModeChangeInfoDTO;
        } catch (Exception exception) {
            log.error("queryLatestUpdateToMrmDriveModeChange error", exception);
            return null;
        }
    }


    /**
     * 查询最新车辆状态变更
     *
     * @return
     */
    public VehicleStatusChangeInfoDTO queryLatestDriveModeChange(String vin) {

        try {
            EveEsQueryRequest request = buildQueryRequest(DRIVE_MODE_CHANGE_EVENT_TYPE, vin);
            log.info("queryLatestDriveModeChange request, requestParams: {}", JacksonUtils.to(request));
            EveHttpResponse<EveEventPlatformResponse> response = HttpUtils.postJson(JacksonUtils.to(request),
                    domain,
                    PATH, null, EveEventPlatformResponse.class);
            log.info("queryLatestDriveModeChange response: {}", JacksonUtils.to(response));
            ChangeDetail changeDetail = handleEveHttpResponse(response, new TypeReference<ChangeDetail>() {
            });
            if (Objects.isNull(changeDetail)) {
                log.error("queryLatestDriveModeChange changeDetail is null");
                return null;
            }
            VehicleStatusChangeInfoDTO vehicleDriveModeChangeInfoDTO = JacksonUtils.from(changeDetail.getExtInfo(),
                    VehicleStatusChangeInfoDTO.class);
            if (Objects.isNull(vehicleDriveModeChangeInfoDTO)) {
                log.error("queryLatestDriveModeChange vehiclePositionChangeInfoDTO is null");
                return null;
            }
            return vehicleDriveModeChangeInfoDTO;

        } catch (Exception exception) {
            log.error("queryLatestDriveModeChange error", exception);
            return null;
        }
    }


    /**
     * 查询最新定位数据源变更
     *
     * @return
     */
    public VehicleStatusChangeInfoDTO queryLatestAutoCarUtmLocationInfoChange(String vin) {

        try {
            // 构建查询请求
            EveEsQueryRequest request = EveEsQueryRequest.builder()
                    .dataset(DATA_SET)
                    .pageSize(1L)
                    .pageIndex(1L)
                    .queryParamsDTO(QueryParamsDTO.builder().termDTOList(Arrays.asList(
                            TermDTO.builder().field("event_type").value(POSITION_CHANGE_EVENT_TYPE).build(),
                            TermDTO.builder().field("vin").value(vin).build(),
                            TermDTO.builder().field("old_value").value(LocationSourceEnum.AUTO_CAR_UTM.getSource())
                                    .build())).build())
                    .sortParamsDTO(
                            Collections.singletonList(
                                    SortParamsDTO.builder().field("update_time").ascending(false).build()))
                    .build();
            log.info("queryLatestAutoCarUtmLocationInfoChange request, requestParams: {}", JacksonUtils.to(request));
            EveHttpResponse<EveEventPlatformResponse> response = HttpUtils.postJson(JacksonUtils.to(request),
                    domain,
                    PATH, null, EveEventPlatformResponse.class);
            log.info("queryLatestAutoCarUtmLocationInfoChange response: {}", JacksonUtils.to(response));
            ChangeDetail changeDetail = handleEveHttpResponse(response, new TypeReference<ChangeDetail>() {
            });
            if (Objects.isNull(changeDetail)) {
                log.error("queryLatestAutoCarUtmLocationInfoChange changeDetail is null");
                return null;
            }
            VehicleStatusChangeInfoDTO vehicleStatusChangeInfoDTO = JacksonUtils.from(changeDetail.getExtInfo(),
                    VehicleStatusChangeInfoDTO.class);
            if (Objects.isNull(vehicleStatusChangeInfoDTO)) {
                log.error("queryLatestAutoCarUtmLocationInfoChange vehiclePositionChangeInfoDTO is null");
                return null;
            }
            return vehicleStatusChangeInfoDTO;

        } catch (Exception exception) {
            log.error("queryLatestAutoCarUtmLocationInfoChange error", exception);
            return null;
        }
    }

    /**
     * 查询一段时间内的车辆连接坐席的数量
     *
     * @param vin
     * @param startTime
     * @param endTime
     * @return
     */
    public Long queryMrmConnectionCountInTimeRange(String vin, Long startTime, Long endTime) {
        try {
            // 构建查询请求
            EveEsQueryRequest request = EveEsQueryRequest.builder()
                    .dataset(MRM_COCKPIT_EVENT_RECORD_DATA_SET)
                    .pageSize(1L)
                    .pageIndex(10L)
                    .queryParamsDTO(QueryParamsDTO.builder().termDTOList(Arrays.asList(
                            TermDTO.builder().field("event").value(MRM_EVENT_TYPE).build(),
                            TermDTO.builder().field("vin").value(vin).build())).rangeDTOList(Arrays.asList(
                            RangeDTO.builder().field("add_time").gt(startTime).lt(endTime).build())).build())
                    .build();
            log.info("queryMrmConnectionCountInTimeRange request, requestParams: {}", JacksonUtils.to(request));
            EveHttpResponse<EveEventPlatformResponse> response = HttpUtils.postJson(JacksonUtils.to(request),
                    domain,
                    PATH, null, EveEventPlatformResponse.class);
            log.info("queryMrmConnectionCountInTimeRange response: {}", JacksonUtils.to(response));
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || Objects.isNull(
                    response.getData().getData())) {
                log.error("queryMrmConnectionCountInTimeRange response is null");
                return null;
            }
            return response.getData().getData().getTotal();
        } catch (Exception exception) {
            log.error("queryMrmConnectionCountInTimeRange error", exception);
            return null;
        }
    }

    /**
     * 查询车辆指定时刻后最近一次连坐席事件信息
     *
     * @param vin
     * @param startTime
     */
    public MrmConnectionInfoDTO getRecentMrmConnectRecord(String vin, Long startTime) {
        try {
            // TODO: 兼容 gt 参数（猜测是大于号，当startTime == 连入时间时查不到，所以做-1 处理）
            startTime = startTime - 1;
            // 构建查询请求
            EveEsQueryRequest request = EveEsQueryRequest.builder()
                    .dataset(MRM_COCKPIT_EVENT_RECORD_DATA_SET)
                    .pageSize(1L)
                    .pageIndex(1L)
                    .queryParamsDTO(QueryParamsDTO.builder().termDTOList(Arrays.asList(
                            TermDTO.builder().field("event").value(MRM_EVENT_TYPE).build(),
                            TermDTO.builder().field("vin").value(vin).build())).rangeDTOList(Arrays.asList(
                            RangeDTO.builder().field("add_time").gt(startTime).build())).build())
                    .sortParamsDTO(
                            Collections.singletonList(
                                    SortParamsDTO.builder().field("add_time").ascending(true).build()))
                    .build();
            log.info("getVehicleNextMrmConnectionInfo request, requestParams: {}", JacksonUtils.to(request));
            EveHttpResponse<EveEventPlatformResponse> response = HttpUtils.postJson(JacksonUtils.to(request),
                    domain,
                    PATH, null, EveEventPlatformResponse.class);

            log.info("getVehicleNextMrmConnectionInfo response: {}", JacksonUtils.to(response));
            MrmCockpitEventRecord record = handleEveHttpResponse(response, new TypeReference<MrmCockpitEventRecord>() {
            });
            if (Objects.isNull(record)) {
                return null;
            }
            return MrmConnectionInfoDTO.builder().connectTime(record.getAddTime())
                    .handler(record.getUser())
                    .build();
        } catch (Exception exception) {
            log.error("getVehicleNextMrmConnectionInfo error", exception);
            return null;
        }
    }

    /**
     * 查询一段时间内的首次连坐席事件信息
     *
     * @param vin
     * @param startTime
     * @param endTime
     * @return
     */
    public MrmConnectionInfoDTO queryFirstMrmConnectionInTimeRange(String vin, Date startTime, Date endTime) {
        try {
            CheckUtil.isNotNull(startTime, "startTime is null");
            CheckUtil.isNotNull(endTime, "endTime is null");
            // 构建查询请求
            EveEsQueryRequest request = EveEsQueryRequest.builder()
                    .dataset(MRM_COCKPIT_EVENT_RECORD_DATA_SET)
                    .pageSize(1L)
                    .pageIndex(1L)
                    .queryParamsDTO(QueryParamsDTO.builder().termDTOList(Arrays.asList(
                                    TermDTO.builder().field("event").value(MRM_EVENT_TYPE).build(),
                                    TermDTO.builder().field("vin").value(vin).build())).rangeDTOList(Arrays.asList(
                                    // todo: 考虑到数据库时间精度只能精确到秒级，所以这里需要使用大于等于和小于等于
                                    RangeDTO.builder().field("add_time").gte(startTime.getTime()).lte(endTime.getTime())
                                            .build()))
                            .build())
                    .sortParamsDTO(
                            Collections.singletonList(
                                    SortParamsDTO.builder().field("add_time").ascending(true).build()))
                    .build();
            log.info("queryFirstMrmConnectionInTimeRange request, requestParams: {}", JacksonUtils.to(request));
            EveHttpResponse<EveEventPlatformResponse> response = HttpUtils.postJson(JacksonUtils.to(request),
                    domain,
                    PATH, null, EveEventPlatformResponse.class);
            log.info("queryFirstMrmConnectionInTimeRange response: {}", JacksonUtils.to(response));
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || Objects.isNull(
                    response.getData().getData())) {
                throw new RemoteErrorException("queryFirstMrmConnectionInTimeRange response is null");
            }
            MrmCockpitEventRecord record = handleEveHttpResponse(response, new TypeReference<MrmCockpitEventRecord>() {
            });
            if (Objects.isNull(record)) {
                return null;
            }
            return MrmConnectionInfoDTO.builder().connectTime(record.getAddTime())
                    .handler(record.getUser())
                    .build();
        } catch (Exception exception) {
            log.error("queryFirstMrmConnectionInTimeRange error", exception);
            return null;
        }
    }


    /**
     * 处理事件平台返回结果
     *
     * @param response
     * @param typeReference
     * @param <T>
     * @return
     */
    private <T> T handleEveHttpResponse(EveHttpResponse<EveEventPlatformResponse> response,
            TypeReference<T> typeReference) {
        if (Objects.isNull(response)
                || Objects.isNull(response.getData())
                || Objects.isNull(response.getData().getData())) {
            log.error("handleEveHttpResponse error", new IllegalArgumentException());
            return null;
        }
        List<T> detail = response.getData().getData().getDetail();

        return CollectionUtils.isEmpty(detail) ? null : JacksonUtils.from(
                JacksonUtils.to(detail.get(0)),
                typeReference);
    }

    /**
     * 构建 es 查询请求
     *
     * @param eventType
     * @param vin
     * @return
     */
    private EveEsQueryRequest buildQueryRequest(String eventType, String vin) {
        return EveEsQueryRequest.builder()
                .dataset(DATA_SET)
                .pageSize(1L)
                .pageIndex(1L)
                .queryParamsDTO(QueryParamsDTO.builder()
                        .termDTOList(Arrays.asList(
                                TermDTO.builder().field("event_type").value(eventType).build(),
                                TermDTO.builder().field("vin").value(vin).build()))
                        .build())
                .sortParamsDTO(
                        Collections.singletonList(
                                SortParamsDTO.builder().field("update_time").ascending(false).build()))
                .build();
    }

    /**
     * 构建查询请求 - 查询驾驶模式变更到云控的数据
     *
     * @param startTime
     * @param endTime
     * @param vin
     * @return
     */
    private EveEsQueryRequest buildUpdateToMrmQueryRequest(String vin, Long startTime, Long endTime) {
        return EveEsQueryRequest.builder()
                .dataset(DATA_SET)
                .pageSize(1L)
                .pageIndex(1L)
                .queryParamsDTO(QueryParamsDTO.builder()
                        .termDTOList(Arrays.asList(
                                TermDTO.builder().field("event_type").value("drive_mode_change").build(),
                                TermDTO.builder().field("vin").value(vin).build()))
                        .termsDTOList(Arrays.asList(
                                TermDTOS.builder().field("new_value").value(Arrays.asList("2", "3")).build()))
                        .rangeDTOList(Arrays.asList(
                                RangeDTO.builder().field("update_time").gte(startTime).lte(endTime).build()))
                        .build())
                .sortParamsDTO(
                        Collections.singletonList(
                                SortParamsDTO.builder().field("update_time").ascending(true).build()))
                .build();
    }

}
