package com.sankuai.wallemonitor.risk.center.infra.utils;

import com.google.common.collect.Sets;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.Velocity;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.ThemeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.io.StringWriter;
import java.util.Date;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;

/**
 * Velocity模板渲染工具类
 */
@Slf4j
public class VelocityUtils {

    /**
     * 通用的处理utils
     */
    private static final Set<Class> commonUtils;

    private static final VelocityEngine velocityEngine;


    private static final String LOG_TAG = "logTag";

    static {

        // 设置安全相关的属性
        Properties properties = new Properties();
        properties.setProperty("resource.loader", "class");
        properties.setProperty("class.resource.loader.class",
                "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        properties.setProperty("runtime.references.strict", "true");
        properties.setProperty("directive.if.tostring.nullcheck", "true");
        properties.setProperty("velocimacro.permissions.allow.inline", "false");
        //增加限制
        properties.setProperty("resource.manager.cache.size", "100");
        commonUtils = Sets.newHashSet(
                //字符串处理类
                StringUtils.class, NumberUtils.class, DatetimeUtil.class,
                RiskCaseTypeEnum.class,
                RiskCaseSourceEnum.class,
                RiskCaseStatusEnum.class,
                Date.class,
                ThemeEnum.class
        );
        velocityEngine = new VelocityEngine(properties);
    }

    /**
     * 使用Velocity渲染模板
     *
     * @param template 模板
     * @param model    渲染模板时使用的数据
     * @return 渲染后的字符串
     */
    public static String render(String template, Map<String, Object> model) {
        try {
            if (StringUtils.isBlank(template)) {
                return CharConstant.CHAR_EMPTY;
            }
            if (MapUtils.isEmpty(model)) {
                return template;
            }
            // 获取模板
            VelocityContext context = new VelocityContext();
            for (Map.Entry<String, Object> entry : model.entrySet()) {
                context.put(entry.getKey(), entry.getValue());
            }
            initCommonUtils(context);
            // 使用StringWriter来收集渲染后的输出
            StringWriter writer = new StringWriter();
            // 使用evaluate方法渲染模板
            boolean result = velocityEngine.evaluate(context, writer, LOG_TAG, template);
            SystemCheckUtil.isTrue(result, "渲染失败");
            return writer.toString();
        } catch (Exception e) {
            log.error(String.format("template%s渲染失败", template), e);
            return CharConstant.CHAR_EMPTY;
        }
    }

    /**
     * 添加常用的上下文类
     *
     * @param context
     */
    private static void initCommonUtils(VelocityContext context) {
        if (CollectionUtils.isEmpty(commonUtils)) {
            return;
        }
        for (Class clazz : commonUtils) {
            context.put(clazz.getSimpleName(), clazz);
        }

    }

    /**
     * 计算物体的合速度大小
     *
     * @param velocity
     * @return
     */
    public static double calculateTotalVelocity(Velocity velocity) {
        if (velocity == null) {
            return 0.0;
        }

        // 三维速度分量
        double vx = velocity.getX() != null ? velocity.getX() : 0.0;
        double vy = velocity.getY() != null ? velocity.getY() : 0.0;
        double vz = velocity.getZ() != null ? velocity.getZ() : 0.0;

        // 使用三维速度分量计算合速度
        return Math.sqrt(vx * vx + vy * vy + vz * vz);
    }
}
