package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Slf4j
public class RiskCaseMoveCarRelationDO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * caseId
     */
    private String caseId;

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 上报人信息
     */
    private String reporter;

    /**
     * 是否已经上报云安全工单
     */
    private Boolean reportedCloudSecurity;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新信息
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    @Builder.Default
    private IsDeleteEnum isDeleted = IsDeleteEnum.NOT_DELETED;
}
