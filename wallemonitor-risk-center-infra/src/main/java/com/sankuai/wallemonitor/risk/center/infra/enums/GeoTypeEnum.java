package com.sankuai.wallemonitor.risk.center.infra.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum GeoTypeEnum {
    // 点
    POINT("Point"),
    MULTI_POINT("MultiPoint"),
    LINE_STRING("LineString"),
    MULTI_LINE_STRING("MultiLineString"),
    POLYG<PERSON>("Polygon"),
    MULTI_POLYGON("MultiPolygon"),
    GEOMETRY_COLLECTION("GeometryCollection"),
    FEATURE("Feature"),
    FEATURE_COLLECTION("FeatureCollection");

    private final String type;

    public static GeoTypeEnum fromValue(String value) {
        for (GeoTypeEnum geoJsonType : GeoTypeEnum.values()) {
            if (geoJsonType.type.equalsIgnoreCase(value)) {
                return geoJsonType;
            }
        }
        return null;
    }

    public static boolean isPolygon(String type) {
        return Objects.equals(type, POLYGON.type) || Objects.equals(type, MULTI_POLYGON.type);
    }

    public static boolean isLineString(String type) {
        return Objects.equals(type, LINE_STRING.type) || Objects.equals(type, MULTI_LINE_STRING.type);
    }
}