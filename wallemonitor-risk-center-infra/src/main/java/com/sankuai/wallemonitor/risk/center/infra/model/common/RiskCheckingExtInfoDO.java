package com.sankuai.wallemonitor.risk.center.infra.model.common;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RiskCheckingExtInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 风险检查结果
     */
    @Builder.Default
    private List<RiskCheckResultDO> lastCheckResult = new ArrayList<>();

    /**
     * 下一轮是否动态
     */
    @Builder.Default
    private Boolean isNextRoundDynamic = false;
}
