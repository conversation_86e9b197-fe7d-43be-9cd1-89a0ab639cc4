package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class NonTrafficLightWaitInLineDTO {

    private boolean enable;

    @Builder.Default
    private Set<String> allowRightLaneTypeList = new HashSet<>();

    /**
     * 排队车辆停靠的位姿和车身的比值
     * 超过车身的比值，认为异常
     */
    @Builder.Default
    private Double distanceThreshold = 0.1D;

    /**
     * 如果后续2个lane之内，任意一个包含直行，则认为排队正常
     */
    @Builder.Default
    private Integer checkMultiTurnCount = 2;

    public Boolean rightLaneCanUse(String laneType) {
        if (CollectionUtils.isEmpty(allowRightLaneTypeList) || StringUtils.isBlank(laneType)) {
            return false;
        }
        return allowRightLaneTypeList.contains(laneType);
    }

    /**
     * 排队的时候，障碍物车辆应该保持一个正确的车道位姿
     *
     * @param width
     * @param distanceToNearCurbList
     * @return
     */
    public boolean isWaitNormal(Double width, List<Double> distanceToNearCurbList) {
        if (CollectionUtils.isEmpty(distanceToNearCurbList) || distanceToNearCurbList.size() != 2
                || distanceThreshold == null || width == null) {
            return false;
        }
        // 距离左边界和右边界，分别
        Double leftDistance = distanceToNearCurbList.get(0);
        Double rightDistance = distanceToNearCurbList.get(1);
        return leftDistance != null && rightDistance != null
                // 更靠近右边界
                && leftDistance > rightDistance
                // 两边差距过大（超过车身比值），算是异常
                && Math.abs(leftDistance - rightDistance) > width * distanceThreshold;

    }
}
