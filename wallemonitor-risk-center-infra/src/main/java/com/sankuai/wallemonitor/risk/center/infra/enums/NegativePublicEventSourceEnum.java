package com.sankuai.wallemonitor.risk.center.infra.enums;

import com.dianping.lion.client.util.CollectionUtils;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 负外部性事件来源枚举
 */
@AllArgsConstructor
@Getter
public enum NegativePublicEventSourceEnum {
    DEFAULT(0, "默认"),
    MEITUAN_CUSTOMER_SERVICE(1, "美团客服"),
    GOVERNMENT(2, "政府"),
    TRAFFIC_POLICE(3, "交警"),
    STREET_OFFICE(4, "街道"),
    PROPERTY_MANAGEMENT(5, "物业"),
    ECONOMIC_INFORMATION_OFFICE(6, "经信办"),
    CYBERSPACE_ADMINISTRATION(7, "网信办"),
    SELF_DRIVING_OFFICE(8, "自驾办"),
    DEVELOPMENT_AND_REFORM_COMMISSION(9, "发改委"),
    TRANSPORTATION_COMMISSION(10, "交通委"),
    HOTLINE_12345(11, "12345"),
    VIP(12, "VIP"),
    TRADITIONAL_MEDIA(13, "传统媒体（电视台/广播/报纸）"),
    NEW_MEDIA(14, "新兴媒体（短视频/社交媒体/新闻客户端）"),
    SELF_MEDIA(15, "自媒体（个人博客/微信公众号/大V账号）"),
    OTHER(16, "其他");


    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static NegativePublicEventSourceEnum fromCode(Integer code) {
        for (NegativePublicEventSourceEnum item : NegativePublicEventSourceEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据code列表获取枚举列表
     *
     * @param sourceList
     * @return
     */
    public static List<NegativePublicEventSourceEnum> getEnumByCode(List<Integer> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return sourceList.stream()
                .map(NegativePublicEventSourceEnum::fromCode)
                .filter(java.util.Objects::nonNull)
                .collect(java.util.stream.Collectors.toList());
    }

}
