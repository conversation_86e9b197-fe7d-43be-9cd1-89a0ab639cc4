package com.sankuai.wallemonitor.risk.center.infra.convert;

import com.sankuai.wallemonitor.risk.center.infra.convert.mapper.EnumsConvertMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.RiskErrorQueueRecord;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskErrorQueueRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 错误排队检测记录转换器
 */
@Mapper(componentModel = "spring", uses = {EnumsConvertMapper.class})
public interface RiskErrorQueueRecordConvert extends SingleConvert<RiskErrorQueueRecord, RiskErrorQueueRecordDO> {

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusEnum")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeletedEnumFromInteger")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseTypeEnum")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "parseVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "parseVehicleRuntimeInfoContextDO")
    RiskErrorQueueRecordDO toDO(RiskErrorQueueRecord riskErrorQueueRecord);

    @Override
    @Mapping(source = "status", target = "status", qualifiedByName = "toDetectRecordStatusInteger")
    @Mapping(source = "type", target = "type", qualifiedByName = "toRiskCaseType")
    @Mapping(source = "isDeleted", target = "isDeleted", qualifiedByName = "toIsDeleteInteger")
    @Mapping(source = "stagnationCounter", target = "stagnationCounter", qualifiedByName = "serializeVehicleCounter")
    @Mapping(source = "vehicleRuntimeInfoSnapshot", target = "vehicleRuntimeInfoSnapshot", qualifiedByName = "serializeVehicleRuntimeInfoContextDO")
    RiskErrorQueueRecord toPO(RiskErrorQueueRecordDO riskErrorQueueRecordDO);
} 