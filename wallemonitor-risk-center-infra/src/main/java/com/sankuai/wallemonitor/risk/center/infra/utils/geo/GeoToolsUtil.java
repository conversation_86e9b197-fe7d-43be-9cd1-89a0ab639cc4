package com.sankuai.wallemonitor.risk.center.infra.utils.geo;

import com.github.davidmoten.rtree.geometry.Geometries;
import com.github.davidmoten.rtree.geometry.Rectangle;
import com.meituan.mars.common.constant.Conversions;
import com.meituan.mars.common.util.CoordinateUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.SystemException;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PolygonDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.OptionalDouble;
import java.util.stream.Collectors;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.geotools.geometry.jts.JTS;
import org.geotools.referencing.CRS;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Envelope;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.geom.PrecisionModel;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.opengis.referencing.FactoryException;
import org.opengis.referencing.crs.CoordinateReferenceSystem;
import org.opengis.referencing.operation.MathTransform;

@Slf4j
public class GeoToolsUtil {

    private static final Integer DEFAULT_ZONE = 32650;
    private static String WGS84 = "EPSG:4326";
    private static String UTM = "EPSG:32650";
    private static String UTM_TEMPLATE = "EPSG:%d";

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel(), 4326);

    private static final int EARTH_RADIUS = 6371000; // 地球半径，单位为米

    private static final double BUFFER_DISTANCE_METERS = 111000.0;
    private static final GeometryFactory geometryFactory = new GeometryFactory();

    public static PositionDO utmToGcj02(Double x, Double y) {
        PositionDO positionDOWgs = utmToWgs84(x, y);
        PositionDO positionDOGcj02 = wgs84ToGcj02(positionDOWgs.getLongitude(), positionDOWgs.getLatitude());
        return positionDOGcj02;
    }

    public static PositionDO utmToWgs84(Double x, Double y) {
        Double[] res = transform(UTM, WGS84, y, x);
        return PositionDO.builder().longitude(res[1]).latitude(res[0]).coordinateSystem(CoordinateSystemEnum.WGS84)
                .build();
    }

    /**
     * utm转wgs84带区号（北半球）
     * 
     * @param x
     * @param y
     * @return
     */
    public static PositionDO utmToWgs84WithZone(Double x, Double y, Integer zone) {
        String utm = String.format(UTM_TEMPLATE, Optional.ofNullable(zone).orElse(DEFAULT_ZONE));
        Double[] res = transform(utm, WGS84, y, x);
        return PositionDO.builder().longitude(res[1]).latitude(res[0]).coordinateSystem(CoordinateSystemEnum.WGS84)
                .build();
    }

    public static PositionDO wgs84ToGcj02(Double longitude, Double latitude) {
        com.meituan.mars.common.domain.Coordinate coordinate = CoordinateUtil.transform(latitude, longitude,
                Conversions.GPS2MARS);
        return PositionDO.builder().latitude(coordinate.getLatitude()).longitude(coordinate.getLongitude())
                .coordinateSystem(
                        CoordinateSystemEnum.GCJ02).build();
    }

    public static PositionDO gcj02Wgs84(Double longitude, Double latitude) {
        com.meituan.mars.common.domain.Coordinate coordinate = CoordinateUtil.transform(latitude, longitude,
                Conversions.MARS2GPS);
        return PositionDO.builder().latitude(coordinate.getLatitude()).longitude(coordinate.getLongitude())
                .coordinateSystem(CoordinateSystemEnum.WGS84).build();
    }

    /**
     * 计算点到线段的距离
     */
    public static double pointToLineSegmentDistance(
            double lon, double lat,           // 点坐标
            double lon1, double lat1,         // 线段起点
            double lon2, double lat2          // 线段终点
    ) {
        // 将经纬度转换为弧度
        double radLat = Math.toRadians(lat);
        double radLon = Math.toRadians(lon);
        double radLat1 = Math.toRadians(lat1);
        double radLon1 = Math.toRadians(lon1);
        double radLat2 = Math.toRadians(lat2);
        double radLon2 = Math.toRadians(lon2);

        // 计算点到线段的投影参数
        double A = (radLon2 - radLon1) * Math.cos((radLat1 + radLat2) / 2);
        double B = radLat2 - radLat1;
        double C = -A * radLon1 - B * radLat1;
        double denominator = Math.sqrt(A * A + B * B);

        final double EPSILON = 1e-10;
        if (Math.abs(denominator) < EPSILON) {
            // 如果线段起点和终点重合，直接计算点到起点的距离
            return calculateHaversineDistance(lat, lon, lat1, lon1);
        }

        // 计算投影点参数
        double param = -(A * radLon + B * radLat + C) / (A * A + B * B);

        // 确定最近点
        double nearestLon, nearestLat;
        if (param < 0) {
            // 最近点是线段起点
            nearestLon = lon1;
            nearestLat = lat1;
        } else if (param > 1) {
            // 最近点是线段终点
            nearestLon = lon2;
            nearestLat = lat2;
        } else {
            // 最近点在线段上
            nearestLon = Math.toDegrees(radLon1 + param * (radLon2 - radLon1));
            nearestLat = Math.toDegrees(radLat1 + param * (radLat2 - radLat1));
        }

        // 计算点到最近点的距离
        return calculateHaversineDistance(lat, lon, nearestLat, nearestLon);
    }

    public static Double pointToLineSegmentDistance(PositionDO pointA, List<PositionDO> pointList) {
        // 1. 参数检查
        Pair<Double, Pair<PositionDO, PositionDO>> pair = pointToLineSegmentDistanceWithPoint(pointA, pointList);
        if (pair == null) {
            return null;
        }
        return pair.getKey();
    }

    public static Pair<Double, Pair<PositionDO, PositionDO>> pointToLineSegmentDistanceWithPoint(PositionDO pointA,
            List<PositionDO> pointList) {
        // 1. 参数检查
        if (pointA == null || CollectionUtils.isEmpty(pointList) || pointList.size() < 2) {
            return null;
        }
        double minDistance = Double.MAX_VALUE;
        Pair<PositionDO, PositionDO> minPoint = null;
        for (int i = 0; i < pointList.size() - 1; i++) {
            PositionDO pointB = pointList.get(i);
            PositionDO pointC = pointList.get(i + 1);
            double distance = pointToLineSegmentDistance(pointA, pointB, pointC);
            if (distance < minDistance) {
                minDistance = distance;
                minPoint = new Pair<>(pointB, pointC);
            }
        }
        if (minDistance >= Double.MAX_VALUE) {
            return null;
        }
        return new Pair<>(minDistance, minPoint);
    }

    /**
     * 排除特定夹角
     * 
     * @param pointA
     * @param pointList
     * @return
     */
    public static Double pointToLineSegmentDistance(PositionDO pointA, List<PositionDO> pointList,
            Pair<PositionDO, PositionDO> direction, Double maxAngle) {
        // 1. 参数检查
        if (pointA == null || CollectionUtils.isEmpty(pointList) || pointList.size() < 2 || direction == null
                || direction.getKey() == null || direction.getValue() == null) {
            return null;
        }
        double minDistance = Double.MAX_VALUE;
        for (int i = 0; i < pointList.size() - 1; i++) {
            // 取线段
            PositionDO pointB = pointList.get(i);
            PositionDO pointC = pointList.get(i + 1);
            // 计算 线段和给定方向的夹角
            // 计算正向和反向夹角
            double angle = angleCalc(direction.getKey(), direction.getValue(), pointB, pointC);
            double reverseAngle = angleCalc(direction.getValue(), direction.getKey(), pointB, pointC);
            double distance = pointToLineSegmentDistance(pointA, pointB, pointC);
            // 判断是否需要舍弃这条边
            // 同向时，夹角不应超过 maxAngle
            // 反向时，反向夹角不应超过 maxAngle
            boolean shouldSkip = angle <= 90 ? angle > maxAngle : reverseAngle > maxAngle;
            if (shouldSkip) {
                continue;
            }
            if (distance < minDistance) {
                minDistance = distance;
            }
        }
        if (minDistance >= Double.MAX_VALUE) {
            return null;
        }
        return minDistance;
    }

    /**
     * 与多边形最近的两个和行径线的近似平行边的距离
     * key是同向边，value是反向边
     *
     * @param pointA
     * @param pointList
     * @return
     */
    public static Pair<Double, Double> pointToPolygonParallelSideDistance(PositionDO pointA, List<PositionDO> pointList,
            Pair<PositionDO, PositionDO> direction, Double maxAngle) {
        // 1. 参数检查
        if (pointA == null || CollectionUtils.isEmpty(pointList) || pointList.size() < 2 || direction == null
                || direction.getKey() == null || direction.getValue() == null) {
            return null;
        }
        //
        Double sameSideDistance = Double.MAX_VALUE;
        Double anotherDistance = Double.MAX_VALUE;
        for (int i = 0; i < pointList.size() - 1; i++) {
            // 取线段
            PositionDO pointB = pointList.get(i);
            PositionDO pointC = pointList.get(i + 1);
            // 计算 线段和给定方向的夹角
            // 计算正向和反向夹角
            double angle = angleCalc(direction.getKey(), direction.getValue(), pointB, pointC);
            double reverseAngle = angleCalc(direction.getValue(), direction.getKey(), pointB, pointC);
            double distance = pointToLineSegmentDistance(pointA, pointB, pointC);
            // 判断是否需要舍弃这条边
            // 同向时，夹角不应超过 maxAngle
            // 反向时，反向夹角不应超过 maxAngle
            boolean shouldSkip = angle <= 90 ? angle > maxAngle : reverseAngle > maxAngle;
            if (shouldSkip) {
                continue;
            }
            if (angle <= 90 && distance < sameSideDistance) {
                // 如果是同向
                sameSideDistance = distance;
            } else if (angle > 90 && distance < anotherDistance) {
                // 如果是反向线段
                anotherDistance = distance;
            }
        }
        if (sameSideDistance >= Double.MAX_VALUE) {
            sameSideDistance = null;
        }
        if (anotherDistance >= Double.MAX_VALUE) {
            anotherDistance = null;
        }
        return new Pair<>(sameSideDistance, anotherDistance);
    }

    /**
     * 计算点到线段的最短距离（大圆距离）
     * 使用 WGS84 坐标系，返回距离单位为米
     *
     * @param pointA 点A的坐标
     * @param pointB 线段起点B的坐标
     * @param pointC 线段终点C的坐标
     * @return 最短距离，单位米
     */
    public static double pointToLineSegmentDistance(PositionDO pointA, PositionDO pointB, PositionDO pointC) {
        // 1. 参数检查
        if (pointA == null || pointB == null || pointC == null) {
            throw new IllegalArgumentException("Input points cannot be null");
        }

        // 2. 如果线段起点和终点重合，直接返回点到起点的距离
        if (pointB.getLongitude().equals(pointC.getLongitude()) && pointB.getLatitude().equals(pointC.getLatitude())) {
            return distance(pointA, pointB);
        }

        // 3. 计算点到线段两端点的距离
        double distAB = distance(pointA, pointB);  // A到B的距离
        double distAC = distance(pointA, pointC);  // A到C的距离
        double distBC = distance(pointB, pointC);  // B到C的距离（线段长度）

        // 4. 使用余弦定理计算角BAC和BCA
        double cosBAC = (distAB * distAB + distBC * distBC - distAC * distAC) / (2 * distAB * distBC);
        double cosBCA = (distAC * distAC + distBC * distBC - distAB * distAB) / (2 * distAC * distBC);

        // 5. 如果角BAC或BCA大于等于90度，则最短距离就是点到端点的距离
        if (cosBAC <= 0) {
            return distAB;  // 返回A到B的距离
        }
        if (cosBCA <= 0) {
            return distAC;  // 返回A到C的距离
        }

        // 6. 计算点到线段所在大圆的距离
        // 将经纬度转换为弧度
        double latA = Math.toRadians(pointA.getLatitude());
        double lonA = Math.toRadians(pointA.getLongitude());
        double latB = Math.toRadians(pointB.getLatitude());
        double lonB = Math.toRadians(pointB.getLongitude());
        double latC = Math.toRadians(pointC.getLatitude());
        double lonC = Math.toRadians(pointC.getLongitude());

        // 计算法向量
        double[] normalVector = crossProduct(
                new double[] {Math.cos(latB) * Math.cos(lonB), Math.cos(latB) * Math.sin(lonB), Math.sin(latB)},
                new double[] {Math.cos(latC) * Math.cos(lonC), Math.cos(latC) * Math.sin(lonC), Math.sin(latC)});

        // 计算点A的笛卡尔坐标
        double[] pointACart = new double[] {Math.cos(latA) * Math.cos(lonA), Math.cos(latA) * Math.sin(lonA),
            Math.sin(latA)};

        // 计算点到大圆的角度（弧度）
        double sinAngle = Math.abs(dotProduct(normalVector, pointACart))
                / (magnitude(normalVector) * magnitude(pointACart));

        // 7. 返回实际距离（米）
        return EARTH_RADIUS * Math.asin(sinAngle);
    }

    /**
     * 计算两个向量的叉积
     */
    private static double[] crossProduct(double[] v1, double[] v2) {
        return new double[] {v1[1] * v2[2] - v1[2] * v2[1], v1[2] * v2[0] - v1[0] * v2[2],
            v1[0] * v2[1] - v1[1] * v2[0]};
    }

    /**
     * 计算两个向量的点积
     */
    private static double dotProduct(double[] v1, double[] v2) {
        return v1[0] * v2[0] + v1[1] * v2[1] + v1[2] * v2[2];
    }

    /**
     * 计算向量的模
     */
    private static double magnitude(double[] v) {
        return Math.sqrt(dotProduct(v, v));
    }

    /**
     * 坐标转换,y为维度，x为经度
     *
     * @param from
     * @param to
     * @param y
     * @param x
     * @return
     */
    public static Double[] transform(String from, String to, Double y, Double x) {
        Double[] res = new Double[2];
        Coordinate tar = new Coordinate();
        try {
            Coordinate sour = new Coordinate(x, y);
            //这里要选择转换的坐标系是可以随意更换的
            CoordinateReferenceSystem source = CRS.decode(from);
            CoordinateReferenceSystem target = CRS.decode(to);
            // 打印坐标系信息
            log.debug("Source CRS: {}", source.getName());
            log.debug("Target CRS: {}", target.getName());
            MathTransform transform = CRS.findMathTransform(source, target, true);
            //转换
            JTS.transform(sour, tar, transform);
        } catch (FactoryException | org.opengis.referencing.operation.TransformException e) {
            log.error("转换失败", e);
        }
        String[] split = (tar.toString().substring(1, tar.toString().length() - 1)).split(",");
        //经纬度精度
        DecimalFormat fm = new DecimalFormat("0.0000000");
        res[0] = Double.valueOf(fm.format(Double.valueOf(split[0])));
        res[1] = Double.valueOf(fm.format(Double.valueOf(split[1])));
        return res;
    }

    /**
     * 膨胀多边形
     *
     * @param polygon
     * @param bufferDistance
     * @return
     */
    public static Polygon expandPolygon(Polygon polygon, double bufferDistance) {
        try {
            Geometry bufferedGeometry = polygon.buffer(bufferDistance);
            if (bufferedGeometry instanceof Polygon) {
                return (Polygon) bufferedGeometry;
            } else {
                log.error(JacksonUtils.to(polygon), new SystemException("膨胀后的几何图形不是多边形"));
                return polygon;
            }
        } catch (Exception e) {
            log.error("多边形膨胀失败", e);
            return polygon;
        }
    }

    /**
     * 计算地理围栏的外接矩形
     *
     * @param polygonDO 地理围栏的多边形数据对象
     * @return 外接矩形的坐标数组
     */
    public static Coordinate[] getBoundingRectangle(PolygonDO polygonDO) {
        if (polygonDO == null) {
            return null;
        }
        // 获取 PolygonDO 的坐标，闭环的
        List<Coordinate> coordinates = polygonDO.getClosedCoordinates();
        if (CollectionUtils.isEmpty(coordinates)) {
            return null;
        }
        // 创建一个 JTS Polygon 对象
        Polygon polygon = geometryFactory.createPolygon(coordinates.toArray(new Coordinate[0]));
        // 获取多边形的外接矩形
        Envelope envelope = polygon.getEnvelopeInternal();
        // 创建外接矩形的多边形
        return new Coordinate[]{new Coordinate(envelope.getMinX(), envelope.getMinY()),
                new Coordinate(envelope.getMinX(), envelope.getMaxY()),
                new Coordinate(envelope.getMaxX(), envelope.getMaxY()),
                new Coordinate(envelope.getMaxX(), envelope.getMinY()),
                new Coordinate(envelope.getMinX(), envelope.getMinY()) // 闭合多边形
        };
    }

    /**
     *
     * @param polygonDO
     * @return
     */
    public static Rectangle getBoundingRectanglePolygon(PolygonDO polygonDO) {
        Coordinate[] boundary = getBoundingRectangle(polygonDO);
        if (boundary == null) {
            return null;
        }
        // [0]: [最小x,最大y]
        // [1]: [最大x,最大y]
        // [2]: [最大x,最小y]
        // [3]: [最小x,最小y]
        return Geometries.rectangle(boundary[1].x, boundary[3].y, boundary[3].x, boundary[1].y);
    }

    /**
     * 按照顺时针顺序排列坐标，并对非闭合的左边，进行闭合
     *
     * @param coordinates 输入的坐标数组
     * @return 按照顺时针顺序排列的坐标数组
     */
    public static Coordinate[] sortCoordinatesClockwise(Coordinate[] coordinates) {
        if (coordinates.length < 3) {
            return coordinates;
        }
        if (coordinates[0].getX() == (coordinates[coordinates.length - 1]).x
                && coordinates[0].getY() == (coordinates[coordinates.length - 1]).y) {
            return coordinates;
        }
        // 计算多边形的质心
        double centroidX = Arrays.stream(coordinates).mapToDouble(c -> c.x).average().orElse(0);
        double centroidY = Arrays.stream(coordinates).mapToDouble(c -> c.y).average().orElse(0);

        // 创建一个新的坐标数组，并复制原始坐标
        Coordinate[] sortedCoordinates = Arrays.copyOf(coordinates, coordinates.length);

        // 按照顺时针顺序排列坐标
        Arrays.sort(sortedCoordinates, new Comparator<Coordinate>() {
            @Override
            public int compare(Coordinate c1, Coordinate c2) {
                double angle1 = Math.atan2(c1.y - centroidY, c1.x - centroidX);
                double angle2 = Math.atan2(c2.y - centroidY, c2.x - centroidX);
                return Double.compare(angle1, angle2);
            }
        });

        // 确保坐标数组是闭合的
        if (!sortedCoordinates[0].equals(sortedCoordinates[sortedCoordinates.length - 1])) {
            sortedCoordinates = Arrays.copyOf(sortedCoordinates, sortedCoordinates.length + 1);
            sortedCoordinates[sortedCoordinates.length - 1] = sortedCoordinates[0];
        }

        return sortedCoordinates;
    }

    /**
     * 闭环坐标点
     *
     * @param coordinates
     * @return
     */
    public static List<Coordinate> sortCoordinatesClockwise(List<Coordinate> coordinates) {
        if (CollectionUtils.isEmpty(coordinates)) {
            return coordinates;
        }
        return Arrays.asList(sortCoordinatesClockwise(coordinates.toArray(new Coordinate[0])));
    }

    /**
     * 按照顺时针顺序排列坐标，并对非闭合的坐标进行闭合
     *
     * @param positionDOList 输入的坐标列表
     * @return 按照顺时针顺序排列的坐标列表
     */
    public static List<PositionDO> sortCoordinatesClockwise(List<PositionDO> positionDOList,
            CoordinateSystemEnum coordinateSystem) {
        if (positionDOList.size() < 3) {
            return positionDOList;
        }

        // 将 PositionDO 转换为 Coordinate
        List<Coordinate> coordinates = positionDOList.stream()
                .map(p -> new Coordinate(p.getLongitude(), p.getLatitude())).collect(Collectors.toList());

        // 计算多边形的质心
        double centroidX = coordinates.stream().mapToDouble(c -> c.x).average().orElse(0);
        double centroidY = coordinates.stream().mapToDouble(c -> c.y).average().orElse(0);

        // 按照顺时针顺序排列坐标
        coordinates.sort((c1, c2) -> {
            double angle1 = Math.atan2(c1.y - centroidY, c1.x - centroidX);
            double angle2 = Math.atan2(c2.y - centroidY, c2.x - centroidX);
            return Double.compare(angle1, angle2);
        });

        // 确保坐标列表是闭合的
        if (!coordinates.get(0).equals(coordinates.get(coordinates.size() - 1))) {
            coordinates.add(coordinates.get(0));
        }

        // 将排序后的 Coordinate 转换回 PositionDO

        return coordinates.stream().map(c -> PositionDO.getPosition(c.x, c.y, coordinateSystem))
                .collect(Collectors.toList());
    }

    /**
     * 膨胀多边形
     *
     * @param polygonDO
     * @param bufferDistanceMeters
     * @return
     */
    public static PolygonDO expandPolygonDO(PolygonDO polygonDO, double bufferDistanceMeters) {
        try {
            // 获取 PolygonDO 的坐标
            List<Coordinate> coordinates = polygonDO.getClosedCoordinates();
            CoordinateSystemEnum coordinateSystemEnum = polygonDO.getCoordinateSystemEnum();
            // 创建一个 JTS Polygon 对象
            Polygon polygon = geometryFactory.createPolygon(coordinates.toArray(new Coordinate[0]));
            // 计算缓冲距离（根据坐标系进行转换）
            double bufferDistanceDegrees;
            if (coordinateSystemEnum == CoordinateSystemEnum.GCJ02
                    || coordinateSystemEnum == CoordinateSystemEnum.WGS84) {
                bufferDistanceDegrees = bufferDistanceMeters / BUFFER_DISTANCE_METERS; // 粗略地将米转换为度
            } else {
                bufferDistanceDegrees = bufferDistanceMeters; // UTM 不需要转换
            }
            // 膨胀多边形
            Polygon expandedPolygon = expandPolygon(polygon, bufferDistanceDegrees);
            // 创建一个新的 PolygonDO 对象，使用膨胀后的坐标
            PolygonDO expandedPolygonDO = PolygonDO.builder().coordinateSystemEnum(coordinateSystemEnum).build();
            List<PositionDO> expandedPoints = Arrays.stream(expandedPolygon.getCoordinates()).map(c -> PositionDO
                            .builder().longitude(c.x).latitude(c.y).coordinateSystem(coordinateSystemEnum).build())
                    .collect(Collectors.toList());
            expandedPolygonDO.setPoints(expandedPoints);
            return expandedPolygonDO;
        } catch (Exception e) {
            log.error("处理 PolygonDO 膨胀时发生错误", e);
            return null;
        }
    }

    // 使用Haversine公式计算两个WGS84坐标之间的距离，单位为米
    private static double calculateHaversineDistance(double lat1, double lon1, double lat2, double lon2) {
        double latDistance = toRadians(lat2 - lat1);
        double lonDistance = toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2) + Math.cos(toRadians(lat1))
                * Math.cos(toRadians(lat2)) * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return EARTH_RADIUS * c; // 计算的距离单位为米
    }

    // 将角度转换为弧度
    private static double toRadians(double degrees) {
        return degrees * Math.PI / 180;
    }

    /**
     * 两个坐标进行距离计算
     *
     * @param obstaclePositionDO
     * @param location
     */
    public static Double distance(PositionDO obstaclePositionDO, PositionDO location) {
        if (obstaclePositionDO == null || location == null || obstaclePositionDO.invalid() || location.invalid()) {
            return null;
        }
        return calculateHaversineDistance(obstaclePositionDO.getLatitude(), obstaclePositionDO.getLongitude(),
                location.getLatitude(), location.getLongitude());

    }

    /**
     * 根据输入的坐标系，进行转换
     *
     * @param positionDO
     * @param coordinateSystemEnum
     * @return
     */
    public static PositionDO transferCoordinateSystemEnum(PositionDO positionDO,
            CoordinateSystemEnum coordinateSystemEnum) {
        if (positionDO.getCoordinateSystem() == coordinateSystemEnum) {
            return positionDO;
        }
        switch (coordinateSystemEnum) {
            case WGS84:
                if (positionDO.getCoordinateSystem() == CoordinateSystemEnum.GCJ02) {
                    return gcj02Wgs84(positionDO.getLongitude(), positionDO.getLatitude());
                } else if (positionDO.getCoordinateSystem() == CoordinateSystemEnum.UTM) {
                    return utmToWgs84(positionDO.getLongitude(), positionDO.getLatitude());
                }
                break;
            case GCJ02:
                if (positionDO.getCoordinateSystem() == CoordinateSystemEnum.WGS84) {
                    return wgs84ToGcj02(positionDO.getLongitude(), positionDO.getLatitude());
                } else if (positionDO.getCoordinateSystem() == CoordinateSystemEnum.UTM) {
                    return utmToGcj02(positionDO.getLongitude(), positionDO.getLatitude());
                }
                break;
            case UTM:
                if (positionDO.getCoordinateSystem() == CoordinateSystemEnum.WGS84) {
                    return wgs84ToUtm(positionDO.getLongitude(), positionDO.getLatitude());
                } else if (positionDO.getCoordinateSystem() == CoordinateSystemEnum.GCJ02) {
                    return gcj02ToUtm(positionDO.getLongitude(), positionDO.getLatitude());
                }
                break;
            default:
                log.error(coordinateSystemEnum.name(), new SystemException("不支持的坐标系!"));
        }

        return null;
    }

    public static PositionDO wgs84ToUtm(Double longitude, Double latitude) {
        Double[] res = transform(WGS84, UTM, longitude, latitude);
        return PositionDO.builder().longitude(res[0]).latitude(res[1]).coordinateSystem(CoordinateSystemEnum.UTM)
                .build();
    }

    public static PositionDO gcj02ToUtm(Double longitude, Double latitude) {
        PositionDO positionDOWgs = gcj02Wgs84(longitude, latitude);
        return wgs84ToUtm(positionDOWgs.getLongitude(), positionDOWgs.getLatitude());
    }

    /**
     * 判断两个向量的夹角是否小于给定的角度（基于WGS84坐标系）
     *
     * @param p1 第一个向量的起点
     * @param p2 第一个向量的终点
     * @param p3 第二个向量的起点
     * @param p4 第二个向量的终点
     * @param angle 给定的角度（单位：度）
     * @return true 如果夹角小于给定角度，否则 false
     */
    public static boolean isAngleLessThan(PositionDO p1, PositionDO p2, PositionDO p3, PositionDO p4, double angle) {
        Double theta = angleCalc(p1, p2, p3, p4);
        if (theta == null) {
            return false;
        }
        // 判断夹角是否小于给定的角度
        return theta < angle;
    }

    /**
     * 计算夹角，0,180
     * 
     * @param p1
     * @param p2
     * @param p3
     * @param p4
     * @return
     */
    public static Double angleCalc(PositionDO p1, PositionDO p2, PositionDO p3, PositionDO p4) {
        if (p1 == null || p2 == null || p3 == null || p4 == null) {
            return null;
        }
        boolean allSame = p1.getCoordinateSystem() == p2.getCoordinateSystem()
                && p1.getCoordinateSystem() == p3.getCoordinateSystem()
                && p1.getCoordinateSystem() == p4.getCoordinateSystem();
        if (!allSame) {
            return null;
        }
        // 计算两个向量
        double[] vector1 = calculateVector(p1, p2);
        double[] vector2 = calculateVector(p3, p4);

        // 计算向量的点积
        double dotProduct = vector1[0] * vector2[0] + vector1[1] * vector2[1];

        // 计算向量的叉积
        double crossProduct = vector1[0] * vector2[1] - vector1[1] * vector2[0];

        // 计算夹角（单位：度）
        double theta = Math.toDegrees(Math.atan2(Math.abs(crossProduct), dotProduct));

        // 判断夹角是否小于给定的角度
        return theta;
    }

    private static double[] calculateVector(PositionDO p1, PositionDO p2) {
        // 使用平面近似计算向量，但考虑到经度的缩放
        double latMid = Math.toRadians((p1.getLatitude() + p2.getLatitude()) / 2.0);
        double x = (p2.getLongitude() - p1.getLongitude()) * Math.cos(latMid);
        double y = p2.getLatitude() - p1.getLatitude();
        return new double[]{x, y};
    }


    /**
     * 计算球面向量的方向余弦
     *
     * @param p1 起点
     * @param p2 终点
     * @return 球面向量的方向余弦 [x, y, z]
     */
    private static double[] calculateSphericalVector(PositionDO p1, PositionDO p2) {
        // 将经纬度转换为弧度
        double lat1 = Math.toRadians(p1.getLatitude());
        double lon1 = Math.toRadians(p1.getLongitude());
        double lat2 = Math.toRadians(p2.getLatitude());
        double lon2 = Math.toRadians(p2.getLongitude());

        // 计算球面向量的方向余弦
        double x = Math.cos(lat2) * Math.cos(lon2) - Math.cos(lat1) * Math.cos(lon1);
        double y = Math.cos(lat2) * Math.sin(lon2) - Math.cos(lat1) * Math.sin(lon1);
        double z = Math.sin(lat2) - Math.sin(lat1);

        return new double[]{x, y, z};
    }

    /**
     * 从点列表中获取最近的点
     * 
     * @param vehicleCurPosition
     * @param routePosition
     * @return
     */
    public static Pair<PositionDO, Integer> getNearestPointFromPointList(PositionDO vehicleCurPosition,
            List<PositionDO> routePosition) {
        if (CollectionUtils.isEmpty(routePosition) || vehicleCurPosition == null) {
            return null;
        }
        // 取距离最小的
        double minDistance = Double.MAX_VALUE;
        int minIndex = -1;
        for (int i = 0; i < routePosition.size(); i++) {
            double distance = GeoToolsUtil.distance(vehicleCurPosition, routePosition.get(i));
            if (distance < minDistance) {
                minDistance = distance;
                minIndex = i;
            }
        }
        if (minIndex == -1) {
            return null;
        }
        return new Pair<>(routePosition.get(minIndex), minIndex);
    }

    /**
     * 将点转换为WKT格式
     * 
     * @return
     */
    public static String point2WKT(Point point) {
        if (point == null) {
            return null;
        }
        return new WKTWriter().write(point);
    }

    /**
     * 将点转换为WKT格式
     *
     * @return
     */
    public static String position2WKT(PositionDO positionDO) {
        if (positionDO == null) {
            return null;
        }
        return point2WKT(positionDO.toPoint());
    }

    /**
     * 将点转换为Position
     *
     * @return
     */
    public static PositionDO wkt2Wgs84Position(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        try {
            Geometry geometry = new WKTReader(GEOMETRY_FACTORY).read(text);
            Point point = geometry instanceof Point ? (Point)geometry : null;
            if (point == null) {
                return null;
            }
            // 默认就是84
            return PositionDO.getPosition(point.getX(), point.getY(), CoordinateSystemEnum.WGS84);
        } catch (org.locationtech.jts.io.ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static Point toPointFromLocation(PositionDO positionDO) {
        if (positionDO == null) {
            return null;
        }
        // 使用 WGS84 的 SRID (4326)
        GeometryFactory factory = new GeometryFactory(new PrecisionModel(), 4326);
        return factory.createPoint(new Coordinate(positionDO.getLongitude(),  // x 坐标使用经度
                positionDO.getLatitude()    // y 坐标使用纬度
        ));
    }

    public static PositionDO toPositionFromPoint(Point point) {
        if (point == null) {
            return null;
        }
        return PositionDO.getPosition(point.getX(), point.getY(), CoordinateSystemEnum.WGS84);
    }

    /**
     * 获取一个点的外接矩形（正方形），distance 是中心点到边的距离（单位：米）
     *
     * @param point WGS84坐标点
     * @param distance 中心点到边的距离（米）
     * @return 正方形边界多边形
     */

    /**
     * 获取一个点的外接矩形（正方形），distance 是中心点到边的距离（单位：米）
     *
     * @param point WGS84坐标点
     * @param distance 中心点到边的距离（米）
     * @return 正方形边界多边形，如果输入无效返回null
     */
    public static Polygon getBoundingPolygon(Point point, Double distance) {
        if (point == null || distance == null || distance <= 0) {
            return null;
        }

        // 获取中心点坐标
        double centerLon = point.getX();
        double centerLat = point.getY();

        // 计算四个方向的偏移量（考虑纬度对经度的影响）
        double latOffset = distance / BUFFER_DISTANCE_METERS;
        double lonOffset = latOffset / Math.cos(Math.toRadians(centerLat));

        // 创建正方形边界坐标（顺时针顺序）
        Coordinate[] coordinates = new Coordinate[] {new Coordinate(centerLon - lonOffset, centerLat - latOffset), // 左下
            new Coordinate(centerLon - lonOffset, centerLat + latOffset), // 左上
            new Coordinate(centerLon + lonOffset, centerLat + latOffset), // 右上
            new Coordinate(centerLon + lonOffset, centerLat - latOffset), // 右下
            new Coordinate(centerLon - lonOffset, centerLat - latOffset)  // 闭合
        };
        return GEOMETRY_FACTORY.createPolygon(coordinates);
    }

    /**
     * 地理形状转wkt
     * 
     * @param extendPolygon
     * @return
     */
    public static String geometry2WKT(Polygon extendPolygon) {
        if (extendPolygon == null) {
            return null;
        }
        return new WKTWriter().write(extendPolygon);
    }

    /**
     * 计算点列的中心
     * @param positionDOList
     * */
    public static PositionDO calculateCenter(List<PositionDO> positionDOList) {
        // 计算经纬度的中心
        OptionalDouble latOption = positionDOList.stream().map(PositionDO::getLatitude).mapToDouble(x -> x)
                .average();

        OptionalDouble lonOption = positionDOList.stream().map(PositionDO::getLongitude).mapToDouble(x -> x)
                .average();

        if(!latOption.isPresent() || !lonOption.isPresent()) {
            return null;
        }

        return PositionDO.builder()
                .latitude(latOption.getAsDouble())
                .longitude(lonOption.getAsDouble())
                .coordinateSystem(CoordinateSystemEnum.GCJ02).build();

    }

}