package com.sankuai.wallemonitor.risk.center.infra.utils.compare;

import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityBaseChangeRecordDTO {


    /**
     * 实体类型
     */
    String entityId;

    /**
     * 更新时间
     */
    Date updateTime;

    /**
     * 具体变更字段
     */
    List<EntityFieldChangeRecordDTO> changes;
}
