package com.sankuai.wallemonitor.risk.center.infra.utils.lion;

import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.constant.CommonConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.StringMessageFormatter;
import com.sankuai.wallemonitor.risk.center.infra.utils.applicationutils.SpringUtils;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Lion config tools
 */
@Slf4j
public final class LionConfigUtils {


    private LionConfigUtils() {
    }

    public static ConfigRepository configRepository(final String username, final String passwd, final String appkey) {
        return configRepository(username, passwd, appkey, null);
    }

    public static ConfigRepository configRepository(final String username, final String passwd, final String appkey,
            final String group) {
        ConfigRepository config;
        if (StringUtils.isNotBlank(appkey) && StringUtils.isNotBlank(group)) {
            config = Lion.getConfigRepository(appkey, group, username, passwd);
        } else if (StringUtils.isNotBlank(appkey) && StringUtils.isBlank(group)) {
            config = Lion.getConfigRepository(appkey, username, passwd);
        } else {
            config = Lion.getConfigRepository(username, group);
        }

        Objects.requireNonNull(config == null,
                String.format("Lion.getConfigRepository is null, appkey: %s,  group: %s", appkey, group));

        return config;
    }

    public static boolean setValue(final String username, final String passwd, final String appkey, final String key,
            final String value) {
        return configRepository(username, passwd, appkey).setValue(key, value);
    }

    public static boolean setValue(final String username, final String passwd, final String appkey, final String group,
            final String key, final String value) {
        return configRepository(username, passwd, appkey, group).setValue(key, value);
    }

    public static boolean setValue(final String username, final String passwd, final String appkey, final String key,
            final String value, final String set,
            final String swimlane, final String desc) {
        return configRepository(username, passwd, appkey).setValue(key, value, set, swimlane, desc);
    }


    public static boolean setValue(final String username, final String passwd, final String appkey, final String group,
            final String key, final String value,
            final String set, final String swimlane, final String desc) {
        return configRepository(username, passwd, appkey, group).setValue(key, value, set, swimlane, desc);
    }

    public static Map<String, String> getConfigs(final String username, final String passwd, final String appkey) {
        return configRepository(username, passwd, appkey).getConfigs();
    }

    public static String getValue(final String username, final String passwd, final String appkey, final String key) {
        return configRepository(username, passwd, appkey).get(key);
    }

    public static String getValue(final String username, final String passwd, final String appkey, final String key,
            final String defaultValue) {
        return configRepository(username, passwd, appkey).get(key, defaultValue);
    }

    public static String getString(String key) {
        String appKey = SpringUtils.getPropertiesValue(CommonConstant.APP_NAME_KEY);
        String userName = SpringUtils.getPropertiesValue(CommonConstant.LION_USERNAME);
        String pwd = SpringUtils.getPropertiesValue(CommonConstant.LION_PWD);
        return configRepository(userName, pwd, appKey).get(key);
    }

    public static <T> T getClass(String key, Class<T> clazz) {
        String appKey = SpringUtils.getPropertiesValue(CommonConstant.APP_NAME_KEY);
        String userName = SpringUtils.getPropertiesValue(CommonConstant.LION_USERNAME);
        String pwd = SpringUtils.getPropertiesValue(CommonConstant.LION_PWD);
        return configRepository(userName, pwd, appKey).getBean(key, clazz);
    }

    public static Integer getInteger(String key) {
        String appKey = SpringUtils.getPropertiesValue(CommonConstant.APP_NAME_KEY);
        String userName = SpringUtils.getPropertiesValue(CommonConstant.LION_USERNAME);
        String pwd = SpringUtils.getPropertiesValue(CommonConstant.LION_PWD);
        return configRepository(userName, pwd, appKey).getIntValue(key);
    }

    public static <T> List<T> getList(String key, Class<T> tClass) {
        String appKey = SpringUtils.getPropertiesValue(CommonConstant.APP_NAME_KEY);
        String userName = SpringUtils.getPropertiesValue(CommonConstant.LION_USERNAME);
        String pwd = SpringUtils.getPropertiesValue(CommonConstant.LION_PWD);
        return configRepository(userName, pwd, appKey).getList(key, tClass);
    }

    public static <T> Set<T> getSet(String key, Class<T> tClass) {
        List<T> list = getList(key, tClass);
        return list == null ? new HashSet<>() : Sets.newHashSet(list);
    }


    public static <V> Map<String, V> getMap(String key, Class<V> vClass) {
        String appKey = SpringUtils.getPropertiesValue(CommonConstant.APP_NAME_KEY);
        String userName = SpringUtils.getPropertiesValue(CommonConstant.LION_USERNAME);
        String pwd = SpringUtils.getPropertiesValue(CommonConstant.LION_PWD);
        return configRepository(userName, pwd, appKey).getMap(key, vClass);
    }

    public static <V> Map<String, V> getMap(String key, Class<V> vClass, String appKey) {
        String userName = SpringUtils.getPropertiesValue(CommonConstant.LION_USERNAME);
        String pwd = SpringUtils.getPropertiesValue(CommonConstant.LION_PWD);
        return configRepository(userName, pwd, appKey).getMap(key, vClass);
    }

    public static <T> Map<String, T> getGroupAsMap(String group, TypeReference<T> tTypeReference) {
        String appKey = SpringUtils.getPropertiesValue(CommonConstant.APP_NAME_KEY);
        String userName = SpringUtils.getPropertiesValue(CommonConstant.LION_USERNAME);
        String pwd = SpringUtils.getPropertiesValue(CommonConstant.LION_PWD);
        Map<String, String> configList = configRepository(userName, pwd, appKey, group).getConfigs();
        if (MapUtils.isEmpty(configList)) {
            return new HashMap<>();
        }
        Map<String, T> result = new HashMap<>();
        configList.forEach((k, v) -> {
            try {
                result.put(k, JacksonUtils.from(v, tTypeReference));
            } catch (Exception e) {
                log.error(StringMessageFormatter.replaceMsg("getGroupAsMap error, group:{}, key:{}, value:{}", group, k,
                        v), e);
            }
        });
        return result;
    }
}
