package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ParkingAreaMethodsStatusEnum {
    ON("ON"),
    OFF("OFF"),
    MONITOR("MONITOR");

    private final String status;

    // 根据字符串找枚举类型，找不到默认是MONITOR
    public static ParkingAreaMethodsStatusEnum getStatus(String status) {
        return Arrays.stream(ParkingAreaMethodsStatusEnum.values())
                .filter(x -> x.getStatus().equals(status)).findFirst().orElse(MONITOR);
    }
}
