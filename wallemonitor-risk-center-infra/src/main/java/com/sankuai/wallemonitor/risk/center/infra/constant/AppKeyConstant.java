package com.sankuai.wallemonitor.risk.center.infra.constant;

/**
 * appkey枚举
 */
public class AppKeyConstant {


    public static final String MAP_APP_KEY = "com.sankuai.apigw.map.facadecenter";

    public static final String RISK_CENTER_APP_KEY = "com.sankuai.wallemonitor.risk.center";

    /**
     * 基础运力服务
     */
    public static final String BASIC_APP_KEY = "com.sankuai.walledelivery.basic";

    /**
     * 数据总线的appkey
     */
    public static final String MONITOR_ONLINE_APP_KEY = "com.sankuai.wallecmdb.monitor.online";

    /**
     * 消息推送服务的APP key
     */
    public static final String OUTPUT_APP_KEY = "com.sankuai.carosscan.common.output";

    /**
     * 约车系统的appKey
     */
    public static final String VRESVFE_APP_KEY = "com.sankuai.wallemonitor.walle.cms";

    /**
     * EVE回放查询服务
     */
    public static final String EVE_HISTORY_DATA_QUERY_APP_KEY = "com.sankuai.wallecmdb.data.replay";

    /**
     * 开放应用平台appKey
     */
    public static final String XM_OPEN_APP_KEY = "com.sankuai.dxenterprise.open.gateway";

    /**
     * 通用搜索
     */
    public static final String COMMON_SEARCH_APP_KEY = "com.sankuai.walleom.common.search";
}
