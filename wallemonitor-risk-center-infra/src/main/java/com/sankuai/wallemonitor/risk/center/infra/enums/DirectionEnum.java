package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车道方向枚举
 */
@AllArgsConstructor
@Getter
public enum DirectionEnum {
    FORWARD(1, "正向"),
    BACKWARD(2, "反向（未真实使用）"),
    BIDIRECTION(3, "双向（未真实使用）");

    private final int code;
    private final String desc;

    /**
     * 根据 code 查询枚举
     *
     * @param code 方向代码
     * @return 对应的枚举值
     */
    public static DirectionEnum findByCode(int code) {
        for (DirectionEnum direction : DirectionEnum.values()) {
            if (direction.getCode() == code) {
                return direction;
            }
        }
        return null;
    }
}