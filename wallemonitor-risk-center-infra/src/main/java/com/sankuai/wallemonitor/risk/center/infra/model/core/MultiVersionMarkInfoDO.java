package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.wallemonitor.risk.center.infra.annotation.DomainUnique;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCheckResultDO;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 多版本标注结果表
 * </p>
 *
 * <AUTHOR> @since 2025-03-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MultiVersionMarkInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */

    private Long id;

    /**
     * caseId
     */
    @DomainUnique
    private String caseId;

    /**
     * 版本
     */

    private String markVersion;

    /**
     * 类别
     */

    private String category;

    /**
     * 子类别
     */

    private String subCategory;

    /**
     * 轮次
     */
    private Integer round;

    /**
     * 拓展信息
     */

    private RiskCheckResultDO checkResult;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 最近更新时间
     */

    private Date updateTime;

    /**
     * 是否删除
     */
    private IsDeleteEnum isDeleted;

    /**
     * 自动标注
     * 
     * @param checkResultEnum
     * @param checkResult
     * @param operator
     * @param round
     */
    public void autoMark(ISCheckCategoryEnum checkResultEnum, RiskCheckResultDO checkResult, String operator,
            Integer round) {
        if (checkResultEnum != null) {
            // 更新自动标注的结果,不再更新subCategory
            this.category = checkResultEnum.getCategory();
            this.subCategory = checkResultEnum.getSubcategory();
            // 如果是无法识别的情况，此处落表视为有风险
            if (ISCheckCategoryEnum.CANT_FOUND_ANY.equals(checkResultEnum)) {
                this.category = "GOOD";
            }
        }
        this.round = round;
        if (checkResult != null) {
            this.checkResult = checkResult;
        }
    }
}
