package com.sankuai.wallemonitor.risk.center.infra.vto.param;

import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.FridayOpenAiAdapter.ReqMessage;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayPromptVTO.FridayPromptPartDTO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FridayModelParamWithMessageVTO {

    private String vin;
    private Date occurTime;

    private String caseId;
    private List<FridayPromptPartDTO> systemPrompt;


    private List<FridayPromptPartDTO> userPrompt;

    private List<FridayPromptPartDTO> userStepPrompt;
    private String modelName;
    private Integer timeout;
    private Integer beforeTime;
    private String appId;

    /**
     * 转换为Friday请求参数
     *
     * @return
     */
    public List<ReqMessage> toReqMessageList() {
        List<ReqMessage> reqMessageList = new ArrayList<>();
        for (FridayPromptPartDTO promptPartDTO : systemPrompt) {
            reqMessageList.add(promptPartDTO.toReqMessage());
        }
        return reqMessageList;
    }


    /**
     * 转换为Friday请求参数
     *
     * @return
     */
    public List<ReqMessage> toExampleReqMessageList() {
        List<ReqMessage> reqMessageList = new ArrayList<>();
        for (FridayPromptPartDTO promptPartDTO : userStepPrompt) {
            reqMessageList.add(promptPartDTO.toReqMessage());
        }
        return reqMessageList;
    }

    /**
     * 转换为Friday请求参数
     *
     * @return
     */
    public List<ReqMessage> toUserReqMessageList() {
        List<ReqMessage> reqMessageList = new ArrayList<>();
        for (FridayPromptPartDTO promptPartDTO : userPrompt) {
            reqMessageList.add(promptPartDTO.toReqMessage());
        }
        //访问图片并生成一下
        return reqMessageList;
    }

}