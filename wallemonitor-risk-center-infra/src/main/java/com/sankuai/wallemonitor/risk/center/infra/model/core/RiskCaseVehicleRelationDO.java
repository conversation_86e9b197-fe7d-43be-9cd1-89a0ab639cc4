package com.sankuai.wallemonitor.risk.center.infra.model.core;

import com.sankuai.walleeve.domain.enums.CloudTriageEventStatusEnum;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.api.response.vo.RiskCaseVehicleVO;
import com.sankuai.wallemonitor.risk.center.infra.constant.CharConstant;
import com.sankuai.wallemonitor.risk.center.infra.enums.IsDeleteEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.MrmRoleEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseVehicleStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.SystemCheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.VehicleCounterInfoDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Slf4j
public class RiskCaseVehicleRelationDO {


    /**
     * 自增ID
     */
    private Long id;

    /**
     * caseId
     */
    private String caseId;

    /**
     * 外部事件ID
     */
    private String eventId;

    /**
     * vin车架号
     */
    private String vin;

    /**
     * 保障系统生成的traceId,关联坐席处置状态
     */
    private String traceId;

    /**
     * 车辆故障时快照信息
     */
    private VehicleInfoDO vehicleSnapshotInfo;

    /**
     * 停滞计时
     */
    private List<VehicleCounterInfoDO> stagnationCounter;

    /**
     * 云控的控车信息，json字段，包含：控车人misId，控车时间，云控角色
     */
    private RiskVehicleExtInfoDO extInfo;

    /**
     * 车辆处置状态,0|初始化,10|待分配,20|预分配,30|已分配,40|已处置,50|已拒绝,99|已取消
     */
    private RiskCaseVehicleStatusEnum status;

    /**
     * 风险类型
     *
     * @see RiskCaseTypeEnum
     */
    private RiskCaseTypeEnum type;

    /**
     * 车辆时间
     */
    private Date occurTime;


    /**
     * 坐席请求时间
     */
    private Date requestSeatTime;

    /**
     * 取消坐席时间
     */
    private Date cancelSeatTime;

    /**
     * 连入时间
     */
    private Date seatConnectTime;

    /**
     * 退控时间
     */
    private Date seatExitTime;

    /**
     * 坐席介入时间
     */
    private Date seatInterventionTime;

    /**
     * 烽火台呼叫坐席连入时间
     */
    private Date seatResponseTime;


    /**
     * 并排时间
     */
    private String sideBySideTimestamp;

    /**
     * 停滞不当时间戳，记录车辆最后一次发生停滞的时间戳。用于风险分析和处理。
     */
    private Date milliBeginTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最近更新时间
     */
    private Date updateTime;

    /**
     * 用车目的
     */
    private String purpose;

    /**
     * vhr模式
     */
    private String vhrMode;

    /**
     * 云控呼叫原因
     */
    private String callMrmReason;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 是否删除
     */
    @Builder.Default
    private IsDeleteEnum isDeleted = IsDeleteEnum.NOT_DELETED;


    /**
     * 删除记录
     */
    public void remove() {
        isDeleted = IsDeleteEnum.DELETED;
    }


    /**
     * 是否满足停滞时长
     *
     * @return
     */
    public Boolean upToStagnationCount(Integer duration) {
        if (CollectionUtils.isEmpty(stagnationCounter)) {
            return false;
        }
        return stagnationCounter.stream()
                .anyMatch(vehicleCounterInfoDO -> vehicleCounterInfoDO.getDuration() >= duration);

    }

    /**
     * 更新车辆快照信息
     *
     * @param vehicleInfoDO
     */
    public void updateVehicleSnapshotInfo(VehicleInfoDO vehicleInfoDO) {
        SystemCheckUtil.isNotNull(vehicleInfoDO, "车辆信息不能为空");
        this.vehicleSnapshotInfo = vehicleInfoDO;
    }

    /**
     * @param duration
     * @return
     */
    public VehicleCounterInfoDO getStagnationCounterByDuration(Integer duration) {
        if (CollectionUtils.isEmpty(stagnationCounter)) {
            return null;
        }
        //获得满足要求的停滞数据
        return stagnationCounter.stream()
                .filter(vehicleCounterInfoDO -> this.upToStagnationCount(duration))
                .findFirst()
                .orElse(null);
    }

    /**
     * 构造车辆名称 vehicleName + "/" + vehicleId
     *
     * @return
     */
    private String formatVehicleName() {
        if (this.getVehicleSnapshotInfo() == null) {
            return StringUtils.EMPTY;
        }

        return String.format("%s / %s", this.getVehicleSnapshotInfo().getVehicleName(),
                this.getVehicleSnapshotInfo().getVehicleId());
    }

    /**
     * 转换成视图对象
     *
     * @return
     */
    public RiskCaseVehicleVO toRiskCaseVehicleVO() {
        RiskCaseVehicleVO vehicle = RiskCaseVehicleVO.builder()
                .vin(this.getVin())
                .vehicleName(this.formatVehicleName())
                .requestSeatTime(DatetimeUtil.formatTime(this.getRequestSeatTime()))
                .seatConnectTime(DatetimeUtil.formatTime(this.getSeatConnectTime()))
                .cancelSeatTime(DatetimeUtil.formatTime(this.getCancelSeatTime()))
                .seatResponseTime(DatetimeUtil.formatTime(this.getSeatResponseTime()))
                .seatInterventionTime(DatetimeUtil.formatTime(this.getSeatInterventionTime()))
                .build();

        VehicleInfoDO vehicleInfoDO = this.getVehicleSnapshotInfo();
        RiskVehicleExtInfoDO vehicleExtInfoDO = this.getExtInfo();
        if (Objects.nonNull(vehicleInfoDO)) {
            vehicle.setExtInfo(JacksonUtils.to(vehicleInfoDO));
        } else {
            vehicle.setExtInfo(JacksonUtils.to(new HashMap<>()));
        }
        if (Objects.nonNull(vehicleExtInfoDO)) {
            vehicle.setExtraInfo(JacksonUtils.to(vehicleExtInfoDO));
        }

        return vehicle;
    }

    /**
     * 更新traceId重置状态
     *
     * @param traceId
     */
    public boolean updateTraceId(String traceId) {
        if (StringUtils.equals(traceId, this.traceId)) {
            //无需更新
            return false;
        }
        this.traceId = traceId;
        if (status.getCode() > RiskCaseVehicleStatusEnum.INIT.getCode()) {
            //如果这个trace被处置了
            this.status = RiskCaseVehicleStatusEnum.INIT;
        }
        return true;
    }

    /**
     * 更新处置状态和时间
     *
     * @param status
     * @param requestSeatTime
     * @param seatConnectTime
     * @param seatExitTime
     */
    public boolean updateStatus(RiskCaseVehicleStatusEnum status, Date requestSeatTime, Date seatConnectTime,
            Date seatExitTime) {
        if (RiskCaseVehicleStatusEnum.isDisposed(this.status)) {
            //已经结束，无需更新
            return false;
        }
        this.status = status;
        //更新状态
        switch (status) {
            case PRE_ASSIGNED: {
                //请求坐席时间
                this.requestSeatTime = requestSeatTime;
                break;
            }
            case REJECTED:
            case ASSIGNED: {
                //坐席连入时间 或者拒绝时间，当做连入时间
                this.seatConnectTime = seatConnectTime;
                break;
            }
            case DISPOSED: {
                //退控时间
                this.seatExitTime = seatExitTime;
                break;
            }
        }
        return true;
    }

    /**
     * @param mrmSeatMisId
     * @param mrmSeatNo
     * @param roleEnum
     */
    public void updateSeatInfo(String mrmSeatMisId, String mrmSeatNo, MrmRoleEnum roleEnum) {
        RiskVehicleExtInfoDO extInfo = Optional.ofNullable(this.getExtInfo())
                .orElse(RiskVehicleExtInfoDO.builder().build());
        if (StringUtils.isNotBlank(mrmSeatNo)) {
            extInfo.setMrmSeatNo(mrmSeatNo);
        }
        if (StringUtils.isNotBlank(mrmSeatMisId)) {
            extInfo.setMrmMisId(mrmSeatMisId);
        }
        if (Objects.nonNull(roleEnum)) {
            extInfo.setRole(roleEnum);
        }
        this.extInfo = extInfo;
    }

    /**
     * 更新安全区域信息
     *
     * @param safetyAreaDO
     */
    public void updateSafetyAreaInfo(SafetyAreaDO safetyAreaDO) {
        if (Objects.isNull(safetyAreaDO)) {
            return;
        }
        RiskVehicleExtInfoDO extInfo = Optional.ofNullable(this.getExtInfo())
                .orElse(RiskVehicleExtInfoDO.builder().build());
        extInfo.setSafetyAreaType(safetyAreaDO.getType());
        this.extInfo = extInfo;
    }

    /**
     * 更新用车目的
     *
     * @param purpose
     */
    public void updatePurpose(String purpose) {
        this.purpose = purpose;
    }

    /**
     * 更新vhr模式
     *
     * @param vhrMode
     */
    public void updateVhrMode(String vhrMode) {
        this.vhrMode = vhrMode;
    }

    /**
     * 更新车型
     *
     * @param vehicleType
     */
    public void updateVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    /**
     * 获取扩展信息
     *
     * @return
     */
    public String getExtInfoStr() {
        if (vehicleSnapshotInfo == null) {
            return CharConstant.CHAR_EMPTY;
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (BooleanUtils.toBoolean(vehicleSnapshotInfo.getWithMaintenanceOrder())) {
            stringBuilder.append("[修]");
        }
        if (BooleanUtils.toBoolean(vehicleSnapshotInfo.getWithRescueOrder())) {
            stringBuilder.append("[救]");
        }
        if (BooleanUtils.toBoolean(vehicleSnapshotInfo.getWithAccidentOrder())) {
            stringBuilder.append("[事]");
        }
        return stringBuilder.toString();
    }

    public void updateWaitRedLight(Boolean isTrafficLight) {
        VehicleInfoDO vehicleInfoDO = Optional.ofNullable(this.getVehicleSnapshotInfo())
                .orElse(VehicleInfoDO.builder().build());
        vehicleInfoDO.setIsWaitingRed(isTrafficLight);
        this.vehicleSnapshotInfo = vehicleInfoDO;

    }

    /**
     * 更新停滞积累字段
     *
     * @param value
     */
    public void updateStagnationCount(VehicleCounterInfoDO value) {
        if (value == null || value.getStartTime() == null) {
            return;
        }
        if (CollectionUtils.isEmpty(this.stagnationCounter)) {
            this.stagnationCounter = new ArrayList<>();
        }
        //取停滞积累的时间
        Map<Date, VehicleCounterInfoDO> startTime2Counter = this.stagnationCounter.stream()
                .collect(Collectors.toMap(VehicleCounterInfoDO::getStartTime, Function.identity(), (v1, v2) -> v1));
        VehicleCounterInfoDO thisStartTimeCounter = startTime2Counter.get(value.getStartTime());
        if (thisStartTimeCounter == null) {
            //不存在的时候，添加
            thisStartTimeCounter = value;
            //顺序加进去
            this.stagnationCounter.add(thisStartTimeCounter);
        } else {
            //存在的时候，更新
            thisStartTimeCounter.setEndTime(value.getEndTime());
            thisStartTimeCounter.setDuration(value.getDuration());
        }
    }

    /**
     * 云安全确认或者取消
     *
     * @param date
     */
    public void safetyConfirmOrCancel(CloudTriageEventStatusEnum status, Date date) {
        if (date == null) {
            return;
        }
        if (extInfo == null) {
            extInfo = RiskVehicleExtInfoDO.builder().build();
        }
        switch (status) {
            case COMPLETED:
                extInfo.setSafetyConfirmTime(date);
                break;
            case CANCELED:
                extInfo.setSafetyCancelTime(date);
                break;
        }
    }

    /**
     * 取消时间
     *
     * @param closeTime
     */
    public void cancelSafety(Date closeTime) {
        if (closeTime == null) {
            return;
        }
        if (extInfo == null) {
            extInfo = RiskVehicleExtInfoDO.builder().build();
        }
        extInfo.setCancelSafetyTime(closeTime);
    }

    /**
     * 呼叫云安全时间
     *
     * @param date
     */
    public void callSafety(Date date) {
        if (date == null) {
            return;
        }
        if (extInfo == null) {
            extInfo = RiskVehicleExtInfoDO.builder().build();
        }
        extInfo.setCallSafetyTime(date);
    }


    /**
     * 使用车辆上下文进行更新
     * 
     * @param contextDO
     */
    public void updateFromVehicleRuntimeContext(VehicleRuntimeInfoContextDO contextDO) {
        if (contextDO == null) {
            return;
        }
        if (vehicleSnapshotInfo == null) {
            vehicleSnapshotInfo = VehicleInfoDO.builder().build();
        }
        if (contextDO.getFenceContext() != null) {
            vehicleSnapshotInfo.setFences(contextDO.getFenceContext().getFenceAndFieldMetas());
        }

    }
}
