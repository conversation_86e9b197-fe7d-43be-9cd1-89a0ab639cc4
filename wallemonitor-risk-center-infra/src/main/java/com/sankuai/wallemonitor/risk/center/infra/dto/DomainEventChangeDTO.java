package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.enums.DomainUniqueKeyEnum;
import com.sankuai.wallemonitor.risk.center.infra.utils.compare.EntityChangeRecordDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.compare.EntityFieldChangeRecordDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.compare.RecordCompareUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections.MapUtils;


/**
 * 获取领域内的变更值
 *
 * @param <T>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class DomainEventChangeDTO<T> extends DomainEventDTO<T> {


    /**
     * 领域类
     */
    private Class<T> domainClass;

    /**
     * 新增的领域对象，key为domain的唯一键uk
     */
    @Builder.Default
    private Set<String> newAddedDomainKey = new HashSet<>();

    /**
     * 删除的领域对象，key为domain的唯一键uk
     */
    @Builder.Default
    private Set<String> deletedDomainKey = new HashSet<>();

    /**
     * 修改的领域对象，key为domain的唯一键uk
     */
    @Builder.Default
    private Set<String> changedDomainKey = new HashSet<>();

    /**
     * 任何实体关联的变更，key为domain的唯一键，value是这个实体对应的
     *
     * @see com.sankuai.wallemonitor.risk.center.infra.enums.DomainUniqueKeyEnum
     */
    @Builder.Default
    private Map<String, EntityChangeRecordDTO> allTypeChangedRecord = new HashMap<>();

    /**
     * 变更前的实例
     */
    @Builder.Default
    private Map<String, T> beforeMap = new HashMap<>();

    /**
     * 变更后的实例
     */
    @Builder.Default
    private Map<String, T> afterMap = new HashMap<>();




    /**
     * 获取领域模型某个字段的变化
     *
     * @return
     */
    public EntityFieldChangeRecordDTO getFieldChange(T after, String fieldName) {
        if (MapUtils.isEmpty(allTypeChangedRecord)) {
            return null;
        }
        return Optional.ofNullable(allTypeChangedRecord.get(
                        RecordCompareUtils.getListKey(after, domainClass, DomainUniqueKeyEnum.getUniqueByClass(domainClass))))
                .map(entityChangeRecordDTO -> entityChangeRecordDTO.getFieldChange(fieldName)).orElse(null);

    }

    /**
     * 获取满足要求变化的对象
     *
     * @return
     */
    public List<T> getBySingleField(Predicate<EntityFieldChangeRecordDTO> entityChangeRecordDTOPredicate) {
        if (MapUtils.isEmpty(allTypeChangedRecord)) {
            return new ArrayList<>();
        }

        return allTypeChangedRecord.values().stream()
                .filter(entityChangeRecordDTO -> entityChangeRecordDTO.getChanges().stream()
                        .anyMatch(entityChangeRecordDTOPredicate::test))
                .map(entityChangeRecordDTO -> afterMap.get(entityChangeRecordDTO.getEntityId()))
                .collect(
                        Collectors.toList());
    }

    /**
     * 获取满足要求变化的对象
     *
     * @return
     */
    public void traverByField(BiFunction<EntityFieldChangeRecordDTO, T, Boolean> entityChangeRecordDTOPredicate) {
        if (MapUtils.isEmpty(allTypeChangedRecord)) {
            return;
        }
        allTypeChangedRecord.values()
                //如果一旦满足要求，这个类便不需要遍历
                .forEach((entityChangeRecordDTO) -> {
                    entityChangeRecordDTO.getChanges().stream().filter(entityFieldChangeRecordDTO ->
                            entityChangeRecordDTOPredicate.apply(entityFieldChangeRecordDTO,
                                    afterMap.get(entityChangeRecordDTO.getEntityId()))
                    ).findFirst();
                });

    }

    /**
     * 获取满足要求变化的对象
     *
     * @return
     */
    public List<T> getByMultiField(Predicate<List<EntityFieldChangeRecordDTO>> entityChangeRecordDTOPredicate) {
        if (MapUtils.isEmpty(allTypeChangedRecord)) {
            return new ArrayList<>();
        }

        return allTypeChangedRecord.values().stream()
                .filter(entityChangeRecordDTO -> entityChangeRecordDTOPredicate.test(
                        entityChangeRecordDTO.getChanges()))
                .map(entityChangeRecordDTO -> afterMap.get(entityChangeRecordDTO.getEntityId()))
                .collect(
                        Collectors.toList());
    }


}
