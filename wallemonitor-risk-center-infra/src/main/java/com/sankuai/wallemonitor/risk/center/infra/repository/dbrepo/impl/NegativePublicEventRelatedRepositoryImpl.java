package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.impl;

import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryExecute;
import com.sankuai.wallemonitor.risk.center.infra.annotation.RepositoryQuery;
import com.sankuai.wallemonitor.risk.center.infra.convert.NegativePublicEventRelatedConvert;
import com.sankuai.wallemonitor.risk.center.infra.dal.mapper.eve.riskcenter.NegativePublicEventRelatedMapper;
import com.sankuai.wallemonitor.risk.center.infra.dal.po.eve.riskcenter.NegativePublicEventRelated;
import com.sankuai.wallemonitor.risk.center.infra.model.core.NegativePublicEventRelatedDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.AbstractMapperSingleRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.NegativePublicEventRelatedRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.NegativePublicEventRelatedDOQueryParamDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NegativePublicEventRelatedRepositoryImpl extends
        AbstractMapperSingleRepository<NegativePublicEventRelatedMapper, NegativePublicEventRelatedConvert, NegativePublicEventRelated, NegativePublicEventRelatedDO>
        implements NegativePublicEventRelatedRepository {

    static final String UK_NEGATIVE_PUBLIC_EVENT_EVENT_ID = "eventId";

    /**
     * 保存函数
     *
     * @param relatedDO
     */
    @Override
    @RepositoryExecute
    public void save(NegativePublicEventRelatedDO relatedDO) {
        super.save(relatedDO);

    }


    /**
     * 根据参数查询数据
     *
     * @param paramDTO
     * @return
     */
    @Override
    @RepositoryQuery
    public List<NegativePublicEventRelatedDO> queryByParam(NegativePublicEventRelatedDOQueryParamDTO paramDTO) {
        return super.queryByParam(paramDTO);
    }

    /**
     * 批量保存负外部性事件关联信息
     *
     * @param relatedDOList
     */
    @Override
    @RepositoryQuery
    public void batchSave(List<NegativePublicEventRelatedDO> relatedDOList) {
        super.batchSave(relatedDOList);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    @Override
    @RepositoryExecute
    public void batchDeleteRelatedDO(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        super.batchDelete(idList);
    }
}
