package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo;

import java.util.List;
import java.util.Map;

/**
 * 用户身份信息仓储层
 *
 * <AUTHOR>
 * @date 2025/06/14
 */
public interface UserIdentityRepository {

    /**
     * 根据员工ID获取MIS号
     *
     * @param empId 员工ID
     * @return MIS号，如果未找到返回null
     */
    String getMisByEmpId(Long empId);

    /**
     * 根据员工ID列表批量获取MIS号
     *
     * @param empIdList 员工ID列表
     * @return 员工ID与MIS号的映射关系
     */
    Map<Long, String> getMisByEmpId(List<Long> empIdList);
} 