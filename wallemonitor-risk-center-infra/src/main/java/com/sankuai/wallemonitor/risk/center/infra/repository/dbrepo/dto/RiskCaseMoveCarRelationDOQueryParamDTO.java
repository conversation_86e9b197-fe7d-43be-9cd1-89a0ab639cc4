package com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto;

import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.GreatTo;
import com.sankuai.wallemonitor.risk.center.infra.annotation.mapper.InQuery;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class RiskCaseMoveCarRelationDOQueryParamDTO {

    /**
     * 风险caseId列表
     */
    @InQuery(field = "caseId")
    private List<String> caseIdList;

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 上报人
     */
    private String reporter;


    /**
     * 创建时间大于某个值
     */
    @GreatTo(field = "createTime")
    private Date createTimeGrateTo;

    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean isDeleted = false;

}
