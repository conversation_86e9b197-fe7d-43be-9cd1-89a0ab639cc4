<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <artifactId>wallemonitor-risk-center-infra</artifactId>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>mybatisplus-generator-maven-plugin</artifactId>
        <configuration>
          <packageInfo>
            <!-- 父级包名称，如果不写，下面的 domain/mapper包名全路径需要自己编写-->
            <domain>po.eve.riskcenter</domain>
            <!--domain包名(默认domain)-->
            <mapper>mapper.eve.riskcenter</mapper>
            <!--mapper包名(默认mapper)-->
            <parent>com.sankuai.wallemonitor.risk.center.infra.dal</parent>
          </packageInfo>
          <strategy>
            <entityLombokModel>true</entityLombokModel>
            <entityTableFieldAnnotationEnable>true</entityTableFieldAnnotationEnable>
            <!-- 替换成要生成的表名 -->
            <include>
              <property>risk_error_wait_cross_walk_record</property>
            </include>
          </strategy>
          <!-- 配置文件生成的位置 -->
          <zebra>
            <!-- 如果数据源是读写分离则配置jdbcRef 不可和bladeJdbcRef同时配置 -->
            <appkey>com.sankuai.wallemonitor.risk.center</appkey>
            <!-- 如果数据源是blade则配置bladeJdbcRef 和 appkey 不可和jdbcRef同时配置 该配置要求mybatisplus-generator-maven-plugin 插件版本>=3.1.7-->
            <jdbcRef>eve_risk_center_${active-profile}</jdbcRef>
          </zebra>
        </configuration>
        <groupId>com.meituan.xframe</groupId>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>com.sankuai.meituan.org</groupId>
      <artifactId>open-sdk</artifactId>
      <version>5.0.14</version>
    </dependency>
    <dependency>
      <groupId>com.sankuai.xm</groupId>
      <artifactId>udb-open-thrift</artifactId>
      <version>1.0.14</version>
    </dependency>
    <dependency>
      <groupId>com.sankuai.wallecmdb.data</groupId>
      <artifactId>eve-replay-inquire-api</artifactId>
    </dependency>
    <dependency>
      <artifactId>zebra-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>mafka-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>squirrel-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>kms-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <groupId>com.sankuai.walleom</groupId>
      <artifactId>common-search-api</artifactId>
    </dependency>
    <dependency>
      <artifactId>config-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>leaf-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>thrift-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>
    <dependency>
      <artifactId>leaf-idl</artifactId>
      <groupId>com.sankuai.inf.leaf</groupId>
    </dependency>
    <dependency>
      <artifactId>podam</artifactId>
      <groupId>uk.co.jemos.podam</groupId>
    </dependency>
    <dependency>
      <artifactId>mybatisplus-xframe-boot-starter</artifactId>
      <groupId>com.meituan.xframe</groupId>
    </dependency>

    <dependency>
      <artifactId>wallemonitor-risk-center-api</artifactId>
      <groupId>com.sankuai.wallemonitor</groupId>
    </dependency>
    <!--push相关-->
    <dependency>
      <artifactId>dpmtpush-backend-api</artifactId>
      <groupId>com.dianping.dpmtpush</groupId>
    </dependency>
    <dependency>
      <artifactId>dpmtpushtoken-mapping-api</artifactId>
      <groupId>com.dianping.dpmtpushtoken</groupId>
    </dependency>
    <dependency>
      <artifactId>dpsf-net</artifactId>
      <groupId>com.dianping.dpsf</groupId>
    </dependency>
    <!--geotools-->
    <dependency>
      <artifactId>gt-geojson</artifactId>
      <groupId>org.geotools</groupId>
    </dependency>
    <dependency>
      <artifactId>gt-epsg-wkt</artifactId>
      <groupId>org.geotools</groupId>
    </dependency>
    <!--指定坐标系查询数据库代码中指定的坐标系会从这里查询，只支持其中有的坐标系-->
    <dependency>
      <artifactId>gt-epsg-hsql</artifactId>
      <groupId>org.geotools</groupId>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-api</artifactId>
    </dependency>
    <dependency>
      <artifactId>gt-main</artifactId>
      <groupId>org.geotools</groupId>
    </dependency>
    <dependency>
      <artifactId>jts-core</artifactId>
      <groupId>org.locationtech.jts</groupId>
    </dependency>
    <dependency>
      <groupId>javax.measure</groupId>
      <artifactId>unit-api</artifactId>
    </dependency>
    <!--模型转换-->
    <dependency>
      <artifactId>mapstruct</artifactId>
      <groupId>org.mapstruct</groupId>
    </dependency>
    <dependency>
      <artifactId>mapstruct-jdk8</artifactId>
      <groupId>org.mapstruct</groupId>
    </dependency>
    <dependency>
      <artifactId>mapstruct-processor</artifactId>
      <groupId>org.mapstruct</groupId>
    </dependency>
    <dependency>
      <groupId>com.sankuai.walledelivery</groupId>
      <artifactId>walle-delivery-operation-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.meituan.mars.framework</groupId>
      <artifactId>mars-common-domain</artifactId>
    </dependency>
    <!--地理位置服务-->
    <dependency>
      <groupId>com.sankuai.map.maf</groupId>
      <artifactId>openplatform-dependency</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sankuai.walledelivery</groupId>
      <artifactId>walle-delivery-basic-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sankuai.carosscan</groupId>
      <artifactId>eve_common_client</artifactId>
      <version>1.1.2</version>
      <exclusions>
        <exclusion>
          <artifactId>lombok</artifactId>
          <groupId>org.projectlombok</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-simple</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>lombok-mapstruct-binding</artifactId>
          <groupId>org.projectlombok</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.sankuai.walle</groupId>
      <artifactId>cms-client</artifactId>
    </dependency>
    <!-- 学城开放平台 -->
    <dependency>
      <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
      <artifactId>open-sdk</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sankuai.ead</groupId>
      <artifactId>citadel-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-referencing</artifactId>
      <exclusions>
        <exclusion>
          <groupId>tech.units</groupId>
          <artifactId>indriya</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>tech.units</groupId>
      <artifactId>indriya</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.davidmoten</groupId>
      <artifactId>rtree</artifactId>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-opengis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-geometry</artifactId>
    </dependency>
    <dependency>
      <groupId>org.geotools</groupId>
      <artifactId>gt-metadata</artifactId>
    </dependency>
    <!-- 富文本工具依赖 -->
    <dependency>
      <groupId>com.sankuai.oa.card.toolkit</groupId>
      <artifactId>card-toolkit</artifactId>
    </dependency>

  </dependencies>

  <modelVersion>4.0.0</modelVersion>
  <name>wallemonitor-risk-center-infra</name>
  <packaging>jar</packaging>

  <parent>
    <artifactId>wallemonitor-risk-center</artifactId>
    <groupId>com.sankuai.wallemonitor</groupId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <version>1.0.0-SNAPSHOT</version>
</project>
