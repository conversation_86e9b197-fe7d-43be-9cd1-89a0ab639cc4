<?xml version="1.0" encoding="UTF-8"?>
<serviceCatalog
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://service.sankuai.com/1.0.0"
  xsi:schemaLocation="http://service.sankuai.com/1.0.0
            http://pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.0/servicecatalog-1.0.0.xsd">

  <serviceDescs>
    <!--
          该示例描述的是使用 Restful 服务框架的情况下如何进行文档编写。该类型的编写方式和普通 java 的一样，都是通过注解的方式在
          代码中进行相关文档的描述。接入文档可以参考：https://km.sankuai.com/page/60715770
          可以将 <serviceDesc> 中 name, description, scenarios 等信息以 @ServiceDoc 的注解在代码中标注，也可以直接
          按下面这种方式进行编写。
    -->
    <serviceDesc>
      <appkey>com.sankuai.wallemonitor.risk.center</appkey>
      <description></description>
      <interfaceDescs>
          <interfaceDesc>
            <class>com.sankuai.wallemonitor.risk.center.api.thrift.IThriftManualRiskCaseService</class>
            <type>octo.thrift.annotation</type>
          </interfaceDesc>
        </interfaceDescs>
      <name>风控RPC</name>
      <scenarios>用于风控服务的RPC相关接口</scenarios>
    </serviceDesc>

  </serviceDescs>

</serviceCatalog>